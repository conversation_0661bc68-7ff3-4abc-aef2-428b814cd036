'''
File Created: Wednesday, 28th May 2025 2:03:04 am
Author: gs(SHANGHAI IDEAL INFORMATION INDUSTRY(GROUP)CO.,LTD)
Last Modified: Monday, 9th June 2025 2:18:34 am
'''

from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union
from enum import Enum

@dataclass
class FieldSchema:
    """字段schema定义"""
    name: str
    field_type: str
    description: str = ""
    is_primary: bool = False
    additional_properties: Optional[Dict[str, Any]] = None


class PartitionStrategy(Enum):
    """分区策略枚举"""
    HASH = "HASH"
    LIST = "LIST"
    RANGE = "RANGE"

@dataclass
class PartitionConfig:
    """分区配置类 - 支持多种分区策略的通用配置"""
    
    # 基础分区配置
    strategy: PartitionStrategy
    partition_keys: List[str]  # 支持复合分区键
    
    # HASH分区特定配置
    hash_partitions_count: Optional[int] = None  # HASH分区数量
    
    # LIST分区特定配置
    list_values: Optional[List[str]] = None  # LIST分区的值列表
    
    # RANGE分区特定配置
    range_bounds: Optional[List[Dict[str, Any]]] = None  # RANGE分区边界
    
    # 分区命名策略
    partition_naming_pattern: str = "{table}_{strategy}_{index:03d}"
    
    # 是否自动创建分区
    auto_create_partitions: bool = True
    
    # 预分区设置（对HASH分区特别有用）
    pre_create_all_partitions: bool = False

@dataclass
class CollectionSchema:
    """集合模式定义"""
    fields: List[FieldSchema]
    description: str = ""
    
    # 新增：分区配置
    partition_config: Optional[PartitionConfig] = None
    
    # 索引配置
    index_configs: Optional[List[Dict[str, Any]]] = None

    def __post_init__(self):
        # 验证字段名称唯一性
        field_names = [field.name for field in self.fields]
        if len(field_names) != len(set(field_names)):
            raise ValueError("字段名称必须唯一")

@dataclass
class ColumnSchema:
    """关系型数据库表的列定义"""
    name: str
    column_type: str
    description: str = ""
    is_primary: bool = False
    additional_properties: Optional[Dict[str, Any]] = None

@dataclass
class RDSTableSchema:
    """关系型数据库的表模式定义"""
    columns: List[ColumnSchema]
    description: str = ""
    # 例如: {"ENGINE": "InnoDB", "CHARSET": "utf8mb4"}
    options: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        # 验证列名唯一性
        column_names = [col.name for col in self.columns]
        if len(column_names) != len(set(column_names)):
            raise ValueError("列名称必须唯一")

@dataclass
class RDBConnectionConfig:
    """关系型数据库连接配置"""
    host: str
    port: int
    user: str
    password: str
    db_name: str


@dataclass
class VDBConnectionConfig:
    """向量数据库连接配置"""
    host: str
    port: int
    user: str
    password: str
    db_name: str


# ==================== 高级查询条件数据结构 ====================

class QueryOperator(Enum):
    """查询操作符枚举"""
    # 比较操作符
    EQ = "="           # 等于
    NE = "!="          # 不等于
    GT = ">"           # 大于
    GTE = ">="         # 大于等于
    LT = "<"           # 小于
    LTE = "<="         # 小于等于

    # 模糊匹配
    LIKE = "LIKE"      # 模糊匹配
    NOT_LIKE = "NOT LIKE"  # 不匹配
    ILIKE = "ILIKE"    # 大小写不敏感模糊匹配

    # 集合操作
    IN = "IN"          # 在集合中
    NOT_IN = "NOT IN"  # 不在集合中

    # 空值检查
    IS_NULL = "IS NULL"        # 为空
    IS_NOT_NULL = "IS NOT NULL"  # 不为空

    # 范围操作
    BETWEEN = "BETWEEN"        # 在范围内
    NOT_BETWEEN = "NOT BETWEEN"  # 不在范围内


@dataclass
class QueryCondition:
    """单个查询条件"""
    field: str                           # 字段名
    operator: QueryOperator              # 操作符
    value: Any                          # 值
    case_sensitive: bool = True         # 是否大小写敏感（用于字符串比较）


@dataclass
class QueryConditionGroup:
    """查询条件组 - 支持AND/OR逻辑"""
    conditions: List[Union[QueryCondition, 'QueryConditionGroup']]
    logic: str = "AND"  # "AND" 或 "OR"


@dataclass
class JoinConfig:
    """JOIN配置"""
    table: str                          # 要JOIN的表名
    on_conditions: List[str]           # ON条件列表，如 ["table1.id = table2.table1_id"]
    join_type: str = "INNER"           # JOIN类型: INNER, LEFT, RIGHT, FULL
    alias: Optional[str] = None        # 表别名


@dataclass
class AggregateConfig:
    """聚合配置"""
    function: str                      # 聚合函数: COUNT, SUM, AVG, MAX, MIN
    field: str                        # 聚合字段
    alias: Optional[str] = None       # 结果别名


@dataclass
class AdvancedQueryConfig:
    """高级查询配置"""
    # 基础查询参数
    table: str
    columns: Optional[List[str]] = None

    # 复杂条件
    conditions: Optional[QueryConditionGroup] = None

    # JOIN配置
    joins: Optional[List[JoinConfig]] = None

    # 聚合配置
    aggregates: Optional[List[AggregateConfig]] = None

    # 分组和排序
    group_by: Optional[List[str]] = None
    having: Optional[QueryConditionGroup] = None
    order_by: Optional[List[str]] = None

    # 分页
    limit: Optional[int] = None
    offset: Optional[int] = None

    # 其他选项
    distinct: bool = False
    for_update: bool = False


# ==================== 便捷查询配置 ====================

@dataclass
class LikeQueryConfig:
    """模糊查询配置"""
    table: str
    search_fields: List[str]            # 要搜索的字段列表
    query: str                          # 搜索关键词
    columns: Optional[List[str]] = None # 返回的列
    additional_conditions: Optional[Dict[str, Any]] = None  # 额外的等值条件
    case_sensitive: bool = False        # 是否大小写敏感
    match_mode: str = "contains"        # 匹配模式: contains, starts_with, ends_with
    limit: Optional[int] = None
    offset: Optional[int] = None
    order_by: Optional[List[str]] = None


@dataclass
class RangeQueryConfig:
    """范围查询配置"""
    table: str
    range_field: str                    # 范围查询的字段
    min_value: Optional[Any] = None     # 最小值
    max_value: Optional[Any] = None     # 最大值
    columns: Optional[List[str]] = None
    additional_conditions: Optional[Dict[str, Any]] = None
    limit: Optional[int] = None
    offset: Optional[int] = None
    order_by: Optional[List[str]] = None
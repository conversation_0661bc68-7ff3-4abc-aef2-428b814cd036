"""
PGVector连接管理模块

统一管理PGVector数据库的连接、会话池、健康检查等功能
基于新的核心架构设计，提供高性能的连接管理

设计原则：
1. 统一接口 - 同步/异步双重支持
2. 连接池优化 - 智能连接池管理
3. 健康监控 - 实时连接状态监控
4. 错误恢复 - 自动重连和故障恢复
5. 性能优化 - 连接预热和缓存

作者: HSBC Knowledge Team
日期: 2025-01-15
"""

import asyncio
import logging
import time
from typing import Any, Dict, Optional, Union
from contextlib import asynccontextmanager, contextmanager

from base.db.base.vdb.core import (
    VectorDBType, ConnectionError, TimeoutError,
    VectorDatabaseClient
)
from base.db.base.schemas import VDBConnectionConfig
from ..session import SessionManager as PGVectorSessionManager

logger = logging.getLogger(__name__)


class PGVectorConnectionManager:
    """PGVector连接管理器
    
    负责管理PGVector数据库的连接生命周期，包括：
    - 连接建立和断开
    - 连接池管理
    - 健康检查和监控
    - 自动重连和故障恢复
    """
    
    def __init__(self, config: VDBConnectionConfig, session_manager=None, **kwargs):
        """
        初始化连接管理器

        Args:
            config: 数据库连接配置
            session_manager: 可选的共享session_manager实例
            **kwargs: 额外配置参数
        """
        self.config = config
        self.session_manager = session_manager or PGVectorSessionManager(config, **kwargs)
        
        # 连接状态
        self._connected = False
        self._last_health_check = 0
        self._health_check_interval = kwargs.get('health_check_interval', 30)
        
        # 性能统计
        self._connection_stats = {
            'total_connections': 0,
            'failed_connections': 0,
            'reconnections': 0,
            'last_connect_time': None,
            'last_disconnect_time': None
        }
        
        logger.debug(f"初始化PGVector连接管理器: {config.host}:{config.port}")
    
    # ==================== 基础连接管理 ====================
    
    def connect(self, **kwargs) -> None:
        """建立数据库连接（同步）
        
        Args:
            **kwargs: 连接参数
            
        Raises:
            ConnectionError: 连接失败时抛出
        """
        try:
            start_time = time.time()
            logger.info(f"开始连接PGVector数据库: {self.config.host}:{self.config.port}")
            
            # 使用会话管理器建立连接
            self.session_manager.connect()
            
            # 更新状态
            self._connected = True
            self._connection_stats['total_connections'] += 1
            self._connection_stats['last_connect_time'] = time.time()
            
            connect_time = time.time() - start_time
            logger.info(f"PGVector连接成功，耗时: {connect_time:.3f}秒")
            
            # 执行连接后的初始化
            self._post_connect_setup()
            
        except Exception as e:
            self._connected = False
            self._connection_stats['failed_connections'] += 1
            
            error_msg = f"PGVector连接失败: {e}"
            logger.error(error_msg)
            
            raise ConnectionError(
                error_msg,
                host=self.config.host,
                port=self.config.port,
                database_type=VectorDBType.PGVECTOR,
                original_error=e
            )
    
    async def aconnect(self, **kwargs) -> None:
        """建立数据库连接（异步）
        
        Args:
            **kwargs: 连接参数
            
        Raises:
            ConnectionError: 连接失败时抛出
        """
        try:
            start_time = time.time()
            logger.info(f"开始异步连接PGVector数据库: {self.config.host}:{self.config.port}")
            
            # 使用会话管理器建立异步连接
            await self.session_manager.async_connect()
            
            # 更新状态
            self._connected = True
            self._connection_stats['total_connections'] += 1
            self._connection_stats['last_connect_time'] = time.time()
            
            connect_time = time.time() - start_time
            logger.info(f"PGVector异步连接成功，耗时: {connect_time:.3f}秒")
            
            # 执行连接后的初始化
            await self._apost_connect_setup()
            
        except Exception as e:
            self._connected = False
            self._connection_stats['failed_connections'] += 1
            
            error_msg = f"PGVector异步连接失败: {e}"
            logger.error(error_msg)
            
            raise ConnectionError(
                error_msg,
                host=self.config.host,
                port=self.config.port,
                database_type=VectorDBType.PGVECTOR,
                original_error=e
            )
    
    def disconnect(self) -> None:
        """断开数据库连接（同步）"""
        try:
            if self._connected:
                logger.info("断开PGVector数据库连接")
                
                # 使用会话管理器断开连接
                self.session_manager.disconnect()
                
                # 更新状态
                self._connected = False
                self._connection_stats['last_disconnect_time'] = time.time()
                
                logger.info("PGVector连接已断开")
            else:
                logger.debug("PGVector连接已经断开，无需重复操作")
                
        except Exception as e:
            logger.warning(f"断开PGVector连接时出错: {e}")
            # 即使出错也要更新状态
            self._connected = False
    
    async def adisconnect(self) -> None:
        """断开数据库连接（异步）"""
        try:
            if self._connected:
                logger.info("异步断开PGVector数据库连接")
                
                # 使用会话管理器断开异步连接
                await self.session_manager.async_disconnect()
                
                # 更新状态
                self._connected = False
                self._connection_stats['last_disconnect_time'] = time.time()
                
                logger.info("PGVector异步连接已断开")
            else:
                logger.debug("PGVector异步连接已经断开，无需重复操作")
                
        except Exception as e:
            logger.warning(f"异步断开PGVector连接时出错: {e}")
            # 即使出错也要更新状态
            self._connected = False
    
    # ==================== 连接状态管理 ====================
    
    def is_connected(self) -> bool:
        """检查同步连接是否已建立"""
        return self._connected and self.session_manager.is_connected

    def is_aconnected(self) -> bool:
        """检查异步连接是否已建立"""
        return self._connected and self.session_manager.is_aconnected
    
    def health_check(self, timeout: float = 5.0) -> Dict[str, Any]:
        """健康检查（同步）
        
        Args:
            timeout: 检查超时时间（秒）
            
        Returns:
            健康检查结果
        """
        start_time = time.time()
        result = {
            'healthy': False,
            'response_time': 0.0,
            'error': None,
            'timestamp': start_time,
            'connection_stats': self._connection_stats.copy()
        }
        
        try:
            if not self.is_connected():
                result['error'] = "连接未建立"
                return result
            
            # 执行简单查询测试连接
            test_query = "SELECT 1 as health_check"
            with self.session_manager.get_cursor() as cursor:
                cursor.execute(test_query)
                cursor.fetchone()
            
            # 计算响应时间
            result['response_time'] = time.time() - start_time
            result['healthy'] = True
            
            # 更新最后健康检查时间
            self._last_health_check = time.time()
            
            logger.debug(f"健康检查通过，响应时间: {result['response_time']:.3f}秒")
            
        except Exception as e:
            result['error'] = str(e)
            result['response_time'] = time.time() - start_time
            logger.warning(f"健康检查失败: {e}")
        
        return result
    
    async def ahealth_check(self, timeout: float = 5.0) -> Dict[str, Any]:
        """健康检查（异步）
        
        Args:
            timeout: 检查超时时间（秒）
            
        Returns:
            健康检查结果
        """
        start_time = time.time()
        result = {
            'healthy': False,
            'response_time': 0.0,
            'error': None,
            'timestamp': start_time,
            'connection_stats': self._connection_stats.copy()
        }
        
        try:
            if not self.is_connected():
                result['error'] = "连接未建立"
                return result
            
            # 执行简单查询测试连接
            test_query = "SELECT 1 as health_check"
            async with self.session_manager.get_async_cursor() as conn:
                await conn.execute(test_query)
            
            # 计算响应时间
            result['response_time'] = time.time() - start_time
            result['healthy'] = True
            
            # 更新最后健康检查时间
            self._last_health_check = time.time()
            
            logger.debug(f"异步健康检查通过，响应时间: {result['response_time']:.3f}秒")
            
        except Exception as e:
            result['error'] = str(e)
            result['response_time'] = time.time() - start_time
            logger.warning(f"异步健康检查失败: {e}")

        return result

    # ==================== 连接池管理 ====================

    def warmup_connections(self, pool_size: int = 5) -> Dict[str, Any]:
        """预热连接池（同步）

        Args:
            pool_size: 预热的连接数量

        Returns:
            预热结果
        """
        start_time = time.time()
        result = {
            'success': False,
            'warmed_connections': 0,
            'failed_connections': 0,
            'total_time': 0.0,
            'errors': []
        }

        try:
            logger.info(f"开始预热连接池，目标连接数: {pool_size}")

            # 使用会话管理器预热连接池
            if hasattr(self.session_manager, 'warmup_pool'):
                warmup_result = self.session_manager.warmup_pool(pool_size)
                result.update(warmup_result)
            else:
                # 如果没有专门的预热方法，手动创建连接
                for i in range(pool_size):
                    try:
                        with self.session_manager.get_cursor() as cursor:
                            cursor.execute("SELECT 1")
                        result['warmed_connections'] += 1
                    except Exception as e:
                        result['failed_connections'] += 1
                        result['errors'].append(str(e))

            result['total_time'] = time.time() - start_time
            result['success'] = result['warmed_connections'] > 0

            logger.info(f"连接池预热完成: {result['warmed_connections']}/{pool_size} 成功")

        except Exception as e:
            result['total_time'] = time.time() - start_time
            result['errors'].append(str(e))
            logger.error(f"连接池预热失败: {e}")

        return result

    async def awarmup_connections(self, pool_size: int = 5) -> Dict[str, Any]:
        """预热连接池（异步）

        Args:
            pool_size: 预热的连接数量

        Returns:
            预热结果
        """
        start_time = time.time()
        result = {
            'success': False,
            'warmed_connections': 0,
            'failed_connections': 0,
            'total_time': 0.0,
            'errors': []
        }

        try:
            logger.info(f"开始异步预热连接池，目标连接数: {pool_size}")

            # 使用会话管理器预热连接池
            if hasattr(self.session_manager, 'awarmup_pool'):
                warmup_result = await self.session_manager.awarmup_pool(pool_size)
                result.update(warmup_result)
            else:
                # 如果没有专门的预热方法，手动创建连接
                tasks = []
                for i in range(pool_size):
                    tasks.append(self._test_async_connection())

                results = await asyncio.gather(*tasks, return_exceptions=True)

                for res in results:
                    if isinstance(res, Exception):
                        result['failed_connections'] += 1
                        result['errors'].append(str(res))
                    else:
                        result['warmed_connections'] += 1

            result['total_time'] = time.time() - start_time
            result['success'] = result['warmed_connections'] > 0

            logger.info(f"异步连接池预热完成: {result['warmed_connections']}/{pool_size} 成功")

        except Exception as e:
            result['total_time'] = time.time() - start_time
            result['errors'].append(str(e))
            logger.error(f"异步连接池预热失败: {e}")

        return result

    # ==================== 自动重连和故障恢复 ====================

    def auto_reconnect(self, max_retries: int = 3, retry_delay: float = 1.0) -> bool:
        """自动重连（同步）

        Args:
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）

        Returns:
            是否重连成功
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试自动重连 (第{attempt + 1}/{max_retries}次)")

                # 先断开现有连接
                self.disconnect()

                # 等待一段时间后重连
                if attempt > 0:
                    time.sleep(retry_delay * attempt)

                # 重新连接
                self.connect()

                # 验证连接
                health_result = self.health_check()
                if health_result['healthy']:
                    self._connection_stats['reconnections'] += 1
                    logger.info("自动重连成功")
                    return True

            except Exception as e:
                logger.warning(f"自动重连失败 (第{attempt + 1}次): {e}")

        logger.error(f"自动重连失败，已尝试{max_retries}次")
        return False

    async def aauto_reconnect(self, max_retries: int = 3, retry_delay: float = 1.0) -> bool:
        """自动重连（异步）

        Args:
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）

        Returns:
            是否重连成功
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试异步自动重连 (第{attempt + 1}/{max_retries}次)")

                # 先断开现有连接
                await self.adisconnect()

                # 等待一段时间后重连
                if attempt > 0:
                    await asyncio.sleep(retry_delay * attempt)

                # 重新连接
                await self.aconnect()

                # 验证连接
                health_result = await self.ahealth_check()
                if health_result['healthy']:
                    self._connection_stats['reconnections'] += 1
                    logger.info("异步自动重连成功")
                    return True

            except Exception as e:
                logger.warning(f"异步自动重连失败 (第{attempt + 1}次): {e}")

        logger.error(f"异步自动重连失败，已尝试{max_retries}次")
        return False

    # ==================== 内部辅助方法 ====================

    def _post_connect_setup(self) -> None:
        """连接后的初始化设置"""
        try:
            # 设置连接参数
            with self.session_manager.get_cursor() as cursor:
                # 设置查询超时
                cursor.execute("SET statement_timeout = '30s'")
                # 设置向量扩展
                cursor.execute("CREATE EXTENSION IF NOT EXISTS vector")
                cursor.connection.commit()

            logger.debug("连接后初始化完成")

        except Exception as e:
            logger.warning(f"连接后初始化失败: {e}")

    async def _apost_connect_setup(self) -> None:
        """连接后的异步初始化设置"""
        try:
            # 设置连接参数
            async with self.session_manager.get_async_cursor() as conn:
                # 设置查询超时
                await conn.execute("SET statement_timeout = '30s'")
                # 设置向量扩展
                await conn.execute("CREATE EXTENSION IF NOT EXISTS vector")

            logger.debug("异步连接后初始化完成")

        except Exception as e:
            logger.warning(f"异步连接后初始化失败: {e}")

    async def _test_async_connection(self) -> bool:
        """测试异步连接"""
        try:
            async with self.session_manager.get_async_cursor() as conn:
                await conn.execute("SELECT 1")
            return True
        except Exception:
            return False

    # ==================== 统计和监控 ====================

    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        stats = self._connection_stats.copy()
        stats.update({
            'current_status': 'connected' if self.is_connected() else 'disconnected',
            'last_health_check': self._last_health_check,
            'health_check_interval': self._health_check_interval,
            'config': {
                'host': self.config.host,
                'port': self.config.port,
                'database': self.config.db_name
            }
        })
        return stats

    # ==================== 上下文管理器支持 ====================

    @contextmanager
    def connection_context(self):
        """同步连接上下文管理器"""
        self.connect()
        try:
            yield self
        finally:
            self.disconnect()

    @asynccontextmanager
    async def aconnection_context(self):
        """异步连接上下文管理器"""
        await self.aconnect()
        try:
            yield self
        finally:
            await self.adisconnect()

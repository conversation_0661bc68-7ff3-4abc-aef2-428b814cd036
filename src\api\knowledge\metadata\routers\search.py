"""
元数据搜索功能API路由

提供元数据的智能搜索功能，参照DD系统的设计模式。
"""

import time
from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Depends
import logging

# 标准化日志导入
logger = logging.getLogger(__name__)

from api.knowledge.models.response_models import StandardResponse
from ..models import MetadataSearchRequest, MetadataSearchResponse
from ..dependencies import get_metadata_search, get_search_params

# 创建路由器
router = APIRouter(tags=["元数据搜索功能"], prefix="/search")


@router.post("/vector", response_model=StandardResponse, summary="向量搜索")
async def vector_search(
    request: MetadataSearchRequest,
    metadata_search = Depends(get_metadata_search)
):
    """
    基于向量相似度的语义搜索
    
    - **query**: 搜索查询文本
    - **knowledge_id**: 知识库ID过滤（可选）
    - **entity_type**: 实体类型过滤（可选）
    - **limit**: 返回结果数量限制（1-100）
    - **min_score**: 最小相似度分数（0.0-1.0）
    
    返回与查询文本语义最相似的元数据记录。
    """
    try:
        start_time = time.time()
        
        # 执行向量搜索
        results = await metadata_search.vector_search(
            query=request.query,
            knowledge_id=request.knowledge_id,
            entity_type=request.entity_type,
            limit=request.limit,
            min_score=request.min_score
        )
        
        search_time = time.time() - start_time
        
        # 构建搜索结果
        search_results = []
        for result in results:
            search_results.append({
                "score": result.get("score", 0.0),
                "entity_type": result.get("entity_type", ""),
                "entity_data": result.get("entity_data", {}),
                "vector_info": {
                    "vector_id": result.get("vector_id"),
                    "field": result.get("field"),
                    "entity_id": result.get("entity_id")
                }
            })
        
        response_data = {
            "results": search_results,
            "total": len(search_results),
            "query": request.query,
            "search_time": round(search_time, 3),
            "search_type": "vector"
        }
        
        return StandardResponse(
            success=True,
            message=f"向量搜索完成，找到 {len(search_results)} 条结果",
            data=response_data
        )
    except Exception as e:
        logger.error(f"向量搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"向量搜索失败: {str(e)}")


@router.post("/hybrid", response_model=StandardResponse, summary="混合搜索")
async def hybrid_search(
    request: MetadataSearchRequest,
    metadata_search = Depends(get_metadata_search)
):
    """
    混合搜索（向量搜索 + 文本搜索）
    
    - **query**: 搜索查询文本
    - **knowledge_id**: 知识库ID过滤（可选）
    - **entity_type**: 实体类型过滤（可选）
    - **limit**: 返回结果数量限制（1-100）
    - **min_score**: 最小相似度分数（0.0-1.0）
    - **vector_weight**: 向量搜索权重（0.0-1.0）
    - **text_weight**: 文本搜索权重（0.0-1.0）
    
    结合向量语义搜索和传统文本搜索，提供更全面的搜索结果。
    """
    try:
        start_time = time.time()
        
        # 执行混合搜索
        results = await metadata_search.hybrid_search(
            query=request.query,
            knowledge_id=request.knowledge_id,
            entity_type=request.entity_type,
            limit=request.limit,
            min_score=request.min_score,
            vector_weight=request.vector_weight,
            text_weight=request.text_weight
        )
        
        search_time = time.time() - start_time
        
        # 构建搜索结果
        search_results = []
        for result in results:
            search_results.append({
                "score": result.get("combined_score", result.get("score", 0.0)),
                "entity_type": result.get("entity_type", ""),
                "entity_data": result.get("entity_data", {}),
                "vector_info": {
                    "vector_id": result.get("vector_id"),
                    "field": result.get("field"),
                    "entity_id": result.get("entity_id"),
                    "vector_score": result.get("vector_score"),
                    "text_score": result.get("text_score")
                }
            })
        
        response_data = {
            "results": search_results,
            "total": len(search_results),
            "query": request.query,
            "search_time": round(search_time, 3),
            "search_type": "hybrid",
            "weights": {
                "vector_weight": request.vector_weight,
                "text_weight": request.text_weight
            }
        }
        
        return StandardResponse(
            success=True,
            message=f"混合搜索完成，找到 {len(search_results)} 条结果",
            data=response_data
        )
    except Exception as e:
        logger.error(f"混合搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"混合搜索失败: {str(e)}")


@router.get("/by-name", response_model=StandardResponse, summary="按名称搜索")
async def search_by_name(
    name: str = Query(..., description="名称"),
    knowledge_id: Optional[str] = Query(None, description="知识库ID过滤"),
    entity_type: Optional[str] = Query(None, description="实体类型过滤"),
    limit: int = Query(10, description="返回结果数量限制", ge=1, le=100),
    metadata_search = Depends(get_metadata_search)
):
    """
    根据名称进行搜索
    
    - **name**: 名称（数据库名、表名、字段名等）
    - **knowledge_id**: 知识库ID过滤（可选）
    - **entity_type**: 实体类型过滤（可选）
    - **limit**: 返回结果数量限制（1-100）
    
    在名称字段中搜索包含指定名称的记录。
    """
    try:
        start_time = time.time()
        
        # 执行名称搜索
        results = await metadata_search.search_by_name(
            name=name,
            knowledge_id=knowledge_id,
            entity_type=entity_type,
            limit=limit
        )
        
        search_time = time.time() - start_time
        
        # 构建搜索结果
        search_results = []
        for result in results:
            search_results.append({
                "score": result.get("score", 1.0),  # 精确匹配给高分
                "entity_type": result.get("entity_type", ""),
                "entity_data": result.get("entity_data", {}),
                "match_info": {
                    "field": "name",
                    "match_type": "name_search",
                    "matched_text": name
                }
            })
        
        response_data = {
            "results": search_results,
            "total": len(search_results),
            "query": name,
            "search_time": round(search_time, 3),
            "search_type": "name_search"
        }
        
        return StandardResponse(
            success=True,
            message=f"名称搜索完成，找到 {len(search_results)} 条结果",
            data=response_data
        )
    except Exception as e:
        logger.error(f"名称搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"名称搜索失败: {str(e)}")


@router.get("/by-description", response_model=StandardResponse, summary="按描述搜索")
async def search_by_description(
    description: str = Query(..., description="描述"),
    knowledge_id: Optional[str] = Query(None, description="知识库ID过滤"),
    entity_type: Optional[str] = Query(None, description="实体类型过滤"),
    limit: int = Query(10, description="返回结果数量限制", ge=1, le=100),
    metadata_search = Depends(get_metadata_search)
):
    """
    根据描述进行搜索
    
    - **description**: 描述内容
    - **knowledge_id**: 知识库ID过滤（可选）
    - **entity_type**: 实体类型过滤（可选）
    - **limit**: 返回结果数量限制（1-100）
    
    在描述字段中搜索包含指定描述的记录。
    """
    try:
        start_time = time.time()
        
        # 执行描述搜索
        results = await metadata_search.search_by_description(
            description=description,
            knowledge_id=knowledge_id,
            entity_type=entity_type,
            limit=limit
        )
        
        search_time = time.time() - start_time
        
        # 构建搜索结果
        search_results = []
        for result in results:
            search_results.append({
                "score": result.get("score", 1.0),  # 精确匹配给高分
                "entity_type": result.get("entity_type", ""),
                "entity_data": result.get("entity_data", {}),
                "match_info": {
                    "field": "description",
                    "match_type": "description_search",
                    "matched_text": description
                }
            })
        
        response_data = {
            "results": search_results,
            "total": len(search_results),
            "query": description,
            "search_time": round(search_time, 3),
            "search_type": "description_search"
        }
        
        return StandardResponse(
            success=True,
            message=f"描述搜索完成，找到 {len(search_results)} 条结果",
            data=response_data
        )
    except Exception as e:
        logger.error(f"描述搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"描述搜索失败: {str(e)}")

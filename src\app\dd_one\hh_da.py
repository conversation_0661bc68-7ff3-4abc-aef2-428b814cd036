import asyncio
import json
import os
import re
from openpyxl import Workbook
import pandas as pd
from typing import List, Dict, Optional, Any, Tuple
from model_serve.model_runtime.model_providers.model_interface import UnifiedLLMInterface
from model_serve.model_runtime.entities import PromptMessage
from docx import Document
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from config.config import qwen_llm_config, style_json_path
from doc_parse.parses.simple_utils.utils.orig_sheet_reader import process_excel_file
from doc_parse.parses.simple_utils.utils.process_get_excel import process_excel, excel_coordinate_to_a1
from doc_parse.parses.simple_utils.dd_utils.convert_doc import convert_doc_to_docx, accept_all_revisions
from doc_parse.parses.simple_utils.dd_utils.dd_prompt import _project_prompt, _get_all_site_prompt, \
    extract_json_from_response

llm_interface = UnifiedLLMInterface()


def process_third_part(text: str) -> List[Dict[str, Any]]:
    """
    处理第三部分文本，按表分割并提取内容。
    - 按“表X：”或“附注项目：”分割，保留第一个元素作为内容。
    - 如果有“附注项目：”，将其视为独立表，第一个元素作为“表一”内容。
    - 如果没有“表X：”或“附注项目：”，将整个文本作为“表一”。
    - “》：”不用于分割，仅用于重置表编号。
    - 为每个表的内容块添加前置换行符。
    - 移除“特定项目注解”及之后内容。
    """
    # 清理空行和多余空格
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    if not lines:
        return []
    cleaned_text = '\n'.join(lines)

    # 移除特定项目注解及之后内容
    intra_pattern = r'\n*特定项目注解'
    intra_match = re.search(intra_pattern, cleaned_text, re.DOTALL)
    if intra_match:
        cleaned_text = cleaned_text[:intra_match.start()].strip()

    # 如果清理后文本为空，返回空列表
    if not cleaned_text:
        return []

    # 定义分割模式，匹配“表X：”或“附注项目：”
    table_pattern = r'(表[一二三四五六]：|附注项目：)'
    parts = re.split(table_pattern, cleaned_text, flags=re.MULTILINE)

    # 过滤空段落
    parts = [part.strip() for part in parts if part.strip()]

    # 检查是否存在分隔符
    has_table = any(re.match(r'表[一二三四五六]：', part) for part in parts)
    has_footnote = '附注项目：' in cleaned_text
    has_special_separator = '》：\n' in cleaned_text

    # 如果没有分隔符，将整个文本作为表一
    if not has_table and not has_footnote:
        return [process_table_content("表一：", "\n" + cleaned_text)]

    result = []
    current_table = "表一："
    current_content = []
    table_counter = 1

    # 如果有“》：”，重置表编号
    if has_special_separator:
        table_counter = 1
        current_table = f"表{num_to_chinese(table_counter)}："

    for i, segment in enumerate(parts):
        # 如果是分隔符
        if re.match(r'表[一二三四五六]：|附注项目：', segment):
            # 保存上一个表的内容
            if current_content:
                result.append(process_table_content(current_table, "\n" + "\n".join(current_content).strip()))
            current_table = segment
            current_content = []
            table_counter += 1
        else:
            # 累积内容，包括第一个元素
            current_content.append(segment)

    # 处理最后一个表的内容
    if current_content:
        result.append(process_table_content(current_table, "\n" + "\n".join(current_content).strip()))

    return result


def num_to_chinese(num: int) -> str:
    """将数字转换为中文表编号（一、二、三等）"""
    chinese_nums = {1: '一', 2: '二', 3: '三', 4: '四', 5: '五', 6: '六'}
    return chinese_nums.get(num, '一')


def process_table_content(table_name: str, content: str) -> Dict[str, Any]:
    """
    处理单个表的内容，提取具体说明、子项目列表和参考码表。
    项目名称：从“：”到前一个换行符之间的内容。
    项目描述：从“：”之后到下一个项目名称之前的全部内容（可为空）。
    当子项目名称包含“ - ”或“-”时，拆分为多个子项目。
    subitem_id 移除尾部的 . 和 ．
    当项目id为空且格式无效时，将该子项目信息合并到上一个子项目的描述中。
    优化：当描述前30个字符包含']'时，将']'前的内容拼接到项目名称。
    """
    print(content)
    content = content.replace(']', ']：').replace(']：至', ']至').replace(']：：', ']：').replace('其中：', '其中:'). \
        replace('第三部分：具体说明及核对关系', '').replace('第Ⅶ部分：贷款投向分行业情况表', '').replace('其中，',
                                                                                                       '').strip()
    subitem_list = []

    # 使用 re.split 分割内容，匹配以换行或开头开始的“：”行
    subitem_pattern = re.compile(r'^(.*?[.].*?：)', flags=re.MULTILINE)
    split_result = subitem_pattern.split(content)
    # 提取具体说明（第一个子项目前的部分）
    detail_content = split_result[0].rstrip('\n具体说明：').strip() if split_result else content.strip()

    # 处理每个子项目（从索引 1 开始，每隔 2 个取项目名称）
    for i in range(1, len(split_result), 2):  # 跳过捕获组1（完整匹配），取捕获组2（项目名称）
        # print(i)
        raw_name = split_result[i].rstrip('：').strip()
        description = split_result[i + 1].strip() if i + 1 < len(split_result) else ""
        if not raw_name:
            continue

        # 检查是否为多子项目
        has_multiple_sites = ' - ' in raw_name or '-' in raw_name
        has_more_pros = ']至[' in raw_name
        if description.startswith('/'):
            if '：：' in description:
                raw_name = raw_name + description.split('：：')[0].replace('：', "")
                description = description.split('：：')[1]
            elif '：' in description:
                raw_name = raw_name + description.split('：')[0].replace('：', "")
                description = description.split('：')[1]
            else:
                raw_name = raw_name + description.split('/')[0]
                description = description.split('/')
        # 检查是否是两个项目堆积
        has_accumulate_sites = '/' in raw_name
        if has_accumulate_sites:
            # 去除 [] 并分割路径
            cleaned = raw_name.lstrip('[').rstrip(']')
            if not cleaned:
                raise ValueError("raw_name 格式不正确，去除括号后为空")

            raw_ids = cleaned.split('/')
            for raw_id_text in raw_ids:
                ids = []
                raw_id_text = raw_id_text.lstrip('[').rstrip(']')
                if not raw_id_text:
                    raise ValueError("raw_id 为空，无法处理")

                # 正则提取 ID + 名称
                match_last_id = re.match(
                    r'^([^\u4e00-\u9fa5]*?)([\u4e00-\u9fa5].*)$',
                    raw_id_text.strip(),
                    re.DOTALL
                )

                if match_last_id not in ['', None]:
                    # 更新 raw_ids[-1]，去掉中文名及标点
                    id_prefix = match_last_id.group(1).rstrip('.．').strip().rstrip(']')
                    ids.append(id_prefix)

                    # 获取最终中文名
                    raw_name = match_last_id.group(2).strip()
                    # 添加到列表
                    for raw_id in ids:
                        subitem_list.append({
                            "项目id": raw_id,
                            "项目名称": raw_name,
                            "描述": description,  # 确保 description 已定义
                            "参考码表": "",
                            "多子项目": False
                        })

        if has_multiple_sites or has_more_pros:
            query = raw_name + '\n' + description
            site_query = _get_all_site_prompt.replace('{subitem_input}', query)
            prompt_messages = [
                PromptMessage(role="user", content=site_query)
            ]
            qwen_llm_config["prompt_messages"] = prompt_messages

            llm_output = llm_interface.invoke(
                **qwen_llm_config
            )
            llm_output = extract_json_from_response(llm_output.message.content)
            subitem_list.append({
                "项目id": None,
                "项目名称": "",
                "描述": description,
                "参考码表": llm_output['影响法规编号'],
                "多子项目": True,
                "子项目列表": {"起始id": llm_output['影响编号起始id'], "结束id": llm_output['影响编号结束id']}
            })
        else:
            # 解析项目ID和名称：项目id是直到第一个中文字符前的所有字符
            raw_subitem = raw_name.lstrip('[').rstrip(']')
            if raw_subitem.startswith('其中'):
                raw_subitem = raw_subitem[2:].lstrip('\n').strip()
            match_id = re.match(r'^([^\u4e00-\u9fa5]*?)([\u4e00-\u9fa5].*)$', raw_subitem, re.DOTALL)

            if match_id:
                site_id = match_id.group(1).rstrip('.').rstrip('．').strip().rstrip(']').lstrip(':')
                site_name = match_id.group(2).strip()
            else:
                site_id = raw_subitem.rstrip('.').rstrip('．').strip().rstrip(']').lstrip(':')
                site_name = ""

            # 优化处理：检查描述前30个字符是否包含']'
            if description[:30].find(']') != -1:
                # 提取']'前的部分并拼接到项目名称
                split_idx = description.find(']') + 1
                name_append = description[:split_idx].strip().rstrip(']')
                site_name = f"{site_name}{name_append}".strip()
                # 更新描述，移除被拼接的部分
                description = description[split_idx:].strip().lstrip('：')

            # 检查项目ID是否为空且格式无效
            if not site_id and subitem_list and not raw_name.startswith('['):
                # 仅当raw_name不以'['开头（即非标准子项目格式）时合并
                last_item = subitem_list[-1]
                last_item["描述"] = f"{last_item['描述']}\n{raw_name}：{description}".strip()
                continue

            subitem_list.append({
                "项目id": site_id,
                "项目名称": site_name,
                "描述": description.lstrip('：'),
                "参考码表": "",
                "多子项目": False,
                "子项目列表": None
            })

    return {
        "表名": table_name,
        "具体说明": detail_content,
        "子项目列表": subitem_list
    }


# 测试用例
if __name__ == "__main__":
    data = '''
    
本表反映填报机构的表外业务情况。根据表外业务的产品特点,将其划分为担保承诺类、代理投融资服务类、中介服务类和其他类四种类型：
担保承诺类业务包括担保、承诺等按照约定承担偿付责任或提供信用服务的业务。担保类业务是指商业银行对第三方承担偿还责任的业务，包括但不限于银行承兑汇票、保函、信用证、信用风险仍由银行承担的销售与购买协议等。承诺类业务是指商业银行在未来某一日期按照事先约定的条件向客户提供约定的信用业务，包括但不限于贷款承诺等。
代理投融资服务类业务指商业银行根据客户委托，按照约定为客户提供投融资服务但不承担代偿责任、不承诺投资回报的表外业务，包括但不限于委托贷款、委托投资、代客理财、代理交易、代理发行和承销债券等。
中介服务类业务指商业银行根据客户委托，提供中介服务、收取手续费的业务，包括但不限于代理收付、代理代销、财务顾问、资产托管、各类保管业务等。
其他类表外业务是指上述业务种类之外的其他表外业务。
具体说明：
[1.承兑汇票]:指由出票人签发并向填报机构申请，经填报机构承兑的汇票。本项目填报期末余额。
[2.跟单信用证]:是指凭跟单汇票或仅凭单据付款的信用证。单据是指代表货物所有权的单据（如海运提单等），或证明货物已交运的单据（如铁路运单、航空运单、邮包收据）。本项目填报期末余额。
[2.1一年以内的跟单信用证]：是指凭跟单汇票或仅凭单据付款的、有效期在一年期以内（含一年）的信用证。单据是指代表货物所有权的单据（如海运提单等），或证明货物已交运的单据（如铁路运单、航空运单、邮包收据）。跟单信用证的期末余额是指银行进口贸易结算项下所有尚未履行完毕的付款或承兑义务，包括尚未到单付款的即期信用证余额、尚未到单承兑的远期信用证余额、已经承兑但尚未付款的信用证余额、以及因单据的争议尚在交涉未付款的信用证余额等。本项目填报期末余额。
[2.2一年以上的跟单信用证]：是指凭跟单汇票或仅凭单据付款的、有效期在一年期以上（不含一年）的信用证。本项目填报期末余额。
[3.保函]:是指银行应委托人的申请而开立的有担保性质的书面文件，一旦委托人未按其与受益人签订的合同的约定偿还债务或履行约定义务时，由银行履行担保责任。本项目填报期末余额。
[3.1融资性保函]：是指以资金融通为目的，填报机构为合约关系一方当事人（担保申请人），向合约关系的另一方当事人（担保受益人）开立的，当担保申请人出现违约时由填报机构承担偿还资金债务、还款担保责任的保函（法律性文书）。本项目填报期末余额。
融资性保函包括：借款保函、融资租赁保函、透支保函、有价证券发行担保、一年期以上的延期付款保函、以货币偿还的补偿贸易保函、银行授信额度保函和融资性备用信用证等。融资性备用信用证是指开证行保证在开证申请人未能履行其应履行的义务时，受益人只要凭备用信用证的规定向开证行开具汇票，并随附开证申请人未履行义务的声明或证明文件即可得到开证行偿付。保兑信用证也统计在此项目。
[3.2非融资性保函]：是指填报机构为客户贸易或工程投标等非融资性经营活动开具担保文书的保函。非融资性保函包括投标保函、履约保函、预付款保函、海事保函、质量保函、关税付款保函、工程维修保函、诉讼保函、提货担保保函等。本项目填报期末余额。
[4.信用风险仍在银行的销售与购买协议]:主要包括有追索权的资产销售和有追索权的买入资产。本项目填报期末余额。
[7.可随时无条件撤销的贷款承诺]：是指填报机构可在任何时候，且不需要事先通知，就可以无条件取消的贷款承诺。本项目填报期末余额。
[8.不可无条件撤销的贷款承诺]：是指有条件撤销或不可撤销的贷款承诺。本项目填报期末余额。
[9.未使用的信用卡授信额度]：是指填报机构对客户信用卡授信额度中，客户可以使用但尚未使用的授信额度。根据《商业银行信用卡业务监督管理办法》，信用卡是指记录持卡人账户信息，具备银行授信额度和透支功能，并为持卡人提供相关银行服务的各种介质，包括信用卡、贷记卡、准贷记卡。按照发行对象不同，分为个人卡和单位卡，其中，单位卡包括商务差旅卡和商务采购卡，个人卡包括对教育机构脱产就读的学生发放的信用卡。本项目填报期末余额。
[13.发行理财产品]：商业银行根据约定条件和实际投资收益情况向投资者支付收益，并不保证投资者本金安全的全部理财产品。本项目填报期末余额。
[14.委托贷款]：委托贷款是指填报机构接受客户或其他单位委托而发放的贷款。委托人提供资金，填报机构根据委托人确定的贷款对象、用途、金额、期限、利率等而代理发放、监督使用并协助收回。委托贷款的资金由委托人提供，填报机构不得代垫资金，其风险由委托人承担。本项目填报期末余额。
[14.1现金管理项下委托贷款] 指填报机构以委托贷款形式，开展现金管理业务，根据客户要求进行账户间资金归集划拨、余额调剂、资金计价、资金清算等业务发生的委托贷款。本项目填报期末余额。
[14.2金融机构委托贷款] 指金融机构作为委托人，委托填报机构发放的委托贷款。本项目填报期末余额。
[14.3非金融机构委托贷款] 指金融机构以外的单位或个人（包括广义政府、企业、个人等）作为委托人，委托填报机构发放的委托贷款。本项目填报期末余额。
[14.3.1其中：公积金委托贷款] 指各地住房公积金管理中心以住房公积金为资金来源，委托银行向缴存住房公积金的职工发放的定向用于购买、建造、翻建、大修住房或改善自住住房条件的住房消费贷款，也包括住房公积金管理中心委托银行向承担保障性住房建设的单位和项目发放的住房公积金贷款。本项目填报期末余额。
[15.委托投资] 本项目反映填报机构接受客户或其他单位委托而进行的投资。委托人提供资金，由填报机构根据委托人确定的投资对象、金额、期限等而代理进行的投资。委托投资的资金由委托人提供，填报机构不得代垫资金，其风险由委托人承担。本项目填报期末余额。本项目不含填报机构发行的表外理财产品。
[15.1金融机构委托投资] 反映委托人为金融机构的委托投资期末余额。
[15.2非金融机构委托投资] 反映委托人为除金融机构以外法人或个人的委托投资期末余额。
[16.代理交易] 指填报机构作为代理人，根据投资者委托，为投资者提供代理交易的业务。本项目及子项按照当年名义本金累计发生额进行填报。
[16.1代理金融衍生产品交易]指填报机构通过营业网点或电子银行系统等所有渠道为个人及法人客户办理的代理金融衍生产品交易业务。本项目填报币种为本外币折人民币。
[16.2代理贵金属交易]指填报机构通过营业网点或电子银行系统等所有渠道为个人及法人客户办理的贵金属账户、现货、递延、代理销售和回购等业务。本项目填报币种为本外币折人民币。
[17.代理发行和承销债券]是指报告期内填报机构作为承销商，接受发行人委托公开发售的各类债券的累计金额，包括面向社会公开、银行间市场发行等，本项目按实际承销份额的面值填报。
[20.代理代销业务]指填报机构受合作机构委托，通过本行渠道向客户推介销售由合作机构依法发行的金融产品的业务。本项目及子项按照当年累计发生额进行填报。
[20.1代理代销信托计划] 指代理推介信托计划并收付信托资金的业务，包括面向个人合格投资者和法人合格投资者的代理代销信托收付。
[20.2代理代销资产管理计划] 指代理推介证券、保险、基金公司及其子公司资产管理计划。
[20.3代理代销保险产品] 是指填报机构接受保险公司的委托，作为兼业代理人，在授权范围内代理保险公司向公司、机构客户和个人销售保险产品，并依法收取手续费的银保代理业务。
[20.4代理代销基金] 指填报机构通过其营业网点和电子银行系统接受投资人基金认购、申购等交易申请的业务，含基金专户，不含资产管理计划。
[20.5代理代销银行理财产品] 指填报机构通过其营业网点和电子银行系统接受投资人银行理财公司理财产品认购、申购等交易申请的业务。
[20.5.1代理代销本行理财公司产品] 指填报机构通过其营业网点和电子银行系统接受投资人对本行设立的理财公司理财产品认购、申购等交易申请的业务。
[20.5.2代理代销养老理财产品] 填报代理代销银行理财产品中的养老理财产品，养老理财产品指根据《关于开展养老理财产品试点的通知》、《关于扩大养老理财产品试点范围的通知》及其他相关规定开展的理财产品业务。
[20.6其他] 本项目反映报告期内填报机构代理销售除基金（含基金专户和资产管理计划）、保险、信托计划、银行理财产品之外金融产品的发生金额。
[21.资产托管] 是商业银行根据合同或协议约定，履行安全保管资产、资金清算、核算估值、投资监督及信息披露等职责，并提供与投资管理相关服务的业务。本项目填报期末余额。
[22.代理收付]指填报机构接受客户委托，根据客户提供的清单或凭证等，代理客户收取或发放款项的业务，按照代理收取款项、代理发放款项分别填报。
[23.保管业务]指填报机构接受客户委托，按照约定条件，代客户保管贵重物品、有价证券以及文件等财物的业务。
[24.财务顾问咨询] 本项目反映填报机构作为客户财务顾问，在切实分析客户实际需求的基础上，灵活运用各种金融知识、金融信息、金融工具、金融渠道和金融资源等，为客户提供有实质性服务内容和个性化特点的顾问服务及金融产品时所获得的收入。
[25.其他中介服务类业务] 反映填报机构开展的其他中介服务类业务的金额或收入。
[29.金融衍生品类（不含代理金融衍生产品交易）] 指价值取决于一种或多种基础资产或指数的金融合约，合约的基本种类包括远期、期货、掉期（互换）和期权等。衍生产品还包括具有远期、期货、掉期（互换）和期权中一种或多种特征的结构化金融工具。此项目填报名义本金期末余额，不包括计入[16.1代理金融衍生产品交易]的衍生品。
银行端收益是指上述各项表外业务对应产生的各项收入，包括手续费及佣金收入、其他业务收入等纳入当期损益表的收入，不考虑各项表外业务对应产生的支出。

[31.置换预售监管资金的保函]指填报机构向房企出具的，用于置换预售监管资金的保函。本项目填报期末余额。

[2.对境内委托贷款] 反映填报机构报告期末已发放的，使用地为除香港、澳门和台湾地区以外的中华人民共和国境内的委托贷款期末余额（非现金管理项下，下同）。
[2.1 - 2.20] 反映填报机构发放给境内法人，以及发放给境内个人用于经营目的的委托贷款期末余额。所列20个行业的分类标准，按照中华人民共和国国家标准GB/T4754-2011“国民经济行业分类”的标准执行，反映报告期末填报机构发放委托贷款的行业投向。如果无法合理地确定贷款的投向，则应按借款人主营业务所在行业进行分类。
[2.21个人贷款(不含个人经营性贷款)] 反映填报机构发放给境内个人，用于经营以外目的的委托贷款期末余额。
[2.21.3住房按揭贷款] 反映填报机构向个人借款人发放的住房按揭贷款，是为购买个人住房、并以此套住房为抵押的贷款。不包括以个人住房作抵押，用作其他用途的贷款、商业用房贷款以及商住两用房贷款。
[3.对境外委托贷款] 反映填报机构发放的使用地为中华人民共和国以外的国家和地区，以及香港、澳门和台湾地区的贷款期末余额。'''
print(process_table_content("table_name", data))

[{'表名': '表一：',
  '具体说明': '本表反映填报机构的表外业务情况。根据表外业务的产品特点,将其划分为担保承诺类、代理投融资服务类、中介服务类和其他类四种类型：\n担保承诺类业务包括担保、承诺等按照约定承担偿付责任或提供信用服务的业务。担保类业务是指商业银行对第三方承担偿还责任的业务，包括但不限于银行承兑汇票、保函、信用证、信用风险仍由银行承担的销售与购买协议等。承诺类业务是指商业银行在未来某一日期按照事先约定的条件向客户提供约定的信用业务，包括但不限于贷款承诺等。\n代理投融资服务类业务指商业银行根据客户委托，按照约定为客户提供投融资服务但不承担代偿责任、不承诺投资回报的表外业务，包括但不限于委托贷款、委托投资、代客理财、代理交易、代理发行和承销债券等。\n中介服务类业务指商业银行根据客户委托，提供中介服务、收取手续费的业务，包括但不限于代理收付、代理代销、财务顾问、资产托管、各类保管业务等。\n其他类表外业务是指上述业务种类之外的其他表外业务。',
  '子项目列表': [{'项目id': '1', '项目名称': '承兑汇票',
                  '描述': ':指由出票人签发并向填报机构申请，经填报机构承兑的汇票。本项目填报期末余额。', '参考码表': '',
                  '多子项目': False, '子项目列表': None}, {'项目id': '2', '项目名称': '跟单信用证',
                                                           '描述': ':是指凭跟单汇票或仅凭单据付款的信用证。单据是指代表货物所有权的单据（如海运提单等），或证明货物已交运的单据（如铁路运单、航空运单、邮包收据）。本项目填报期末余额。',
                                                           '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '2.1', '项目名称': '一年以内的跟单信用证',
                  '描述': '是指凭跟单汇票或仅凭单据付款的、有效期在一年期以内（含一年）的信用证。单据是指代表货物所有权的单据（如海运提单等），或证明货物已交运的单据（如铁路运单、航空运单、邮包收据）。跟单信用证的期末余额是指银行进口贸易结算项下所有尚未履行完毕的付款或承兑义务，包括尚未到单付款的即期信用证余额、尚未到单承兑的远期信用证余额、已经承兑但尚未付款的信用证余额、以及因单据的争议尚在交涉未付款的信用证余额等。本项目填报期末余额。',
                  '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '2.2', '项目名称': '一年以上的跟单信用证',
                  '描述': '是指凭跟单汇票或仅凭单据付款的、有效期在一年期以上（不含一年）的信用证。本项目填报期末余额。',
                  '参考码表': '', '多子项目': False, '子项目列表': None}, {'项目id': '3', '项目名称': '保函',
                                                                           '描述': ':是指银行应委托人的申请而开立的有担保性质的书面文件，一旦委托人未按其与受益人签订的合同的约定偿还债务或履行约定义务时，由银行履行担保责任。本项目填报期末余额。',
                                                                           '参考码表': '', '多子项目': False,
                                                                           '子项目列表': None},
                 {'项目id': '3.1', '项目名称': '融资性保函',
                  '描述': '是指以资金融通为目的，填报机构为合约关系一方当事人（担保申请人），向合约关系的另一方当事人（担保受益人）开立的，当担保申请人出现违约时由填报机构承担偿还资金债务、还款担保责任的保函（法律性文书）。本项目填报期末余额。\n融资性保函包括：借款保函、融资租赁保函、透支保函、有价证券发行担保、一年期以上的延期付款保函、以货币偿还的补偿贸易保函、银行授信额度保函和融资性备用信用证等。融资性备用信用证是指开证行保证在开证申请人未能履行其应履行的义务时，受益人只要凭备用信用证的规定向开证行开具汇票，并随附开证申请人未履行义务的声明或证明文件即可得到开证行偿付。保兑信用证也统计在此项目。',
                  '参考码表': '', '多子项目': False, '子项目列表': None}, {'项目id': '3.2', '项目名称': '非融资性保函',
                                                                           '描述': '是指填报机构为客户贸易或工程投标等非融资性经营活动开具担保文书的保函。非融资性保函包括投标保函、履约保函、预付款保函、海事保函、质量保函、关税付款保函、工程维修保函、诉讼保函、提货担保保函等。本项目填报期末余额。',
                                                                           '参考码表': '', '多子项目': False,
                                                                           '子项目列表': None},
                 {'项目id': '4', '项目名称': '信用风险仍在银行的销售与购买协议',
                  '描述': ':主要包括有追索权的资产销售和有追索权的买入资产。本项目填报期末余额。', '参考码表': '',
                  '多子项目': False, '子项目列表': None}, {'项目id': '7', '项目名称': '可随时无条件撤销的贷款承诺',
                                                           '描述': '是指填报机构可在任何时候，且不需要事先通知，就可以无条件取消的贷款承诺。本项目填报期末余额。',
                                                           '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '8', '项目名称': '不可无条件撤销的贷款承诺',
                  '描述': '是指有条件撤销或不可撤销的贷款承诺。本项目填报期末余额。', '参考码表': '', '多子项目': False,
                  '子项目列表': None}, {'项目id': '9', '项目名称': '未使用的信用卡授信额度',
                                        '描述': '是指填报机构对客户信用卡授信额度中，客户可以使用但尚未使用的授信额度。根据《商业银行信用卡业务监督管理办法》，信用卡是指记录持卡人账户信息，具备银行授信额度和透支功能，并为持卡人提供相关银行服务的各种介质，包括信用卡、贷记卡、准贷记卡。按照发行对象不同，分为个人卡和单位卡，单位卡包括商务差旅卡和商务采购卡，个人卡包括对教育机构脱产就读的学生发放的信用卡。本项目填报期末余额。',
                                        '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '13', '项目名称': '发行理财产品',
                  '描述': '商业银行根据约定条件和实际投资收益情况向投资者支付收益，并不保证投资者本金安全的全部理财产品。本项目填报期末余额。',
                  '参考码表': '', '多子项目': False, '子项目列表': None}, {'项目id': '14', '项目名称': '委托贷款',
                                                                           '描述': '委托贷款是指填报机构接受客户或其他单位委托而发放的贷款。委托人提供资金，填报机构根据委托人确定的贷款对象、用途、金额、期限、利率等而代理发放、监督使用并协助收回。委托贷款的资金由委托人提供，填报机构不得代垫资金，其风险由委托人承担。本项目填报期末余额。',
                                                                           '参考码表': '', '多子项目': False,
                                                                           '子项目列表': None},
                 {'项目id': '14.1', '项目名称': '现金管理项下委托贷款',
                  '描述': '指填报机构以委托贷款形式，开展现金管理业务，根据客户要求进行账户间资金归集划拨、余额调剂、资金计价、资金清算等业务发生的委托贷款。本项目填报期末余额。',
                  '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '14.2', '项目名称': '金融机构委托贷款',
                  '描述': '指金融机构作为委托人，委托填报机构发放的委托贷款。本项目填报期末余额。', '参考码表': '',
                  '多子项目': False, '子项目列表': None}, {'项目id': '14.3', '项目名称': '非金融机构委托贷款',
                                                           '描述': '指金融机构以外的单位或个人（包括广义政府、企业、个人等）作为委托人，委托填报机构发放的委托贷款。本项目填报期末余额。',
                                                           '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '14.3.1', '项目名称': '其中:公积金委托贷款',
                  '描述': '指各地住房公积金管理中心以住房公积金为资金来源，委托银行向缴存住房公积金的职工发放的定向用于购买、建造、翻建、大修住房或改善自住住房条件的住房消费贷款，也包括住房公积金管理中心委托银行向承担保障性住房建设的单位和项目发放的住房公积金贷款。本项目填报期末余额。',
                  '参考码表': '', '多子项目': False, '子项目列表': None}, {'项目id': '15', '项目名称': '委托投资',
                                                                           '描述': '本项目反映填报机构接受客户或其他单位委托而进行的投资。委托人提供资金，由填报机构根据委托人确定的投资对象、金额、期限等而代理进行的投资。委托投资的资金由委托人提供，填报机构不得代垫资金，其风险由委托人承担。本项目填报期末余额。本项目不含填报机构发行的表外理财产品。',
                                                                           '参考码表': '', '多子项目': False,
                                                                           '子项目列表': None},
                 {'项目id': '15.1', '项目名称': '金融机构委托投资', '描述': '反映委托人为金融机构的委托投资期末余额。',
                  '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '15.2', '项目名称': '非金融机构委托投资',
                  '描述': '反映委托人为除金融机构以外法人或个人的委托投资期末余额。', '参考码表': '', '多子项目': False,
                  '子项目列表': None}, {'项目id': '16', '项目名称': '代理交易',
                                        '描述': '指填报机构作为代理人，根据投资者委托，为投资者提供代理交易的业务。本项目及子项按照当年名义本金累计发生额进行填报。',
                                        '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '16.1', '项目名称': '代理金融衍生产品交易',
                  '描述': '指填报机构通过营业网点或电子银行系统等所有渠道为个人及法人客户办理的代理金融衍生产品交易业务。本项目填报币种为本外币折人民币。',
                  '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '16.2', '项目名称': '代理贵金属交易',
                  '描述': '指填报机构通过营业网点或电子银行系统等所有渠道为个人及法人客户办理的贵金属账户、现货、递延、代理销售和回购等业务。本项目填报币种为本外币折人民币。',
                  '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '17', '项目名称': '代理发行和承销债券',
                  '描述': '是指报告期内填报机构作为承销商，接受发行人委托公开发售的各类债券的累计金额，包括面向社会公开、银行间市场发行等，本项目按实际承销份额的面值填报。',
                  '参考码表': '', '多子项目': False, '子项目列表': None}, {'项目id': '20', '项目名称': '代理代销业务',
                                                                           '描述': '指填报机构受合作机构委托，通过本行渠道向客户推介销售由合作机构依法发行的金融产品的业务。本项目及子项按照当年累计发生额进行填报。',
                                                                           '参考码表': '', '多子项目': False,
                                                                           '子项目列表': None},
                 {'项目id': '20.1', '项目名称': '代理代销信托计划',
                  '描述': '指代理推介信托计划并收付信托资金的业务，包括面向个人合格投资者和法人合格投资者的代理代销信托收付。',
                  '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '20.2', '项目名称': '代理代销资产管理计划',
                  '描述': '指代理推介证券、保险、基金公司及其子公司资产管理计划。', '参考码表': '', '多子项目': False,
                  '子项目列表': None}, {'项目id': '20.3', '项目名称': '代理代销保险产品',
                                        '描述': '是指填报机构接受保险公司的委托，作为兼业代理人，在授权范围内代理保险公司向公司、机构客户和个人销售保险产品，并依法收取手续费的银保代理业务。',
                                        '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '20.4', '项目名称': '代理代销基金',
                  '描述': '指填报机构通过其营业网点和电子银行系统接受投资人基金认购、申购等交易申请的业务，含基金专户，不含资产管理计划。',
                  '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '20.5', '项目名称': '代理代销银行理财产品',
                  '描述': '指填报机构通过其营业网点和电子银行系统接受投资人银行理财公司理财产品认购、申购等交易申请的业务。',
                  '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '20.5.1', '项目名称': '代理代销本行理财公司产品',
                  '描述': '指填报机构通过其营业网点和电子银行系统接受投资人对本行设立的理财公司理财产品认购、申购等交易申请的业务。',
                  '参考码表': '', '多子项目': False, '子项目列表': None},
                 {'项目id': '20.5.2', '项目名称': '代理代销养老理财产品',
                  '描述': '填报代理代销银行理财产品中的养老理财产品，养老理财产品指根据《关于开展养老理财产品试点的通知》、《关于扩大养老理财产品试点范围的通知》及其他相关规定开展的理财产品业务。',
                  '参考码表': '', '多子项目': False, '子项目列表': None}, {'项目id': '20.6', '项目名称': '其他',
                                                                           '描述': '本项目反映报告期内填报机构代理销售除基金（含基金专户和资产管理计划）、保险、信托计划、银行理财产品之外金融产品的发生金额。',
                                                                           '参考码表': '', '多子项目': False,
                                                                           '子项目列表': None},
                 {'项目id': '21', '项目名称': '资产托管',
                  '描述': '是商业银行根据合同或协议约定，履行安全保管资产、资金清算、核算估值、投资监督及信息披露等职责，并提供与投资管理相关服务的业务。本项目填报期末余额。',
                  '参考码表': '', '多子项目': False, '子项目列表': None}, {'项目id': '22', '项目名称': '代理收付',
                                                                           '描述': '指填报机构接受客户委托，根据客户提供的清单或凭证等，代理客户收取或发放款项的业务，按照代理收取款项、代理发放款项分别填报。',
                                                                           '参考码表': '', '多子项目': False,
                                                                           '子项目列表': None},
                 {'项目id': '23', '项目名称': '保管业务',
                  '描述': '指填报机构接受客户委托，按照约定条件，代客户保管贵重物品、有价证券以及文件等财物的业务。',
                  '参考码表': '', '多子项目': False, '子项目列表': None}, {'项目id': '24', '项目名称': '财务顾问咨询',
                                                                           '描述': '本项目反映填报机构作为客户财务顾问，在切实分析客户实际需求的基础上，灵活运用各种金融知识、金融信息、金融工具、金融渠道和金融资源等，为客户提供有实质性服务内容和个性化特点的顾问服务及金融产品时所获得的收入。',
                                                                           '参考码表': '', '多子项目': False,
                                                                           '子项目列表': None},
                 {'项目id': '25', '项目名称': '其他中介服务类业务',
                  '描述': '反映填报机构开展的其他中介服务类业务的金额或收入。', '参考码表': '', '多子项目': False,
                  '子项目列表': None}, {'项目id': '29', '项目名称': '金融衍生品类（不含代理金融衍生产品交易）',
                                        '描述': '指价值取决于一种或多种基础资产或指数的金融合约，合约的基本种类包括远期、期货、掉期（互换）和期权等。衍生产品还包括具有远期、期货、掉期（互换）和期权中一种或多种特征的结构化金融工具。此项目填报名义本金期末余额，不包括计入[16.1代理金融衍生产品交易]：的衍生品。\n银行端收益是指上述各项表外业务对应产生的各项收入，包括手续费及佣金收入、其他业务收入等纳入当期损益表的收入，不考虑各项表外业务对应产生的支出。',
                                        '参考码表': '', '多子项目': False, '子项目列表': None}]},
 {'表名': '附注项目：', '具体说明': '', '子项目列表': [{'项目id': '31', '项目名称': '置换预售监管资金的保函',
                                                       '描述': '指填报机构向房企出具的，用于置换预售监管资金的保函。本项目填报期末余额。',
                                                       '参考码表': '', '多子项目': False, '子项目列表': None}]},
 {'表名': '表二：', '具体说明': '', '子项目列表': [{'项目id': '2', '项目名称': '对境内委托贷款',
                                                   '描述': '反映填报机构报告期末已发放的，使用地为除香港、澳门和台湾地区以外的中华人民共和国境内的委托贷款期末余额（非现金管理项下，下同）。',
                                                   '参考码表': '', '多子项目': False, '子项目列表': None},
                                                  {'项目id': None, '项目名称': '',
                                                   '描述': '反映填报机构发放给境内法人，以及发放给境内个人用于经营目的的委托贷款期末余额。所列20个行业的分类标准，按照中华人民共和国国家标准GB/T4754-2011“国民经济行业分类”的标准执行，反映报告期末填报机构发放委托贷款的行业投向。如果无法合理地确定贷款的投向，则应按借款人主营业务所在行业进行分类。',
                                                   '参考码表': 'GB/T4754-2011', '多子项目': True,
                                                   '子项目列表': {'起始id': '2.1', '结束id': '2.20'}},
                                                  {'项目id': '2.21', '项目名称': '个人贷款(不含个人经营性贷款)',
                                                   '描述': '反映填报机构发放给境内个人，用于经营以外目的的委托贷款期末余额。',
                                                   '参考码表': '', '多子项目': False, '子项目列表': None},
                                                  {'项目id': '2.21.3', '项目名称': '住房按揭贷款',
                                                   '描述': '反映填报机构向个人借款人发放的住房按揭贷款，是为购买个人住房、并以此套住房为抵押的贷款。不包括以个人住房作抵押，用作其他用途的贷款、商业用房贷款以及商住两用房贷款。',
                                                   '参考码表': '', '多子项目': False, '子项目列表': None},
                                                  {'项目id': '3', '项目名称': '对境外委托贷款',
                                                   '描述': '反映填报机构发放的使用地为中华人民共和国以外的国家和地区，以及香港、澳门和台湾地区的贷款期末余额。',
                                                   '参考码表': '', '多子项目': False, '子项目列表': None}]}]

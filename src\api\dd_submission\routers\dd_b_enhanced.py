"""
DD-B增强处理API路由

提供DD-B模块的异步数据处理功能，包括：
- 立即验证和响应
- 后台异步处理（消息队列模式）
- 批量处理（15条/批次）
- 前端回调通知
"""

import asyncio
import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse

from ..models.request_models import DDBEnhancedProcessRequest
from ..models.response_models import (
    DDBEnhancedProcessResponse,
    DDBCallbackRequest,
    DDBCallbackItem
)
from service import get_client
from ..config import dd_b_config

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/dd-b",
    tags=["DD-B增强处理"],
    responses={
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)


async def get_database_clients():
    """获取数据库客户端依赖"""
    try:
        # 获取必需的MySQL客户端
        rdb_client = await get_client('database.rdbs.mysql')
        
        # 尝试获取可选的向量数据库和embedding客户端
        vdb_client = None
        embedding_client = None
        
        try:
            vdb_client = await get_client('database.vdbs.pgvector')
        except Exception as e:
            logger.warning(f"PGVector客户端获取失败: {e}")
        
        try:
            embedding_client = await get_client('model.embeddings.moka-m3e-base')
        except Exception as e:
            logger.warning(f"Embedding客户端获取失败: {e}")
        
        return {
            'rdb_client': rdb_client,
            'vdb_client': vdb_client,
            'embedding_client': embedding_client
        }
        
    except Exception as e:
        logger.error(f"数据库客户端获取失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"数据库连接失败: {str(e)}"
        )


async def validate_data_exists(rdb_client, report_code: str, dept_id: str) -> Dict[str, Any]:
    """验证数据是否存在（使用DD CRUD优化查询）"""
    try:
        # 使用DD CRUD进行数据验证
        from modules.knowledge.dd.crud import DDCrud
        from modules.dd_submission.dd_b.infrastructure.constants import DDBUtils

        dd_crud = DDCrud(rdb_client=rdb_client)

        # 构建查询条件（包含字段映射）
        conditions = DDBUtils.build_query_conditions(report_code, dept_id)
        logger.info(f"数据验证查询条件: {conditions}")

        # 使用批量查询方法验证数据存在性
        conditions_list = [conditions]
        records = await dd_crud.batch_query_post_distributions(
            conditions_list=conditions_list,
            batch_size=1,
            max_concurrency=1,
            timeout_per_batch=30.0
        )

        if records and len(records) > 0:
            logger.info(f"数据验证成功: 找到 {len(records)} 条记录")
            return {
                "exists": True,
                "count": len(records),
                "error": None
            }
        else:
            logger.warning(f"数据验证失败: 未找到匹配记录，条件={conditions}")
            return {
                "exists": False,
                "count": 0,
                "error": "无法在数据库里搜索到相关信息"
            }

    except Exception as e:
        logger.error(f"数据验证异常: {e}")
        return {
            "exists": False,
            "count": 0,
            "error": f"数据库查询失败: {str(e)}"
        }


async def process_dd_b_background(
    report_code: str,
    dept_id: str,
    rdb_client,
    vdb_client,
    embedding_client
):
    """后台异步处理DD-B数据"""
    try:
        logger.info(f"开始后台处理DD-B: report_code={report_code}, dept_id={dept_id}")

        # 导入新的DD-B核心处理器
        from modules.dd_submission.dd_b.dd_b_core_processor import DDBCoreProcessor

        # 创建DD-B核心处理器
        processor = DDBCoreProcessor(
            rdb_client=rdb_client,
            vdb_client=vdb_client,
            embedding_client=embedding_client,
            batch_size=15,  # 15条/批次
            enable_checkpoint=True
        )

        # 执行核心处理逻辑
        result = await processor.process_core_logic(
            report_code=report_code,
            dept_id=dept_id
        )

        logger.info(f"后台处理完成: 处理{result.processed_records}条记录，成功率{result.success_rate:.1f}%")

            # 构建回调数据
            callback_items = []
            for record in result.final_data:
                # 根据记录类型确定entry_type
                entry_type = "TABLE" if record.get('submission_type') == 'RANGE' else "ITEM"

                # 构建BDR字段数据（只包含BDR05-BDR17）
                callback_item = DDBCallbackItem(
                    entry_id=record.get('record_id'),
                    entry_type=entry_type,
                    # 只传递BDR05-BDR17字段
                    BDR01=None,  # 不传递
                    BDR02=None,  # 不传递
                    BDR03=None,  # 不传递
                    BDR04=None,  # 不传递
                    BDR05=record.get('bdr05'),
                    BDR06=record.get('bdr06'),
                    BDR07=record.get('bdr07'),
                    BDR08=record.get('bdr08'),
                    BDR09=record.get('bdr09'),
                    BDR10=record.get('bdr10'),
                    BDR11=record.get('bdr11'),
                    BDR12=record.get('bdr12'),
                    BDR13=record.get('bdr13'),
                    BDR14=record.get('bdr14'),
                    BDR15=record.get('bdr15'),
                    BDR16=record.get('bdr16'),
                    BDR17=record.get('bdr17'),
                    BDR18=None,  # 不传递
                    BDR19=None   # 不传递
                )
                callback_items.append(callback_item)

            # 构建回调请求
            callback_request = DDBCallbackRequest(
                report_code=report_code,
                dept_id=dept_id,
                item=callback_items
            )

            # 调用前端回调接口
            await call_frontend_callback(callback_request)

    except Exception as e:
        logger.error(f"后台处理失败: {e}")
        # 即使失败也要通知前端
        try:
            callback_request = DDBCallbackRequest(
                report_code=report_code,
                dept_id=dept_id,
                item=[]
            )
            await call_frontend_callback(callback_request)
        except:
            pass


async def call_frontend_callback(callback_request: DDBCallbackRequest):
    """调用前端回调接口"""
    try:
        import httpx

        # 使用配置的前端回调URL
        callback_url = dd_b_config.get_frontend_callback_url()
        logger.info(f"调用前端回调: {callback_url}")

        async with httpx.AsyncClient(timeout=dd_b_config.CALLBACK_TIMEOUT) as client:
            response = await client.post(
                callback_url,
                json=callback_request.model_dump()
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    logger.info(f"前端回调成功: {result.get('msg')}")
                else:
                    logger.warning(f"前端回调返回错误: {result}")
            else:
                logger.error(f"前端回调失败: HTTP {response.status_code}")

    except Exception as e:
        logger.error(f"前端回调异常: {e}")


@router.post(
    "/process",
    response_model=DDBEnhancedProcessResponse,
    summary="DD-B增强数据处理（异步模式）",
    description="立即验证数据存在性并返回状态，后台异步处理数据并回调前端"
)
async def process_dd_b_enhanced(
    request: DDBEnhancedProcessRequest,
    background_tasks: BackgroundTasks,
    clients: Dict[str, Any] = Depends(get_database_clients)
) -> DDBEnhancedProcessResponse:
    """
    DD-B增强数据处理接口（异步模式）

    处理流程：
    1. 立即验证数据存在性
    2. 返回处理状态（成功/失败）
    3. 后台异步处理（消息队列模式，15条/批次）
    4. 处理完成后回调前端接口

    前端回调格式：
    {
        "report_code": "G0107_beta_v1.0",
        "dept_id": "部门id",
        "item": [
            {
                "entry_id": "submission_id",
                "entry_type": "TABLE/ITEM",
                "BDR05": "...",
                "BDR19": "..."
            }
        ]
    }
    """
    try:
        logger.info(f"收到DD-B处理请求: report_code={request.report_code}, dept_id={request.dept_id}")

        # 1. 立即验证数据存在性
        validation_result = await validate_data_exists(
            clients['rdb_client'],
            request.report_code,
            request.dept_id
        )

        if not validation_result["exists"]:
            # 数据不存在，立即返回错误
            logger.warning(f"数据验证失败: {validation_result['error']}")
            return DDBEnhancedProcessResponse(
                code="400",
                msg="无法在数据库里搜索到相关信息"
            )

        logger.info(f"数据验证通过，找到{validation_result['count']}条记录")

        # 2. 启动后台异步处理
        background_tasks.add_task(
            process_dd_b_background,
            request.report_code,
            request.dept_id,
            clients['rdb_client'],
            clients['vdb_client'],
            clients['embedding_client']
        )

        # 3. 立即返回处理中状态
        return DDBEnhancedProcessResponse(
            code="0",
            msg="业务信息正在处理中"
        )

    except Exception as e:
        logger.error(f"DD-B处理请求失败: {e}")
        return DDBEnhancedProcessResponse(
            code="400",
            msg=f"处理失败: {str(e)}"
        )





@router.get(
    "/health",
    summary="DD-B模块健康检查",
    description="检查DD-B模块的服务状态和依赖"
)
async def health_check():
    """DD-B模块健康检查"""
    try:
        # 检查数据库连接
        clients = await get_database_clients()
        
        # 检查DD-B模块导入
        from modules.dd_submission.dd_b import create_enhanced_data_processor
        
        health_status = {
            "status": "healthy",
            "mysql_client": "connected" if clients['rdb_client'] else "disconnected",
            "pgvector_client": "connected" if clients['vdb_client'] else "optional",
            "embedding_client": "connected" if clients['embedding_client'] else "optional",
            "dd_b_module": "available"
        }
        
        return JSONResponse(
            status_code=200,
            content={
                "code": "0",
                "msg": "DD-B模块运行正常",
                "data": health_status
            }
        )
    
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "code": "500",
                "msg": f"DD-B模块异常: {str(e)}",
                "data": {"status": "unhealthy"}
            }
        )


@router.post(
    "/config/callback-url",
    summary="设置前端回调URL",
    description="动态设置前端回调URL"
)
async def set_callback_url(callback_url: str):
    """设置前端回调URL"""
    try:
        dd_b_config.set_frontend_callback_url(callback_url)
        logger.info(f"前端回调URL已更新: {callback_url}")

        return JSONResponse(
            status_code=200,
            content={
                "code": "0",
                "msg": "回调URL设置成功",
                "data": {"callback_url": callback_url}
            }
        )

    except Exception as e:
        logger.error(f"设置回调URL失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "code": "500",
                "msg": f"设置失败: {str(e)}",
                "data": None
            }
        )


@router.get(
    "/config",
    summary="获取DD-B配置",
    description="获取当前DD-B模块配置信息"
)
async def get_config():
    """获取DD-B配置"""
    try:
        config_info = {
            "frontend_callback_url": dd_b_config.get_frontend_callback_url(),
            "batch_size": dd_b_config.BATCH_SIZE,
            "max_llm_concurrent": dd_b_config.MAX_LLM_CONCURRENT,
            "callback_timeout": dd_b_config.CALLBACK_TIMEOUT,
            "callback_retry_times": dd_b_config.CALLBACK_RETRY_TIMES
        }

        return JSONResponse(
            status_code=200,
            content={
                "code": "0",
                "msg": "配置获取成功",
                "data": config_info
            }
        )

    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "code": "500",
                "msg": f"获取配置失败: {str(e)}",
                "data": None
            }
        )

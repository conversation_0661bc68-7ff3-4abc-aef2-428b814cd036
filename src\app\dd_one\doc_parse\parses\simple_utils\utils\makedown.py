from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.config.parser import ConfigParser

# test_path='D:\ideal\金融数智\项目\外部项目\汇丰\资料\G01_VII（231版）.xls'
# test_path1='D:\ideal\金融数智\项目\外部项目\汇丰\外规内化\汇丰中国突发事件及重大事项内部报告指引 V9.0.pdf'
# test_path2='D:\ideal\金融数智\项目\外部项目\汇丰\外规内化\金规〔2024〕24号 .pdf'
# test_path3='D:\ideal\金融数智\项目\外部项目\汇丰\外规内化\中国银保监会关于印发银行业保险业突发事件信息报告办法的通知.tif'

from PIL import Image

import json
import os


def convert_tiff_to_pdf(input_path, output_path):
    with Image.open(input_path) as img:
        # 获取 TIFF 文件中的所有页面
        images = []
        while True:
            images.append(img.copy())
            try:
                img.seek(img.tell() + 1)
            except EOFError:
                break
        images = [image.convert("RGB") for image in images]
        images[0].save(output_path, "PDF", save_all=True, append_images=images[1:])
        print(f"Converted TIFF to PDF: {output_path}")
        # return output_path


def get_file_extension(file_path):
    return os.path.splitext(file_path)[1].lower()


def initialize_converter(config):
    config_parser = ConfigParser(config)
    converter = PdfConverter(
        config=config_parser.generate_config_dict(),
        artifact_dict=create_model_dict(),
        processor_list=config_parser.get_processors(),
        renderer=config_parser.get_renderer(),
        llm_service=config_parser.get_llm_service(),
    )
    return converter


class FileProcessor:
    def __init__(self, config):
        """
        初始化 FileProcessor。
        """
        self.config = config
        self.converter = initialize_converter(self.config)

    def process_file(self, input_path):
        file_extension = get_file_extension(input_path)
        if file_extension in [".tiff", ".tif"]:
            output_pdf_path = "output.pdf"
            convert_tiff_to_pdf(input_path, output_pdf_path)
            print(f"Converted TIFF to PDF: {output_pdf_path}")
            return self.converter(output_pdf_path)
        elif file_extension == ".pdf":
            return self.converter(input_path)
        else:
            raise ValueError(f"Unsupported file type: {file_extension}")


if __name__ == '__main__':
    config = {
        "output_format": "markdown",
        "ADDITIONAL_KEY": "VALUE"
    }
    processor = FileProcessor(config)
    path = r"/data/ideal/code/hr_code/test_dd_file/file/pdf/金发〔2023〕16号 国家金融监督管理总局关于做好2024年银行业非现场监管报表填报工作的通知.pdf"

    try:
        # 处理文件并获取结果
        result = processor.process_file(path).markdown

        # 保存结果到 txt 文件，保留换行符
        output_file = "output_result.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result)

        print(f"结果已成功保存至 {output_file}")

    except Exception as e:
        print(f"An error occurred: {e}")

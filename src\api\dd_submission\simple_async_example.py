#!/usr/bin/env python3
"""
DD-B异步处理API简单使用示例

展示异步处理模式的完整流程
"""

import asyncio
import json
import httpx


async def simple_async_dd_b_example():
    """简单的DD-B异步API调用示例"""
    
    # API配置
    BASE_URL = "http://localhost:30337"
    
    # 请求参数（您只需要修改这两个参数）
    request_data = {
        "report_code": "G0107_beta_v1.0",  # 您的报表代码
        "dept_id": "30239"                 # 您的部门ID
    }
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            print("🚀 开始调用DD-B异步处理API...")
            print(f"📋 请求参数: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
            
            # 1. 设置前端回调URL（可选）
            callback_url = "http://your-frontend/api/dd-b/callback"
            try:
                await client.post(
                    f"{BASE_URL}/api/dd/dd-b/config/callback-url",
                    params={"callback_url": callback_url}
                )
                print(f"✅ 回调URL已设置: {callback_url}")
            except:
                print("⚠️ 回调URL设置失败，使用默认配置")
            
            # 2. 调用异步处理API
            response = await client.post(
                f"{BASE_URL}/api/dd/dd-b/process",
                json=request_data
            )
            
            # 3. 处理立即响应
            if response.status_code == 200:
                result = response.json()
                print("✅ API调用成功!")
                print(f"📊 立即响应:")
                print(f"  状态码: {result.get('code')}")
                print(f"  消息: {result.get('msg')}")
                
                if result.get('code') == '0':
                    print("🔄 数据存在，后台正在异步处理...")
                    print("📞 处理完成后将回调前端接口")
                    return True
                elif result.get('code') == '400':
                    print("❌ 数据不存在，无法处理")
                    return False
                else:
                    print(f"⚠️ 未知状态码: {result.get('code')}")
                    return False
            
            else:
                print(f"❌ API调用失败: HTTP {response.status_code}")
                print(f"错误响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 调用异常: {e}")
            return False


async def mock_frontend_callback_server():
    """模拟前端回调服务器"""
    from fastapi import FastAPI, Request
    import uvicorn
    
    app = FastAPI()
    
    @app.post("/api/dd-b/callback")
    async def receive_callback(request: Request):
        """接收DD-B回调"""
        try:
            callback_data = await request.json()
            print("\n📞 收到DD-B回调:")
            print(f"📋 回调数据: {json.dumps(callback_data, indent=2, ensure_ascii=False)}")
            
            # 处理回调数据
            report_code = callback_data.get("report_code")
            dept_id = callback_data.get("dept_id")
            items = callback_data.get("item", [])
            
            print(f"  报表代码: {report_code}")
            print(f"  部门ID: {dept_id}")
            print(f"  处理项数: {len(items)}")
            
            # 显示前几个处理项
            for i, item in enumerate(items[:3]):
                print(f"  项目{i+1}: {item.get('entry_id')} ({item.get('entry_type')})")
            
            if len(items) > 3:
                print(f"  ... 还有 {len(items) - 3} 个项目")
            
            # 返回成功响应
            return {
                "code": 200,
                "msg": "操作成功",
                "data": False
            }
        
        except Exception as e:
            print(f"❌ 回调处理失败: {e}")
            return {
                "code": 500,
                "msg": f"处理失败: {str(e)}",
                "data": False
            }
    
    # 启动模拟服务器
    print("🖥️ 启动模拟前端回调服务器...")
    print("📍 回调地址: http://localhost:8001/api/dd-b/callback")
    
    config = uvicorn.Config(app, host="0.0.0.0", port=8001, log_level="info")
    server = uvicorn.Server(config)
    await server.serve()


async def test_config_api():
    """测试配置API"""
    BASE_URL = "http://localhost:30337"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            print("🔧 测试配置API...")
            
            # 获取当前配置
            response = await client.get(f"{BASE_URL}/api/dd/dd-b/config")
            if response.status_code == 200:
                config = response.json()
                print(f"📋 当前配置: {json.dumps(config, indent=2, ensure_ascii=False)}")
                return True
            else:
                print(f"❌ 获取配置失败: {response.status_code}")
                return False
        
        except Exception as e:
            print(f"❌ 配置API测试失败: {e}")
            return False


async def main():
    """主函数"""
    print("🎯 DD-B异步处理API使用示例")
    print("=" * 50)
    
    # 选择运行模式
    print("\n请选择运行模式:")
    print("1. 测试异步处理API")
    print("2. 启动模拟前端回调服务器")
    print("3. 测试配置API")
    
    try:
        choice = input("请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            print("\n📋 运行异步处理API测试...")
            success = await simple_async_dd_b_example()
            if success:
                print("\n🎉 异步API调用示例完成!")
            else:
                print("\n❌ 异步API调用失败")
        
        elif choice == "2":
            print("\n📋 启动模拟前端回调服务器...")
            await mock_frontend_callback_server()
        
        elif choice == "3":
            print("\n📋 运行配置API测试...")
            success = await test_config_api()
            if success:
                print("\n🎉 配置API测试完成!")
            else:
                print("\n❌ 配置API测试失败")
        
        else:
            print("❌ 无效选择")
            return False
        
        return True
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出程序")
        return True
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        return False


if __name__ == "__main__":
    # 运行示例
    success = asyncio.run(main())
    exit(0 if success else 1)

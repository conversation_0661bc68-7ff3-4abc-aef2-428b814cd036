"""
部门职责分配异常定义

定义义务解读部门职责分配相关的异常类
"""

from typing import Optional, Dict, Any


class DepartmentAssignmentError(Exception):
    """部门职责分配基础异常"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "DEPT_ASSIGNMENT_ERROR"
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class DepartmentAssignmentValidationError(DepartmentAssignmentError):
    """部门职责分配参数验证异常"""
    
    def __init__(
        self, 
        message: str, 
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        **kwargs
    ):
        super().__init__(
            message, 
            error_code="DEPT_ASSIGNMENT_VALIDATION_ERROR", 
            **kwargs
        )
        self.field_name = field_name
        self.field_value = field_value


class DepartmentAssignmentSearchError(DepartmentAssignmentError):
    """部门职责分配搜索异常"""
    
    def __init__(
        self, 
        message: str, 
        search_type: Optional[str] = None,
        search_params: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        super().__init__(
            message, 
            error_code="DEPT_ASSIGNMENT_SEARCH_ERROR", 
            **kwargs
        )
        self.search_type = search_type
        self.search_params = search_params or {}


class DepartmentAssignmentFilterError(DepartmentAssignmentError):
    """部门职责分配筛选异常"""
    
    def __init__(
        self, 
        message: str, 
        filter_layer: Optional[str] = None,
        filter_criteria: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        super().__init__(
            message, 
            error_code="DEPT_ASSIGNMENT_FILTER_ERROR", 
            **kwargs
        )
        self.filter_layer = filter_layer
        self.filter_criteria = filter_criteria or {}


class DepartmentAssignmentDatabaseError(DepartmentAssignmentError):
    """部门职责分配数据库异常"""
    
    def __init__(
        self, 
        message: str, 
        operation: Optional[str] = None,
        table_name: Optional[str] = None,
        **kwargs
    ):
        super().__init__(
            message, 
            error_code="DEPT_ASSIGNMENT_DATABASE_ERROR", 
            **kwargs
        )
        self.operation = operation
        self.table_name = table_name


class DepartmentAssignmentConfigError(DepartmentAssignmentError):
    """部门职责分配配置异常"""
    
    def __init__(
        self, 
        message: str, 
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        **kwargs
    ):
        super().__init__(
            message, 
            error_code="DEPT_ASSIGNMENT_CONFIG_ERROR", 
            **kwargs
        )
        self.config_key = config_key
        self.config_value = config_value


class DepartmentAssignmentTimeoutError(DepartmentAssignmentError):
    """部门职责分配超时异常"""
    
    def __init__(
        self, 
        message: str, 
        operation: Optional[str] = None,
        timeout_seconds: Optional[float] = None,
        **kwargs
    ):
        super().__init__(
            message, 
            error_code="DEPT_ASSIGNMENT_TIMEOUT_ERROR", 
            **kwargs
        )
        self.operation = operation
        self.timeout_seconds = timeout_seconds


class DepartmentAssignmentProcessingError(DepartmentAssignmentError):
    """部门职责分配处理异常"""
    
    def __init__(
        self, 
        message: str, 
        processing_stage: Optional[str] = None,
        input_data: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        super().__init__(
            message, 
            error_code="DEPT_ASSIGNMENT_PROCESSING_ERROR", 
            **kwargs
        )
        self.processing_stage = processing_stage
        self.input_data = input_data or {}


# ==================== 异常工具函数 ====================

def create_validation_error(field_name: str, field_value: Any, reason: str) -> DepartmentAssignmentValidationError:
    """创建验证异常"""
    message = f"字段 '{field_name}' 验证失败: {reason}"
    return DepartmentAssignmentValidationError(
        message=message,
        field_name=field_name,
        field_value=field_value,
        details={"reason": reason}
    )


def create_search_error(search_type: str, error_message: str, search_params: Dict[str, Any] = None) -> DepartmentAssignmentSearchError:
    """创建搜索异常"""
    message = f"{search_type}搜索失败: {error_message}"
    return DepartmentAssignmentSearchError(
        message=message,
        search_type=search_type,
        search_params=search_params or {},
        details={"error_message": error_message}
    )


def create_filter_error(filter_layer: str, error_message: str, filter_criteria: Dict[str, Any] = None) -> DepartmentAssignmentFilterError:
    """创建筛选异常"""
    message = f"{filter_layer}筛选失败: {error_message}"
    return DepartmentAssignmentFilterError(
        message=message,
        filter_layer=filter_layer,
        filter_criteria=filter_criteria or {},
        details={"error_message": error_message}
    )


def create_database_error(operation: str, table_name: str, error_message: str) -> DepartmentAssignmentDatabaseError:
    """创建数据库异常"""
    message = f"数据库操作失败 - 操作: {operation}, 表: {table_name}, 错误: {error_message}"
    return DepartmentAssignmentDatabaseError(
        message=message,
        operation=operation,
        table_name=table_name,
        details={"error_message": error_message}
    )


def create_timeout_error(operation: str, timeout_seconds: float) -> DepartmentAssignmentTimeoutError:
    """创建超时异常"""
    message = f"操作超时 - 操作: {operation}, 超时时间: {timeout_seconds}秒"
    return DepartmentAssignmentTimeoutError(
        message=message,
        operation=operation,
        timeout_seconds=timeout_seconds
    )


def create_processing_error(stage: str, error_message: str, input_data: Dict[str, Any] = None) -> DepartmentAssignmentProcessingError:
    """创建处理异常"""
    message = f"处理阶段 '{stage}' 失败: {error_message}"
    return DepartmentAssignmentProcessingError(
        message=message,
        processing_stage=stage,
        input_data=input_data or {},
        details={"error_message": error_message}
    )


# ==================== 异常处理装饰器 ====================

def handle_department_assignment_errors(func):
    """部门职责分配异常处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except DepartmentAssignmentError:
            # 重新抛出已知的部门分配异常
            raise
        except ValueError as e:
            # 转换为验证异常
            raise DepartmentAssignmentValidationError(
                message=f"参数验证失败: {str(e)}",
                details={"original_error": str(e)}
            )
        except Exception as e:
            # 转换为通用部门分配异常
            raise DepartmentAssignmentError(
                message=f"未知错误: {str(e)}",
                details={"original_error": str(e), "error_type": type(e).__name__}
            )
    return wrapper


def handle_async_department_assignment_errors(func):
    """异步部门职责分配异常处理装饰器"""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except DepartmentAssignmentError:
            # 重新抛出已知的部门分配异常
            raise
        except ValueError as e:
            # 转换为验证异常
            raise DepartmentAssignmentValidationError(
                message=f"参数验证失败: {str(e)}",
                details={"original_error": str(e)}
            )
        except Exception as e:
            # 转换为通用部门分配异常
            raise DepartmentAssignmentError(
                message=f"未知错误: {str(e)}",
                details={"original_error": str(e), "error_type": type(e).__name__}
            )
    return wrapper

"""
知识库管理API

提供知识库的CRUD操作和模型配置查询功能，包括：
1. 知识库列表查询（支持分页和筛选）
2. 知识库创建
3. 知识库详情查询
4. 知识库更新
5. 知识库删除
6. 可用模型列表查询
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Query, Depends, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, field_validator
import logging

from modules.knowledge.knowledge import KnowledgeCrud
from service import get_config
from utils.common.uuid_utils import validate_uuid, is_valid_uuid4

# 使用标准日志
logger = logging.getLogger(__name__)


# 创建路由器
router = APIRouter(prefix="/knowledge", tags=["知识库管理"])


def validate_knowledge_id_param(knowledge_id: str) -> str:
    """验证路径参数中的knowledge_id格式"""
    try:
        return validate_uuid(knowledge_id, field_name="knowledge_id")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


# 请求/响应模型
class KnowledgeBaseCreateRequest(BaseModel):
    """创建知识库请求"""
    knowledge_name: str = Field(..., min_length=1, max_length=255, description="知识库名称")
    knowledge_type: str = Field(..., description="知识库类型：DD/Doc/MetaData")
    knowledge_desc: str = Field("", max_length=500, description="知识库描述")
    models: Optional[Dict[str, str]] = Field(
        None,
        description="模型配置（可选，不提供则使用默认配置）",
        example={"embedding": "moka-m3e-base", "llm": "opentrek"}
    )
    knowledge_id: Optional[str] = Field(None, description="知识库ID（可选，不提供则自动生成）")

    @field_validator('knowledge_id')
    @classmethod
    def validate_knowledge_id_format(cls, v):
        """验证knowledge_id格式"""
        if v is not None and not is_valid_uuid4(v):
            raise ValueError('knowledge_id必须是有效的UUID4格式')
        return v


class KnowledgeBaseUpdateRequest(BaseModel):
    """更新知识库请求"""
    knowledge_name: Optional[str] = Field(None, min_length=1, max_length=255, description="知识库名称")
    knowledge_desc: Optional[str] = Field(None, max_length=500, description="知识库描述")
    models: Optional[Dict[str, str]] = Field(
        None,
        description="模型配置（JSON格式）- 用于更新时",
        example={
            "embedding": "moka-m3e-base",
            "llm": "opentrek"
        }
    )


class KnowledgeBaseResponse(BaseModel):
    """知识库响应"""
    id: int
    knowledge_id: str
    knowledge_name: str
    doc_nums: int
    knowledge_type: str
    knowledge_desc: str
    models: Optional[Dict[str, str]] = Field(default_factory=dict, description="模型配置（JSON格式）", example={"embedding": "moka-m3e-base", "llm": "opentrek"})
    create_time: str
    update_time: str


class KnowledgeBaseListResponse(BaseModel):
    """知识库列表响应"""
    success: bool = True
    message: str = "查询成功"
    data: List[KnowledgeBaseResponse]
    total: int
    page: int
    page_size: int


class KnowledgeBaseCreateResponse(BaseModel):
    """创建知识库响应"""
    success: bool = True
    message: str = "创建成功"
    knowledge_id: str


class ModelInfoResponse(BaseModel):
    """模型信息响应"""
    model_type: str
    model_name: str
    provider: str
    description: str
    is_available: bool = True


class AvailableModelsResponse(BaseModel):
    """可用模型列表响应"""
    success: bool = True
    message: str = "查询成功"
    data: Dict[str, List[ModelInfoResponse]]


class StandardResponse(BaseModel):
    """标准响应"""
    success: bool
    message: str
    data: Optional[Any] = None


class ErrorResponse(BaseModel):
    """错误响应"""
    success: bool = False
    message: str
    data: Optional[Any] = None


class KnowledgeBaseRenameRequest(BaseModel):
    """重命名知识库请求"""
    knowledge_name: str = Field(..., min_length=1, max_length=255, description="新的知识库名称")


class KnowledgeBaseModelsUpdateRequest(BaseModel):
    """更新知识库模型配置请求"""
    models: Dict[str, str] = Field(
        ...,
        description="新的模型配置（JSON格式）",
        example={
            "embedding": "moka-m3e-base",
            "llm": "opentrek"
        }
    )


def get_knowledge_base_manager():
    """获取知识库管理器实例"""
    return KnowledgeCrud()


def create_error_response(message: str, status_code: int = 500) -> JSONResponse:
    """创建统一格式的错误响应"""
    return JSONResponse(
        status_code=status_code,
        content=ErrorResponse(message=message).model_dump()
    )


@router.get("/", response_model=KnowledgeBaseListResponse)
async def list_knowledge_bases(
    knowledge_name: Optional[str] = Query(None, description="知识库名称（模糊查询）"),
    knowledge_type: Optional[str] = Query(None, description="知识库类型：DD/Doc/MetaData"),
    page: Optional[str] = Query("1", description="页码（从1开始）"),
    page_size: Optional[str] = Query("20", description="每页大小（1-100）"),
    manager: KnowledgeCrud = Depends(get_knowledge_base_manager)
):
    """
    查询知识库列表

    支持按知识库名称和类型进行筛选，支持分页查询。
    """
    try:
        # 处理分页参数，支持空字符串和None
        try:
            page_int = int(page) if page and page.strip() else 1
            if page_int < 1:
                page_int = 1
        except (ValueError, AttributeError):
            page_int = 1

        try:
            page_size_int = int(page_size) if page_size and page_size.strip() else 20
            if page_size_int < 1:
                page_size_int = 20
            elif page_size_int > 100:
                page_size_int = 100
        except (ValueError, AttributeError):
            page_size_int = 20

        knowledge_bases, total = await manager.list_knowledge_bases(
            knowledge_name=knowledge_name,
            knowledge_type=knowledge_type,
            page=page_int,
            page_size=page_size_int
        )
        
        # 转换为响应格式
        data = []
        for kb in knowledge_bases:
            # 确保models字段是有效的字典
            models_data = kb.models if isinstance(kb.models, dict) else {}

            kb_response = KnowledgeBaseResponse(
                id=kb.id,
                knowledge_id=kb.knowledge_id,
                knowledge_name=kb.knowledge_name,
                doc_nums=kb.doc_nums,
                knowledge_type=kb.knowledge_type,
                knowledge_desc=kb.knowledge_desc,
                models=models_data,
                create_time=kb.create_time.isoformat(),
                update_time=kb.update_time.isoformat()
            )
            data.append(kb_response)
        
        return KnowledgeBaseListResponse(
            data=data,
            total=total,
            page=page_int,
            page_size=page_size_int
        )
        
    except Exception as e:
        logger.error(f"查询知识库列表失败: {str(e)}")
        return create_error_response(f"查询知识库列表失败: {str(e)}", 500)


@router.post("/", response_model=KnowledgeBaseCreateResponse)
async def create_knowledge_base(
    request: KnowledgeBaseCreateRequest,
    manager: KnowledgeCrud = Depends(get_knowledge_base_manager)
):
    """
    创建新的知识库

    创建一个新的知识库，知识库名称在用户范围内必须唯一。

    **模型配置要求：**
    - MetaData类型：必须包含 `embedding` 模型
    - Doc类型：必须包含 `embedding` 和 `llm` 模型
    - DD类型：必须包含 `embedding` 和 `llm` 模型

    **示例：**
    ```json
    {
        "knowledge_name": "我的文档库",
        "knowledge_type": "Doc",
        "knowledge_desc": "用于存储技术文档",
        "models": {
            "embedding": "moka-m3e-base",
            "llm": "opentrek"
        }
    }
    ```
    """
    try:
        # 验证知识库类型
        valid_types = ["DD", "Doc", "MetaData"]
        if request.knowledge_type not in valid_types:
            return create_error_response(
                f"无效的知识库类型，支持的类型: {', '.join(valid_types)}", 400
            )

        knowledge_id = await manager.create_knowledge_base(
            knowledge_name=request.knowledge_name,
            knowledge_type=request.knowledge_type,
            knowledge_desc=request.knowledge_desc,
            models=request.models,
            knowledge_id=request.knowledge_id
        )
        
        return KnowledgeBaseCreateResponse(knowledge_id=knowledge_id)
        
    except ValueError as e:
        # 业务逻辑错误（如名称重复）
        return create_error_response(str(e), 400)
    except Exception as e:
        logger.error(f"创建知识库失败: {str(e)}")
        return create_error_response(f"创建知识库失败: {str(e)}", 500)


@router.get("/{knowledge_id}", response_model=StandardResponse)
async def get_knowledge_base(
    knowledge_id: str = Depends(validate_knowledge_id_param),
    manager: KnowledgeCrud = Depends(get_knowledge_base_manager)
):
    """
    获取知识库详情

    根据知识库ID获取详细信息。
    """
    try:
        kb_info = await manager.get_knowledge_base(knowledge_id)

        if not kb_info:
            return create_error_response("知识库不存在", 404)

        # 确保models字段是有效的字典
        models_data = kb_info.models if isinstance(kb_info.models, dict) else {}

        data = KnowledgeBaseResponse(
            id=kb_info.id,
            knowledge_id=kb_info.knowledge_id,
            knowledge_name=kb_info.knowledge_name,
            doc_nums=kb_info.doc_nums,
            knowledge_type=kb_info.knowledge_type,
            knowledge_desc=kb_info.knowledge_desc,
            models=models_data,
            create_time=kb_info.create_time.isoformat(),
            update_time=kb_info.update_time.isoformat()
        )
        
        return StandardResponse(
            success=True,
            message="查询成功",
            data=data
        )
        
    except Exception as e:
        logger.error(f"获取知识库详情失败: {str(e)}")
        return create_error_response(f"获取知识库详情失败: {str(e)}", 500)


@router.put("/{knowledge_id}", response_model=StandardResponse)
async def update_knowledge_base(
    request: KnowledgeBaseUpdateRequest,
    knowledge_id: str = Depends(validate_knowledge_id_param),
    manager: KnowledgeCrud = Depends(get_knowledge_base_manager)
):
    """
    更新知识库信息

    更新知识库的名称、描述或模型配置。注意：知识库类型创建后不可更改。
    """
    try:
        success = await manager.update_knowledge_base(
            knowledge_id=knowledge_id,
            knowledge_name=request.knowledge_name,
            knowledge_desc=request.knowledge_desc,
            models=request.models
        )

        if not success:
            return create_error_response("知识库不存在或更新失败", 404)

        return StandardResponse(
            success=True,
            message="更新成功"
        )

    except Exception as e:
        logger.error(f"更新知识库失败: {str(e)}")
        return create_error_response(f"更新知识库失败: {str(e)}", 500)


@router.patch("/{knowledge_id}/rename", response_model=StandardResponse)
async def rename_knowledge_base(
    request: KnowledgeBaseRenameRequest,
    knowledge_id: str = Depends(validate_knowledge_id_param),
    manager: KnowledgeCrud = Depends(get_knowledge_base_manager)
):
    """
    重命名知识库

    专门用于重命名知识库的接口，只更新知识库名称。
    """
    try:
        success = await manager.update_knowledge_base(
            knowledge_id=knowledge_id,
            knowledge_name=request.knowledge_name,
            knowledge_desc=None,
            models=None
        )

        if not success:
            return create_error_response("知识库不存在或重命名失败", 404)

        return StandardResponse(
            success=True,
            message="重命名成功"
        )

    except Exception as e:
        logger.error(f"重命名知识库失败: {str(e)}")
        return create_error_response(f"重命名知识库失败: {str(e)}", 500)


@router.patch("/{knowledge_id}/models", response_model=StandardResponse)
async def update_knowledge_base_models(
    request: KnowledgeBaseModelsUpdateRequest,
    knowledge_id: str = Depends(validate_knowledge_id_param),
    manager: KnowledgeCrud = Depends(get_knowledge_base_manager)
):
    """
    更新知识库模型配置

    专门用于更新知识库模型配置的接口，支持更新embedding和llm模型。

    **模型配置要求：**
    - MetaData类型：必须包含 `embedding` 模型
    - Doc类型：必须包含 `embedding` 和 `llm` 模型
    - DD类型：必须包含 `embedding` 和 `llm` 模型

    **示例：**
    ```json
    {
        "models": {
            "embedding": "moka-m3e-base",
            "llm": "opentrek"
        }
    }
    ```
    """
    try:
        success = await manager.update_knowledge_base(
            knowledge_id=knowledge_id,
            knowledge_name=None,
            knowledge_desc=None,
            models=request.models
        )

        if not success:
            return create_error_response("知识库不存在或模型更新失败", 404)

        return StandardResponse(
            success=True,
            message="模型配置更新成功"
        )

    except Exception as e:
        logger.error(f"更新知识库模型配置失败: {str(e)}")
        return create_error_response(f"更新知识库模型配置失败: {str(e)}", 500)


@router.delete("/{knowledge_id}", response_model=StandardResponse)
async def delete_knowledge_base(
    knowledge_id: str = Depends(validate_knowledge_id_param),
    manager: KnowledgeCrud = Depends(get_knowledge_base_manager)
):
    """
    删除知识库

    删除指定的知识库，相关的文档和元数据也会被级联删除。
    """
    try:
        success = await manager.delete_knowledge_base(knowledge_id)

        if not success:
            return create_error_response("知识库不存在或删除失败", 404)

        return StandardResponse(
            success=True,
            message="删除成功"
        )

    except Exception as e:
        logger.error(f"删除知识库失败: {str(e)}")
        return create_error_response(f"删除知识库失败: {str(e)}", 500)


@router.get("/models/available", response_model=AvailableModelsResponse)
async def get_available_models():
    """
    获取可用的模型列表

    返回系统中配置的所有可用模型，包括大语言模型和向量化模型。
    """
    try:
        # 从服务层获取配置信息
        config_data = get_config()
        if not config_data:
            raise ValueError("配置未初始化")

        # 获取embedding模型列表
        embedding_models = []
        if hasattr(config_data, 'model') and hasattr(config_data.model, 'embeddings'):
            for model_name, model_config in config_data.model.embeddings.items():
                embedding_models.append(ModelInfoResponse(
                    model_type="embedding",
                    model_name=model_name,
                    provider=getattr(model_config, 'provider', 'unknown'),
                    description=f"Embedding模型: {model_name}",
                    is_available=True
                ))

        # 获取LLM模型列表
        llm_models = []
        if hasattr(config_data, 'model') and hasattr(config_data.model, 'llms'):
            for model_name, model_config in config_data.model.llms.items():
                llm_models.append(ModelInfoResponse(
                    model_type="llm",
                    model_name=model_name,
                    provider=getattr(model_config, 'provider', 'unknown'),
                    description=f"大语言模型: {model_name}",
                    is_available=True
                ))

        response_data = {
            "embedding": embedding_models,
            "llm": llm_models
        }

        return AvailableModelsResponse(data=response_data)

    except Exception as e:
        logger.error(f"获取可用模型列表失败: {str(e)}")
        return create_error_response(f"获取可用模型列表失败: {str(e)}", 500)


# 批量操作相关模型
class BatchKnowledgeBaseItem(BaseModel):
    """批量操作中的知识库项"""
    knowledge_name: str = Field(..., description="知识库名称")
    knowledge_type: str = Field(..., description="知识库类型：DD/Doc/MetaData")
    knowledge_desc: str = Field(..., description="知识库描述")
    models: Optional[Dict[str, str]] = Field(
        None,
        description="模型配置（JSON格式）- 可选，不提供则使用默认配置",
        example={
            "embedding": "moka-m3e-base",
            "llm": "opentrek"
        }
    )
    knowledge_id: Optional[str] = Field(None, description="知识库ID（可选，不提供则自动生成）")

    @field_validator('knowledge_id')
    @classmethod
    def validate_knowledge_id_format(cls, v):
        """验证knowledge_id格式"""
        if v is not None and not is_valid_uuid4(v):
            raise ValueError('knowledge_id必须是有效的UUID4格式')
        return v

class BatchCreateRequest(BaseModel):
    """批量创建知识库请求"""
    knowledge_bases: List[BatchKnowledgeBaseItem] = Field(..., description="知识库列表")


class BatchCreateResponse(BaseModel):
    """批量创建知识库响应"""
    success: bool = True
    message: str = "批量创建完成"
    data: Dict[str, Any] = Field(..., description="创建结果")
    total: int = Field(..., description="总数")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")


class BatchDeleteRequest(BaseModel):
    """批量删除知识库请求"""
    knowledge_ids: List[str] = Field(..., description="知识库ID列表")

    @field_validator('knowledge_ids')
    @classmethod
    def validate_knowledge_ids_format(cls, v):
        """验证knowledge_ids格式"""
        for knowledge_id in v:
            if not is_valid_uuid4(knowledge_id):
                raise ValueError(f'knowledge_id必须是有效的UUID4格式: {knowledge_id}')
        return v


class BatchDeleteResponse(BaseModel):
    """批量删除知识库响应"""
    success: bool = True
    message: str = "批量删除完成"
    data: Dict[str, Any] = Field(..., description="删除结果")
    total: int = Field(..., description="总数")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")


@router.post("/batch", response_model=BatchCreateResponse)
async def batch_create_knowledge_bases(
    request: BatchCreateRequest,
    manager: KnowledgeCrud = Depends(get_knowledge_base_manager)
):
    """
    批量创建知识库

    支持一次性创建多个知识库，提高操作效率。
    如果某个知识库创建失败，不会影响其他知识库的创建。
    """
    try:
        # 验证知识库列表不能为空
        if not request.knowledge_bases:
            return create_error_response("知识库列表不能为空", 422)

        results = []
        success_count = 0
        failed_count = 0

        for kb_request in request.knowledge_bases:
            try:
                # 创建知识库
                knowledge_id = await manager.create_knowledge_base(
                    knowledge_name=kb_request.knowledge_name,
                    knowledge_type=kb_request.knowledge_type,
                    knowledge_desc=kb_request.knowledge_desc,
                    models=kb_request.models
                )

                results.append({
                    "knowledge_name": kb_request.knowledge_name,
                    "knowledge_id": knowledge_id,
                    "status": "success",
                    "message": "创建成功"
                })
                success_count += 1

            except Exception as e:
                results.append({
                    "knowledge_name": kb_request.knowledge_name,
                    "knowledge_id": None,
                    "status": "failed",
                    "message": str(e)
                })
                failed_count += 1
                logger.error(f"批量创建知识库失败: {kb_request.knowledge_name}, 错误: {str(e)}")

        return BatchCreateResponse(
            data={
                "results": results,
                "summary": {
                    "total": len(request.knowledge_bases),
                    "success": success_count,
                    "failed": failed_count
                }
            },
            total=len(request.knowledge_bases),
            success_count=success_count,
            failed_count=failed_count
        )

    except Exception as e:
        logger.error(f"批量创建知识库失败: {str(e)}")
        return create_error_response(f"批量创建知识库失败: {str(e)}", 500)


@router.post("/batch/delete", response_model=BatchDeleteResponse)
async def batch_delete_knowledge_bases(
    request: BatchDeleteRequest,
    manager: KnowledgeCrud = Depends(get_knowledge_base_manager)
):
    """
    批量删除知识库

    支持一次性删除多个知识库，提高操作效率。
    如果某个知识库删除失败，不会影响其他知识库的删除。
    """
    try:
        # 验证知识库ID列表不能为空
        if not request.knowledge_ids:
            return create_error_response("知识库ID列表不能为空", 422)

        results = []
        success_count = 0
        failed_count = 0

        for knowledge_id in request.knowledge_ids:
            try:
                # 删除知识库
                await manager.delete_knowledge_base(
                    knowledge_id=knowledge_id
                )

                results.append({
                    "knowledge_id": knowledge_id,
                    "status": "success",
                    "message": "删除成功"
                })
                success_count += 1

            except Exception as e:
                results.append({
                    "knowledge_id": knowledge_id,
                    "status": "failed",
                    "message": str(e)
                })
                failed_count += 1
                logger.error(f"批量删除知识库失败: {knowledge_id}, 错误: {str(e)}")

        return BatchDeleteResponse(
            data={
                "results": results,
                "summary": {
                    "total": len(request.knowledge_ids),
                    "success": success_count,
                    "failed": failed_count
                }
            },
            total=len(request.knowledge_ids),
            success_count=success_count,
            failed_count=failed_count
        )

    except Exception as e:
        logger.error(f"批量删除知识库失败: {str(e)}")
        return create_error_response(f"批量删除知识库失败: {str(e)}", 500)

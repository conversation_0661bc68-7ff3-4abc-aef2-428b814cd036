-- ==========================================
-- PostgreSQL + pgvector 向量数据库设计 (企业级优化版)
-- 创建时间: 2025-01-16
-- 数据库: PostgreSQL + pgvector  
-- 描述: Text2SQL系统向量数据库表结构 (企业级分区设计)
-- 
-- 企业级分区设计原则：
-- 1. 统一分区策略：所有表使用128分区，平衡性能与维护成本
-- 2. 预留扩展空间：支持未来10年业务增长
-- 3. 简化运维：统一的分区数量便于自动化维护
-- 4. 查询优化：分区键选择支持主要查询模式
-- ==========================================

-- 启用pgvector扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- ==========================================
-- 1. 元数据表级向量搜索表 (Metadata Table-Level Embeddings)
-- 企业级设计：128分区支持大规模部署，查询性能与维护成本平衡
-- ==========================================
DROP TABLE IF EXISTS md_table_embeddings CASCADE;
CREATE TABLE md_table_embeddings (
    id BIGSERIAL,
    embedding VECTOR(768) NOT NULL,
    
    -- 必要的关联键 (用于回查关系型数据库)
    knowledge_id VARCHAR(255) NOT NULL,
    source_type VARCHAR(20) NOT NULL,
    db_id BIGINT NOT NULL,
    table_id BIGINT NOT NULL,
    
    -- 向量化内容标识
    content_type VARCHAR(50) NOT NULL,
    


    create_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    update_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    PRIMARY KEY (knowledge_id, source_type, db_id, id)
) PARTITION BY HASH (knowledge_id, source_type, db_id);

-- 添加表和字段注释
COMMENT ON TABLE md_table_embeddings IS '元数据表级向量搜索表，存储表名、中文名、描述的向量化信息';
COMMENT ON COLUMN md_table_embeddings.embedding IS '表的向量表示，维度768';
COMMENT ON COLUMN md_table_embeddings.knowledge_id IS '知识库ID，用于隔离不同知识库的数据';
COMMENT ON COLUMN md_table_embeddings.source_type IS '数据源类型：SOURCE-源数据，INDEX-指标数据';
COMMENT ON COLUMN md_table_embeddings.table_id IS '关联的关系型数据库表ID，用于回查详细信息';
COMMENT ON COLUMN md_table_embeddings.content_type IS '向量化的内容类型：table_name, table_name_cn, table_desc';
COMMENT ON COLUMN md_table_embeddings.create_time IS '向量创建时间';
COMMENT ON COLUMN md_table_embeddings.update_time IS '向量更新时间，用于调试和增量同步';

-- 企业级分区创建：128个分区的统一标准
-- 设计考虑：支持1000+知识库、10年扩展、自动化运维
DO $$
BEGIN
    FOR i IN 0..127 LOOP
        EXECUTE format('CREATE TABLE md_table_embeddings_part_%s PARTITION OF md_table_embeddings FOR VALUES WITH (modulus 128, remainder %s)',
                      lpad(i::text, 3, '0'), i);
    END LOOP;
END $$;

-- 索引策略：企业级性能优化
CREATE INDEX idx_md_table_embeddings_hnsw ON md_table_embeddings USING HNSW (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 200);

CREATE INDEX idx_md_table_embeddings_table_id ON md_table_embeddings (table_id);

CREATE INDEX idx_md_table_embeddings_time ON md_table_embeddings (create_time DESC);

CREATE INDEX idx_md_table_embeddings_update_time ON md_table_embeddings (update_time DESC);

-- 添加唯一约束以支持向量去重（必须包含所有分区键）
CREATE UNIQUE INDEX idx_md_table_embeddings_unique ON md_table_embeddings (knowledge_id, source_type, db_id, table_id, content_type);

-- 添加索引注释
COMMENT ON INDEX idx_md_table_embeddings_hnsw IS '表向量HNSW索引：企业级参数优化，m=16保证查询性能，ef_construction=200平衡构建速度';
COMMENT ON INDEX idx_md_table_embeddings_table_id IS '关系型数据库表ID索引，用于关联查询';
COMMENT ON INDEX idx_md_table_embeddings_time IS '创建时间索引，支持增量同步和数据治理';
COMMENT ON INDEX idx_md_table_embeddings_update_time IS '更新时间索引，用于调试和增量同步优化';

-- ==========================================
-- 2. 元数据列级向量搜索表 (Metadata Column-Level Embeddings)
-- 企业级设计：统一128分区，支持表级范围搜索优化
-- ==========================================
DROP TABLE IF EXISTS md_column_embeddings CASCADE;
CREATE TABLE md_column_embeddings (
    id BIGSERIAL,
    embedding VECTOR(768) NOT NULL,
    
    -- 必要的关联键
    knowledge_id VARCHAR(255) NOT NULL,
    source_type VARCHAR(20) NOT NULL,
    table_id BIGINT NOT NULL,
    column_id BIGINT NOT NULL,
    
    -- 向量化内容标识
    content_type VARCHAR(50) NOT NULL,

    create_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    update_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    PRIMARY KEY (knowledge_id, source_type, table_id, id)
) PARTITION BY HASH (knowledge_id, source_type, table_id);

-- 添加表和字段注释
COMMENT ON TABLE md_column_embeddings IS '元数据列级向量搜索表，存储字段名、中文名、描述的向量化信息';
COMMENT ON COLUMN md_column_embeddings.embedding IS '列的向量表示，维度768';
COMMENT ON COLUMN md_column_embeddings.knowledge_id IS '知识库ID，用于隔离不同知识库的数据';
COMMENT ON COLUMN md_column_embeddings.source_type IS '数据源类型：SOURCE-源数据，INDEX-指标数据';
COMMENT ON COLUMN md_column_embeddings.table_id IS '关联的关系型数据库表ID，用于表范围过滤';
COMMENT ON COLUMN md_column_embeddings.column_id IS '关联的关系型数据库列ID，用于回查详细信息';
COMMENT ON COLUMN md_column_embeddings.content_type IS '向量化的内容类型：column_name, column_name_cn, column_desc';
COMMENT ON COLUMN md_column_embeddings.create_time IS '向量创建时间';
COMMENT ON COLUMN md_column_embeddings.update_time IS '向量更新时间，用于调试和增量同步';

-- 统一128分区设计
DO $$
BEGIN
    FOR i IN 0..127 LOOP
        EXECUTE format('CREATE TABLE md_column_embeddings_part_%s PARTITION OF md_column_embeddings FOR VALUES WITH (modulus 128, remainder %s)',
                      lpad(i::text, 3, '0'), i);
    END LOOP;
END $$;

-- 索引策略
CREATE INDEX idx_md_column_embeddings_hnsw ON md_column_embeddings USING HNSW (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 200);

CREATE INDEX idx_md_column_embeddings_ids ON md_column_embeddings (table_id, column_id);

CREATE INDEX idx_md_column_embeddings_source_type ON md_column_embeddings (source_type);

CREATE INDEX idx_md_column_embeddings_time ON md_column_embeddings (create_time DESC);

CREATE INDEX idx_md_column_embeddings_update_time ON md_column_embeddings (update_time DESC);

-- 添加唯一约束以支持向量去重
CREATE UNIQUE INDEX idx_md_column_embeddings_unique ON md_column_embeddings (knowledge_id, source_type, table_id, column_id, content_type);

-- 添加索引注释
COMMENT ON INDEX idx_md_column_embeddings_hnsw IS '列向量HNSW索引：企业级参数优化';
COMMENT ON INDEX idx_md_column_embeddings_ids IS '关系型数据库表ID和列ID复合索引，用于关联查询';
COMMENT ON INDEX idx_md_column_embeddings_source_type IS '数据源类型索引，用于区分SOURCE和INDEX数据';
COMMENT ON INDEX idx_md_column_embeddings_time IS '创建时间索引，支持增量同步和数据治理';
COMMENT ON INDEX idx_md_column_embeddings_update_time IS '更新时间索引，用于调试和增量同步优化';

-- ==========================================
-- 3. 元数据码值向量搜索表 (Metadata Code Value Embeddings)
-- 重新设计：以码值集为核心，完全解绑字段依赖
-- ==========================================
DROP TABLE IF EXISTS md_code_embeddings CASCADE;
CREATE TABLE md_code_embeddings (
    id BIGSERIAL,
    embedding VECTOR(768) NOT NULL,

    -- 核心关联键：以码值集为中心
    knowledge_id VARCHAR(255) NOT NULL,
    code_set_id BIGINT NOT NULL,
    code_value_id BIGINT NOT NULL,

    -- 向量化内容标识
    content_type VARCHAR(20) NOT NULL,  -- 'code' 或 'text'

    create_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    update_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    PRIMARY KEY (knowledge_id, code_set_id, id)
) PARTITION BY HASH (knowledge_id, code_set_id);

-- 添加表和字段注释
COMMENT ON TABLE md_code_embeddings IS '元数据码值向量搜索表 - 重新设计：以码值集为核心，完全解绑字段依赖';
COMMENT ON COLUMN md_code_embeddings.embedding IS '码值的向量表示，维度768';
COMMENT ON COLUMN md_code_embeddings.knowledge_id IS '知识库ID，用于隔离不同知识库的数据';
COMMENT ON COLUMN md_code_embeddings.code_set_id IS '码值集ID - 新设计的核心关联字段';
COMMENT ON COLUMN md_code_embeddings.code_value_id IS '码值ID - 指向具体的码值记录';
COMMENT ON COLUMN md_code_embeddings.content_type IS '内容类型：code(码值本身) 或 text(显示文本)';
COMMENT ON COLUMN md_code_embeddings.create_time IS '向量创建时间';
COMMENT ON COLUMN md_code_embeddings.update_time IS '向量更新时间，用于调试和增量同步';

-- 统一128分区设计
DO $$
BEGIN
    FOR i IN 0..127 LOOP
        EXECUTE format('CREATE TABLE md_code_embeddings_part_%s PARTITION OF md_code_embeddings FOR VALUES WITH (modulus 128, remainder %s)',
                      lpad(i::text, 3, '0'), i);
    END LOOP;
END $$;

-- 索引策略
CREATE INDEX idx_md_code_embeddings_hnsw ON md_code_embeddings USING HNSW (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 200);

CREATE INDEX idx_md_code_embeddings_code_set ON md_code_embeddings (code_set_id, code_value_id);

CREATE INDEX idx_md_code_embeddings_content_type ON md_code_embeddings (content_type);

CREATE INDEX idx_md_code_embeddings_time ON md_code_embeddings (create_time DESC);

CREATE INDEX idx_md_code_embeddings_update_time ON md_code_embeddings (update_time DESC);

-- 添加唯一约束以支持向量去重
CREATE UNIQUE INDEX idx_md_code_embeddings_unique ON md_code_embeddings (knowledge_id, code_set_id, code_value_id, content_type);

-- 添加索引注释
COMMENT ON INDEX idx_md_code_embeddings_hnsw IS '码值向量HNSW索引：企业级参数优化';
COMMENT ON INDEX idx_md_code_embeddings_code_set IS '码值集ID、码值ID复合索引，用于关联查询';
COMMENT ON INDEX idx_md_code_embeddings_content_type IS '内容类型索引，用于区分code和text类型的向量';
COMMENT ON INDEX idx_md_code_embeddings_time IS '创建时间索引，支持增量同步和数据治理';
COMMENT ON INDEX idx_md_code_embeddings_update_time IS '更新时间索引，用于调试和增量同步优化';

-- ==========================================
-- 企业级分区管理视图和函数
-- ==========================================

-- 分区统计视图：替代之前的分区记录表
CREATE OR REPLACE VIEW v_partition_stats AS
SELECT
    schemaname,
    tablename as table_name,
    COUNT(*) as partition_count,
    'HASH(knowledge_id, source_type, entity_id)' as partition_strategy,
    'Enterprise Standard: 128 partitions for scalability' as design_rationale
FROM pg_tables
WHERE tablename LIKE 'md_%_embeddings_part_%'
GROUP BY schemaname, tablename;

-- ==========================================
-- 企业级查询视图 (无变化)
-- ==========================================

-- 表级搜索视图（连接MySQL获取元数据）
CREATE OR REPLACE VIEW v_md_table_search AS
SELECT
    te.id,
    te.embedding,
    te.knowledge_id,
    te.source_type,
    te.table_id,
    te.content_type
FROM md_table_embeddings te;

-- 列级搜索视图
CREATE OR REPLACE VIEW v_md_column_search AS
SELECT
    ce.id,
    ce.embedding,
    ce.knowledge_id,
    ce.source_type,
    ce.table_id,
    ce.column_id,
    ce.content_type
FROM md_column_embeddings ce;

-- 码值搜索视图
CREATE OR REPLACE VIEW v_md_code_search AS
SELECT
    ce.id,
    ce.embedding,
    ce.knowledge_id,
    ce.code_set_id,
    ce.code_value_id,
    ce.content_type,
    ce.create_time,
    ce.update_time
FROM md_code_embeddings ce;

-- ==========================================
-- 6. 分区信息查询函数
-- ==========================================
CREATE OR REPLACE FUNCTION get_partition_info(table_name text)
RETURNS TABLE(
    partition_name text,
    partition_expression text,
    row_count bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        schemaname||'.'||tablename as partition_name,
        pg_get_expr(c.relpartbound, c.oid) as partition_expression,
        pg_class.reltuples::bigint as row_count
    FROM pg_inherits
    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
    JOIN pg_class c ON pg_inherits.inhrelid = c.oid
    JOIN pg_tables ON pg_tables.tablename = c.relname
    WHERE parent.relname = table_name
    ORDER BY c.relname;
END;
$$ LANGUAGE plpgsql;

-- 使用示例：
-- SELECT * FROM get_partition_info('md_table_embeddings');

-- ==========================================
-- 说明：验证和维护脚本
-- ==========================================
-- 验证脚本已移动到：verify/verify_partitioning_concept.sql
-- 维护脚本已移动到：maintenance/partition_maintenance.sql
-- 
-- 本文件仅包含：
-- 1. 表结构定义
-- 2. 分区创建
-- 3. 基础索引
-- 4. 必要的查询视图
-- 5. 基础的分区信息函数 
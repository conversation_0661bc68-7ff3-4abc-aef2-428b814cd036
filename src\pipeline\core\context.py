"""
轻量化Pipeline上下文
替代重型WorkflowState，基于service层设计
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid

@dataclass
class PipelineContext:
    """
    轻量化Pipeline上下文
    只包含必要数据，充分利用service层
    """
    # 基础信息
    user_question: str
    hint: str = "My poor users haven't entered the information we need, and I'm about to be laid off by the company. I'm millions in debt and still have to support my entire family. Please help me, a poor soul, and give me your accurate answer after careful consideration."
    
    # 执行配置
    model_name: str = "opentrek"
    execution_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    
    # 步骤间数据传递
    data: Dict[str, Any] = field(default_factory=dict)
    
    # 执行状态
    current_step: str = ""
    step_results: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # 缓存数据
    cache: Dict[str, Any] = field(default_factory=dict)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取数据"""
        return self.data.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """设置数据"""
        self.data[key] = value
    
    def update(self, updates: Dict[str, Any]) -> None:
        """批量更新数据"""
        self.data.update(updates)
    
    def get_cache(self, key: str, default: Any = None) -> Any:
        """获取缓存数据"""
        return self.cache.get(key, default)
    
    def set_cache(self, key: str, value: Any) -> None:
        """设置缓存数据"""
        self.cache[key] = value
    
    def record_step_result(self, step_name: str, success: bool, data: Any = None, error: str = None) -> None:
        """记录步骤执行结果"""
        self.step_results[step_name] = {
            "success": success,
            "data": data,
            "error": error,
            "timestamp": datetime.now(),
            "execution_id": self.execution_id
        }
    
    def get_step_result(self, step_name: str) -> Optional[Dict[str, Any]]:
        """获取步骤执行结果"""
        return self.step_results.get(step_name)
    
    def is_step_successful(self, step_name: str) -> bool:
        """检查步骤是否成功"""
        result = self.get_step_result(step_name)
        return result is not None and result.get("success", False)

    def set_step_error(self, step_name: str, error: str):
        """设置步骤错误信息"""
        self.step_results[step_name] = {
            "success": False,
            "result": None,
            "error": error,
            "timestamp": datetime.now(),
            "execution_id": self.execution_id
        }
    
    def get_successful_steps(self) -> List[str]:
        """获取所有成功的步骤"""
        return [
            step_name for step_name, result in self.step_results.items()
            if result.get("success", False)
        ]
    
    def get_failed_steps(self) -> List[str]:
        """获取所有失败的步骤"""
        return [
            step_name for step_name, result in self.step_results.items()
            if not result.get("success", False)
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "user_question": self.user_question,
            "hint": self.hint,
            "model_name": self.model_name,
            "execution_id": self.execution_id,
            "current_step": self.current_step,
            "data": self.data,
            "step_results": self.step_results,
            "cache": self.cache
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PipelineContext':
        """从字典创建上下文"""
        context = cls(
            user_question=data.get("user_question", ""),
            hint=data.get("hint", ""),
            model_name=data.get("model_name", "qwen_vllm"),
            execution_id=data.get("execution_id", str(uuid.uuid4()))
        )
        context.current_step = data.get("current_step", "")
        context.data = data.get("data", {})
        context.step_results = data.get("step_results", {})
        context.cache = data.get("cache", {})
        return context
    
    # def to_workflow_state(self):
    #     """转换为WorkflowState（兼容性）"""
    #     from workflow.base.state import WorkflowState
        
    #     # 创建WorkflowState实例
    #     state = WorkflowState(
    #         user_question=self.user_question,
    #         hint=self.hint,
    #         model_name=self.model_name,
    #         current_step=self.current_step
    #     )
        
    #     # 设置常用字段
    #     common_fields = [
    #         'dept_id', 'keywords', 'candidate_columns', 'selected_columns',
    #         'temp_db_schema', 'db_schema', 'rdb_schema', 'formatted_columns',
    #         'fewshot_examples', 'associated_keys_schema', 'sql_candidates', 
    #         'execution_results', 'merged_column_info', 'col_data_examples',
    #         'table_metadata', 'column_metadata', 'business_logic'
    #     ]
        
    #     for field in common_fields:
    #         if field in self.data:
    #             setattr(state, field, self.data[field])
        
    #     # 设置缓存
    #     state.cache = self.cache
        
    #     return state
    
    # @classmethod
    # def from_workflow_state(cls, state) -> 'PipelineContext':
    #     """从WorkflowState创建（兼容性）"""
    #     context = cls(
    #         user_question=getattr(state, 'user_question', ''),
    #         hint=getattr(state, 'hint', ''),
    #         model_name=getattr(state, 'model_name', 'qwen_vllm')
    #     )
        
    #     context.current_step = getattr(state, 'current_step', '')
        
    #     # 提取常用字段
    #     common_fields = [
    #         'dept_id', 'keywords', 'candidate_columns', 'selected_columns',
    #         'temp_db_schema', 'db_schema', 'rdb_schema', 'formatted_columns',
    #         'fewshot_examples', 'associated_keys_schema', 'sql_candidates',
    #         'execution_results', 'merged_column_info', 'col_data_examples',
    #         'table_metadata', 'column_metadata', 'business_logic'
    #     ]
        
    #     for field in common_fields:
    #         if hasattr(state, field):
    #             context.set(field, getattr(state, field))
        
    #     # 设置缓存
    #     if hasattr(state, 'cache'):
    #         context.cache = getattr(state, 'cache')
        
    #     return context

# ==================== 上下文工厂 ====================

class ContextFactory:
    """上下文工厂"""
    
    @staticmethod
    def create_nl2sql_context(
        user_question: str,
        hint: str = "",
        dept_id: Optional[str] = None,
        **kwargs
    ) -> PipelineContext:
        """创建NL2SQL上下文"""
        context = PipelineContext(
            user_question=user_question,
            hint=hint or "所有统计的指标，都需要满足是银保监各项贷款口径，即IS_CBIRC_LOAN='Y'"
        )
        
        # 设置部门ID
        if dept_id:
            context.set("dept_id", dept_id)
        
        # 设置其他参数
        context.update(kwargs)
        
        return context
    
    @staticmethod
    def create_from_request(request_data: Dict[str, Any]) -> PipelineContext:
        """从请求数据创建上下文"""
        return ContextFactory.create_nl2sql_context(
            user_question=request_data.get("user_question", ""),
            hint=request_data.get("hint", ""),
            dept_id=request_data.get("dept_id"),
            **{k: v for k, v in request_data.items() 
               if k not in ["user_question", "hint", "dept_id"]}
        )

#!/usr/bin/env python3
"""
业务报送核心服务

实现完整的业务报送逻辑：
1. 直接使用report_code作为version查询biz_dd_pre表
2. 三层搜索：精确匹配 → 混合搜索 → TFIDF推荐
3. 四级筛选：套系 → 报告类型 → 提交类型 → DR01 → 评分
4. 批量数据入库到biz_dd_post_distribution
5. 标准响应格式和前端通信
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import json

logger = logging.getLogger(__name__)

from service import get_client
try:
    from modules.knowledge.dd.crud import DDCrud
except ImportError:
    DDCrud = None

try:
    from modules.dd_submission.department_assignment.monitoring.performance_monitor import monitor_batch_operation_enhanced
except ImportError:
    # 如果监控组件不可用，创建一个简单的装饰器
    def monitor_batch_operation_enhanced(operation_name):
        def decorator(func):
            return func
        return decorator
from .search_service import SearchService
from .validation_service import ValidationService


class BusinessSubmissionService:
    """业务报送核心服务"""
    
    def __init__(self, rdb_client=None, vdb_client=None):
        """初始化业务报送服务"""
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        
        # 初始化核心组件
        self.dd_crud = DDCrud(rdb_client, vdb_client) if rdb_client else None
        self.search_service = SearchService(rdb_client, vdb_client)
        self.validation_service = ValidationService(rdb_client)
        
        # 性能统计
        self.performance_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_processing_time': 0.0
        }
    
    async def initialize(self):
        """初始化服务（获取数据库客户端）"""
        if not self.rdb_client:
            self.rdb_client = await get_client('database.rdbs.mysql')
        if not self.vdb_client:
            try:
                self.vdb_client = await get_client('database.vdbs.pgvector')
            except:
                logger.warning("向量数据库客户端初始化失败，将使用关系数据库")
                self.vdb_client = None
        
        # 重新初始化组件
        self.dd_crud = DDCrud(self.rdb_client, self.vdb_client)
        self.search_service = SearchService(self.rdb_client, self.vdb_client)
        self.validation_service = ValidationService(self.rdb_client)
    
    async def validate_report_code(self, report_code: str) -> Dict[str, Any]:
        """验证report_code并检查数据存在性"""
        try:
            version = report_code  # 直接使用，不拆分
            
            # 查询biz_dd_pre_distribution表（实际表名）
            sql = "SELECT COUNT(*) as count FROM biz_dd_pre_distribution WHERE version = %s"
            # 处理不同客户端的参数格式
            try:
                result = await self.rdb_client.afetch_one(sql, [version])
            except Exception as e:
                if "List argument must consist only of tuples or dictionaries" in str(e):
                    # 尝试使用字典参数
                    sql = "SELECT COUNT(*) as count FROM biz_dd_pre_distribution WHERE version = :version"
                    result = await self.rdb_client.afetch_one(sql, {"version": version})
                else:
                    raise e

            # 处理不同的返回格式
            if isinstance(result, dict):
                count = result.get('count', 0)
                if count > 0:
                    return {
                        "valid": True,
                        "count": count,
                        "version": version
                    }
                else:
                    return {
                        "valid": False,
                        "count": 0,
                        "version": version,
                        "error": "未找到对应的数据"
                    }
            elif hasattr(result, 'success'):
                if result.success and result.data and result.data['count'] > 0:
                    return {
                        "valid": True,
                        "count": result.data['count'],
                        "version": version
                    }
                else:
                    return {
                        "valid": False,
                        "count": 0,
                        "version": version,
                        "error": "未找到对应的数据"
                    }
            else:
                return {
                    "valid": False,
                    "count": 0,
                    "version": version,
                    "error": "数据库查询格式异常"
                }
                
        except Exception as e:
            logger.error(f"验证report_code失败: {e}")
            return {
                "valid": False,
                "count": 0,
                "version": report_code,
                "error": str(e)
            }
    
    @monitor_batch_operation_enhanced("business_submission_process")
    async def process_business_submission(self, report_code: str) -> Dict[str, Any]:
        """处理业务报送主流程"""
        start_time = time.time()
        self.performance_stats['total_requests'] += 1
        
        try:
            version = report_code  # 直接使用，不拆分
            
            # 1. 获取搜索项
            search_items = await self._get_search_items_from_pre_table(version)
            if not search_items:
                self.performance_stats['failed_requests'] += 1
                return {
                    "code": "400",
                    "msg": "无法在数据库里搜索到相关信息",
                    "data": []
                }
            
            logger.info(f"获取到{len(search_items)}个搜索项")
            
            # 2. 批量处理搜索项（使用asyncio.gather并发执行）
            results = []
            batch_size = 30  # 每批处理20个任务 TODO 注意需要根据行内embedding并发调整！！！
            
            for i in range(0, len(search_items), batch_size):
                batch_items = search_items[i:i + batch_size]
                
                # 创建当前批次的异步任务
                batch_tasks = [
                    self._process_single_search_item(item) 
                    for item in batch_items
                ]
                
                # 并发执行当前批次
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # 处理结果
                for j, result in enumerate(batch_results):
                    if isinstance(result, Exception):
                        logger.error(f"处理搜索项失败 {batch_items[j].get('submission_id')}: {result}")
                        continue
                    results.extend(result)
                
                # 显示进度
                processed_count = min(i + batch_size, len(search_items))
                print(f'三层搜索已完成{processed_count}/{len(search_items)},进度{processed_count/len(search_items)*100:.1f}%')
            
            logger.info(f"处理完成，生成{len(results)}个结果")
            
            # 3. 批量入库
            save_count = await self._batch_save_results(results, version)
            logger.info(f"批量入库完成，保存{save_count}条记录")
            
            # 4. 格式化输出
            output_data = self._format_output_data(results)
            
            # 更新性能统计
            processing_time = (time.time() - start_time) * 1000
            self.performance_stats['successful_requests'] += 1
            self.performance_stats['avg_processing_time'] = (
                (self.performance_stats['avg_processing_time'] * (self.performance_stats['total_requests'] - 1) + processing_time) /
                self.performance_stats['total_requests']
            )
            
            return {
                "code": "0",
                "msg": "义务报送信息处理完成",
                "data": output_data,
                "statistics": {
                    "total_items": len(search_items),
                    "result_count": len(results),
                    "save_count": save_count,
                    "processing_time_ms": processing_time
                }
            }
            
        except Exception as e:
            self.performance_stats['failed_requests'] += 1
            logger.error(f"业务报送处理失败: {e}")
            return {
                "code": "400",
                "msg": f"处理失败: {str(e)}",
                "data": []
            }
    
    async def process_business_submission_background(self, report_code: str, callback_url: str):
        """
        后台处理业务报送任务
        
        这个方法专门用于BackgroundTasks，不返回结果给前端
        处理完成后可以通过其他方式（如WebSocket、回调等）通知前端
        
        Args:
            report_code: 报表代码
        """
        try:
            logger.info(f"开始后台处理业务报送: {report_code}")
            
            # 调用主处理流程
            result = await self.process_business_submission(report_code)
            
            # 处理完成后通知前端（通过回调URL）
            import httpx
            
            # 格式化回调数据
            callback_data = {
                "report_code": report_code,
                "ddDutyCreateList": []
            }
            
            # 如果处理成功，格式化结果数据
            if result.get("code") == "0" and result.get("data"):
                for item in result.get("data", []):
                    duty_item = {
                        "entry_id": item.get("entry_id", ""),
                        "entry_type": item.get("entry_type", ""),
                        "DR22": item.get("DR22", []),
                        "BDR01": item.get("BDR01", []),
                        "BDR03": str(item.get("BDR03", "")) if item.get("BDR03") else ""
                    }
                    callback_data["ddDutyCreateList"].append(duty_item)
            
            # 发送回调请求（使用httpx原生异步请求）
            async with httpx.AsyncClient() as client:
                try:
                    callback_response = await client.post(
                        callback_url,
                        json=callback_data,
                        timeout=10.0
                    )
                    logger.info(f"回调请求发送成功: {callback_response.status_code}")
                    print(f"{callback_data=}")
                    print(f"回调请求发送成功: {callback_response.status_code}")
                    print(f"回调请求发送成功: {callback_response.json()}")
                    print(f"回调请求发送成功: {callback_response.text}")
                except Exception as callback_error:
                    logger.error(f"回调请求失败: {callback_error}")
            
            logger.info(f"后台处理完成: {report_code}, 结果: {result.get('code')}")
            print("manual!!!!!!!!!!!")
            print(f"后台处理完成: {report_code}, 结果: {result.get('code')}")

        except Exception as e:
            logger.error(f"后台处理失败: {report_code}, 错误: {e}")
    
    async def _get_search_items_from_pre_table(self, version: str) -> List[Dict[str, Any]]:
        """从biz_dd_pre表获取搜索项"""
        try:
            sql = """
            SELECT id, submission_id, submission_type, `set`, report_type, version, dr01, dr07,dr09, dr17,
                   create_time, update_time
            FROM biz_dd_pre_distribution
            WHERE version = %s
            ORDER BY submission_id
            """

            # 处理不同客户端的参数格式
            try:
                result = await self.rdb_client.afetch_all(sql, [version])
            except Exception as e:
                if "List argument must consist only of tuples or dictionaries" in str(e):
                    # 尝试使用字典参数
                    sql = """
                    SELECT id, submission_id, `set`,submission_type, report_type, version, dr01, dr07,dr09, dr17,
                           create_time, update_time
                    FROM biz_dd_pre_distribution
                    WHERE version = :version
                    ORDER BY submission_id
                    """
                    result = await self.rdb_client.afetch_all(sql, {"version": version})
                else:
                    raise e

            # 处理不同的返回格式
            if isinstance(result, list):
                return result
            elif hasattr(result, 'data'):
                # QueryResponse对象
                if result.data:
                    return result.data
                else:
                    logger.warning(f"未找到version={version}的数据")
                    return []
            elif hasattr(result, 'success'):
                if result.success and result.data:
                    return result.data
                else:
                    logger.warning(f"未找到version={version}的数据")
                    return []
            else:
                logger.warning(f"数据库查询格式异常: {type(result)}")
                return []
                
        except Exception as e:
            logger.error(f"查询biz_dd_pre表失败: {e}")
            return []
    
    async def _process_single_search_item(self, search_item: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理单个搜索项"""
        try:
            dr09 = search_item.get('dr09', '')
            dr17 = search_item.get('dr17', '')
            
            if not dr09 or not dr17:
                logger.warning(f"搜索项缺少必要字段: {search_item['submission_id']}")
                return []
            
            # 执行三层搜索
            search_result = await self.search_service.execute_three_layer_search(
                dr09, dr17, search_item
            )
            
            # 转换为输出格式
            return self._convert_search_result_to_output(search_item, search_result)
            
        except Exception as e:
            logger.error(f"处理搜索项失败 {search_item.get('submission_id')}: {e}")
            return []
    
    def _convert_search_result_to_output(self, search_item: Dict, search_result: Dict) -> List[Dict[str, Any]]:
        """将搜索结果转换为输出格式"""
        results = []
        
        try:
            submission_id = search_item['submission_id']
            submission_type = search_item['submission_type']
            
            # 转换submission_type
            entry_type = self._convert_submission_type(submission_type)
            
            if search_result['search_type'] == 'exact' and search_result.get('multiple_results'):
                # 精确匹配多个结果
                for match in search_result['results']:
                    dept_ids = [match.get('dept_id', '')]
                    results.append({
                        'entry_id': submission_id,
                        'entry_type': entry_type,
                        'DR22': dept_ids,
                        'BDR01': dept_ids,
                        'BDR03': '',
                        'dd_pre_distribution_id': search_item['id'],
                        'submission_type': search_item['submission_type'],
                        'report_type': search_item['report_type'],
                        'set': search_item['set'],
                        'dr01': search_item['dr01'],
                        'dr07': search_item['dr07'],
                    })
            else:
                # 单个结果（精确匹配、混合搜索、TFIDF推荐）
                if search_result['search_type'] == 'tfidf':
                    dept_ids = [search_result['result']['dept_id']]
                else:
                    dept_ids = [search_result['result'].get('dept_id', '')]
                
                results.append({
                    'entry_id': submission_id,
                    'entry_type': entry_type,
                    'DR22': dept_ids,
                    'BDR01': dept_ids,
                    'BDR03': '',
                    'dd_pre_distribution_id': search_item['id'],
                    'submission_type': search_item['submission_type'],
                    'report_type': search_item['report_type'],
                    'set': search_item['set'],
                    'dr01': search_item['dr01'],
                    'dr07': search_item['dr07'],
                })
            
            return results
            
        except Exception as e:
            logger.error(f"转换搜索结果失败: {e}")
            return []
    
    def _convert_submission_type(self, submission_type: str) -> str:
        """转换submission_type"""
        type_mapping = {
            'SUBMISSION': 'ITEM',
            'RANGE': 'TABLE'
        }
        return type_mapping.get(submission_type, submission_type)
    
    async def _batch_save_results(self, results: List[Dict], version: str) -> int:
        """批量保存结果到biz_dd_post_distribution - 使用高性能的abatch_insert方法"""
        if not results:
            return 0
        
        try:
            # 转换为数据库格式
            save_records = []
            for result in results:
                # 处理数组字段
                # dr22_str = json.dumps(result.get('DR22',[]))
                # bdr01_str = json.dumps(result.get('BDR01',[]))
                dr22_list = result.get('DR22',[])
                dr22_str = dr22_list[0] if dr22_list else ''

                bdr01_list = result.get('BDR01',[])
                bdr01_str = bdr01_list[0] if bdr01_list else ''
                
                record = {
                    'pre_distribution_id': result['dd_pre_distribution_id'],
                    'submission_type': result['submission_type'],
                    'report_type': result['report_type'],
                    'set': result['set'],
                    'submission_id': result['entry_id'],
                    'version': version,
                    # 'dept_id': result['DR22'][0] if result['DR22'] else '',
                    'dept_id': dr22_str,
                    'dr22': dr22_str,
                    'bdr01': bdr01_str,
                    'bdr02': '',  # 默认值
                    'bdr03': result['BDR03'],
                    'bdr04': '',  # 默认值
                    'bdr05': '',  # 默认值
                    'dr01': result['dr01'],
                    'dr07': result['dr07'],
                    'create_time': datetime.now(),
                    'update_time': datetime.now()
                }
                save_records.append(record)
            
            # 先删除现有记录（避免重复）
            await self._batch_delete_existing_records(save_records, version)
            
            # 使用abatch_insert进行高性能批量插入
            logger.info(f"开始批量插入 {len(save_records)} 条记录")
            
            try:
                result = await self.rdb_client.abatch_insert(
                    table='biz_dd_post_distribution',
                    data=save_records,
                    batch_size=100,  # 每批处理100条记录
                    max_concurrency=3,  # 最大3个并发批次
                    timeout_per_batch=60.0  # 每批次60秒超时
                )
                
                if result.success:
                    logger.info(f"批量插入成功: 影响行数={result.affected_rows}, 执行时间={result.execution_time:.2f}秒")
                    return result.affected_rows
                else:
                    logger.warning(f"批量插入部分失败: {result.operation_parameters.get('errors', [])}")
                    # 返回成功插入的记录数
                    return result.affected_rows
                    
            except Exception as batch_error:
                logger.error(f"批量插入失败，回退到逐行插入: {batch_error}")
                # 如果批量插入失败，回退到逐行插入（兼容性处理）
                return await self._fallback_insert_records(save_records)
            
        except Exception as e:
            logger.error(f"批量保存失败: {e}")
            return 0
    
    async def _fallback_insert_records(self, save_records: List[Dict]) -> int:
        """回退的逐行插入方法（兼容性处理）"""
        try:
            success_count = 0
            for record in save_records:
                try:
                    sql = """
                    INSERT INTO biz_dd_post_distribution 
                    (pre_distribution_id, submission_type, report_type, submission_id, version, dept_id, dr22, bdr01, bdr02, bdr03, bdr04, bdr05, 
                     create_time, update_time)
                    VALUES (:pre_distribution_id, :submission_type, :report_type, :submission_id, :version, :dept_id, :dr22, :bdr01, :bdr02, :bdr03, :bdr04, :bdr05, 
                     :create_time, :update_time)
                    """
                    
                    result = await self.rdb_client.aexecute(sql, record)
                    if result.success:
                        success_count += 1
                        
                except Exception as e:
                    logger.error(f"逐行插入记录失败: {e}")
            
            logger.info(f"逐行插入完成: {success_count}/{len(save_records)}")
            return success_count
            
        except Exception as e:
            logger.error(f"逐行插入失败: {e}")
            return 0
    
    async def _batch_delete_existing_records(self, records: List[Dict], version: str):
        """批量删除现有记录 - 使用高性能的abatch_delete方法"""
        try:
            if not records:
                logger.info("没有需要删除的记录")
                return
            
            # 构建删除条件列表
            delete_conditions = []
            for record in records:
                condition = {
                    'submission_id': record['submission_id'],
                    'dept_id': record['dept_id'],
                    'version': version
                }
                delete_conditions.append(condition)
            
            logger.info(f"开始批量删除 {len(delete_conditions)} 条现有记录")
            
            # 使用abatch_delete进行高性能批量删除
            result = await self.rdb_client.abatch_delete(
                table='biz_dd_post_distribution',
                conditions=delete_conditions,
                batch_size=100,  # 每批处理100条记录
                max_concurrency=3,  # 最大3个并发批次
                timeout_per_batch=60.0  # 每批次60秒超时
            )
            
            if result.success:
                logger.info(f"批量删除成功: 影响行数={result.affected_rows}, 执行时间={result.execution_time:.2f}秒")
            else:
                logger.warning(f"批量删除部分失败: {result.operation_parameters.get('errors', [])}")
                
        except Exception as e:
            logger.error(f"批量删除现有记录失败: {e}")
            # 如果批量删除失败，回退到逐行删除（兼容性处理）
            logger.info("回退到逐行删除模式")
            await self._fallback_delete_records(records, version)
    
    async def _fallback_delete_records(self, records: List[Dict], version: str):
        """回退的逐行删除方法（兼容性处理）"""
        try:
            for record in records:
                sql = """
                DELETE FROM biz_dd_post_distribution 
                WHERE submission_id = :submission_id AND dept_id = :dept_id AND version = :version
                """
                await self.rdb_client.aexecute(sql, {
                    'submission_id': record['submission_id'],
                    'dept_id': record['dept_id'],
                    'version': version
                })
            logger.info(f"逐行删除完成: {len(records)} 条记录")
        except Exception as e:
            logger.error(f"逐行删除失败: {e}")
    
    def _format_output_data(self, results: List[Dict]) -> List[Dict[str, Any]]:
        """格式化输出数据"""
        return results
    
    async def notify_frontend(self, processing_result: Dict[str, Any]):
        """通知前端处理结果（预留接口）"""
        # 这里可以实现WebSocket通知或回调机制
        logger.info(f"通知前端处理结果: {processing_result.get('code')}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self.performance_stats.copy()

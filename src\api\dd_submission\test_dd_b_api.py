#!/usr/bin/env python3
"""
DD-B增强处理API测试脚本

测试新创建的DD-B API端点
"""

import asyncio
import json
import logging
from typing import Dict, Any
import httpx

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DDBAPITester:
    """DD-B API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:30337"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def test_health_check(self) -> Dict[str, Any]:
        """测试健康检查端点"""
        logger.info("🔍 测试健康检查端点...")
        
        try:
            response = await self.client.get(f"{self.base_url}/api/dd/dd-b/health")
            result = response.json()
            
            logger.info(f"健康检查状态码: {response.status_code}")
            logger.info(f"健康检查响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response": result
            }
        
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def test_dd_b_process(self, report_code: str, dept_id: str) -> Dict[str, Any]:
        """测试DD-B增强处理端点（单个report_code）"""
        logger.info(f"🚀 测试DD-B增强处理: {report_code} - {dept_id}")

        request_data = {
            "report_code": report_code,
            "dept_id": dept_id,
            "enable_auto_fill": True,
            "return_original_data": False
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/api/dd/dd-b/process",
                json=request_data
            )
            result = response.json()
            
            logger.info(f"处理状态码: {response.status_code}")
            logger.info(f"处理响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response": result
            }
        
        except Exception as e:
            logger.error(f"DD-B处理失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    

    
    async def test_invalid_request(self) -> Dict[str, Any]:
        """测试无效请求处理"""
        logger.info("❌ 测试无效请求处理...")
        
        request_data = {
            "report_code": "",  # 空的report_code
            "dept_id": "",      # 空的dept_id
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/api/dd/dd-b/process",
                json=request_data
            )
            result = response.json()
            
            logger.info(f"无效请求状态码: {response.status_code}")
            logger.info(f"无效请求响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return {
                "success": response.status_code == 422,  # 期望验证错误
                "status_code": response.status_code,
                "response": result
            }
        
        except Exception as e:
            logger.error(f"无效请求测试失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()


async def main():
    """主测试函数"""
    logger.info("🎯 DD-B增强处理API测试")
    logger.info("=" * 60)
    
    # 测试参数
    TEST_REPORT_CODE = "S71_ADS_RELEASE_V0"  # 使用您的测试参数
    TEST_DEPT_ID = "30239"                   # 使用您的测试参数
    
    tester = DDBAPITester()
    
    try:
        # 1. 健康检查测试
        logger.info("\n📋 1. 健康检查测试")
        health_result = await tester.test_health_check()
        
        if not health_result["success"]:
            logger.error("❌ 健康检查失败，跳过其他测试")
            return False
        
        logger.info("✅ 健康检查通过")
        
        # 2. DD-B增强处理测试
        logger.info("\n📋 2. DD-B增强处理测试")
        process_result = await tester.test_dd_b_process(TEST_REPORT_CODE, TEST_DEPT_ID)

        if process_result["success"]:
            logger.info("✅ DD-B增强处理测试通过")
        else:
            logger.warning("⚠️ DD-B增强处理测试失败")

        # 3. 无效请求测试
        logger.info("\n📋 3. 无效请求处理测试")
        invalid_result = await tester.test_invalid_request()
        
        if invalid_result["success"]:
            logger.info("✅ 无效请求处理测试通过")
        else:
            logger.warning("⚠️ 无效请求处理测试失败")
        
        # 测试总结
        logger.info("\n" + "=" * 60)
        logger.info("📊 测试总结:")
        logger.info(f"  健康检查: {'✅ 通过' if health_result['success'] else '❌ 失败'}")
        logger.info(f"  增强处理: {'✅ 通过' if process_result['success'] else '❌ 失败'}")
        logger.info(f"  无效请求: {'✅ 通过' if invalid_result['success'] else '❌ 失败'}")

        # 计算成功率
        total_tests = 3
        passed_tests = sum([
            health_result["success"],
            process_result["success"],
            invalid_result["success"]
        ])
        
        success_rate = (passed_tests / total_tests) * 100
        logger.info(f"  成功率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        
        if success_rate >= 75:
            logger.info("🎉 API测试基本通过！")
            return True
        else:
            logger.warning("⚠️ API测试存在问题，需要检查")
            return False
    
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        return False
    
    finally:
        await tester.close()


if __name__ == "__main__":
    # 运行API测试
    success = asyncio.run(main())
    exit(0 if success else 1)

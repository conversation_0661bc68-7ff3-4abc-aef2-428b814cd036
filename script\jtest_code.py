import asyncio
import sys
import os
import hydra
from pathlib import Path
import time
from datetime import datetime



current_dir = Path(__file__).parent
project_root = current_dir.parent / 'src'
sys.path.insert(0, str(project_root))

# from utils.common.config_util import config
#
# # 初始化Hydra配置
# with hydra.initialize(version_base=None, config_path="../src/config"):
#     cfg = hydra.compose(config_name="config")
#     config.initialize(cfg)


if __name__ == "__main__":
    from service import get_client, get_config
    from base.db.implementations.vs.pgvector.factory import PGVectorClient
    from base.model_serve.model_runtime.model_providers.embedding_model.generic_embedding import GenericEmbedding


    async def main():
        embedding_client: GenericEmbedding = await get_client('model.embeddings.moka-m3e-base')
        res = await embedding_client.ainvoke(texts=["ceshi"]*2)
        print(res)
        print(f'{len(res.embeddings)=}')

        vdb_client:PGVectorClient = await get_client("database.vdbs.pgvector")
        data = {
            "knowledge_id": "ABC1236",
            "doc_id": "DOC456",
            "chunk_id": "CHK789",
            "chunk_info_id": "INFO012",
            "info_type": "embedding0",
            "embedding":res.embeddings[0],
            "create_time": datetime.fromisoformat("2023-01-01T00:00:00"),
            "update_time": datetime.fromisoformat("2023-01-01T00:00:00")
        }
        res = await vdb_client.ainsert('doc_embeddings',[data])
        # res = await vdb_client.aquery('doc_embeddings', 'id = 123', )
        print(res)


    asyncio.run(main())
from decimal import Decimal

from pydantic import BaseModel

from model_serve.model_runtime.entities.model_entities import ModelUsage


class EmbeddingUsage(ModelUsage):
    """
    Model class for embedding usage.
    """

    tokens: int
    total_tokens: int
    unit_price: Decimal
    price_unit: Decimal
    total_price: Decimal
    currency: str
    latency: float
    
    @classmethod
    def empty_usage(cls):
        return cls(
            tokens=0,
            total_tokens=0,
            unit_price=Decimal('0'),
            price_unit=Decimal('0'),
            total_price=Decimal('0'),
            currency="",
            latency=0.0
        )


class TextEmbeddingResult(BaseModel):
    """
    Model class for text embedding result.
    """

    model: str
    embeddings: list[list[float]]
    usage: EmbeddingUsage = EmbeddingUsage.empty_usage()

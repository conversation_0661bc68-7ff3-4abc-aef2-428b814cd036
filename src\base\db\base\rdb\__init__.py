"""
RDB数据库抽象层

企业级关系型数据库抽象层，提供统一的API接口和数据模型。

设计原则：
1. 输入输出标准化 - 统一的请求/响应模型
2. 适配器模式 - 通过转换层适配不同数据库
3. 性能优先 - 支持原生驱动（pymysql、asyncpg）
4. 分层设计 - base层定义规范，具体实现在各自目录
5. SQLAlchemy独立 - 作为特殊实现单独处理

目录结构：
- core/          # 核心抽象层（类型、模型、异常、接口）
- adapters/      # 适配器层（结果转换、错误处理）
- query/         # 查询抽象层（构建器、过滤器、验证器）
- utils/         # 工具层（连接管理、辅助函数）

使用示例：
    from base.db.base.rdb import (
        DatabaseClient, QueryRequest, QueryFilter, 
        ComparisonOperator, LogicalOperator
    )
    
    # 创建查询请求
    request = QueryRequest(
        table="users",
        columns=["id", "name", "email"],
        filters=QueryFilterGroup(
            operator=LogicalOperator.AND,
            filters=[
                QueryFilter("age", ComparisonOperator.GT, 18),
                QueryFilter("status", ComparisonOperator.EQ, "active")
            ]
        ),
        limit=10
    )
    
    # 执行查询
    response = client.query(request)
    print(f"Found {response.count} users")
"""

# 导入核心组件
from .core import (
    # ==================== 类型定义 ====================
    DatabaseValue, DatabaseRecord, DatabaseRecords, QueryParameters,
    DatabaseType, SortOrder, ComparisonOperator, LogicalOperator, JoinType,
    TransactionIsolation, ConnectionPoolStatus,
    DatabaseConnection, DatabaseTransaction, DatabasePool,
    QueryResult,

    # ==================== 数据模型 ====================
    QueryFilter, QueryFilterGroup,
    QuerySort, QueryJoin, QueryAggregation, QueryGroupBy,
    QueryRequest, InsertRequest, UpdateRequest, DeleteRequest,
    QueryResponse, OperationResponse,
    ConnectionConfig,

    # ==================== 异常体系 ====================
    RDBError,
    ConnectionError, ConnectionTimeoutError, ConnectionPoolError,
    AuthenticationError, DatabaseNotFoundError,
    QueryError, SQLSyntaxError, QueryTimeoutError,
    TableNotFoundError, ColumnNotFoundError,
    DataError, ValidationError, IntegrityError,
    UniqueConstraintError, ForeignKeyConstraintError,
    CheckConstraintError, NotNullConstraintError,
    TransactionError, TransactionRollbackError,
    DeadlockError, SerializationError,
    ConfigurationError, UnsupportedDatabaseError, UnsupportedFeatureError,
    OperationError, BulkOperationError, MigrationError,
    wrap_database_error,

    # ==================== 核心接口 ====================
    DatabaseClient, QueryBuilder, DatabaseDialect,
    ConnectionPoolManager, ResultAdapter, DatabaseClientFactory,
)

# 导入适配器组件
from .adapters import (
    BaseAdapter, DefaultResultAdapter, StreamingResultAdapter,
    ErrorAdapter, DefaultErrorAdapter,
    MySQLErrorAdapter, PostgreSQLErrorAdapter, SQLiteErrorAdapter,
    create_error_adapter,
)

# 导入查询组件
from .query import (
    DefaultQueryBuilder, FilterBuilder, FilterValidator,
    RequestValidator, DefaultRequestValidator,
)

# 导入工具组件
from .utils import (
    ConnectionManager, DefaultConnectionManager,
    format_sql, validate_connection_string, parse_connection_string,
    create_connection_config, merge_configs,
)

# 版本信息
__version__ = "1.0.0"

# 导出公共API
__all__ = [
    # ==================== 核心类型 ====================
    "DatabaseValue", "DatabaseRecord", "DatabaseRecords", "QueryParameters",
    "DatabaseType", "SortOrder", "ComparisonOperator", "LogicalOperator", "JoinType",
    "TransactionIsolation", "ConnectionPoolStatus",
    "DatabaseConnection", "DatabaseTransaction", "DatabasePool",
    "QueryResult",

    # ==================== 数据模型 ====================
    # 过滤器和查询组件
    "QueryFilter", "QueryFilterGroup",
    "QuerySort", "QueryJoin", "QueryAggregation", "QueryGroupBy",

    # 请求和响应模型
    "QueryRequest", "InsertRequest", "UpdateRequest", "DeleteRequest",
    "QueryResponse", "OperationResponse",

    # 配置模型
    "ConnectionConfig",

    # ==================== 异常体系 ====================
    # 基础异常
    "RDBError",

    # 连接异常
    "ConnectionError", "ConnectionTimeoutError", "ConnectionPoolError",
    "AuthenticationError", "DatabaseNotFoundError",

    # 查询异常
    "QueryError", "SQLSyntaxError", "QueryTimeoutError",
    "TableNotFoundError", "ColumnNotFoundError",

    # 数据异常
    "DataError", "ValidationError", "IntegrityError",
    "UniqueConstraintError", "ForeignKeyConstraintError",
    "CheckConstraintError", "NotNullConstraintError",

    # 事务异常
    "TransactionError", "TransactionRollbackError",
    "DeadlockError", "SerializationError",

    # 配置异常
    "ConfigurationError", "UnsupportedDatabaseError", "UnsupportedFeatureError",

    # 操作异常
    "OperationError", "BulkOperationError", "MigrationError",

    # 工具函数
    "wrap_database_error",

    # ==================== 核心接口 ====================
    "DatabaseClient", "QueryBuilder", "DatabaseDialect",
    "ConnectionPoolManager", "ResultAdapter", "DatabaseClientFactory",

    # ==================== 适配器组件 ====================
    "BaseAdapter", "DefaultResultAdapter", "StreamingResultAdapter",
    "ErrorAdapter", "DefaultErrorAdapter",
    "MySQLErrorAdapter", "PostgreSQLErrorAdapter", "SQLiteErrorAdapter",
    "create_error_adapter",

    # ==================== 查询组件 ====================
    "DefaultQueryBuilder", "FilterBuilder", "FilterValidator",
    "RequestValidator", "DefaultRequestValidator",

    # ==================== 工具组件 ====================
    "ConnectionManager", "DefaultConnectionManager",
    "format_sql", "validate_connection_string", "parse_connection_string",
    "create_connection_config", "merge_configs",
]

# 便捷函数
def create_query_filter(field: str, operator: str, value: DatabaseValue) -> QueryFilter:
    """创建查询过滤器的便捷函数"""
    op = ComparisonOperator(operator)
    return QueryFilter(field=field, operator=op, value=value)


def create_and_filter_group(*filters: QueryFilter) -> QueryFilterGroup:
    """创建AND过滤器组的便捷函数"""
    return QueryFilterGroup(
        operator=LogicalOperator.AND,
        filters=list(filters)
    )


def create_or_filter_group(*filters: QueryFilter) -> QueryFilterGroup:
    """创建OR过滤器组的便捷函数"""
    return QueryFilterGroup(
        operator=LogicalOperator.OR,
        filters=list(filters)
    )


def create_simple_query(
    table: str,
    columns: list[str] = None,
    where_conditions: dict = None,
    limit: int = None,
    offset: int = None,
    order_by: list[tuple[str, str]] = None
) -> QueryRequest:
    """创建简单查询的便捷函数
    
    Args:
        table: 表名
        columns: 选择的列
        where_conditions: WHERE条件字典，格式为 {field: value}
        limit: 限制条数
        offset: 偏移量
        order_by: 排序条件，格式为 [(field, direction), ...]
    
    Returns:
        QueryRequest对象
    """
    # 构建过滤器
    filters = None
    if where_conditions:
        filter_list = [
            QueryFilter(field=field, operator=ComparisonOperator.EQ, value=value)
            for field, value in where_conditions.items()
        ]
        filters = QueryFilterGroup(
            operator=LogicalOperator.AND,
            filters=filter_list
        )
    
    # 构建排序
    sorts = None
    if order_by:
        sorts = [
            QuerySort(field=field, order=SortOrder(direction.lower()))
            for field, direction in order_by
        ]
    
    return QueryRequest(
        table=table,
        columns=columns,
        filters=filters,
        sorts=sorts,
        limit=limit,
        offset=offset
    )


# 添加便捷函数到导出列表
__all__.extend([
    "create_query_filter",
    "create_and_filter_group", 
    "create_or_filter_group",
    "create_simple_query",
])

# 模块元信息
__author__ = "HSBC Knowledge Team"
__description__ = "企业级关系型数据库抽象层"
__license__ = "MIT"

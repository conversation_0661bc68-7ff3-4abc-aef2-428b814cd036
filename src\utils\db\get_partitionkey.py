import re
import base64
import hashlib


def convert_to_fixed_29_bytes(name: str) -> str:
    """将输入字符串转换为固定 29 字节的 PostgreSQL 合法分区键"""
    prefix_str = 'hsbc_embedding_data_partition_for_'  # 前缀，34 字节
    max_prefix_bytes = 18  # 前缀 18 字节
    hash_bytes = 10  # 后缀 10 字节
    separator = '_'  # 分隔符 1 字节

    # 剔除前缀（如果存在）
    if name.startswith(prefix_str):
        name = name[len(prefix_str):]
    if not name:
        name = 'default'

    # 规范化名称：替换非法字符，转换为小写
    normalized_name = re.sub(r'[^a-z0-9_]', '_', name.lower()).rstrip('_')
    if not normalized_name:
        normalized_name = 'default'

    # 生成前缀，补齐到 18 字节
    prefix = ''
    byte_count = 0
    for char in normalized_name:
        char_bytes = len(char.encode('utf-8'))
        if byte_count + char_bytes > max_prefix_bytes:
            break
        prefix += char
        byte_count += char_bytes

    # 补齐前缀到 18 字节
    while byte_count < max_prefix_bytes:
        prefix += '_'
        byte_count += 1

    # 确保前缀以字母或下划线开头
    if prefix and prefix[0].isdigit():
        prefix = f"p_{prefix}"[:max_prefix_bytes]
        byte_count = len(prefix.encode('utf-8'))
        while byte_count < max_prefix_bytes:
            prefix += '_'
            byte_count += 1

    # 计算 MD5 哈希，转换为 Base64
    md5_hash = hashlib.md5(name.encode('utf-8')).digest()
    hash_suffix = base64.b64encode(md5_hash).decode('utf-8').rstrip('=')[:hash_bytes]
    hash_suffix = hash_suffix.replace('/', '_').replace('+', '_').replace('=', '_')

    # 拼接
    fixed_name = f"{prefix}{separator}{hash_suffix}"

    # 验证长度
    final_bytes = len(fixed_name.encode('utf-8'))
    if final_bytes != 29:
        # 调整后缀长度（处理超长或不足）
        excess = final_bytes - 29
        if excess > 0:
            hash_suffix = hash_suffix[:hash_bytes - excess]
        elif excess < 0:
            hash_suffix = hash_suffix + '_' * (-excess)
        fixed_name = f"{prefix}{separator}{hash_suffix}"

    # 最终验证
    if len(fixed_name.encode('utf-8')) != 29:
        raise ValueError(f"Generated partition_key length is {len(fixed_name.encode('utf-8'))}, expected 29")

    # 确保合法标识符
    if not fixed_name[0].isalpha() and fixed_name[0] != '_':
        fixed_name = f"p_{fixed_name}"[:29]

    return fixed_name


def get_or_create_partition_key(content: str, mysql_client) -> str:
    """
    获取或创建分区键 - 兼容新的基类架构
    """
    try:
        # 查询是否已存在 - 使用新的基类方法
        existing_results = mysql_client.select(
            table="partition_key",
            condition={"content": content}
        )
        
        if existing_results and len(existing_results) > 0:
            partition_key = existing_results[0]["partition_key"]
            return f"{partition_key}"

        # 生成 29 字节的 partition_key
        partition_key = convert_to_fixed_29_bytes(content)

        # 检查 partition_key 是否重复
        counter = 1
        original_key = partition_key
        while True:
            check_results = mysql_client.select(
                table="partition_key",
                condition={"partition_key": partition_key}
            )
            
            if not check_results or len(check_results) == 0:
                break
                
            partition_key = f"{original_key[:26]}{counter:03d}"
            counter += 1

        # 插入 partition_key 表 - 使用新的基类方法
        insert_result = mysql_client.insert(
            table="partition_key",
            data={"content": content, "partition_key": partition_key}
        )
        
        if not insert_result:
            raise Exception(f"Failed to insert partition_key for content: {content}")

        # 返回完整分区名称
        return f"{partition_key}"
        
    except Exception as e:
        # 如果新方法失败，尝试使用老方法（向后兼容）
        if hasattr(mysql_client, 'batch_operation'):
            # 使用老的batch_operation方法
    query_data = {
        "op_type": "SELECT",
        "table_name": "partition_key",
        "data_dict": {
            "logic": "AND",
            "conditions": [{"type": "=", "col_name": "content", "col_val": content}]
        }
    }
    result = mysql_client.batch_operation(query_data)[0]
            
    if result and isinstance(result, list) and len(result) > 0:
        partition_key = result[0]["partition_key"]
        return f"{partition_key}"

            # 生成并检查partition_key
    partition_key = convert_to_fixed_29_bytes(content)

    query_check = {
        "op_type": "SELECT",
        "table_name": "partition_key",
        "data_dict": {
            "logic": "AND",
            "conditions": [{"type": "=", "col_name": "partition_key", "col_val": partition_key}]
        }
    }
    check_result = mysql_client.batch_operation(query_check)[0]
    counter = 1
    original_key = partition_key
    while check_result and len(check_result) > 0:
        partition_key = f"{original_key[:26]}{counter:03d}"
        query_check["data_dict"]["conditions"][0]["col_val"] = partition_key
        check_result = mysql_client.batch_operation(query_check)
        counter += 1

            # 插入数据
    insert_data = {
        "op_type": "INSERT",
        "table_name": "partition_key",
        "data_dict": [{"content": content, "partition_key": partition_key}]
    }
    message = mysql_client.batch_operation(insert_data)
    if message != 'INSERT SUCCESS 1 data':
        raise Exception(f"Failed to insert partition_key for content: {content}")

            return f"{partition_key}"
        else:
            raise Exception(f"Failed to get or create partition key for content: {content}, error: {str(e)}")

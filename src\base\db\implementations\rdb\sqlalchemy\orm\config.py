"""
ORM客户端配置管理

基于universal配置，添加ORM特定的配置选项
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from urllib.parse import quote_plus

# 复用universal的配置基础
from ..universal.config import UniversalConnectionConfig
from ..universal.exceptions import ConfigurationError


@dataclass
class ORMConnectionConfig(UniversalConnectionConfig):
    """ORM数据库连接配置，继承universal配置并添加ORM特定选项"""
    
    # ORM特定配置
    enable_dynamic_models: bool = True
    model_cache_size: int = 100
    auto_reflect_tables: bool = True
    lazy_loading: bool = True
    
    # 查询优化配置
    enable_query_cache: bool = True
    query_cache_size: int = 500
    enable_relationship_loading: bool = True
    
    # 会话配置
    session_autoflush: bool = True
    session_autocommit: bool = False
    session_expire_on_commit: bool = True
    
    # 模型生成配置
    include_views: bool = False
    table_prefix_filter: Optional[str] = None
    table_suffix_filter: Optional[str] = None
    excluded_tables: list = field(default_factory=list)
    use_independent_base: bool = True  # 是否使用独立的declarative base
    
    # 性能配置
    enable_bulk_operations: bool = True
    batch_size: int = 1000
    
    def __post_init__(self):
        """配置后处理"""
        super().__post_init__()
        
        # 验证ORM特定配置
        if self.model_cache_size <= 0:
            raise ConfigurationError("model_cache_size must be positive")
        
        if self.query_cache_size <= 0:
            raise ConfigurationError("query_cache_size must be positive")
        
        if self.batch_size <= 0:
            raise ConfigurationError("batch_size must be positive")

    def __str__(self) -> str:
        """安全的字符串表示，隐藏敏感信息"""
        # 隐藏密码信息
        safe_url = self.database_url
        if '@' in safe_url:
            # 格式: scheme://user:password@host:port/database
            parts = safe_url.split('@')
            if len(parts) >= 2:
                scheme_user_pass = parts[0]
                if ':' in scheme_user_pass:
                    scheme_user = scheme_user_pass.rsplit(':', 1)[0]
                    safe_url = f"{scheme_user}:***@{'@'.join(parts[1:])}"

        return f"ORMConnectionConfig(database_url='{safe_url}', pool_size={self.pool_size})"

    def __repr__(self) -> str:
        """安全的repr表示"""
        return self.__str__()

    @classmethod
    def from_universal_config(
        cls,
        universal_config: UniversalConnectionConfig,
        **orm_options
    ) -> 'ORMConnectionConfig':
        """
        从universal配置创建ORM配置
        
        Args:
            universal_config: Universal配置实例
            **orm_options: ORM特定配置选项
        
        Returns:
            ORMConnectionConfig实例
        """
        # 获取universal配置的所有字段
        universal_dict = {
            field.name: getattr(universal_config, field.name)
            for field in universal_config.__dataclass_fields__.values()
        }
        
        # 合并ORM选项
        universal_dict.update(orm_options)
        
        return cls(**universal_dict)
    
    def to_universal_config(self) -> UniversalConnectionConfig:
        """
        转换为universal配置（去除ORM特定选项）
        
        Returns:
            UniversalConnectionConfig实例
        """
        universal_fields = set(UniversalConnectionConfig.__dataclass_fields__.keys())
        universal_dict = {
            field: getattr(self, field)
            for field in universal_fields
            if hasattr(self, field)
        }
        
        return UniversalConnectionConfig(**universal_dict)

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ORMConnectionConfig':
        """
        从字典创建ORM配置

        Args:
            config_dict: 配置字典

        Returns:
            ORMConnectionConfig实例
        """
        return cls(**config_dict)

    @classmethod
    def from_components(
        cls,
        dialect: str,
        host: str,
        database: str,
        username: str,
        password: str,
        port: Optional[int] = None,
        driver: Optional[str] = None,
        **options
    ) -> 'ORMConnectionConfig':
        """
        从组件创建ORM配置

        Args:
            dialect: 数据库方言
            host: 主机
            database: 数据库名
            username: 用户名
            password: 密码
            port: 端口
            driver: 驱动
            **options: 其他选项

        Returns:
            ORMConnectionConfig实例
        """
        # 使用universal的配置创建方法
        connection_params = {
            'host': host,
            'database': database,
            'username': username,
            'password': password
        }
        if port is not None:
            connection_params['port'] = port
        if driver is not None:
            connection_params['driver'] = driver

        return create_orm_config_for_database(dialect, connection_params, **options)


def create_orm_config_for_database(
    database_type: str,
    connection_params: Dict[str, Any],
    **options
) -> ORMConnectionConfig:
    """
    为特定数据库类型创建ORM配置

    Args:
        database_type: 数据库类型 (mysql, postgresql, etc.)
        connection_params: 连接参数
        **options: 额外配置选项

    Returns:
        ORMConnectionConfig实例
    """
    # 处理SQLite特殊情况
    if database_type == 'sqlite':
        if 'database' not in connection_params:
            raise ConfigurationError("SQLite requires 'database' parameter (file path)")

        database_path = connection_params['database']
        database_url = f"sqlite:///{database_path}"

        # 合并默认配置
        default_config = get_default_orm_config('sqlite')
        options.update(default_config)

        return ORMConnectionConfig(database_url=database_url, **options)

    # 对于其他数据库类型，使用universal的配置创建方法
    required_params = ['host', 'database', 'username', 'password']
    missing_params = [param for param in required_params if param not in connection_params]
    if missing_params:
        raise ConfigurationError(f"Missing required parameters: {missing_params}")

    # 分离ORM特定选项和universal选项
    orm_specific_options = {}
    universal_options = {}

    orm_fields = set(ORMConnectionConfig.__dataclass_fields__.keys())
    universal_fields = set(UniversalConnectionConfig.__dataclass_fields__.keys())

    for key, value in options.items():
        if key in orm_fields and key not in universal_fields:
            orm_specific_options[key] = value
        elif key not in universal_fields:
            # ORM特有的选项，不传递给universal
            orm_specific_options[key] = value
        else:
            universal_options[key] = value

    # 使用universal的配置创建方法
    from ..universal.config import create_config_for_database
    universal_config = create_config_for_database(database_type, connection_params, **universal_options)

    # 转换为ORM配置并添加ORM特定选项
    return ORMConnectionConfig.from_universal_config(universal_config, **orm_specific_options)


# 预定义的数据库配置模板
ORM_DATABASE_CONFIGS = {
    'mysql': {
        'enable_dynamic_models': True,
        'auto_reflect_tables': True,
        'enable_query_cache': True,
        'enable_bulk_operations': True,
    },
    'postgresql': {
        'enable_dynamic_models': True,
        'auto_reflect_tables': True,
        'enable_query_cache': True,
        'enable_relationship_loading': True,
    },
    'sqlite': {
        'enable_dynamic_models': True,
        'auto_reflect_tables': True,
        'enable_query_cache': False,  # SQLite内存数据库不需要查询缓存
        'session_autoflush': False,   # SQLite优化
    }
}


def get_default_orm_config(database_type: str) -> Dict[str, Any]:
    """
    获取数据库类型的默认ORM配置
    
    Args:
        database_type: 数据库类型
    
    Returns:
        默认配置字典
    """
    return ORM_DATABASE_CONFIGS.get(database_type, {})

# @package _here_
# PGVector性能配置

# 批量操作配置
batch:
  size: 100
  max_size: 1000
  timeout: 60

# 缓存配置
cache:
  enable: true
  ttl: 300
  max_size: 10000
  
# 连接管理
connection:
  timeout: 30
  query_timeout: 60
  max_retries: 3
  retry_delay: 1.0

# 监控配置
monitoring:
  enable: true
  log_slow_queries: true
  slow_query_threshold: 5.0
  collect_metrics: true
  
# 性能优化
optimization:
  enable_prepared_statements: true
  enable_connection_pooling: true
  enable_query_optimization: true

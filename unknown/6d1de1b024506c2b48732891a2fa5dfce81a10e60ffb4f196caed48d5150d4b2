"""
RDB适配器层

提供数据库特定结果到统一格式的转换功能

适配器类型：
- BaseAdapter: 基础适配器，提供通用转换逻辑
- ResultAdapter: 结果适配器，转换查询和操作结果
- ErrorAdapter: 错误适配器，转换数据库异常
"""

from .base_adapter import BaseAdapter
from .result_adapter import DefaultResultAdapter, StreamingResultAdapter
from .error_adapter import (
    ErrorAdapter, DefaultErrorAdapter,
    MySQLErrorAdapter, PostgreSQLErrorAdapter, SQLiteErrorAdapter,
    create_error_adapter
)

__all__ = [
    # 基础适配器
    "BaseAdapter",

    # 结果适配器
    "DefaultResultAdapter",
    "StreamingResultAdapter",

    # 错误适配器
    "ErrorAdapter",
    "DefaultErrorAdapter",
    "MySQLErrorAdapter",
    "PostgreSQLErrorAdapter",
    "SQLiteErrorAdapter",
    "create_error_adapter",
]

#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：src 
@File    ：file_utils.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/22 17:26 
@Desc    ：文件相关的小工具
"""
import os
import subprocess

from loguru import logger
import markdown
from PIL import Image

from modules.knowledge.doc.entities.base_models import (DocumentFormat,
                                                        DocumentType)

def get_file_extension(file_path: str) -> str:
    return os.path.splitext(file_path)[1].lower()

def get_file_name(file_path: str) -> str:
    return os.path.basename(file_path)

def detect_format(file_path: str) -> DocumentFormat | None:
    """
    根据文件路径后缀判断文件格式

    Args:
        file_path: 文件路径

    Returns:
        DocumentFormat枚举值，如果无法匹配则返回None
    """
    if not file_path:
        return None

    # 获取文件扩展名（小写，带点）
    ext_with_dot = os.path.splitext(file_path)[1].lower()

    # 移除点，获取纯扩展名
    if ext_with_dot:
        ext = ext_with_dot[1:]  # 例如：".pdf" -> "pdf"
    else:
        return None

    # 匹配枚举值
    try:
        return DocumentFormat(ext)
    except ValueError:
        return None


def detect_document_type(input_str: str) -> DocumentType | None:
    """
    将字符串转换为DocumentType枚举值

    Args:
        input_str: 输入字符串，如 "outside" 或 "inside"

    Returns:
        DocumentType枚举值，如果无法匹配则返回None
    """
    if not input_str:
        return None

    try:
        # 忽略大小写，通过值查找枚举成员
        return DocumentType(input_str.lower())
    except ValueError:
        return None

def convert_to_md(text: str) -> str:
    md = markdown.markdown(text)
    return md

def convert_tiff_to_pdf(input_path, output_path):
    with Image.open(input_path) as img:
        # 获取 TIFF 文件中的所有页面
        images = []
        while True:
            images.append(img.copy())
            try:
                img.seek(img.tell() + 1)
            except EOFError:
                break
        images = [image.convert("RGB") for image in images]
        images[0].save(output_path, "PDF", save_all=True, append_images=images[1:])
        logger.info(f"Converted TIFF to PDF: {output_path}")

def convert_doc_to_docx(input_path: str, output_path: str) -> str:
    """
    使用 LibreOffice 命令行将 .doc 文件转换为 .docx 文件。

    Args:
        input_path (str): 输入的 .doc 文件路径
        output_path (str): 输出目录

    Returns:
        str: 转换后的 .docx 文件路径
    """
    if not os.path.exists(input_path):
        raise FileNotFoundError(f"Input file {input_path} does not exist")

    output_dir = os.path.dirname(output_path)
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # LibreOffice 命令
    command = [
        "soffice",
        "--headless",
        "--convert-to", "pdf",
        input_path,
        "--outdir", output_dir
    ]

    try:
        subprocess.run(command, check=True, capture_output=True, text=True)
        temp_file = os.path.splitext(input_path)[0] + ".pdf"
        if os.path.exists(temp_file):
            os.rename(temp_file, output_path)
        if not os.path.exists(output_path):
            raise FileNotFoundError(f"Conversion failed: {output_path} not found")
        logger.info(f"Converted {input_path} to {output_path}")
        return output_path
    except subprocess.CalledProcessError as e:
        raise Exception(f"Conversion error: {e.stderr}")

def batch_chunks(chunks: list[str], batch_size: int = 10, max_chars: int = 2000) -> list[str]:
    """
    将数据块按每组指定数量或字符数限制进行拼接

    Args:
        chunks: 原始数据块列表
        batch_size: 每组的数量，默认为10
        max_chars: 每组合并后的最大字符数，默认为2000

    Returns:
        拼接后的批次列表
    """
    if not chunks:
        return []

    batches = []
    current_batch = []
    current_size = 0  # 当前批次的字符总数

    for chunk in chunks:
        # 检查添加当前块后是否会超过字符限制
        if current_size + len(chunk) > max_chars and current_batch:
            batches.append(''.join(current_batch))
            current_batch = []
            current_size = 0

        current_batch.append(chunk)
        current_size += len(chunk)

        # 当当前批次达到指定大小时，也进行拼接
        if len(current_batch) == batch_size:
            batches.append(''.join(current_batch))
            current_batch = []
            current_size = 0

    # 处理剩余的不足一组的数据
    if current_batch:
        batches.append(''.join(current_batch))

    return batches


def extract_law_info(texts):
    import re
    # 初始化结果列表
    result = []

    # 正则表达式：匹配《法规名》（法规号）或《法规名》[法规号]等格式
    pattern = r'《([^》]+)》(?:\s*[(（\[【]([\w\-\/]+号)[)）\]】])?'

    for text in texts:
        # 查找所有匹配项
        matches = re.finditer(pattern, text)
        for match in matches:
            law_name = match.group(1)  # 法规文件名
            law_number = match.group(2) if match.group(2) else ""  # 法规号（可能为空）
            result.append({
                'law_name': law_name,
                'law_number': law_number
            })

    return result


if __name__ == '__main__':
    print(get_file_name("/home/<USER>/docs/report.pdf"))
    print(get_file_extension("/home/<USER>/docs/report.pdf"))
    print(detect_format("/home/<USER>/docs/report.pdf"))

    text1 = """
    第一条 为提高金融机构依法合规经营能力,根据《中华人
民共和国银行业监督管理法》《中华人民共和国商业银行法》
《中华人民共和国保险法》《中华人民共和国信托法》等法律
法规,制定本办法。
第二条 依法由国家金融监督管理总局及其派出机构监管的
政策性银行、商业银行、金融资产管理公司、企业集团财务公
司、金融租赁公司、汽车金融公司、消费金融公司、货币经纪
公司、信托公司、理财公司、金融资产投资公司、保险公司
(包括再保险公司)、保险资产管理公司、保险集团(控股)公
司、相互保险组织等机构(以下统称金融机构)适用本办法。
    """
    text2 = """
    第五十八条 本办法由国家金融监督管理总局负责解释,自
2025年3月1日起施行,过渡期为本办法施行之日起一年。不符
合本办法规定的,应当在过渡期内完成整改。《商业银行合规
风险管理指引》(银监发〔2006〕76号)、《保险公司合规管理
办法》(保监发〔2016〕116号)、《中国保监会关于进一步加
强保险公司合规管理工作有关问题的通知》(保监发〔2016〕
38号)同时废止。其他部门规章、规范性文件与本办法不一致
的,以本办法为准。
    """
    print(extract_law_info([text1, text2]))



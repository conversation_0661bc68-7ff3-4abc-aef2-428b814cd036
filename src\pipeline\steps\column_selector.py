"""
列选择步骤 - 多Schema片段专用版本
从多个Schema片段的候选列中选择最相关的列，支持并发处理和智能合并
"""

from typing import Dict, Any, List, Union, Tuple
import json
import ast
import asyncio
from copy import deepcopy
import logging

logger = logging.getLogger(__name__)

from pipeline.core.base_step import LLMStep
from pipeline.core.context import PipelineContext

class ColumnSelectorStep(LLMStep):
    """
    列选择步骤 - 多Schema片段专用版本

    功能：
    - 并发处理多个Schema片段
    - 智能合并多个片段的列选择结果
    - 自动去重和结果优化

    输入格式：
    context.db_schema = [
        {
            "prompt": "CREATE TABLE ...",
            "db_to_tables": {"db1": ["table1"]},
            "table_to_columns": {"table1": ["col1", "col2"]}
        },
        ...
    ]

    输出格式：
    context.candidate_columns = {
        "table1": ["col1", "col2"],
        "table2": ["col3", "col4"]
    }
    """

    def __init__(self):
        super().__init__(
            name="column_selector",
            description="多Schema片段并发列选择，智能合并结果",
            template_name="select_columns",
            parser_name="select_columns",
            num_calls=1
        )
    
    async def preprocess(self, context: PipelineContext) -> Union[List[Dict[str, Any]], None]:
        """阶段1: 预处理 - 构建多Schema片段的prompt变量"""
        db_schema_list = context.get("db_schema", [])

        # 验证输入格式
        if not isinstance(db_schema_list, list):
            logger.error(f"db_schema必须是列表格式，当前类型: {type(db_schema_list)}")
            return None

        if not db_schema_list:
            logger.warning("Schema片段列表为空")
            return None

        return self._build_fragment_prompts(db_schema_list, context)

    def _build_fragment_prompts(self, db_schema_list: List[Dict[str, Any]], context: PipelineContext) -> List[Dict[str, Any]]:
        """构建多Schema片段的prompt数据"""
        # 使用filter和列表推导式过滤有效的schema片段
        def is_valid_fragment(fragment_with_index):
            i, fragment = fragment_with_index
            if not isinstance(fragment, dict):
                logger.warning(f"Schema片段 {i} 格式无效，跳过")
                return False
            if not fragment.get("prompt", "").strip():
                logger.warning(f"Schema片段 {i} 的prompt为空，跳过")
                return False
            if not fragment.get("table_to_columns", {}):
                logger.warning(f"Schema片段 {i} 的table_to_columns为空，跳过")
                return False
            return True

        valid_fragments = [
            {
                "prompt": fragment["prompt"],
                "table_to_columns": fragment["table_to_columns"]
            }
            for i, fragment in filter(is_valid_fragment, enumerate(db_schema_list))
        ]

        if not valid_fragments:
            logger.error("没有找到有效的Schema片段")
            return None

        logger.info(f"构建 {len(valid_fragments)} 个有效Schema片段的prompt数据")

        # 使用列表推导式构建prompt数据
        fragment_prompts = [
            {
                "system": {
                    "QUESTION": context.user_question,
                    "HINT": context.hint,
                    "DATABASE_SCHEMA": fragment["prompt"]
                },
                "user": {
                    "CANDIDATE_COLUMNS": fragment["table_to_columns"]
                }
            }
            for fragment in valid_fragments
        ]

        return fragment_prompts

    async def process(self, fragment_prompts: List[Dict[str, Any]], context: PipelineContext) -> List[str]:
        """阶段2: 核心处理 - 并发处理多个Schema片段"""
        if not fragment_prompts:
            logger.warning("没有有效的片段prompts")
            return []

        logger.info(f"开始并发处理 {len(fragment_prompts)} 个Schema片段")

        # 使用列表推导式创建并发任务
        tasks = [
            self._process_single_fragment(i, prompt_data, context)
            for i, prompt_data in enumerate(fragment_prompts)
        ]

        # 并发执行所有任务
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果，过滤异常
            valid_responses = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"片段 {i} 处理失败: {result}")
                elif isinstance(result, list) and result:
                    valid_responses.extend(result)
                    logger.info(f"片段 {i} 处理成功，获得 {len(result)} 个响应")
                else:
                    logger.warning(f"片段 {i} 返回空结果")

            logger.info(f"多片段处理完成，总共获得 {len(valid_responses)} 个有效响应")
            return valid_responses

        except Exception as e:
            logger.error(f"多片段并发处理失败: {e}")
            return []



    async def _process_single_fragment(self, fragment_index: int, prompt_data: Dict[str, Any], context: PipelineContext) -> List[str]:
        """处理单个Schema片段"""
        try:
            # 构建messages
            messages = await self.build_prompt(prompt_data, context)

            # 调用LLM
            responses = await self.call_llm(messages, context)

            logger.debug(f"片段 {fragment_index} LLM调用成功")
            return responses

        except Exception as e:
            logger.error(f"片段 {fragment_index} 处理失败: {e}")
            # 返回异常而不是空列表，让上层处理
            raise
    
    async def parse_result(self, result: List[str], context: PipelineContext) -> List[Any]:
        """阶段3: 解析LLM结果（支持多片段结果）"""
        if not result:
            logger.warning("没有LLM结果需要解析")
            return []

        # 先使用父类的解析器解析
        parsed_results = await super().parse_result(result, context)

        # 进一步处理解析结果
        final_results = []
        for i, parsed in enumerate(parsed_results):
            if isinstance(parsed, str):
                try:
                    # 尝试JSON解析
                    parsed = json.loads(parsed)
                except json.JSONDecodeError:
                    try:
                        # 备选：Python字面量解析
                        parsed = ast.literal_eval(parsed)
                    except (SyntaxError, ValueError):
                        logger.error(f"无法解析列选择结果 {i}: {parsed[:200] if parsed else 'None'}...")
                        parsed = {}

            # 确保解析结果是字典格式
            if isinstance(parsed, dict):
                final_results.append(parsed)
            else:
                logger.warning(f"解析结果 {i} 不是字典格式，跳过: {type(parsed)}")

        logger.info(f"成功解析 {len(final_results)} 个有效的列选择结果")
        return final_results
    
    async def postprocess(self, parsed_results: List[Any], context: PipelineContext) -> Dict[str, List[str]]:
        """阶段4: 后处理 - 合并多个片段的列选择结果"""
        if not parsed_results:
            logger.warning("列选择步骤没有获得有效结果")
            return {}

        return self._merge_multiple_results(parsed_results)

    def _merge_multiple_results(self, results: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """合并多个片段的列选择结果"""
        logger.info(f"开始合并 {len(results)} 个片段的列选择结果")

        merged_columns = {}

        for i, result in enumerate(results):
            if not isinstance(result, dict):
                logger.warning(f"结果 {i} 格式不正确，跳过: {type(result)}")
                continue

            # 处理列信息，移除思考过程
            result_columns = deepcopy(result)
            result_columns.pop('chain_of_thought_reasoning', None)

            # 合并到总结果中
            for table_name, columns in result_columns.items():
                if not isinstance(columns, list):
                    logger.warning(f"结果 {i} 中表 {table_name} 的列不是列表格式，跳过")
                    continue

                if table_name not in merged_columns:
                    merged_columns[table_name] = []

                # 合并列，去重但保持顺序
                for column in columns:
                    if column not in merged_columns[table_name]:
                        merged_columns[table_name].append(column)

        # 过滤掉空列表的表项
        filtered_columns = {table_name: columns for table_name, columns in merged_columns.items() if columns}
        
        # 记录过滤前后的变化
        filtered_count = len(merged_columns) - len(filtered_columns)
        if filtered_count > 0:
            logger.info(f"过滤掉 {filtered_count} 个空列列表的表项")

        # 记录合并结果
        total_tables = len(filtered_columns)
        total_columns = sum(len(cols) for cols in filtered_columns.values())
        logger.info(f"合并完成: {total_tables} 个表，共 {total_columns} 个列")

        # 记录详细的合并信息
        for table_name, columns in filtered_columns.items():
            logger.debug(f"表 {table_name}: {len(columns)} 个列 - {columns}")

        return filtered_columns
    
    async def update_context(self, result: Dict[str, List[str]], context: PipelineContext) -> None:
        """更新上下文 - 存储选择的列并更新schema_generation_params"""
        context.set("candidate_columns", result)

        # 更新schema_generation_params用于下一个schema_generator
        current_params = context.get("schema_generation_params", {})
        updated_params = current_params.copy()

        # 移除table_names，添加candidate_columns
        if "table_names" in updated_params:
            del updated_params["table_names"]
        updated_params["candidate_columns"] = result

        # 保持无限制列数和中间模式
        updated_params["column_limit"] = None
        updated_params["is_final"] = False

        context.set("schema_generation_params", updated_params)

        # 记录更新信息
        total_columns = sum(len(cols) for cols in result.values())
        logger.info(f"已更新上下文：candidate_columns包含{len(result)}个表，共{total_columns}个列")
        logger.info(f"已更新schema_generation_params：candidate_columns包含{len(result)}个表")
    
    def format_display_result(self, final_result: Dict[str, Any]) -> str:
        """
        格式化多Schema片段合并结果用于显示

        Args:
            final_result: 合并后的列选择结果

        Returns:
            格式化后的字符串
        """
        if not final_result or not isinstance(final_result, dict):
            return "❌ 未获得有效的筛选列结果或结果格式不正确。"

        display_parts = ["## 📋 多Schema片段列选择结果：\n"]

        if not final_result:
            display_parts.append("- 未筛选出任何相关字段。")
        else:
            # 统计信息
            total_tables = len(final_result)
            total_columns = sum(len(cols) if isinstance(cols, list) else 0 for cols in final_result.values())
            display_parts.append(f"📊 **统计**: {total_tables} 个表，共 {total_columns} 个字段\n")

            # 显示每个表的列
            for table_name, columns in final_result.items():
                if isinstance(columns, list):
                    if columns:
                        display_parts.append(f"- **表名:** `{table_name}` ({len(columns)} 个字段)")
                        display_parts.append(f"  - **选中列:** {', '.join(f'`{col}`' for col in columns)}")
                    else:
                        display_parts.append(f"- **表名:** `{table_name}` (未选中列)")
                else:
                    display_parts.append(f"- **表名:** `{table_name}` (数据格式错误，非列表)")

        return "\n".join(display_parts)

"""
结果处理模块 - 搜索结果的通用处理工具

本模块提供：
1. 搜索结果的格式化和标准化
2. 多来源结果的合并和去重
3. 结果排序和过滤
4. 结果统计和分析

设计原则：
- 支持不同表结构的灵活处理
- 提供统一的结果格式
- 可配置的处理策略
"""

import json
from typing import List, Dict, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from collections import defaultdict
from loguru import logger


@dataclass
class ResultProcessConfig:
    """结果处理配置"""
    unique_key: str = "content_id"  # 用于去重的关键字段
    sort_key: str = "distance"  # 排序字段
    sort_ascending: bool = True  # 是否升序排序
    max_results: int = 100  # 最大结果数量
    distance_threshold: Optional[float] = None  # 距离阈值过滤
    required_fields: List[str] = field(default_factory=list)  # 必需字段
    exclude_fields: List[str] = field(default_factory=list)  # 排除字段


@dataclass
class ProcessedResult:
    """处理后的结果对象"""
    results: List[Dict[str, Any]]
    metadata: Dict[str, Any] = field(default_factory=dict)


def merge_search_results(
    results_dict: Dict[str, List[Dict[str, Any]]],
    config: Optional[ResultProcessConfig] = None
) -> ProcessedResult:
    """
    合并多个搜索结果并去重
    
    适配原workflow中的merge_search_results逻辑，但更加通用
    
    Args:
        results_dict: 搜索结果字典，格式为 {search_type: [results], ...}
        config: 处理配置
        
    Returns:
        ProcessedResult: 处理后的结果
    """
    try:
        config_obj = config or ResultProcessConfig()
        
        if not results_dict:
            logger.warning("搜索结果字典为空")
            return ProcessedResult(results=[], metadata={"status": "empty_input"})
        
        # 使用字典存储合并结果，以unique_key为键
        merged_results = {}
        source_counts = {}
        
        # 遍历所有搜索类型和结果
        for search_type, items in results_dict.items():
            source_counts[search_type] = len(items) if items else 0
            
            if not items:
                logger.info(f"搜索类型 {search_type} 无结果")
                continue
                
            for item in items:
                if not isinstance(item, dict):
                    logger.warning(f"跳过非字典类型的项目: {type(item)}")
                    continue
                
                # 获取唯一标识符
                unique_value = item.get(config_obj.unique_key)
                if not unique_value:
                    logger.warning(f"项目缺少唯一标识符 '{config_obj.unique_key}': {item}")
                    continue
                
                # 如果是新的唯一值，添加到结果中
                if unique_value not in merged_results:
                    # 创建处理后的项目
                    processed_item = process_single_item(item, config_obj)
                    if processed_item:
                        processed_item["_source_type"] = search_type  # 添加来源信息
                        merged_results[unique_value] = processed_item
                # 如果已存在，可以选择保留距离更小的（如果有距离字段）
                elif config_obj.sort_key in item and config_obj.sort_key in merged_results[unique_value]:
                    existing_score = merged_results[unique_value].get(config_obj.sort_key, float('inf'))
                    new_score = item.get(config_obj.sort_key, float('inf'))
                    
                    if (config_obj.sort_ascending and new_score < existing_score) or \
                       (not config_obj.sort_ascending and new_score > existing_score):
                        processed_item = process_single_item(item, config_obj)
                        if processed_item:
                            processed_item["_source_type"] = search_type
                            merged_results[unique_value] = processed_item
        
        # 转换为列表并排序
        final_results = list(merged_results.values())
        
        # 应用排序
        if config_obj.sort_key and final_results:
            try:
                final_results.sort(
                    key=lambda x: x.get(config_obj.sort_key, float('inf') if config_obj.sort_ascending else float('-inf')),
                    reverse=not config_obj.sort_ascending
                )
            except Exception as e:
                logger.warning(f"排序失败: {e}")
        
        # 限制结果数量
        if config_obj.max_results > 0:
            final_results = final_results[:config_obj.max_results]
        
        logger.info(f"结果合并完成: 原始数量={sum(source_counts.values())}, "
                   f"去重后={len(merged_results)}, 最终={len(final_results)}")
        
        return ProcessedResult(
            results=final_results,
            metadata={
                "status": "success",
                "original_count": sum(source_counts.values()),
                "unique_count": len(merged_results),
                "final_count": len(final_results),
                "source_counts": source_counts,
                "config": config_obj.__dict__
            }
        )
        
    except Exception as e:
        logger.error(f"结果合并失败: {str(e)}")
        return ProcessedResult(
            results=[],
            metadata={"status": "error", "error": str(e)}
        )


def process_single_item(
    item: Dict[str, Any],
    config: ResultProcessConfig
) -> Optional[Dict[str, Any]]:
    """
    处理单个结果项
    
    Args:
        item: 原始结果项
        config: 处理配置
        
    Returns:
        处理后的结果项，如果不符合要求则返回None
    """
    try:
        # 检查必需字段
        if config.required_fields:
            for field in config.required_fields:
                if field not in item or item[field] is None:
                    logger.debug(f"项目缺少必需字段 '{field}': {item}")
                    return None
        
        # 应用距离阈值过滤
        if config.distance_threshold is not None and config.sort_key in item:
            distance = item.get(config.sort_key)
            if distance is not None and distance > config.distance_threshold:
                logger.debug(f"项目距离 {distance} 超过阈值 {config.distance_threshold}")
                return None
        
        # 复制项目并处理字段
        processed_item = {}
        for key, value in item.items():
            # 跳过排除字段
            if key in config.exclude_fields:
                continue
                
            # 处理特殊值
            if value in [None, "null", "NULL"]:
                processed_item[key] = ""
            else:
                processed_item[key] = value
        
        return processed_item
        
    except Exception as e:
        logger.warning(f"处理单个项目失败: {str(e)}, item: {item}")
        return None


def format_search_results(
    results: List[Dict[str, Any]],
    format_type: str = "default"
) -> Union[List[Dict], Dict, str]:
    """
    格式化搜索结果为不同的输出格式
    
    Args:
        results: 搜索结果列表
        format_type: 格式类型 ('default', 'json', 'text', 'summary')
        
    Returns:
        格式化后的结果
    """
    try:
        if not results:
            if format_type == 'json':
                return {}
            elif format_type == 'text':
                return "无搜索结果"
            elif format_type == 'summary':
                return {"total": 0, "results": []}
            return []
        
        if format_type == 'json':
            return json.loads(json.dumps(results, ensure_ascii=False))
        
        elif format_type == 'text':
            text_result = f"搜索结果总数: {len(results)}\n\n"
            for i, result in enumerate(results, 1):
                text_result += f"结果 {i}:\n"
                for key, value in result.items():
                    if key.startswith('_'):  # 跳过内部字段
                        continue
                    text_result += f"  {key}: {value}\n"
                
                # 添加相似度信息（如果有）
                distance = result.get('distance')
                if distance is not None:
                    similarity = max(0, 1 - distance)
                    text_result += f"  相似度: {similarity:.4f}\n"
                
                text_result += "\n"
            return text_result.strip()
        
        elif format_type == 'summary':
            summary = {
                "total": len(results),
                "results": results[:5],  # 只返回前5个结果的摘要
                "has_more": len(results) > 5
            }
            
            # 添加统计信息
            if results:
                distances = [r.get('distance') for r in results if 'distance' in r]
                if distances:
                    summary["distance_stats"] = {
                        "min": min(distances),
                        "max": max(distances),
                        "avg": sum(distances) / len(distances)
                    }
                
                # 统计来源类型
                source_types = [r.get('_source_type') for r in results if '_source_type' in r]
                if source_types:
                    source_counts = {}
                    for source in source_types:
                        source_counts[source] = source_counts.get(source, 0) + 1
                    summary["source_distribution"] = source_counts
            
            return summary
        
        # 默认返回原始列表
        return results
        
    except Exception as e:
        logger.error(f"格式化搜索结果失败: {str(e)}")
        return {"error": f"格式化失败: {str(e)}"}


def extract_candidate_columns(
    merged_results: Dict[str, Any], 
    model_id: str,
    table_prefix: str = "table_"
) -> Dict[str, List[str]]:
    """
    从搜索结果中提取候选列信息，按表组织
    
    适配原workflow中的extract_candidate_columns逻辑
    
    Args:
        merged_results: 合并后的搜索结果
        model_id: 模型ID
        table_prefix: 表名前缀
        
    Returns:
        按表名组织的候选列字典
    """
    try:
        candidate_columns = {}
        
        for key, column_info in merged_results.items():
            # 构建表名（适配SQLAlchemy的要求）
            table_name = f"{table_prefix}{model_id}"
            
            # 获取列名，支持不同的字段名
            column_name = column_info.get("col_name") or column_info.get("column_name") or key
            
            if not column_name:
                logger.warning(f"无法获取列名: {column_info}")
                continue
            
            # 初始化表对应的集合（用于自动去重）
            if table_name not in candidate_columns:
                candidate_columns[table_name] = set()
            
            # 添加到集合，自动去重
            candidate_columns[table_name].add(column_name)
        
        # 将集合转回列表
        for table_name in candidate_columns:
            candidate_columns[table_name] = list(candidate_columns[table_name])
        
        logger.info(f"提取候选列完成: {sum(len(cols) for cols in candidate_columns.values())} 个列, "
                   f"{len(candidate_columns)} 个表")
        
        return candidate_columns
        
    except Exception as e:
        logger.error(f"提取候选列失败: {str(e)}")
        return {}


def filter_results_by_condition(
    results: List[Dict[str, Any]],
    condition: Callable[[Dict[str, Any]], bool]
) -> List[Dict[str, Any]]:
    """
    根据自定义条件过滤结果
    
    Args:
        results: 结果列表
        condition: 过滤条件函数，接受单个结果项，返回布尔值
        
    Returns:
        过滤后的结果列表
    """
    try:
        filtered_results = [result for result in results if condition(result)]
        logger.info(f"条件过滤完成: 原始={len(results)}, 过滤后={len(filtered_results)}")
        return filtered_results
        
    except Exception as e:
        logger.error(f"条件过滤失败: {str(e)}")
        return results


def aggregate_results_by_field(
    results: List[Dict[str, Any]],
    group_by: str,
    aggregation: str = "count"
) -> Dict[str, Any]:
    """
    按字段聚合结果
    
    Args:
        results: 结果列表
        group_by: 分组字段
        aggregation: 聚合方式 ('count', 'avg_distance', 'min_distance', 'max_distance')
        
    Returns:
        聚合结果字典
    """
    try:
        groups = defaultdict(list)
        
        # 分组
        for result in results:
            group_value = result.get(group_by, "unknown")
            groups[group_value].append(result)
        
        # 聚合
        aggregated = {}
        for group_value, group_results in groups.items():
            if aggregation == "count":
                aggregated[group_value] = len(group_results)
            elif aggregation == "avg_distance":
                distances = [r.get('distance') for r in group_results if 'distance' in r]
                aggregated[group_value] = sum(distances) / len(distances) if distances else None
            elif aggregation == "min_distance":
                distances = [r.get('distance') for r in group_results if 'distance' in r]
                aggregated[group_value] = min(distances) if distances else None
            elif aggregation == "max_distance":
                distances = [r.get('distance') for r in group_results if 'distance' in r]
                aggregated[group_value] = max(distances) if distances else None
            else:
                aggregated[group_value] = group_results
        
        logger.info(f"结果聚合完成: 按 {group_by} 分组, {aggregation} 聚合, {len(aggregated)} 个组")
        return aggregated
        
    except Exception as e:
        logger.error(f"结果聚合失败: {str(e)}")
        return {}


def validate_result_structure(
    results: List[Dict[str, Any]],
    expected_fields: List[str]
) -> Dict[str, Any]:
    """
    验证结果结构的完整性
    
    Args:
        results: 结果列表
        expected_fields: 期望的字段列表
        
    Returns:
        验证报告
    """
    try:
        report = {
            "total_results": len(results),
            "valid_results": 0,
            "missing_fields": defaultdict(int),
            "field_coverage": {},
            "errors": []
        }
        
        if not results:
            return report
        
        for i, result in enumerate(results):
            if not isinstance(result, dict):
                report["errors"].append(f"结果 {i} 不是字典类型: {type(result)}")
                continue
                
            is_valid = True
            for field in expected_fields:
                if field not in result:
                    report["missing_fields"][field] += 1
                    is_valid = False
            
            if is_valid:
                report["valid_results"] += 1
        
        # 计算字段覆盖率
        for field in expected_fields:
            missing_count = report["missing_fields"][field]
            coverage = (len(results) - missing_count) / len(results) if len(results) > 0 else 0
            report["field_coverage"][field] = coverage
        
        logger.info(f"结构验证完成: 有效结果={report['valid_results']}/{len(results)}")
        return report
        
    except Exception as e:
        logger.error(f"结构验证失败: {str(e)}")
        return {"error": str(e)}


# 使用示例
if __name__ == "__main__":
    # 示例数据
    sample_results = {
        "col_name_cn": [
            {"content_id": "1", "col_name": "name", "distance": 0.1},
            {"content_id": "2", "col_name": "age", "distance": 0.2}
        ],
        "col_desc": [
            {"content_id": "1", "col_name": "name", "distance": 0.15},  # 重复项
            {"content_id": "3", "col_name": "email", "distance": 0.3}
        ]
    }
    
    # 测试合并功能
    config = ResultProcessConfig(
        unique_key="content_id",
        sort_key="distance",
        max_results=10
    )
    
    result = merge_search_results(sample_results, config)
    print("合并结果:", result.metadata)
    print("结果数量:", len(result.results))
    
    # 测试格式化功能
    text_output = format_search_results(result.results, "text")
    print("\n文本格式:\n", text_output[:200] + "..." if len(text_output) > 200 else text_output) 
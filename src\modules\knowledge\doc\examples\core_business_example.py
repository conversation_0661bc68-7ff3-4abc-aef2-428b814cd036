"""
核心业务流程示例

专注于DOC模块的两个核心业务流程：
1. 文档入库流程 - 完整的层级化文档结构存储
2. 语义搜索流程 - 基于向量的相似度搜索

展示了：
- 使用实体类封装数据操作
- 分块的层级结构存储（parent_id和chapter_layer）
- 可选的向量化存储
- 基于chunk_info_id的向量映射关系
"""

import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

from modules.knowledge.doc.operations.document_ops import DocumentOperation
from modules.knowledge.doc.operations.chunk_ops import ChunkOperation
from modules.knowledge.doc.entities.api_models import DocumentCreate
from modules.knowledge.doc.entities.base_models import (
    DocumentStatus, ParseType, DocumentFormat, Chunk, ChunkInfo
)
from uuid import uuid4

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CoreBusinessWorkflow:
    """核心业务流程演示"""
    
    def __init__(self):
        """初始化操作类"""
        self.doc_ops = DocumentOperation()
        self.chunk_ops = ChunkOperation()
        self.knowledge_id = "core-business-kb"
    
    async def scenario_1_document_ingestion_with_hierarchy(self):
        """
        场景1: 完整的文档入库流程（层级化结构）
        
        业务流程：
        1. 创建文档基础信息 -> doc_documents表
        2. 用户自行拆分chunk和层级 -> 构建分块树状结构
        3. 提取chunk_info（内容、摘要、关键词） -> doc_chunks_info表
        4. 可选：将chunk_info向量化 -> doc_embeddings表
        """
        print("\n" + "="*80)
        print("📄 场景1: 文档入库流程（层级化分块结构）")
        print("="*80)
        
        try:
            # 步骤1: 创建文档基础信息
            print("\n🏗️  步骤1: 创建文档基础信息")
            document_data = DocumentCreate(
                knowledge_id=self.knowledge_id,
                doc_name="系统设计手册.pdf",
                doc_type=None,
                author="技术团队",
                vector_similarity_weight=0.8,
                similarity_threshold=0.75,
                parse_type=ParseType.PDF,
                status=DocumentStatus.PENDING,
                parse_end_time=None,
                parse_message=None,
                doc_format=DocumentFormat.PDF,
                location="/docs/system-design-manual.pdf",
                metadata='{"category": "technical", "version": "v2.1"}',
                created_time=None,
                updated_time=None,
                is_active=True
            )
            
            doc_id = await self.doc_ops.create_document(document_data)
            print(f"✅ 文档创建成功: {doc_id}")
            
            # 步骤2: 构建层级化分块结构
            print("\n🌳 步骤2: 构建层级化分块结构")
            await self.doc_ops.update_document_status(
                doc_id, DocumentStatus.PROCESSING, "开始解析文档结构"
            )
            
            # 模拟用户解析后的分块结构（层级化）
            hierarchical_chunks = await self._create_hierarchical_chunk_structure(doc_id)
            
            print(f"✅ 层级化分块结构创建完成: {len(hierarchical_chunks)} 个分块")
            
            # 步骤3: 展示分块层级关系
            print("\n📊 步骤3: 分块层级关系展示")
            await self._display_chunk_hierarchy(hierarchical_chunks)
            
            # 步骤4: 完成文档处理
            print("\n✅ 步骤4: 完成文档处理")
            await self.doc_ops.update_document_status(
                doc_id, DocumentStatus.COMPLETED, 
                f"解析完成，共生成{len(hierarchical_chunks)}个分块（层级化结构）"
            )
            
            # 更新文档统计信息
            total_chunk_infos = sum(
                len(chunk.chunk_infos) if chunk.chunk_infos else 0 
                for chunk in hierarchical_chunks
            )
            await self.doc_ops.update_document_progress(
                doc_id, chunk_nums=len(hierarchical_chunks), percentage=100.0
            )
            
            print(f"\n🎉 文档入库流程完成！")
            print(f"   📄 文档ID: {doc_id}")
            print(f"   🌳 分块总数: {len(hierarchical_chunks)}")
            print(f"   📝 信息条目: {total_chunk_infos}")
            print(f"   🔗 层级关系: 已建立父子关系")
            
            return doc_id, hierarchical_chunks
            
        except Exception as e:
            logger.error(f"文档入库流程失败: {e}")
            raise
    
    async def _create_hierarchical_chunk_structure(self, doc_id: str) -> List[Chunk]:
        """创建层级化的分块结构"""
        chunks_created = []
        
        # 第一层: 主要章节
        print("   📖 创建主章节分块")
        chapter1_data = {
            "chapter_layer": "第一章.系统概述",
            "parent_id": None,  # 顶级分块
            "chunk_infos": [
                {"info_type": "content", "info_value": "系统设计手册第一章详细描述了整个系统的架构概述、设计理念和核心组件。本章为后续章节的技术实现提供了理论基础和整体框架。"},
                {"info_type": "summary", "info_value": "系统架构概述，包含设计理念和核心组件介绍"},
                {"info_type": "keyword", "info_value": "系统架构,设计理念,核心组件,技术框架"}
            ]
        }
        
        chapter1_id = await self.chunk_ops.create_chunk_with_info_and_vector(
            knowledge_id=self.knowledge_id,
            doc_id=doc_id,
            chapter_layer=chapter1_data["chapter_layer"],
            parent_id=chapter1_data["parent_id"],
            chunk_infos=chapter1_data["chunk_infos"]
        )
        
        # 从数据库获取完整的分块信息并转换为实体
        chapter1_entity = await self.chunk_ops.get_chunk_entity(chapter1_id)
        if chapter1_entity:
            chunks_created.append(chapter1_entity)
        
        # 第二层: 章节下的小节
        print("   📑 创建小节分块")
        section1_1_data = {
            "chapter_layer": "第一章.第一节.架构设计",
            "parent_id": chapter1_id,  # 父分块是第一章
            "chunk_infos": [
                {"info_type": "content", "info_value": "架构设计部分详细阐述了系统的分层架构模式、模块化设计原则和组件间的交互机制。采用了微服务架构模式，支持水平扩展和独立部署。"},
                {"info_type": "summary", "info_value": "详述分层架构和微服务设计模式"},
                {"info_type": "keyword", "info_value": "分层架构,微服务,模块化,组件交互,水平扩展"}
            ]
        }
        
        section1_1_id = await self.chunk_ops.create_chunk_with_info_and_vector(
            knowledge_id=self.knowledge_id,
            doc_id=doc_id,
            chapter_layer=section1_1_data["chapter_layer"],
            parent_id=section1_1_data["parent_id"],
            chunk_infos=section1_1_data["chunk_infos"]
        )
        
        section1_1_entity = await self.chunk_ops.get_chunk_entity(section1_1_id)
        if section1_1_entity:
            chunks_created.append(section1_1_entity)
        
        section1_2_data = {
            "chapter_layer": "第一章.第二节.技术选型",
            "parent_id": chapter1_id,  # 父分块是第一章
            "chunk_infos": [
                {"info_type": "content", "info_value": "技术选型章节分析了各种技术方案的优劣，最终选择了Python Flask框架作为后端服务，React作为前端框架，MySQL作为主数据库，Redis作为缓存层。"},
                {"info_type": "summary", "info_value": "技术栈选择：Python Flask + React + MySQL + Redis"},
                {"info_type": "keyword", "info_value": "技术选型,Python Flask,React,MySQL,Redis,技术栈"}
            ]
        }
        
        section1_2_id = await self.chunk_ops.create_chunk_with_info_and_vector(
            knowledge_id=self.knowledge_id,
            doc_id=doc_id,
            chapter_layer=section1_2_data["chapter_layer"],
            parent_id=section1_2_data["parent_id"],
            chunk_infos=section1_2_data["chunk_infos"]
        )
        
        section1_2_entity = await self.chunk_ops.get_chunk_entity(section1_2_id)
        if section1_2_entity:
            chunks_created.append(section1_2_entity)
        
        # 第三层: 小节下的具体内容
        print("   🔍 创建具体内容分块")
        detail1_1_1_data = {
            "chapter_layer": "第一章.第二节.技术选型.数据库设计",
            "parent_id": section1_2_id,  # 父分块是第二节
            "chunk_infos": [
                {"info_type": "content", "info_value": "数据库设计采用了关系型数据库MySQL 8.0，设计了用户表、权限表、日志表等核心表结构。使用了索引优化查询性能，采用主从复制保证数据可靠性。"},
                {"info_type": "summary", "info_value": "MySQL数据库设计，包含核心表结构和性能优化"},
                {"info_type": "keyword", "info_value": "MySQL,数据库设计,索引优化,主从复制,表结构"}
            ]
        }
        
        detail1_1_1_id = await self.chunk_ops.create_chunk_with_info_and_vector(
            knowledge_id=self.knowledge_id,
            doc_id=doc_id,
            chapter_layer=detail1_1_1_data["chapter_layer"],
            parent_id=detail1_1_1_data["parent_id"],
            chunk_infos=detail1_1_1_data["chunk_infos"]
        )
        
        detail1_1_1_entity = await self.chunk_ops.get_chunk_entity(detail1_1_1_id)
        if detail1_1_1_entity:
            chunks_created.append(detail1_1_1_entity)
        
        # 另一个顶级章节
        print("   📚 创建第二章节分块")
        chapter2_data = {
            "chapter_layer": "第二章.实施指南",
            "parent_id": None,  # 顶级分块
            "chunk_infos": [
                {"info_type": "content", "info_value": "实施指南章节提供了系统部署、配置和运维的详细步骤。包括环境准备、安装部署、配置管理、监控告警等实际操作指导。"},
                {"info_type": "summary", "info_value": "系统实施指南，涵盖部署、配置、运维全流程"},
                {"info_type": "keyword", "info_value": "实施指南,系统部署,配置管理,运维监控,操作指导"}
            ]
        }
        
        chapter2_id = await self.chunk_ops.create_chunk_with_info_and_vector(
            knowledge_id=self.knowledge_id,
            doc_id=doc_id,
            chapter_layer=chapter2_data["chapter_layer"],
            parent_id=chapter2_data["parent_id"],
            chunk_infos=chapter2_data["chunk_infos"]
        )
        
        chapter2_entity = await self.chunk_ops.get_chunk_entity(chapter2_id)
        if chapter2_entity:
            chunks_created.append(chapter2_entity)
        
        return chunks_created
    
    async def _display_chunk_hierarchy(self, chunks: List[Chunk]):
        """展示分块的层级关系"""
        print("   🌳 分块层级结构:")
        
        # 构建层级映射
        parent_to_children = {}
        all_chunks = {chunk.chunk_id: chunk for chunk in chunks}
        
        for chunk in chunks:
            parent_id = chunk.parent_id or "ROOT"
            if parent_id not in parent_to_children:
                parent_to_children[parent_id] = []
            parent_to_children[parent_id].append(chunk)
        
        def print_chunk_tree(parent_id: str, level: int = 0):
            """递归打印分块树"""
            if parent_id not in parent_to_children:
                return
            
            for chunk in parent_to_children[parent_id]:
                indent = "   " * level
                icon = "📁" if chunk.chunk_id in parent_to_children else "📄"
                print(f"{indent}{icon} {chunk.chapter_layer}")
                print(f"{indent}   └─ ID: {chunk.chunk_id[:16]}...")
                if chunk.chunk_infos:
                    print(f"{indent}   └─ 信息类型: {[info.info_type for info in chunk.chunk_infos]}")
                
                # 递归打印子分块
                print_chunk_tree(chunk.chunk_id, level + 1)
        
        # 从根节点开始打印
        print_chunk_tree("ROOT")
    
    async def scenario_2_semantic_search_by_content(self):
        """
        场景2: 基于内容的语义搜索
        
        业务流程：
        1. 用户输入查询文本（info_type=content）
        2. 对查询文本进行向量化
        3. 在向量数据库中搜索相似的chunk_info
        4. 返回相关的chunk_id和文档信息
        """
        print("\n" + "="*80)
        print("🔍 场景2: 基于内容的语义搜索")
        print("="*80)
        
        try:
            # 搜索测试案例
            search_queries = [
                "在使用 Python 进行调试时，如果你正在使用像 pdb 或者 IDE（如 VS Code、PyCharm）的图形化调试器，并且你希望在调试窗口中调用一个需要 await 的异步函数，你需要确保你是在一个异步上下文中执行它。",
                "MySQL数据库配置",
                "微服务部署方案",
                "技术栈选择原因",
                "系统监控和运维"
            ]
            
            for i, query in enumerate(search_queries, 1):
                print(f"\n🔍 搜索{i}: '{query}'")
                
                # 执行语义搜索 (只搜索content类型的信息)
                search_results = await self.chunk_ops.search_similar_chunks(
                    query_text=query,
                    knowledge_id=self.knowledge_id,
                    info_types=["content"],  # 只搜索内容类型
                    top_k=3,
                    similarity_threshold=0.1
                )
                
                if search_results:
                    print(f"   📚 找到 {len(search_results)} 个相关结果:")
                    
                    for j, result in enumerate(search_results, 1):
                        # 基于chunk_info_id获取完整的分块信息
                        chunk_info_id = result['chunk_info_id']
                        chunk_id = result['chunk_id']
                        doc_id = result['doc_id']
                        similarity = result['similarity_score']
                        content = result['info_value']
                        
                        print(f"\n      🎯 结果{j} (相似度: {similarity:.3f})")
                        print(f"         Chunk Info ID: {chunk_info_id}")
                        print(f"         Chunk ID: {chunk_id[:16]}...")
                        print(f"         Doc ID: {doc_id[:16]}...")
                        print(f"         内容预览: {content[:100]}...")
                        
                        # 通过chunk_id获取完整分块信息（包括层级）
                        chunk_full_info = await self.chunk_ops.get_chunk_with_info(chunk_id)
                        if chunk_full_info:
                            chapter_layer = chunk_full_info.get('chapter_layer', '未知层级')
                            print(f"         章节层级: {chapter_layer}")
                        
                        # 获取文档基础信息
                        document_info = await self.doc_ops.get_document_by_id(doc_id)
                        if document_info:
                            doc_name = document_info.get('doc_name', '未知文档')
                            print(f"         所属文档: {doc_name}")
                        
                        print("         " + "-" * 60)
                    
                    # 演示基于第一个结果查找相关内容
                    if len(search_results) > 0:
                        top_result = search_results[0]
                        print(f"\n   🔗 基于顶部结果查找相关内容:")
                        
                        related_chunks = await self.chunk_ops.search_similar_chunks_by_chunk_info_id(
                            chunk_info_id=top_result['chunk_info_id'],
                            knowledge_id=self.knowledge_id,
                            top_k=2,
                            exclude_self=True,
                            similarity_threshold=0.1
                        )
                        
                        if related_chunks:
                            for k, related in enumerate(related_chunks, 1):
                                print(f"      相关{k}. 相似度: {related['similarity_score']:.3f}")
                                print(f"            内容: {related['info_value'][:80]}...")
                        else:
                            print("      暂无相关内容")
                else:
                    print(f"   ❌ 未找到相关结果")
                
                print("-" * 80)
            
            print(f"\n✅ 语义搜索演示完成！")
            print("\n💡 搜索机制说明:")
            print("   • 基于 chunk_info_id 实现向量数据库与关系数据库的一一映射")
            print("   • 支持按 info_type 过滤搜索范围（content, summary, keyword等）")
            print("   • 返回结果包含完整的 doc_id, chunk_id, chunk_info_id 映射关系")
            print("   • 可通过返回的ID查询到原始分块和文档的完整信息")
            
        except Exception as e:
            logger.error(f"语义搜索演示失败: {e}")
            raise


async def main():
    """主函数 - 运行核心业务流程演示"""
    print("🌟 Knowledge Doc Module - 核心业务流程演示")
    print("=" * 80)
    print("本演示重点展示两个核心业务功能：")
    print("1. 📄 文档入库流程 - 层级化分块结构存储")
    print("2. 🔍 语义搜索流程 - 基于向量的相似度匹配")
    print("=" * 80)
    
    workflow = CoreBusinessWorkflow()
    
    try:
        # 场景1: 文档入库流程（层级化）
        doc_id, chunks = await workflow.scenario_1_document_ingestion_with_hierarchy()
        
        # 场景2: 语义搜索流程
        await workflow.scenario_2_semantic_search_by_content()
        
        print("\n" + "🎉" * 30)
        print("🎉 核心业务流程演示完成！")
        print("🎉" * 30)
        
        print("\n✅ 核心功能验证总结:")
        print("   1. ✅ 层级化文档入库 - 支持parent_id和chapter_layer的树状结构")
        print("   2. ✅ 实体类封装 - Chunk和ChunkInfo实体，参考DD模块模式")
        print("   3. ✅ 可选向量化 - 用户可选择是否将chunk_info向量化")
        print("   4. ✅ 映射关系维护 - chunk_info_id在关系型和向量数据库的一一对应")
        print("   5. ✅ 语义搜索 - 基于向量相似度的内容检索")
        print("   6. ✅ 完整链路追踪 - 从搜索结果可追溯到原始文档和分块")
        
        print("\n💡 核心业务价值:")
        print("   • 结构化存储 - 保持文档的原始层级结构")
        print("   • 灵活向量化 - 按需对chunk_info进行向量化")
        print("   • 精确映射 - 确保向量和原始数据的一一对应")
        print("   • 高效检索 - 语义搜索快速定位相关内容")
        print("   • 完整追溯 - 从搜索结果可获取完整上下文信息")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1) 
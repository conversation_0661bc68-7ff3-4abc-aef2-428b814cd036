"""
ORM数据库方言基础类

基于Universal的方言系统，适配ORM的SQLAlchemy实现
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum


class FeatureSupport(Enum):
    """Feature support levels"""
    FULL = "full"           # Fully supported
    PARTIAL = "partial"     # Partially supported
    EMULATED = "emulated"   # Emulated through workarounds
    NONE = "none"          # Not supported


@dataclass
class DatabaseFeatures:
    """Database feature support matrix"""
    json_support: FeatureSupport = FeatureSupport.NONE
    array_support: FeatureSupport = FeatureSupport.NONE
    window_functions: FeatureSupport = FeatureSupport.NONE
    cte_support: FeatureSupport = FeatureSupport.NONE
    full_text_search: FeatureSupport = FeatureSupport.NONE
    partitioning: FeatureSupport = FeatureSupport.NONE
    upsert_support: FeatureSupport = FeatureSupport.NONE
    returning_clause: FeatureSupport = FeatureSupport.NONE
    bulk_insert: FeatureSupport = FeatureSupport.NONE
    async_support: FeatureSupport = FeatureSupport.NONE


class DatabaseDialect(ABC):
    """Abstract base class for database dialects"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Database dialect name"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Database dialect description"""
        pass
    
    @property
    @abstractmethod
    def default_port(self) -> Optional[int]:
        """Default port for this database"""
        pass
    
    @property
    @abstractmethod
    def features(self) -> DatabaseFeatures:
        """Supported features for this dialect"""
        pass
    
    @abstractmethod
    def quote_identifier(self, identifier: str) -> str:
        """Quote an identifier (table name, column name, etc.)"""
        pass
    
    @abstractmethod
    def escape_string(self, value: str) -> str:
        """Escape a string value"""
        pass
    
    @abstractmethod
    def format_limit_offset(self, limit: Optional[int], offset: Optional[int]) -> str:
        """Format LIMIT/OFFSET clause"""
        pass
    
    @abstractmethod
    def get_table_exists_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to check if table exists"""
        pass
    
    @abstractmethod
    def get_column_info_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to retrieve column information"""
        pass
    
    @abstractmethod
    def get_primary_key_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to retrieve primary key information"""
        pass
    
    @abstractmethod
    def get_foreign_key_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to retrieve foreign key information"""
        pass
    
    @abstractmethod
    def get_index_info_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to retrieve index information"""
        pass
    
    def supports_feature(self, feature: str) -> FeatureSupport:
        """Check if a feature is supported"""
        return getattr(self.features, feature, FeatureSupport.NONE)
    
    def format_placeholder(self, param_name: str) -> str:
        """Format parameter placeholder for prepared statements"""
        return f":{param_name}"
    
    def get_upsert_query(self, table: str, columns: List[str], 
                        conflict_columns: List[str], 
                        update_columns: Optional[List[str]] = None) -> str:
        """Generate UPSERT query if supported"""
        if self.supports_feature('upsert_support') == FeatureSupport.NONE:
            raise NotImplementedError(f"UPSERT not supported by {self.name}")
        
        # Default implementation - should be overridden by specific dialects
        raise NotImplementedError("UPSERT query generation not implemented")
    
    def get_bulk_insert_query(self, table: str, columns: List[str], 
                             batch_size: int = 1000) -> str:
        """Generate bulk insert query if supported"""
        if self.supports_feature('bulk_insert') == FeatureSupport.NONE:
            # Fall back to standard INSERT
            placeholders = ", ".join([self.format_placeholder(f"param_{i}") 
                                    for i in range(len(columns))])
            return f"INSERT INTO {self.quote_identifier(table)} ({', '.join(map(self.quote_identifier, columns))}) VALUES ({placeholders})"
        
        # Default implementation for bulk insert
        placeholders = ", ".join([self.format_placeholder(f"param_{i}") 
                                for i in range(len(columns))])
        return f"INSERT INTO {self.quote_identifier(table)} ({', '.join(map(self.quote_identifier, columns))}) VALUES ({placeholders})"
    
    def optimize_query(self, query: str, query_type: str = "SELECT") -> str:
        """Apply dialect-specific query optimizations"""
        # Default implementation - no optimization
        return query
    
    def get_connection_params(self) -> Dict[str, Any]:
        """Get dialect-specific connection parameters"""
        return {}
    
    def validate_identifier(self, identifier: str) -> bool:
        """Validate if identifier is valid for this dialect"""
        # Basic validation - can be overridden
        if not identifier:
            return False
        
        # Check for SQL injection patterns
        dangerous_patterns = [';', '--', '/*', '*/', 'xp_', 'sp_']
        identifier_lower = identifier.lower()
        
        for pattern in dangerous_patterns:
            if pattern in identifier_lower:
                return False
        
        return True
    
    def get_schema_query(self, schema: Optional[str] = None) -> str:
        """Get query to list all tables in schema"""
        # Default implementation - should be overridden
        raise NotImplementedError("Schema query not implemented")
    
    def format_datetime(self, dt_format: str = "YYYY-MM-DD HH:MI:SS") -> str:
        """Get datetime format string for this dialect"""
        # Default SQL standard format
        return "YYYY-MM-DD HH:MI:SS"
    
    def get_version_query(self) -> str:
        """Get query to retrieve database version"""
        return "SELECT VERSION()"
    
    def __str__(self) -> str:
        return f"{self.name} ({self.description})"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}: {self.name}>"

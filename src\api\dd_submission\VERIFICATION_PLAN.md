# DD部门分配系统验证计划

## 📋 验证概述

基于重构后的两个核心接口，制定全面的验证计划，确保业务逻辑正确性和系统稳定性。

**验证范围：**
1. 义务分发接口 - DD部门分发功能 + 数据存储
2. 数据回填处理接口 - 用户修改DD后的回填处理逻辑

**验证时间：** 2025-07-20
**验证版本：** v2.0.0

---

## 🎯 核心验证项目

### 1. 数据库表结构验证

| 验证项目 | 位置 | 验证内容 | 验证方法 | 优先级 |
|---------|------|----------|----------|--------|
| 表名修复验证 | `src/modules/db/preparation/create_rdb_dd.sql:358` | 外键约束正确引用 `dd_departments` | SQL执行测试 | 🔴 高 |
| 测试数据兼容性 | `src/modules/db/preparation/dd_test/` | 测试数据字段与建表语句匹配 | 数据插入测试 | 🔴 高 |
| 部门关联表结构 | `dd_departments_relation` | 部门ID与table_id关联正确 | 关联查询测试 | 🔴 高 |

### 2. 义务分发接口验证

| 验证项目 | 位置 | 验证内容 | 验证方法 | 优先级 |
|---------|------|----------|----------|--------|
| 接口响应格式 | `src/api/dd_submission/core/routers.py:duty_distribution` | 立即返回 success/failure 响应 | API测试 | 🔴 高 |
| 异步任务启动 | `process_duty_distribution_task` | 后台任务正确启动和执行 | 异步测试 | 🔴 高 |
| 历史匹配算法 | `src/modules/dd_submission/department_assignment/assignment_logic.py` | 精确搜索/混合搜索/IFTDF推荐 | 算法测试 | 🔴 高 |
| 数据格式约束 | 分配结果构建 | DR22 = BDR01，BDR03 留空 | 数据验证 | 🔴 高 |
| 数据库保存 | `PostDistributionDBOperations.save_assignment_results` | 结果正确保存到post库 | 数据库测试 | 🔴 高 |
| 前端回调机制 | `send_callback_to_frontend` | 处理完成后回调前端 | 集成测试 | 🟡 中 |

### 3. 数据回填处理接口验证

| 验证项目 | 位置 | 验证内容 | 验证方法 | 优先级 |
|---------|------|----------|----------|--------|
| 字段变化检测 | `check_field_changes` | 正确检测 DR22、BDR01、BDR03 变化 | 单元测试 | 🔴 高 |
| 部门关联验证 | `get_table_ids_by_dept_ids` | 根据部门ID查询table_id集合 | 数据库测试 | 🔴 高 |
| 交集运算逻辑 | `process_department_validation_and_update` | 与bdr09字段进行交集运算 | 逻辑测试 | 🔴 高 |
| 差异化更新 | `update_changed_fields_only` | 交集不为空时只修改变化字段 | 数据库测试 | 🔴 高 |
| 清空更新逻辑 | `clear_and_update_fields` | 交集为空时清空BRD05-sdr15字段 | 数据库测试 | 🔴 高 |
| 义务解读步骤 | 步骤验证 | 只支持"义务解读"步骤 | 参数验证 | 🟡 中 |

### 4. 业务逻辑核心验证

| 验证项目 | 位置 | 验证内容 | 验证方法 | 优先级 |
|---------|------|----------|----------|--------|
| 四层筛选算法 | `DepartmentAssignmentLogic.assign_department` | 套系→报表→提交→数据层筛选 | 算法测试 | 🔴 高 |
| 决策逻辑 | 筛选结果处理 | 唯一/多个/无结果的决策逻辑 | 逻辑测试 | 🔴 高 |
| TF-IDF推荐 | `TFIDFDepartmentRecommender` | 语义匹配和置信度评分 | 算法测试 | 🟡 中 |
| 混合搜索 | `DDSearch.hybrid_search` | 向量+文本混合搜索 | 搜索测试 | 🟡 中 |
| 套系优先级 | `PriorityProcessor` | 标准套系vs Survey套系优先级 | 优先级测试 | 🟡 中 |

### 5. 数据流验证

| 验证项目 | 位置 | 验证内容 | 验证方法 | 优先级 |
|---------|------|----------|----------|--------|
| 分发前数据获取 | `get_pre_distribution_data` | 根据report_code查询分发前数据 | 数据查询测试 | 🔴 高 |
| 数据映射转换 | entry_type映射 | 填报项/范围项 ↔ submission/range | 映射测试 | 🔴 高 |
| 字段数组处理 | DR22/BDR01/BDR03 | 数组格式存储和传递 | 数据格式测试 | 🔴 高 |
| 版本解析 | report_code解析 | G0107_beta_v1.0 → dr07+version | 解析测试 | 🟡 中 |

### 6. 异常处理验证

| 验证项目 | 位置 | 验证内容 | 验证方法 | 优先级 |
|---------|------|----------|----------|--------|
| 数据库连接异常 | 所有数据库操作 | 连接失败时的错误处理 | 异常测试 | 🟡 中 |
| 无效report_code | 接口入参验证 | 找不到数据时返回failure | 边界测试 | 🔴 高 |
| 部门ID不存在 | 部门关联查询 | 无效部门ID的处理 | 异常测试 | 🟡 中 |
| 并发处理 | 异步任务管理 | 多个并发请求的处理 | 并发测试 | 🟡 中 |

---

## 🧪 验证方法详细说明

### 单元测试
- **目标**: 验证单个函数的逻辑正确性
- **工具**: pytest + asyncio
- **覆盖**: 核心算法、数据处理、异常处理

### 集成测试  
- **目标**: 验证模块间协作和数据流
- **工具**: FastAPI TestClient + 数据库
- **覆盖**: 接口调用、数据库操作、业务流程

### 数据验证测试
- **目标**: 验证数据格式、约束、一致性
- **工具**: SQL查询 + 数据对比
- **覆盖**: 表结构、数据插入、字段映射

### 性能测试
- **目标**: 验证系统性能和并发能力
- **工具**: 压力测试工具
- **覆盖**: 响应时间、并发处理、资源使用

---

## 📊 验证执行计划

### 阶段1：基础验证（第1-2天）
1. ✅ 数据库表结构验证
2. ✅ 测试数据兼容性验证  
3. ✅ 接口基本功能验证

### 阶段2：核心逻辑验证（第3-4天）
1. 🔄 义务分发接口完整流程
2. 🔄 数据回填处理逻辑
3. 🔄 业务算法正确性

### 阶段3：集成验证（第5天）
1. ⏳ 端到端业务流程
2. ⏳ 异常处理机制
3. ⏳ 性能和并发测试

### 阶段4：生产准备（第6天）
1. ⏳ 生产环境配置验证
2. ⏳ 监控和日志验证
3. ⏳ 部署文档完善

---

## 🎯 验证成功标准

### 功能正确性
- [ ] 所有核心接口返回预期结果
- [ ] 数据库操作无错误
- [ ] 业务逻辑符合需求

### 性能要求
- [ ] 接口响应时间 < 200ms
- [ ] 并发处理能力 ≥ 10 requests/s
- [ ] 数据库操作成功率 ≥ 99%

### 稳定性要求
- [ ] 异常情况正确处理
- [ ] 系统资源使用合理
- [ ] 长时间运行无内存泄漏

---

## 📝 验证记录

**验证负责人**: 开发团队  
**验证开始时间**: 2025-07-20  
**预计完成时间**: 2025-07-26  
**验证状态**: 进行中

**当前进度**: 
- ✅ API目录结构整理完成
- ✅ 核心接口代码重构完成  
- ✅ 数据库表名修复完成
- 🔄 验证计划制定完成
- ⏳ 开始执行验证测试

"""
元数据提取器

从解析后的文档模板中提取元数据并入库。
"""

import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
import logging

# 标准化日志导入
logger = logging.getLogger(__name__)

# 添加项目根目录到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent.parent
sys.path.insert(0, str(project_root))


class MetadataExtractor:
    """
    元数据提取器
    
    从解析后的文档模板中提取元数据：
    - 数据库信息提取
    - 表信息提取
    - 字段信息提取
    - 码值集信息提取
    - 自动入库处理
    """
    
    def __init__(self, rdb_client, vdb_client):
        """
        初始化元数据提取器
        
        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        logger.info("元数据提取器初始化完成")
    
    async def extract_metadata(
        self,
        knowledge_id: str,
        parsed_data: Dict[str, Any],
        template_type: str
    ) -> Dict[str, Any]:
        """
        提取元数据并入库
        
        Args:
            knowledge_id: 知识库ID
            parsed_data: 解析后的数据
            template_type: 模板类型
            
        Returns:
            Dict[str, Any]: 提取结果
        """
        try:
            extraction_result = {
                "success": True,
                "extracted_entities": {
                    "databases": [],
                    "tables": [],
                    "columns": [],
                    "code_sets": []
                },
                "statistics": {
                    "total_databases": 0,
                    "total_tables": 0,
                    "total_columns": 0,
                    "total_code_sets": 0
                },
                "errors": []
            }
            
            # 根据模板类型提取元数据
            if template_type == "excel":
                await self._extract_from_excel(knowledge_id, parsed_data, extraction_result)
            elif template_type == "csv":
                await self._extract_from_csv(knowledge_id, parsed_data, extraction_result)
            elif template_type == "json":
                await self._extract_from_json(knowledge_id, parsed_data, extraction_result)
            else:
                raise ValueError(f"不支持的模板类型: {template_type}")
            
            # 更新统计信息
            self._update_statistics(extraction_result)
            
            # 设置最终状态
            extraction_result["success"] = len(extraction_result["errors"]) == 0
            
            logger.info(f"元数据提取完成: knowledge_id={knowledge_id}, success={extraction_result['success']}")
            return extraction_result
            
        except Exception as e:
            logger.error(f"元数据提取失败: knowledge_id={knowledge_id}, error={e}")
            return {
                "success": False,
                "extracted_entities": {"databases": [], "tables": [], "columns": [], "code_sets": []},
                "statistics": {"total_databases": 0, "total_tables": 0, "total_columns": 0, "total_code_sets": 0},
                "errors": [f"提取过程异常: {str(e)}"]
            }
    
    async def _extract_from_excel(
        self,
        knowledge_id: str,
        parsed_data: Dict[str, Any],
        extraction_result: Dict[str, Any]
    ):
        """从Excel数据中提取元数据"""
        try:
            sheets = parsed_data.get("sheets", {})
            
            for sheet_name, sheet_data in sheets.items():
                # 分析工作表内容
                await self._analyze_sheet_content(
                    knowledge_id, sheet_name, sheet_data, extraction_result
                )
            
        except Exception as e:
            extraction_result["errors"].append(f"Excel元数据提取失败: {str(e)}")
    
    async def _extract_from_csv(
        self,
        knowledge_id: str,
        parsed_data: Dict[str, Any],
        extraction_result: Dict[str, Any]
    ):
        """从CSV数据中提取元数据"""
        try:
            data = parsed_data.get("data", {})
            
            # 分析CSV内容
            await self._analyze_tabular_content(
                knowledge_id, "csv_data", data, extraction_result
            )
            
        except Exception as e:
            extraction_result["errors"].append(f"CSV元数据提取失败: {str(e)}")
    
    async def _extract_from_json(
        self,
        knowledge_id: str,
        parsed_data: Dict[str, Any],
        extraction_result: Dict[str, Any]
    ):
        """从JSON数据中提取元数据"""
        try:
            data = parsed_data.get("data", {})
            
            # 分析JSON结构
            await self._analyze_json_content(
                knowledge_id, data, extraction_result
            )
            
        except Exception as e:
            extraction_result["errors"].append(f"JSON元数据提取失败: {str(e)}")
    
    async def _analyze_sheet_content(
        self,
        knowledge_id: str,
        sheet_name: str,
        sheet_data: Dict[str, Any],
        extraction_result: Dict[str, Any]
    ):
        """分析工作表内容"""
        try:
            # 获取元数据候选字段
            candidates = sheet_data.get("metadata_candidates", {})
            sample_data = sheet_data.get("sample_data", [])
            
            # 提取数据库信息
            databases = await self._extract_databases(knowledge_id, candidates, sample_data)
            extraction_result["extracted_entities"]["databases"].extend(databases)
            
            # 提取表信息
            tables = await self._extract_tables(knowledge_id, candidates, sample_data)
            extraction_result["extracted_entities"]["tables"].extend(tables)
            
            # 提取字段信息
            columns = await self._extract_columns(knowledge_id, candidates, sample_data)
            extraction_result["extracted_entities"]["columns"].extend(columns)
            
            # 提取码值集信息
            code_sets = await self._extract_code_sets(knowledge_id, candidates, sample_data)
            extraction_result["extracted_entities"]["code_sets"].extend(code_sets)
            
        except Exception as e:
            extraction_result["errors"].append(f"工作表 '{sheet_name}' 分析失败: {str(e)}")
    
    async def _analyze_tabular_content(
        self,
        knowledge_id: str,
        data_name: str,
        data: Dict[str, Any],
        extraction_result: Dict[str, Any]
    ):
        """分析表格内容"""
        try:
            # 获取元数据候选字段
            candidates = data.get("metadata_candidates", {})
            sample_data = data.get("sample_data", [])
            
            # 提取各类元数据
            databases = await self._extract_databases(knowledge_id, candidates, sample_data)
            extraction_result["extracted_entities"]["databases"].extend(databases)
            
            tables = await self._extract_tables(knowledge_id, candidates, sample_data)
            extraction_result["extracted_entities"]["tables"].extend(tables)
            
            columns = await self._extract_columns(knowledge_id, candidates, sample_data)
            extraction_result["extracted_entities"]["columns"].extend(columns)
            
            code_sets = await self._extract_code_sets(knowledge_id, candidates, sample_data)
            extraction_result["extracted_entities"]["code_sets"].extend(code_sets)
            
        except Exception as e:
            extraction_result["errors"].append(f"表格数据 '{data_name}' 分析失败: {str(e)}")
    
    async def _analyze_json_content(
        self,
        knowledge_id: str,
        data: Any,
        extraction_result: Dict[str, Any]
    ):
        """分析JSON内容"""
        try:
            # JSON结构分析相对复杂，这里提供基础实现
            # 可以根据具体的JSON结构进行定制化处理
            
            if isinstance(data, dict):
                # 如果是对象，尝试提取键值对作为元数据
                for key, value in data.items():
                    if isinstance(value, str) and len(value) > 0:
                        # 简单的字符串值可能是元数据
                        await self._process_metadata_item(knowledge_id, key, value, extraction_result)
            
            elif isinstance(data, list) and data:
                # 如果是数组，分析第一个元素的结构
                first_item = data[0]
                if isinstance(first_item, dict):
                    await self._analyze_json_content(knowledge_id, first_item, extraction_result)
            
        except Exception as e:
            extraction_result["errors"].append(f"JSON内容分析失败: {str(e)}")
    
    async def _extract_databases(
        self,
        knowledge_id: str,
        candidates: Dict[str, List[str]],
        sample_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """提取数据库信息"""
        try:
            databases = []
            db_name_fields = candidates.get("database_names", [])
            
            for field in db_name_fields:
                # 从样本数据中提取数据库名称
                db_names = set()
                for row in sample_data:
                    if field in row and row[field]:
                        db_names.add(str(row[field]).strip())
                
                # 为每个数据库名称创建数据库实体
                for db_name in db_names:
                    if db_name:
                        database = {
                            "knowledge_id": knowledge_id,
                            "db_name": db_name,
                            "db_type": "source",  # 默认为源数据库
                            "data_layer": "ODS",  # 默认数据层
                            "source_field": field,
                            "extracted_from": "template"
                        }
                        databases.append(database)
            
            return databases
            
        except Exception as e:
            logger.error(f"提取数据库信息失败: {e}")
            return []
    
    async def _extract_tables(
        self,
        knowledge_id: str,
        candidates: Dict[str, List[str]],
        sample_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """提取表信息"""
        try:
            tables = []
            table_name_fields = candidates.get("table_names", [])
            
            for field in table_name_fields:
                # 从样本数据中提取表名称
                table_names = set()
                for row in sample_data:
                    if field in row and row[field]:
                        table_names.add(str(row[field]).strip())
                
                # 为每个表名称创建表实体
                for table_name in table_names:
                    if table_name:
                        table = {
                            "knowledge_id": knowledge_id,
                            "table_name": table_name,
                            "table_type": "source",  # 默认为源表
                            "source_field": field,
                            "extracted_from": "template"
                        }
                        tables.append(table)
            
            return tables
            
        except Exception as e:
            logger.error(f"提取表信息失败: {e}")
            return []
    
    async def _extract_columns(
        self,
        knowledge_id: str,
        candidates: Dict[str, List[str]],
        sample_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """提取字段信息"""
        try:
            columns = []
            column_name_fields = candidates.get("column_names", [])
            description_fields = candidates.get("descriptions", [])
            type_fields = candidates.get("data_types", [])
            
            # 组合字段信息
            for i, row in enumerate(sample_data):
                for field in column_name_fields:
                    if field in row and row[field]:
                        column_name = str(row[field]).strip()
                        if column_name:
                            column = {
                                "knowledge_id": knowledge_id,
                                "column_name": column_name,
                                "column_type": "source",  # 默认为源字段
                                "source_field": field,
                                "extracted_from": "template",
                                "row_index": i
                            }
                            
                            # 尝试匹配描述
                            for desc_field in description_fields:
                                if desc_field in row and row[desc_field]:
                                    column["column_desc"] = str(row[desc_field]).strip()
                                    break
                            
                            # 尝试匹配数据类型
                            for type_field in type_fields:
                                if type_field in row and row[type_field]:
                                    column["data_type"] = str(row[type_field]).strip()
                                    break
                            
                            columns.append(column)
            
            return columns
            
        except Exception as e:
            logger.error(f"提取字段信息失败: {e}")
            return []
    
    async def _extract_code_sets(
        self,
        knowledge_id: str,
        candidates: Dict[str, List[str]],
        sample_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """提取码值集信息"""
        try:
            code_sets = []
            
            # 简单的码值集识别逻辑
            # 可以根据具体需求进行扩展
            
            return code_sets
            
        except Exception as e:
            logger.error(f"提取码值集信息失败: {e}")
            return []
    
    async def _process_metadata_item(
        self,
        knowledge_id: str,
        key: str,
        value: str,
        extraction_result: Dict[str, Any]
    ):
        """处理单个元数据项"""
        try:
            # 根据键名判断元数据类型
            key_lower = key.lower()
            
            if any(keyword in key_lower for keyword in ["database", "db", "数据库"]):
                database = {
                    "knowledge_id": knowledge_id,
                    "db_name": value,
                    "db_type": "source",
                    "extracted_from": "json_template"
                }
                extraction_result["extracted_entities"]["databases"].append(database)
            
            elif any(keyword in key_lower for keyword in ["table", "表", "表名"]):
                table = {
                    "knowledge_id": knowledge_id,
                    "table_name": value,
                    "table_type": "source",
                    "extracted_from": "json_template"
                }
                extraction_result["extracted_entities"]["tables"].append(table)
            
            elif any(keyword in key_lower for keyword in ["column", "field", "字段", "列"]):
                column = {
                    "knowledge_id": knowledge_id,
                    "column_name": value,
                    "column_type": "source",
                    "extracted_from": "json_template"
                }
                extraction_result["extracted_entities"]["columns"].append(column)
            
        except Exception as e:
            logger.error(f"处理元数据项失败: key={key}, value={value}, error={e}")
    
    def _update_statistics(self, extraction_result: Dict[str, Any]):
        """更新统计信息"""
        try:
            entities = extraction_result["extracted_entities"]
            stats = extraction_result["statistics"]
            
            stats["total_databases"] = len(entities["databases"])
            stats["total_tables"] = len(entities["tables"])
            stats["total_columns"] = len(entities["columns"])
            stats["total_code_sets"] = len(entities["code_sets"])
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")

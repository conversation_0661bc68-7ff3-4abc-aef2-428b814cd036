#!/usr/bin/env python3
"""
SQL执行脚本

用于执行 modules/db/preparation 目录下的所有SQL文件
支持PostgreSQL和MySQL数据库
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from service import get_client, cleanup


async def execute_sql_files():
    """执行所有SQL文件"""
    print("🚀 开始执行SQL文件...")
    
    try:
        # 获取项目根目录
        project_root = Path(__file__).parent.parent.parent.parent
        sql_dir = project_root / "src" / "modules" / "db" / "preparation"
        
        if not sql_dir.exists():
            print(f"❌ SQL目录不存在: {sql_dir}")
            return False
        
        # 查找所有SQL文件
        sql_files = list(sql_dir.glob("*.sql"))
        if not sql_files:
            print(f"❌ 未找到SQL文件: {sql_dir}")
            return False
        
        print(f"📁 找到 {len(sql_files)} 个SQL文件:")
        for sql_file in sql_files:
            print(f"   - {sql_file.name}")
        
        # 按文件名排序执行
        sql_files.sort(key=lambda x: x.name)
        
        # 执行每个SQL文件
        for sql_file in sql_files:
            print(f"\n📝 执行文件: {sql_file.name}")
            success = await execute_single_sql_file(sql_file)
            if not success:
                print(f"❌ 执行失败: {sql_file.name}")
                return False
            print(f"✅ 执行成功: {sql_file.name}")
        
        print("\n🎉 所有SQL文件执行完成!")
        return True
        
    except Exception as e:
        print(f"❌ 执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理资源
        try:
            await cleanup()
            print("🧹 资源清理完成")
        except Exception as e:
            print(f"⚠️ 资源清理时发生错误: {e}")


async def execute_single_sql_file(sql_file: Path) -> bool:
    """
    执行单个SQL文件
    
    Args:
        sql_file: SQL文件路径
        
    Returns:
        执行是否成功
    """
    try:
        # 读取SQL文件内容
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 根据文件名确定数据库类型
        db_type = determine_db_type(sql_file.name)
        if not db_type:
            print(f"⚠️ 无法确定数据库类型: {sql_file.name}")
            return False
        
        # 获取相应的数据库客户端
        client = await get_client(db_type)
        print(f"🔌 连接数据库: {db_type}")
        
        # 分割SQL语句并执行
        statements = split_sql_statements(sql_content)
        print(f"📋 SQL语句数量: {len(statements)}")
        
        executed_count = 0
        for i, statement in enumerate(statements):
            statement = statement.strip()
            if not statement:
                continue
                
            try:
                # 执行SQL语句
                if statement.upper().startswith(('SELECT', 'SHOW', 'DESC')):
                    # 查询语句
                    result = await client.afetch_all(statement)
                    print(f"   查询语句 {i+1}: {len(result.data) if result.data else 0} 行结果")
                else:
                    # 执行语句
                    result = await client.aexecute(statement)
                    affected_rows = getattr(result, 'affected_rows', 0) or 0
                    print(f"   执行语句 {i+1}: 影响 {affected_rows} 行")
                
                executed_count += 1
                
            except Exception as e:
                # 某些语句可能预期会出错（如DROP IF EXISTS）
                if "DROP" in statement.upper() and "does not exist" in str(e):
                    print(f"   语句 {i+1}: 表不存在，跳过 (正常情况)")
                    executed_count += 1
                    continue
                else:
                    print(f"❌ 执行语句 {i+1} 失败: {e}")
                    print(f"   SQL: {statement[:100]}...")
                    return False
        
        print(f"✅ 成功执行 {executed_count}/{len(statements)} 条语句")
        return True
        
    except Exception as e:
        print(f"❌ 执行SQL文件失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def determine_db_type(filename: str) -> str:
    """
    根据文件名确定数据库类型
    
    Args:
        filename: SQL文件名
        
    Returns:
        数据库配置路径
    """
    filename_lower = filename.lower()
    
    if 'pgvector' in filename_lower or 'postgresql' in filename_lower:
        return "database.vdbs.pgvector"
    elif 'mysql' in filename_lower:
        return "database.rdbs.mysql"
    elif 'sql' in filename_lower:
        # 默认使用MySQL
        return "database.rdbs.mysql"
    else:
        return None


def split_sql_statements(sql_content: str) -> List[str]:
    """
    分割SQL语句
    
    Args:
        sql_content: SQL内容
        
    Returns:
        SQL语句列表
    """
    # 简单的SQL语句分割（按分号分割）
    statements = []
    current_statement = ""
    in_string = False
    string_char = None
    i = 0
    
    while i < len(sql_content):
        char = sql_content[i]
        
        # 处理字符串
        if char in ("'", '"') and (i == 0 or sql_content[i-1] != '\\'):
            if not in_string:
                in_string = True
                string_char = char
            elif char == string_char:
                in_string = False
                string_char = None
        # 处理注释
        elif char == '-' and i + 1 < len(sql_content) and sql_content[i+1] == '-' and not in_string:
            # 单行注释
            newline_pos = sql_content.find('\n', i)
            if newline_pos == -1:
                break  # 注释到文件末尾
            i = newline_pos
        elif char == '/' and i + 1 < len(sql_content) and sql_content[i+1] == '*' and not in_string:
            # 多行注释
            end_comment_pos = sql_content.find('*/', i + 2)
            if end_comment_pos == -1:
                break  # 注释未闭合
            i = end_comment_pos + 1
        # 处理语句分隔符
        elif char == ';' and not in_string:
            current_statement = current_statement.strip()
            if current_statement:
                statements.append(current_statement)
            current_statement = ""
        else:
            current_statement += char
            
        i += 1
    
    # 添加最后一个语句（如果没有分号结尾）
    current_statement = current_statement.strip()
    if current_statement:
        statements.append(current_statement)
    
    return statements


async def main():
    """主函数"""
    print("=" * 60)
    print("🗄️  SQL文件执行脚本")
    print("=" * 60)
    
    try:
        success = await execute_sql_files()
        if success:
            print("\n🎉 SQL文件执行成功完成!")
            return 0
        else:
            print("\n❌ SQL文件执行失败!")
            return 1
    except Exception as e:
        print(f"\n💥 脚本执行过程中发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)


from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum


class FeatureSupport(Enum):
    """Feature support levels"""
    FULL = "full"           # Fully supported
    PARTIAL = "partial"     # Partially supported
    EMULATED = "emulated"   # Emulated through workarounds
    NONE = "none"          # Not supported


@dataclass
class DatabaseFeatures:
    """Database feature support matrix"""
    json_support: FeatureSupport = FeatureSupport.NONE
    array_support: FeatureSupport = FeatureSupport.NONE
    window_functions: FeatureSupport = FeatureSupport.NONE
    cte_support: FeatureSupport = FeatureSupport.NONE
    full_text_search: FeatureSupport = FeatureSupport.NONE
    partitioning: FeatureSupport = FeatureSupport.NONE
    upsert_support: FeatureSupport = FeatureSupport.NONE
    returning_clause: FeatureSupport = FeatureSupport.NONE
    bulk_insert: FeatureSupport = FeatureSupport.NONE
    async_support: FeatureSupport = FeatureSupport.NONE


class DatabaseDialect(ABC):
    """Abstract base class for database dialects"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Database dialect name"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Database dialect description"""
        pass
    
    @property
    @abstractmethod
    def default_port(self) -> Optional[int]:
        """Default port for this database"""
        pass
    
    @property
    @abstractmethod
    def default_driver(self) -> str:
        """Default driver for this database"""
        pass
    
    @property
    @abstractmethod
    def async_driver(self) -> Optional[str]:
        """Async driver for this database"""
        pass
    
    @property
    @abstractmethod
    def features(self) -> DatabaseFeatures:
        """Supported features for this database"""
        pass
    
    # ==================== Query Building ====================
    
    @abstractmethod
    def build_limit_clause(self, limit: int, offset: Optional[int] = None) -> str:
        """
        Build LIMIT clause for pagination
        
        Args:
            limit: Maximum number of rows
            offset: Number of rows to skip
        
        Returns:
            LIMIT clause string
        """
        pass
    
    @abstractmethod
    def build_upsert_statement(
        self,
        table_name: str,
        data: Dict[str, Any],
        conflict_columns: List[str],
        update_columns: Optional[List[str]] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Build UPSERT statement
        
        Args:
            table_name: Target table name
            data: Data to insert/update
            conflict_columns: Columns that define conflicts
            update_columns: Columns to update on conflict
        
        Returns:
            Tuple of (SQL statement, parameters)
        """
        pass
    
    def build_bulk_insert_statement(
        self,
        table_name: str,
        columns: List[str],
        batch_size: int,
        ignore_conflicts: bool = False
    ) -> str:
        """
        Build bulk insert statement

        Args:
            table_name: Target table name
            columns: Column names
            batch_size: Number of rows per batch
            ignore_conflicts: Whether to ignore conflicts

        Returns:
            SQL statement template
        """
        placeholders = ", ".join([self.get_parameter_placeholder()] * len(columns))
        values_clause = ", ".join([f"({placeholders})"] * batch_size)
        columns_clause = ", ".join([self.quote_identifier(col) for col in columns])

        ignore_clause = self.get_ignore_conflicts_clause() if ignore_conflicts else ""

        return f"INSERT {ignore_clause}INTO {self.quote_identifier(table_name)} ({columns_clause}) VALUES {values_clause}"
    
    # ==================== Identifier Handling ====================
    
    def quote_identifier(self, identifier: str) -> str:
        """
        Quote database identifier (table name, column name, etc.)

        Args:
            identifier: Identifier to quote

        Returns:
            Quoted identifier

        Raises:
            ValueError: If identifier contains invalid characters
        """
        # 验证标识符安全性
        self._validate_identifier(identifier)

        quote_char = self.get_identifier_quote_char()

        # 转义内部的引号字符
        escaped_identifier = identifier.replace(quote_char, quote_char + quote_char)

        return f"{quote_char}{escaped_identifier}{quote_char}"

    def _validate_identifier(self, identifier: str) -> None:
        """
        验证标识符的安全性

        Args:
            identifier: 要验证的标识符

        Raises:
            ValueError: 如果标识符包含危险字符
        """
        if not identifier:
            raise ValueError("Identifier cannot be empty")

        if len(identifier) > 64:  # 大多数数据库的标识符长度限制
            raise ValueError("Identifier too long (max 64 characters)")

        # 检查危险字符
        dangerous_chars = [';', '--', '/*', '*/', '\x00', '\n', '\r', '\t']
        for char in dangerous_chars:
            if char in identifier:
                raise ValueError(f"Identifier contains dangerous character: {char}")

        # 检查SQL关键字（基础检查）
        sql_keywords = {
            'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER',
            'TRUNCATE', 'EXEC', 'EXECUTE', 'UNION', 'SCRIPT', 'DECLARE'
        }

        if identifier.upper() in sql_keywords:
            raise ValueError(f"Identifier cannot be SQL keyword: {identifier}")

        # 检查是否以字母或下划线开头
        if not (identifier[0].isalpha() or identifier[0] == '_'):
            raise ValueError("Identifier must start with letter or underscore")

        # 检查是否只包含字母、数字、下划线
        if not all(c.isalnum() or c == '_' for c in identifier):
            raise ValueError("Identifier can only contain letters, numbers, and underscores")
    
    @abstractmethod
    def get_identifier_quote_char(self) -> str:
        """Get character used to quote identifiers"""
        pass
    
    @abstractmethod
    def get_parameter_placeholder(self) -> str:
        """Get parameter placeholder for prepared statements"""
        pass
    
    # ==================== Data Type Mapping ====================
    
    @abstractmethod
    def map_python_type_to_sql(self, python_type: type) -> str:
        """
        Map Python type to SQL type
        
        Args:
            python_type: Python type
        
        Returns:
            SQL type string
        """
        pass
    
    @abstractmethod
    def get_boolean_literal(self, value: bool) -> str:
        """
        Get boolean literal representation
        
        Args:
            value: Boolean value
        
        Returns:
            SQL boolean literal
        """
        pass
    
    # ==================== Feature-Specific Methods ====================
    
    def get_ignore_conflicts_clause(self) -> str:
        """Get clause for ignoring conflicts in INSERT"""
        if self.features.bulk_insert == FeatureSupport.FULL:
            return "IGNORE "
        return ""
    
    def supports_returning_clause(self) -> bool:
        """Check if database supports RETURNING clause"""
        return self.features.returning_clause in [FeatureSupport.FULL, FeatureSupport.PARTIAL]
    
    def supports_upsert(self) -> bool:
        """Check if database supports UPSERT operations"""
        return self.features.upsert_support in [FeatureSupport.FULL, FeatureSupport.PARTIAL, FeatureSupport.EMULATED]
    
    def supports_json(self) -> bool:
        """Check if database supports JSON operations"""
        return self.features.json_support in [FeatureSupport.FULL, FeatureSupport.PARTIAL]
    
    def supports_arrays(self) -> bool:
        """Check if database supports array types"""
        return self.features.array_support in [FeatureSupport.FULL, FeatureSupport.PARTIAL]
    
    def supports_async(self) -> bool:
        """Check if database supports async operations"""
        return self.features.async_support in [FeatureSupport.FULL, FeatureSupport.PARTIAL]
    
    # ==================== Connection Handling ====================
    
    def get_connection_url_template(self) -> str:
        """
        Get connection URL template
        
        Returns:
            URL template with placeholders
        """
        if self.async_driver:
            return f"{self.name}+{self.async_driver}://{{username}}:{{password}}@{{host}}:{{port}}/{{database}}"
        else:
            return f"{self.name}+{self.default_driver}://{{username}}:{{password}}@{{host}}:{{port}}/{{database}}"
    
    def build_connection_url(
        self,
        host: str,
        database: str,
        username: str,
        password: str,
        port: Optional[int] = None,
        use_async: bool = True,
        **kwargs
    ) -> str:
        """
        Build connection URL
        
        Args:
            host: Database host
            database: Database name
            username: Username
            password: Password
            port: Port number
            use_async: Whether to use async driver
            **kwargs: Additional parameters
        
        Returns:
            Connection URL
        """
        if port is None:
            port = self.default_port
        
        driver = self.async_driver if (use_async and self.async_driver) else self.default_driver
        scheme = f"{self.name}+{driver}" if driver else self.name
        
        url = f"{scheme}://{username}:{password}@{host}:{port}/{database}"
        
        # Add query parameters
        if kwargs:
            params = "&".join([f"{k}={v}" for k, v in kwargs.items()])
            url += f"?{params}"
        
        return url
    
    # ==================== Utility Methods ====================
    
    def __str__(self) -> str:
        return f"{self.name.title()} Dialect"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(name='{self.name}')>"

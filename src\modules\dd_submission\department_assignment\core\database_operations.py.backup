"""
数据库操作模块

处理部门职责分配结果的数据库入库操作
"""

import time
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

from ..infrastructure.models import BatchAssignmentItem, BatchProcessingResult
# 引入最新的CRUD实现
from modules.knowledge.dd.crud import DDCrud
from modules.knowledge.dd.shared.constants import DDTableNames
from modules.knowledge.dd.shared.exceptions import DDError


class PostDistributionDBOperations:
    """分发后数据库操作类"""

    def __init__(self, rdb_client: Any, vdb_client: Any = None):
        """
        初始化数据库操作类

        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端（可选）
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        # 使用最新的CRUD实现
        self.dd_crud = DDCrud(rdb_client, vdb_client)
        self.table_name = DDTableNames.BIZ_POST_DISTRIBUTION
    
    async def save_batch_results(
        self,
        batch_result: BatchProcessingResult,
        pre_distribution_records: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        保存批量处理结果到数据库（使用事务确保原子性）

        Args:
            batch_result: 批量处理结果
            pre_distribution_records: 原始分发前数据记录

        Returns:
            保存操作结果统计
        """
        start_time = time.time()
        save_stats = {
            "total_items": len(batch_result.assignment_items),
            "successful_saves": 0,
            "failed_saves": 0,
            "save_time_ms": 0.0,
            "errors": [],
            "transaction_used": True
        }

        try:
            logger.info(f"开始保存批量处理结果到数据库: {len(batch_result.assignment_items)}条记录")

            # 创建submission_id到原始记录的映射
            pre_records_map = {
                record.get('submission_id'): record
                for record in pre_distribution_records
            }

            # 使用事务确保批量操作的原子性
            async with self.rdb_client.atransaction():
                logger.debug("开始数据库事务")

                # 批量插入或更新记录
                for assignment_item in batch_result.assignment_items:
                    try:
                        # 获取原始记录
                        pre_record = pre_records_map.get(assignment_item.entry_id)
                        if not pre_record:
                            error_msg = f"未找到submission_id={assignment_item.entry_id}的原始记录"
                            logger.warning(error_msg)
                            save_stats["errors"].append(error_msg)
                            save_stats["failed_saves"] += 1
                            continue

                        # 构建入库数据
                        post_record = self._build_post_distribution_record(
                            assignment_item, pre_record, batch_result
                        )

                        # 使用CRUD方法执行入库操作
                        try:
                            await self.dd_crud.create_post_distribution(post_record)
                            save_stats["successful_saves"] += 1
                            logger.debug(f"成功保存记录: {assignment_item.entry_id}")
                        except DDError as e:
                            # 如果是重复记录，尝试更新
                            logger.warning(f"创建记录失败，尝试更新: {e}")
                            await self.dd_crud.update_post_distribution(
                                post_record,
                                submission_id=assignment_item.entry_id,
                                version=batch_result.version
                            )
                            save_stats["successful_saves"] += 1
                            logger.debug(f"成功更新记录: {assignment_item.entry_id}")

                    except Exception as e:
                        error_msg = f"保存记录 {assignment_item.entry_id} 失败: {e}"
                        logger.error(error_msg)
                        save_stats["errors"].append(error_msg)
                        save_stats["failed_saves"] += 1
                        # 在事务中，任何错误都会导致整个事务回滚
                        raise

                logger.debug("数据库事务提交成功")

            save_stats["save_time_ms"] = (time.time() - start_time) * 1000

            logger.info(f"批量保存完成: 成功={save_stats['successful_saves']}, "
                       f"失败={save_stats['failed_saves']}, "
                       f"耗时={save_stats['save_time_ms']:.2f}ms")

            return save_stats

        except Exception as e:
            logger.error(f"批量保存失败，事务已回滚: {e}")
            save_stats["errors"].append(f"批量保存失败，事务已回滚: {e}")
            save_stats["save_time_ms"] = (time.time() - start_time) * 1000
            save_stats["transaction_used"] = True
            save_stats["transaction_rollback"] = True
            return save_stats
    
    def _build_post_distribution_record(
        self, 
        assignment_item: BatchAssignmentItem,
        pre_record: Dict[str, Any],
        batch_result: BatchProcessingResult
    ) -> Dict[str, Any]:
        """
        构建分发后数据记录
        
        Args:
            assignment_item: 分配结果项
            pre_record: 原始分发前记录
            batch_result: 批量处理结果
            
        Returns:
            分发后数据记录
        """
        current_time = datetime.now()
        
        # 基础字段映射 - 完全符合真实表结构 (biz_dd_post_distribution)
        post_record = {
            # 必填关联字段
            "pre_distribution_id": pre_record.get("id", 0),  # 关联分发前记录ID
            "submission_id": assignment_item.entry_id,
            "submission_type": pre_record.get("submission_type", "SUBMISSION"),
            "report_type": pre_record.get("report_type", "detail"),
            "set": pre_record.get("set"),  # 可以为NULL
            "version": batch_result.version,

            # 部门分配结果 - 使用推荐的第一个部门或占位符
            "dept_id": self._extract_department_id(assignment_item),

            # DR字段 (从分发前数据继承和分配结果)
            "dr01": pre_record.get("dr01"),  # 可以为NULL
            "dr07": batch_result.dr07,  # 表名ID
            "dr22": self._format_department_list(assignment_item.DR22),  # RRMS数据提供部门

            # BDR字段 (业务解读数据需求 - 分配结果)
            "bdr01": self._format_department_list(assignment_item.BDR01),  # 数据生产/处理部门
            "bdr03": assignment_item.BDR03,  # 数据管理部门

            # 其他BDR字段设为NULL，后续可由各部门填写
            "bdr02": None,  # 数据生成/处理部门代表
            "bdr04": None,  # 数据管理部门代表

            # 时间字段由数据库自动生成 (create_time, update_time)
        }
        
        return post_record

    def _extract_department_id(self, assignment_item: BatchAssignmentItem) -> str:
        """
        从分配结果中提取部门ID

        Args:
            assignment_item: 分配结果项

        Returns:
            部门ID字符串
        """
        # 优先使用BDR03（数据管理部门）
        if assignment_item.BDR03 and assignment_item.BDR03 != "TODO_PLACEHOLDER":
            # 简单映射：将部门名称转换为部门ID
            dept_name = assignment_item.BDR03
            return self._map_department_name_to_id(dept_name)

        # 其次使用BDR01（数据生产/处理部门）的第一个
        if assignment_item.BDR01:
            dept_name = assignment_item.BDR01[0] if assignment_item.BDR01 else ""
            if dept_name and dept_name != "TODO_PLACEHOLDER":
                return self._map_department_name_to_id(dept_name)

        # 最后使用DR22（RRMS数据提供部门）的第一个
        if assignment_item.DR22:
            dept_name = assignment_item.DR22[0] if assignment_item.DR22 else ""
            if dept_name and dept_name != "TODO_PLACEHOLDER":
                return self._map_department_name_to_id(dept_name)

        # 默认使用业务部门
        return "DEPT_BUSINESS"

    def _map_department_name_to_id(self, dept_name: str) -> str:
        """
        将部门名称映射为部门ID

        Args:
            dept_name: 部门名称

        Returns:
            部门ID
        """
        # 简单的名称到ID映射
        name_to_id_mapping = {
            "RRMS数据提供部门": "DEPT_RRMS",
            "业务部门": "DEPT_BUSINESS",
            "IT部门": "DEPT_IT",
            "数据管理部门": "DEPT_DATA_MGT",
            "合规部门": "DEPT_COMPLIANCE"
        }

        # 精确匹配
        if dept_name in name_to_id_mapping:
            return name_to_id_mapping[dept_name]

        # 模糊匹配
        dept_name_lower = dept_name.lower()
        if "rrms" in dept_name_lower or "监管" in dept_name or "报送" in dept_name:
            return "DEPT_RRMS"
        elif "it" in dept_name_lower or "技术" in dept_name or "系统" in dept_name:
            return "DEPT_IT"
        elif "数据管理" in dept_name or "数据治理" in dept_name:
            return "DEPT_DATA_MGT"
        elif "合规" in dept_name or "风控" in dept_name:
            return "DEPT_COMPLIANCE"
        else:
            return "DEPT_BUSINESS"  # 默认业务部门

    def _format_department_list(self, dept_list: List[str]) -> str:
        """
        格式化部门列表为字符串
        
        Args:
            dept_list: 部门列表
            
        Returns:
            格式化的部门字符串
        """
        if not dept_list:
            return ""
        
        # 如果只有一个部门，直接返回
        if len(dept_list) == 1:
            return dept_list[0]
        
        # 多个部门用逗号分隔
        return ",".join(dept_list)
    
    def _determine_assignment_method(self, assignment_item: BatchAssignmentItem) -> str:
        """
        确定分配方法
        
        Args:
            assignment_item: 分配结果项
            
        Returns:
            分配方法标识
        """
        # 根据是否有推荐部门判断分配方法
        if assignment_item.DR22:
            return "auto_assignment"  # 自动分配
        else:
            return "manual_required"  # 需要人工分配
    
    async def _upsert_post_distribution_record(self, record: Dict[str, Any]) -> None:
        """
        插入或更新分发后数据记录（带重试机制，用于非事务操作）

        Args:
            record: 分发后数据记录
        """
        try:
            # 检查记录是否已存在
            existing_record = await self._check_existing_record(
                record["submission_id"], record.get("dr07", ""), record["version"]
            )

            if existing_record:
                # 更新现有记录
                await self._update_existing_record(record)
                logger.debug(f"更新现有记录: submission_id={record['submission_id']}")
            else:
                # 插入新记录
                await self._insert_new_record(record)
                logger.debug(f"插入新记录: submission_id={record['submission_id']}")

        except Exception as e:
            logger.error(f"入库操作失败: {e}")
            raise

    async def _upsert_post_distribution_record_in_transaction(self, record: Dict[str, Any]) -> None:
        """
        在事务内插入或更新分发后数据记录（无重试机制，依赖事务回滚）

        Args:
            record: 分发后数据记录
        """
        try:
            # 检查记录是否已存在
            existing_record = await self._check_existing_record(
                record["submission_id"], record.get("dr07", ""), record["version"]
            )

            if existing_record:
                # 更新现有记录（事务内，无重试）
                await self._update_existing_record_in_transaction(record)
                logger.debug(f"事务内更新现有记录: submission_id={record['submission_id']}")
            else:
                # 插入新记录（事务内，无重试）
                await self._insert_new_record_in_transaction(record)
                logger.debug(f"事务内插入新记录: submission_id={record['submission_id']}")

        except Exception as e:
            logger.error(f"事务内入库操作失败: {e}")
            raise
    
    async def _check_existing_record(
        self, 
        submission_id: str, 
        dr07: str, 
        version: str
    ) -> Optional[Dict[str, Any]]:
        """
        检查记录是否已存在
        
        Args:
            submission_id: 填报项ID
            dr07: 数据需求编号
            version: 版本号
            
        Returns:
            现有记录（如果存在）
        """
        try:
            # 构建查询SQL - 简化查询条件，只使用主要标识字段
            sql = f"""
                SELECT id, submission_id, version
                FROM {self.table_name}
                WHERE submission_id = :submission_id
                  AND version = :version
                LIMIT 1
            """
            params = {
                "submission_id": submission_id,
                "version": version
            }

            response = await self.rdb_client.afetch_all(sql, params)
            results = response.data or []

            return results[0] if results else None
            
        except Exception as e:
            logger.error(f"检查现有记录失败: {e}")
            return None
    
    async def _insert_new_record(self, record: Dict[str, Any]) -> None:
        """
        插入新记录，带重试机制

        Args:
            record: 要插入的记录
        """
        import asyncio

        max_retries = 3
        retry_delay = 1.0  # 秒

        for attempt in range(max_retries):
            try:
                # 使用正确的参数格式调用 ainsert
                result = await self.rdb_client.ainsert({
                    "table": self.table_name,
                    "data": record
                })
                logger.debug(f"插入记录成功: {result}")
                return

            except Exception as e:
                error_msg = str(e).lower()
                if "lock wait timeout" in error_msg or "deadlock" in error_msg:
                    if attempt < max_retries - 1:
                        logger.warning(f"数据库锁定，第{attempt + 1}次重试，等待{retry_delay}秒...")
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
                        continue
                    else:
                        logger.error(f"数据库锁定重试{max_retries}次后仍然失败")
                        raise
                else:
                    # 其他错误直接抛出
                    raise

    async def _insert_new_record_in_transaction(self, record: Dict[str, Any]) -> None:
        """
        在事务内插入新记录（无重试机制）

        Args:
            record: 要插入的记录
        """
        try:
            # 在事务内直接调用 ainsert，不使用重试机制
            result = await self.rdb_client.ainsert({
                "table": self.table_name,
                "data": record
            })
            logger.debug(f"事务内插入记录成功: {result}")

        except Exception as e:
            logger.error(f"事务内插入记录失败: {e}")
            raise

    async def _update_existing_record_in_transaction(self, record: Dict[str, Any]) -> None:
        """
        在事务内更新现有记录（无重试机制）

        Args:
            record: 要更新的记录
        """
        # 移除不需要更新的字段
        update_data = record.copy()
        update_data.pop("created_at", None)  # 不更新创建时间

        try:
            # 在事务内直接调用 aupdate，不使用重试机制
            result = await self.rdb_client.aupdate({
                "table": self.table_name,
                "data": update_data,
                "filters": {
                    "submission_id": record["submission_id"],
                    "version": record["version"]
                }
            })
            logger.debug(f"事务内更新记录成功: {result}")

        except Exception as e:
            logger.error(f"事务内更新记录失败: {e}")
            raise
    
    async def _update_existing_record(self, record: Dict[str, Any]) -> None:
        """
        更新现有记录
        
        Args:
            record: 要更新的记录
        """
        # 移除不需要更新的字段
        update_data = record.copy()
        update_data.pop("created_at", None)  # 不更新创建时间
        
        # 使用重试机制调用 aupdate
        import asyncio

        max_retries = 3
        retry_delay = 1.0  # 秒

        for attempt in range(max_retries):
            try:
                result = await self.rdb_client.aupdate({
                    "table": self.table_name,
                    "data": update_data,
                    "filters": {
                        "submission_id": record["submission_id"],
                        "version": record["version"]
                    }
                })
                logger.debug(f"更新记录成功: {result}")
                return

            except Exception as e:
                error_msg = str(e).lower()
                if "lock wait timeout" in error_msg or "deadlock" in error_msg:
                    if attempt < max_retries - 1:
                        logger.warning(f"数据库锁定，第{attempt + 1}次重试，等待{retry_delay}秒...")
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
                        continue
                    else:
                        logger.error(f"数据库锁定重试{max_retries}次后仍然失败")
                        raise
                else:
                    # 其他错误直接抛出
                    raise
    
    async def get_batch_processing_stats(
        self, 
        dr07: str, 
        version: str
    ) -> Dict[str, Any]:
        """
        获取批量处理统计信息
        
        Args:
            dr07: 数据需求编号
            version: 版本号
            
        Returns:
            处理统计信息
        """
        try:
            # 查询处理结果统计
            results = await self.rdb_client.select(
                table_name=self.table_name,
                columns=[
                    "COUNT(*) as total_count",
                    "SUM(CASE WHEN DR22 != '' THEN 1 ELSE 0 END) as assigned_count",
                    "SUM(CASE WHEN DR22 = '' THEN 1 ELSE 0 END) as unassigned_count"
                ],
                where={
                    "dr07": dr07,
                    "version": version
                }
            )
            
            if results:
                stats = results[0]
                return {
                    "total_records": stats.get("total_count", 0),
                    "assigned_records": stats.get("assigned_count", 0),
                    "unassigned_records": stats.get("unassigned_count", 0),
                    "assignment_rate": (
                        stats.get("assigned_count", 0) / stats.get("total_count", 1) 
                        if stats.get("total_count", 0) > 0 else 0
                    )
                }
            else:
                return {
                    "total_records": 0,
                    "assigned_records": 0,
                    "unassigned_records": 0,
                    "assignment_rate": 0.0
                }
                
        except Exception as e:
            logger.error(f"获取处理统计信息失败: {e}")
            return {
                "total_records": 0,
                "assigned_records": 0,
                "unassigned_records": 0,
                "assignment_rate": 0.0,
                "error": str(e)
            }

    async def get_pre_distribution_data(self, report_code: str) -> List[Dict[str, Any]]:
        """获取分发前数据"""
        try:
            # 解析report_code (格式: G0107_beta_v1.0)
            parts = report_code.split('_')
            if len(parts) >= 3:
                dr07 = parts[0]  # G0107
                version = parts[-1]  # v1.0
            else:
                dr07 = report_code
                version = 'v1.0'

            sql = """
            SELECT * FROM biz_dd_pre_distribution
            WHERE dr07 = :dr07 AND version = :version
            ORDER BY id
            """
            params = {'dr07': dr07, 'version': version}

            response = await self.rdb_client.afetch_all(sql, params)
            if response.data:
                logger.info(f"找到 {len(response.data)} 条分发前数据: dr07={dr07}, version={version}")
                return [dict(row) for row in response.data]
            else:
                logger.warning(f"未找到分发前数据: dr07={dr07}, version={version}")
                return []

        except Exception as e:
            logger.error(f"获取分发前数据失败: {e}")
            raise DatabaseError(f"获取分发前数据失败: {e}")

    async def save_assignment_results(
        self,
        report_code: str,
        pre_distribution_data: List[Dict[str, Any]],
        assignment_results: List[Dict[str, Any]]
    ) -> int:
        """保存分配结果到biz_dd_post_distribution表"""
        try:
            saved_count = 0

            for i, (pre_data, result) in enumerate(zip(pre_distribution_data, assignment_results)):
                try:
                    # 构建保存数据
                    save_data = {
                        'pre_distribution_id': pre_data.get('id'),
                        'submission_id': result.get('entry_id', pre_data.get('submission_id')),
                        'submission_type': pre_data.get('submission_type', 'REGULAR'),
                        'report_type': pre_data.get('report_type', 'MONTHLY'),
                        'set': pre_data.get('set'),
                        'version': pre_data.get('version', 'v1.0'),
                        'dept_id': result['DR22'][0] if result.get('DR22') else 'DEPT_BUSINESS',
                        'create_time': datetime.now(),
                        'update_time': datetime.now(),
                        'dr01': pre_data.get('dr01'),
                        'dr07': pre_data.get('dr07'),
                        'dr22': result['DR22'][0] if result.get('DR22') else 'DEPT_BUSINESS',
                        'bdr01': result['BDR01'][0] if result.get('BDR01') else 'DEPT_BUSINESS',
                        'bdr03': result.get('BDR03', '智能推荐结果')
                    }

                    # 检查是否已存在记录（避免重复插入）
                    existing_sql = """
                    SELECT id FROM biz_dd_post_distribution
                    WHERE pre_distribution_id = :pre_distribution_id
                    AND dept_id = :dept_id
                    """
                    existing_params = {
                        'pre_distribution_id': save_data['pre_distribution_id'],
                        'dept_id': save_data['dept_id']
                    }

                    existing_response = await self.rdb_client.afetch_all(existing_sql, existing_params)

                    if existing_response.data:
                        # 记录已存在，更新
                        update_sql = """
                        UPDATE biz_dd_post_distribution
                        SET bdr03 = :bdr03, update_time = :update_time
                        WHERE id = :id
                        """
                        update_params = {
                            'id': existing_response.data[0]['id'],
                            'bdr03': save_data['bdr03'],
                            'update_time': save_data['update_time']
                        }

                        update_response = await self.rdb_client.aexecute(update_sql, update_params)
                        if update_response.success:
                            saved_count += 1
                            logger.debug(f"更新分配结果: {result.get('entry_id')}")
                    else:
                        # 插入新记录
                        insert_response = await self.rdb_client.ainsert({
                            "table": "biz_dd_post_distribution",
                            "data": save_data
                        })

                        if insert_response.success:
                            saved_count += 1
                            logger.debug(f"保存分配结果: {result.get('entry_id')}")
                        else:
                            logger.error(f"保存分配结果失败: {insert_response.message}")

                except Exception as e:
                    logger.error(f"保存单条分配结果失败 {result.get('entry_id')}: {e}")
                    continue

            logger.info(f"分配结果保存完成: {saved_count}/{len(assignment_results)} 条记录")
            return saved_count

        except Exception as e:
            logger.error(f"保存分配结果失败: {e}")
            raise DatabaseError(f"保存分配结果失败: {e}")

    async def update_assignment_by_user_feedback(
        self,
        report_code: str,
        entry_id: str,
        feedback_data: Dict[str, Any]
    ) -> bool:
        """根据用户反馈更新分配结果"""
        try:
            # 解析report_code
            parts = report_code.split('_')
            if len(parts) >= 3:
                dr07 = parts[0]
                version = parts[-1]
            else:
                dr07 = report_code
                version = 'v1.0'

            # 构建更新数据
            update_fields = []
            update_params = {'entry_id': entry_id, 'dr07': dr07, 'version': version}

            # 处理用户修改的字段
            if 'A4.DR22' in feedback_data:
                dept_ids = feedback_data['A4.DR22']
                if dept_ids and len(dept_ids) > 0:
                    update_fields.append("dr22 = :dr22")
                    update_fields.append("dept_id = :dept_id")
                    update_params['dr22'] = dept_ids[0]
                    update_params['dept_id'] = dept_ids[0]

            if 'B1.BDR01' in feedback_data:
                dept_ids = feedback_data['B1.BDR01']
                if dept_ids and len(dept_ids) > 0:
                    update_fields.append("bdr01 = :bdr01")
                    update_params['bdr01'] = dept_ids[0]

            if 'B1.BDR03' in feedback_data:
                dept_ids = feedback_data['B1.BDR03']
                if dept_ids and len(dept_ids) > 0:
                    update_fields.append("bdr03 = :bdr03")
                    update_params['bdr03'] = dept_ids[0]

            if not update_fields:
                logger.warning(f"用户反馈中没有需要更新的字段: {entry_id}")
                return False

            # 添加更新时间
            update_fields.append("update_time = :update_time")
            update_params['update_time'] = datetime.now()

            # 执行更新
            sql = f"""
            UPDATE biz_dd_post_distribution
            SET {', '.join(update_fields)}
            WHERE submission_id = :entry_id
            AND dr07 = :dr07
            AND version = :version
            """

            response = await self.rdb_client.aexecute(sql, update_params)

            if response.success and response.affected_rows > 0:
                logger.info(f"用户反馈更新成功: {entry_id}")
                return True
            else:
                logger.warning(f"用户反馈更新失败，没有找到匹配的记录: {entry_id}")
                return False

        except Exception as e:
            logger.error(f"用户反馈更新失败 {entry_id}: {e}")
            return False

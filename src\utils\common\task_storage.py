"""
任务存储抽象层
支持内存缓存和Redis两种实现，接口保持一致
"""

import json
import threading
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Optional, Any, List
from loguru import logger


class TaskStorage(ABC):
    """任务存储抽象基类"""
    
    @abstractmethod
    async def create_task(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """创建新任务"""
        pass
    
    @abstractmethod
    async def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        pass
    
    @abstractmethod
    async def update_task(self, task_id: str, updates: Dict[str, Any]) -> bool:
        """更新任务信息"""
        pass
    
    @abstractmethod
    async def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        pass
    
    @abstractmethod
    async def add_task_result(self, task_id: str, result: Dict[str, Any]) -> bool:
        """添加任务结果"""
        pass


class MemoryTaskStorage(TaskStorage):
    """内存任务存储实现"""
    
    def __init__(self):
        self._cache = {}
        self._lock = threading.Lock()
        logger.info("初始化内存任务存储")
    
    async def create_task(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """创建新任务"""
        try:
            with self._lock:
                task_info = {
                    'task_id': task_id,
                    'status': 'pending',
                    'progress': 0.0,
                    'processed_files': 0,
                    'success_files': 0,
                    'failed_files': 0,
                    'current_file': None,
                    'error_message': None,
                    'results': [],
                    'start_time': datetime.now().isoformat(),
                    'end_time': None,
                    **task_data  # 合并传入的任务数据
                }
                self._cache[task_id] = task_info
                logger.info(f"创建任务: {task_id}")
                return True
        except Exception as e:
            logger.error(f"创建任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    async def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        with self._lock:
            task = self._cache.get(task_id)
            if task:
                # 返回副本，避免外部修改
                return task.copy()
            return None
    
    async def update_task(self, task_id: str, updates: Dict[str, Any]) -> bool:
        """更新任务信息"""
        try:
            with self._lock:
                if task_id not in self._cache:
                    logger.warning(f"任务不存在: {task_id}")
                    return False
                
                # 更新任务信息
                self._cache[task_id].update(updates)
                
                # 自动计算进度
                task = self._cache[task_id]
                if task.get('total_files', 0) > 0:
                    task['progress'] = round((task['processed_files'] / task['total_files']) * 100, 2)
                
                # 如果任务完成，设置结束时间
                if updates.get('status') in ['completed', 'failed']:
                    task['end_time'] = datetime.now().isoformat()
                
                logger.debug(f"更新任务: {task_id}, 进度: {task['progress']}%")
                return True
        except Exception as e:
            logger.error(f"更新任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    async def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        try:
            with self._lock:
                if task_id in self._cache:
                    del self._cache[task_id]
                    logger.info(f"删除任务: {task_id}")
                    return True
                return False
        except Exception as e:
            logger.error(f"删除任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    async def add_task_result(self, task_id: str, result: Dict[str, Any]) -> bool:
        """添加任务结果"""
        try:
            with self._lock:
                if task_id not in self._cache:
                    logger.warning(f"任务不存在: {task_id}")
                    return False
                
                self._cache[task_id]['results'].append(result)
                
                # 更新统计信息
                if result.get('success'):
                    self._cache[task_id]['success_files'] += 1
                else:
                    self._cache[task_id]['failed_files'] += 1
                
                self._cache[task_id]['processed_files'] += 1
                
                logger.debug(f"添加任务结果: {task_id}, 文件: {result.get('file_name', 'Unknown')}")
                return True
        except Exception as e:
            logger.error(f"添加任务结果失败: {task_id}, 错误: {str(e)}")
            return False
    
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务（同步方法，可以在后台线程调用）"""
        try:
            with self._lock:
                current_time = datetime.now()
                to_remove = []
                
                for task_id, task in self._cache.items():
                    try:
                        start_time = datetime.fromisoformat(task['start_time'])
                        if (current_time - start_time).total_seconds() > max_age_hours * 3600:
                            to_remove.append(task_id)
                    except:
                        # 如果时间解析失败，也删除这个任务
                        to_remove.append(task_id)
                
                for task_id in to_remove:
                    del self._cache[task_id]
                
                if to_remove:
                    logger.info(f"清理了 {len(to_remove)} 个过期任务")
        except Exception as e:
            logger.error(f"清理任务失败: {str(e)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        with self._lock:
            return {
                "storage_type": "memory",
                "total_tasks": len(self._cache),
                "active_tasks": len([t for t in self._cache.values() if t['status'] in ['pending', 'processing']])
            }


class RedisTaskStorage(TaskStorage):
    """Redis任务存储实现（将来实现）"""
    
    def __init__(self, redis_client=None, key_prefix="task:"):
        self.redis_client = redis_client
        self.key_prefix = key_prefix
        logger.info("初始化Redis任务存储（暂未实现）")
    
    async def create_task(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """创建新任务"""
        # TODO: 实现Redis版本
        raise NotImplementedError("Redis存储暂未实现")
    
    async def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        # TODO: 实现Redis版本
        raise NotImplementedError("Redis存储暂未实现")
    
    async def update_task(self, task_id: str, updates: Dict[str, Any]) -> bool:
        """更新任务信息"""
        # TODO: 实现Redis版本
        raise NotImplementedError("Redis存储暂未实现")
    
    async def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        # TODO: 实现Redis版本
        raise NotImplementedError("Redis存储暂未实现")
    
    async def add_task_result(self, task_id: str, result: Dict[str, Any]) -> bool:
        """添加任务结果"""
        # TODO: 实现Redis版本
        raise NotImplementedError("Redis存储暂未实现")


# 全局任务存储实例
# 将来可以通过配置切换到Redis实现
_task_storage: Optional[TaskStorage] = None

def get_task_storage() -> TaskStorage:
    """获取任务存储实例"""
    global _task_storage
    if _task_storage is None:
        # 现在使用内存存储，将来可以根据配置选择Redis
        _task_storage = MemoryTaskStorage()
    return _task_storage

def set_task_storage(storage: TaskStorage):
    """设置任务存储实例（用于切换到Redis）"""
    global _task_storage
    _task_storage = storage
    logger.info(f"切换任务存储到: {type(storage).__name__}")

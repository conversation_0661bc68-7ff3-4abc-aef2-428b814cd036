"""
DD系统分发数据管理API路由

提供分发前后数据的CRUD操作接口，包括：
- 分发前数据管理
- 分发后数据管理
"""

import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Depends

logger = logging.getLogger(__name__)

from api.knowledge.models.response_models import (
    ListResponse, CreateResponse, UpdateResponse, DeleteResponse, DetailResponse
)
from ..models.requests import PreDistributionCreateRequest, PostDistributionCreateRequest
from ..models.responses import DistributionResponse
from ..models.enums import DataLayerEnum
from ..dependencies.common import get_dd_crud, validate_pagination
from ..utils.helpers import format_response

# 创建路由器
router = APIRouter(tags=["DD分发数据管理"], prefix="/distribution")


# ==================== 分发前数据API ====================

@router.post("/pre", response_model=CreateResponse, summary="创建分发前数据")
async def create_pre_distribution(
    request: PreDistributionCreateRequest,
    dd_crud = Depends(get_dd_crud)
):
    """
    创建分发前数据（DR01-DR21字段）
    
    - **submission_id**: 填报ID，必须唯一
    - **version**: 版本号
    - **type**: 填报类型
    - **dr01**: 数据层
    - **dr06**: 表名
    - **dr07**: 表名ID
    - **dr09**: 数据项名称
    - **dr17**: 需求口径
    """
    try:
        pre_data = request.model_dump()
        pre_id = await dd_crud.create_pre_distribution(pre_data)
        
        return CreateResponse(
            success=True,
            message="分发前数据创建成功",
            data={"pre_distribution_id": pre_id}
        )
    except Exception as e:
        logger.error(f"创建分发前数据失败: {e}")
        raise HTTPException(status_code=400, detail=f"创建分发前数据失败: {str(e)}")


@router.get("/pre/{pre_id}", response_model=DetailResponse, summary="获取分发前数据详情")
async def get_pre_distribution(
    pre_id: int,
    dd_crud = Depends(get_dd_crud)
):
    """
    根据ID获取分发前数据详情
    
    - **pre_id**: 分发前数据ID
    """
    try:
        pre_distribution = await dd_crud.get_pre_distribution(pre_id)
        
        if not pre_distribution:
            raise HTTPException(status_code=404, detail=f"分发前数据不存在: {pre_id}")
        
        return DetailResponse(
            success=True,
            message="获取分发前数据详情成功",
            data=pre_distribution
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分发前数据详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取分发前数据详情失败: {str(e)}")


@router.get("/pre", response_model=ListResponse, summary="查询分发前数据列表")
async def list_pre_distribution(
    data_layer: Optional[DataLayerEnum] = Query(None, description="数据层过滤"),
    version: Optional[str] = Query(None, description="版本过滤"),
    pagination = Depends(validate_pagination),
    dd_crud = Depends(get_dd_crud)
):
    """
    查询分发前数据列表，支持分页和过滤
    
    - **data_layer**: 数据层过滤（可选）
    - **version**: 版本过滤（可选）
    - **page**: 页码（默认1）
    - **page_size**: 每页数量（默认20，最大100）
    """
    try:
        page, page_size, offset = pagination
        
        # 构建查询参数
        filters = {}
        if data_layer is not None:
            filters["dr01"] = data_layer.value
        if version is not None:
            filters["version"] = version
        
        # 查询分发前数据列表
        pre_distributions = await dd_crud.list_pre_distributions(
            **filters,
            limit=page_size,
            offset=offset
        )
        
        # 查询总数
        total_pre_distributions = await dd_crud.list_pre_distributions(**filters)
        total = len(total_pre_distributions)
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        return ListResponse(
            success=True,
            message="查询分发前数据列表成功",
            data={
                "items": pre_distributions,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }
        )
    except Exception as e:
        logger.error(f"查询分发前数据列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询分发前数据列表失败: {str(e)}")


# ==================== 分发后数据API ====================

@router.post("/post", response_model=CreateResponse, summary="创建分发后数据")
async def create_post_distribution(
    request: PostDistributionCreateRequest,
    dd_crud = Depends(get_dd_crud)
):
    """
    创建分发后数据（DR22及后续字段）
    
    - **pre_distribution_id**: 关联的分发前数据ID
    - **submission_id**: 填报ID
    - **version**: 版本号
    - **dept_id**: 部门ID
    - **dr01**: 数据层
    - **dr07**: 表名ID
    - **dr22**: 责任部门
    - **bdr01**: 业务部门
    - **bdr02**: 责任人
    """
    try:
        post_data = request.model_dump()
        post_id = await dd_crud.create_post_distribution(post_data)
        
        return CreateResponse(
            success=True,
            message="分发后数据创建成功",
            data={"post_distribution_id": post_id}
        )
    except Exception as e:
        logger.error(f"创建分发后数据失败: {e}")
        raise HTTPException(status_code=400, detail=f"创建分发后数据失败: {str(e)}")


@router.get("/post/{post_id}", response_model=DetailResponse, summary="获取分发后数据详情")
async def get_post_distribution(
    post_id: int,
    dd_crud = Depends(get_dd_crud)
):
    """
    根据ID获取分发后数据详情
    
    - **post_id**: 分发后数据ID
    """
    try:
        post_distribution = await dd_crud.get_post_distribution(post_id)
        
        if not post_distribution:
            raise HTTPException(status_code=404, detail=f"分发后数据不存在: {post_id}")
        
        return DetailResponse(
            success=True,
            message="获取分发后数据详情成功",
            data=post_distribution
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分发后数据详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取分发后数据详情失败: {str(e)}")


@router.get("/post", response_model=ListResponse, summary="查询分发后数据列表")
async def list_post_distribution(
    dept_id: Optional[str] = Query(None, description="部门ID过滤"),
    data_layer: Optional[DataLayerEnum] = Query(None, description="数据层过滤"),
    version: Optional[str] = Query(None, description="版本过滤"),
    pagination = Depends(validate_pagination),
    dd_crud = Depends(get_dd_crud)
):
    """
    查询分发后数据列表，支持分页和过滤
    
    - **dept_id**: 部门ID过滤（可选）
    - **data_layer**: 数据层过滤（可选）
    - **version**: 版本过滤（可选）
    - **page**: 页码（默认1）
    - **page_size**: 每页数量（默认20，最大100）
    """
    try:
        page, page_size, offset = pagination
        
        # 构建查询参数
        filters = {}
        if dept_id is not None:
            filters["dept_id"] = dept_id
        if data_layer is not None:
            filters["dr01"] = data_layer.value
        if version is not None:
            filters["version"] = version
        
        # 查询分发后数据列表
        post_distributions = await dd_crud.list_post_distributions(
            **filters,
            limit=page_size,
            offset=offset
        )
        
        # 查询总数
        total_post_distributions = await dd_crud.list_post_distributions(**filters)
        total = len(total_post_distributions)
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        return ListResponse(
            success=True,
            message="查询分发后数据列表成功",
            data={
                "items": post_distributions,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }
        )
    except Exception as e:
        logger.error(f"查询分发后数据列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询分发后数据列表失败: {str(e)}")

"""
VDB核心异常体系

定义向量数据库操作中的统一异常体系
参考RDB的异常设计，结合向量数据库的特殊需求

设计原则：
1. 层次化 - 清晰的异常继承层次
2. 信息丰富 - 包含详细的错误上下文
3. 可恢复性 - 区分可恢复和不可恢复的错误
4. 调试友好 - 提供足够的调试信息
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from .types import VectorDBType


# ==================== 基础异常类 ====================

class VectorDBException(Exception):
    """向量数据库基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None,
        database_type: Optional[VectorDBType] = None,
        collection_name: Optional[str] = None,
        operation: Optional[str] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.original_error = original_error
        self.database_type = database_type
        self.collection_name = collection_name
        self.operation = operation
        self.timestamp = datetime.now()
        
        # 如果有原始异常，将其信息添加到详情中
        if original_error:
            self.details.update({
                "original_error_type": type(original_error).__name__,
                "original_error_message": str(original_error)
            })
    
    def __str__(self) -> str:
        """格式化异常信息"""
        parts = []
        
        if self.error_code:
            parts.append(f"[{self.error_code}]")
        
        if self.database_type:
            parts.append(f"[{self.database_type.value}]")
        
        if self.collection_name:
            parts.append(f"[{self.collection_name}]")
        
        if self.operation:
            parts.append(f"[{self.operation}]")
        
        parts.append(self.message)
        
        return " ".join(parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_type": type(self).__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details,
            "database_type": self.database_type.value if self.database_type else None,
            "collection_name": self.collection_name,
            "operation": self.operation,
            "timestamp": self.timestamp.isoformat()
        }


# ==================== 连接相关异常 ====================

class ConnectionError(VectorDBException):
    """连接错误"""
    
    def __init__(self, message: str, host: Optional[str] = None, 
                 port: Optional[int] = None, **kwargs):
        super().__init__(message, error_code="CONNECTION_ERROR", **kwargs)
        if host:
            self.details["host"] = host
        if port:
            self.details["port"] = port


class AuthenticationError(VectorDBException):
    """认证错误"""
    
    def __init__(self, message: str, username: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="AUTHENTICATION_ERROR", **kwargs)
        if username:
            self.details["username"] = username


class TimeoutError(VectorDBException):
    """超时错误"""
    
    def __init__(self, message: str, timeout_seconds: Optional[float] = None, **kwargs):
        super().__init__(message, error_code="TIMEOUT_ERROR", **kwargs)
        if timeout_seconds:
            self.details["timeout_seconds"] = timeout_seconds


# ==================== 集合相关异常 ====================

class CollectionError(VectorDBException):
    """集合相关错误基类"""
    pass


class CollectionNotFoundError(CollectionError):
    """集合不存在错误"""
    
    def __init__(self, collection_name: str, **kwargs):
        super().__init__(
            f"集合 '{collection_name}' 不存在",
            error_code="COLLECTION_NOT_FOUND",
            collection_name=collection_name,
            **kwargs
        )


class CollectionAlreadyExistsError(CollectionError):
    """集合已存在错误"""
    
    def __init__(self, collection_name: str, **kwargs):
        super().__init__(
            f"集合 '{collection_name}' 已存在",
            error_code="COLLECTION_ALREADY_EXISTS",
            collection_name=collection_name,
            **kwargs
        )


class SchemaError(CollectionError):
    """模式定义错误"""
    
    def __init__(self, message: str, schema_details: Optional[Dict[str, Any]] = None, **kwargs):
        super().__init__(message, error_code="SCHEMA_ERROR", **kwargs)
        if schema_details:
            self.details.update(schema_details)


# ==================== 数据相关异常 ====================

class DataError(VectorDBException):
    """数据相关错误基类"""
    pass


class DataValidationError(DataError):
    """数据验证错误"""
    
    def __init__(self, message: str, field_name: Optional[str] = None, 
                 invalid_value: Any = None, **kwargs):
        super().__init__(message, error_code="DATA_VALIDATION_ERROR", **kwargs)
        if field_name:
            self.details["field_name"] = field_name
        if invalid_value is not None:
            self.details["invalid_value"] = str(invalid_value)


class VectorDimensionError(DataError):
    """向量维度错误"""
    
    def __init__(self, message: str, expected_dim: Optional[int] = None,
                 actual_dim: Optional[int] = None, **kwargs):
        super().__init__(message, error_code="VECTOR_DIMENSION_ERROR", **kwargs)
        if expected_dim is not None:
            self.details["expected_dimension"] = expected_dim
        if actual_dim is not None:
            self.details["actual_dimension"] = actual_dim


class InsertError(DataError):
    """插入错误"""
    
    def __init__(self, message: str, data_count: Optional[int] = None, **kwargs):
        super().__init__(message, error_code="INSERT_ERROR", operation="insert", **kwargs)
        if data_count is not None:
            self.details["data_count"] = data_count


class DeleteError(DataError):
    """删除错误"""
    
    def __init__(self, message: str, filter_expr: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="DELETE_ERROR", operation="delete", **kwargs)
        if filter_expr:
            self.details["filter_expr"] = filter_expr


class UpdateError(DataError):
    """更新错误"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, error_code="UPDATE_ERROR", operation="update", **kwargs)


# ==================== 查询相关异常 ====================

class QueryError(VectorDBException):
    """查询相关错误基类"""
    pass


class SearchError(QueryError):
    """搜索错误"""
    
    def __init__(self, message: str, search_params: Optional[Dict[str, Any]] = None, **kwargs):
        super().__init__(message, error_code="SEARCH_ERROR", operation="search", **kwargs)
        if search_params:
            self.details["search_params"] = search_params


class VectorSearchError(SearchError):
    """向量搜索错误"""
    
    def __init__(self, message: str, vector_field: Optional[str] = None,
                 vector_count: Optional[int] = None, **kwargs):
        super().__init__(message, error_code="VECTOR_SEARCH_ERROR", **kwargs)
        if vector_field:
            self.details["vector_field"] = vector_field
        if vector_count is not None:
            self.details["vector_count"] = vector_count


class HybridSearchError(SearchError):
    """混合搜索错误"""
    
    def __init__(self, message: str, request_count: Optional[int] = None,
                 ranker_type: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="HYBRID_SEARCH_ERROR", **kwargs)
        if request_count is not None:
            self.details["request_count"] = request_count
        if ranker_type:
            self.details["ranker_type"] = ranker_type


# ==================== 索引相关异常 ====================

class IndexError(VectorDBException):
    """索引相关错误"""
    
    def __init__(self, message: str, field_name: Optional[str] = None,
                 index_type: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="INDEX_ERROR", **kwargs)
        if field_name:
            self.details["field_name"] = field_name
        if index_type:
            self.details["index_type"] = index_type


# ==================== 配置相关异常 ====================

class ConfigurationError(VectorDBException):
    """配置错误"""
    
    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="CONFIGURATION_ERROR", **kwargs)
        if config_key:
            self.details["config_key"] = config_key


class UnsupportedOperationError(VectorDBException):
    """不支持的操作错误"""
    
    def __init__(self, message: str, operation: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="UNSUPPORTED_OPERATION", **kwargs)
        if operation:
            self.details["unsupported_operation"] = operation


# ==================== 资源相关异常 ====================

class ResourceError(VectorDBException):
    """资源相关错误基类"""
    pass


class ResourceExhaustedError(ResourceError):
    """资源耗尽错误"""
    
    def __init__(self, message: str, resource_type: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="RESOURCE_EXHAUSTED", **kwargs)
        if resource_type:
            self.details["resource_type"] = resource_type


class PartitionError(ResourceError):
    """分区相关错误"""
    
    def __init__(self, message: str, partition_name: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="PARTITION_ERROR", **kwargs)
        if partition_name:
            self.details["partition_name"] = partition_name


# ==================== 便捷异常创建函数 ====================

def create_connection_error(host: str, port: int, reason: str, **kwargs) -> ConnectionError:
    """创建连接错误"""
    return ConnectionError(
        f"无法连接到向量数据库 {host}:{port} - {reason}",
        host=host,
        port=port,
        **kwargs
    )


def create_collection_not_found_error(collection_name: str, **kwargs) -> CollectionNotFoundError:
    """创建集合不存在错误"""
    return CollectionNotFoundError(collection_name, **kwargs)


def create_search_error(collection_name: str, reason: str, **kwargs) -> SearchError:
    """创建搜索错误"""
    return SearchError(
        f"在集合 '{collection_name}' 中搜索失败: {reason}",
        collection_name=collection_name,
        **kwargs
    )


def create_insert_error(collection_name: str, data_count: int, reason: str, **kwargs) -> InsertError:
    """创建插入错误"""
    return InsertError(
        f"向集合 '{collection_name}' 插入 {data_count} 条数据失败: {reason}",
        collection_name=collection_name,
        data_count=data_count,
        **kwargs
    )


def create_schema_error(collection_name: str, reason: str, **kwargs) -> SchemaError:
    """创建模式错误"""
    return SchemaError(
        f"集合 '{collection_name}' 的模式定义错误: {reason}",
        collection_name=collection_name,
        **kwargs
    )


def create_data_validation_error(field_name: str, reason: str, **kwargs) -> DataValidationError:
    """创建数据验证错误"""
    return DataValidationError(
        f"字段 '{field_name}' 数据验证失败: {reason}",
        field_name=field_name,
        **kwargs
    )

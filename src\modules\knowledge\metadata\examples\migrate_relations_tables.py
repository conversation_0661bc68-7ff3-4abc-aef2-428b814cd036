"""
数据库迁移脚本：为关联键信息表添加 knowledge_id 字段
"""

import asyncio
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def migrate_relations_tables():
    """执行关联键信息表的迁移"""
    print("🔧 开始迁移关联键信息表")
    print("-" * 40)

    try:
        # 获取数据库客户端
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
        from service import get_client

        rdb_client = await get_client("database.rdbs.mysql")
        
        # 迁移SQL语句
        migration_sqls = [
            # 1. 备份现有数据（如果有的话）
            """
            CREATE TABLE IF NOT EXISTS `md_source_key_relation_info_backup` AS 
            SELECT * FROM `md_source_key_relation_info`
            """,
            
            """
            CREATE TABLE IF NOT EXISTS `md_index_key_relation_info_backup` AS 
            SELECT * FROM `md_index_key_relation_info`
            """,
            
            # 2. 删除现有表
            "DROP TABLE IF EXISTS `md_source_key_relation_info`",
            "DROP TABLE IF EXISTS `md_index_key_relation_info`",
            
            # 3. 重新创建源关联键信息表（包含 knowledge_id 字段）
            """
            CREATE TABLE `md_source_key_relation_info` (
              `relation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
              `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
              `source_column_id` bigint NOT NULL COMMENT '源字段ID',
              `target_column_id` bigint NOT NULL COMMENT '目标字段ID',
              `relation_type` varchar(20) NOT NULL DEFAULT 'FK' COMMENT '关联类型：FK-外键，REF-参考关联',
              `comment` varchar(500) DEFAULT NULL COMMENT '备注说明',
              `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (`relation_id`),
              KEY `idx_knowledge_id` (`knowledge_id`),
              KEY `idx_source_column_id` (`source_column_id`),
              KEY `idx_target_column_id` (`target_column_id`),
              KEY `idx_relation_type` (`relation_type`),
              UNIQUE KEY `uk_source_key_relation_business` (`knowledge_id`, `source_column_id`, `target_column_id`, `relation_type`),
              CONSTRAINT `fk_md_source_key_relation_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE,
              CONSTRAINT `fk_md_source_key_relation_source` FOREIGN KEY (`source_column_id`) REFERENCES `md_source_columns` (`column_id`) ON DELETE CASCADE ON UPDATE CASCADE,
              CONSTRAINT `fk_md_source_key_relation_target` FOREIGN KEY (`target_column_id`) REFERENCES `md_source_columns` (`column_id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='源关联键信息表'
            """,
            
            # 4. 重新创建指标关联键信息表（包含 knowledge_id 字段）
            """
            CREATE TABLE `md_index_key_relation_info` (
              `relation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
              `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
              `source_column_id` bigint NOT NULL COMMENT '源字段ID',
              `target_column_id` bigint NOT NULL COMMENT '目标字段ID',
              `relation_type` varchar(20) NOT NULL DEFAULT 'FK' COMMENT '关联类型：FK-外键，REF-参考关联',
              `comment` varchar(500) DEFAULT NULL COMMENT '备注说明',
              `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (`relation_id`),
              KEY `idx_knowledge_id` (`knowledge_id`),
              KEY `idx_source_column_id` (`source_column_id`),
              KEY `idx_target_column_id` (`target_column_id`),
              KEY `idx_relation_type` (`relation_type`),
              UNIQUE KEY `uk_index_key_relation_business` (`knowledge_id`, `source_column_id`, `target_column_id`, `relation_type`),
              CONSTRAINT `fk_md_index_key_relation_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE,
              CONSTRAINT `fk_md_index_key_relation_source` FOREIGN KEY (`source_column_id`) REFERENCES `md_index_columns` (`column_id`) ON DELETE CASCADE ON UPDATE CASCADE,
              CONSTRAINT `fk_md_index_key_relation_target` FOREIGN KEY (`target_column_id`) REFERENCES `md_index_columns` (`column_id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标关联键信息表'
            """
        ]
        
        # 执行迁移
        for i, sql in enumerate(migration_sqls, 1):
            try:
                print(f"   执行步骤 {i}/{len(migration_sqls)}: {sql.strip()[:50]}...")
                await rdb_client.aexecute(sql)
                print(f"   ✅ 步骤 {i} 完成")
            except Exception as e:
                if "doesn't exist" in str(e) or "Unknown table" in str(e):
                    print(f"   ⚠️  步骤 {i} 跳过（表不存在）: {e}")
                else:
                    print(f"   ❌ 步骤 {i} 失败: {e}")
                    raise
        
        print("\n✅ 关联键信息表迁移完成！")
        print("   - md_source_key_relation_info 表已更新，包含 knowledge_id 字段")
        print("   - md_index_key_relation_info 表已更新，包含 knowledge_id 字段")
        print("   - 相应的索引和外键约束已创建")
        
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        print(f"\n❌ 迁移失败: {e}")
        raise


async def main():
    """主函数"""
    await migrate_relations_tables()


if __name__ == "__main__":
    asyncio.run(main())

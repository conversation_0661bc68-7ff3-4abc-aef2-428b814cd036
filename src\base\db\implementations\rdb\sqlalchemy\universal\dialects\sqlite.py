

from typing import Dict, Any, List, Optional, Tuple
from .base import DatabaseDialect, DatabaseFeatures, FeatureSupport


class SQLiteDialect(DatabaseDialect):
    """SQLite database dialect implementation"""
    
    @property
    def name(self) -> str:
        return "sqlite"
    
    @property
    def description(self) -> str:
        return "SQLite Database (3.12+)"
    
    @property
    def default_port(self) -> Optional[int]:
        return None  # SQLite is file-based
    
    @property
    def default_driver(self) -> str:
        return "pysqlite"  # Built-in sqlite3
    
    @property
    def async_driver(self) -> Optional[str]:
        return "aiosqlite"
    
    @property
    def features(self) -> DatabaseFeatures:
        return DatabaseFeatures(
            json_support=FeatureSupport.PARTIAL,   # JSON1 extension (3.38+)
            array_support=FeatureSupport.NONE,     # No native array support
            window_functions=FeatureSupport.FULL,  # SQLite 3.25+
            cte_support=FeatureSupport.FULL,       # SQLite 3.8+
            full_text_search=FeatureSupport.FULL,  # FTS5 extension
            partitioning=FeatureSupport.NONE,      # No native partitioning
            upsert_support=FeatureSupport.FULL,    # ON CONFLICT (3.24+)
            returning_clause=FeatureSupport.PARTIAL, # SQLite 3.35+
            bulk_insert=FeatureSupport.PARTIAL,    # Limited bulk operations
            async_support=FeatureSupport.FULL      # aiosqlite
        )
    
    # ==================== Query Building ====================
    
    def build_limit_clause(self, limit: int, offset: Optional[int] = None) -> str:
        """Build SQLite LIMIT clause"""
        clause = f"LIMIT {limit}"
        if offset is not None:
            clause += f" OFFSET {offset}"
        return clause
    
    def build_upsert_statement(
        self,
        table_name: str,
        data: Dict[str, Any],
        conflict_columns: List[str],
        update_columns: Optional[List[str]] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """Build SQLite ON CONFLICT DO UPDATE statement"""
        
        # Determine columns to update
        if update_columns is None:
            # Update all columns except conflict columns
            update_columns = [col for col in data.keys() if col not in conflict_columns]
        
        # Build INSERT part
        columns = list(data.keys())
        placeholders = ", ".join([f":{col}" for col in columns])
        columns_clause = ", ".join([self.quote_identifier(col) for col in columns])
        
        insert_sql = f"INSERT INTO {self.quote_identifier(table_name)} ({columns_clause}) VALUES ({placeholders})"
        
        # Build ON CONFLICT part
        conflict_clause = ", ".join([self.quote_identifier(col) for col in conflict_columns])
        
        if update_columns:
            update_clauses = []
            for col in update_columns:
                quoted_col = self.quote_identifier(col)
                update_clauses.append(f"{quoted_col} = excluded.{quoted_col}")
            
            update_sql = f" ON CONFLICT ({conflict_clause}) DO UPDATE SET " + ", ".join(update_clauses)
            full_sql = insert_sql + update_sql
        else:
            full_sql = insert_sql + f" ON CONFLICT ({conflict_clause}) DO NOTHING"
        
        return full_sql, data
    
    def build_bulk_insert_statement(
        self,
        table_name: str,
        columns: List[str],
        batch_size: int,
        ignore_conflicts: bool = False
    ) -> str:
        """Build SQLite bulk insert statement"""
        placeholders = ", ".join(["?"] * len(columns))
        values_clause = ", ".join([f"({placeholders})"] * batch_size)
        columns_clause = ", ".join([self.quote_identifier(col) for col in columns])
        
        sql = f"INSERT INTO {self.quote_identifier(table_name)} ({columns_clause}) VALUES {values_clause}"
        
        if ignore_conflicts:
            sql = sql.replace("INSERT", "INSERT OR IGNORE")
        
        return sql
    
    # ==================== Identifier Handling ====================
    
    def get_identifier_quote_char(self) -> str:
        """SQLite uses double quotes for identifier quoting"""
        return '"'
    
    def get_parameter_placeholder(self) -> str:
        """SQLite uses ? for parameter placeholders"""
        return "?"
    
    # ==================== Data Type Mapping ====================
    
    def map_python_type_to_sql(self, python_type: type) -> str:
        """Map Python types to SQLite SQL types"""
        type_mapping = {
            int: "INTEGER",
            float: "REAL",
            str: "TEXT",
            bool: "INTEGER",  # SQLite stores booleans as integers
            bytes: "BLOB",
            dict: "TEXT",     # Store as JSON string
            list: "TEXT",     # Store as JSON string
        }
        
        return type_mapping.get(python_type, "TEXT")
    
    def get_boolean_literal(self, value: bool) -> str:
        """SQLite boolean literals (stored as integers)"""
        return "1" if value else "0"
    
    # ==================== SQLite-Specific Features ====================
    
    def build_fts_search_query(
        self,
        fts_table: str,
        search_term: str,
        columns: Optional[List[str]] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Build SQLite FTS (Full-Text Search) query
        
        Args:
            fts_table: FTS virtual table name
            search_term: Search term
            columns: Specific columns to search (optional)
        
        Returns:
            Tuple of (SQL, parameters)
        """
        table = self.quote_identifier(fts_table)
        
        if columns:
            # Search in specific columns
            column_searches = []
            for col in columns:
                column_searches.append(f"{self.quote_identifier(col)}: :search_term")
            where_clause = " OR ".join(column_searches)
            sql = f"SELECT *, rank FROM {table} WHERE {table} MATCH :search_term ORDER BY rank"
        else:
            # Search in all columns
            sql = f"SELECT *, rank FROM {table} WHERE {table} MATCH :search_term ORDER BY rank"
        
        return sql, {"search_term": search_term}
    
    def build_json_extract_query(
        self,
        table_name: str,
        json_column: str,
        json_path: str,
        where_clause: Optional[str] = None
    ) -> str:
        """
        Build SQLite JSON extraction query (requires JSON1 extension)
        
        Args:
            table_name: Target table
            json_column: JSON column name
            json_path: JSON path expression
            where_clause: Optional WHERE clause
        
        Returns:
            SQL query
        """
        json_col = self.quote_identifier(json_column)
        table = self.quote_identifier(table_name)
        
        sql = f"SELECT json_extract({json_col}, '{json_path}') AS extracted_value FROM {table}"
        
        if where_clause:
            sql += f" WHERE {where_clause}"
        
        return sql
    
    def create_fts_table(
        self,
        table_name: str,
        source_table: str,
        columns: List[str],
        fts_version: str = "fts5"
    ) -> str:
        """
        Create FTS virtual table
        
        Args:
            table_name: FTS table name
            source_table: Source table name
            columns: Columns to index
            fts_version: FTS version (fts4 or fts5)
        
        Returns:
            CREATE VIRTUAL TABLE SQL
        """
        columns_clause = ", ".join([self.quote_identifier(col) for col in columns])
        
        if fts_version == "fts5":
            sql = f"""
            CREATE VIRTUAL TABLE {self.quote_identifier(table_name)} 
            USING fts5({columns_clause}, content='{source_table}')
            """
        else:  # fts4
            sql = f"""
            CREATE VIRTUAL TABLE {self.quote_identifier(table_name)} 
            USING fts4({columns_clause}, content='{source_table}')
            """
        
        return sql
    
    def get_table_info(self, table_name: str) -> str:
        """Get SQLite table information"""
        return f"PRAGMA table_info({table_name})"
    
    def get_index_info(self, table_name: str) -> str:
        """Get SQLite index information"""
        return f"PRAGMA index_list({table_name})"
    
    def get_foreign_key_info(self, table_name: str) -> str:
        """Get SQLite foreign key information"""
        return f"PRAGMA foreign_key_list({table_name})"
    
    def optimize_database(self) -> str:
        """Get SQLite database optimization commands"""
        return "VACUUM; ANALYZE;"
    
    def enable_wal_mode(self) -> str:
        """Enable WAL (Write-Ahead Logging) mode"""
        return "PRAGMA journal_mode=WAL;"
    
    def get_database_size(self) -> str:
        """Get database size information"""
        return "SELECT page_count * page_size AS size FROM pragma_page_count(), pragma_page_size();"
    
    # ==================== Connection Handling ====================
    
    def build_connection_url(
        self,
        database: str,  # File path for SQLite
        use_async: bool = True,
        **kwargs
    ) -> str:
        """
        Build SQLite connection URL
        
        Args:
            database: Database file path
            use_async: Whether to use async driver
            **kwargs: Additional parameters
        
        Returns:
            Connection URL
        """
        if use_async and self.async_driver:
            scheme = f"{self.name}+{self.async_driver}"
        else:
            scheme = self.name
        
        # For SQLite, we don't need host/port/username/password
        url = f"{scheme}:///{database}"
        
        # Add query parameters
        if kwargs:
            params = "&".join([f"{k}={v}" for k, v in kwargs.items()])
            url += f"?{params}"
        
        return url
    
    def get_recommended_engine_options(self) -> Dict[str, Any]:
        """Get recommended SQLAlchemy engine options for SQLite"""
        return {
            'pool_pre_ping': False,  # Not needed for SQLite
            'connect_args': {
                'check_same_thread': False,  # Allow multi-threading
                'timeout': 20,  # Connection timeout
                'isolation_level': None,  # Autocommit mode
            },
            'poolclass': None,  # Use NullPool for SQLite
        }
    
    # ==================== SQLite Pragma Helpers ====================
    
    def set_pragma(self, pragma_name: str, value: Any) -> str:
        """Set SQLite pragma"""
        return f"PRAGMA {pragma_name} = {value};"
    
    def get_pragma(self, pragma_name: str) -> str:
        """Get SQLite pragma value"""
        return f"PRAGMA {pragma_name};"
    
    def get_common_pragmas(self) -> Dict[str, str]:
        """Get common SQLite pragma settings for performance"""
        return {
            'synchronous': 'NORMAL',      # Balance between safety and speed
            'cache_size': '-64000',       # 64MB cache
            'temp_store': 'MEMORY',       # Store temp tables in memory
            'mmap_size': '268435456',     # 256MB memory-mapped I/O
            'journal_mode': 'WAL',        # Write-Ahead Logging
            'foreign_keys': 'ON',         # Enable foreign key constraints
        }

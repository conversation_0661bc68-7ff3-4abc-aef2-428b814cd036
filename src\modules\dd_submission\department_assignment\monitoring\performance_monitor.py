#!/usr/bin/env python3
"""
DDCrud批量操作性能监控系统

提供实时性能监控、指标收集、告警触发等功能
"""

import time
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from collections import deque
import json
import os

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    timestamp: datetime
    operation_name: str
    record_count: int
    execution_time: float
    records_per_second: float
    database_calls: int
    success: bool
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class AlertRule:
    """告警规则配置"""
    name: str
    condition: Callable[[PerformanceMetrics], bool]
    severity: str  # 'low', 'medium', 'high', 'critical'
    message_template: str
    cooldown_seconds: int = 300  # 告警冷却时间
    enabled: bool = True


class PerformanceMonitor:
    """DDCrud批量操作性能监控器"""
    
    def __init__(self, max_history_size: int = 1000):
        """
        初始化性能监控器
        
        Args:
            max_history_size: 最大历史记录数量
        """
        self.metrics_history = deque(maxlen=max_history_size)
        self.alert_rules = []
        self.last_alert_times = {}  # 记录最后告警时间
        self.monitoring_enabled = True
        
        # 性能基准
        self.performance_baselines = {
            'min_records_per_second': 100,
            'max_execution_time': 10.0,
            'max_error_rate': 0.05,
            'max_fallback_rate': 0.1
        }
        
        # 初始化默认告警规则
        self._setup_default_alert_rules()
    
    def _setup_default_alert_rules(self):
        """设置默认告警规则"""
        
        # 性能下降告警
        self.add_alert_rule(AlertRule(
            name="performance_degradation",
            condition=lambda m: m.success and m.records_per_second < self.performance_baselines['min_records_per_second'],
            severity="high",
            message_template="批量操作性能严重下降: {records_per_second:.1f}条/秒 (基准: {baseline}条/秒)",
            cooldown_seconds=300
        ))
        
        # 执行时间过长告警
        self.add_alert_rule(AlertRule(
            name="slow_execution",
            condition=lambda m: m.success and m.execution_time > self.performance_baselines['max_execution_time'],
            severity="medium",
            message_template="批量操作执行时间过长: {execution_time:.2f}秒 (基准: {baseline}秒)",
            cooldown_seconds=180
        ))
        
        # 操作失败告警
        self.add_alert_rule(AlertRule(
            name="operation_failure",
            condition=lambda m: not m.success,
            severity="high",
            message_template="批量操作失败: {error_message}",
            cooldown_seconds=60
        ))
        
        # 大批量数据处理异常告警
        self.add_alert_rule(AlertRule(
            name="large_batch_anomaly",
            condition=lambda m: m.record_count > 1000 and m.records_per_second < 500,
            severity="medium",
            message_template="大批量数据处理异常: {record_count}条记录仅{records_per_second:.1f}条/秒",
            cooldown_seconds=600
        ))
    
    def add_alert_rule(self, rule: AlertRule):
        """添加告警规则"""
        self.alert_rules.append(rule)
        logger.info(f"添加告警规则: {rule.name} ({rule.severity})")
    
    def record_operation(
        self,
        operation_name: str,
        record_count: int,
        execution_time: float,
        database_calls: int,
        success: bool,
        error_message: Optional[str] = None
    ):
        """记录操作性能指标"""
        
        if not self.monitoring_enabled:
            return
        
        # 计算性能指标
        records_per_second = record_count / execution_time if execution_time > 0 else 0
        
        # 创建性能指标对象
        metrics = PerformanceMetrics(
            timestamp=datetime.now(),
            operation_name=operation_name,
            record_count=record_count,
            execution_time=execution_time,
            records_per_second=records_per_second,
            database_calls=database_calls,
            success=success,
            error_message=error_message
        )
        
        # 存储到历史记录
        self.metrics_history.append(metrics)
        
        # 记录日志
        if success:
            logger.info(
                f"性能监控 - {operation_name}: "
                f"{record_count}条记录, {execution_time:.2f}秒, "
                f"{records_per_second:.1f}条/秒, {database_calls}次调用"
            )
        else:
            logger.error(
                f"性能监控 - {operation_name}失败: "
                f"{record_count}条记录, {execution_time:.2f}秒, "
                f"错误: {error_message}"
            )
        
        # 检查告警条件
        self._check_alerts(metrics)
        
        # 保存到文件（可选）
        self._save_metrics_to_file(metrics)
    
    def _check_alerts(self, metrics: PerformanceMetrics):
        """检查告警条件"""
        
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
            
            # 检查冷却时间
            last_alert_time = self.last_alert_times.get(rule.name)
            if last_alert_time:
                time_since_last_alert = (datetime.now() - last_alert_time).total_seconds()
                if time_since_last_alert < rule.cooldown_seconds:
                    continue
            
            # 检查告警条件
            try:
                if rule.condition(metrics):
                    self._trigger_alert(rule, metrics)
                    self.last_alert_times[rule.name] = datetime.now()
            except Exception as e:
                logger.error(f"告警规则{rule.name}检查失败: {e}")
    
    def _trigger_alert(self, rule: AlertRule, metrics: PerformanceMetrics):
        """触发告警"""
        
        # 格式化告警消息
        message = rule.message_template.format(
            **metrics.to_dict(),
            baseline=self.performance_baselines.get('min_records_per_second', 'N/A')
        )
        
        # 记录告警日志
        log_level = {
            'low': logging.INFO,
            'medium': logging.WARNING,
            'high': logging.ERROR,
            'critical': logging.CRITICAL
        }.get(rule.severity, logging.WARNING)
        
        logger.log(log_level, f"🚨 告警触发 [{rule.severity.upper()}] {rule.name}: {message}")
        
        # 发送告警通知（可扩展）
        self._send_alert_notification(rule, message, metrics)
    
    def _send_alert_notification(self, rule: AlertRule, message: str, metrics: PerformanceMetrics):
        """发送告警通知（可扩展实现）"""
        
        # 这里可以集成各种通知方式：
        # - 邮件通知
        # - 钉钉/企业微信通知
        # - 短信通知
        # - 监控系统API调用
        
        alert_data = {
            'rule_name': rule.name,
            'severity': rule.severity,
            'message': message,
            'timestamp': metrics.timestamp.isoformat(),
            'operation_name': metrics.operation_name,
            'metrics': metrics.to_dict()
        }
        
        # 示例：保存到告警文件
        alert_file = os.path.join(os.path.dirname(__file__), 'alerts.log')
        try:
            with open(alert_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(alert_data, ensure_ascii=False) + '\n')
        except Exception as e:
            logger.error(f"保存告警记录失败: {e}")
    
    def _save_metrics_to_file(self, metrics: PerformanceMetrics):
        """保存性能指标到文件"""
        
        metrics_file = os.path.join(os.path.dirname(__file__), 'performance_metrics.log')
        try:
            with open(metrics_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(metrics.to_dict(), ensure_ascii=False) + '\n')
        except Exception as e:
            logger.debug(f"保存性能指标失败: {e}")
    
    def get_recent_metrics(self, minutes: int = 60) -> List[PerformanceMetrics]:
        """获取最近N分钟的性能指标"""
        
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [m for m in self.metrics_history if m.timestamp >= cutoff_time]
    
    def get_performance_summary(self, minutes: int = 60) -> Dict[str, Any]:
        """获取性能摘要"""
        
        recent_metrics = self.get_recent_metrics(minutes)
        
        if not recent_metrics:
            return {
                'period_minutes': minutes,
                'total_operations': 0,
                'message': '暂无数据'
            }
        
        # 计算统计指标
        total_operations = len(recent_metrics)
        successful_operations = [m for m in recent_metrics if m.success]
        failed_operations = [m for m in recent_metrics if not m.success]
        
        total_records = sum(m.record_count for m in successful_operations)
        total_time = sum(m.execution_time for m in successful_operations)
        
        avg_records_per_second = 0
        if successful_operations:
            avg_records_per_second = sum(m.records_per_second for m in successful_operations) / len(successful_operations)
        
        return {
            'period_minutes': minutes,
            'total_operations': total_operations,
            'successful_operations': len(successful_operations),
            'failed_operations': len(failed_operations),
            'success_rate': len(successful_operations) / total_operations if total_operations > 0 else 0,
            'total_records_processed': total_records,
            'average_records_per_second': avg_records_per_second,
            'total_execution_time': total_time,
            'performance_status': self._assess_performance_status(recent_metrics)
        }
    
    def _assess_performance_status(self, metrics: List[PerformanceMetrics]) -> str:
        """评估性能状态"""
        
        if not metrics:
            return 'unknown'
        
        successful_metrics = [m for m in metrics if m.success]
        
        if not successful_metrics:
            return 'critical'
        
        avg_speed = sum(m.records_per_second for m in successful_metrics) / len(successful_metrics)
        error_rate = (len(metrics) - len(successful_metrics)) / len(metrics)
        
        if avg_speed >= self.performance_baselines['min_records_per_second'] and error_rate <= self.performance_baselines['max_error_rate']:
            return 'excellent'
        elif avg_speed >= self.performance_baselines['min_records_per_second'] * 0.8:
            return 'good'
        elif avg_speed >= self.performance_baselines['min_records_per_second'] * 0.5:
            return 'degraded'
        else:
            return 'poor'
    
    def enable_monitoring(self):
        """启用监控"""
        self.monitoring_enabled = True
        logger.info("性能监控已启用")
    
    def disable_monitoring(self):
        """禁用监控"""
        self.monitoring_enabled = False
        logger.info("性能监控已禁用")
    
    def update_baseline(self, metric_name: str, value: float):
        """更新性能基准"""
        if metric_name in self.performance_baselines:
            old_value = self.performance_baselines[metric_name]
            self.performance_baselines[metric_name] = value
            logger.info(f"更新性能基准 {metric_name}: {old_value} -> {value}")
        else:
            logger.warning(f"未知的性能基准指标: {metric_name}")


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def monitor_batch_operation_enhanced(operation_name: str):
    """增强版批量操作监控装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            record_count = 0
            database_calls = 1  # 默认值
            
            try:
                # 尝试从参数中获取记录数量
                if args and hasattr(args[1], '__len__'):  # 假设第二个参数是数据列表
                    record_count = len(args[1])
                elif 'data' in kwargs and hasattr(kwargs['data'], '__len__'):
                    record_count = len(kwargs['data'])
                
                # 执行原函数
                result = await func(*args, **kwargs)
                
                # 记录成功指标
                execution_time = time.time() - start_time
                
                # 尝试从结果中获取实际处理的记录数
                if isinstance(result, int):
                    record_count = result
                elif hasattr(result, 'affected_rows'):
                    record_count = result.affected_rows
                
                performance_monitor.record_operation(
                    operation_name=operation_name,
                    record_count=record_count,
                    execution_time=execution_time,
                    database_calls=database_calls,
                    success=True
                )
                
                return result
                
            except Exception as e:
                # 记录失败指标
                execution_time = time.time() - start_time
                
                performance_monitor.record_operation(
                    operation_name=operation_name,
                    record_count=record_count,
                    execution_time=execution_time,
                    database_calls=database_calls,
                    success=False,
                    error_message=str(e)
                )
                
                raise
        
        return wrapper
    return decorator


# 使用示例
if __name__ == "__main__":
    # 模拟性能数据
    async def simulate_monitoring():
        print("🔄 模拟性能监控数据...")
        
        # 模拟正常操作
        performance_monitor.record_operation(
            operation_name="batch_save_test",
            record_count=1000,
            execution_time=0.5,
            database_calls=2,
            success=True
        )
        
        # 模拟慢操作
        performance_monitor.record_operation(
            operation_name="slow_batch_save",
            record_count=1000,
            execution_time=15.0,
            database_calls=2,
            success=True
        )
        
        # 模拟失败操作
        performance_monitor.record_operation(
            operation_name="failed_batch_save",
            record_count=500,
            execution_time=2.0,
            database_calls=1,
            success=False,
            error_message="数据库连接超时"
        )
        
        # 获取性能摘要
        summary = performance_monitor.get_performance_summary(60)
        print(f"📊 性能摘要: {json.dumps(summary, indent=2, ensure_ascii=False)}")
    
    asyncio.run(simulate_monitoring())

"""
Configuration Loaders

配置加载器 - 支持多种配置源

Author: AI Assistant
Created: 2025-07-11
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from pathlib import Path

import hydra
from hydra import compose, initialize
from omegaconf import DictConfig, OmegaConf

from ..exceptions import ConfigError

logger = logging.getLogger(__name__)


class ConfigLoader(ABC):
    """配置加载器基类"""
    
    @abstractmethod
    async def load(self, **kwargs) -> DictConfig:
        """加载配置"""
        pass
    
    @abstractmethod
    async def cleanup(self):
        """清理资源"""
        pass


class HydraLoader(ConfigLoader):
    """
    Hydra配置加载器
    
    从Hydra配置文件加载静态配置
    """
    
    def __init__(self):
        self._initialized = False
        self._config_path = None
    
    async def load(self,
                   config_path: str = None,
                   config_name: str = "config",
                   **kwargs) -> DictConfig:
        """
        加载Hydra配置

        Args:
            config_path: 配置文件路径（相对路径），None表示自动检测
            config_name: 配置文件名
            **kwargs: 额外参数

        Returns:
            配置对象
        """
        try:
            # 如果没有指定config_path，自动检测
            if config_path is None:
                # Hydra的initialize是相对于调用文件的位置解析路径
                # 当前文件是 service/config/loader.py
                # 需要找到相对于这个文件的config目录路径
                current_file = Path(__file__)  # service/config/loader.py
                loader_dir = current_file.parent  # service/config/
                service_dir = loader_dir.parent  # service/
                src_dir = service_dir.parent  # src/
                config_dir = src_dir / "config"  # src/config/

                # 计算相对于loader.py文件的路径
                try:
                    config_path = str(config_dir.relative_to(loader_dir))
                    # 这应该得到 "../../config"
                except ValueError:
                    config_path = "../../config"

            # 验证配置路径（相对于loader.py文件）
            loader_dir = Path(__file__).parent
            abs_config_path = loader_dir / config_path
            abs_config_path = abs_config_path.resolve()  # 解析为绝对路径

            if not abs_config_path.exists():
                raise ConfigError(f"配置路径不存在: {abs_config_path}")

            # 检查配置文件是否存在
            config_file = abs_config_path / f"{config_name}.yaml"
            if not config_file.exists():
                raise ConfigError(f"配置文件不存在: {config_file}")

            self._config_path = config_path

            # 使用Hydra的initialize和compose
            # config_path是相对于当前文件（loader.py）的路径
            with initialize(version_base=None, config_path=config_path):
                config = compose(config_name=config_name, **kwargs)
                self._initialized = True
                logger.info(f"Hydra配置加载成功: {config_path}/{config_name}")
                return config

        except Exception as e:
            logger.error(f"Hydra配置加载失败: {e}")
            raise ConfigError(f"Failed to load Hydra config: {e}") from e
    
    async def cleanup(self):
        """清理Hydra资源"""
        # Hydra的清理通常由框架自动处理
        self._initialized = False
        logger.debug("Hydra配置加载器清理完成")


class DatabaseLoader(ConfigLoader):
    """
    数据库配置加载器
    
    从数据库动态加载配置
    """
    
    def __init__(self):
        self._connection = None
    
    async def load(self, 
                   connection_config: Optional[Dict] = None,
                   **kwargs) -> DictConfig:
        """
        从数据库加载配置
        
        Args:
            connection_config: 数据库连接配置
            **kwargs: 额外参数
            
        Returns:
            配置对象
        """
        try:
            # 建立数据库连接
            await self._connect(connection_config)
            
            # 加载配置数据
            config_data = await self._load_config_from_db(**kwargs)
            
            # 转换为DictConfig
            config = OmegaConf.create(config_data)
            
            logger.info("数据库配置加载成功")
            return config
            
        except Exception as e:
            logger.error(f"数据库配置加载失败: {e}")
            raise ConfigError(f"Failed to load database config: {e}") from e
    
    async def _connect(self, connection_config: Optional[Dict]):
        """建立数据库连接"""
        if self._connection:
            return
        
        # TODO: 实现数据库连接逻辑
        # 这里需要根据实际的数据库配置表结构来实现
        logger.info("数据库连接建立成功")
    
    async def _load_config_from_db(self, **kwargs) -> Dict:
        """从数据库加载配置数据"""
        # TODO: 实现从数据库加载配置的逻辑
        # 这里需要根据实际的配置表结构来实现
        
        # 示例配置结构
        config_data = {
            "database": {
                "rdbs": {
                    "mysql": {
                        "host": "dynamic_host",
                        "port": 3306,
                        # 其他动态配置...
                    }
                },
                "vdbs": {
                    "pgvector": {
                        "host": "dynamic_vector_host",
                        "port": 5432,
                        # 其他动态配置...
                    }
                }
            }
        }
        
        logger.debug("从数据库加载配置数据完成")
        return config_data
    
    async def cleanup(self):
        """清理数据库连接"""
        if self._connection:
            # TODO: 关闭数据库连接
            self._connection = None
        
        logger.debug("数据库配置加载器清理完成")


class FileLoader(ConfigLoader):
    """
    文件配置加载器
    
    从普通配置文件加载配置
    """
    
    async def load(self, 
                   file_path: str,
                   file_format: str = "yaml",
                   **kwargs) -> DictConfig:
        """
        从文件加载配置
        
        Args:
            file_path: 文件路径
            file_format: 文件格式 ("yaml", "json")
            **kwargs: 额外参数
            
        Returns:
            配置对象
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                raise ConfigError(f"配置文件不存在: {file_path}")
            
            if file_format.lower() == "yaml":
                config = OmegaConf.load(file_path)
            elif file_format.lower() == "json":
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                config = OmegaConf.create(data)
            else:
                raise ConfigError(f"不支持的文件格式: {file_format}")
            
            logger.info(f"文件配置加载成功: {file_path}")
            return config
            
        except Exception as e:
            logger.error(f"文件配置加载失败: {e}")
            raise ConfigError(f"Failed to load file config: {e}") from e
    
    async def cleanup(self):
        """清理资源"""
        logger.debug("文件配置加载器清理完成")

# 系统功能需求文档

## 第一个接口功能实现：业务报送

### 输入处理

- 前端传入 `report_code` 格式： {
    "report_code": "G0107_ADS_release" ->version 现在这里不需要拆分，直接被认为是version本身
}
- 根据 `version` 参数，在 `biz_dd_pre` 表中按where条件搜索满足条件的行数
- 如果能搜到，返回
```json
[
  {
	  "code": "0",
	  "msg": "义务报送信息正在处理中"
  }
]
```
- 如果搜不到，返回
```json
[
  {
	  "code": "400", # 真的错误码
	  "msg": "无法在数据库里搜索到相关信息" # 真的错误
  }
]
```
此时，我们并不会结束服务，和前端的通信一直在，直到下面的步骤运行完毕。
### 核心处理流程

#### 1. 精确匹配搜索

- 对在`biz_dd_pre`搜索出的每一行，使用 其 `dr09` 和 `dr17` 字段
- 在 `dd_submission` 表中进行完全匹配搜索
- **如果找到匹配**：返回 `dd_submission_data` 中所有匹配行的完整信息
- **如果未找到**：进入混合搜索流程

#### 2. 混合搜索

- 使用 `dr09` 和 `dr17` 字段，对 `dr17` 进行NLP分词处理
- 将分词结果+`dr09` 和 `dr17`向量化，进行向量搜索和模糊搜索
- **如果有高于阈值（threshold=0.7）的匹配项**：
    - 提取 `dd_submission_data` 中匹配行及其评分
    - 如果只有一个结果：直接返回
    - 如果多个结果：执行业务逻辑筛选

#### 2.1  业务逻辑筛选（多个匹配结果时）

- **套系匹配筛选（第一级）**：
    
    - 根据 `dd_submission_data` 匹配行中的`dd_report_data_id` 到 `dd_report_data` 表查找对应的 `set` （套系）信息（有surver/1104/djz等具体的枚举值）
    - 维护字典：survey为非标准套系，其他为标准套系
    - 优先选择同类套系（standard/non-standard）的结果
- **报告类型筛选（如果上述筛选后还多于1个，进入第二级）**：
    
    - 根据 `dd_report_data_id` 查找对应的 `report_type`（detail/index两种）
    - 判断是否匹配搜索项的报告类型
- **提交类型筛选（如果上述筛选后还多于1个，进入第三级）**：
    
    - 比较 `biz_dd_pre.submission_type` 与 `dd_submission.type` 是否一致
- **DR01字段筛选（如果上述筛选后还多于1个，进入第四级）****：
    
    - 判断 `dr01` 列是否一致
- **评分筛选**：
    
    - 如果某步骤筛选前>2个，筛选后为0个，或最终仍有多个结果，则根据`dd_submission_data` 中匹配行及其评分选择最高的一个
    - 根据混合搜索的score选择最高分的结果

#### 3. TFIDF部门推荐（上述混合搜索无结果（即全部低于阈值）时）

- 使用TFIDF方法，以 `dd_departments` 表的 `dept_name` 和分词后的 `dept_desc` 作为基础
- 用 `dr09` 和分词后的 `dr17` 进行词频匹配
- 找到词频最高的部门，获取其 `dept_id`
- 根据 `dept_id` 到 `dd_department_relation` 表查找所有关联的 `table_id` 列表
需注意，dept_id永远只有一个
### 数据入库操作

- 将 `biz_dd_pre`（搜索项）和 `dd_submission_data`（结果项）信息拼接
- 存入 `biz_dd_post` 库，确保字段完全覆盖（`dept_id` 对应 `dr22` 列,`dr22`=`bdr01`,`brd03`="空“）
- 总共包含
DR22 / BDR01 / BDR02 / BDR03 / BDR04 / BDR05
其中 DR22,BDR01,BDR03需要填写；BDR02默认值：“”，BDR04默认值：“”；BDR05默认值：“”
- **TFIDF推荐结果**：`dr22` 和 `bdr01` 设置为 `dept_id`，但是为`list[dept_id]`,`bdr03`="“
- 此时需在`biz_dd_post_distribution` 里先删除一遍（根据submission_id, dept_id和version，唯一键) 记录，确保用户不会重复入库，再入库；由于可能会有非常多的数据，需要每行数据通过上述流程检索后，搜出来放一起，再统一入库

### 输出格式
输出时会访问前端接口（待定）

```json
[
  {
    "entry_id": "搜索项的submission_id",
    "entry_type": "搜索项的submission_type" 需要转换 ITEM(前端输出，此json需要) <-> SUBMISSION(submission_type), 和 TABLE(前端输出，此json需要) <-> RANGE(submission_type)
    "DR22": ["部门id"],
    "BDR01": ["部门id"],
    "BDR03": "" // 空字符串或varchar(200)支持的格式
  }
]
```

- 精确搜索多结果时：list包含多个dict
- 其他情况：list只包含一个dict
此后，前端会返回给我们一个类似
```json
[
  {
	  "code": "0",
	  "msg": "义务报送信息正在处理中"
  }
]
```
不管怎么样此时交互结束
## 第二个接口功能实现:数据回填

### 输入参数
```json
{
  "report_code": "G0107_beta_version", # biz_dd_post_distribution 的version=report_code
  “dept_id”:"部门ID"
  "step": "义务解读",  # 报表解读/义务解读/业务解读/IT解读
  "data":[
    {
      "entry_id": "...",
      "entry_type": "范围项",
      "DR22":[部门id],
      "BDR01": [部门id],
      "BDR03": str,
      ...
    },
    {
      "entry_id": "...",
      "entry_type": "填报项",
      "DR22":[部门id],
      "BDR01": [部门id],
      "BDR03": str,
      ...
    }
  ]
}
```
根据version(完全等于report_code，不需要拆分)，dept_id和submission_id（每一个entry_id）（submission_type(entry_type)用不用都可以）能够确认biz_dd_post_distribution的唯一行，前端传了dr22 就更新dr22,但是规定只检查`"dr22","bdr01","bdr02","bdr03","bdr04"`其余参数不管。返回
如果能更新成功，返回
```json
[
  {
	  "code": "0",
	  "msg": "义务报送信息更新成功"
  }
]
```
- 如果搜不到，返回
```json
[
  {
	  "code": "400", # 真的错误码
	  "msg": "无法在数据库里搜索到相关信息"  #真的错误
  }
]
```
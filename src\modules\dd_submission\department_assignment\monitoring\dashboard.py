#!/usr/bin/env python3
"""
DDCrud批量操作监控仪表板

提供Web界面展示性能监控数据和告警信息
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any
from pathlib import Path

try:
    from flask import Flask, render_template_string, jsonify, request
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("Flask未安装，仪表板功能不可用。安装命令: pip install flask")

from .performance_monitor import performance_monitor


class MonitoringDashboard:
    """监控仪表板"""
    
    def __init__(self, host='localhost', port=5000):
        self.host = host
        self.port = port
        self.app = None
        
        if FLASK_AVAILABLE:
            self.app = Flask(__name__)
            self._setup_routes()
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.route('/')
        def dashboard():
            """主仪表板页面"""
            return render_template_string(DASHBOARD_HTML_TEMPLATE)
        
        @self.app.route('/api/metrics')
        def get_metrics():
            """获取性能指标API"""
            minutes = request.args.get('minutes', 60, type=int)
            summary = performance_monitor.get_performance_summary(minutes)
            return jsonify(summary)
        
        @self.app.route('/api/recent_operations')
        def get_recent_operations():
            """获取最近操作记录API"""
            minutes = request.args.get('minutes', 60, type=int)
            recent_metrics = performance_monitor.get_recent_metrics(minutes)
            
            operations = []
            for metric in recent_metrics[-20:]:  # 最近20条
                operations.append({
                    'timestamp': metric.timestamp.strftime('%H:%M:%S'),
                    'operation_name': metric.operation_name,
                    'record_count': metric.record_count,
                    'execution_time': round(metric.execution_time, 2),
                    'records_per_second': round(metric.records_per_second, 1),
                    'success': metric.success,
                    'error_message': metric.error_message
                })
            
            return jsonify(operations)
        
        @self.app.route('/api/performance_chart')
        def get_performance_chart():
            """获取性能图表数据API"""
            minutes = request.args.get('minutes', 60, type=int)
            recent_metrics = performance_monitor.get_recent_metrics(minutes)
            
            chart_data = {
                'timestamps': [],
                'records_per_second': [],
                'execution_times': []
            }
            
            for metric in recent_metrics:
                if metric.success:
                    chart_data['timestamps'].append(metric.timestamp.strftime('%H:%M'))
                    chart_data['records_per_second'].append(metric.records_per_second)
                    chart_data['execution_times'].append(metric.execution_time)
            
            return jsonify(chart_data)
        
        @self.app.route('/api/alerts')
        def get_alerts():
            """获取告警信息API"""
            # 读取告警日志文件
            alerts = []
            alert_file = Path(__file__).parent / 'alerts.log'
            
            if alert_file.exists():
                try:
                    with open(alert_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        
                    # 获取最近的告警
                    for line in lines[-10:]:  # 最近10条告警
                        try:
                            alert_data = json.loads(line.strip())
                            alerts.append({
                                'timestamp': alert_data['timestamp'],
                                'severity': alert_data['severity'],
                                'rule_name': alert_data['rule_name'],
                                'message': alert_data['message']
                            })
                        except json.JSONDecodeError:
                            continue
                            
                except Exception as e:
                    print(f"读取告警文件失败: {e}")
            
            return jsonify(alerts)
    
    def run(self, debug=False):
        """启动仪表板"""
        if not FLASK_AVAILABLE:
            print("❌ Flask未安装，无法启动仪表板")
            return
        
        print(f"🚀 启动监控仪表板: http://{self.host}:{self.port}")
        self.app.run(host=self.host, port=self.port, debug=debug)


# HTML模板
DASHBOARD_HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DDCrud批量操作监控仪表板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        .status-excellent { color: #28a745; }
        .status-good { color: #17a2b8; }
        .status-degraded { color: #ffc107; }
        .status-poor { color: #dc3545; }
        .status-critical { color: #dc3545; }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .operations-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .success { color: #28a745; }
        .failure { color: #dc3545; }
        .alerts-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        .alert-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .alert-high { border-left-color: #dc3545; background-color: #f8d7da; }
        .alert-medium { border-left-color: #ffc107; background-color: #fff3cd; }
        .alert-low { border-left-color: #17a2b8; background-color: #d1ecf1; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 DDCrud批量操作监控仪表板</h1>
        <p>实时监控批量操作性能，确保553倍性能提升效果</p>
    </div>

    <div class="metrics-grid">
        <div class="metric-card">
            <div class="metric-label">总操作数</div>
            <div class="metric-value" id="total-operations">-</div>
        </div>
        <div class="metric-card">
            <div class="metric-label">成功率</div>
            <div class="metric-value" id="success-rate">-</div>
        </div>
        <div class="metric-card">
            <div class="metric-label">平均处理速度</div>
            <div class="metric-value" id="avg-speed">-</div>
            <div class="metric-label">条/秒</div>
        </div>
        <div class="metric-card">
            <div class="metric-label">性能状态</div>
            <div class="metric-value" id="performance-status">-</div>
        </div>
    </div>

    <div class="chart-container">
        <h3>📊 性能趋势图</h3>
        <canvas id="performanceChart" width="400" height="200"></canvas>
    </div>

    <div class="operations-table">
        <h3 style="padding: 20px 20px 0 20px;">📋 最近操作记录</h3>
        <table>
            <thead>
                <tr>
                    <th>时间</th>
                    <th>操作名称</th>
                    <th>记录数</th>
                    <th>执行时间(秒)</th>
                    <th>处理速度(条/秒)</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody id="operations-tbody">
            </tbody>
        </table>
    </div>

    <div class="alerts-section">
        <h3>🚨 最近告警</h3>
        <div id="alerts-container">
            <p>暂无告警信息</p>
        </div>
    </div>

    <script>
        let performanceChart;

        // 初始化图表
        function initChart() {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '处理速度 (条/秒)',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 更新指标
        async function updateMetrics() {
            try {
                const response = await fetch('/api/metrics?minutes=60');
                const data = await response.json();
                
                document.getElementById('total-operations').textContent = data.total_operations;
                document.getElementById('success-rate').textContent = 
                    (data.success_rate * 100).toFixed(1) + '%';
                document.getElementById('avg-speed').textContent = 
                    data.average_records_per_second.toFixed(1);
                
                const statusElement = document.getElementById('performance-status');
                statusElement.textContent = data.performance_status;
                statusElement.className = 'metric-value status-' + data.performance_status;
                
            } catch (error) {
                console.error('更新指标失败:', error);
            }
        }

        // 更新图表
        async function updateChart() {
            try {
                const response = await fetch('/api/performance_chart?minutes=60');
                const data = await response.json();
                
                performanceChart.data.labels = data.timestamps;
                performanceChart.data.datasets[0].data = data.records_per_second;
                performanceChart.update();
                
            } catch (error) {
                console.error('更新图表失败:', error);
            }
        }

        // 更新操作记录
        async function updateOperations() {
            try {
                const response = await fetch('/api/recent_operations?minutes=60');
                const operations = await response.json();
                
                const tbody = document.getElementById('operations-tbody');
                tbody.innerHTML = '';
                
                operations.forEach(op => {
                    const row = tbody.insertRow();
                    row.innerHTML = `
                        <td>${op.timestamp}</td>
                        <td>${op.operation_name}</td>
                        <td>${op.record_count}</td>
                        <td>${op.execution_time}</td>
                        <td>${op.records_per_second}</td>
                        <td class="${op.success ? 'success' : 'failure'}">
                            ${op.success ? '✅ 成功' : '❌ 失败'}
                        </td>
                    `;
                });
                
            } catch (error) {
                console.error('更新操作记录失败:', error);
            }
        }

        // 更新告警
        async function updateAlerts() {
            try {
                const response = await fetch('/api/alerts');
                const alerts = await response.json();
                
                const container = document.getElementById('alerts-container');
                
                if (alerts.length === 0) {
                    container.innerHTML = '<p>✅ 暂无告警信息</p>';
                } else {
                    container.innerHTML = '';
                    alerts.forEach(alert => {
                        const alertDiv = document.createElement('div');
                        alertDiv.className = `alert-item alert-${alert.severity}`;
                        alertDiv.innerHTML = `
                            <strong>${alert.rule_name}</strong> - ${alert.message}
                            <br><small>${new Date(alert.timestamp).toLocaleString()}</small>
                        `;
                        container.appendChild(alertDiv);
                    });
                }
                
            } catch (error) {
                console.error('更新告警失败:', error);
            }
        }

        // 初始化和定时更新
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            updateMetrics();
            updateChart();
            updateOperations();
            updateAlerts();
            
            // 每30秒更新一次
            setInterval(() => {
                updateMetrics();
                updateChart();
                updateOperations();
                updateAlerts();
            }, 30000);
        });
    </script>
</body>
</html>
'''


# 命令行启动脚本
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='DDCrud批量操作监控仪表板')
    parser.add_argument('--host', default='localhost', help='服务器地址')
    parser.add_argument('--port', type=int, default=5000, help='端口号')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    dashboard = MonitoringDashboard(host=args.host, port=args.port)
    dashboard.run(debug=args.debug)

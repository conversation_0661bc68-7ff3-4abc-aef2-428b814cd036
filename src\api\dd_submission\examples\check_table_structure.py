#!/usr/bin/env python3
"""
检查具体表的字段结构
"""

import asyncio
import logging
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '../../..')
sys.path.insert(0, src_dir)

from service import get_client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def check_table_structure():
    """检查表结构"""
    try:
        # 获取MySQL客户端
        mysql_client = await get_client('database.rdbs.mysql')
        
        # 检查dd_submission_data表结构
        logger.info("🔍 检查dd_submission_data表结构")
        desc_sql = "DESCRIBE dd_submission_data"
        desc_result = await mysql_client.afetch_all(desc_sql)
        
        if hasattr(desc_result, 'data'):
            columns = desc_result.data
        else:
            columns = desc_result
        
        logger.info(f"📊 dd_submission_data表字段 ({len(columns)}个):")
        for col in columns:
            if isinstance(col, dict):
                field_name = col.get('Field', 'Unknown')
                field_type = col.get('Type', 'Unknown')
                logger.info(f"  - {field_name}: {field_type}")
        
        # 检查biz_dd_pre_distribution表结构
        logger.info("\n🔍 检查biz_dd_pre_distribution表结构")
        desc_sql = "DESCRIBE biz_dd_pre_distribution"
        desc_result = await mysql_client.afetch_all(desc_sql)
        
        if hasattr(desc_result, 'data'):
            columns = desc_result.data
        else:
            columns = desc_result
        
        logger.info(f"📊 biz_dd_pre_distribution表字段 ({len(columns)}个):")
        for col in columns:
            if isinstance(col, dict):
                field_name = col.get('Field', 'Unknown')
                field_type = col.get('Type', 'Unknown')
                logger.info(f"  - {field_name}: {field_type}")
        
        # 检查biz_dd_post_distribution表结构
        logger.info("\n🔍 检查biz_dd_post_distribution表结构")
        desc_sql = "DESCRIBE biz_dd_post_distribution"
        desc_result = await mysql_client.afetch_all(desc_sql)
        
        if hasattr(desc_result, 'data'):
            columns = desc_result.data
        else:
            columns = desc_result
        
        logger.info(f"📊 biz_dd_post_distribution表字段 ({len(columns)}个):")
        for col in columns:
            if isinstance(col, dict):
                field_name = col.get('Field', 'Unknown')
                field_type = col.get('Type', 'Unknown')
                logger.info(f"  - {field_name}: {field_type}")
        
    except Exception as e:
        logger.error(f"❌ 检查失败: {e}")


if __name__ == "__main__":
    asyncio.run(check_table_structure())

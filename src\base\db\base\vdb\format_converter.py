"""
向量数据库格式转换器

提供各种向量数据库格式与统一标准格式之间的转换功能。
支持Milvus、PGVector、Chroma等数据库的格式转换。

作者: HSBC Knowledge Team
日期: 2025-01-15
"""

from typing import Any, Dict, List, Optional, Union
import logging

from .models import (
    Entity, SearchResult, AnnSearchRequest, QueryRequest, GetRequest,
    InsertRequest, DeleteRequest, FieldSchema, CollectionSchema
)
from .exceptions import DataValidationError

logger = logging.getLogger(__name__)


class FormatConverter:
    """格式转换器基类"""
    
    @staticmethod
    def to_standard_entity(data: Dict[str, Any]) -> Entity:
        """转换为标准实体格式"""
        return Entity(data=data)
    
    @staticmethod
    def from_standard_entity(entity: Entity) -> Dict[str, Any]:
        """从标准实体格式转换"""
        return entity.to_dict()
    
    @staticmethod
    def to_standard_search_result(id_val: Any, distance: float, 
                                 entity_data: Dict[str, Any]) -> SearchResult:
        """转换为标准搜索结果格式"""
        entity = Entity(data=entity_data)
        return SearchResult(id=id_val, distance=distance, entity=entity)
    
    @staticmethod
    def from_standard_search_result(result: SearchResult) -> Dict[str, Any]:
        """从标准搜索结果格式转换"""
        return result.to_dict()


class MilvusConverter(FormatConverter):
    """Milvus格式转换器"""
    
    @staticmethod
    def to_milvus_search_result(result: SearchResult) -> Dict[str, Any]:
        """转换为Milvus搜索结果格式"""
        return {
            "id": result.id,
            "distance": result.distance,
            "entity": result.entity.to_dict()
        }
    
    @staticmethod
    def from_milvus_search_result(milvus_result: Dict[str, Any]) -> SearchResult:
        """从Milvus搜索结果格式转换"""
        return SearchResult(
            id=milvus_result["id"],
            distance=milvus_result["distance"],
            entity=Entity(data=milvus_result.get("entity", {}))
        )
    
    @staticmethod
    def to_milvus_search_params(request: AnnSearchRequest) -> Dict[str, Any]:
        """转换为Milvus搜索参数"""
        return {
            "collection_name": None,  # 需要外部提供
            "data": request.data,
            "anns_field": request.anns_field,
            "param": request.param,
            "limit": request.limit,
            "expr": request.expr,
            "output_fields": None  # 需要外部提供
        }
    
    @staticmethod
    def to_milvus_query_params(request: QueryRequest) -> Dict[str, Any]:
        """转换为Milvus查询参数"""
        params = {
            "expr": request.expr
        }
        if request.output_fields:
            params["output_fields"] = request.output_fields
        if request.limit is not None:
            params["limit"] = request.limit
        if request.offset is not None:
            params["offset"] = request.offset
        return params


class PGVectorConverter(FormatConverter):
    """PGVector格式转换器"""
    
    @staticmethod
    def to_pgvector_search_result(result: SearchResult) -> Dict[str, Any]:
        """转换为PGVector搜索结果格式"""
        # PGVector使用与标准格式相同的结构
        return result.to_dict()
    
    @staticmethod
    def from_pgvector_search_result(pg_result: Dict[str, Any]) -> SearchResult:
        """从PGVector搜索结果格式转换"""
        return SearchResult(
            id=pg_result["id"],
            distance=pg_result["distance"],
            entity=Entity(data=pg_result.get("entity", {}))
        )
    
    @staticmethod
    def to_pgvector_filter(expr: Optional[str]) -> Optional[Dict[str, Any]]:
        """将表达式转换为PGVector过滤条件"""
        if not expr:
            return None
        
        # 简单的表达式解析（可以扩展为更复杂的解析器）
        try:
            # 处理简单的等值条件：field = 'value'
            if " = " in expr:
                field, value = expr.split(" = ", 1)
                field = field.strip()
                value = value.strip().strip("'\"")
                return {field: value}
            
            # 处理IN条件：field IN ['value1', 'value2']
            if " IN " in expr:
                field, values_str = expr.split(" IN ", 1)
                field = field.strip()
                # 简单解析列表格式
                values_str = values_str.strip().strip("[]()").replace("'", "").replace('"', "")
                values = [v.strip() for v in values_str.split(",")]
                return {field: values}
            
            # 其他复杂条件暂时不支持，返回原始表达式
            logger.warning(f"复杂表达式暂不支持自动转换: {expr}")
            return {"_raw_expr": expr}
            
        except Exception as e:
            logger.error(f"表达式解析失败: {expr}, 错误: {e}")
            return {"_raw_expr": expr}
    
    @staticmethod
    def from_pgvector_filter(filters: Optional[Dict[str, Any]]) -> Optional[str]:
        """将PGVector过滤条件转换为表达式"""
        if not filters:
            return None
        
        conditions = []
        for field, value in filters.items():
            if field == "_raw_expr":
                return value
            
            if isinstance(value, list):
                # IN条件
                value_str = ", ".join([f"'{v}'" for v in value])
                conditions.append(f"{field} IN [{value_str}]")
            else:
                # 等值条件
                conditions.append(f"{field} = '{value}'")
        
        return " AND ".join(conditions)


class ChromaConverter(FormatConverter):
    """Chroma格式转换器"""
    
    @staticmethod
    def to_chroma_search_result(result: SearchResult) -> Dict[str, Any]:
        """转换为Chroma搜索结果格式"""
        # Chroma使用不同的结构
        return {
            "ids": [result.id],
            "distances": [result.distance],
            "metadatas": [result.entity.to_dict()],
            "documents": [result.entity.get("document", "")]
        }
    
    @staticmethod
    def from_chroma_search_result(chroma_result: Dict[str, Any], index: int = 0) -> SearchResult:
        """从Chroma搜索结果格式转换"""
        entity_data = {}
        
        # 提取元数据
        if "metadatas" in chroma_result and chroma_result["metadatas"]:
            entity_data.update(chroma_result["metadatas"][index] or {})
        
        # 提取文档内容
        if "documents" in chroma_result and chroma_result["documents"]:
            entity_data["document"] = chroma_result["documents"][index]
        
        return SearchResult(
            id=chroma_result["ids"][index],
            distance=chroma_result["distances"][index],
            entity=Entity(data=entity_data)
        )
    
    @staticmethod
    def to_chroma_query_params(request: QueryRequest) -> Dict[str, Any]:
        """转换为Chroma查询参数"""
        # Chroma使用where条件而不是表达式
        return {
            "where": PGVectorConverter.to_pgvector_filter(request.expr),
            "n_results": request.limit or 10
        }


class UniversalConverter:
    """通用格式转换器"""
    
    def __init__(self, db_type: str):
        """
        初始化转换器
        
        Args:
            db_type: 数据库类型 ('milvus', 'pgvector', 'chroma')
        """
        self.db_type = db_type.lower()
        
        if self.db_type == "milvus":
            self.converter = MilvusConverter()
        elif self.db_type == "pgvector":
            self.converter = PGVectorConverter()
        elif self.db_type == "chroma":
            self.converter = ChromaConverter()
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")
    
    def to_standard_results(self, raw_results: List[Dict[str, Any]]) -> List[SearchResult]:
        """将原始结果转换为标准格式"""
        standard_results = []
        
        for result in raw_results:
            try:
                if self.db_type == "milvus":
                    standard_result = MilvusConverter.from_milvus_search_result(result)
                elif self.db_type == "pgvector":
                    standard_result = PGVectorConverter.from_pgvector_search_result(result)
                elif self.db_type == "chroma":
                    # Chroma需要特殊处理，因为它返回的是批量格式
                    standard_result = ChromaConverter.from_chroma_search_result(result, 0)
                else:
                    # 默认处理
                    standard_result = FormatConverter.to_standard_search_result(
                        result.get("id"), result.get("distance", 0.0), 
                        result.get("entity", {})
                    )
                
                standard_results.append(standard_result)
                
            except Exception as e:
                logger.error(f"结果转换失败: {result}, 错误: {e}")
                continue
        
        return standard_results
    
    def from_standard_results(self, results: List[SearchResult]) -> List[Dict[str, Any]]:
        """将标准格式转换为目标数据库格式"""
        converted_results = []
        
        for result in results:
            try:
                if self.db_type == "milvus":
                    converted_result = MilvusConverter.to_milvus_search_result(result)
                elif self.db_type == "pgvector":
                    converted_result = PGVectorConverter.to_pgvector_search_result(result)
                elif self.db_type == "chroma":
                    converted_result = ChromaConverter.to_chroma_search_result(result)
                else:
                    converted_result = result.to_dict()
                
                converted_results.append(converted_result)
                
            except Exception as e:
                logger.error(f"结果转换失败: {result}, 错误: {e}")
                continue
        
        return converted_results
    
    def convert_search_params(self, request: AnnSearchRequest) -> Dict[str, Any]:
        """转换搜索参数"""
        if self.db_type == "milvus":
            return MilvusConverter.to_milvus_search_params(request)
        elif self.db_type == "pgvector":
            # PGVector使用自定义参数格式
            return {
                "query_vector": request.data[0] if request.data else [],
                "vector_field": request.anns_field,
                "top_k": request.limit,
                "filters": PGVectorConverter.to_pgvector_filter(request.expr),
                "metric_type": request.param.get("metric_type", "COSINE")
            }
        elif self.db_type == "chroma":
            return {
                "query_embeddings": request.data,
                "n_results": request.limit,
                "where": PGVectorConverter.to_pgvector_filter(request.expr)
            }
        else:
            return request.to_dict()


# ==================== 便捷函数 ====================

def create_converter(db_type: str) -> UniversalConverter:
    """创建通用转换器"""
    return UniversalConverter(db_type)


def convert_to_standard_format(raw_results: List[Dict[str, Any]], 
                              db_type: str) -> List[SearchResult]:
    """将原始结果转换为标准格式"""
    converter = create_converter(db_type)
    return converter.to_standard_results(raw_results)


def convert_from_standard_format(results: List[SearchResult], 
                                db_type: str) -> List[Dict[str, Any]]:
    """将标准格式转换为目标格式"""
    converter = create_converter(db_type)
    return converter.from_standard_results(results)

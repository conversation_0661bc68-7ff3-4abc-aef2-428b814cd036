"""
创建完整的金融领域测试数据（高性能批量化版本）

使用指定的knowledge_id创建结构化的金融领域测试数据，包括：
- 3个源数据库（银行收支、行业投资、股票债券）
- 每个数据库20个表（共60个表）
- 每个表20个字段（共1200个字段）
- 相关的码值集和码值数据
- 表间关联关系数据
- 字段与码值集的关联关系

使用高性能批量化API进行数据创建，大幅提升创建效率。
"""

import asyncio
import logging
import time
import sys
import os
from typing import Dict, List, Any, Tuple
import random

# 添加项目根目录到sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

from service import get_client
from modules.knowledge.metadata.crud import MetadataCrud

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 指定的knowledge_id
TARGET_KNOWLEDGE_ID = "9edb2ffa-41a9-4112-bf4b-4b7cd1a089c9"

# 金融领域数据结构定义
FINANCIAL_DATABASES = {
    "bank_transaction_db": {
        "name": "银行收支明细数据库",
        "desc": "包含各银行的收支、转账、贷款等明细数据，支持全面的银行业务分析",
        "layer": "ods",
        "tables": [
            "bank_account_info", "bank_transfer_details", "bank_deposit_records", "bank_withdrawal_records",
            "bank_loan_applications", "bank_loan_repayments", "bank_credit_card_transactions", "bank_savings_accounts",
            "bank_checking_accounts", "bank_investment_accounts", "bank_mortgage_details", "bank_personal_loans",
            "bank_business_loans", "bank_overdraft_records", "bank_interest_calculations", "bank_fee_structures",
            "bank_customer_profiles", "bank_branch_operations", "bank_atm_transactions", "bank_online_banking_logs"
        ]
    },
    "industry_investment_db": {
        "name": "行业投资数据库",
        "desc": "涵盖体育、农业、制造业、科技等各行业投资明细数据，提供行业投资分析支持",
        "layer": "bdm",
        "tables": [
            "sports_industry_investments", "agriculture_investment_records", "manufacturing_investments", "technology_sector_investments",
            "healthcare_industry_funding", "education_sector_investments", "real_estate_investments", "energy_sector_funding",
            "transportation_investments", "retail_industry_funding", "financial_services_investments", "telecommunications_funding",
            "entertainment_industry_investments", "food_beverage_investments", "automotive_industry_funding", "aerospace_investments",
            "biotechnology_funding", "renewable_energy_investments", "mining_industry_investments", "construction_sector_funding"
        ]
    },
    "securities_bonds_db": {
        "name": "股票债券数据库",
        "desc": "包含股票、债券、基金等各类金融产品的交易和持仓数据，支持投资组合分析",
        "layer": "ads", 
        "tables": [
            "stock_daily_prices", "stock_trading_volumes", "stock_market_indices", "stock_dividend_records",
            "corporate_bonds_info", "government_bonds_data", "municipal_bonds_records", "bond_yield_curves",
            "mutual_fund_nav", "etf_trading_data", "hedge_fund_performance", "pension_fund_holdings",
            "stock_options_data", "futures_contracts", "commodity_prices", "currency_exchange_rates",
            "derivatives_trading", "portfolio_allocations", "risk_metrics", "performance_analytics"
        ]
    }
}

# 金融字段定义模板
FINANCIAL_FIELD_TEMPLATES = {
    "common": [
        ("record_id", "NUMBER", "记录唯一标识符"),
        ("created_time", "DATE", "记录创建时间"),
        ("updated_time", "DATE", "记录更新时间"),
        ("status", "STRING", "记录状态（有效/无效）"),
        ("data_source", "STRING", "数据来源系统")
    ],
    "bank": [
        ("account_number", "STRING", "银行账户号码"),
        ("customer_id", "NUMBER", "客户唯一标识"),
        ("transaction_amount", "NUMBER", "交易金额"),
        ("transaction_type", "STRING", "交易类型"),
        ("currency_code", "STRING", "货币代码"),
        ("bank_code", "STRING", "银行机构代码"),
        ("branch_code", "STRING", "分行代码"),
        ("transaction_date", "DATE", "交易日期"),
        ("balance_after", "NUMBER", "交易后余额"),
        ("interest_rate", "NUMBER", "利率"),
        ("fee_amount", "NUMBER", "手续费金额"),
        ("reference_number", "STRING", "交易参考号"),
        ("counterparty_account", "STRING", "对方账户"),
        ("transaction_channel", "STRING", "交易渠道"),
        ("risk_level", "STRING", "风险等级")
    ],
    "investment": [
        ("investment_id", "NUMBER", "投资项目标识"),
        ("industry_code", "STRING", "行业分类代码"),
        ("investment_amount", "NUMBER", "投资金额"),
        ("investment_date", "DATE", "投资日期"),
        ("investor_type", "STRING", "投资者类型"),
        ("investment_stage", "STRING", "投资阶段"),
        ("expected_return_rate", "NUMBER", "预期收益率"),
        ("actual_return_rate", "NUMBER", "实际收益率"),
        ("investment_period", "NUMBER", "投资期限（月）"),
        ("risk_rating", "STRING", "风险评级"),
        ("project_name", "STRING", "项目名称"),
        ("geographic_region", "STRING", "地理区域"),
        ("investment_strategy", "STRING", "投资策略"),
        ("exit_strategy", "STRING", "退出策略"),
        ("performance_metrics", "STRING", "绩效指标")
    ],
    "securities": [
        ("security_code", "STRING", "证券代码"),
        ("security_name", "STRING", "证券名称"),
        ("market_code", "STRING", "市场代码"),
        ("trading_date", "DATE", "交易日期"),
        ("opening_price", "NUMBER", "开盘价"),
        ("closing_price", "NUMBER", "收盘价"),
        ("highest_price", "NUMBER", "最高价"),
        ("lowest_price", "NUMBER", "最低价"),
        ("trading_volume", "NUMBER", "交易量"),
        ("trading_value", "NUMBER", "交易金额"),
        ("market_cap", "NUMBER", "市值"),
        ("pe_ratio", "NUMBER", "市盈率"),
        ("dividend_yield", "NUMBER", "股息率"),
        ("beta_coefficient", "NUMBER", "贝塔系数"),
        ("volatility", "NUMBER", "波动率")
    ]
}

# 码值集定义
CODE_SETS = {
    "bank_types": {
        "name": "银行类型分类",
        "desc": "银行机构类型分类码值集",
        "values": [
            ("COMMERCIAL", "商业银行"), ("INVESTMENT", "投资银行"), ("CENTRAL", "中央银行"),
            ("COOPERATIVE", "合作银行"), ("SAVINGS", "储蓄银行"), ("DEVELOPMENT", "开发银行")
        ]
    },
    "currency_codes": {
        "name": "货币代码",
        "desc": "国际标准货币代码",
        "values": [
            ("CNY", "人民币"), ("USD", "美元"), ("EUR", "欧元"), ("JPY", "日元"),
            ("GBP", "英镑"), ("HKD", "港币"), ("SGD", "新加坡元"), ("AUD", "澳元")
        ]
    },
    "transaction_types": {
        "name": "交易类型",
        "desc": "银行交易类型分类",
        "values": [
            ("DEPOSIT", "存款"), ("WITHDRAWAL", "取款"), ("TRANSFER", "转账"), ("PAYMENT", "付款"),
            ("LOAN", "放贷"), ("REPAYMENT", "还款"), ("INTEREST", "利息"), ("FEE", "手续费")
        ]
    },
    "industry_sectors": {
        "name": "行业分类",
        "desc": "投资行业分类码值集",
        "values": [
            ("TECH", "科技行业"), ("HEALTHCARE", "医疗健康"), ("FINANCE", "金融服务"), ("ENERGY", "能源行业"),
            ("MANUFACTURING", "制造业"), ("RETAIL", "零售业"), ("REAL_ESTATE", "房地产"), ("AGRICULTURE", "农业"),
            ("TRANSPORTATION", "交通运输"), ("EDUCATION", "教育行业"), ("ENTERTAINMENT", "娱乐业"), ("SPORTS", "体育产业")
        ]
    },
    "investment_stages": {
        "name": "投资阶段",
        "desc": "投资项目阶段分类",
        "values": [
            ("SEED", "种子轮"), ("ANGEL", "天使轮"), ("SERIES_A", "A轮"), ("SERIES_B", "B轮"),
            ("SERIES_C", "C轮"), ("PRE_IPO", "Pre-IPO"), ("IPO", "首次公开募股"), ("MATURE", "成熟期")
        ]
    },
    "risk_levels": {
        "name": "风险等级",
        "desc": "投资风险等级分类",
        "values": [
            ("LOW", "低风险"), ("MEDIUM_LOW", "中低风险"), ("MEDIUM", "中等风险"),
            ("MEDIUM_HIGH", "中高风险"), ("HIGH", "高风险"), ("VERY_HIGH", "极高风险")
        ]
    },
    "security_types": {
        "name": "证券类型",
        "desc": "金融证券产品类型分类",
        "values": [
            ("STOCK", "股票"), ("BOND", "债券"), ("FUND", "基金"), ("ETF", "交易型开放式指数基金"),
            ("OPTION", "期权"), ("FUTURE", "期货"), ("WARRANT", "权证"), ("DERIVATIVE", "衍生品")
        ]
    },
    "market_codes": {
        "name": "交易市场代码",
        "desc": "证券交易市场代码",
        "values": [
            ("SSE", "上海证券交易所"), ("SZSE", "深圳证券交易所"), ("HKEX", "香港交易所"),
            ("NYSE", "纽约证券交易所"), ("NASDAQ", "纳斯达克"), ("LSE", "伦敦证券交易所")
        ]
    }
}


async def setup_clients():
    """设置客户端连接"""
    print("🔧 设置客户端连接")
    print("-" * 50)
    
    try:
        # 获取数据库客户端
        rdb_client = await get_client("database.rdbs.mysql")
        print("   ✅ 获取MySQL客户端成功")
        
        # 获取向量客户端
        try:
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
            print("   ✅ 获取向量客户端成功")
        except:
            vdb_client = None
            embedding_client = None
            print("   ⚠️  向量客户端未配置，将跳过向量化")
        
        # 创建MetadataCrud实例
        metadata_crud = MetadataCrud(rdb_client, vdb_client, embedding_client)
        
        return metadata_crud
        
    except Exception as e:
        logger.error(f"设置客户端失败: {e}")
        raise


async def batch_create_databases(metadata_crud: MetadataCrud) -> Dict[str, int]:
    """批量创建金融数据库"""
    print("\n📊 批量创建金融数据库")
    print("-" * 50)

    # 准备批量创建数据
    databases_data = []
    for db_key, db_info in FINANCIAL_DATABASES.items():
        db_data = {
            'knowledge_id': TARGET_KNOWLEDGE_ID,
            'db_name': db_key,
            'db_name_cn': db_info['name'],
            'data_layer': db_info['layer'],
            'db_desc': db_info['desc'],
            'is_active': True
        }
        databases_data.append(db_data)

    try:
        # 使用批量创建API
        db_ids, vector_results = await metadata_crud.batch_create_source_databases(databases_data)
        
        # 构建返回的映射
        database_ids = {}
        for i, db_key in enumerate(FINANCIAL_DATABASES.keys()):
            if i < len(db_ids):
                database_ids[db_key] = db_ids[i]
                print(f"   ✅ 创建数据库 {FINANCIAL_DATABASES[db_key]['name']}: {db_ids[i]}")
            else:
                logger.error(f"数据库创建失败: {db_key}")
                
        print(f"\n   📊 批量创建完成: {len(database_ids)} 个数据库")
        return database_ids
        
    except Exception as e:
        logger.error(f"批量创建数据库失败: {e}")
        raise


async def batch_create_tables(metadata_crud: MetadataCrud, database_ids: Dict[str, int]) -> Dict[str, List[int]]:
    """批量创建金融数据表"""
    print("\n📋 批量创建金融数据表")
    print("-" * 50)

    # 准备所有表的数据
    all_tables_data = []
    table_mapping = []  # 记录表数据与数据库的对应关系
    
    for db_key, db_info in FINANCIAL_DATABASES.items():
        db_id = database_ids[db_key]
        
        for table_name in db_info['tables']:
            table_desc = generate_table_description(table_name, db_key)
            table_name_cn = generate_table_chinese_name(table_name, db_key)

            table_data = {
                'knowledge_id': TARGET_KNOWLEDGE_ID,
                'db_id': db_id,
                'table_name': table_name,
                'table_name_cn': table_name_cn,
                'table_desc': table_desc,
                'is_active': True
            }
            all_tables_data.append(table_data)
            table_mapping.append((db_key, table_name))

    # 批量创建表（分批处理，每批20个）
    table_ids = {db_key: [] for db_key in FINANCIAL_DATABASES.keys()}
    batch_size = 20
    
    for i in range(0, len(all_tables_data), batch_size):
        batch_data = all_tables_data[i:i+batch_size]
        batch_mapping = table_mapping[i:i+batch_size]
        
        try:
            # 使用底层批量插入API
            result = await metadata_crud.rdb_client.abatch_insert(
                table="md_source_tables",
                data=batch_data,
                batch_size=batch_size
            )
            
            if not result.success:
                raise Exception(f"批量插入表失败: {result.error}")
                
            # 获取插入的ID
            for j, (db_key, table_name) in enumerate(batch_mapping):
                table_data = batch_data[j]
                # 使用get方法查询
                existing_table = await metadata_crud.get_source_table(
                    knowledge_id=table_data['knowledge_id'],
                    db_id=table_data['db_id'],
                    table_name=table_data['table_name']
                )
                
                if existing_table:
                    table_id = existing_table.get('table_id', 0)
                    table_ids[db_key].append(table_id)
                    print(f"   ✅ 创建表 {table_name}: {table_id}")
                else:
                    table_ids[db_key].append(0)
                    logger.error(f"未找到插入的表记录: {table_name}")
                    
        except Exception as e:
            logger.error(f"批量创建表失败（批次 {i//batch_size + 1}）: {e}")
            raise

    print(f"\n   📊 批量创建完成: {sum(len(tables) for tables in table_ids.values())} 个表")
    return table_ids


async def batch_create_columns(metadata_crud: MetadataCrud, table_ids: Dict[str, List[int]]) -> int:
    """批量创建金融数据字段"""
    print("\n📝 批量创建金融数据字段")
    print("-" * 50)

    all_columns_data = []
    total_columns = 0

    for db_key, db_table_ids in table_ids.items():
        db_info = FINANCIAL_DATABASES[db_key]
        
        # 确定字段模板类型
        if "bank" in db_key:
            field_type = "bank"
        elif "investment" in db_key:
            field_type = "investment"
        elif "securities" in db_key or "bonds" in db_key:
            field_type = "securities"
        else:
            field_type = "common"

        for i, table_id in enumerate(db_table_ids):
            if table_id == 0:
                continue
                
            table_name = db_info['tables'][i]
            fields = generate_table_fields(table_name, field_type)

            for j, (field_name, data_type, field_desc) in enumerate(fields[:20], 1):
                field_name_cn = generate_field_chinese_name(field_name, field_type)

                column_data = {
                    'knowledge_id': TARGET_KNOWLEDGE_ID,
                    'table_id': table_id,
                    'column_name': field_name,
                    'column_name_cn': field_name_cn,
                    'column_desc': field_desc,
                    'data_type': data_type,
                    'is_primary_key': j == 1  # 第一个字段作为主键
                }
                all_columns_data.append(column_data)

    # 批量创建字段（分批处理，每批50个）
    batch_size = 50
    
    for i in range(0, len(all_columns_data), batch_size):
        batch_data = all_columns_data[i:i+batch_size]
        
        try:
            # 使用底层批量插入API
            result = await metadata_crud.rdb_client.abatch_insert(
                table="md_source_columns",
                data=batch_data,
                batch_size=batch_size
            )
            
            if not result.success:
                raise Exception(f"批量插入字段失败: {result.error}")
                
            total_columns += len(batch_data)
            if i == 0:  # 只显示第一批的详情
                print(f"   ✅ 批量创建字段（第1批）: {len(batch_data)} 个")
            elif i % (batch_size * 5) == 0:  # 每5批显示一次进度
                print(f"   ✅ 批量创建字段（第{i//batch_size + 1}批）: 累计 {total_columns} 个")
                
        except Exception as e:
            logger.error(f"批量创建字段失败（批次 {i//batch_size + 1}）: {e}")
            raise

    print(f"\n   📊 批量创建完成: {total_columns} 个字段")
    return total_columns


async def batch_create_code_sets_and_values(metadata_crud: MetadataCrud) -> Tuple[Dict[str, int], int]:
    """批量创建码值集和码值"""
    print("\n🏷️  批量创建码值集和码值")
    print("-" * 50)
    
    code_set_ids = {}
    total_code_values = 0
    
    # 批量创建码值集
    code_sets_data = []
    for code_set_key, code_set_info in CODE_SETS.items():
        code_set_data = {
            'knowledge_id': TARGET_KNOWLEDGE_ID,
            'code_set_name': code_set_key,
            'code_set_type': 'ENUM',
            'code_set_desc': f"{code_set_info['name']} - {code_set_info['desc']}",
            'is_active': True
        }
        code_sets_data.append(code_set_data)

    # 批量创建码值集
    try:
        result = await metadata_crud.rdb_client.abatch_insert(
            table="md_reference_code_set",
            data=code_sets_data,
            batch_size=len(code_sets_data)
        )
        
        if not result.success:
            raise Exception(f"批量插入码值集失败: {result.error}")
            
        # 获取插入的码值集ID
        for code_set_key, code_set_info in CODE_SETS.items():
            existing_code_set = await metadata_crud.get_code_set(
                knowledge_id=TARGET_KNOWLEDGE_ID,
                code_set_name=code_set_key
            )
            if existing_code_set:
                code_set_id = existing_code_set.get('id', 0)
                code_set_ids[code_set_key] = code_set_id
                print(f"   ✅ 创建码值集 {code_set_info['name']}: {code_set_id}")
            else:
                logger.error(f"未找到插入的码值集记录: {code_set_key}")
                
    except Exception as e:
        logger.error(f"批量创建码值集失败: {e}")
        raise

    # 批量创建码值
    all_code_values_data = []
    for code_set_key, code_set_info in CODE_SETS.items():
        if code_set_key not in code_set_ids:
            continue
            
        code_set_id = code_set_ids[code_set_key]
        for code_value, code_desc in code_set_info['values']:
            code_value_data = {
                'knowledge_id': TARGET_KNOWLEDGE_ID,
                'code_set_id': code_set_id,
                'code_value': code_value,
                'code_desc': code_desc,
                'code_value_cn': code_desc,  # 添加中文名称字段，与描述相同
                'is_active': True
            }
            all_code_values_data.append(code_value_data)
    
    # 批量插入码值
    try:
        result = await metadata_crud.rdb_client.abatch_insert(
            table="md_reference_code_value",
            data=all_code_values_data,
            batch_size=len(all_code_values_data)
        )
        
        if not result.success:
            raise Exception(f"批量插入码值失败: {result.error}")
            
        total_code_values = len(all_code_values_data)
        print(f"   ✅ 批量创建码值: {total_code_values} 个")
        
    except Exception as e:
        logger.error(f"批量创建码值失败: {e}")
        raise
    
    return code_set_ids, total_code_values


async def batch_create_key_relations(metadata_crud: MetadataCrud, table_ids: Dict[str, List[int]]) -> int:
    """批量创建表间关联关系（大幅增强版）"""
    print("\n🔗 批量创建表间关联关系（增强版）")
    print("-" * 50)
    
    # 收集所有字段ID和字段信息
    db_column_mapping = {}  # 记录字段属于哪个数据库
    table_column_mapping = {}  # 记录字段属于哪个表
    column_info_mapping = {}  # 记录字段的详细信息
    
    for db_key, db_table_ids in table_ids.items():
        db_column_mapping[db_key] = []
        for table_id in db_table_ids:
            if table_id == 0:
                continue
                
            table_column_mapping[table_id] = []
            
            # 查询该表的所有字段
            columns = await metadata_crud.list_source_columns(
                knowledge_id=TARGET_KNOWLEDGE_ID,
                table_id=table_id
            )
            
            for column in columns:
                column_id = column.get('column_id', 0)
                if column_id:
                    db_column_mapping[db_key].append(column_id)
                    table_column_mapping[table_id].append(column_id)
                    column_info_mapping[column_id] = {
                        'column_name': column.get('column_name', ''),
                        'table_id': table_id,
                        'db_key': db_key,
                        'data_type': column.get('data_type', '')
                    }

    # 生成表间关联关系
    relations_data = []
    
    # 1. 跨数据库的主要关联关系
    bank_columns = db_column_mapping.get('bank_transaction_db', [])
    investment_columns = db_column_mapping.get('industry_investment_db', [])
    securities_columns = db_column_mapping.get('securities_bonds_db', [])
    
    # 1.1 银行-投资关联（基于业务逻辑）
    if bank_columns and investment_columns:
        # 客户ID关联
        bank_customer_cols = [col for col in bank_columns if 'customer' in column_info_mapping[col]['column_name']]
        investment_customer_cols = [col for col in investment_columns if 'customer' in column_info_mapping[col]['column_name']]
        
        for bank_col in bank_customer_cols[:3]:
            for inv_col in investment_customer_cols[:2]:
                relations_data.append({
                    'knowledge_id': TARGET_KNOWLEDGE_ID,
                    'source_column_id': bank_col,
                    'target_column_id': inv_col,
                    'relation_type': 'FK',
                    'comment': '银行客户与投资客户的关联关系'
                })
        
        # 金额字段关联
        bank_amount_cols = [col for col in bank_columns if 'amount' in column_info_mapping[col]['column_name']]
        investment_amount_cols = [col for col in investment_columns if 'amount' in column_info_mapping[col]['column_name']]
        
        for bank_col in bank_amount_cols[:2]:
            for inv_col in investment_amount_cols[:2]:
                relations_data.append({
                    'knowledge_id': TARGET_KNOWLEDGE_ID,
                    'source_column_id': bank_col,
                    'target_column_id': inv_col,
                    'relation_type': 'REF',
                    'comment': '银行交易金额与投资金额的参考关系'
                })

    # 1.2 投资-证券关联
    if investment_columns and securities_columns:
        # 投资项目与证券代码关联
        investment_id_cols = [col for col in investment_columns if 'investment_id' in column_info_mapping[col]['column_name']]
        security_code_cols = [col for col in securities_columns if 'security_code' in column_info_mapping[col]['column_name']]
        
        for inv_col in investment_id_cols[:2]:
            for sec_col in security_code_cols[:3]:
                relations_data.append({
                    'knowledge_id': TARGET_KNOWLEDGE_ID,
                    'source_column_id': inv_col,
                    'target_column_id': sec_col,
                    'relation_type': 'FK',
                    'comment': '投资项目与证券产品的关联关系'
                })

        # 日期字段关联
        investment_date_cols = [col for col in investment_columns if 'date' in column_info_mapping[col]['column_name']]
        securities_date_cols = [col for col in securities_columns if 'date' in column_info_mapping[col]['column_name']]
        
        for inv_col in investment_date_cols[:2]:
            for sec_col in securities_date_cols[:2]:
                relations_data.append({
                    'knowledge_id': TARGET_KNOWLEDGE_ID,
                    'source_column_id': inv_col,
                    'target_column_id': sec_col,
                    'relation_type': 'REF',
                    'comment': '投资日期与交易日期的参考关系'
                })

    # 1.3 银行-证券直接关联
    if bank_columns and securities_columns:
        # 账户号与证券代码关联
        bank_account_cols = [col for col in bank_columns if 'account' in column_info_mapping[col]['column_name']]
        security_code_cols = [col for col in securities_columns if 'code' in column_info_mapping[col]['column_name']]
        
        for bank_col in bank_account_cols[:2]:
            for sec_col in security_code_cols[:2]:
                relations_data.append({
                    'knowledge_id': TARGET_KNOWLEDGE_ID,
                    'source_column_id': bank_col,
                    'target_column_id': sec_col,
                    'relation_type': 'REF',
                    'comment': '银行账户与证券产品的参考关系'
                })

    # 2. 同一数据库内的表间关联关系
    for db_key, db_table_ids in table_ids.items():
        if len(db_table_ids) < 2:
            continue
            
        # 为每个数据库内的表创建关联
        for i, table1_id in enumerate(db_table_ids):
            if table1_id == 0:
                continue
            for j, table2_id in enumerate(db_table_ids[i+1:], i+1):
                if table2_id == 0:
                    continue
                    
                table1_cols = table_column_mapping.get(table1_id, [])
                table2_cols = table_column_mapping.get(table2_id, [])
                
                if not table1_cols or not table2_cols:
                    continue
                
                # 基于字段名的智能匹配
                for col1 in table1_cols:
                    col1_name = column_info_mapping[col1]['column_name']
                    for col2 in table2_cols:
                        col2_name = column_info_mapping[col2]['column_name']
                        
                        # 如果字段名相似或有共同的业务含义，创建关联
                        if (col1_name == col2_name and col1_name in ['customer_id', 'transaction_date', 'currency_code', 'status']):
                            relations_data.append({
                                'knowledge_id': TARGET_KNOWLEDGE_ID,
                                'source_column_id': col1,
                                'target_column_id': col2,
                                'relation_type': 'FK',
                                'comment': f'同库表间{col1_name}字段关联'
                            })
                        elif ('id' in col1_name and 'id' in col2_name and 
                              col1_name != col2_name and len(relations_data) < 50):
                            relations_data.append({
                                'knowledge_id': TARGET_KNOWLEDGE_ID,
                                'source_column_id': col1,
                                'target_column_id': col2,
                                'relation_type': 'REF',
                                'comment': f'ID字段参考关联：{col1_name}-{col2_name}'
                            })

    # 3. 基于业务逻辑的特殊关联
    # 记录ID字段与其他表的关联
    for db_key in db_column_mapping:
        record_id_cols = [col for col in db_column_mapping[db_key] 
                         if column_info_mapping[col]['column_name'] == 'record_id']
        other_cols = [col for col in db_column_mapping[db_key] 
                     if column_info_mapping[col]['column_name'] != 'record_id']
        
        # 为每个record_id创建一些关联
        for record_col in record_id_cols[:2]:
            for other_col in other_cols[:3]:
                if len(relations_data) < 80:  # 控制总数量
                    relations_data.append({
                        'knowledge_id': TARGET_KNOWLEDGE_ID,
                        'source_column_id': record_col,
                        'target_column_id': other_col,
                        'relation_type': 'REF',
                        'comment': '记录ID与业务字段的参考关联'
                    })

    # 使用批量创建API
    if relations_data:
        try:
            # 分批创建，避免一次性插入过多数据
            batch_size = 20
            total_created = 0
            
            for i in range(0, len(relations_data), batch_size):
                batch_data = relations_data[i:i+batch_size]
                relation_ids, vector_results = await metadata_crud.relations.batch_create_source_key_relations(batch_data)
                total_created += len(relation_ids)
                print(f"   ✅ 批量创建关联关系（第{i//batch_size + 1}批）: {len(relation_ids)} 个")
            
            print(f"   📊 总计创建关联关系: {total_created} 个")
            return total_created
        except Exception as e:
            logger.error(f"批量创建关联关系失败: {e}")
            return 0
    else:
        print("   ⚠️  没有生成关联关系数据")
        return 0


async def batch_create_code_relations(metadata_crud: MetadataCrud, table_ids: Dict[str, List[int]], 
                                     code_set_ids: Dict[str, int]) -> int:
    """批量创建字段与码值集的关联关系（大幅增强版）"""
    print("\n🔗 批量创建字段码值关联关系（增强版）")
    print("-" * 50)
    
    # 扩展的字段与码值集映射关系
    field_code_mapping = {
        # 精确匹配
        'currency_code': 'currency_codes',
        'transaction_type': 'transaction_types', 
        'bank_code': 'bank_types',
        'industry_code': 'industry_sectors',
        'investment_stage': 'investment_stages',
        'risk_level': 'risk_levels',
        'risk_rating': 'risk_levels',
        'security_code': 'security_types',
        'market_code': 'market_codes',
        
        # 模糊匹配 - 包含关键词的字段
        'currency': 'currency_codes',
        'transaction': 'transaction_types',
        'bank': 'bank_types',
        'industry': 'industry_sectors',
        'stage': 'investment_stages',
        'risk': 'risk_levels',
        'security': 'security_types',
        'market': 'market_codes',
        
        # 业务逻辑映射
        'investor_type': 'industry_sectors',  # 投资者类型可以关联行业分类
        'investment_strategy': 'investment_stages',  # 投资策略关联阶段
        'transaction_channel': 'bank_types',  # 交易渠道关联银行类型
        'account_type': 'bank_types',  # 账户类型关联银行类型
        'security_name': 'security_types',  # 证券名称关联证券类型
    }
    
    # 多对多关系映射 - 某些字段可以关联多个码值集
    multi_mapping = {
        'status': ['bank_types', 'investment_stages'],  # 状态字段可以关联多个业务域
        'data_source': ['bank_types', 'industry_sectors', 'security_types'],  # 数据来源可以关联多个域
        'type': ['bank_types', 'security_types', 'industry_sectors'],  # 类型字段通用关联
    }
    
    relations_data = []
    processed_pairs = set()  # 避免重复关联
    
    # 遍历所有表，查找匹配的字段
    for db_key, db_table_ids in table_ids.items():
        for table_id in db_table_ids:
            if table_id == 0:
                continue
                
            # 查询该表的所有字段
            columns = await metadata_crud.list_source_columns(
                knowledge_id=TARGET_KNOWLEDGE_ID,
                table_id=table_id
            )
            
            for column in columns:
                column_name = column.get('column_name', '').lower()
                column_id = column.get('column_id', 0)
                
                # 1. 精确和模糊匹配
                for field_pattern, code_set_key in field_code_mapping.items():
                    if field_pattern in column_name and code_set_key in code_set_ids:
                        pair_key = (column_id, code_set_ids[code_set_key])
                        if pair_key not in processed_pairs:
                            relations_data.append({
                                'knowledge_id': TARGET_KNOWLEDGE_ID,
                                'column_id': column_id,
                                'code_set_id': code_set_ids[code_set_key],
                                'column_type': 'source',
                                'comment': f'字段{column.get("column_name", "")}关联码值集{code_set_key}'
                            })
                            processed_pairs.add(pair_key)
                
                # 2. 多对多关系处理
                for field_pattern, code_set_keys in multi_mapping.items():
                    if field_pattern in column_name:
                        for code_set_key in code_set_keys:
                            if code_set_key in code_set_ids:
                                pair_key = (column_id, code_set_ids[code_set_key])
                                if pair_key not in processed_pairs:
                                    relations_data.append({
                                        'knowledge_id': TARGET_KNOWLEDGE_ID,
                                        'column_id': column_id,
                                        'code_set_id': code_set_ids[code_set_key],
                                        'column_type': 'source',
                                        'comment': f'字段{column.get("column_name", "")}多域关联码值集{code_set_key}'
                                    })
                                    processed_pairs.add(pair_key)
                
                # 3. 基于数据库类型的智能关联
                db_type_mapping = {
                    'bank_transaction_db': ['bank_types', 'currency_codes', 'transaction_types'],
                    'industry_investment_db': ['industry_sectors', 'investment_stages', 'risk_levels'],
                    'securities_bonds_db': ['security_types', 'market_codes', 'risk_levels']
                }
                
                if db_key in db_type_mapping:
                    for code_set_key in db_type_mapping[db_key]:
                        if code_set_key in code_set_ids:
                            # 为每个数据库的前5个字段建立关联
                            if columns.index(column) < 5:
                                pair_key = (column_id, code_set_ids[code_set_key])
                                if pair_key not in processed_pairs:
                                    relations_data.append({
                                        'knowledge_id': TARGET_KNOWLEDGE_ID,
                                        'column_id': column_id,
                                        'code_set_id': code_set_ids[code_set_key],
                                        'column_type': 'source',
                                        'comment': f'数据库{db_key}字段{column.get("column_name", "")}关联码值集{code_set_key}'
                                    })
                                    processed_pairs.add(pair_key)
                
                # 4. 字段数据类型关联
                data_type = column.get('data_type', '').upper()
                if data_type == 'STRING':
                    # STRING类型字段更可能关联枚举类码值集
                    for code_set_key in ['bank_types', 'transaction_types', 'security_types', 'industry_sectors']:
                        if code_set_key in code_set_ids and len(relations_data) < 150:
                            pair_key = (column_id, code_set_ids[code_set_key])
                            if pair_key not in processed_pairs:
                                relations_data.append({
                                    'knowledge_id': TARGET_KNOWLEDGE_ID,
                                    'column_id': column_id,
                                    'code_set_id': code_set_ids[code_set_key],
                                    'column_type': 'source',
                                    'comment': f'STRING类型字段{column.get("column_name", "")}关联码值集{code_set_key}'
                                })
                                processed_pairs.add(pair_key)
                                break  # 每个STRING字段只关联一个码值集

    # 5. 添加一些随机的交叉关联以增加数据复杂性
    all_columns = []
    for db_key, db_table_ids in table_ids.items():
        for table_id in db_table_ids:
            if table_id == 0:
                continue
            columns = await metadata_crud.list_source_columns(
                knowledge_id=TARGET_KNOWLEDGE_ID,
                table_id=table_id
            )
            all_columns.extend([(col.get('column_id', 0), col.get('column_name', '')) for col in columns])
    
    # 随机选择一些字段进行交叉关联
    import random
    random.shuffle(all_columns)
    available_code_sets = list(code_set_ids.items())
    
    for i, (column_id, column_name) in enumerate(all_columns[:20]):  # 只取前20个
        if column_id and len(relations_data) < 200:
            code_set_key, code_set_id = random.choice(available_code_sets)
            pair_key = (column_id, code_set_id)
            if pair_key not in processed_pairs:
                relations_data.append({
                    'knowledge_id': TARGET_KNOWLEDGE_ID,
                    'column_id': column_id,
                    'code_set_id': code_set_id,
                    'column_type': 'source',
                    'comment': f'交叉关联：字段{column_name}关联码值集{code_set_key}'
                })
                processed_pairs.add(pair_key)

    # 使用批量创建API
    if relations_data:
        try:
            # 分批创建，避免一次性插入过多数据
            batch_size = 25
            total_created = 0
            
            for i in range(0, len(relations_data), batch_size):
                 batch_data = relations_data[i:i+batch_size]
                 relation_ids = await metadata_crud.codes.batch_create_code_relations(batch_data)
                 total_created += len(relation_ids)
                 print(f"   ✅ 批量创建码值关联（第{i//batch_size + 1}批）: {len(relation_ids)} 个")
            
            print(f"   📊 总计创建码值关联: {total_created} 个")
            return total_created
        except Exception as e:
            logger.error(f"批量创建码值关联失败: {e}")
            return 0
    else:
        print("   ⚠️  没有生成码值关联数据")
        return 0


def generate_table_chinese_name(table_name: str, db_type: str) -> str:
    """根据表名和数据库类型生成中文表名"""
    chinese_names = {
        # 银行相关表中文名
        "bank_account_info": "银行账户信息表",
        "bank_transfer_details": "银行转账明细表",
        "bank_deposit_records": "银行存款记录表",
        "bank_withdrawal_records": "银行取款记录表",
        "bank_loan_applications": "银行贷款申请表",
        "bank_loan_repayments": "银行贷款还款表",
        "bank_credit_card_transactions": "银行信用卡交易表",
        "bank_savings_accounts": "银行储蓄账户表",
        "bank_checking_accounts": "银行支票账户表",
        "bank_investment_accounts": "银行投资账户表",
        "bank_mortgage_details": "银行抵押贷款明细表",
        "bank_personal_loans": "银行个人贷款表",
        "bank_business_loans": "银行企业贷款表",
        "bank_overdraft_records": "银行透支记录表",
        "bank_interest_calculations": "银行利息计算表",
        "bank_fee_structures": "银行费用结构表",
        "bank_customer_profiles": "银行客户档案表",
        "bank_branch_operations": "银行分行运营表",
        "bank_atm_transactions": "银行ATM交易表",
        "bank_online_banking_logs": "银行网上银行日志表",

        # 投资相关表中文名
        "sports_industry_investments": "体育产业投资表",
        "agriculture_investment_records": "农业投资记录表",
        "manufacturing_investments": "制造业投资表",
        "technology_sector_investments": "科技行业投资表",
        "healthcare_industry_funding": "医疗健康行业资金表",
        "education_sector_investments": "教育行业投资表",
        "real_estate_investments": "房地产投资表",
        "energy_sector_funding": "能源行业资金表",
        "transportation_investments": "交通运输投资表",
        "retail_industry_funding": "零售行业资金表",
        "financial_services_investments": "金融服务投资表",
        "telecommunications_funding": "电信行业资金表",
        "entertainment_industry_investments": "娱乐行业投资表",
        "food_beverage_investments": "食品饮料投资表",
        "automotive_industry_funding": "汽车行业资金表",
        "aerospace_investments": "航空航天投资表",
        "biotechnology_funding": "生物技术资金表",
        "renewable_energy_investments": "可再生能源投资表",
        "mining_industry_investments": "采矿业投资表",
        "construction_sector_funding": "建筑行业资金表",

        # 证券相关表中文名
        "stock_daily_prices": "股票日价格表",
        "stock_trading_volumes": "股票交易量表",
        "stock_market_indices": "股票市场指数表",
        "stock_dividend_records": "股票分红记录表",
        "corporate_bonds_info": "企业债券信息表",
        "government_bonds_data": "政府债券数据表",
        "municipal_bonds_records": "市政债券记录表",
        "bond_yield_curves": "债券收益率曲线表",
        "mutual_fund_nav": "共同基金净值表",
        "etf_trading_data": "ETF交易数据表",
        "hedge_fund_performance": "对冲基金业绩表",
        "pension_fund_holdings": "养老基金持仓表",
        "stock_options_data": "股票期权数据表",
        "futures_contracts": "期货合约表",
        "commodity_prices": "商品价格表",
        "currency_exchange_rates": "货币汇率表",
        "derivatives_trading": "衍生品交易表",
        "portfolio_allocations": "投资组合配置表",
        "risk_metrics": "风险指标表",
        "performance_analytics": "绩效分析表"
    }

    return chinese_names.get(table_name, f"{table_name}表")


def generate_table_description(table_name: str, db_type: str) -> str:
    """根据表名和数据库类型生成专业的表描述"""
    descriptions = {
        # 银行相关表描述
        "bank_account_info": "银行账户基本信息表，存储客户账户的详细信息和状态",
        "bank_transfer_details": "银行转账明细表，记录所有转账交易的详细信息",
        "bank_deposit_records": "银行存款记录表，追踪客户的存款交易历史",
        "bank_withdrawal_records": "银行取款记录表，记录客户的取款交易明细",
        "bank_loan_applications": "银行贷款申请表，存储客户贷款申请的详细信息",
        "bank_loan_repayments": "银行贷款还款表，追踪贷款的还款记录和状态",
        "bank_credit_card_transactions": "银行信用卡交易表，记录信用卡的所有交易明细",
        "bank_savings_accounts": "银行储蓄账户表，管理储蓄账户的信息和余额",
        "bank_checking_accounts": "银行支票账户表，存储支票账户的详细信息",
        "bank_investment_accounts": "银行投资账户表，管理客户的投资账户信息",
        "bank_mortgage_details": "银行抵押贷款明细表，记录房屋抵押贷款信息",
        "bank_personal_loans": "银行个人贷款表，存储个人贷款的详细信息",
        "bank_business_loans": "银行企业贷款表，管理企业贷款的相关信息",
        "bank_overdraft_records": "银行透支记录表，追踪账户透支的历史记录",
        "bank_interest_calculations": "银行利息计算表，存储利息计算的详细数据",
        "bank_fee_structures": "银行费用结构表，定义各种银行服务的收费标准",
        "bank_customer_profiles": "银行客户档案表，存储客户的基本信息和风险评级",
        "bank_branch_operations": "银行分行运营表，记录各分行的运营数据",
        "bank_atm_transactions": "银行ATM交易表，记录ATM机的所有交易明细",
        "bank_online_banking_logs": "银行网上银行日志表，追踪网银操作的详细记录",
        
        # 投资相关表描述
        "sports_industry_investments": "体育产业投资表，记录体育行业的投资项目和收益",
        "agriculture_investment_records": "农业投资记录表，追踪农业领域的投资活动",
        "manufacturing_investments": "制造业投资表，存储制造业投资项目的详细信息",
        "technology_sector_investments": "科技行业投资表，记录科技领域的投资和回报",
        "healthcare_industry_funding": "医疗健康行业资金表，管理医疗行业的投资资金",
        "education_sector_investments": "教育行业投资表，追踪教育领域的投资项目",
        "real_estate_investments": "房地产投资表，记录房地产投资的详细信息",
        "energy_sector_funding": "能源行业资金表，管理能源领域的投资资金",
        "transportation_investments": "交通运输投资表，存储交通行业的投资数据",
        "retail_industry_funding": "零售行业资金表，追踪零售业的投资活动",
        "financial_services_investments": "金融服务投资表，记录金融服务业的投资",
        "telecommunications_funding": "电信行业资金表，管理电信领域的投资",
        "entertainment_industry_investments": "娱乐行业投资表，存储娱乐业投资信息",
        "food_beverage_investments": "食品饮料投资表，记录食品行业的投资项目",
        "automotive_industry_funding": "汽车行业资金表，管理汽车产业的投资",
        "aerospace_investments": "航空航天投资表，追踪航空航天业的投资",
        "biotechnology_funding": "生物技术资金表，记录生物技术领域的投资",
        "renewable_energy_investments": "可再生能源投资表，管理清洁能源投资",
        "mining_industry_investments": "采矿业投资表，存储采矿行业的投资数据",
        "construction_sector_funding": "建筑行业资金表，追踪建筑业的投资活动",
        
        # 证券相关表描述
        "stock_daily_prices": "股票日价格表，记录股票每日的价格变动数据",
        "stock_trading_volumes": "股票交易量表，存储股票的交易量统计信息",
        "stock_market_indices": "股票市场指数表，追踪各种股票市场指数",
        "stock_dividend_records": "股票分红记录表，记录股票的分红派息信息",
        "corporate_bonds_info": "企业债券信息表，存储企业债券的详细信息",
        "government_bonds_data": "政府债券数据表，管理政府债券的相关数据",
        "municipal_bonds_records": "市政债券记录表，追踪市政债券的发行和交易",
        "bond_yield_curves": "债券收益率曲线表，记录债券收益率的变化",
        "mutual_fund_nav": "共同基金净值表，存储基金的净值变动数据",
        "etf_trading_data": "ETF交易数据表，记录ETF的交易信息",
        "hedge_fund_performance": "对冲基金业绩表，追踪对冲基金的表现",
        "pension_fund_holdings": "养老基金持仓表，管理养老基金的投资组合",
        "stock_options_data": "股票期权数据表，存储期权的交易和定价信息",
        "futures_contracts": "期货合约表，记录期货合约的详细信息",
        "commodity_prices": "商品价格表，追踪各种商品的价格变动",
        "currency_exchange_rates": "货币汇率表，记录各种货币的汇率信息",
        "derivatives_trading": "衍生品交易表，存储衍生品的交易数据",
        "portfolio_allocations": "投资组合配置表，管理投资组合的资产配置",
        "risk_metrics": "风险指标表，记录各种风险度量和评估数据",
        "performance_analytics": "绩效分析表，存储投资绩效的分析结果"
    }
    
    return descriptions.get(table_name, f"{table_name}表，存储相关业务数据")


def generate_table_fields(table_name: str, field_type: str) -> List[tuple]:
    """为表生成专业的字段定义"""
    # 基础字段（所有表都有）
    fields = FINANCIAL_FIELD_TEMPLATES["common"].copy()
    
    # 根据字段类型添加专业字段
    if field_type in FINANCIAL_FIELD_TEMPLATES:
        fields.extend(FINANCIAL_FIELD_TEMPLATES[field_type])
    
    # 根据表名添加特定字段
    specific_fields = get_table_specific_fields(table_name)
    fields.extend(specific_fields)
    
    return fields


def generate_field_chinese_name(field_name: str, field_type: str) -> str:
    """根据字段名和字段类型生成中文字段名"""
    chinese_names = {
        # 通用字段中文名
        "record_id": "记录标识",
        "created_time": "创建时间",
        "updated_time": "更新时间",
        "status": "状态",
        "data_source": "数据来源",

        # 银行字段中文名
        "account_number": "账户号码",
        "customer_id": "客户标识",
        "transaction_amount": "交易金额",
        "transaction_type": "交易类型",
        "currency_code": "货币代码",
        "bank_code": "银行代码",
        "branch_code": "分行代码",
        "transaction_date": "交易日期",
        "balance_after": "交易后余额",
        "interest_rate": "利率",
        "fee_amount": "手续费金额",
        "reference_number": "交易参考号",
        "counterparty_account": "对方账户",
        "transaction_channel": "交易渠道",
        "risk_level": "风险等级",
        "account_type": "账户类型",
        "account_status": "账户状态",
        "credit_limit": "信用额度",

        # 投资字段中文名
        "investment_id": "投资项目标识",
        "industry_code": "行业分类代码",
        "investment_amount": "投资金额",
        "investment_date": "投资日期",
        "investor_type": "投资者类型",
        "investment_stage": "投资阶段",
        "expected_return_rate": "预期收益率",
        "actual_return_rate": "实际收益率",
        "investment_period": "投资期限",
        "risk_rating": "风险评级",
        "project_name": "项目名称",
        "geographic_region": "地理区域",
        "investment_strategy": "投资策略",
        "exit_strategy": "退出策略",
        "performance_metrics": "绩效指标",
        "crop_type": "作物类型",
        "farming_method": "种植方式",
        "yield_expectation": "预期产量",

        # 证券字段中文名
        "security_code": "证券代码",
        "security_name": "证券名称",
        "market_code": "市场代码",
        "trading_date": "交易日期",
        "opening_price": "开盘价",
        "closing_price": "收盘价",
        "highest_price": "最高价",
        "lowest_price": "最低价",
        "trading_volume": "交易量",
        "trading_value": "交易金额",
        "market_cap": "市值",
        "pe_ratio": "市盈率",
        "dividend_yield": "股息率",
        "beta_coefficient": "贝塔系数",
        "volatility": "波动率",
        "adjusted_close": "复权收盘价",
        "price_change": "价格变动",
        "percentage_change": "涨跌幅"
    }

    return chinese_names.get(field_name, field_name)


def get_table_specific_fields(table_name: str) -> List[tuple]:
    """根据表名获取特定的字段定义"""
    specific_fields = {
        "bank_account_info": [
            ("account_type", "STRING", "账户类型"),
            ("account_status", "STRING", "账户状态"),
            ("credit_limit", "NUMBER", "信用额度")
        ],
        "stock_daily_prices": [
            ("adjusted_close", "NUMBER", "复权收盘价"),
            ("price_change", "NUMBER", "价格变动"),
            ("percentage_change", "NUMBER", "涨跌幅")
        ],
        "agriculture_investment_records": [
            ("crop_type", "STRING", "作物类型"),
            ("farming_method", "STRING", "种植方式"),
            ("yield_expectation", "NUMBER", "预期产量")
        ]
    }

    return specific_fields.get(table_name, [])


async def cleanup_existing_data(metadata_crud: MetadataCrud):
    """清理已存在的测试数据"""
    print("\n🧹 清理已存在的测试数据")
    print("-" * 50)

    try:
        # 查找并删除已存在的数据库
        for db_key in FINANCIAL_DATABASES.keys():
            existing_db = await metadata_crud.get_source_database(
                knowledge_id=TARGET_KNOWLEDGE_ID,
                db_name=db_key
            )
            if existing_db:
                db_id = existing_db['db_id']
                print(f"   🗑️  删除已存在的数据库: {db_key} (ID: {db_id})")
                await metadata_crud.delete_source_database(db_id)

        # 查找并删除已存在的码值集
        for code_set_key in CODE_SETS.keys():
            existing_code_set = await metadata_crud.get_code_set(
                knowledge_id=TARGET_KNOWLEDGE_ID,
                code_set_name=code_set_key
            )
            if existing_code_set:
                code_set_id = existing_code_set['id']
                print(f"   🗑️  删除已存在的码值集: {code_set_key} (ID: {code_set_id})")
                await metadata_crud.delete_code_set(code_set_id)

        print("   ✅ 数据清理完成")

    except Exception as e:
        print(f"   ⚠️  数据清理过程中出现错误: {e}")


async def main():
    """主函数：创建完整的金融领域测试数据（高性能批量化版本）"""
    print("🏦 创建完整的金融领域测试数据（高性能批量化版本）")
    print("=" * 80)
    print(f"目标知识库ID: {TARGET_KNOWLEDGE_ID}")
    print("=" * 80)

    start_time = time.time()

    try:
        # 1. 设置客户端
        metadata_crud = await setup_clients()

        # 2. 清理已存在的数据
        await cleanup_existing_data(metadata_crud)
        
        # 3. 批量创建数据库（3个）
        database_ids = await batch_create_databases(metadata_crud)

        # 4. 批量创建表（60个）
        table_ids = await batch_create_tables(metadata_crud, database_ids)

        # 5. 批量创建字段（1200个）
        total_columns = await batch_create_columns(metadata_crud, table_ids)

        # 6. 批量创建码值集和码值
        code_set_ids, total_code_values = await batch_create_code_sets_and_values(metadata_crud)
        
        # 7. 批量创建表间关联关系
        total_key_relations = await batch_create_key_relations(metadata_crud, table_ids)
        
        # 8. 批量创建字段码值关联关系
        total_code_relations = await batch_create_code_relations(metadata_crud, table_ids, code_set_ids)
        
        # 9. 统计和验证
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 80)
        print("🎉 金融领域测试数据创建完成！（高性能批量化版本）")
        print("=" * 80)
        
        print(f"\n📊 数据创建统计:")
        print(f"   - 数据库: {len(database_ids)} 个")
        print(f"   - 数据表: {sum(len(tables) for tables in table_ids.values())} 个")
        print(f"   - 数据字段: {total_columns} 个")
        print(f"   - 码值集: {len(CODE_SETS)} 个")
        print(f"   - 码值: {total_code_values} 个")
        print(f"   - 表间关联: {total_key_relations} 个")
        print(f"   - 码值关联: {total_code_relations} 个")
        print(f"   - 总耗时: {duration:.2f} 秒")
        
        print(f"\n🚀 性能提升:")
        estimated_single_time = (len(database_ids) * 0.5 + 
                                sum(len(tables) for tables in table_ids.values()) * 0.3 + 
                                total_columns * 0.1 + 
                                total_code_values * 0.1)
        if estimated_single_time > 0:
            improvement = estimated_single_time / duration
            print(f"   - 预估单个操作耗时: {estimated_single_time:.2f} 秒")
            print(f"   - 性能提升倍数: {improvement:.1f}x")
        
        print(f"\n🎯 数据结构:")
        for db_key, db_info in FINANCIAL_DATABASES.items():
            db_id = database_ids.get(db_key, 0)
            table_count = len(table_ids.get(db_key, []))
            print(f"   - {db_info['name']} (ID: {db_id}): {table_count} 个表")
        
        print(f"\n✅ 所有数据已成功关联到知识库: {TARGET_KNOWLEDGE_ID}")
        
        # 10. 提供测试建议和table_ids
        await provide_test_guidance(metadata_crud, database_ids, table_ids)
        
        return True
        
    except Exception as e:
        logger.error(f"创建金融领域测试数据失败: {e}")
        return False


async def provide_test_guidance(metadata_crud: MetadataCrud, database_ids: Dict[str, int], table_ids: Dict[str, List[int]]):
    """提供多表关联测试建议和相关table_ids"""
    print("\n" + "=" * 80)
    print("🧪 多表关联测试建议与指南")
    print("=" * 80)
    
    # 收集关键表的table_ids
    key_tables = {}
    
    # 银行核心表
    bank_db_id = database_ids.get('bank_transaction_db')
    bank_table_ids = table_ids.get('bank_transaction_db', [])
    if bank_table_ids:
        key_tables.update({
            'bank_account_info': bank_table_ids[0] if len(bank_table_ids) > 0 else None,
            'bank_transfer_details': bank_table_ids[1] if len(bank_table_ids) > 1 else None,
            'bank_customer_profiles': bank_table_ids[16] if len(bank_table_ids) > 16 else None,
        })
    
    # 投资核心表
    investment_db_id = database_ids.get('industry_investment_db')
    investment_table_ids = table_ids.get('industry_investment_db', [])
    if investment_table_ids:
        key_tables.update({
            'sports_industry_investments': investment_table_ids[0] if len(investment_table_ids) > 0 else None,
            'technology_sector_investments': investment_table_ids[3] if len(investment_table_ids) > 3 else None,
            'real_estate_investments': investment_table_ids[6] if len(investment_table_ids) > 6 else None,
        })
    
    # 证券核心表
    securities_db_id = database_ids.get('securities_bonds_db')
    securities_table_ids = table_ids.get('securities_bonds_db', [])
    if securities_table_ids:
        key_tables.update({
            'stock_daily_prices': securities_table_ids[0] if len(securities_table_ids) > 0 else None,
            'corporate_bonds_info': securities_table_ids[4] if len(securities_table_ids) > 4 else None,
            'portfolio_allocations': securities_table_ids[17] if len(securities_table_ids) > 17 else None,
        })
    
    print("📋 关键测试表Table IDs:")
    print("-" * 50)
    for table_name, table_id in key_tables.items():
        if table_id:
            print(f"   {table_name}: {table_id}")
    
    print(f"\n📊 数据库IDs:")
    print("-" * 30)
    for db_key, db_id in database_ids.items():
        print(f"   {db_key}: {db_id}")
    
    print(f"\n🎯 多表关联测试建议:")
    print("-" * 50)
    
    # 测试场景1：跨数据库关联查询
    print("1. 跨数据库关联查询测试:")
    print("   💡 问题示例:")
    print("      '银行客户的投资组合中有哪些证券产品？'")
    print("      '科技行业投资项目对应的股票交易情况如何？'")
    print("      '哪些银行账户参与了房地产投资？'")
    print(f"   🔧 相关table_ids: {key_tables.get('bank_customer_profiles')}, {key_tables.get('technology_sector_investments')}, {key_tables.get('stock_daily_prices')}")
    
    # 测试场景2：业务主题关联
    print("\n2. 业务主题关联分析测试:")
    print("   💡 问题示例:")
    print("      '客户风险等级与投资偏好的关联性分析'")
    print("      '不同交易渠道的投资回报率比较'")
    print("      '银行费用结构对投资决策的影响'")
    print(f"   🔧 相关table_ids: {key_tables.get('bank_account_info')}, {key_tables.get('sports_industry_investments')}, {key_tables.get('portfolio_allocations')}")
    
    # 测试场景3：时间序列关联
    print("\n3. 时间序列关联分析测试:")
    print("   💡 问题示例:")
    print("      '银行转账记录与同期股票价格变化的关联'")
    print("      '投资阶段变化与证券市场表现的时间关系'")
    print("      '月度银行交易量与季度投资回报的相关性'")
    print(f"   🔧 相关table_ids: {key_tables.get('bank_transfer_details')}, {key_tables.get('stock_daily_prices')}, {key_tables.get('real_estate_investments')}")
    
    # 测试场景4：码值关联查询
    print("\n4. 码值关联查询测试:")
    print("   💡 问题示例:")
    print("      '不同货币类型的交易在各投资阶段的分布'")
    print("      '各种银行类型客户的证券投资偏好'")
    print("      '风险等级与行业分类的交叉分析'")
    print("   🔧 利用码值集: currency_codes, bank_types, risk_levels, industry_sectors")
    
    # 测试场景5：复杂多表JOIN
    print("\n5. 复杂多表JOIN测试:")
    print("   💡 问题示例:")
    print("      '通过银行账户->客户档案->投资记录->证券交易的完整链路查询'")
    print("      '多维度客户画像：银行行为+投资偏好+证券持仓'")
    print("      '跨数据源的风险评估：银行信用+投资回报+市场波动'")
    
    # 提供具体的查询hint
    print(f"\n🔍 推荐测试查询Hints:")
    print("-" * 40)
    print("1. 使用knowledge_id作为基础过滤条件:")
    print(f"   WHERE knowledge_id = '{TARGET_KNOWLEDGE_ID}'")
    
    print(f"\n2. 关键关联字段:")
    print("   - customer_id: 客户维度关联")
    print("   - transaction_date/investment_date/trading_date: 时间维度关联")
    print("   - currency_code: 货币维度关联")
    print("   - risk_level/risk_rating: 风险维度关联")
    
    print(f"\n3. 分层查询策略:")
    print("   - 第一层：单库内表关联")
    print("   - 第二层：跨库表关联")  
    print("   - 第三层：码值关联增强")
    
    print(f"\n4. 性能测试建议:")
    print("   - 测试单表查询性能baseline")
    print("   - 测试2表JOIN性能")
    print("   - 测试3+表复杂JOIN性能")
    print("   - 测试码值关联查询性能")
    
    # 提供完整的测试SQL示例
    print(f"\n📝 示例SQL查询:")
    print("-" * 30)
    bank_customer_id = key_tables.get('bank_customer_profiles')
    tech_investment_id = key_tables.get('technology_sector_investments') 
    stock_price_id = key_tables.get('stock_daily_prices')
    
    sample_sql = f"""
-- 示例1: 跨数据库关联查询
SELECT 
    bc.customer_id,
    ti.investment_amount,
    sp.closing_price
FROM md_source_columns bc_cols
JOIN md_source_columns ti_cols ON bc_cols.column_name = 'customer_id' 
JOIN md_source_columns sp_cols ON ti_cols.table_id = {tech_investment_id}
WHERE bc_cols.table_id = {bank_customer_id}
  AND sp_cols.table_id = {stock_price_id}
  AND bc_cols.knowledge_id = '{TARGET_KNOWLEDGE_ID}';

-- 示例2: 码值关联查询  
SELECT 
    c.column_name,
    cs.code_set_name,
    cv.code_value,
    cv.code_value_cn
FROM md_source_columns c
JOIN md_reference_code_relation cr ON c.column_id = cr.column_id
JOIN md_reference_code_set cs ON cr.code_set_id = cs.id
JOIN md_reference_code_value cv ON cs.id = cv.code_set_id
WHERE c.knowledge_id = '{TARGET_KNOWLEDGE_ID}'
  AND cs.code_set_name IN ('bank_types', 'currency_codes')
LIMIT 10;
"""
    print(sample_sql)
    
    print("\n" + "=" * 80)
    print("🚀 测试数据已就绪！开始你的多表关联查询测试吧！")
    print("=" * 80)


if __name__ == "__main__":
    asyncio.run(main()) 
"""
元数据数据主题管理API路由

提供数据主题的完整CRUD操作接口，参照DD系统的设计模式。
"""

from fastapi import APIRouter, HTTPException, Query, Depends, Path
import logging

# 标准化日志导入
logger = logging.getLogger(__name__)

from api.knowledge.models.response_models import (
    ListResponse, CreateResponse, UpdateResponse, DeleteResponse, DetailResponse
)
from ..models import DataSubjectCreateRequest, DataSubjectUpdateRequest
from ..dependencies import get_metadata_crud, validate_knowledge_id, validate_pagination

# 创建路由器
router = APIRouter(tags=["元数据数据主题管理"], prefix="/data-subjects")


@router.post("/", response_model=CreateResponse, summary="创建数据主题")
async def create_data_subject(
    knowledge_id: str = Query(..., description="知识库ID"),
    request: DataSubjectCreateRequest = ...,
    metadata_crud = Depends(get_metadata_crud)
):
    """创建新数据主题"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        subject_data = request.model_dump()
        subject_id = await metadata_crud.create_data_subject(knowledge_id, subject_data)
        
        return CreateResponse(
            success=True,
            message="数据主题创建成功",
            data={"subject_id": subject_id, "knowledge_id": knowledge_id}
        )
    except Exception as e:
        logger.error(f"创建数据主题失败: {e}")
        raise HTTPException(status_code=400, detail=f"创建数据主题失败: {str(e)}")


@router.get("/{subject_id}", response_model=DetailResponse, summary="获取数据主题详情")
async def get_data_subject(
    subject_id: int = Path(..., description="数据主题ID"),
    knowledge_id: str = Query(..., description="知识库ID"),
    metadata_crud = Depends(get_metadata_crud)
):
    """获取数据主题详情"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        subject = await metadata_crud.get_data_subject(knowledge_id, subject_id)
        
        if not subject:
            raise HTTPException(status_code=404, detail=f"数据主题不存在: {subject_id}")
        
        return DetailResponse(
            success=True,
            message="获取数据主题详情成功",
            data=subject
        )
    except Exception as e:
        logger.error(f"获取数据主题详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据主题详情失败: {str(e)}")


@router.get("/", response_model=ListResponse, summary="查询数据主题列表")
async def list_data_subjects(
    knowledge_id: str = Query(..., description="知识库ID"),
    pagination = Depends(validate_pagination),
    metadata_crud = Depends(get_metadata_crud)
):
    """查询数据主题列表"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        page, page_size, offset = pagination
        
        subjects = await metadata_crud.list_data_subjects(knowledge_id)
        
        total = len(subjects)
        paginated_subjects = subjects[offset:offset + page_size]
        total_pages = (total + page_size - 1) // page_size
        
        return ListResponse(
            success=True,
            message="查询数据主题列表成功",
            data={
                "items": paginated_subjects,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }
        )
    except Exception as e:
        logger.error(f"查询数据主题列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询数据主题列表失败: {str(e)}")

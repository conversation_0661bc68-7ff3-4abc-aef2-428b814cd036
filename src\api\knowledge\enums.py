"""
API枚举定义模块
API Enums Definition Module

统一定义所有API中使用的枚举类型，确保在OpenAPI JSON中正确显示。
"""

from enum import Enum


# ==================== 基础枚举 ====================

class EntityType(str, Enum):
    """实体类型枚举"""
    SOURCE = "source"
    INDEX = "index"


class OperationType(str, Enum):
    """操作类型枚举"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"


# ==================== 元数据相关枚举 ====================

class TemplateType(str, Enum):
    """模板类型枚举"""
    SOURCE_DATABASE = "source_database"
    INDEX_DATABASE = "index_database"
    SOURCE_TABLES = "source_tables"
    INDEX_TABLES = "index_tables"
    SOURCE_COLUMNS = "source_columns"
    INDEX_COLUMNS = "index_columns"
    CODE_SET = "code_set"
    CODE_VALUE = "code_value"
    DATA_SUBJECT = "data_subject"
    DATA_SUBJECT_RELATION = "data_subject_relation"
    CODE_RELATION = "code_relation"
    COLUMN_RELATION = "column_relation"  # 列关联模板


class SearchScope(str, Enum):
    """搜索范围枚举"""
    GLOBAL = "global"
    CODE_SET = "code_set"


class ContentType(str, Enum):
    """内容类型枚举"""
    CODE = "code"
    TEXT = "text"
    BOTH = "code,text"


# ==================== DD相关枚举 ====================

class DDFieldCategory(str, Enum):
    """DD字段类别枚举"""
    REQUIREMENT = "A"  # 结果数据需求
    BUSINESS = "B"     # 业务解读
    TECHNICAL = "C"    # IT解读
    INDICATOR = "D"    # 指标解读


class DDCompletionStatus(str, Enum):
    """DD完成状态枚举"""
    INCOMPLETE = "incomplete"  # 未完成
    COMPLETE = "complete"      # 已完成
    VALIDATED = "validated"    # 已校验


class DDProcessingStatus(str, Enum):
    """DD处理状态枚举"""
    PENDING = "pending"        # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"          # 失败


class DDVersionStrategy(str, Enum):
    """DD版本策略枚举"""
    AUTO_UUID = "auto_uuid"           # 自动UUID版本（基于时间+随机）
    AUTO_TIMESTAMP = "auto_timestamp"  # 自动时间戳版本（精确到秒）
    AUTO_DATE = "auto_date"           # 自动日期版本（YYYYMMDD）
    MANUAL = "manual"                 # 手动指定版本
    HASH_BASED = "hash_based"         # 基于内容哈希的版本


class DDUploadMode(str, Enum):
    """DD上传模式枚举"""
    CREATE_NEW = "create_new"         # 创建新版本（默认）
    OVERWRITE = "overwrite"           # 覆盖现有版本
    MERGE = "merge"                   # 合并到现有版本
    FAIL_IF_EXISTS = "fail_if_exists" # 如果存在则失败


# ==================== 文档相关枚举 ====================

class DocumentType(str, Enum):
    """文档类型枚举"""
    INSIDE = "inside"   # 内部文档
    OUTSIDE = "outside" # 外部文档


class ParseType(str, Enum):
    """解析方式枚举"""
    CHUNK_NORMAL = "chunk_normal"   # 普通分块解析
    CHUNK_DETAIL = "chunk_detail"   # 详细分块解析


class DocumentStatus(str, Enum):
    """文档状态枚举"""
    PENDING = "pending"       # 待处理
    PROCESSING = "processing" # 处理中
    SUCCESS = "success"       # 处理成功
    FAILED = "failed"         # 处理失败


class InfoType(str, Enum):
    """信息类型枚举"""
    CONTENT = "content"     # 分块内容
    TITLE = "title"         # 标题
    KEYWORDS = "keywords"   # 关键词
    SUMMARY = "summary"     # 摘要
    METADATA = "metadata"   # 元数据


# ==================== 知识库相关枚举 ====================

class KnowledgeBaseType(str, Enum):
    """知识库类型枚举"""
    METADATA = "MetaData"  # 元数据类型
    DOC = "Doc"           # 文档类型
    DD = "DD"             # 数据字典类型


class ModelType(str, Enum):
    """模型类型枚举"""
    EMBEDDING = "embedding"  # 嵌入模型
    LLM = "llm"             # 大语言模型
    OTHER = "other"         # 其他模型


# ==================== 任务状态枚举 ====================

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"       # 待处理
    PROCESSING = "processing" # 处理中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"         # 失败


class UploadStatus(str, Enum):
    """上传状态枚举"""
    PENDING = "pending"       # 待上传
    UPLOADING = "uploading"   # 上传中
    SUCCESS = "success"       # 上传成功
    FAILED = "failed"         # 上传失败


# ==================== 工具函数 ====================

def get_enum_values(enum_class):
    """获取枚举类的所有值"""
    return [{"value": e.value, "name": e.name, "description": e.__doc__ or ""} for e in enum_class]


def get_all_enums():
    """获取所有枚举类的信息"""
    return {
        "entity_type": get_enum_values(EntityType),
        "operation_type": get_enum_values(OperationType),
        "template_type": get_enum_values(TemplateType),
        "search_scope": get_enum_values(SearchScope),
        "content_type": get_enum_values(ContentType),
        "dd_field_category": get_enum_values(DDFieldCategory),
        "dd_completion_status": get_enum_values(DDCompletionStatus),
        "dd_processing_status": get_enum_values(DDProcessingStatus),
        "dd_version_strategy": get_enum_values(DDVersionStrategy),
        "dd_upload_mode": get_enum_values(DDUploadMode),
        "document_type": get_enum_values(DocumentType),
        "parse_type": get_enum_values(ParseType),
        "document_status": get_enum_values(DocumentStatus),
        "info_type": get_enum_values(InfoType),
        "knowledge_base_type": get_enum_values(KnowledgeBaseType),
        "model_type": get_enum_values(ModelType),
        "task_status": get_enum_values(TaskStatus),
        "upload_status": get_enum_values(UploadStatus)
    }


# ==================== 验证函数 ====================

def validate_enum_value(enum_class, value):
    """验证枚举值是否有效"""
    try:
        enum_class(value)
        return True
    except ValueError:
        return False


def get_enum_choices(enum_class):
    """获取枚举类的选择列表（用于验证）"""
    return [e.value for e in enum_class]

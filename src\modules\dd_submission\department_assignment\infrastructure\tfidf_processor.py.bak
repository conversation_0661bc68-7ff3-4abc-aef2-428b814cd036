"""
TF-IDF处理器

基于scikit-learn的TfidfVectorizer实现，提供高效和准确的TF-IDF计算
"""

import numpy as np
import jieba
import logging
from typing import List, Dict, Any, Tuple, Optional
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

logger = logging.getLogger(__name__)

from .nlp_processor import NLPProcessor


class TFIDFProcessor:
    """TF-IDF处理器"""
    
    def __init__(self, nlp_processor: Optional[NLPProcessor] = None):
        """
        初始化TF-IDF处理器
        
        Args:
            nlp_processor: NLP处理器实例，如果为None则创建新实例
        """
        self.nlp_processor = nlp_processor or NLPProcessor()
        
        # 初始化TF-IDF向量化器
        self.vectorizer = TfidfVectorizer(
            tokenizer=self._jieba_tokenizer,
            lowercase=True,
            stop_words=None,  # 我们在tokenizer中处理停用词
            max_features=5000,  # 限制特征数量
            min_df=1,  # 最小文档频率
            max_df=0.95,  # 最大文档频率
            ngram_range=(1, 2),  # 支持1-2gram
            norm='l2',  # L2标准化
            use_idf=True,
            smooth_idf=True,
            sublinear_tf=True  # 使用sublinear TF scaling
        )
        
        # 缓存
        self._fitted = False
        self._document_vectors = None
        self._documents = None
        
    def _jieba_tokenizer(self, text: str) -> List[str]:
        """
        自定义jieba分词器，集成到sklearn中
        
        Args:
            text: 输入文本
            
        Returns:
            分词结果列表
        """
        try:
            # 使用NLP处理器进行分词和过滤
            keywords = self.nlp_processor.extract_keywords(text, max_keywords=50)
            return keywords
        except Exception as e:
            logger.error(f"jieba分词失败: {e}")
            return []
    
    def fit_transform(self, documents: List[str]) -> np.ndarray:
        """
        训练TF-IDF模型并转换文档
        
        Args:
            documents: 文档列表
            
        Returns:
            TF-IDF矩阵
        """
        try:
            logger.info(f"开始训练TF-IDF模型，文档数: {len(documents)}")
            
            # 过滤空文档
            valid_documents = [doc for doc in documents if doc and doc.strip()]
            if not valid_documents:
                logger.warning("没有有效文档用于TF-IDF训练")
                return np.array([])
            
            # 训练并转换
            tfidf_matrix = self.vectorizer.fit_transform(valid_documents)
            
            # 缓存结果
            self._fitted = True
            self._document_vectors = tfidf_matrix
            self._documents = valid_documents
            
            logger.info(f"TF-IDF模型训练完成，特征数: {tfidf_matrix.shape[1]}")
            
            return tfidf_matrix.toarray()
            
        except Exception as e:
            logger.error(f"TF-IDF模型训练失败: {e}")
            return np.array([])
    
    def transform(self, documents: List[str]) -> np.ndarray:
        """
        使用已训练的模型转换文档
        
        Args:
            documents: 文档列表
            
        Returns:
            TF-IDF矩阵
        """
        if not self._fitted:
            raise ValueError("TF-IDF模型尚未训练，请先调用fit_transform")
        
        try:
            tfidf_matrix = self.vectorizer.transform(documents)
            return tfidf_matrix.toarray()
        except Exception as e:
            logger.error(f"TF-IDF文档转换失败: {e}")
            return np.array([])
    
    def calculate_similarity(self, query: str, documents: List[str] = None) -> List[Tuple[int, float]]:
        """
        计算查询与文档的相似度
        
        Args:
            query: 查询文本
            documents: 文档列表，如果为None则使用训练时的文档
            
        Returns:
            (文档索引, 相似度分数)的列表，按相似度降序排列
        """
        try:
            if documents is None:
                if not self._fitted:
                    raise ValueError("TF-IDF模型尚未训练")
                document_vectors = self._document_vectors
            else:
                document_vectors = self.vectorizer.transform(documents)
            
            # 转换查询
            query_vector = self.vectorizer.transform([query])
            
            # 计算余弦相似度
            similarities = cosine_similarity(query_vector, document_vectors).flatten()
            
            # 排序并返回结果
            scored_indices = [(i, float(score)) for i, score in enumerate(similarities)]
            scored_indices.sort(key=lambda x: x[1], reverse=True)
            
            return scored_indices
            
        except Exception as e:
            logger.error(f"相似度计算失败: {e}")
            return []
    
    def get_feature_names(self) -> List[str]:
        """获取特征名称（词汇）"""
        if not self._fitted:
            return []
        
        try:
            return self.vectorizer.get_feature_names_out().tolist()
        except Exception as e:
            logger.error(f"获取特征名称失败: {e}")
            return []
    
    def get_top_features_for_document(self, doc_index: int, top_k: int = 10) -> List[Tuple[str, float]]:
        """
        获取文档的top-k特征词
        
        Args:
            doc_index: 文档索引
            top_k: 返回的特征数量
            
        Returns:
            (特征词, TF-IDF分数)的列表
        """
        if not self._fitted or self._document_vectors is None:
            return []
        
        try:
            if doc_index >= self._document_vectors.shape[0]:
                return []
            
            # 获取文档向量
            doc_vector = self._document_vectors[doc_index].toarray().flatten()
            
            # 获取特征名称
            feature_names = self.get_feature_names()
            
            # 获取top-k特征
            top_indices = np.argsort(doc_vector)[-top_k:][::-1]
            top_features = [(feature_names[i], doc_vector[i]) for i in top_indices if doc_vector[i] > 0]
            
            return top_features
            
        except Exception as e:
            logger.error(f"获取文档top特征失败: {e}")
            return []
    
    def calculate_enhanced_similarity(
        self, 
        query: str, 
        documents: List[str],
        query_keywords: Dict[str, List[str]] = None
    ) -> List[Tuple[int, float, Dict[str, Any]]]:
        """
        计算增强的相似度，结合TF-IDF和关键词匹配
        
        Args:
            query: 查询文本
            documents: 文档列表
            query_keywords: 查询关键词信息
            
        Returns:
            (文档索引, 综合分数, 详细信息)的列表
        """
        try:
            # 1. 计算TF-IDF相似度
            tfidf_similarities = self.calculate_similarity(query, documents)
            
            # 2. 计算关键词匹配分数
            keyword_scores = []
            if query_keywords:
                for i, doc in enumerate(documents):
                    keyword_score, matched_keywords = self._calculate_keyword_match_score(
                        doc, query_keywords
                    )
                    keyword_scores.append((keyword_score, matched_keywords))
            else:
                keyword_scores = [(0.0, []) for _ in documents]
            
            # 3. 综合计算分数
            results = []
            for i, (doc_idx, tfidf_score) in enumerate(tfidf_similarities):
                keyword_score, matched_keywords = keyword_scores[doc_idx]
                
                # 综合分数计算（可调整权重）
                tfidf_weight = 0.6
                keyword_weight = 0.4
                combined_score = tfidf_score * tfidf_weight + keyword_score * keyword_weight
                
                # 详细信息
                details = {
                    'tfidf_score': tfidf_score,
                    'keyword_score': keyword_score,
                    'matched_keywords': matched_keywords,
                    'combined_score': combined_score
                }
                
                results.append((doc_idx, combined_score, details))
            
            # 按综合分数排序
            results.sort(key=lambda x: x[1], reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"增强相似度计算失败: {e}")
            return []
    
    def _calculate_keyword_match_score(
        self, 
        document: str, 
        query_keywords: Dict[str, List[str]]
    ) -> Tuple[float, List[str]]:
        """
        计算关键词匹配分数（改进版）
        
        Args:
            document: 文档文本
            query_keywords: 查询关键词信息
            
        Returns:
            (匹配分数, 匹配的关键词列表)
        """
        try:
            # 提取文档关键词
            doc_keywords = set(self.nlp_processor.extract_keywords(document))
            
            # 获取查询关键词
            dr09_keywords = set(query_keywords.get('dr09_keywords', []))
            dr17_keywords = set(query_keywords.get('dr17_keywords', []))
            all_keywords = dr09_keywords | dr17_keywords
            
            if not all_keywords:
                return 0.0, []
            
            # 计算匹配
            dr09_matches = dr09_keywords & doc_keywords
            dr17_matches = dr17_keywords & doc_keywords
            
            # 计算加权分数
            dr09_score = len(dr09_matches) / len(dr09_keywords) if dr09_keywords else 0.0
            dr17_score = len(dr17_matches) / len(dr17_keywords) if dr17_keywords else 0.0
            
            # 权重：dr09更重要
            weighted_score = dr09_score * 0.7 + dr17_score * 0.3
            
            # 匹配的关键词
            matched_keywords = list(dr09_matches | dr17_matches)
            
            return weighted_score, matched_keywords
            
        except Exception as e:
            logger.error(f"关键词匹配分数计算失败: {e}")
            return 0.0, []
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if not self._fitted:
            return {"status": "not_fitted"}
        
        return {
            "status": "fitted",
            "n_documents": self._document_vectors.shape[0] if self._document_vectors is not None else 0,
            "n_features": self._document_vectors.shape[1] if self._document_vectors is not None else 0,
            "vectorizer_params": self.vectorizer.get_params()
        }

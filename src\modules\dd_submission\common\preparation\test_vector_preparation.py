"""
向量数据预填充测试脚本

测试向量数据预填充功能的基本可用性
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).resolve().parent
src_dir = current_dir.parent.parent.parent.parent
sys.path.insert(0, str(src_dir))

from vector_data_preparation import VectorDataPreparation

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_vector_preparation():
    """测试向量数据预填充功能"""
    logger.info("🧪 开始测试向量数据预填充功能")
    
    try:
        # 创建预填充实例
        preparation = VectorDataPreparation()
        
        # 测试客户端连接
        logger.info("🔧 测试客户端连接...")
        if not await preparation.setup_clients():
            logger.error("❌ 客户端连接失败")
            return
        
        logger.info("✅ 客户端连接成功")
        
        # 测试获取记录总数
        logger.info("📊 测试获取记录总数...")
        total_count = await preparation.get_submission_data_count()
        logger.info(f"   总记录数: {total_count}")
        
        if total_count == 0:
            logger.warning("⚠️ 没有符合条件的记录")
            return
        
        # 测试获取批量数据
        logger.info("📦 测试获取批量数据...")
        batch_data = await preparation.get_submission_data_batch(0, 3)
        logger.info(f"   获取到{len(batch_data)}条记录")
        
        if not batch_data:
            logger.warning("⚠️ 批量数据为空")
            return
        
        # 显示样本数据
        for i, record in enumerate(batch_data):
            dr09 = record.get('dr09', '')[:30]
            dr17 = record.get('dr17', '')[:30]
            logger.info(f"   样本{i+1}: dr09='{dr09}...', dr17='{dr17}...'")
        
        # 测试DDCrud向量化功能
        logger.info("🔤 测试DDCrud向量化功能...")
        test_dr09 = batch_data[0].get('dr09', '')
        test_dr17 = batch_data[0].get('dr17', '')
        test_knowledge_id = f"test_{batch_data[0].get('data_row_id', 1)}"
        test_data_row_id = batch_data[0].get('data_row_id', 1)
        test_data_layer = batch_data[0].get('dr01', 'ADS')

        if test_dr09 or test_dr17:
            dr09_success, dr17_success = await preparation.create_vectors_using_dd_crud(
                test_dr09, test_dr17, test_knowledge_id, test_data_row_id, test_data_layer
            )
            logger.info(f"   DDCrud向量化结果: dr09={dr09_success}, dr17={dr17_success}")
        else:
            logger.warning("⚠️ 没有可测试的文本内容")
        
        # 测试处理单条记录
        logger.info("🔄 测试处理单条记录...")
        test_record = batch_data[0]
        result = await preparation.process_single_record(test_record)
        
        logger.info(f"   处理结果:")
        logger.info(f"     data_row_id: {result['data_row_id']}")
        logger.info(f"     knowledge_id: {result['knowledge_id']}")
        logger.info(f"     dr09_success: {result['dr09_success']}")
        logger.info(f"     dr17_success: {result['dr17_success']}")
        logger.info(f"     errors: {result['errors']}")
        
        # 测试小批量处理
        logger.info("📦 测试小批量处理...")
        small_batch = batch_data[:2]  # 只处理前2条记录
        batch_results = await preparation.process_batch(small_batch)
        
        successful_count = len([r for r in batch_results if r['dr09_success'] or r['dr17_success']])
        logger.info(f"   批量处理结果: {successful_count}/{len(batch_results)}成功")
        
        logger.info("🎉 向量数据预填充功能测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'preparation' in locals():
            await preparation.cleanup()


async def test_small_batch_preparation():
    """测试小批量向量数据预填充"""
    logger.info("🧪 开始小批量向量数据预填充测试")
    
    try:
        preparation = VectorDataPreparation()
        
        # 运行小批量预填充（只处理前10条记录）
        await preparation.run_preparation(
            batch_size=5,
            max_records=10
        )
        
        logger.info("🎉 小批量向量数据预填充测试完成")
        
    except Exception as e:
        logger.error(f"❌ 小批量测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='向量数据预填充测试')
    parser.add_argument('--test-type', choices=['basic', 'small-batch'], default='basic',
                       help='测试类型: basic=基础功能测试, small-batch=小批量预填充测试')
    
    args = parser.parse_args()
    
    if args.test_type == 'basic':
        await test_vector_preparation()
    elif args.test_type == 'small-batch':
        await test_small_batch_preparation()


if __name__ == "__main__":
    asyncio.run(main())

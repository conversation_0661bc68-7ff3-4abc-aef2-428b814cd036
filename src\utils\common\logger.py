# -*- coding: utf-8 -*-
"""
全局Logger配置模块

重新设计的企业级日志系统，支持三种模式：
1. 企业级模式：基于标准logging，实现类似loguru的美观输出
2. 开发模式：使用loguru，北京时间，完整功能
3. 进阶企业模式：在企业级基础上支持structlog

使用方式：
方式1 - 便捷使用（推荐）：
    from utils.common.logger import setup_enterprise_logger
    logger = setup_enterprise_logger(level="DEBUG")
    logger.info("这样就能正常工作了")

方式2 - 分步配置（高级用法）：
    from utils.common.logger import setup_logger, get_logger
    config = setup_logger(mode="enterprise", level="DEBUG")
    logger = get_logger(__name__)
    logger.info("这样也能工作")

方式3 - 标准logging兼容：
    from utils.common.logger import get_logger
    logger = get_logger(__name__)  # 会自动使用默认配置
    logger.info("兼容标准logging使用方式")

"""

import os
import sys
import time
import logging
import logging.handlers
from typing import Optional, Dict, Any, Union
from enum import Enum
from pathlib import Path

# 第三方依赖（按需导入）
try:
    from loguru import logger as loguru_logger
    LOGURU_AVAILABLE = True
except ImportError:
    LOGURU_AVAILABLE = False
    loguru_logger = None

try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False
    structlog = None

from omegaconf import DictConfig


class LoggerMode(Enum):
    """日志模式枚举"""
    ENTERPRISE = "enterprise"          # 企业级模式：标准logging + 美观格式
    DEVELOPMENT = "development"        # 开发模式：loguru + 北京时间
    ENTERPRISE_PLUS = "enterprise_plus"  # 进阶企业模式：structlog + 标准logging


class LoggerConfig:
    """日志配置类"""
    
    def __init__(self):
        # 基础配置
        self.mode: LoggerMode = LoggerMode.ENTERPRISE
        self.level: str = "INFO"
        self.enable_file_logging: bool = True
        self.enable_console_logging: bool = True
        
        # 路径配置
        self.log_dir: Path = self._get_default_log_dir()
        self.log_filename: str = "application.log"
        
        # 格式配置
        self.console_format: Optional[str] = None
        self.file_format: Optional[str] = None
        
        # 文件轮转配置
        self.max_file_size: str = "10MB"
        self.backup_count: int = 5
        self.rotation_time: str = "1 day"
        
        # 性能配置
        self.enable_async: bool = True
        self.buffer_size: int = 1000
        
        # 企业级配置
        self.enable_json_format: bool = False
        self.include_caller_info: bool = True
        self.enable_exception_trace: bool = True
        
    def _get_default_log_dir(self) -> Path:
        """获取默认日志目录"""
        # 从当前文件位置推断项目根目录
        current_file = Path(__file__)
        # utils/common/logger.py -> src -> project_root
        project_root = current_file.parent.parent.parent.parent
        return project_root / "logs"


# 全局配置实例
_global_config: Optional[LoggerConfig] = None
_initialized: bool = False


# ============================================================================
# 时区设置
# ============================================================================

def _setup_timezone():
    """设置北京时间"""
    os.environ['TZ'] = 'Asia/Shanghai'
    if hasattr(time, 'tzset'):
        time.tzset()


# ============================================================================
# 企业级模式实现（基于标准logging）
# ============================================================================

class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器（企业级模式）"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def __init__(self, fmt=None, datefmt=None, use_colors=True):
        super().__init__(fmt, datefmt)
        self.use_colors = use_colors and self._supports_color()
    
    def _supports_color(self) -> bool:
        """检查终端是否支持颜色"""
        return hasattr(sys.stderr, 'isatty') and sys.stderr.isatty()
    
    def format(self, record):
        # 改进调用者信息显示
        if not hasattr(record, 'caller_info'):
            # 获取更清晰的模块名
            module_name = self._get_clear_module_name(record)
            record.caller_info = f"{module_name}:{record.funcName}:{record.lineno}"

        # 格式化基础消息
        formatted = super().format(record)

        # 添加颜色（如果支持）
        if self.use_colors:
            color = self.COLORS.get(record.levelname, '')
            reset = self.COLORS['RESET']
            # 只给级别名称和消息添加颜色
            formatted = formatted.replace(
                record.levelname,
                f"{color}{record.levelname}{reset}"
            )

        return formatted

    def _get_clear_module_name(self, record):
        """获取清晰的模块名"""
        module_name = record.name

        # 如果是__main__，尝试从文件路径获取更清晰的名称
        if module_name == "__main__":
            try:
                # 从pathname获取文件路径
                file_path = Path(record.pathname)

                # 尝试构建相对于src的路径
                try:
                    # 查找src目录
                    parts = file_path.parts
                    if 'src' in parts:
                        src_index = parts.index('src')
                        # 获取src之后的路径部分
                        relative_parts = parts[src_index + 1:]
                        if relative_parts:
                            # 移除.py扩展名
                            module_parts = list(relative_parts)
                            if module_parts[-1].endswith('.py'):
                                module_parts[-1] = module_parts[-1][:-3]
                            module_name = '.'.join(module_parts)
                except (ValueError, IndexError):
                    # 如果无法构建相对路径，使用文件名
                    module_name = file_path.stem

            except Exception:
                # 如果出错，保持原始名称
                pass

        return module_name


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器（进阶企业模式）"""
    
    def __init__(self, fmt=None, datefmt=None, enable_json=False):
        super().__init__(fmt, datefmt)
        self.enable_json = enable_json
    
    def format(self, record):
        if self.enable_json:
            import json
            log_data = {
                'timestamp': self.formatTime(record),
                'level': record.levelname,
                'logger': record.name,
                'function': record.funcName,
                'line': record.lineno,
                'message': record.getMessage(),
            }
            
            # 添加异常信息
            if record.exc_info:
                log_data['exception'] = self.formatException(record.exc_info)
            
            # 添加额外字段
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 
                              'pathname', 'filename', 'module', 'lineno', 
                              'funcName', 'created', 'msecs', 'relativeCreated',
                              'thread', 'threadName', 'processName', 'process',
                              'exc_info', 'exc_text', 'stack_info']:
                    log_data[key] = value
            
            return json.dumps(log_data, ensure_ascii=False)
        else:
            # 添加调用者信息
            if not hasattr(record, 'caller_info'):
                # 获取更清晰的模块名
                module_name = self._get_clear_module_name(record)
                record.caller_info = f"{module_name}:{record.funcName}:{record.lineno}"
            return super().format(record)

    def _get_clear_module_name(self, record):
        """获取清晰的模块名"""
        module_name = record.name

        # 如果是__main__，尝试从文件路径获取更清晰的名称
        if module_name == "__main__":
            try:
                # 从pathname获取文件路径
                file_path = Path(record.pathname)

                # 尝试构建相对于src的路径
                try:
                    # 查找src目录
                    parts = file_path.parts
                    if 'src' in parts:
                        src_index = parts.index('src')
                        # 获取src之后的路径部分
                        relative_parts = parts[src_index + 1:]
                        if relative_parts:
                            # 移除.py扩展名
                            module_parts = list(relative_parts)
                            if module_parts[-1].endswith('.py'):
                                module_parts[-1] = module_parts[-1][:-3]
                            module_name = '.'.join(module_parts)
                except (ValueError, IndexError):
                    # 如果无法构建相对路径，使用文件名
                    module_name = file_path.stem

            except Exception:
                # 如果出错，保持原始名称
                pass

        return module_name


def _setup_enterprise_logging(config: LoggerConfig):
    """设置企业级日志模式"""
    # 清除现有处理器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    # 设置日志级别
    root_logger.setLevel(getattr(logging, config.level.upper()))
    
    # 控制台处理器
    if config.enable_console_logging:
        console_handler = logging.StreamHandler(sys.stderr)
        console_format = config.console_format or (
            "%(asctime)s | %(levelname)-8s | %(caller_info)s - %(message)s"
        )
        console_formatter = ColoredFormatter(
            fmt=console_format,
            datefmt="%Y-%m-%d %H:%M:%S"
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
    
    # 文件处理器
    if config.enable_file_logging:
        # 确保日志目录存在
        config.log_dir.mkdir(parents=True, exist_ok=True)
        log_file = config.log_dir / config.log_filename
        
        # 使用轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            filename=str(log_file),
            maxBytes=_parse_size(config.max_file_size),
            backupCount=config.backup_count,
            encoding='utf-8'
        )
        
        file_format = config.file_format or (
            "%(asctime)s | %(levelname)-8s | %(caller_info)s - %(message)s"
        )
        file_formatter = StructuredFormatter(
            fmt=file_format,
            datefmt="%Y-%m-%d %H:%M:%S",
            enable_json=config.enable_json_format
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)


def _parse_size(size_str: str) -> int:
    """解析大小字符串为字节数"""
    size_str = size_str.upper().strip()
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)


# ============================================================================
# 开发模式实现（基于loguru）
# ============================================================================

class LoguruInterceptHandler(logging.Handler):
    """将标准logging重定向到loguru"""

    def emit(self, record):
        if not LOGURU_AVAILABLE:
            return

        try:
            level = loguru_logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # 改进模块名显示
        original_name = record.name
        if original_name == "__main__":
            # 尝试从文件路径获取更清晰的模块名
            try:
                file_path = Path(record.pathname)
                parts = file_path.parts
                if 'src' in parts:
                    src_index = parts.index('src')
                    relative_parts = parts[src_index + 1:]
                    if relative_parts:
                        module_parts = list(relative_parts)
                        if module_parts[-1].endswith('.py'):
                            module_parts[-1] = module_parts[-1][:-3]
                        record.name = '.'.join(module_parts)
            except Exception:
                pass

        frame, depth = logging.currentframe(), 2
        while frame and frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        loguru_logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )

        # 恢复原始名称
        record.name = original_name


def _setup_development_logging(config: LoggerConfig):
    """设置开发模式日志（loguru）"""
    if not LOGURU_AVAILABLE:
        raise ImportError("loguru is required for development mode")

    # 设置北京时间
    _setup_timezone()

    # 清除loguru默认处理器
    loguru_logger.remove()

    # 控制台处理器
    if config.enable_console_logging:
        console_format = config.console_format or (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}:{function}:{line}</cyan> - "
            "<level>{message}</level>"
        )

        loguru_logger.add(
            sys.stderr,
            level=config.level.upper(),
            format=console_format,
            colorize=True,
            enqueue=config.enable_async,
            backtrace=config.enable_exception_trace,
            diagnose=True
        )

    # 文件处理器
    if config.enable_file_logging:
        config.log_dir.mkdir(parents=True, exist_ok=True)
        log_file = config.log_dir / config.log_filename

        file_format = config.file_format or (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
            "{level: <8} | "
            "{name}:{function}:{line} - "
            "{message}"
        )

        loguru_logger.add(
            str(log_file),
            level=config.level.upper(),
            format=file_format,
            rotation=config.max_file_size,
            retention=config.backup_count,  # loguru使用数字表示保留文件数
            enqueue=config.enable_async,
            backtrace=config.enable_exception_trace,
            diagnose=True,
            encoding='utf-8'
        )

    # 拦截标准logging
    logging.basicConfig(handlers=[LoguruInterceptHandler()], level=0, force=True)

    # 设置异常钩子
    if config.enable_exception_trace:
        sys.excepthook = loguru_logger.catch


# ============================================================================
# 进阶企业模式实现（structlog + logging）
# ============================================================================

def _setup_enterprise_plus_logging(config: LoggerConfig):
    """设置进阶企业模式日志（structlog + logging）"""
    if not STRUCTLOG_AVAILABLE:
        raise ImportError("structlog is required for enterprise_plus mode")

    # 配置structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if config.enable_json_format
            else structlog.dev.ConsoleRenderer(colors=True),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # 设置标准logging作为后端
    _setup_enterprise_logging(config)


# ============================================================================
# 主要配置函数
# ============================================================================

def setup_logger(
    mode: Union[str, LoggerMode] = LoggerMode.ENTERPRISE,
    level: str = "INFO",
    log_dir: Optional[Union[str, Path]] = None,
    log_filename: str = "application.log",
    enable_file_logging: bool = True,
    enable_console_logging: bool = True,
    enable_json_format: bool = False,
    **kwargs
) -> LoggerConfig:
    """
    设置全局logger配置

    Args:
        mode: 日志模式 (enterprise/development/enterprise_plus)
        level: 日志级别
        log_dir: 日志目录
        log_filename: 日志文件名
        enable_file_logging: 是否启用文件日志
        enable_console_logging: 是否启用控制台日志
        enable_json_format: 是否启用JSON格式（仅企业模式）
        **kwargs: 其他配置参数

    Returns:
        LoggerConfig: 配置对象

    Examples:
        # 企业级模式（默认）
        setup_logger()

        # 开发模式
        setup_logger(mode="development", level="DEBUG")

        # 进阶企业模式
        setup_logger(mode="enterprise_plus", enable_json_format=True)
    """
    global _global_config, _initialized

    # 创建配置
    config = LoggerConfig()

    # 设置模式
    if isinstance(mode, str):
        mode = LoggerMode(mode)
    config.mode = mode

    # 设置基础参数
    config.level = level.upper()
    config.enable_file_logging = enable_file_logging
    config.enable_console_logging = enable_console_logging
    config.enable_json_format = enable_json_format
    config.log_filename = log_filename

    # 设置日志目录
    if log_dir:
        config.log_dir = Path(log_dir)

    # 应用其他配置
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)

    # 根据模式设置logger
    try:
        if mode == LoggerMode.ENTERPRISE:
            _setup_enterprise_logging(config)
        elif mode == LoggerMode.DEVELOPMENT:
            _setup_development_logging(config)
        elif mode == LoggerMode.ENTERPRISE_PLUS:
            _setup_enterprise_plus_logging(config)
        else:
            raise ValueError(f"Unsupported logger mode: {mode}")

        _global_config = config
        _initialized = True

        # 设置第三方框架兼容性
        setup_framework_compatibility()

        # 记录初始化成功
        if mode == LoggerMode.DEVELOPMENT and LOGURU_AVAILABLE:
            loguru_logger.info(f"Logger initialized in {mode.value} mode")
        else:
            logging.getLogger(__name__).info(f"Logger initialized in {mode.value} mode")

        return config

    except Exception as e:
        print(f"Failed to setup logger: {e}", file=sys.stderr)
        # 降级到基础配置
        logging.basicConfig(
            level=getattr(logging, level.upper()),
            format="%(asctime)s | %(levelname)-8s | %(name)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )
        raise


# ============================================================================
# 便捷函数
# ============================================================================

def setup_enterprise_logger(**kwargs):
    """
    设置企业级logger（便捷函数）

    这个函数会配置日志系统并返回可用的logger实例，可以直接用于日志记录。

    Args:
        **kwargs: 传递给setup_logger的参数

    Returns:
        logger实例：可以直接调用.info(), .error()等方法

    Examples:
        # 直接使用返回的logger
        logger = setup_enterprise_logger(level="DEBUG")
        logger.info("企业级日志系统已启动")

        # 带更多配置
        logger = setup_enterprise_logger(
            level="INFO",
            log_filename="my_app.log",
            enable_json_format=True
        )
        logger.warning("这是一条警告消息")
    """
    # 配置日志系统
    setup_logger(mode=LoggerMode.ENTERPRISE, **kwargs)
    # 返回logger实例
    return get_logger()


def setup_development_logger(**kwargs):
    """
    设置开发模式logger（便捷函数）

    这个函数会配置日志系统并返回可用的logger实例，使用loguru提供美观的开发体验。

    Args:
        **kwargs: 传递给setup_logger的参数

    Returns:
        logger实例：可以直接调用.info(), .error()等方法

    Examples:
        # 直接使用返回的logger
        logger = setup_development_logger(level="DEBUG")
        logger.info("开发模式日志系统已启动")

        # 带更多配置
        logger = setup_development_logger(
            level="DEBUG",
            enable_async=True
        )
        logger.debug("这是一条调试消息")
    """
    # 配置日志系统
    setup_logger(mode=LoggerMode.DEVELOPMENT, **kwargs)
    # 返回logger实例
    return get_logger()


def setup_enterprise_plus_logger(**kwargs):
    """
    设置进阶企业模式logger（便捷函数）

    这个函数会配置日志系统并返回可用的logger实例，使用structlog提供结构化日志。

    Args:
        **kwargs: 传递给setup_logger的参数

    Returns:
        logger实例：可以直接调用.info(), .error()等方法

    Examples:
        # 直接使用返回的logger
        logger = setup_enterprise_plus_logger(level="DEBUG")
        logger.info("进阶企业模式日志系统已启动")

        # 带更多配置
        logger = setup_enterprise_plus_logger(
            level="INFO",
            enable_json_format=True
        )
        logger.error("这是一条错误消息")
    """
    # 配置日志系统
    setup_logger(mode=LoggerMode.ENTERPRISE_PLUS, **kwargs)
    # 返回logger实例
    return get_logger()


def get_logger(name: Optional[str] = None):
    """
    获取logger实例

    Args:
        name: logger名称，默认为调用模块名

    Returns:
        logger实例

    Examples:
        # 在模块中使用
        logger = get_logger(__name__)
        logger.info("Hello world")

        # 或者简单使用
        logger = get_logger()
        logger.info("Hello world")
    """
    if not _initialized:
        # 如果未初始化，使用默认配置
        setup_logger()

    if _global_config and _global_config.mode == LoggerMode.DEVELOPMENT and LOGURU_AVAILABLE:
        # 开发模式返回loguru logger
        return loguru_logger
    elif _global_config and _global_config.mode == LoggerMode.ENTERPRISE_PLUS and STRUCTLOG_AVAILABLE:
        # 进阶企业模式返回structlog logger
        return structlog.get_logger(name)
    else:
        # 企业模式返回标准logging logger
        if name is None:
            # 自动获取调用者模块名
            import inspect
            frame = inspect.currentframe()
            try:
                caller_frame = frame.f_back
                name = caller_frame.f_globals.get('__name__', 'root')
            finally:
                del frame
        return logging.getLogger(name)


def is_initialized() -> bool:
    """检查logger是否已初始化"""
    return _initialized


def get_config() -> Optional[LoggerConfig]:
    """获取当前logger配置"""
    return _global_config


def reset_logger():
    """重置logger配置"""
    global _global_config, _initialized

    # 清理现有配置
    if _global_config and _global_config.mode == LoggerMode.DEVELOPMENT and LOGURU_AVAILABLE:
        loguru_logger.remove()

    # 清理标准logging
    root_logger = logging.getLogger()
    root_logger.handlers.clear()

    _global_config = None
    _initialized = False


# ============================================================================
# 第三方框架兼容性
# ============================================================================

def setup_framework_compatibility():
    """
    设置第三方框架的日志兼容性

    自动捕获和重定向以下框架的日志：
    - Hydra
    - FastAPI/Uvicorn
    - SQLAlchemy
    - Requests
    - 其他使用标准logging的库
    """
    # 需要拦截的第三方库logger
    third_party_loggers = [
        'hydra',
        'hydra.core',
        'hydra._internal',
        'omegaconf',
        'fastapi',
        'uvicorn',
        'uvicorn.access',
        'uvicorn.error',
        'starlette',
        'sqlalchemy',
        'sqlalchemy.engine',
        'sqlalchemy.pool',
        'requests',
        'urllib3',
        'asyncio',
    ]

    # 设置第三方库的日志级别和处理器
    for logger_name in third_party_loggers:
        third_party_logger = logging.getLogger(logger_name)
        # 不设置级别，让它们使用root logger的级别
        # 确保它们的日志会被我们的处理器捕获
        third_party_logger.propagate = True

    # 特别处理uvicorn的访问日志格式
    uvicorn_access_logger = logging.getLogger("uvicorn.access")
    uvicorn_access_logger.propagate = True

    # 特别处理hydra的日志
    hydra_logger = logging.getLogger("hydra")
    hydra_logger.propagate = True


# ============================================================================
# 导出接口
# ============================================================================

__all__ = [
    # 主要配置函数
    'setup_logger',
    'setup_enterprise_logger',
    'setup_development_logger',
    'setup_enterprise_plus_logger',

    # 获取logger
    'get_logger',

    # 配置管理
    'get_config',
    'is_initialized',
    'reset_logger',

    # 框架兼容性
    'setup_framework_compatibility',

    # 枚举和配置类
    'LoggerMode',
    'LoggerConfig',
]

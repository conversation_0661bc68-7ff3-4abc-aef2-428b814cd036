#!/usr/bin/env python3
"""
DD-B模块真实环境测试入口

最简单的测试入口，使用真实的数据库服务
您只需要定义入参即可
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '../../..')
sys.path.insert(0, src_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入真实服务
from service import get_client
from modules.dd_submission.dd_b import (
    EnhancedDataProcessor,
    DDBProcessRequest,
    create_enhanced_data_processor
)


async def simple_dd_b_test(
    report_code: str = "g0107_beta_v1.0",
    dept_id: str = "DEPT_FINANCE",
    enable_auto_fill: bool = True,
    return_original_data: bool = True
):
    """
    最简单的DD-B测试函数
    
    Args:
        report_code: 报表代码
        dept_id: 部门ID  
        enable_auto_fill: 是否启用自动填充
        return_original_data: 是否返回原始数据
    """
    logger.info("🚀 开始DD-B真实环境测试")
    logger.info(f"参数: report_code={report_code}, dept_id={dept_id}")
    
    try:
        # 1. 获取真实数据库客户端
        logger.info("📡 获取数据库客户端...")
        rdb_client = await get_client('database.rdbs.mysql')
        logger.info("✅ MySQL客户端获取成功")
        
        # 尝试获取向量数据库客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            vdb_client = await get_client('database.vdbs.pgvector')
            logger.info("✅ PGVector客户端获取成功")
            
            # 尝试获取embedding客户端（可选）
            try:
                embedding_client = await get_client('ai.embedding')
                logger.info("✅ Embedding客户端获取成功")
            except Exception as e:
                logger.warning(f"⚠️ Embedding客户端获取失败: {e}")
                
        except Exception as e:
            logger.warning(f"⚠️ PGVector客户端获取失败: {e}")
        
        # 2. 创建增强数据处理器
        logger.info("🔧 创建增强数据处理器...")
        processor = create_enhanced_data_processor(
            rdb_client=rdb_client,
            vdb_client=vdb_client,
            embedding_client=embedding_client,
            enable_concurrency=True,
            max_llm_concurrent=15
        )
        
        # 3. 启动处理器
        await processor.start()
        logger.info("✅ 处理器启动成功")
        
        try:
            # 4. 创建处理请求
            request = DDBProcessRequest(
                report_code=report_code,
                dept_id=dept_id,
                enable_auto_fill=enable_auto_fill,
                return_original_data=return_original_data
            )
            
            # 5. 执行处理
            logger.info("⚙️ 开始处理DD-B数据...")
            result = await processor.process_records(request)
            
            # 6. 输出结果
            logger.info("📊 处理结果:")
            logger.info(f"  状态: {result.status}")
            logger.info(f"  找到记录: {result.total_records_found} 条")
            logger.info(f"  处理记录: {result.records_processed} 条")
            logger.info(f"  完整记录: {result.records_with_complete_main_fields} 条")
            logger.info(f"  需要填充: {result.records_requiring_fill} 条")
            logger.info(f"  填充字段: {result.total_fields_filled} 个")
            logger.info(f"  处理耗时: {result.processing_time_ms:.2f}ms")
            
            if result.processing_notes:
                logger.info("  处理说明:")
                for note in result.processing_notes:
                    logger.info(f"    - {note}")
            
            # 7. 显示填充详情（前5个）
            if result.fill_details:
                logger.info("  填充详情（前5个）:")
                for i, detail in enumerate(result.fill_details[:5]):
                    logger.info(f"    {i+1}. {detail.field_name}: '{detail.original_value}' -> '{detail.filled_value}' ({detail.fill_reason})")
                
                if len(result.fill_details) > 5:
                    logger.info(f"    ... 还有 {len(result.fill_details) - 5} 个字段")
            
            # 8. 获取性能统计
            stats = processor.get_performance_stats()
            logger.info("📈 性能统计:")
            logger.info(f"  处理器类型: {stats['processor_type']}")
            logger.info(f"  历史连接器: {'启用' if stats['history_connector_enabled'] else '禁用'}")
            logger.info(f"  Pipeline集成器: {'启用' if stats.get('pipeline_integrator_enabled') else '禁用'}")
            logger.info(f"  并发处理: {'启用' if stats['concurrency_enabled'] else '禁用'}")
            
            logger.info("🎉 DD-B真实环境测试完成！")
            return result
            
        finally:
            # 9. 停止处理器
            await processor.stop()
            logger.info("✅ 处理器已停止")
    
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        raise


async def batch_dd_b_test(
    report_code: str = "g0107_beta_v1.0",
    dept_id: str = "DEPT_FINANCE",
    batch_size: int = 15,
    use_message_queue: bool = False
):
    """
    批量处理测试函数
    
    Args:
        report_code: 报表代码
        dept_id: 部门ID
        batch_size: 批量大小
        use_message_queue: 是否使用消息队列
    """
    logger.info("🚀 开始DD-B批量处理测试")
    logger.info(f"参数: report_code={report_code}, dept_id={dept_id}, batch_size={batch_size}")
    
    try:
        # 获取数据库客户端
        rdb_client = await get_client('database.rdbs.mysql')
        vdb_client = None
        embedding_client = None
        
        try:
            vdb_client = await get_client('database.vdbs.pgvector')
            embedding_client = await get_client('ai.embedding')
        except:
            pass
        
        # 创建处理器
        processor = create_enhanced_data_processor(
            rdb_client=rdb_client,
            vdb_client=vdb_client,
            embedding_client=embedding_client,
            enable_concurrency=True,
            max_llm_concurrent=15
        )
        
        await processor.start()
        
        try:
            # 创建请求
            request = DDBProcessRequest(
                report_code=report_code,
                dept_id=dept_id,
                enable_auto_fill=True,
                return_original_data=True
            )
            
            # 执行批量处理
            logger.info("⚙️ 开始批量处理...")
            result = await processor.process_records_in_batches(
                request=request,
                batch_size=batch_size,
                use_message_queue=use_message_queue
            )
            
            # 输出结果
            logger.info("📊 批量处理结果:")
            logger.info(f"  状态: {result.status}")
            logger.info(f"  找到记录: {result.total_records_found} 条")
            logger.info(f"  处理记录: {result.records_processed} 条")
            logger.info(f"  填充字段: {result.total_fields_filled} 个")
            logger.info(f"  处理耗时: {result.processing_time_ms:.2f}ms")
            
            if result.processing_notes:
                logger.info("  处理说明:")
                for note in result.processing_notes:
                    logger.info(f"    - {note}")
            
            logger.info("🎉 批量处理测试完成！")
            return result
            
        finally:
            await processor.stop()
    
    except Exception as e:
        logger.error(f"❌ 批量处理测试失败: {e}")
        raise


async def main():
    """主函数 - 您可以在这里定义测试参数"""
    
    # ==================== 在这里定义您的测试参数 ====================
    
    # 基础测试参数
    TEST_REPORT_CODE = "g0107_beta_v1.0"  # 您的报表代码
    TEST_DEPT_ID = "DEPT_FINANCE"         # 您的部门ID
    
    # 测试选项
    RUN_BASIC_TEST = True      # 是否运行基础测试
    RUN_BATCH_TEST = True      # 是否运行批量测试
    
    # 批量测试参数
    BATCH_SIZE = 15            # 批量大小
    USE_MESSAGE_QUEUE = False  # 是否使用消息队列
    
    # ================================================================
    
    logger.info("🎯 DD-B模块真实环境测试")
    logger.info("=" * 60)
    
    try:
        # 基础测试
        if RUN_BASIC_TEST:
            logger.info("\n📋 运行基础测试...")
            basic_result = await simple_dd_b_test(
                report_code=TEST_REPORT_CODE,
                dept_id=TEST_DEPT_ID,
                enable_auto_fill=True,
                return_original_data=True
            )
            logger.info("✅ 基础测试完成")
        
        # 批量测试
        if RUN_BATCH_TEST:
            logger.info("\n📦 运行批量测试...")
            batch_result = await batch_dd_b_test(
                report_code=TEST_REPORT_CODE,
                dept_id=TEST_DEPT_ID,
                batch_size=BATCH_SIZE,
                use_message_queue=USE_MESSAGE_QUEUE
            )
            logger.info("✅ 批量测试完成")
        
        logger.info("\n🎉 所有测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        return False


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    exit(0 if success else 1)

#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：src 
@File    ：data_utils.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/28 9:09 
@Desc    ：数据结构的处理方法
"""
def get_surrounding_items(lst: list, pos: int, p_range: int =3):
    """
    对一个列表进行切片
    """
    if pos < 0 or pos >= len(lst):
        return []  # 如果 pos 超出范围，返回空列表

    start = max(0, pos - p_range)
    end = min(len(lst), pos + p_range + 1)

    return lst[start:end]

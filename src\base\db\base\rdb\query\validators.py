"""
请求验证器

验证查询请求的有效性和安全性
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Set
import re

from ..core.models import (
    QueryRequest, InsertRequest, UpdateRequest, DeleteRequest,
    QueryFilter, QueryFilterGroup
)
from ..core.exceptions import ValidationError
from .filters import FilterValidator


class RequestValidator(ABC):
    """请求验证器接口"""
    
    @abstractmethod
    def validate_query_request(self, request: QueryRequest) -> None:
        """验证查询请求"""
        pass
    
    @abstractmethod
    def validate_insert_request(self, request: InsertRequest) -> None:
        """验证插入请求"""
        pass
    
    @abstractmethod
    def validate_update_request(self, request: UpdateRequest) -> None:
        """验证更新请求"""
        pass
    
    @abstractmethod
    def validate_delete_request(self, request: DeleteRequest) -> None:
        """验证删除请求"""
        pass


class DefaultRequestValidator(RequestValidator):
    """默认请求验证器
    
    提供通用的请求验证逻辑
    """
    
    def __init__(
        self,
        allowed_tables: Optional[List[str]] = None,
        allowed_columns: Optional[Dict[str, List[str]]] = None,
        max_limit: int = 10000,
        max_offset: int = 1000000,
        require_where_for_update: bool = True,
        require_where_for_delete: bool = True
    ):
        """
        Args:
            allowed_tables: 允许的表名列表
            allowed_columns: 允许的列名映射 {table: [columns]}
            max_limit: 最大LIMIT值
            max_offset: 最大OFFSET值
            require_where_for_update: 更新操作是否必须有WHERE条件
            require_where_for_delete: 删除操作是否必须有WHERE条件
        """
        self.allowed_tables = set(allowed_tables) if allowed_tables else None
        self.allowed_columns = allowed_columns or {}
        self.max_limit = max_limit
        self.max_offset = max_offset
        self.require_where_for_update = require_where_for_update
        self.require_where_for_delete = require_where_for_delete
        
        # 创建过滤器验证器
        self.filter_validator = FilterValidator()
    
    def validate_query_request(self, request: QueryRequest) -> None:
        """验证查询请求"""
        # 验证表名
        self._validate_table_name(request.table)
        
        # 验证列名
        if request.columns:
            self._validate_column_names(request.table, request.columns)
        
        # 验证过滤器
        if request.filters:
            self._validate_filters(request.table, request.filters)
        
        # 验证JOIN
        if request.joins:
            for join in request.joins:
                self._validate_table_name(join.table)
                self._validate_join_condition(join.on_condition)
                if join.filters:
                    self._validate_filters(join.table, join.filters)
        
        # 验证排序
        if request.sorts:
            sort_fields = [sort.field for sort in request.sorts]
            self._validate_column_names(request.table, sort_fields)
        
        # 验证分组
        if request.group_by:
            self._validate_column_names(request.table, request.group_by.fields)
            if request.group_by.having:
                self._validate_filters(request.table, request.group_by.having)
        
        # 验证聚合
        if request.aggregations:
            for agg in request.aggregations:
                if agg.field:
                    self._validate_column_names(request.table, [agg.field])
        
        # 验证分页参数
        if request.limit is not None:
            if request.limit <= 0:
                raise ValidationError("Limit must be positive")
            if request.limit > self.max_limit:
                raise ValidationError(f"Limit exceeds maximum ({self.max_limit})")
        
        if request.offset is not None:
            if request.offset < 0:
                raise ValidationError("Offset must be non-negative")
            if request.offset > self.max_offset:
                raise ValidationError(f"Offset exceeds maximum ({self.max_offset})")
        
        # 验证超时
        if request.timeout is not None:
            if request.timeout <= 0:
                raise ValidationError("Timeout must be positive")
    
    def validate_insert_request(self, request: InsertRequest) -> None:
        """验证插入请求"""
        # 验证表名
        self._validate_table_name(request.table)
        
        # 验证数据
        if not request.data:
            raise ValidationError("Insert data cannot be empty")
        
        # 验证数据格式
        if isinstance(request.data, list):
            if not request.data:
                raise ValidationError("Insert data list cannot be empty")
            
            # 验证每条记录
            for i, record in enumerate(request.data):
                if not isinstance(record, dict):
                    raise ValidationError(f"Insert record {i} must be a dictionary")
                
                if not record:
                    raise ValidationError(f"Insert record {i} cannot be empty")
                
                # 验证列名
                self._validate_column_names(request.table, list(record.keys()))
        else:
            if not isinstance(request.data, dict):
                raise ValidationError("Insert data must be a dictionary or list of dictionaries")
            
            if not request.data:
                raise ValidationError("Insert data cannot be empty")
            
            # 验证列名
            self._validate_column_names(request.table, list(request.data.keys()))
        
        # 验证返回列
        if request.returning:
            self._validate_column_names(request.table, request.returning)
        
        # 验证超时
        if request.timeout is not None:
            if request.timeout <= 0:
                raise ValidationError("Timeout must be positive")
    
    def validate_update_request(self, request: UpdateRequest) -> None:
        """验证更新请求"""
        # 验证表名
        self._validate_table_name(request.table)
        
        # 验证更新数据
        if not request.data:
            raise ValidationError("Update data cannot be empty")
        
        if not isinstance(request.data, dict):
            raise ValidationError("Update data must be a dictionary")
        
        # 验证列名
        self._validate_column_names(request.table, list(request.data.keys()))
        
        # 验证WHERE条件
        if self.require_where_for_update and not request.filters:
            raise ValidationError("Update operation requires WHERE conditions for safety")
        
        if request.filters:
            self._validate_filters(request.table, request.filters)
        
        # 验证JOIN
        if request.joins:
            for join in request.joins:
                self._validate_table_name(join.table)
                self._validate_join_condition(join.on_condition)
        
        # 验证返回列
        if request.returning:
            self._validate_column_names(request.table, request.returning)
        
        # 验证超时
        if request.timeout is not None:
            if request.timeout <= 0:
                raise ValidationError("Timeout must be positive")
    
    def validate_delete_request(self, request: DeleteRequest) -> None:
        """验证删除请求"""
        # 验证表名
        self._validate_table_name(request.table)
        
        # 验证WHERE条件
        if self.require_where_for_delete and not request.filters:
            raise ValidationError("Delete operation requires WHERE conditions for safety")
        
        if request.filters:
            self._validate_filters(request.table, request.filters)
        
        # 验证JOIN
        if request.joins:
            for join in request.joins:
                self._validate_table_name(join.table)
                self._validate_join_condition(join.on_condition)
        
        # 验证返回列
        if request.returning:
            self._validate_column_names(request.table, request.returning)
        
        # 验证超时
        if request.timeout is not None:
            if request.timeout <= 0:
                raise ValidationError("Timeout must be positive")
    
    def _validate_table_name(self, table_name: str) -> None:
        """验证表名"""
        if not table_name:
            raise ValidationError("Table name cannot be empty")
        
        # 检查允许的表名
        if self.allowed_tables and table_name not in self.allowed_tables:
            raise ValidationError(f"Table '{table_name}' is not allowed")
        
        # 检查表名安全性
        self._validate_identifier(table_name, "table")
    
    def _validate_column_names(self, table_name: str, column_names: List[str]) -> None:
        """验证列名"""
        if not column_names:
            return
        
        # 检查允许的列名
        if table_name in self.allowed_columns:
            allowed_cols = set(self.allowed_columns[table_name])
            for col in column_names:
                if col not in allowed_cols:
                    raise ValidationError(f"Column '{col}' is not allowed for table '{table_name}'")
        
        # 检查列名安全性
        for col in column_names:
            if not col:
                raise ValidationError("Column name cannot be empty")
            self._validate_identifier(col, "column")
    
    def _validate_filters(self, table_name: str, filters: QueryFilterGroup) -> None:
        """验证过滤器"""
        # 获取表的允许列名
        allowed_fields = None
        if table_name in self.allowed_columns:
            allowed_fields = self.allowed_columns[table_name]
        
        # 创建过滤器验证器
        filter_validator = FilterValidator(allowed_fields)
        filter_validator.validate_filter_group(filters)
    
    def _validate_join_condition(self, condition: str) -> None:
        """验证JOIN条件"""
        if not condition:
            raise ValidationError("Join condition cannot be empty")
        
        # 简单的JOIN条件格式检查
        # 应该包含等号或其他比较操作符
        if not re.search(r'[=<>!]', condition):
            raise ValidationError("Join condition must contain a comparison operator")
        
        # 检查是否包含危险字符
        dangerous_patterns = [
            r';\s*drop\s+table',
            r';\s*delete\s+from',
            r';\s*update\s+',
            r';\s*insert\s+into',
            r'--',
            r'/\*',
            r'\*/',
        ]
        
        condition_lower = condition.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, condition_lower):
                raise ValidationError(f"Join condition contains dangerous pattern: {condition}")
    
    def _validate_identifier(self, identifier: str, identifier_type: str) -> None:
        """验证SQL标识符（表名、列名等）"""
        if not identifier:
            raise ValidationError(f"{identifier_type.capitalize()} name cannot be empty")
        
        # 检查长度
        if len(identifier) > 64:  # MySQL/PostgreSQL标准限制
            raise ValidationError(f"{identifier_type.capitalize()} name too long: {identifier}")
        
        # 检查字符
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', identifier):
            raise ValidationError(f"Invalid {identifier_type} name: {identifier}")
        
        # 检查SQL关键字
        sql_keywords = {
            'select', 'insert', 'update', 'delete', 'drop', 'create', 'alter',
            'table', 'database', 'index', 'view', 'procedure', 'function',
            'from', 'where', 'join', 'union', 'order', 'group', 'having',
            'and', 'or', 'not', 'in', 'exists', 'between', 'like', 'is',
            'null', 'true', 'false', 'case', 'when', 'then', 'else', 'end'
        }
        
        if identifier.lower() in sql_keywords:
            raise ValidationError(f"{identifier_type.capitalize()} name cannot be a SQL keyword: {identifier}")


class StrictRequestValidator(DefaultRequestValidator):
    """严格的请求验证器
    
    提供更严格的验证规则，适用于生产环境
    """
    
    def __init__(self, **kwargs):
        # 设置更严格的默认值
        kwargs.setdefault('max_limit', 1000)
        kwargs.setdefault('max_offset', 100000)
        kwargs.setdefault('require_where_for_update', True)
        kwargs.setdefault('require_where_for_delete', True)
        
        super().__init__(**kwargs)
        
        # 更严格的过滤器验证
        self.filter_validator.max_filter_depth = 5
        self.filter_validator.max_in_values = 100
    
    def validate_query_request(self, request: QueryRequest) -> None:
        """验证查询请求（严格模式）"""
        super().validate_query_request(request)
        
        # 额外的严格检查
        if request.limit is None:
            raise ValidationError("LIMIT is required in strict mode")
        
        if request.limit > 100:
            raise ValidationError("LIMIT too large in strict mode (max: 100)")


class PermissiveRequestValidator(DefaultRequestValidator):
    """宽松的请求验证器
    
    提供较宽松的验证规则，适用于开发环境
    """
    
    def __init__(self, **kwargs):
        # 设置更宽松的默认值
        kwargs.setdefault('max_limit', 100000)
        kwargs.setdefault('max_offset', 10000000)
        kwargs.setdefault('require_where_for_update', False)
        kwargs.setdefault('require_where_for_delete', False)
        
        super().__init__(**kwargs)
        
        # 更宽松的过滤器验证
        self.filter_validator.max_filter_depth = 20
        self.filter_validator.max_in_values = 10000

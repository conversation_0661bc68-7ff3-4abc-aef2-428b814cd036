"""
文本报告生成器数据模型

定义了指标信息、章节结构、报告结构等核心数据模型
"""

from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime


class IndicatorInfo(BaseModel):
    """指标信息数据模型"""
    id: str = Field(..., description="指标唯一标识符")
    id_name: str = Field(..., description="指标名称，如'农林牧短期'")
    id_desc: str = Field(..., description="指标描述，如'描述了本期农林牧短期贷款'")
    id_value: str = Field(..., description="指标数值")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "id_name": "农林牧短期",
                "id_desc": "描述了本期农林牧短期贷款",
                "id_value": "15000"
            }
        }


class ChapterInfo(BaseModel):
    """章节信息数据模型"""
    chapter_name: str = Field(..., description="章节标题")
    chapter_summary: str = Field(..., description="章节内容摘要")
    chapter_content: str = Field(..., description="章节原始内容")
    chapter_referenced_index_id: List[int] = Field(default_factory=list, description="匹配的指标ID列表")
    chapter_index: Optional[int] = Field(None, description="章节索引")

    class Config:
        json_schema_extra = {
            "example": {
                "chapter_name": "贷款总览",
                "chapter_summary": "描述了贷款情况",
                "chapter_content": "本月，农林牧相关的长期贷款为10000元，农林牧相关的短期贷款为5000元...",
                "referenced_index_id": [1, 2, 3, 4],
                "chapter_index": None
            }
        }


class ReportGenerationRequest(BaseModel):
    """报告生成请求数据模型"""
    series_name: str = Field(..., description="套系名称")
    report_name: str = Field(..., description="报表名称")
    indicators: List[IndicatorInfo] = Field(..., description="报表携带的所有指标信息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "series_name": "月度贷款报告",
                "report_name": "2025年3月贷款总览",
                "indicators": [
                    {
                        "id": 1,
                        "id_name": "农林牧短期",
                        "id_desc": "描述了本期农林牧短期贷款",
                        "id_value": 15000
                    }
                ]
            }
        }


class HistoricalReportQuery(BaseModel):
    """历史报告查询请求"""
    report_name: str = Field(..., description="要查询的报告名称")
    knowledge_id: Optional[str] = Field(None, description="限定知识库业务范围")
    offset: int = Field(0, description="偏移量")
    limit: int = Field(10, description="限制数量")

class HistoricalReportQueryResult(BaseModel):
    """历史报告查询结果"""
    doc_id: str = Field(..., description="文档ID")
    doc_series_name: str = Field(..., description="文档套系名称")
    doc_report_name: str = Field(..., description="文档报表名称")
    doc_timestamp: datetime = Field(..., description="文档时间戳")


class ReferenceDocumentUpload(BaseModel):
    """参考文档上传请求"""
    content: str = Field(..., description="上传的文档内容（Markdown格式）")
    file_name: str = Field(..., description="上传的文件名")


class ChapterGenerationRequest(BaseModel):
    """章节内容生成请求"""
    chapter: ChapterInfo = Field(..., description="章节信息")
    indicators: List[IndicatorInfo] = Field(..., description="用于填充的指标信息")


class ReportConfirmationRequest(BaseModel):
    """报告确认请求"""
    series_name: str = Field(..., description="套系名称")
    report_name: str = Field(..., description="报表名称")
    chapters: List[ChapterInfo] = Field(..., description="确认后的各章节内容")
    timestamp: datetime = Field(default_factory=datetime.now, description="确认时间戳")


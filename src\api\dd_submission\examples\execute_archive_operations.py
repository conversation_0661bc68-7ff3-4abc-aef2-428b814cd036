#!/usr/bin/env python3
"""
执行代码归档操作

基于代码审查结果，安全归档不再需要的文件
"""

import os
import shutil
import logging
from datetime import datetime
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CodeArchiver:
    """代码归档器"""
    
    def __init__(self):
        # 获取当前工作目录作为基础目录
        self.base_dir = Path.cwd()
        self.archive_date = datetime.now().strftime("%Y%m%d")
        self.archived_files = []
        self.errors = []
    
    def create_archive_directories(self):
        """创建归档目录"""
        archive_dirs = [
            "src/api/dd_submission/archive/completed_tests",
            "src/api/dd_submission/archive/completed_docs", 
            "src/api/dd_submission/archive/backup_files",
            "src/modules/dd_submission/department_assignment/archive/completed_tests",
            "src/modules/dd_submission/department_assignment/archive/completed_docs",
            "src/modules/dd_submission/department_assignment/archive/demo_files"
        ]
        
        for dir_path in archive_dirs:
            full_path = self.base_dir / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ 创建归档目录: {dir_path}")
    
    def archive_api_test_files(self):
        """归档API测试文件"""
        files_to_archive = [
            ("src/api/dd_submission/tests/simple_test_runner.py", 
             "src/api/dd_submission/archive/completed_tests/simple_test_runner.py"),
            ("src/api/dd_submission/tests/test_new_api_implementation.py",
             "src/api/dd_submission/archive/completed_tests/test_new_api_implementation.py")
        ]
        
        for src, dst in files_to_archive:
            self._move_file(src, dst, "API测试文件")
    
    def archive_backup_files(self):
        """归档备份文件"""
        backup_dir = self.base_dir / "src/api/dd_submission/backup"
        if backup_dir.exists():
            dst_dir = self.base_dir / f"src/api/dd_submission/archive/backup_files/backup_{self.archive_date}"
            try:
                shutil.move(str(backup_dir), str(dst_dir))
                self.archived_files.append(f"backup/ → archive/backup_files/backup_{self.archive_date}/")
                logger.info(f"✅ 归档备份目录: backup/ → archive/backup_files/backup_{self.archive_date}/")
            except Exception as e:
                self.errors.append(f"归档备份目录失败: {e}")
                logger.error(f"❌ 归档备份目录失败: {e}")
    
    def archive_completed_docs(self):
        """归档已完成的文档"""
        docs_to_archive = [
            # 分析文档
            "src/modules/dd_submission/department_assignment/docs/api_analysis_final_report.md",
            "src/modules/dd_submission/department_assignment/docs/batch_operations_guide.md",
            "src/modules/dd_submission/department_assignment/docs/batch_operations_performance_analysis.md",
            "src/modules/dd_submission/department_assignment/docs/batch_optimization_final_report.md",
            "src/modules/dd_submission/department_assignment/docs/batch_optimization_final_results.md",
            "src/modules/dd_submission/department_assignment/docs/batch_optimization_solutions.md",
            "src/modules/dd_submission/department_assignment/docs/code_comparison_guide.md",
            "src/modules/dd_submission/department_assignment/docs/code_review_and_optimization.md",
            "src/modules/dd_submission/department_assignment/docs/data_backfill_optimization_analysis.md",
            "src/modules/dd_submission/department_assignment/docs/data_backfill_optimization_summary.md",
            "src/modules/dd_submission/department_assignment/docs/database_layer_improvement_plan.md",
            "src/modules/dd_submission/department_assignment/docs/ddcrud_optimization_impact_analysis.md",
            "src/modules/dd_submission/department_assignment/docs/ddcrud_safety_confirmation.md",
            "src/modules/dd_submission/department_assignment/docs/existing_api_analysis.md",
            "src/modules/dd_submission/department_assignment/docs/implementation_breakdown.md",
            "src/modules/dd_submission/department_assignment/docs/implementation_recommendations.md",
            "src/modules/dd_submission/department_assignment/docs/integration_test_results.md",
            "src/modules/dd_submission/department_assignment/docs/knowledge_transfer_summary.md",
            "src/modules/dd_submission/department_assignment/docs/optimization_application_recommendations.md",
            "src/modules/dd_submission/department_assignment/docs/risk_assessment_rollback.md"
        ]
        
        for doc_file in docs_to_archive:
            src_path = self.base_dir / doc_file
            if src_path.exists():
                dst_path = self.base_dir / f"src/modules/dd_submission/department_assignment/archive/completed_docs/{src_path.name}"
                self._move_file(str(src_path), str(dst_path), "完成的文档")
    
    def archive_completed_tests(self):
        """归档已完成的测试文件"""
        tests_to_archive = [
            "src/modules/dd_submission/department_assignment/tests/backfill_optimization_test.py",
            "src/modules/dd_submission/department_assignment/tests/comprehensive_test_results.log",
            "src/modules/dd_submission/department_assignment/tests/core_logic_test.py",
            "src/modules/dd_submission/department_assignment/tests/ddcrud_optimization_verification_test.py",
            "src/modules/dd_submission/department_assignment/tests/integration_test.py",
            "src/modules/dd_submission/department_assignment/tests/integration_test_plan.md",
            "src/modules/dd_submission/department_assignment/tests/new_api_test_data.py",
            "src/modules/dd_submission/department_assignment/tests/safety_verification_test.py",
            "src/modules/dd_submission/department_assignment/tests/simple_backfill_test.py",
            "src/modules/dd_submission/department_assignment/tests/test_batch_config.py",
            "src/modules/dd_submission/department_assignment/tests/test_batch_operations_performance.py",
            "src/modules/dd_submission/department_assignment/tests/test_database_operations_improved.py",
            "src/modules/dd_submission/department_assignment/tests/test_database_operations_simple.py",
            "src/modules/dd_submission/department_assignment/tests/verify_crud_integration.py"
        ]
        
        for test_file in tests_to_archive:
            src_path = self.base_dir / test_file
            if src_path.exists():
                dst_path = self.base_dir / f"src/modules/dd_submission/department_assignment/archive/completed_tests/{src_path.name}"
                self._move_file(str(src_path), str(dst_path), "完成的测试")
    
    def archive_demo_files(self):
        """归档演示文件"""
        demo_dir = self.base_dir / "src/modules/dd_submission/department_assignment/demo"
        if demo_dir.exists():
            dst_dir = self.base_dir / f"src/modules/dd_submission/department_assignment/archive/demo_files/demo_{self.archive_date}"
            try:
                shutil.move(str(demo_dir), str(dst_dir))
                self.archived_files.append(f"demo/ → archive/demo_files/demo_{self.archive_date}/")
                logger.info(f"✅ 归档演示目录: demo/ → archive/demo_files/demo_{self.archive_date}/")
            except Exception as e:
                self.errors.append(f"归档演示目录失败: {e}")
                logger.error(f"❌ 归档演示目录失败: {e}")
    
    def archive_backup_database_file(self):
        """归档数据库备份文件"""
        backup_file = self.base_dir / "src/modules/dd_submission/department_assignment/core/database_operations.py.backup"
        if backup_file.exists():
            dst_file = self.base_dir / f"src/modules/dd_submission/department_assignment/archive/completed_tests/database_operations.py.backup"
            self._move_file(str(backup_file), str(dst_file), "数据库备份文件")
    
    def archive_analysis_directory(self):
        """归档分析目录"""
        analysis_dir = self.base_dir / "src/modules/dd_submission/department_assignment/docs/analysis"
        if analysis_dir.exists():
            dst_dir = self.base_dir / f"src/modules/dd_submission/department_assignment/archive/completed_docs/analysis_{self.archive_date}"
            try:
                shutil.move(str(analysis_dir), str(dst_dir))
                self.archived_files.append(f"docs/analysis/ → archive/completed_docs/analysis_{self.archive_date}/")
                logger.info(f"✅ 归档分析目录: docs/analysis/ → archive/completed_docs/analysis_{self.archive_date}/")
            except Exception as e:
                self.errors.append(f"归档分析目录失败: {e}")
                logger.error(f"❌ 归档分析目录失败: {e}")
    
    def _move_file(self, src: str, dst: str, file_type: str):
        """移动文件"""
        src_path = Path(src) if not Path(src).is_absolute() else Path(src)
        dst_path = Path(dst) if not Path(dst).is_absolute() else Path(dst)
        
        if not src_path.is_absolute():
            src_path = self.base_dir / src_path
        if not dst_path.is_absolute():
            dst_path = self.base_dir / dst_path
        
        try:
            if src_path.exists():
                # 确保目标目录存在
                dst_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.move(str(src_path), str(dst_path))
                self.archived_files.append(f"{src_path.name} → {dst_path.relative_to(self.base_dir)}")
                logger.info(f"✅ 归档{file_type}: {src_path.name}")
            else:
                logger.warning(f"⚠️ 文件不存在: {src_path}")
        except Exception as e:
            self.errors.append(f"归档{file_type} {src_path.name} 失败: {e}")
            logger.error(f"❌ 归档{file_type} {src_path.name} 失败: {e}")
    
    def create_archive_summary(self):
        """创建归档总结"""
        summary_content = f"""# 代码归档总结 - {self.archive_date}

## 📊 归档统计

### 成功归档的文件 ({len(self.archived_files)}个)
"""
        for file_info in self.archived_files:
            summary_content += f"- {file_info}\n"
        
        if self.errors:
            summary_content += f"\n### 归档错误 ({len(self.errors)}个)\n"
            for error in self.errors:
                summary_content += f"- ❌ {error}\n"
        
        summary_content += f"""
## 🎯 归档原因

### 测试文件归档
- **功能已集成**: 旧的单独测试功能已集成到新的综合测试中
- **被新测试替代**: 新的`real_environment_integration_test.py`提供更全面的测试
- **验证已完成**: 安全验证、性能验证等一次性验证任务已完成

### 文档文件归档
- **分析已完成**: 各种分析报告的目的已达成
- **实施已完成**: 实施指南和建议已应用到新API中
- **历史记录**: 保留在归档中作为历史记录和参考

### 演示文件归档
- **演示目的达成**: 优化效果已在新API中体现
- **功能已集成**: 演示的功能已集成到生产代码中

## ✅ 归档后验证

归档操作完成后，请运行以下命令验证新API功能：

```bash
# 运行完整的真实环境集成测试
cd src
python api/dd_submission/tests/real_environment_integration_test.py

# 验证测试数据生成
python api/dd_submission/tests/create_test_data.py
```

预期结果：所有测试应该继续保持100%通过率。

## 📋 保留的关键文件

### 新API核心文件
- `src/api/dd_submission/services/` - 所有服务文件
- `src/api/dd_submission/routers/` - 所有路由文件
- `src/api/dd_submission/models/` - 所有模型文件

### 重要测试文件
- `src/api/dd_submission/tests/create_test_data.py` - 测试数据生成
- `src/api/dd_submission/tests/real_environment_integration_test.py` - 集成测试
- `src/api/dd_submission/tests/100_percent_success_report.md` - 成功报告

### 核心引擎文件
- `src/modules/dd_submission/department_assignment/core/` - 核心引擎（新API仍在使用）
- `src/modules/dd_submission/department_assignment/infrastructure/` - 基础设施
- `src/modules/dd_submission/department_assignment/monitoring/` - 性能监控

归档日期: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""
        
        summary_path = self.base_dir / f"src/api/dd_submission/tests/archive_summary_{self.archive_date}.md"
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        logger.info(f"✅ 创建归档总结: {summary_path}")
        return summary_path


def main():
    """主函数"""
    logger.info("🚀 开始执行代码归档操作")
    
    archiver = CodeArchiver()
    
    try:
        # 1. 创建归档目录
        archiver.create_archive_directories()
        
        # 2. 归档API测试文件
        archiver.archive_api_test_files()
        
        # 3. 归档备份文件
        archiver.archive_backup_files()
        
        # 4. 归档已完成的文档
        archiver.archive_completed_docs()
        
        # 5. 归档已完成的测试
        archiver.archive_completed_tests()
        
        # 6. 归档演示文件
        archiver.archive_demo_files()
        
        # 7. 归档数据库备份文件
        archiver.archive_backup_database_file()
        
        # 8. 归档分析目录
        archiver.archive_analysis_directory()
        
        # 9. 创建归档总结
        summary_path = archiver.create_archive_summary()
        
        logger.info(f"\n🎉 归档操作完成！")
        logger.info(f"📊 成功归档: {len(archiver.archived_files)} 个文件")
        if archiver.errors:
            logger.warning(f"⚠️ 归档错误: {len(archiver.errors)} 个")
        logger.info(f"📄 归档总结: {summary_path}")
        
    except Exception as e:
        logger.error(f"❌ 归档操作失败: {e}")
        raise


if __name__ == "__main__":
    main()

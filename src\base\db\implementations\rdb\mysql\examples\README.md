# MySQL数据库客户端

基于Universal架构设计的MySQL专用数据库客户端，直接基于原生MySQL驱动实现，提供高性能的数据库操作。

## 特性

- **完整的RDB抽象层接口支持** - 实现所有DatabaseClient接口方法
- **同步/异步双重操作模式** - 支持sync和async两种操作方式
- **高效的连接池管理** - 基于pymysql和aiomysql的连接池
- **完善的错误处理和重试机制** - 智能错误分类和自动重试
- **事务管理支持** - 支持事务隔离级别和自动回滚
- **性能监控和健康检查** - 内置性能统计和健康检查功能
- **Hydra配置系统集成** - 支持配置文件驱动的客户端创建

## 快速开始

### 安装依赖

```bash
# 同步操作依赖
pip install pymysql

# 异步操作依赖  
pip install aiomysql

# 或者同时安装
pip install pymysql aiomysql
```

### 基本使用

```python
from base.db.implementations.rdb.mysql import create_mysql_client

# 创建客户端
client = create_mysql_client(
    host="localhost",
    database="mydb",
    username="user", 
    password="pass"
)

# 执行查询
result = client.fetch_all("SELECT * FROM users WHERE active = %s", [True])
print(f"找到 {len(result.data)} 个活跃用户")

# 使用RDB接口
from base.db.base.rdb import QueryRequest

request = QueryRequest(
    table="users",
    columns=["id", "name", "email"],
    limit=10
)

response = client.query(request)
print(f"查询返回 {response.total_count} 条记录")
```

### 异步使用

```python
import asyncio

async def async_example():
    # 连接数据库
    await client.aconnect()
    
    # 异步查询
    result = await client.afetch_all("SELECT * FROM users")
    print(f"异步查询返回 {len(result.data)} 条记录")
    
    # 异步事务
    async with client.atransaction():
        await client.aexecute("INSERT INTO users (name) VALUES (%s)", ["Alice"])
        await client.aexecute("INSERT INTO users (name) VALUES (%s)", ["Bob"])
    
    # 断开连接
    await client.adisconnect()

# 运行异步示例
asyncio.run(async_example())
```

## 配置选项

### 从字典创建

```python
from base.db.implementations.rdb.mysql import create_mysql_client_from_dict

config = {
    "host": "localhost",
    "port": 3306,
    "database": "mydb",
    "username": "user",
    "password": "pass",
    
    # 连接池配置
    "pool_size": 20,
    "max_overflow": 30,
    "pool_timeout": 30.0,
    "pool_recycle": 3600,
    
    # MySQL特定配置
    "charset": "utf8mb4",
    "use_unicode": True,
    "autocommit": False,
    
    # 性能配置
    "enable_cache": True,
    "cache_size": 1000,
    "max_retries": 3,
    "retry_delay": 1.0
}

client = create_mysql_client_from_dict(config)
```

### SSL连接

```python
from base.db.implementations.rdb.mysql import create_mysql_client_with_ssl

client = create_mysql_client_with_ssl(
    host="secure-mysql.example.com",
    database="mydb",
    username="user",
    password="pass",
    ssl_cert="/path/to/client-cert.pem",
    ssl_key="/path/to/client-key.pem", 
    ssl_ca="/path/to/ca-cert.pem"
)
```

## RDB接口使用

### 查询操作

```python
from base.db.base.rdb import (
    QueryRequest, QueryFilter, QueryFilterGroup,
    ComparisonOperator, LogicalOperator, QuerySort, SortOrder
)

# 基本查询
request = QueryRequest(
    table="users",
    columns=["id", "name", "email", "created_at"]
)
response = client.query(request)

# 条件查询
request = QueryRequest(
    table="users",
    filters=QueryFilter(
        field="active",
        operator=ComparisonOperator.EQ,
        value=True
    )
)
response = client.query(request)

# 复杂条件查询
complex_filter = QueryFilterGroup(
    operator=LogicalOperator.AND,
    filters=[
        QueryFilter(field="age", operator=ComparisonOperator.GTE, value=18),
        QueryFilter(field="status", operator=ComparisonOperator.IN, value=["active", "pending"])
    ]
)

request = QueryRequest(
    table="users",
    filters=complex_filter,
    sorts=[QuerySort(field="created_at", order=SortOrder.DESC)],
    limit=50,
    offset=0
)
response = client.query(request)
```

### CRUD操作

```python
from base.db.base.rdb import InsertRequest, UpdateRequest, DeleteRequest

# 插入数据
insert_request = InsertRequest(
    table="users",
    data={
        "name": "Alice",
        "email": "<EMAIL>",
        "age": 25
    }
)
result = client.insert(insert_request)

# 更新数据
update_request = UpdateRequest(
    table="users",
    data={"age": 26},
    filters=QueryFilter(field="name", operator=ComparisonOperator.EQ, value="Alice")
)
result = client.update(update_request)

# 删除数据
delete_request = DeleteRequest(
    table="users",
    filters=QueryFilter(field="active", operator=ComparisonOperator.EQ, value=False)
)
result = client.delete(delete_request)
```

### 事务管理

```python
from base.db.base.rdb import TransactionIsolation

# 同步事务
with client.transaction(TransactionIsolation.READ_COMMITTED):
    client.execute("INSERT INTO users (name) VALUES (%s)", ["Alice"])
    client.execute("INSERT INTO orders (user_id, amount) VALUES (%s, %s)", [1, 100.0])

# 异步事务
async with client.atransaction(TransactionIsolation.REPEATABLE_READ):
    await client.aexecute("UPDATE accounts SET balance = balance - %s WHERE id = %s", [100, 1])
    await client.aexecute("UPDATE accounts SET balance = balance + %s WHERE id = %s", [100, 2])
```

## 性能监控

```python
# 获取性能统计
stats = client.get_performance_stats()
print(f"总操作数: {stats['operations_count']}")
print(f"平均查询时间: {stats['avg_query_time']:.3f}秒")

# 获取连接池统计
pool_stats = client.get_pool_stats()
print(f"连接池状态: {pool_stats}")

# 健康检查
health = client.health_check()
print(f"数据库状态: {health['status']}")
```

## 错误处理

```python
from base.db.implementations.rdb.mysql import (
    MySQLError, MySQLConnectionError, MySQLQueryError
)

try:
    result = client.fetch_all("SELECT * FROM non_existent_table")
except MySQLQueryError as e:
    print(f"查询错误: {e}")
    print(f"原始错误: {e.original_error}")
except MySQLConnectionError as e:
    print(f"连接错误: {e}")
except MySQLError as e:
    print(f"MySQL错误: {e}")
```

## 测试

运行综合测试：

```bash
cd src/base/db/implementations/rdb/mysql/examples
python comprehensive_test.py
```

测试包括：
1. 连接测试
2. 表创建测试  
3. 插入操作测试
4. 查询操作测试
5. 更新操作测试
6. 删除操作测试
7. 事务测试
8. 异步操作测试
9. 性能和安全测试

## 架构设计

MySQL客户端遵循Universal架构设计原则：

- **分层架构**: 抽象接口 + 具体实现
- **工厂模式**: 统一的客户端创建方式
- **配置驱动**: 支持字典配置和Hydra集成
- **错误适配**: 统一的异常处理体系
- **连接池管理**: 高效的连接复用机制

## 依赖关系

```
MySQLClient
├── MySQLConnectionConfig (配置管理)
├── PooledConnectionMixin (连接池管理)
├── DefaultResultAdapter (结果适配)
├── DefaultErrorAdapter (错误适配)
└── mysql_operation (装饰器)
```

## 注意事项

1. **连接管理**: 客户端支持按需连接，首次调用方法时自动建立连接
2. **连接池**: 同步和异步操作使用独立的连接池
3. **事务隔离**: 支持MySQL的所有事务隔离级别
4. **SQL注入防护**: 所有参数化查询都经过安全处理
5. **性能优化**: 内置查询缓存和性能监控功能

## 与Universal实现的对比

| 特性 | MySQL实现 | Universal实现 |
|------|-----------|---------------|
| 驱动 | pymysql/aiomysql | SQLAlchemy |
| 性能 | 原生驱动，更高性能 | ORM抽象，通用性更好 |
| 功能 | MySQL专用优化 | 多数据库支持 |
| 复杂度 | 相对简单 | 功能更丰富 |
| 维护性 | MySQL特定 | 统一接口 |

选择建议：
- 需要最高性能且只使用MySQL：选择MySQL实现
- 需要多数据库支持或复杂ORM功能：选择Universal实现

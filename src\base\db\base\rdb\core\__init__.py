"""
RDB核心模块

提供数据库抽象层的核心组件：
- 统一的类型定义
- 标准化的数据模型
- 完整的异常体系
- 抽象的接口定义

这是整个RDB抽象层的基础，确保不同数据库实现的一致性。
"""

# 导入核心类型
from .types import (
    # 基础类型
    DatabaseValue, DatabaseRecord, DatabaseRecords, QueryParameters, ConnectionString,
    
    # 枚举类型
    DatabaseType, SortOrder, ComparisonOperator, LogicalOperator, JoinType,
    TransactionIsolation, ConnectionPoolStatus,
    
    # 协议类型
    DatabaseConnection, DatabaseTransaction, DatabasePool,
    
    # 泛型类型
    QueryResult,
    
    # 回调类型
    ConnectionCallback, AsyncConnectionCallback,
    QueryCallback, AsyncQueryCallback,
    ErrorCallback, AsyncErrorCallback,
    
    # 配置类型
    PoolConfig, SSLConfig,
    
    # 工厂类型
    DatabaseClientFactory as DatabaseClientFactoryType,
    AsyncDatabaseClientFactory,
    
    # 上下文管理器类型
    DatabaseContextManager, TransactionContextManager,
)

# 导入核心模型
from .models import (
    # 过滤器模型
    QueryFilter, QueryFilterGroup,
    
    # 查询组件模型
    QuerySort, QueryJoin, QueryAggregation, QueryGroupBy,
    
    # 请求模型
    QueryRequest, InsertRequest, UpdateRequest, DeleteRequest,
    
    # 响应模型
    QueryResponse, OperationResponse,
    
    # 配置模型
    ConnectionConfig,
)

# 导入异常体系
from .exceptions import (
    # 基础异常
    RDBError,
    
    # 连接异常
    ConnectionError, ConnectionTimeoutError, ConnectionPoolError,
    AuthenticationError, DatabaseNotFoundError,
    
    # 查询异常
    QueryError, SQLSyntaxError, QueryTimeoutError,
    TableNotFoundError, ColumnNotFoundError,
    
    # 数据异常
    DataError, ValidationError, IntegrityError,
    UniqueConstraintError, ForeignKeyConstraintError,
    CheckConstraintError, NotNullConstraintError,
    
    # 事务异常
    TransactionError, TransactionRollbackError,
    DeadlockError, SerializationError,
    
    # 配置异常
    ConfigurationError, UnsupportedDatabaseError, UnsupportedFeatureError,
    
    # 操作异常
    OperationError, BulkOperationError, MigrationError,
    
    # 工具函数
    wrap_database_error,
)

# 导入核心接口
from .interfaces import (
    # 核心接口
    DatabaseClient, QueryBuilder, DatabaseDialect,
    ConnectionPoolManager, ResultAdapter, DatabaseClientFactory,
)

# 版本信息
__version__ = "1.0.0"

# 导出所有公共API
__all__ = [
    # ==================== 类型定义 ====================
    # 基础类型
    "DatabaseValue", "DatabaseRecord", "DatabaseRecords", 
    "QueryParameters", "ConnectionString",
    
    # 枚举类型
    "DatabaseType", "SortOrder", "ComparisonOperator", "LogicalOperator", 
    "JoinType", "TransactionIsolation", "ConnectionPoolStatus",
    
    # 协议类型
    "DatabaseConnection", "DatabaseTransaction", "DatabasePool",
    
    # 泛型类型
    "QueryResult",
    
    # 回调类型
    "ConnectionCallback", "AsyncConnectionCallback",
    "QueryCallback", "AsyncQueryCallback",
    "ErrorCallback", "AsyncErrorCallback",
    
    # 配置类型
    "PoolConfig", "SSLConfig",
    
    # 工厂类型
    "DatabaseClientFactoryType", "AsyncDatabaseClientFactory",
    
    # 上下文管理器类型
    "DatabaseContextManager", "TransactionContextManager",
    
    # ==================== 数据模型 ====================
    # 过滤器模型
    "QueryFilter", "QueryFilterGroup",
    
    # 查询组件模型
    "QuerySort", "QueryJoin", "QueryAggregation", "QueryGroupBy",
    
    # 请求模型
    "QueryRequest", "InsertRequest", "UpdateRequest", "DeleteRequest",
    
    # 响应模型
    "QueryResponse", "OperationResponse",
    
    # 配置模型
    "ConnectionConfig",
    
    # ==================== 异常体系 ====================
    # 基础异常
    "RDBError",
    
    # 连接异常
    "ConnectionError", "ConnectionTimeoutError", "ConnectionPoolError",
    "AuthenticationError", "DatabaseNotFoundError",
    
    # 查询异常
    "QueryError", "SQLSyntaxError", "QueryTimeoutError",
    "TableNotFoundError", "ColumnNotFoundError",
    
    # 数据异常
    "DataError", "ValidationError", "IntegrityError",
    "UniqueConstraintError", "ForeignKeyConstraintError",
    "CheckConstraintError", "NotNullConstraintError",
    
    # 事务异常
    "TransactionError", "TransactionRollbackError",
    "DeadlockError", "SerializationError",
    
    # 配置异常
    "ConfigurationError", "UnsupportedDatabaseError", "UnsupportedFeatureError",
    
    # 操作异常
    "OperationError", "BulkOperationError", "MigrationError",
    
    # 工具函数
    "wrap_database_error",
    
    # ==================== 核心接口 ====================
    "DatabaseClient", "QueryBuilder", "DatabaseDialect",
    "ConnectionPoolManager", "ResultAdapter", "DatabaseClientFactory",
]

# 模块元信息
__author__ = "HSBC Knowledge Team"
__description__ = "RDB数据库抽象层核心模块"
__license__ = "MIT"

import os
import logging
from typing import Any
import re

logger = logging.getLogger(__name__)

from langchain.prompts import (
    PromptTemplate,
    HumanMessagePromptTemplate,
    SystemMessagePromptTemplate,
    ChatPromptTemplate,
)

# 使用相对路径，基于当前脚本位置定位模板目录
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
TEMPLATES_ROOT_PATH = os.path.join(BASE_DIR, "templates")

def _load_template(template_name: str) -> str:
    """
    Loads a template from a file.

    Args:
        template_name (str): The name of the template to load.

    Returns:
        str: The content of the template.
    """
    
    file_name = f"template_{template_name}.txt"
    template_path = os.path.join(TEMPLATES_ROOT_PATH, file_name)
    
    try:
        with open(template_path, "r", encoding="utf-8") as file:
            template = file.read()
        logger.info(f"Template {template_name} loaded successfully.")
        return template
    except FileNotFoundError:
        logger.error(f"Template file not found: {template_path}")
        raise
    except Exception as e:
        logger.error(f"Error loading template {template_name}: {e}")
        raise

def _extract_input_variables(template: str) -> Any:
        pattern = r'\{(.*?)\}'
        placeholders = re.findall(pattern, template)
        return placeholders

def get_prompt(template_name: str = None, template: str = None) -> ChatPromptTemplate:
    """
    Creates a ChatPromptTemplate from a template.

    Supports both single template and system+user dual template structure:
    - Single template: template_{name}.txt
    - Dual template: template_{name}_system.txt + template_{name}_user.txt

    Args:
        template_name (str): The name of the template to load.
        template (str): The content of the template.

    Returns:
        ChatPromptTemplate: The prompt
    """
    if template_name:
        # Try to load system+user dual template structure first
        system_file = f"template_{template_name}_system.txt"
        user_file = f"template_{template_name}_user.txt"
        system_path = os.path.join(TEMPLATES_ROOT_PATH, system_file)
        user_path = os.path.join(TEMPLATES_ROOT_PATH, user_file)

        if os.path.exists(system_path) and os.path.exists(user_path):
            # Load dual template structure
            try:
                with open(system_path, "r", encoding="utf-8") as f:
                    system_template = f.read()
                with open(user_path, "r", encoding="utf-8") as f:
                    user_template = f.read()

                # Extract input variables from both templates
                system_variables = _extract_input_variables(system_template)
                user_variables = _extract_input_variables(user_template)
                all_variables = list(set(system_variables + user_variables))

                # Create system and user message templates
                system_message_template = SystemMessagePromptTemplate(
                    prompt=PromptTemplate(
                        template=system_template,
                        input_variables=system_variables,
                    )
                )

                user_message_template = HumanMessagePromptTemplate(
                    prompt=PromptTemplate(
                        template=user_template,
                        input_variables=user_variables,
                    )
                )

                combined_prompt_template = ChatPromptTemplate.from_messages(
                    [system_message_template, user_message_template]
                )

                logger.info(f"Dual template {template_name} (system+user) loaded successfully.")
                return combined_prompt_template

            except Exception as e:
                logger.warning(f"Failed to load dual template {template_name}: {e}, falling back to single template")

        # Fall back to single template
        template = _load_template(template_name)

    # Single template processing
    input_variables = _extract_input_variables(template)

    human_message_prompt_template = HumanMessagePromptTemplate(
        prompt=PromptTemplate(
            template=template,
            input_variables=input_variables,
        )
    )

    combined_prompt_template = ChatPromptTemplate.from_messages(
        [human_message_prompt_template]
    )

    return combined_prompt_template

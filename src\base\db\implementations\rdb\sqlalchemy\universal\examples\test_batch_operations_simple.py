"""
UniversalSQLAlchemyClient 批量操作简单测试

快速验证批量操作功能的简化版测试
"""

import asyncio
import sys
import os
import time

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '../../../../../../..')
sys.path.insert(0, os.path.abspath(project_root))
from service import get_client, cleanup
from base.db.base.rdb import InsertRequest


async def quick_batch_test():
    """快速批量操作测试"""
    print("🚀 快速批量操作测试")
    print("="*40)
    
    # 获取客户端
    rdb_client = await get_client("database.rdbs.mysql")
    
    # 清理可能存在的测试数据
    await rdb_client.aexecute("DELETE FROM model_info WHERE col_code LIKE 'quick#test#%'")
    
    # 1. 测试批量插入 - 展示三种调用方式
    print("\n1. 批量插入测试 - 三种调用方式:")

    # 准备测试数据
    test_data_1 = []
    test_data_2 = []
    test_data_3 = []

    for i in range(1500):
        test_data_1.append({
            'col_code': f'quick#method1#{i:03d}',
            'table_code': f'quick_test.method1_{i}',
            'col_name': f'method1_column_{i}',
            'col_name_cn': f'方式1_字段_{i}',
            'col_desc': f'方式1测试描述_{i}',
            'col_type': 'STRING',
            'col_data_example': f'方式1示例_{i}'
        })

        test_data_2.append({
            'col_code': f'quick#method2#{i:03d}',
            'table_code': f'quick_test.method2_{i}',
            'col_name': f'method2_column_{i}',
            'col_name_cn': f'方式2_字段_{i}',
            'col_desc': f'方式2测试描述_{i}',
            'col_type': 'STRING',
            'col_data_example': f'方式2示例_{i}'
        })

        test_data_3.append({
            'col_code': f'quick#method3#{i:03d}',
            'table_code': f'quick_test.method3_{i}',
            'col_name': f'method3_column_{i}',
            'col_name_cn': f'方式3_字段_{i}',
            'col_desc': f'方式3测试描述_{i}',
            'col_type': 'STRING',
            'col_data_example': f'方式3示例_{i}'
        })

    # 方式1: 使用 InsertRequest 对象
    print("   方式1: 使用 InsertRequest 对象")
    request = InsertRequest(table="model_info", data=test_data_1)
    start_time = time.time()
    result1 = await rdb_client.abatch_insert(request, batch_size=5)
    time1 = time.time() - start_time
    print(f"      ✅ 插入 {result1.affected_rows} 条记录，耗时 {time1:.3f}秒")

    # 方式2: 使用字典
    print("   方式2: 使用字典参数")
    start_time = time.time()
    result2 = await rdb_client.abatch_insert({"table": "model_info", "data": test_data_2}, batch_size=5)
    time2 = time.time() - start_time
    print(f"      ✅ 插入 {result2.affected_rows} 条记录，耗时 {time2:.3f}秒")

    # 方式3: 使用关键字参数（推荐）
    print("   方式3: 使用关键字参数（推荐）")
    start_time = time.time()
    result3 = await rdb_client.abatch_insert(table="model_info", data=test_data_3, batch_size=5)
    time3 = time.time() - start_time
    print(f"      ✅ 插入 {result3.affected_rows} 条记录，耗时 {time3:.3f}秒")

    total_inserted = result1.affected_rows + result2.affected_rows + result3.affected_rows
    print(f"   📊 总计插入 {total_inserted} 条记录")
    
    # 2. 测试批量更新 - 传统格式（向后兼容）
    print("\n2. 批量更新测试 - 传统格式:")
    updates_traditional = []
    for i in range(3):
        updates_traditional.append({
            "data": {"col_name_cn": f"已更新_方式1_字段_{i}"},
            "filters": {"col_code": f"quick#method1#{i:03d}"}
        })

    start_time = time.time()
    update_result1 = await rdb_client.abatch_update("model_info", updates_traditional, batch_size=3)
    update_time1 = time.time() - start_time

    print(f"   ✅ 传统格式更新 {update_result1.affected_rows} 条记录，耗时 {update_time1:.3f}秒")

    # 2.2 测试批量更新 - 新格式（使用where）
    print("\n   批量更新测试 - 新格式:")
    updates_new = []
    for i in range(3, 6):  # 更新另外3条记录
        updates_new.append({
            "data": {"col_name_cn": f"新格式更新_方式1_字段_{i}"},
            "where": {"col_code": f"quick#method1#{i:03d}"}
        })

    start_time = time.time()
    update_result2 = await rdb_client.abatch_update("model_info", updates_new, batch_size=3)
    update_time2 = time.time() - start_time

    print(f"   ✅ 新格式更新 {update_result2.affected_rows} 条记录，耗时 {update_time2:.3f}秒")

    # 2.3 测试批量更新 - 复杂条件
    print("\n   批量更新测试 - 复杂条件:")
    updates_complex = [
        {
            "data": {"col_desc": "复杂条件更新 - 包含method1"},
            "where": {"col_code": {"$like": "%method1%"}, "col_name_cn": {"$like": "%已更新%"}}
        }
    ]

    start_time = time.time()
    update_result3 = await rdb_client.abatch_update("model_info", updates_complex, batch_size=1)
    update_time3 = time.time() - start_time

    print(f"   ✅ 复杂条件更新 {update_result3.affected_rows} 条记录，耗时 {update_time3:.3f}秒")

    # 3. 测试批量删除 - 简单ID删除
    print("\n3. 批量删除测试 - 简单ID删除:")
    conditions_simple = [{"col_code": f"quick#method2#{i:03d}"} for i in range(3)]  # 删除方式2的数据

    start_time = time.time()
    delete_result1 = await rdb_client.abatch_delete("model_info", conditions_simple, batch_size=3)
    delete_time1 = time.time() - start_time

    print(f"   ✅ 简单ID删除 {delete_result1.affected_rows} 条记录，耗时 {delete_time1:.3f}秒")

    # 3.2 测试批量删除 - 复杂条件删除
    print("\n   批量删除测试 - 复杂条件:")
    conditions_complex = [
        {"col_code": {"$like": "%method3%"}},  # 删除所有method3的记录
        {"col_name_cn": {"$like": "%新格式更新%"}}  # 删除刚才新格式更新的记录
    ]

    start_time = time.time()
    delete_result2 = await rdb_client.abatch_delete("model_info", conditions_complex, batch_size=2)
    delete_time2 = time.time() - start_time

    print(f"   ✅ 复杂条件删除 {delete_result2.affected_rows} 条记录，耗时 {delete_time2:.3f}秒")
    
    # 4. 验证结果
    print("\n4. 验证结果:")
    result = await rdb_client.afetch_all(
        "SELECT col_code, col_name_cn FROM model_info WHERE col_code LIKE 'quick#method%' ORDER BY col_code"
    )

    print(f"   ✅ 剩余 {len(result.data)} 条记录:")
    for row in result.data:
        print(f"      {row['col_code']} - {row['col_name_cn']}")

    # 5. 清理
    print("\n5. 清理测试数据:")
    cleanup_result = await rdb_client.aexecute("DELETE FROM model_info WHERE col_code LIKE 'quick#method%'")
    print(f"   ✅ 清理了 {cleanup_result.affected_rows} 条记录")

    print(f"\n🎉 快速测试完成！")
    print(f"   - 批量插入: {total_inserted} 条 (三种方式)")
    print(f"   - 批量更新: {update_result1.affected_rows + update_result2.affected_rows + update_result3.affected_rows} 条 (三种格式)")
    print(f"   - 批量删除: {delete_result1.affected_rows + delete_result2.affected_rows} 条 (两种方式)")


async def performance_demo():
    """性能演示"""
    print("\n" + "="*40)
    print("📊 性能演示")
    print("="*40)
    
    rdb_client = await get_client("database.rdbs.mysql")
    
    # 准备测试数据
    batch_data = []
    individual_data = []
    
    for i in range(50):
        batch_data.append({
            'col_code': f'perf#batch#{i:03d}',
            'table_code': f'perf_test.batch_{i}',
            'col_name': f'batch_column_{i}',
            'col_name_cn': f'批量字段_{i}',
            'col_desc': f'批量测试_{i}',
            'col_type': 'STRING',
            'col_data_example': f'批量数据_{i}'
        })
        
        individual_data.append({
            'col_code': f'perf#individual#{i:03d}',
            'table_code': f'perf_test.individual_{i}',
            'col_name': f'individual_column_{i}',
            'col_name_cn': f'逐条字段_{i}',
            'col_desc': f'逐条测试_{i}',
            'col_type': 'STRING',
            'col_data_example': f'逐条数据_{i}'
        })
    
    # 批量插入测试
    print(f"\n批量插入 {len(batch_data)} 条记录...")
    request = InsertRequest(table="model_info", data=batch_data)
    start_time = time.time()
    batch_result = await rdb_client.abatch_insert(request)
    batch_time = time.time() - start_time
    
    print(f"   批量插入: {batch_time:.3f}秒")
    
    # 逐条插入测试
    print(f"\n逐条插入 {len(individual_data)} 条记录...")
    start_time = time.time()
    individual_count = 0
    for data in individual_data:
        single_request = InsertRequest(table="model_info", data=data)
        result = await rdb_client.ainsert(single_request)
        individual_count += result.affected_rows
    individual_time = time.time() - start_time
    
    print(f"   逐条插入: {individual_time:.3f}秒")
    
    # 性能对比
    speedup = individual_time / batch_time if batch_time > 0 else 0
    print(f"\n📈 性能对比:")
    print(f"   - 批量插入: {batch_time:.3f}秒 ({len(batch_data)/batch_time:.0f}条/秒)")
    print(f"   - 逐条插入: {individual_time:.3f}秒 ({len(individual_data)/individual_time:.0f}条/秒)")
    print(f"   - 性能提升: {speedup:.1f}倍")
    
    # 清理
    await rdb_client.aexecute("DELETE FROM model_info WHERE col_code LIKE 'perf#%'")
    print(f"   ✅ 性能测试数据已清理")


async def demo_new_batch_operations():
    """演示新的批量操作功能"""
    print("\n" + "="*40)
    print("🆕 新的批量操作功能演示")
    print("="*40)

    rdb_client = await get_client("database.rdbs.mysql")

    # 清理可能存在的演示数据
    await rdb_client.aexecute("DELETE FROM model_info WHERE col_code LIKE 'demo#%'")

    # 准备测试数据
    demo_data = []
    for i in range(10):
        demo_data.append({
            'col_code': f'demo#batch#{i:03d}',
            'table_code': f'demo_table_{i}',
            'col_name': f'demo_column_{i}',
            'col_name_cn': f'演示字段_{i}',
            'col_desc': f'批量操作演示_{i}',
            'col_type': 'STRING' if i % 2 == 0 else 'NUMBER',
            'col_data_example': f'演示数据_{i}'
        })

    # 插入测试数据
    await rdb_client.abatch_insert(table="model_info", data=demo_data)
    print(f"✅ 插入了 {len(demo_data)} 条测试数据")

    print("\n📝 批量更新的多种格式:")

    # 1. 传统格式（向后兼容）
    print("\n1️⃣ 传统格式 - filters:")
    updates_traditional = [
        {"data": {"col_desc": "传统格式更新"}, "filters": {"col_code": "demo#batch#000"}},
        {"data": {"col_desc": "传统格式更新"}, "filters": {"col_code": "demo#batch#001"}}
    ]
    result1 = await rdb_client.abatch_update("model_info", updates_traditional)
    print(f"   ✅ 传统格式更新 {result1.affected_rows} 条记录")

    # 2. 新格式 - where
    print("\n2️⃣ 新格式 - where:")
    updates_new = [
        {"data": {"col_desc": "新格式更新"}, "where": {"col_code": "demo#batch#002"}},
        {"data": {"col_desc": "新格式更新"}, "where": {"col_code": "demo#batch#003"}}
    ]
    result2 = await rdb_client.abatch_update("model_info", updates_new)
    print(f"   ✅ 新格式更新 {result2.affected_rows} 条记录")

    # 3. 复杂条件更新
    print("\n3️⃣ 复杂条件更新 - MongoDB风格操作符:")
    updates_complex = [
        {
            "data": {"col_desc": "复杂条件更新 - STRING类型"},
            "where": {"col_type": "STRING", "col_code": {"$like": "%batch%"}}
        },
        {
            "data": {"col_desc": "复杂条件更新 - NUMBER类型"},
            "where": {"col_type": "NUMBER"}
        }
    ]
    result3 = await rdb_client.abatch_update("model_info", updates_complex)
    print(f"   ✅ 复杂条件更新 {result3.affected_rows} 条记录")

    print("\n📝 批量删除的多种格式:")

    # 1. 简单ID删除
    print("\n1️⃣ 简单ID删除:")
    delete_conditions_simple = [
        {"col_code": "demo#batch#000"},
        {"col_code": "demo#batch#001"}
    ]
    result4 = await rdb_client.abatch_delete("model_info", delete_conditions_simple)
    print(f"   ✅ 简单ID删除 {result4.affected_rows} 条记录")

    # 2. 复杂条件删除
    print("\n2️⃣ 复杂条件删除 - MongoDB风格操作符:")
    delete_conditions_complex = [
        {"col_type": "STRING", "col_desc": {"$like": "%复杂条件更新%"}},
        {"col_code": {"$like": "%batch#00[2-3]%"}}
    ]
    result5 = await rdb_client.abatch_delete("model_info", delete_conditions_complex)
    print(f"   ✅ 复杂条件删除 {result5.affected_rows} 条记录")

    # 3. 使用QueryFilterGroup的删除
    print("\n3️⃣ QueryFilterGroup删除:")
    from base.db.base.rdb import QueryFilterGroup, QueryFilter, ComparisonOperator, LogicalOperator

    complex_filter = QueryFilterGroup(
        operator=LogicalOperator.OR,
        filters=[
            QueryFilter("col_type", ComparisonOperator.EQ, "NUMBER"),
            QueryFilter("col_code", ComparisonOperator.LIKE, "%batch#00[4-9]%")
        ]
    )
    delete_conditions_qfg = [complex_filter]
    result6 = await rdb_client.abatch_delete("model_info", delete_conditions_qfg)
    print(f"   ✅ QueryFilterGroup删除 {result6.affected_rows} 条记录")

    # 验证剩余数据
    remaining = await rdb_client.afetch_all(
        "SELECT col_code, col_type, col_desc FROM model_info WHERE col_code LIKE 'demo#batch#%' ORDER BY col_code"
    )
    print(f"\n📊 剩余 {len(remaining.data)} 条记录:")
    for row in remaining.data:
        print(f"   {row['col_code']} - {row['col_type']} - {row['col_desc']}")

    print(f"\n🎯 新功能总结:")
    print(f"   - 批量更新支持三种格式: filters(传统), where(新), 复杂条件")
    print(f"   - 批量删除支持三种格式: 简单ID, 复杂条件, QueryFilterGroup")
    print(f"   - MongoDB风格操作符: $like, $in, $gt, $lt 等")
    print(f"   - 完全向后兼容，原有代码无需修改")

    # 清理演示数据
    cleanup_result = await rdb_client.aexecute("DELETE FROM model_info WHERE col_code LIKE 'demo#%'")
    print(f"   - 清理了 {cleanup_result.affected_rows} 条演示数据")


async def demo_mongodb_style_operators():
    """演示MongoDB风格操作符"""
    print("\n" + "="*40)
    print("🔍 MongoDB风格操作符演示")
    print("="*40)

    rdb_client = await get_client("database.rdbs.mysql")

    # 清理并准备测试数据
    await rdb_client.aexecute("DELETE FROM model_info WHERE col_code LIKE 'mongo#%'")

    test_data = []
    for i in range(20):
        test_data.append({
            'col_code': f'mongo#test#{i:03d}',
            'table_code': f'mongo_table_{i}',
            'col_name': f'mongo_column_{i}',
            'col_name_cn': f'MongoDB字段_{i}',
            'col_desc': f'年龄{18 + i}岁的用户' if i < 10 else f'高级用户_{i}',
            'col_type': 'STRING' if i % 3 == 0 else ('NUMBER' if i % 3 == 1 else 'DATE'),
            'col_data_example': f'数据示例_{i}'
        })

    await rdb_client.abatch_insert(table="model_info", data=test_data)
    print(f"✅ 插入了 {len(test_data)} 条测试数据")

    print("\n📝 MongoDB风格操作符测试:")

    # 1. 等值和不等值
    print("\n1️⃣ 等值和不等值操作符:")
    updates_eq = [
        {"data": {"col_desc": "等值更新 - STRING"}, "where": {"col_type": {"$eq": "STRING"}}},
        {"data": {"col_desc": "不等值更新 - 非INTEGER"}, "where": {"col_type": {"$ne": "INTEGER"}}}
    ]
    result1 = await rdb_client.abatch_update("model_info", updates_eq[:1])  # 只执行第一个
    print(f"   ✅ $eq 操作符更新 {result1.affected_rows} 条记录")

    # 2. 模糊匹配
    print("\n2️⃣ 模糊匹配操作符:")
    updates_like = [
        {"data": {"col_desc": "包含'用户'的记录"}, "where": {"col_desc": {"$like": "%用户%"}}}
    ]
    result2 = await rdb_client.abatch_update("model_info", updates_like)
    print(f"   ✅ $like 操作符更新 {result2.affected_rows} 条记录")

    # 3. IN操作符
    print("\n3️⃣ IN操作符:")
    updates_in = [
        {"data": {"col_desc": "类型在指定列表中"}, "where": {"col_type": {"$in": ["INTEGER", "BOOLEAN"]}}}
    ]
    result3 = await rdb_client.abatch_update("model_info", updates_in)
    print(f"   ✅ $in 操作符更新 {result3.affected_rows} 条记录")

    # 4. 删除操作 - 复杂条件组合
    print("\n4️⃣ 复杂条件删除:")
    delete_conditions = [
        # 删除包含"高级用户"且类型为BOOLEAN的记录
        {"col_desc": {"$like": "%高级用户%"}, "col_type": "BOOLEAN"},
        # 删除特定编号范围的记录
        {"col_code": {"$like": "%test#01[0-5]%"}}
    ]
    result4 = await rdb_client.abatch_delete("model_info", delete_conditions)
    print(f"   ✅ 复杂条件删除 {result4.affected_rows} 条记录")

    # 验证结果
    remaining = await rdb_client.afetch_all(
        "SELECT col_code, col_type, col_desc FROM model_info WHERE col_code LIKE 'mongo#%' ORDER BY col_code LIMIT 10"
    )
    print(f"\n📊 剩余记录示例 (前10条):")
    for row in remaining.data:
        print(f"   {row['col_code']} - {row['col_type']} - {row['col_desc']}")

    # 清理
    cleanup_result = await rdb_client.aexecute("DELETE FROM model_info WHERE col_code LIKE 'mongo#%'")
    print(f"\n🧹 清理了 {cleanup_result.affected_rows} 条测试数据")


async def main():
    """主函数"""
    try:
        await quick_batch_test()
        await demo_new_batch_operations()
        await demo_mongodb_style_operators()
        await performance_demo()

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

    finally:
        await cleanup()


if __name__ == "__main__":
    asyncio.run(main())

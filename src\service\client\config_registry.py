"""
企业级配置注册表和解析器

解决当前隐式配置解析的风险，提供显式、可追踪的配置管理
"""

import logging
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Tuple, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)


class DatabaseType(Enum):
    """数据库类型枚举 - 类型安全"""
    MYSQL = "mysql"
    PGVECTOR = "pgvector"
    POSTGRESQL = "postgresql"
    
    @classmethod
    def from_string(cls, db_type: str) -> 'DatabaseType':
        """从字符串安全转换"""
        normalized = db_type.lower().strip()
        
        # 支持别名
        aliases = {
            'mysql': cls.MYSQL,
            'pgvector': cls.PGVECTOR,
            'pg': cls.PGVECTOR,
            'postgres': cls.POSTGRESQL,
            'postgresql': cls.POSTGRESQL,
            'vector': cls.PGVECTOR,
        }
        
        if normalized in aliases:
            return aliases[normalized]
        
        raise ValueError(f"不支持的数据库类型: {db_type}. 支持的类型: {list(aliases.keys())}")


class ServicePriority(Enum):
    """服务优先级枚举 - 类型安全"""
    HIGH = "high"
    STANDARD = "standard"
    LOW = "low"
    
    @classmethod
    def from_string(cls, priority: str) -> 'ServicePriority':
        """从字符串安全转换"""
        normalized = priority.lower().strip()
        
        # 支持别名
        aliases = {
            'high': cls.HIGH,
            'critical': cls.HIGH,
            'urgent': cls.HIGH,
            'standard': cls.STANDARD,
            'normal': cls.STANDARD,
            'medium': cls.STANDARD,
            'low': cls.LOW,
            'background': cls.LOW,
            'batch': cls.LOW,
        }
        
        if normalized in aliases:
            return aliases[normalized]
        
        logger.warning(f"未知优先级 '{priority}'，使用默认优先级 'standard'")
        return cls.STANDARD


class ConfigPath(Enum):
    """配置路径枚举 - 显式配置路径"""
    # MySQL 配置路径
    MYSQL_HIGH = "database.composed.mysql_high"
    MYSQL_STANDARD = "database.composed.mysql_standard"
    MYSQL_LOW = "database.composed.mysql_low"
    
    # PGVector 配置路径
    PGVECTOR_HIGH = "database.composed.pgvector_high"
    PGVECTOR_STANDARD = "database.composed.pgvector_standard"
    PGVECTOR_LOW = "database.composed.pgvector_low"
    
    # PostgreSQL 配置路径
    POSTGRESQL_HIGH = "database.composed.postgresql_high"
    POSTGRESQL_STANDARD = "database.composed.postgresql_standard"
    POSTGRESQL_LOW = "database.composed.postgresql_low"


@dataclass
class ConfigResolution:
    """配置解析结果 - 用于追踪和审计"""
    original_request: str
    db_type: DatabaseType
    priority: ServicePriority
    resolved_path: ConfigPath
    resolution_method: str
    timestamp: datetime
    is_fallback: bool = False
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


class EnterpriseConfigRegistry:
    """
    企业级配置注册表
    
    提供显式、类型安全、可追踪的配置解析
    """
    
    # 配置映射表 - 显式定义所有支持的配置组合
    _CONFIG_MAPPING: Dict[Tuple[DatabaseType, ServicePriority], ConfigPath] = {
        # MySQL 配置
        (DatabaseType.MYSQL, ServicePriority.HIGH): ConfigPath.MYSQL_HIGH,
        (DatabaseType.MYSQL, ServicePriority.STANDARD): ConfigPath.MYSQL_STANDARD,
        (DatabaseType.MYSQL, ServicePriority.LOW): ConfigPath.MYSQL_LOW,
        
        # PGVector 配置
        (DatabaseType.PGVECTOR, ServicePriority.HIGH): ConfigPath.PGVECTOR_HIGH,
        (DatabaseType.PGVECTOR, ServicePriority.STANDARD): ConfigPath.PGVECTOR_STANDARD,
        (DatabaseType.PGVECTOR, ServicePriority.LOW): ConfigPath.PGVECTOR_LOW,
        
        # PostgreSQL 配置
        (DatabaseType.POSTGRESQL, ServicePriority.HIGH): ConfigPath.POSTGRESQL_HIGH,
        (DatabaseType.POSTGRESQL, ServicePriority.STANDARD): ConfigPath.POSTGRESQL_STANDARD,
        (DatabaseType.POSTGRESQL, ServicePriority.LOW): ConfigPath.POSTGRESQL_LOW,
    }
    
    # 向后兼容的配置路径映射
    _LEGACY_PATH_MAPPING: Dict[str, Tuple[DatabaseType, ServicePriority]] = {
        "database.rdbs.mysql": (DatabaseType.MYSQL, ServicePriority.STANDARD),
        "database.vdbs.pgvector": (DatabaseType.PGVECTOR, ServicePriority.STANDARD),
        "database.rdbs.postgresql": (DatabaseType.POSTGRESQL, ServicePriority.STANDARD),
    }
    
    @classmethod
    def resolve_config(cls, 
                      config_request: str, 
                      priority: Optional[str] = None) -> ConfigResolution:
        """
        解析配置请求为具体的配置路径
        
        Args:
            config_request: 配置请求（可能是传统路径或数据库类型）
            priority: 可选的优先级
            
        Returns:
            ConfigResolution: 配置解析结果
            
        Raises:
            ValueError: 当配置无法解析时
        """
        timestamp = datetime.now()
        warnings = []
        
        try:
            # 尝试解析为数据库类型 + 优先级
            if cls._is_database_type(config_request):
                return cls._resolve_by_type_and_priority(
                    config_request, priority, timestamp, warnings
                )
            
            # 尝试解析为传统配置路径
            if cls._is_legacy_config_path(config_request):
                return cls._resolve_legacy_path(
                    config_request, priority, timestamp, warnings
                )
            
            # 尝试解析为显式配置路径
            if cls._is_explicit_config_path(config_request):
                return cls._resolve_explicit_path(
                    config_request, priority, timestamp, warnings
                )
            
            # 无法解析
            raise ValueError(
                f"无法解析配置请求: '{config_request}'. "
                f"支持的格式: 数据库类型('mysql'), 传统路径('database.rdbs.mysql'), "
                f"或显式路径('database.composed.mysql_high')"
            )
            
        except Exception as e:
            logger.error(f"配置解析失败: {config_request}, priority={priority}, error={e}")
            raise
    
    @classmethod
    def _is_database_type(cls, config_request: str) -> bool:
        """检查是否为数据库类型"""
        try:
            DatabaseType.from_string(config_request)
            return True
        except ValueError:
            return False
    
    @classmethod
    def _is_legacy_config_path(cls, config_request: str) -> bool:
        """检查是否为传统配置路径"""
        return config_request in cls._LEGACY_PATH_MAPPING
    
    @classmethod
    def _is_explicit_config_path(cls, config_request: str) -> bool:
        """检查是否为显式配置路径"""
        try:
            ConfigPath(config_request)
            return True
        except ValueError:
            return False
    
    @classmethod
    def _resolve_by_type_and_priority(cls, 
                                    db_type_str: str, 
                                    priority_str: Optional[str],
                                    timestamp: datetime,
                                    warnings: List[str]) -> ConfigResolution:
        """通过数据库类型和优先级解析"""
        db_type = DatabaseType.from_string(db_type_str)
        priority = ServicePriority.from_string(priority_str or 'standard')
        
        key = (db_type, priority)
        if key not in cls._CONFIG_MAPPING:
            raise ValueError(f"不支持的配置组合: {db_type.value}:{priority.value}")
        
        config_path = cls._CONFIG_MAPPING[key]
        
        return ConfigResolution(
            original_request=db_type_str,
            db_type=db_type,
            priority=priority,
            resolved_path=config_path,
            resolution_method="type_and_priority",
            timestamp=timestamp,
            warnings=warnings
        )
    
    @classmethod
    def _resolve_legacy_path(cls,
                           legacy_path: str,
                           priority_str: Optional[str],
                           timestamp: datetime,
                           warnings: List[str]) -> ConfigResolution:
        """解析传统配置路径"""
        db_type, default_priority = cls._LEGACY_PATH_MAPPING[legacy_path]
        
        # 如果提供了优先级参数，使用参数值；否则使用默认值
        if priority_str:
            priority = ServicePriority.from_string(priority_str)
            if priority != default_priority:
                warnings.append(f"优先级参数 '{priority_str}' 覆盖了传统路径的默认优先级")
        else:
            priority = default_priority
        
        key = (db_type, priority)
        if key not in cls._CONFIG_MAPPING:
            raise ValueError(f"不支持的配置组合: {db_type.value}:{priority.value}")
        
        config_path = cls._CONFIG_MAPPING[key]
        
        return ConfigResolution(
            original_request=legacy_path,
            db_type=db_type,
            priority=priority,
            resolved_path=config_path,
            resolution_method="legacy_path",
            timestamp=timestamp,
            warnings=warnings
        )
    
    @classmethod
    def _resolve_explicit_path(cls,
                             explicit_path: str,
                             priority_str: Optional[str],
                             timestamp: datetime,
                             warnings: List[str]) -> ConfigResolution:
        """解析显式配置路径"""
        config_path = ConfigPath(explicit_path)
        
        # 从配置路径反推数据库类型和优先级
        db_type, priority = cls._extract_type_and_priority_from_path(config_path)
        
        # 检查优先级参数冲突
        if priority_str:
            requested_priority = ServicePriority.from_string(priority_str)
            if requested_priority != priority:
                raise ValueError(
                    f"优先级冲突: 显式路径 '{explicit_path}' 指定优先级为 '{priority.value}', "
                    f"但 priority 参数指定为 '{requested_priority.value}'"
                )
        
        return ConfigResolution(
            original_request=explicit_path,
            db_type=db_type,
            priority=priority,
            resolved_path=config_path,
            resolution_method="explicit_path",
            timestamp=timestamp,
            warnings=warnings
        )
    
    @classmethod
    def _extract_type_and_priority_from_path(cls, config_path: ConfigPath) -> Tuple[DatabaseType, ServicePriority]:
        """从配置路径提取数据库类型和优先级"""
        # 反向映射
        for (db_type, priority), path in cls._CONFIG_MAPPING.items():
            if path == config_path:
                return db_type, priority
        
        raise ValueError(f"无法从配置路径提取类型和优先级: {config_path.value}")
    
    @classmethod
    def list_supported_configurations(cls) -> Dict[str, List[str]]:
        """列出所有支持的配置组合"""
        result = {}
        for (db_type, priority), config_path in cls._CONFIG_MAPPING.items():
            db_name = db_type.value
            if db_name not in result:
                result[db_name] = []
            result[db_name].append(f"{priority.value} -> {config_path.value}")
        
        return result
    
    @classmethod
    def validate_configuration_exists(cls, config_path: ConfigPath) -> bool:
        """验证配置文件是否存在"""
        # 这里可以添加实际的文件存在性检查
        # 目前返回 True，实际实现中应该检查配置文件
        return True


# 便捷函数
def resolve_config_with_tracking(config_request: str, 
                               priority: Optional[str] = None) -> ConfigResolution:
    """
    便捷函数：解析配置并记录追踪信息
    
    Args:
        config_request: 配置请求
        priority: 可选优先级
        
    Returns:
        ConfigResolution: 配置解析结果
    """
    resolution = EnterpriseConfigRegistry.resolve_config(config_request, priority)
    
    # 记录配置解析日志
    logger.info(
        f"配置解析: {resolution.original_request} -> {resolution.resolved_path.value} "
        f"[{resolution.db_type.value}:{resolution.priority.value}] "
        f"via {resolution.resolution_method}"
    )
    
    # 记录警告
    for warning in resolution.warnings:
        logger.warning(f"配置解析警告: {warning}")
    
    return resolution

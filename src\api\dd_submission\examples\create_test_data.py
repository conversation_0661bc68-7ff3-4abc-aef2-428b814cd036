#!/usr/bin/env python3
"""
创建完整的测试数据集

目标：将测试通过率从83.3%提升到100%
重点解决"业务报送完整流程"测试失败的问题
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
import json

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '../../..')
sys.path.insert(0, src_dir)

from service import get_client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestDataCreator:
    """测试数据创建器"""
    
    def __init__(self):
        self.mysql_client = None
        self.current_time = datetime.now()
    
    async def initialize(self):
        """初始化数据库客户端"""
        self.mysql_client = await get_client('database.rdbs.mysql')
        logger.info("✅ MySQL客户端初始化成功")
    
    async def create_knowledge_base_data(self):
        """创建知识库基础数据"""
        logger.info("📚 创建知识库基础数据...")
        
        # 检查kb_knowledge表是否存在测试知识库
        check_sql = "SELECT COUNT(*) as count FROM kb_knowledge WHERE knowledge_id = :knowledge_id"
        result = await self.mysql_client.afetch_one(check_sql, {"knowledge_id": "test_knowledge_dd"})
        
        if hasattr(result, 'data'):
            count = result.data.get('count', 0)
        else:
            count = result.get('count', 0)
        
        if count == 0:
            # 创建测试知识库
            insert_sql = """
            INSERT INTO kb_knowledge (knowledge_id, knowledge_name, knowledge_desc, knowledge_type, doc_nums, models)
            VALUES (:knowledge_id, :knowledge_name, :knowledge_desc, :knowledge_type, :doc_nums, :models)
            """
            await self.mysql_client.aexecute(insert_sql, {
                "knowledge_id": "test_knowledge_dd",
                "knowledge_name": "DD测试知识库",
                "knowledge_desc": "用于DD系统测试的知识库",
                "knowledge_type": "DD",
                "doc_nums": 0,
                "models": json.dumps({"embedding": "text-embedding-ada-002", "llm": "gpt-3.5-turbo"})
            })
            logger.info("✅ 创建测试知识库成功")
        else:
            logger.info("ℹ️ 测试知识库已存在")
    
    async def create_report_data(self):
        """创建dd_report_data表测试数据"""
        logger.info("📊 创建dd_report_data表测试数据...")
        
        report_data = [
            {
                'knowledge_id': 'test_knowledge_dd',
                'version': 'G0107_ADS_release',
                'report_name': 'G0107 ADS发布版报表',
                'report_code': 'G0107_ADS',
                'report_layer': 'ADS',
                'report_department': 'DEPT_RRMS',
                'report_type': 'detail',
                'set': 'standard',
                'is_manual': 0,
                'create_time': self.current_time,
                'update_time': self.current_time
            },
            {
                'knowledge_id': 'test_knowledge_dd',
                'version': 'G0107_beta_v1.0',
                'report_name': 'G0107 Beta版本报表',
                'report_code': 'G0107_BETA',
                'report_layer': 'BDM',
                'report_department': 'DEPT_BUSINESS',
                'report_type': 'detail',
                'set': 'standard',
                'is_manual': 0,
                'create_time': self.current_time,
                'update_time': self.current_time
            },
            {
                'knowledge_id': 'test_knowledge_dd',
                'version': 'TEST_REPORT_001',
                'report_name': '测试报表001',
                'report_code': 'TEST_001',
                'report_layer': 'ADM',
                'report_department': 'DEPT_DATA_MGT',
                'report_type': 'index',
                'set': 'survey',
                'is_manual': 1,
                'create_time': self.current_time,
                'update_time': self.current_time
            }
        ]
        
        # 清理现有测试数据
        await self.mysql_client.aexecute(
            "DELETE FROM dd_report_data WHERE knowledge_id = :knowledge_id",
            {"knowledge_id": "test_knowledge_dd"}
        )

        # 插入新数据
        for data in report_data:
            insert_sql = """
            INSERT INTO dd_report_data (
                knowledge_id, version, report_name, report_code, report_layer,
                report_department, report_type, `set`, is_manual, create_time, update_time
            ) VALUES (:knowledge_id, :version, :report_name, :report_code, :report_layer,
                     :report_department, :report_type, :set, :is_manual, :create_time, :update_time)
            """
            await self.mysql_client.aexecute(insert_sql, data)
        
        logger.info(f"✅ 创建{len(report_data)}条dd_report_data记录")
    
    async def create_pre_distribution_data(self):
        """创建biz_dd_pre_distribution表测试数据"""
        logger.info("📋 创建biz_dd_pre_distribution表测试数据...")
        
        pre_data = [
            {
                'submission_id': 'SUB_G0107_ADS_001',
                'submission_type': 'SUBMISSION',
                'report_type': 'detail',
                'set': 'standard',
                'version': 'G0107_ADS_release',
                'dr01': 'ADS',
                'dr02': 'G0107客户信息需求文档',
                'dr03': '客户管理',
                'dr04': 'CUST_001',
                'dr05': 'customer_info_table',
                'dr06': '客户信息表',
                'dr07': 'CUST_INFO_001',
                'dr08': '全量',
                'dr09': '客户信息表',
                'dr10': 'CUST_INFO_FIELD_001',
                'dr11': 'customer_name',
                'dr12': '主键',
                'dr13': '客户姓名',
                'dr14': 'CUST_NAME_001',
                'dr15': '客户的真实姓名信息',
                'dr16': 'VARCHAR(100)',
                'dr17': '记录客户基本信息',
                'dr18': '客户类型码表v1.0',
                'dr19': '日报',
                'dr20': '非空检查',
                'dr21': '姓名脱敏',
                'create_time': self.current_time,
                'update_time': self.current_time
            },
            {
                'submission_id': 'SUB_G0107_BETA_001',
                'submission_type': 'SUBMISSION',
                'report_type': 'detail',
                'set': 'standard',
                'version': 'G0107_beta_v1.0',
                'dr01': 'BDM',
                'dr02': 'G0107 Beta版需求文档',
                'dr03': '客户管理',
                'dr04': 'CUST_002',
                'dr05': 'customer_beta_table',
                'dr06': '客户Beta表',
                'dr07': 'CUST_BETA_001',
                'dr08': '增量',
                'dr09': '客户信息表',
                'dr10': 'CUST_BETA_FIELD_001',
                'dr11': 'customer_id',
                'dr12': '外键',
                'dr13': '客户ID',
                'dr14': 'CUST_ID_001',
                'dr15': '客户的唯一标识',
                'dr16': 'BIGINT',
                'dr17': '记录客户基本信息',
                'dr18': '客户ID码表v1.0',
                'dr19': '周报',
                'dr20': '唯一性检查',
                'dr21': 'ID脱敏',
                'create_time': self.current_time,
                'update_time': self.current_time
            },
            {
                'submission_id': 'SUB_TEST_001',
                'submission_type': 'RANGE',
                'report_type': 'index',
                'set': 'survey',
                'version': 'TEST_REPORT_001',
                'dr01': 'ADM',
                'dr02': '测试需求文档001',
                'dr03': '测试主题域',
                'dr04': 'TEST_001',
                'dr05': 'test_table',
                'dr06': '测试表',
                'dr07': 'TEST_TABLE_001',
                'dr08': '变量',
                'dr09': '客户信息表',
                'dr10': 'TEST_FIELD_001',
                'dr11': 'test_field',
                'dr12': '普通字段',
                'dr13': '测试字段',
                'dr14': 'TEST_FIELD_001',
                'dr15': '用于测试的字段',
                'dr16': 'TEXT',
                'dr17': '记录客户基本信息',
                'dr18': '测试码表v1.0',
                'dr19': '月报',
                'dr20': '格式检查',
                'dr21': '无脱敏要求',
                'create_time': self.current_time,
                'update_time': self.current_time
            }
        ]
        
        # 清理现有测试数据
        await self.mysql_client.aexecute(
            "DELETE FROM biz_dd_pre_distribution WHERE version IN (:v1, :v2, :v3)",
            {"v1": "G0107_ADS_release", "v2": "G0107_beta_v1.0", "v3": "TEST_REPORT_001"}
        )

        # 插入新数据
        for data in pre_data:
            insert_sql = """
            INSERT INTO biz_dd_pre_distribution (
                submission_id, submission_type, report_type, `set`, version,
                dr01, dr02, dr03, dr04, dr05, dr06, dr07, dr08, dr09, dr10,
                dr11, dr12, dr13, dr14, dr15, dr16, dr17, dr18, dr19, dr20, dr21,
                create_time, update_time
            ) VALUES (
                :submission_id, :submission_type, :report_type, :set, :version,
                :dr01, :dr02, :dr03, :dr04, :dr05, :dr06, :dr07, :dr08, :dr09, :dr10,
                :dr11, :dr12, :dr13, :dr14, :dr15, :dr16, :dr17, :dr18, :dr19, :dr20, :dr21,
                :create_time, :update_time
            )
            """
            await self.mysql_client.aexecute(insert_sql, data)
        
        logger.info(f"✅ 创建{len(pre_data)}条biz_dd_pre_distribution记录")

    async def create_submission_data(self):
        """创建dd_submission_data表测试数据"""
        logger.info("📝 创建dd_submission_data表测试数据...")

        # 首先获取report_data_id
        report_ids = {}
        for version in ['G0107_ADS_release', 'G0107_beta_v1.0', 'TEST_REPORT_001']:
            result = await self.mysql_client.afetch_one(
                "SELECT id FROM dd_report_data WHERE version = :version",
                {"version": version}
            )
            if hasattr(result, 'data'):
                report_ids[version] = result.data.get('id')
            else:
                report_ids[version] = result.get('id')

        submission_data = [
            {
                'submission_id': 'SUB_G0107_ADS_001',
                'report_data_id': report_ids.get('G0107_ADS_release'),
                'version': 'G0107_ADS_release',
                'type': 'SUBMISSION',
                'dr01': 'ADS',
                'dr09': '客户信息表',
                'dr17': '记录客户基本信息',
                'dr22': 'DEPT_RRMS',
                'create_time': self.current_time,
                'update_time': self.current_time
            },
            {
                'submission_id': 'SUB_G0107_BETA_001',
                'report_data_id': report_ids.get('G0107_beta_v1.0'),
                'version': 'G0107_beta_v1.0',
                'type': 'SUBMISSION',
                'dr01': 'BDM',
                'dr09': '客户信息表',
                'dr17': '记录客户基本信息',
                'dr22': 'DEPT_BUSINESS',
                'create_time': self.current_time,
                'update_time': self.current_time
            },
            {
                'submission_id': 'SUB_TEST_001',
                'report_data_id': report_ids.get('TEST_REPORT_001'),
                'version': 'TEST_REPORT_001',
                'type': 'RANGE',
                'dr01': 'ADM',
                'dr09': '客户信息表',
                'dr17': '记录客户基本信息',
                'dr22': 'DEPT_DATA_MGT',
                'create_time': self.current_time,
                'update_time': self.current_time
            },
            # 添加更多搜索测试数据
            {
                'submission_id': 'SUB_SEARCH_001',
                'report_data_id': report_ids.get('G0107_ADS_release'),
                'version': 'G0107_ADS_release',
                'type': 'SUBMISSION',
                'dr01': 'ADS',
                'dr09': '客户信息表',
                'dr17': '记录客户基本信息',
                'dr22': 'DEPT_RRMS',
                'create_time': self.current_time,
                'update_time': self.current_time
            },
            {
                'submission_id': 'SUB_SEARCH_002',
                'report_data_id': report_ids.get('G0107_beta_v1.0'),
                'version': 'G0107_beta_v1.0',
                'type': 'SUBMISSION',
                'dr01': 'BDM',
                'dr09': '账户信息表',
                'dr17': '记录账户详细信息',
                'dr22': 'DEPT_BUSINESS',
                'create_time': self.current_time,
                'update_time': self.current_time
            }
        ]

        # 清理现有测试数据
        await self.mysql_client.aexecute(
            "DELETE FROM dd_submission_data WHERE version IN (:v1, :v2, :v3)",
            {"v1": "G0107_ADS_release", "v2": "G0107_beta_v1.0", "v3": "TEST_REPORT_001"}
        )

        # 插入新数据
        for data in submission_data:
            insert_sql = """
            INSERT INTO dd_submission_data (
                submission_id, report_data_id, version, type,
                dr01, dr09, dr17, dr22, create_time, update_time
            ) VALUES (:submission_id, :report_data_id, :version, :type,
                     :dr01, :dr09, :dr17, :dr22, :create_time, :update_time)
            """
            await self.mysql_client.aexecute(insert_sql, data)

        logger.info(f"✅ 创建{len(submission_data)}条dd_submission_data记录")

    async def create_post_distribution_data(self):
        """创建biz_dd_post_distribution表测试数据"""
        logger.info("📤 创建biz_dd_post_distribution表测试数据...")

        # 获取pre_distribution_id
        pre_ids = {}
        for submission_id in ['SUB_G0107_ADS_001', 'SUB_G0107_BETA_001', 'SUB_TEST_001']:
            result = await self.mysql_client.afetch_one(
                "SELECT id FROM biz_dd_pre_distribution WHERE submission_id = :submission_id",
                {"submission_id": submission_id}
            )
            if hasattr(result, 'data'):
                pre_ids[submission_id] = result.data.get('id')
            else:
                pre_ids[submission_id] = result.get('id')

        post_data = [
            {
                'pre_distribution_id': pre_ids.get('SUB_G0107_ADS_001'),
                'submission_id': 'SUB_G0107_ADS_001',
                'submission_type': 'SUBMISSION',
                'report_type': 'detail',
                'set': 'standard',
                'version': 'G0107_ADS_release',
                'dept_id': 'DEPT_RRMS',
                'dr01': 'ADS',
                'dr07': 'CUST_INFO_001',
                'dr22': 'DEPT_RRMS',
                'bdr01': 'DEPT_RRMS',
                'bdr02': '张三',
                'bdr03': 'DEPT_DATA_MGT',
                'bdr04': '李四',
                'create_time': self.current_time,
                'update_time': self.current_time
            },
            {
                'pre_distribution_id': pre_ids.get('SUB_G0107_BETA_001'),
                'submission_id': 'SUB_G0107_BETA_001',
                'submission_type': 'SUBMISSION',
                'report_type': 'detail',
                'set': 'standard',
                'version': 'G0107_beta_v1.0',
                'dept_id': 'DEPT_BUSINESS',
                'dr01': 'BDM',
                'dr07': 'CUST_BETA_001',
                'dr22': 'DEPT_BUSINESS',
                'bdr01': 'DEPT_BUSINESS',
                'bdr02': '王五',
                'bdr03': 'DEPT_DATA_MGT',
                'bdr04': '赵六',
                'create_time': self.current_time,
                'update_time': self.current_time
            }
        ]

        # 清理现有测试数据
        await self.mysql_client.aexecute(
            "DELETE FROM biz_dd_post_distribution WHERE version IN (:v1, :v2, :v3)",
            {"v1": "G0107_ADS_release", "v2": "G0107_beta_v1.0", "v3": "TEST_REPORT_001"}
        )

        # 插入新数据
        for data in post_data:
            insert_sql = """
            INSERT INTO biz_dd_post_distribution (
                pre_distribution_id, submission_id, submission_type, report_type, `set`,
                version, dept_id, dr01, dr07, dr22, bdr01, bdr02, bdr03, bdr04,
                create_time, update_time
            ) VALUES (:pre_distribution_id, :submission_id, :submission_type, :report_type, :set,
                     :version, :dept_id, :dr01, :dr07, :dr22, :bdr01, :bdr02, :bdr03, :bdr04,
                     :create_time, :update_time)
            """
            await self.mysql_client.aexecute(insert_sql, data)

        logger.info(f"✅ 创建{len(post_data)}条biz_dd_post_distribution记录")


async def main():
    """主函数"""
    logger.info("🚀 开始创建测试数据集")
    
    creator = TestDataCreator()
    await creator.initialize()
    
    try:
        # 创建知识库基础数据
        await creator.create_knowledge_base_data()

        # 创建报表数据
        await creator.create_report_data()

        # 创建分发前数据
        await creator.create_pre_distribution_data()

        # 创建填报数据
        await creator.create_submission_data()

        # 创建分发后数据
        await creator.create_post_distribution_data()

        logger.info("🎉 测试数据集创建完成！")
        logger.info("📊 数据统计:")
        logger.info("  - dd_report_data: 3条记录")
        logger.info("  - biz_dd_pre_distribution: 3条记录")
        logger.info("  - dd_submission_data: 5条记录")
        logger.info("  - biz_dd_post_distribution: 2条记录")
        
    except Exception as e:
        logger.error(f"❌ 创建测试数据失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())

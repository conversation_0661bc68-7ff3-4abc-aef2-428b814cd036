#!/usr/bin/env python3
"""
MySQL客户端测试运行器

用于验证complete_test.py的基本功能
"""

import sys
import os

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '../../../../../../..')
sys.path.insert(0, os.path.abspath(project_root))

def test_imports():
    """测试导入是否正常"""
    print("🔍 测试导入...")
    
    try:
        # 测试MySQL客户端导入
        sys.path.insert(0, os.path.join(project_root, 'src'))
        from base.db.implementations.rdb.mysql import (
            create_mysql_client_from_dict,
            MySQLClient,
            MySQLConnectionConfig
        )
        print("✅ MySQL客户端导入成功")

        # 测试RDB接口导入
        from base.db.base.rdb import (
            QueryRequest, InsertRequest, UpdateRequest, DeleteRequest,
            QueryFilter, ComparisonOperator, DatabaseType
        )
        print("✅ RDB接口导入成功")
        
        # 测试complete_test模块导入
        from complete_test import (
            get_test_config, check_database_availability,
            test_1_factory_and_connection
        )
        print("✅ 完整测试模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_config():
    """测试配置功能"""
    print("\n🔍 测试配置功能...")
    
    try:
        from complete_test import get_test_config, check_database_availability
        
        # 测试配置获取
        config = get_test_config()
        if config:
            print(f"✅ 获取到配置: {config['host']}:{config['port']}")
            
            # 测试数据库可用性检查
            available = check_database_availability(config['host'], config['port'])
            print(f"✅ 数据库可用性: {'可用' if available else '不可用'}")
            
            return True
        else:
            print("⚠️ 没有可用的数据库配置")
            return False
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_client_creation():
    """测试客户端创建"""
    print("\n🔍 测试客户端创建...")
    
    try:
        from base.db.implementations.rdb.mysql import create_mysql_client_from_dict
        from complete_test import get_test_config
        
        config = get_test_config()
        if not config:
            print("⚠️ 跳过客户端创建测试（无可用配置）")
            return True
        
        # 创建客户端
        client = create_mysql_client_from_dict(config)
        print(f"✅ 客户端创建成功: {type(client).__name__}")
        
        # 检查基本属性
        db_type = client.get_database_type()
        print(f"✅ 数据库类型: {db_type}")
        
        # 检查连接状态
        print(f"✅ 初始连接状态: {client.is_connected()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 客户端创建测试失败: {e}")
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("\n🔍 测试基本功能...")
    
    try:
        from complete_test import test_1_factory_and_connection
        
        # 运行第一个测试
        result = test_1_factory_and_connection()
        
        if result:
            print("✅ 基本功能测试通过")
            return True
        else:
            print("❌ 基本功能测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 基本功能测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 MySQL客户端测试运行器")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("配置测试", test_config),
        ("客户端创建测试", test_client_creation),
        ("基本功能测试", test_basic_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"💥 {test_name} - 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础测试通过！可以运行完整测试。")
        print("\n💡 运行完整测试:")
        print("   python complete_test.py")
        return True
    else:
        print("⚠️ 部分测试失败，请检查环境配置。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""
元数据系统通用依赖注入

提供元数据系统API的通用依赖注入功能，参照DD系统的设计模式。
"""

import sys
from pathlib import Path
from typing import Optional, Tuple
from fastapi import HTTPException, Query
import logging

# 标准化日志导入
logger = logging.getLogger(__name__)

# 添加项目根目录到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from service import get_client
from modules.knowledge.metadata import MetadataCrud, MetadataSearch
from utils.common.uuid_utils import is_valid_uuid4


# ==================== 业务逻辑依赖 ====================

async def get_metadata_crud() -> MetadataCrud:
    """
    获取元数据CRUD实例
    
    Returns:
        MetadataCrud: 元数据系统的CRUD操作实例
        
    Raises:
        HTTPException: 当服务初始化失败时
    """
    try:
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        return MetadataCrud(rdb_client, vdb_client)
    except Exception as e:
        logger.error(f"获取元数据CRUD实例失败: {e}")
        raise HTTPException(status_code=500, detail="元数据CRUD服务初始化失败")


async def get_metadata_search() -> MetadataSearch:
    """
    获取元数据搜索实例
    
    Returns:
        MetadataSearch: 元数据系统的搜索功能实例
        
    Raises:
        HTTPException: 当搜索服务初始化失败时
    """
    try:
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        return MetadataSearch(rdb_client, vdb_client)
    except Exception as e:
        logger.error(f"获取元数据搜索实例失败: {e}")
        raise HTTPException(status_code=500, detail="元数据搜索服务初始化失败")


# ==================== 参数验证依赖 ====================

def validate_knowledge_id(knowledge_id: str) -> str:
    """
    验证知识库ID格式
    
    Args:
        knowledge_id: 知识库ID
        
    Returns:
        str: 验证通过的知识库ID
        
    Raises:
        HTTPException: 当ID格式无效时
    """
    if not is_valid_uuid4(knowledge_id):
        raise HTTPException(
            status_code=400,
            detail=f"无效的knowledge_id格式: {knowledge_id}，必须是标准UUID4格式"
        )
    return knowledge_id


def validate_pagination(
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(20, description="每页数量", ge=1, le=100)
) -> Tuple[int, int, int]:
    """
    验证分页参数
    
    Args:
        page: 页码（从1开始）
        page_size: 每页数量（1-100）
        
    Returns:
        Tuple[int, int, int]: (page, page_size, offset)
    """
    offset = (page - 1) * page_size
    return page, page_size, offset


# ==================== 查询参数依赖 ====================

def get_optional_filters(
    entity_type: Optional[str] = Query(None, description="实体类型过滤"),
    is_active: Optional[bool] = Query(None, description="激活状态过滤"),
    data_layer: Optional[str] = Query(None, description="数据层过滤")
) -> dict:
    """
    获取可选的查询过滤条件
    
    Args:
        entity_type: 实体类型过滤
        is_active: 激活状态过滤
        data_layer: 数据层过滤
        
    Returns:
        dict: 过滤条件字典
    """
    filters = {}
    if entity_type is not None:
        filters["entity_type"] = entity_type
    if is_active is not None:
        filters["is_active"] = is_active
    if data_layer is not None:
        filters["data_layer"] = data_layer
    return filters


def get_search_params(
    limit: int = Query(10, description="返回结果数量限制", ge=1, le=100),
    min_score: float = Query(0.5, description="最小相似度分数", ge=0.0, le=1.0),
    search_type: str = Query("hybrid", description="搜索类型：vector/text/hybrid")
) -> dict:
    """
    获取搜索参数
    
    Args:
        limit: 结果数量限制
        min_score: 最小相似度分数
        search_type: 搜索类型
        
    Returns:
        dict: 搜索参数字典
    """
    params = {
        "limit": limit,
        "min_score": min_score,
        "search_type": search_type
    }
    return params


# ==================== 错误处理依赖 ====================

def handle_not_found(item_name: str, item_id: str):
    """
    处理资源不存在的情况
    
    Args:
        item_name: 资源名称
        item_id: 资源ID
        
    Raises:
        HTTPException: 404错误
    """
    raise HTTPException(
        status_code=404, 
        detail=f"{item_name}不存在: {item_id}"
    )


def handle_validation_error(field_name: str, error_msg: str):
    """
    处理验证错误
    
    Args:
        field_name: 字段名称
        error_msg: 错误信息
        
    Raises:
        HTTPException: 400错误
    """
    raise HTTPException(
        status_code=400,
        detail=f"{field_name}验证失败: {error_msg}"
    )


def handle_service_error(service_name: str, error: Exception):
    """
    处理服务错误
    
    Args:
        service_name: 服务名称
        error: 异常对象
        
    Raises:
        HTTPException: 500错误
    """
    logger.error(f"{service_name}服务错误: {error}")
    raise HTTPException(
        status_code=500,
        detail=f"{service_name}服务暂时不可用，请稍后重试"
    )


# ==================== 实体验证依赖 ====================

def validate_entity_id(entity_id: int) -> int:
    """
    验证实体ID
    
    Args:
        entity_id: 实体ID
        
    Returns:
        int: 验证通过的实体ID
        
    Raises:
        HTTPException: 当ID无效时
    """
    if entity_id <= 0:
        raise HTTPException(
            status_code=400,
            detail=f"无效的实体ID: {entity_id}，必须是正整数"
        )
    return entity_id


def validate_entity_name(entity_name: str, entity_type: str) -> str:
    """
    验证实体名称
    
    Args:
        entity_name: 实体名称
        entity_type: 实体类型
        
    Returns:
        str: 验证通过的实体名称
        
    Raises:
        HTTPException: 当名称无效时
    """
    if not entity_name or len(entity_name.strip()) == 0:
        raise HTTPException(
            status_code=400,
            detail=f"{entity_type}名称不能为空"
        )
    
    if len(entity_name) > 100:
        raise HTTPException(
            status_code=400,
            detail=f"{entity_type}名称长度不能超过100字符"
        )
    
    return entity_name.strip()


# ==================== 批量操作依赖 ====================

def validate_batch_size(batch_size: int, max_size: int = 1000) -> int:
    """
    验证批量操作大小
    
    Args:
        batch_size: 批量大小
        max_size: 最大批量大小
        
    Returns:
        int: 验证通过的批量大小
        
    Raises:
        HTTPException: 当批量大小无效时
    """
    if batch_size <= 0:
        raise HTTPException(
            status_code=400,
            detail="批量操作大小必须大于0"
        )
    
    if batch_size > max_size:
        raise HTTPException(
            status_code=400,
            detail=f"批量操作大小不能超过{max_size}"
        )
    
    return batch_size

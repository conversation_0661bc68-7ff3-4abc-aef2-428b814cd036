curl --location --request POST 'http://localhost:30338/api/dd/sql-recommend/recommend-sql' \
--header 'Content-Type: application/json' \
--data-raw '{
    "report_code": "G0107_release_v1.0",
    "dept_id": "114",
    "entry_id": "SUBMIT_001"
  }'

 curl --location --request POST 'http://localhost:30338/api/dd/sql-recommend/recommend-questions' \
--header 'Content-Type: application/json' \
--data-raw '{
    "report_code": "G0107_release_v1.0",
    "dept_id": "114",
    "entry_id": "SUBMIT_001"
  }'

 curl --location --request POST 'http://localhost:30338/api/dd/sql-recommend/generate-sql-list' \
--header 'Content-Type: application/json' \
--data-raw '{
    "report_code": "G0107_release_v1.0",
    "dept_id": "114",
    "entry_id": "SUBMIT_001",
    "question_list": [
      "需要从b表拿到kk字段，c表拿到ww字段",
      "以union的形式，筛选ctime将他们拼接起来"
    ]
  }'


 curl --location --request POST 'http://localhost:30338/api/dd/sql-recommend/generate-single-sql' \
--header 'Content-Type: application/json' \
--data-raw '{
    "report_code": "G0107_release_v1.0",
    "dept_id": "114",
    "entry_id": "SUBMIT_001",
    "question": "需要从b表拿到kk字段，c表拿到ww字段"
  }'

 curl --location --request POST 'http://localhost:30338/api/dd/sql-recommend/integrate-sql' \
--header 'Content-Type: application/json' \
--data-raw '{
    "report_code": "G0107_release_v1.0",
    "dept_id": "114",
    "entry_id": "SUBMIT_001",
    "question_list": [
        "从客户基本信息表获取姓名、身份证号、联系电话和地址字段",
        "通过客户ID关联客户基本信息表和交易表，获取完整的客户交易信息",
        "从交易表中筛选出ctime字段，将以上信息拼接起来"
    ],
    "sql_list": [
        "select user_name, user_id, user_phone, user_address \nfrom ADM_TOP_G07 \nwhere IS_CBIRC_LOAN=Y\n",
        "select columns_a, user_name, user_mail, user_gender, user_credit\nfrom ADM_LON_REALESTATE_LOAN\njoin ADM_TOP_G07 on ADM_LON_REALESTATE_LOAN.user_credit = ADM_TOP_G07.user_credit\njoin ADM_PUB_BRANCH_LEVEL_INFO on ADM_LON_REALESTATE_LOAN.user_credit = ADM_PUB_BRANCH_LEVEL_INFO.user_credit\nwhere ADM_LON_REALESTATE_LOAN.IS_CBIRC_LOAN=Y\n",
        "select ctime \nfrom ADM_LON_REALESTATE_LOAN \nwhere IS_CBIRC_LOAN=Y\n"
    ]
  }'
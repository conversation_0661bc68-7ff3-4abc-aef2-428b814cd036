"""
内容检索模块 - 基于新vector_search.py的内容检索方案

本模块完全基于新的vector_search.py模块设计，提供：
1. 基于execute_simple_vector_search和execute_hybrid_search的高级封装
2. 结合向量搜索和关系型查询的完整检索流程
3. 灵活的配置和结果处理

设计原则：
- 复用vector_search.py中的新功能
- 避免使用兼容性接口
- 保持与现有API的兼容性
- 充分利用基类架构
"""

from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field
from loguru import logger

# 使用现有的基础设施
from utils.db.common.clients import vdb_client, rdb_client
from utils.llm.providers import embedding_provider
from utils.db.get_partitionkey import get_or_create_partition_key

# 导入新的vector_search功能
from utils.db.rag.vector_search import (
    execute_simple_vector_search, 
    execute_hybrid_search,
    VectorSearchConfig, 
    HybridSearchConfig,
    VectorSearchResult
)


@dataclass
class RelationalQueryConfig:
    """关系型数据库查询配置"""
    table_name: str
    id_column_mapping: Dict[str, str] = field(default_factory=lambda: {
        "where_info": "where_id",
        "model_info": "col_code"
    })
    columns: Optional[List[str]] = None
    
    def get_id_column(self) -> str:
        """根据表名获取对应的ID列名"""
        return self.id_column_mapping.get(self.table_name, "col_code")


@dataclass
class ContentRetrievalConfig:
    """内容检索配置"""
    # 向量搜索配置
    vector_config: Optional[VectorSearchConfig] = None
    hybrid_config: Optional[HybridSearchConfig] = None
    # 关系型查询配置  
    relational_config: Optional[RelationalQueryConfig] = None
    # 检索模式
    search_mode: str = "simple"  # simple, hybrid
    # 是否按embedding类型分组搜索
    group_by_embedding_type: bool = True


@dataclass
class ContentRetrievalResult:
    """内容检索结果"""
    query_text: str
    vector_results: List[Dict[str, Any]]
    relational_results: Dict[str, List[Dict[str, Any]]]
    metadata: Dict[str, Any] = field(default_factory=dict)


def execute_relational_query(
    content_ids: List[Any],
    query_config: RelationalQueryConfig,
    rdb_client_instance: Optional[Any] = None
) -> List[Dict[str, Any]]:
    """
    根据内容ID列表查询关系型数据库 - 直接使用RDB基类方法
    """
    try:
        if not content_ids:
            logger.warning("内容ID列表为空，返回空结果")
            return []
        
        # 使用传入的客户端或全局客户端
        rdb = rdb_client_instance or rdb_client
        
        # 获取表对应的ID列名
        id_column = query_config.get_id_column()
        
        logger.debug(f"执行关系型数据库查询: table={query_config.table_name}, {id_column} IN {content_ids}")
        
        if len(content_ids) == 1:
            # 单个ID查询 - 使用基类的select方法
            condition = {id_column: content_ids[0]}
            results = rdb.select(
                table=query_config.table_name,
                columns=query_config.columns,
                condition=condition
            )
        else:
            # 多个ID查询 - 使用基类的execute_query方法
            placeholders = ', '.join(['%s'] * len(content_ids))
            columns_clause = ', '.join(query_config.columns) if query_config.columns else '*'
            
            query_sql = f"SELECT {columns_clause} FROM {query_config.table_name} WHERE {id_column} IN ({placeholders})"
            results = rdb.execute_query(query_sql, content_ids)
        
        logger.info(f"关系型数据库查询完成: 表={query_config.table_name}, 结果数量={len(results)}")
        return results
        
    except Exception as e:
        logger.error(f"关系型数据库查询失败: table={query_config.table_name}, content_ids={content_ids}, error={str(e)}")
        raise Exception(f"关系型数据库查询失败: {str(e)}") from e


def retrieve_content_by_vector_search(
    query_text: str,
    partition_identifier: str,
    target_table: str = "model_info",
    embedding_types: Optional[List[str]] = None,
    search_mode: str = "simple",
    top_k: int = 3,
    distance_threshold: float = 0.3,
    additional_filter: Optional[str] = None,
    vdb_client_instance: Optional[Any] = None,
    rdb_client_instance: Optional[Any] = None,
    embedding_client_instance: Optional[Any] = None
) -> Dict[str, List[Dict[str, Any]]]:
    """
    基于新vector_search模块的完整内容检索流程 - 简化版
    """
    try:
        logger.info(f"开始内容检索: query='{query_text}', partition='{partition_identifier}', table='{target_table}'")
        
        # 1. 获取或创建分区键
        rdb = rdb_client_instance or rdb_client
        partition_key = get_or_create_partition_key(partition_identifier, rdb)
        logger.debug(f"获取分区键: {partition_identifier} -> {partition_key}")
        
        # 2. 构建向量搜索配置
        if search_mode == "simple":
            search_config = VectorSearchConfig(
                table_name="hsbc_embedding_data",
                vector_field="embedding",
                output_fields=["content_id"],
                top_k=top_k,
                distance_threshold=distance_threshold,
                partition_name=partition_key
            )
        else:
            search_config = HybridSearchConfig(
                table_name="hsbc_embedding_data",
                vector_fields={"embedding": "embedding"},
                output_fields=["content_id"],
                top_k=top_k,
                distance_threshold=distance_threshold,
                partition_name=partition_key,
                rank_algorithm="rrf"
            )
        
        # 3. 按类型执行向量搜索
        all_vector_results = []
        if embedding_types:
            for embedding_type in embedding_types:
                # 构建过滤表达式
                filter_conditions = [f"embedding_type = '{embedding_type}'"]
                if additional_filter:
                    filter_conditions.append(additional_filter)
                filter_expr = " AND ".join(filter_conditions)
                
                # 设置过滤条件
                if search_mode == "simple":
                    type_config = VectorSearchConfig(
                        table_name=search_config.table_name,
                        vector_field=search_config.vector_field,
                        output_fields=search_config.output_fields,
                        top_k=search_config.top_k,
                        distance_threshold=search_config.distance_threshold,
                        metric_type=search_config.metric_type,
                        partition_name=partition_key,
                        filter_expression=filter_expr
                    )
                    
                    # 执行简单向量搜索
                    result = execute_simple_vector_search(
                        query_text=query_text,
                        search_config=type_config,
                        vdb_client_instance=vdb_client_instance,
                        embedding_client_instance=embedding_client_instance
                    )
                else:
                    type_config = HybridSearchConfig(
                        table_name=search_config.table_name,
                        vector_fields=search_config.vector_fields,
                        output_fields=search_config.output_fields,
                        top_k=search_config.top_k,
                        distance_threshold=search_config.distance_threshold,
                        metric_type=search_config.metric_type,
                        partition_name=partition_key,
                        filter_expression=filter_expr,
                        rank_algorithm=search_config.rank_algorithm,
                        rank_config=search_config.rank_config
                    )
                    
                    # 执行混合搜索
                    result = execute_hybrid_search(
                        query_text=query_text,
                        search_config=type_config,
                        vdb_client_instance=vdb_client_instance,
                        embedding_client_instance=embedding_client_instance
                    )
                
                # 为结果添加type信息
                for item in result.results:
                    item['embedding_type'] = embedding_type
                all_vector_results.extend(result.results)
                
                logger.info(f"类型 {embedding_type} 搜索完成: 结果数量={len(result.results)}")
        
        # 4. 如果没有向量搜索结果，返回空结果
        if not all_vector_results:
            logger.warning(f"向量搜索无结果: query='{query_text}'")
            return {}
        
        # 5. 按类型组织关系型查询
        relational_results = {}
        rel_config = RelationalQueryConfig(table_name=target_table)
        
        if embedding_types:
            for emb_type in embedding_types:
                type_vector_results = [r for r in all_vector_results if r.get("embedding_type") == emb_type]
                if type_vector_results:
                    content_ids = [r.get("content_id") for r in type_vector_results if r.get("content_id")]
                    
                    if content_ids:
                        type_results = execute_relational_query(
                            content_ids=content_ids,
                            query_config=rel_config,
                            rdb_client_instance=rdb_client_instance
                        )
                        relational_results[emb_type] = type_results
                        logger.info(f"类型 {emb_type} 查询到 {len(type_results)} 条关系数据")
        
        logger.info(f"内容检索完成: query='{query_text}', 向量结果={len(all_vector_results)}, 关系结果={sum(len(v) for v in relational_results.values())}")
        return relational_results
        
    except Exception as e:
        logger.error(f"内容检索失败: query='{query_text}', partition='{partition_identifier}', error={str(e)}")
        raise Exception(f"内容检索失败: {str(e)}") from e


# 别名函数：完全兼容原API
def get_all_content(
    pg_client, 
    mysql_client, 
    mysql_name: str, 
    partition: str,
    serach_content: str, 
    type: List[str], 
    expr: Optional[str] = None,
    topk: int = 3
) -> Dict[str, List[Dict[str, Any]]]:
    """
    原get_all_content函数的直接替代品 - 基于新vector_search模块
    """
    logger.info(f"调用兼容性函数get_all_content: query='{serach_content}', table='{mysql_name}'")
    
    result = retrieve_content_by_vector_search(
        query_text=serach_content,
        partition_identifier=partition,
        target_table=mysql_name,
        embedding_types=type if type else None,
        search_mode="simple",
        top_k=topk,
        distance_threshold=0.3,
        additional_filter=expr,
        vdb_client_instance=pg_client,
        rdb_client_instance=mysql_client
    )
    
    # 处理返回格式，保持与原函数一致
    if not result:
        return {}
    
    # 如果没有指定类型且有结果，需要特殊处理
    if not type and result:
        return {"no_type": list(result.values())[0] if result else []}
    
    return result


# 测试代码
if __name__ == "__main__":
    import hydra
    from omegaconf import DictConfig
    import sys
    import os
    
    # 动态添加项目根目录到sys.path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.abspath(os.path.join(current_dir, '..', '..', '..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    # 导入配置和服务
    from utils.common.config_util import Config, config, reset_config_for_testing
    from utils.common.service_client import ServiceClient, reset_service_client_registry
    
    @hydra.main(version_base=None, config_path="../../../config", config_name="config")
    def test_content_retrieval(cfg: DictConfig) -> None:
        """测试内容检索功能"""
        
        # 重置并初始化配置
        reset_service_client_registry()
        reset_config_for_testing()
        config.initialize(cfg)
        
        print("=== 新架构内容检索功能测试 ===")
        print(f"数据库配置: VDB={cfg.active_vdb}, RDB={cfg.active_rdb}, Embedding={cfg.active_embedding}")
        
        try:
            # 获取服务实例
            vdb_client_instance = ServiceClient(config.vdb)._get_instance()
            rdb_client_instance = ServiceClient(config.rdb)._get_instance()
            embedding_client_instance = ServiceClient(config.embedding)._get_instance()
            
            print(f"✅ 成功获取服务实例:")
            print(f"   VDB: {type(vdb_client_instance).__name__}")
            print(f"   RDB: {type(rdb_client_instance).__name__}")
            print(f"   Embedding: {type(embedding_client_instance).__name__}")
            
            # 示例1：简单向量搜索模式
            print("\n--- 测试1: 简单向量搜索模式 ---")
            
            result = retrieve_content_by_vector_search(
                query_text="业务类型相关的字段",
                partition_identifier="model_12345",
                target_table="model_info",
                embedding_types=["col_name_cn", "col_desc", "col_name"],
                search_mode="simple",
                top_k=3,
                distance_threshold=0.3,
                vdb_client_instance=vdb_client_instance,
                rdb_client_instance=rdb_client_instance,
                embedding_client_instance=embedding_client_instance
            )
            
            print(f"✅ 简单向量搜索检索结果: 类型数量={len(result)}")
            for emb_type, results in result.items():
                print(f"   {emb_type}: {len(results)} 条记录")
            
            # 示例2：原始API兼容性测试
            print("\n--- 测试2: 原始API兼容性 (get_all_content) ---")
            
            legacy_results = get_all_content(
                pg_client=vdb_client_instance,
                mysql_client=rdb_client_instance,
                mysql_name="model_info",
                partition="meta#ADM_LON_VARIOUS#BUSINESS_TYPE",
                serach_content="业务数据字段",
                type=["col_name_cn", "col_desc"],
                topk=2
            )
            
            print(f"✅ 原始API结果: 类型数量={len(legacy_results)}")
            for emb_type, results in legacy_results.items():
                print(f"   {emb_type}: {len(results)} 条记录")
            
            print("\n🎉 所有新架构内容检索测试完成!")
            
        except Exception as e:
            print(f"❌ 内容检索测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行测试
    test_content_retrieval()

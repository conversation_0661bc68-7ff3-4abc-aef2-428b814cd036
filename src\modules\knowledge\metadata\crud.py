"""
Metadata系统统一CRUD操作入口

整合所有模块的CRUD操作，提供统一的接口：
- 核心数据库/表/字段模块 (crud_meta.py)
- 数据主题和关联模块 (crud_subjects.py)
- 码值和关联模块 (crud_codes.py)
- 关联关系模块 (crud_relations.py)
"""

from typing import Any, Dict, List, Optional, Tuple, Union
import logging

from .crud_modules.crud_meta import MetadataCrudMeta
from .crud_modules.crud_subjects import MetadataCrudSubjects
from .crud_modules.crud_codes import MetadataCrudCodes
from .crud_modules.crud_relations import MetadataCrudRelations
from .shared.exceptions import MetadataError, MetadataValidationError, MetadataNotFoundError, MetadataConflictError

logger = logging.getLogger(__name__)


class MetadataCrud:
    """
    Metadata系统统一CRUD操作类
    
    整合所有模块的功能，提供统一的接口访问
    """

    def __init__(self, rdb_client: Any, vdb_client: Any = None, embedding_client: Any = None):
        """
        初始化统一CRUD操作

        Args:
            rdb_client: 关系型数据库客户端（MySQL）
            vdb_client: 向量数据库客户端（PGVector，可选）
            embedding_client: 向量化模型客户端（可选）
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client

        # 初始化各个模块
        self.meta = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)
        self.subjects = MetadataCrudSubjects(rdb_client, vdb_client, embedding_client)
        self.codes = MetadataCrudCodes(rdb_client, vdb_client, embedding_client)
        self.relations = MetadataCrudRelations(rdb_client, vdb_client, embedding_client)

    # ==================== 源数据库操作 ====================

    async def create_source_database(self, db_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建源数据库"""
        return await self.meta.create_source_database(db_data)

    async def get_source_database(self, db_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取源数据库"""
        return await self.meta.get_source_database(db_id, **where_conditions)

    async def update_source_database(self, db_data: Dict[str, Any], db_id: int = None, **where_conditions) -> bool:
        """更新源数据库"""
        return await self.meta.update_source_database(db_data, db_id, **where_conditions)

    async def delete_source_database(self, db_id: int = None, **where_conditions) -> bool:
        """删除源数据库"""
        return await self.meta.delete_source_database(db_id, **where_conditions)

    async def list_source_databases(self, knowledge_id: Optional[str] = None, data_layer: Optional[str] = None,
                                   is_active: Optional[bool] = None, limit: Optional[int] = None,
                                   offset: Optional[int] = None, **filters) -> List[Dict[str, Any]]:
        """查询源数据库列表"""
        return await self.meta.list_source_databases(knowledge_id, data_layer, is_active, limit, offset, **filters)

    async def batch_list_source_databases(self, query_conditions: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """批量查询源数据库列表"""
        return await self.meta.batch_list_source_databases(query_conditions)

    async def batch_create_source_databases(self, databases_data: List[Dict[str, Any]]) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """批量创建源数据库"""
        return await self.meta.batch_create_source_databases(databases_data)

    # ==================== 新增批量操作方法 ====================

    async def batch_create_source_tables(self, tables_data: List[Dict[str, Any]]) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """批量创建源表"""
        return await self.meta.batch_create_source_tables(tables_data)

    async def batch_create_source_columns(self, columns_data: List[Dict[str, Any]]) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """批量创建源字段"""
        return await self.meta.batch_create_source_columns(columns_data)

    async def batch_create_index_tables(self, tables_data: List[Dict[str, Any]]) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """批量创建指标表"""
        return await self.meta.batch_create_index_tables(tables_data)

    async def batch_create_index_columns(self, columns_data: List[Dict[str, Any]]) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """批量创建指标字段"""
        return await self.meta.batch_create_index_columns(columns_data)

    # ==================== 指标数据库操作 ====================

    async def create_index_database(self, db_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建指标数据库"""
        return await self.meta.create_index_database(db_data)

    async def get_index_database(self, db_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取指标数据库"""
        return await self.meta.get_index_database(db_id, **where_conditions)

    async def update_index_database(self, db_data: Dict[str, Any], db_id: int = None, **where_conditions) -> bool:
        """更新指标数据库"""
        return await self.meta.update_index_database(db_data, db_id, **where_conditions)

    async def delete_index_database(self, db_id: int = None, **where_conditions) -> bool:
        """删除指标数据库"""
        return await self.meta.delete_index_database(db_id, **where_conditions)

    async def list_index_databases(self, knowledge_id: Optional[str] = None, data_layer: Optional[str] = None,
                                  is_active: Optional[bool] = None, limit: Optional[int] = None,
                                  offset: Optional[int] = None, **filters) -> List[Dict[str, Any]]:
        """查询指标数据库列表"""
        return await self.meta.list_index_databases(knowledge_id, data_layer, is_active, limit, offset, **filters)

    # ==================== 源表操作 ====================

    async def create_source_table(self, table_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建源表"""
        return await self.meta.create_source_table(table_data)

    async def get_source_table(self, table_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取源表"""
        return await self.meta.get_source_table(table_id, **where_conditions)

    async def update_source_table(self, table_data: Dict[str, Any], table_id: int = None, **where_conditions) -> bool:
        """更新源表"""
        return await self.meta.update_source_table(table_data, table_id, **where_conditions)

    async def delete_source_table(self, table_id: int = None, **where_conditions) -> bool:
        """删除源表"""
        return await self.meta.delete_source_table(table_id, **where_conditions)

    async def list_source_tables(self, knowledge_id: Optional[str] = None, db_id: Optional[int] = None,
                                 is_active: Optional[bool] = None, limit: Optional[int] = None,
                                 offset: Optional[int] = None, **filters) -> List[Dict[str, Any]]:
        """查询源表列表"""
        return await self.meta.list_source_tables(knowledge_id, db_id, is_active, limit, offset, **filters)

    # ==================== 指标表操作 ====================

    async def create_index_table(self, table_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建指标表"""
        return await self.meta.create_index_table(table_data)

    async def get_index_table(self, table_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取指标表"""
        return await self.meta.get_index_table(table_id, **where_conditions)

    async def update_index_table(self, table_data: Dict[str, Any], table_id: int = None, **where_conditions) -> bool:
        """更新指标表"""
        return await self.meta.update_index_table(table_data, table_id, **where_conditions)

    async def delete_index_table(self, table_id: int = None, **where_conditions) -> bool:
        """删除指标表"""
        return await self.meta.delete_index_table(table_id, **where_conditions)

    async def list_index_tables(self, knowledge_id: Optional[str] = None, db_id: Optional[int] = None,
                                is_active: Optional[bool] = None, limit: Optional[int] = None,
                                offset: Optional[int] = None, **filters) -> List[Dict[str, Any]]:
        """查询指标表列表"""
        return await self.meta.list_index_tables(knowledge_id, db_id, is_active, limit, offset, **filters)

    # ==================== 源字段操作 ====================

    async def create_source_column(self, column_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建源字段"""
        return await self.meta.create_source_column(column_data)

    async def get_source_column(self, column_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取源字段"""
        return await self.meta.get_source_column(column_id, **where_conditions)

    async def update_source_column(self, column_data: Dict[str, Any], column_id: int = None, **where_conditions) -> bool:
        """更新源字段"""
        return await self.meta.update_source_column(column_data, column_id, **where_conditions)

    async def delete_source_column(self, column_id: int = None, **where_conditions) -> bool:
        """删除源字段"""
        return await self.meta.delete_source_column(column_id, **where_conditions)

    async def list_source_columns(self, knowledge_id: Optional[str] = None, table_id: Optional[int] = None,
                                 is_active: Optional[bool] = None, limit: Optional[int] = None,
                                 offset: Optional[int] = None, **filters) -> List[Dict[str, Any]]:
        """查询源字段列表"""
        return await self.meta.list_source_columns(knowledge_id, table_id, is_active, limit, offset, **filters)

    # ==================== 指标字段操作 ====================

    async def create_index_column(self, column_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建指标字段"""
        return await self.meta.create_index_column(column_data)

    async def get_index_column(self, column_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取指标字段"""
        return await self.meta.get_index_column(column_id, **where_conditions)

    async def update_index_column(self, column_data: Dict[str, Any], column_id: int = None, **where_conditions) -> bool:
        """更新指标字段"""
        return await self.meta.update_index_column(column_data, column_id, **where_conditions)

    async def delete_index_column(self, column_id: int = None, **where_conditions) -> bool:
        """删除指标字段"""
        return await self.meta.delete_index_column(column_id, **where_conditions)

    async def list_index_columns(self, knowledge_id: Optional[str] = None, table_id: Optional[int] = None,
                                is_active: Optional[bool] = None, limit: Optional[int] = None,
                                offset: Optional[int] = None, **filters) -> List[Dict[str, Any]]:
        """查询指标字段列表"""
        return await self.meta.list_index_columns(knowledge_id, table_id, is_active, limit, offset, **filters)

    # ==================== 数据主题操作 ====================

    async def create_data_subject(self, subject_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建数据主题"""
        return await self.subjects.create_data_subject(subject_data)

    async def get_data_subject(self, subject_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取数据主题"""
        return await self.subjects.get_data_subject(subject_id, **where_conditions)

    async def update_data_subject(self, subject_data: Dict[str, Any], subject_id: int = None, **where_conditions) -> bool:
        """更新数据主题"""
        return await self.subjects.update_data_subject(subject_data, subject_id, **where_conditions)

    async def delete_data_subject(self, subject_id: int = None, **where_conditions) -> bool:
        """删除数据主题"""
        return await self.subjects.delete_data_subject(subject_id, **where_conditions)

    async def list_data_subjects(self, knowledge_id: Optional[str] = None, is_active: Optional[bool] = None,
                                limit: Optional[int] = None, offset: Optional[int] = None, **filters) -> List[Dict[str, Any]]:
        """查询数据主题列表"""
        return await self.subjects.list_data_subjects(knowledge_id, is_active, limit, offset, **filters)

    async def batch_list_data_subjects(self, query_conditions: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """批量查询数据主题列表"""
        return await self.subjects.batch_list_data_subjects(query_conditions)

    # ==================== 数据主题关联操作 ====================

    async def create_subject_relation(self, relation_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建数据主题关联"""
        return await self.subjects.create_subject_relation(relation_data)

    async def get_subject_relation(self, relation_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取数据主题关联"""
        return await self.subjects.get_subject_relation(relation_id, **where_conditions)

    async def delete_subject_relation(self, relation_id: int = None, **where_conditions) -> bool:
        """删除数据主题关联"""
        return await self.subjects.delete_subject_relation(relation_id, **where_conditions)

    async def list_subject_relations(self, knowledge_id: Optional[str] = None, subject_id: Optional[int] = None,
                                    entity_type: Optional[str] = None, limit: Optional[int] = None,
                                    offset: Optional[int] = None, **filters) -> List[Dict[str, Any]]:
        """查询数据主题关联列表"""
        return await self.subjects.list_subject_relations(knowledge_id, subject_id, entity_type, limit, offset, **filters)

    # ==================== 码值集操作 ====================

    async def create_code_set(self, code_set_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建码值集"""
        return await self.codes.create_code_set(code_set_data)

    async def get_code_set(self, code_set_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取码值集"""
        return await self.codes.get_code_set(code_set_id, **where_conditions)

    async def update_code_set(self, code_set_data: Dict[str, Any], code_set_id: int = None, **where_conditions) -> bool:
        """更新码值集"""
        return await self.codes.update_code_set(code_set_data, code_set_id, **where_conditions)

    async def delete_code_set(self, code_set_id: int = None, **where_conditions) -> bool:
        """删除码值集"""
        return await self.codes.delete_code_set(code_set_id, **where_conditions)

    async def list_code_sets(self, knowledge_id: Optional[str] = None, is_active: Optional[bool] = None,
                             limit: Optional[int] = None, offset: Optional[int] = None, **filters) -> List[Dict[str, Any]]:
        """查询码值集列表"""
        return await self.codes.list_code_sets(knowledge_id, is_active, limit, offset, **filters)

    # ==================== 码值操作 ====================

    async def create_code_value(self, code_value_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建码值"""
        return await self.codes.create_code_value(code_value_data)

    async def get_code_value(self, code_value_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取码值"""
        return await self.codes.get_code_value(code_value_id, **where_conditions)

    async def update_code_value(self, code_value_data: Dict[str, Any], code_value_id: int = None, **where_conditions) -> bool:
        """更新码值"""
        return await self.codes.update_code_value(code_value_data, code_value_id, **where_conditions)

    async def delete_code_value(self, code_value_id: int = None, **where_conditions) -> bool:
        """删除码值"""
        return await self.codes.delete_code_value(code_value_id, **where_conditions)

    async def list_code_values(self, knowledge_id: Optional[str] = None, code_set_id: Optional[int] = None,
                              is_active: Optional[bool] = None, limit: Optional[int] = None,
                              offset: Optional[int] = None, **filters) -> List[Dict[str, Any]]:
        """查询码值列表"""
        return await self.codes.list_code_values(knowledge_id, code_set_id, is_active, limit, offset, **filters)

    # ==================== 关联键信息操作 ====================

    async def create_source_key_relation(self, relation_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建源关联键信息"""
        return await self.relations.create_source_key_relation(relation_data)

    async def get_source_key_relation(self, relation_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取源关联键信息"""
        return await self.relations.get_source_key_relation(relation_id, **where_conditions)

    async def update_source_key_relation(self, relation_data: Dict[str, Any], relation_id: int = None, **where_conditions) -> bool:
        """更新源关联键信息"""
        return await self.relations.update_source_key_relation(relation_data, relation_id, **where_conditions)

    async def delete_source_key_relation(self, relation_id: int = None, **where_conditions) -> bool:
        """删除源关联键信息"""
        return await self.relations.delete_source_key_relation(relation_id, **where_conditions)

    async def list_source_key_relations(self, knowledge_id: Optional[str] = None, db_id: Optional[int] = None,
                                       is_active: Optional[bool] = None, limit: Optional[int] = None,
                                       offset: Optional[int] = None, **filters) -> List[Dict[str, Any]]:
        """查询源关联键信息列表"""
        return await self.relations.list_source_key_relations(knowledge_id, db_id, is_active, limit, offset, **filters)

    async def create_index_key_relation(self, relation_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建指标关联键信息"""
        return await self.relations.create_index_key_relation(relation_data)

    async def get_index_key_relation(self, relation_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取指标关联键信息"""
        return await self.relations.get_index_key_relation(relation_id, **where_conditions)

    async def update_index_key_relation(self, relation_data: Dict[str, Any], relation_id: int = None, **where_conditions) -> bool:
        """更新指标关联键信息"""
        return await self.relations.update_index_key_relation(relation_data, relation_id, **where_conditions)

    async def delete_index_key_relation(self, relation_id: int = None, **where_conditions) -> bool:
        """删除指标关联键信息"""
        return await self.relations.delete_index_key_relation(relation_id, **where_conditions)

    async def list_index_key_relations(self, knowledge_id: Optional[str] = None, db_id: Optional[int] = None,
                                      is_active: Optional[bool] = None, limit: Optional[int] = None,
                                      offset: Optional[int] = None, **filters) -> List[Dict[str, Any]]:
        """查询指标关联键信息列表"""
        return await self.relations.list_index_key_relations(knowledge_id, db_id, is_active, limit, offset, **filters)

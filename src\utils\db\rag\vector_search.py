"""
向量搜索模块 - 基于基类架构的普通向量搜索方案

本模块提供：
1. 基于VDB基类的普通向量搜索封装
2. 支持多种向量数据库的统一接口
3. 灵活的搜索配置和结果处理
4. 集成新的rank功能的混合搜索

设计原则：
- 直接使用VSClient基类的search方法
- 配置驱动，支持不同表结构
- 提供简洁统一的接口
- 复用新的rank服务
"""

from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field
from loguru import logger

# 使用现有的基础设施
from utils.db.common.clients import vdb_client
from utils.llm.providers import embedding_provider

# 导入新的rank功能
from utils.db.rank.rank_service import get_rank_service


@dataclass
class VectorSearchConfig:
    """普通向量搜索配置"""
    table_name: str = "hsbc_embedding_data"
    vector_field: str = "embedding"
    output_fields: List[str] = field(default_factory=lambda: ["content_id"])
    top_k: int = 10
    distance_threshold: float = 0.5
    metric_type: str = "cosine"
    partition_name: Optional[Union[str, int]] = None
    filter_expression: str = ""


@dataclass
class HybridSearchConfig:
    """混合搜索配置"""
    table_name: str = "hsbc_embedding_data"
    vector_fields: Dict[str, str] = field(default_factory=lambda: {"embedding": "embedding"})  # 字段名到向量字段的映射
    output_fields: List[str] = field(default_factory=lambda: ["content_id"])
    top_k: int = 10
    distance_threshold: float = 0.3
    metric_type: str = "cosine"
    partition_name: Optional[Union[str, int]] = None
    filter_expression: str = ""
    # rank配置
    rank_algorithm: str = "rrf"  # rrf, weighted, hybrid_weighted
    rank_config: Dict[str, Any] = field(default_factory=dict)


@dataclass
class VectorSearchResult:
    """向量搜索结果"""
    query_text: str
    results: List[Dict[str, Any]]
    metadata: Dict[str, Any] = field(default_factory=dict)


def execute_simple_vector_search(
    query_text: str,
    search_config: Optional[VectorSearchConfig] = None,
    vdb_client_instance: Optional[Any] = None,
    embedding_client_instance: Optional[Any] = None
) -> VectorSearchResult:
    """
    执行普通向量搜索 - 直接利用VDB基类的search方法
    
    Args:
        query_text: 查询文本
        search_config: 搜索配置
        vdb_client_instance: 向量数据库客户端实例
        embedding_client_instance: 嵌入模型客户端实例
        
    Returns:
        VectorSearchResult: 向量搜索结果
    """
    try:
        # 使用配置
        config_obj = search_config or VectorSearchConfig()
        
        # 使用客户端
        vdb = vdb_client_instance or vdb_client
        embedding_service = embedding_client_instance or embedding_provider
        
        # 获取查询文本的向量表示
        logger.debug(f"获取查询文本的向量表示: {query_text}")
        embedding_result = embedding_service.invoke(
            texts=[query_text],
            user="vector_search_system"
        )
        
        if not embedding_result.embeddings or not embedding_result.embeddings[0]:
            raise ValueError(f"无法获取查询文本的向量表示: {query_text}")
            
        query_vector = embedding_result.embeddings[0]
        
        # 构建搜索参数
        search_params = {
            "vector_field": config_obj.vector_field,
            "output_fields": config_obj.output_fields
        }
        
        # 构建分区名称列表
        partition_names = [str(config_obj.partition_name)] if config_obj.partition_name else None
        
        logger.debug(f"执行向量搜索: table={config_obj.table_name}, params={search_params}")
        
        # 执行搜索 - 使用新的基类方法
        raw_results = vdb.search(
            collection_name=config_obj.table_name,
            vector=query_vector,
            top_k=config_obj.top_k,
            metric_type=config_obj.metric_type,
            filter=config_obj.filter_expression if config_obj.filter_expression else None,
            partition_names=partition_names,
            search_params=search_params
        )
        
        # 格式化结果 - 新的基类返回 List[List[Dict]] 格式
        formatted_results = []
        
        # raw_results 是 List[List[Dict]]，我们取第一个查询的结果
        if raw_results and len(raw_results) > 0:
            query_results = raw_results[0] if isinstance(raw_results[0], list) else raw_results
            
            for item in query_results:
                if isinstance(item, dict):
                    # 新基类直接返回字典格式
                    formatted_entry = {field: item.get(field) for field in config_obj.output_fields if field in item}
                    if 'distance' in item:
                        formatted_entry['distance'] = item['distance']
                    formatted_results.append(formatted_entry)
                else:
                    logger.warning(f"未预期的结果格式: {type(item)}")
        
        # 应用距离阈值过滤
        if config_obj.distance_threshold is not None:
            filtered_results = [
                result for result in formatted_results 
                if result.get('distance', float('inf')) <= config_obj.distance_threshold
            ]
        else:
            filtered_results = formatted_results
        
        logger.info(f"向量搜索完成: query='{query_text}', 原始结果={len(formatted_results)}, 过滤后={len(filtered_results)}")
        
        # 构建结果对象
        result = VectorSearchResult(
            query_text=query_text,
            results=filtered_results,
            metadata={
                "status": "success",
                "original_count": len(formatted_results),
                "filtered_count": len(filtered_results),
                "config": config_obj.__dict__
            }
        )
        
        return result
        
    except Exception as e:
        logger.error(f"向量搜索失败: query_text={query_text}, error={str(e)}")
        return VectorSearchResult(
            query_text=query_text,
            results=[],
            metadata={"status": "error", "error": str(e)}
        )


def execute_hybrid_search(
    query_text: str,
    search_config: Optional[HybridSearchConfig] = None,
    vdb_client_instance: Optional[Any] = None,
    embedding_client_instance: Optional[Any] = None
) -> VectorSearchResult:
    """
    执行混合搜索 - 支持多向量字段搜索+新rank功能
    
    这个函数替代老的hybrid_search，使用新的基类架构和rank服务
    
    Args:
        query_text: 查询文本
        search_config: 混合搜索配置
        vdb_client_instance: 向量数据库客户端实例
        embedding_client_instance: 嵌入模型客户端实例
        
    Returns:
        VectorSearchResult: 混合搜索结果
    """
    try:
        # 使用配置
        config_obj = search_config or HybridSearchConfig()
        
        # 使用客户端
        vdb = vdb_client_instance or vdb_client
        embedding_service = embedding_client_instance or embedding_provider
        
        logger.info(f"开始混合搜索: query='{query_text}', 向量字段={list(config_obj.vector_fields.keys())}")
        
        # 获取查询文本的向量表示
        embedding_result = embedding_service.invoke(
            texts=[query_text],
            user="hybrid_search_system"
        )
        
        if not embedding_result.embeddings or not embedding_result.embeddings[0]:
            raise ValueError(f"无法获取查询文本的向量表示: {query_text}")
            
        query_vector = embedding_result.embeddings[0]
        
        # 准备多向量字段搜索的数据字典（兼容rank服务的data_dict格式）
        search_data_dict = {}
        
        # 对每个向量字段进行搜索
        for field_name, vector_field in config_obj.vector_fields.items():
            logger.debug(f"搜索向量字段: {field_name} -> {vector_field}")
            
            # 构建搜索参数
            search_params = {
                "vector_field": vector_field,
                "output_fields": config_obj.output_fields
            }
            
            # 构建分区名称列表
            partition_names = [str(config_obj.partition_name)] if config_obj.partition_name else None
            
            # 执行单个字段的搜索 - 使用新的基类方法
            raw_results = vdb.search(
                collection_name=config_obj.table_name,
                vector=query_vector,
                top_k=config_obj.top_k,
                metric_type=config_obj.metric_type,
                filter=config_obj.filter_expression if config_obj.filter_expression else None,
                partition_names=partition_names,
                search_params=search_params
            )
            
            # 格式化为rank服务所需的格式
            formatted_field_results = []
            
            # 处理新基类的返回格式 List[List[Dict]]
            if raw_results and len(raw_results) > 0:
                query_results = raw_results[0] if isinstance(raw_results[0], list) else raw_results
                
                for item in query_results:
                    if isinstance(item, dict):
                        # 构建兼容格式：包含content_id和distance
                        entry = {
                            "content_id": item.get("content_id") or item.get(config_obj.output_fields[0]) if config_obj.output_fields else None,
                            "distance": item.get("distance", 0.0)
                        }
                        # 添加其他字段
                        for field in config_obj.output_fields:
                            if field in item:
                                entry[field] = item[field]
                        formatted_field_results.append(entry)
                    else:
                        logger.warning(f"字段 {field_name} 搜索结果格式异常: {type(item)}")
            
            search_data_dict[field_name] = formatted_field_results
            logger.debug(f"字段 {field_name} 搜索到 {len(formatted_field_results)} 个结果")
        
        # 如果没有任何搜索结果，直接返回空结果
        if not any(search_data_dict.values()):
            logger.warning(f"混合搜索无结果: query='{query_text}'")
            return VectorSearchResult(
                query_text=query_text,
                results=[],
                metadata={"status": "no_results", "search_fields": list(config_obj.vector_fields.keys())}
            )
        
        # 使用新的rank服务进行排序
        rank_service = get_rank_service()
        
        # 准备rank配置
        rank_config_override = dict(config_obj.rank_config)
        if config_obj.rank_algorithm == "weighted" and not rank_config_override.get("weights"):
            # 如果是加权排序但没有指定权重，给每个字段相等权重
            rank_config_override["weights"] = {
                field: 1.0 / len(config_obj.vector_fields) 
                for field in config_obj.vector_fields.keys()
            }
        elif config_obj.rank_algorithm == "rrf" and not rank_config_override.get("k"):
            rank_config_override["k"] = 60  # RRF默认参数
        
        logger.debug(f"执行rank排序: algorithm={config_obj.rank_algorithm}, config={rank_config_override}")
        
        # 执行排序
        rank_result = rank_service.rank(
            query=query_text,
            docs=[],  # 空的docs，使用data_dict格式
            algorithm=config_obj.rank_algorithm,
            config_override=rank_config_override,
            data_dict=search_data_dict
        )
        
        # 应用距离阈值过滤
        filtered_results = []
        for doc in rank_result.docs:
            distance = doc.get('distance', float('inf'))
            if distance <= config_obj.distance_threshold:
                filtered_results.append(doc)
        
        logger.info(f"混合搜索完成: query='{query_text}', rank后={len(rank_result.docs)}, 过滤后={len(filtered_results)}")
        
        # 构建结果对象
        result = VectorSearchResult(
            query_text=query_text,
            results=filtered_results,
            metadata={
                "status": "success",
                "search_type": "hybrid",
                "rank_algorithm": config_obj.rank_algorithm,
                "search_fields": list(config_obj.vector_fields.keys()),
                "original_count": len(rank_result.docs),
                "filtered_count": len(filtered_results),
                "rank_metadata": rank_result.metadata,
                "config": config_obj.__dict__
            }
        )
        
        return result
        
    except Exception as e:
        logger.error(f"混合搜索失败: query_text={query_text}, error={str(e)}")
        return VectorSearchResult(
            query_text=query_text,
            results=[],
            metadata={"status": "error", "error": str(e), "search_type": "hybrid"}
        )


def execute_batch_vector_search(
    queries: List[str],
    search_config: Optional[VectorSearchConfig] = None,
    vdb_client_instance: Optional[Any] = None,
    embedding_client_instance: Optional[Any] = None
) -> List[VectorSearchResult]:
    """
    批量执行向量搜索
    
    Args:
        queries: 查询文本列表
        search_config: 搜索配置
        vdb_client_instance: 向量数据库客户端实例
        embedding_client_instance: 嵌入模型客户端实例
        
    Returns:
        List[VectorSearchResult]: 批量搜索结果列表
    """
    try:
        logger.info(f"开始批量向量搜索: {len(queries)} 个查询")
        
        results = []
        for i, query in enumerate(queries):
            logger.debug(f"处理第 {i+1}/{len(queries)} 个查询: {query}")
            result = execute_simple_vector_search(
                query_text=query,
                search_config=search_config,
                vdb_client_instance=vdb_client_instance,
                embedding_client_instance=embedding_client_instance
            )
            results.append(result)
        
        logger.info(f"批量向量搜索完成: 成功={sum(1 for r in results if r.metadata.get('status') == 'success')}, "
                   f"失败={sum(1 for r in results if r.metadata.get('status') == 'error')}")
        
        return results
        
    except Exception as e:
        logger.error(f"批量向量搜索失败: error={str(e)}")
        raise Exception(f"批量向量搜索失败: {str(e)}") from e


def search_by_vector(
    vector: List[float],
    search_config: Optional[VectorSearchConfig] = None,
    vdb_client_instance: Optional[Any] = None
) -> VectorSearchResult:
    """
    直接使用向量进行搜索（不需要embedding转换）
    
    Args:
        vector: 查询向量
        search_config: 搜索配置
        vdb_client_instance: 向量数据库客户端实例
        
    Returns:
        VectorSearchResult: 向量搜索结果
    """
    try:
        # 使用配置
        config_obj = search_config or VectorSearchConfig()
        
        # 使用客户端
        vdb = vdb_client_instance or vdb_client
        
        # 构建搜索参数
        search_params = {
            "vector_field": config_obj.vector_field,
            "output_fields": config_obj.output_fields
        }
        
        # 构建分区名称列表
        partition_names = [str(config_obj.partition_name)] if config_obj.partition_name else None
        
        logger.debug(f"执行向量搜索 (直接向量): vector_dim={len(vector)}")
        
        # 执行搜索 - 使用新的基类方法
        raw_results = vdb.search(
            collection_name=config_obj.table_name,
            vector=vector,
            top_k=config_obj.top_k,
            metric_type=config_obj.metric_type,
            filter=config_obj.filter_expression if config_obj.filter_expression else None,
            partition_names=partition_names,
            search_params=search_params
        )
        
        # 格式化结果（复用上面的逻辑）
        formatted_results = []
        for item in raw_results:
            if isinstance(item, list):
                formatted_entry = {field: item[i] for i, field in enumerate(config_obj.output_fields)}
                if len(item) > len(config_obj.output_fields):
                    formatted_entry['distance'] = item[-1]
                formatted_results.append(formatted_entry)
            elif isinstance(item, dict):
                formatted_entry = {field: item.get(field) for field in config_obj.output_fields}
                if 'distance' in item:
                    formatted_entry['distance'] = item['distance']
                formatted_results.append(formatted_entry)
            else:
                logger.warning(f"未知的结果类型: {type(item)}")
        
        # 应用距离阈值过滤
        if config_obj.distance_threshold is not None:
            filtered_results = [
                result for result in formatted_results 
                if result.get('distance', float('inf')) <= config_obj.distance_threshold
            ]
        else:
            filtered_results = formatted_results
        
        logger.info(f"向量搜索完成 (直接向量): 原始结果={len(formatted_results)}, 过滤后={len(filtered_results)}")
        
        # 构建结果对象
        result = VectorSearchResult(
            query_text=f"<直接向量查询: dim={len(vector)}>",
            results=filtered_results,
            metadata={
                "status": "success",
                "search_type": "direct_vector",
                "vector_dimension": len(vector),
                "original_count": len(formatted_results),
                "filtered_count": len(filtered_results),
                "config": config_obj.__dict__
            }
        )
        
        return result
        
    except Exception as e:
        logger.error(f"直接向量搜索失败: error={str(e)}")
        return VectorSearchResult(
            query_text=f"<直接向量查询: dim={len(vector) if vector else 0}>",
            results=[],
            metadata={"status": "error", "error": str(e), "search_type": "direct_vector"}
        )


# 兼容性函数：与原vdb_util中的search_vector_db保持接口一致
def search_vector_db(
    query: str, 
    db_type: str = 'pgvector',
    table_name: str = "hsbc_embedding_data",
    vector_field: str = "embedding",
    output_fields: List[str] = None,
    limit: int = 3,
    expr: str = '',
    partition_name: Union[str, int] = None,
    metric_type: str = 'cosine',
    vdb_client_instance: Optional[Any] = None,
    **kwargs
) -> List[Dict]:
    """
    兼容性函数：保持与原search_vector_db函数相同的接口
    
    这个函数提供向后兼容性，同时利用新的基类架构
    """
    logger.info(f"调用兼容性函数search_vector_db: query='{query}', table='{table_name}'")
    
    # 转换参数格式
    output_fields = output_fields or ["content_id"]
    
    config = VectorSearchConfig(
        table_name=table_name,
        vector_field=vector_field,
        output_fields=output_fields,
        top_k=limit,
        metric_type=metric_type,
        partition_name=partition_name,
        filter_expression=expr
    )
    
    # 执行搜索
    result = execute_simple_vector_search(
        query_text=query,
        search_config=config,
        vdb_client_instance=vdb_client_instance
    )
    
    # 返回原格式的结果（列表格式）
    return result.results


# 兼容性函数：提供与老hybrid_search相同的接口
def hybrid_search_compatible(
    query_text: str,
    vec_dict: Dict[str, Any],
    rank_dict: Dict[str, Any],
    out_filed: List[str],
    table_name: str = "hsbc_embedding_data",
    topk: int = 3,
    expr: str = '',
    partition_name: Union[str, int] = None,
    metric_type: str = 'cosine',
    vdb_client_instance: Optional[Any] = None,
    **kwargs
) -> List[Dict[str, Any]]:
    """
    兼容性函数：提供与老hybrid_search函数相同的接口
    
    这个函数用于无缝替换老的hybrid_search调用
    """
    logger.info(f"调用兼容性函数hybrid_search_compatible: query='{query_text}', 向量字段数={len(vec_dict)}")
    
    try:
        # 转换vec_dict格式为新的vector_fields格式
        vector_fields = {}
        for field_name, vector_data in vec_dict.items():
            vector_fields[field_name] = field_name  # 假设字段名就是向量字段名
        
        # 转换rank_dict格式
        rank_type = rank_dict.get("type", "weighted")
        rank_rule = rank_dict.get("rank_rule", [])
        
        # 映射老的rank类型到新的算法
        if rank_type in ["hybrid-weighted", "weighted"]:
            algorithm = "weighted"
            rank_config = {}
            if rank_rule:
                if isinstance(rank_rule, list) and rank_rule:
                    rank_config["weights"] = rank_rule[0] if isinstance(rank_rule[0], dict) else {}
                elif isinstance(rank_rule, dict):
                    rank_config["weights"] = rank_rule
        elif rank_type == "rrf":
            algorithm = "rrf"
            rank_config = {"k": 60}
        else:
            algorithm = "weighted"
            rank_config = {"weights": {field: 1.0 for field in vector_fields.keys()}}
        
        # 构建新的配置
        config = HybridSearchConfig(
            table_name=table_name,
            vector_fields=vector_fields,
            output_fields=out_filed,
            top_k=topk,
            distance_threshold=0.3,  # 老代码中的默认值
            metric_type=metric_type,
            partition_name=partition_name,
            filter_expression=expr,
            rank_algorithm=algorithm,
            rank_config=rank_config
        )
        
        # 执行混合搜索
        result = execute_hybrid_search(
            query_text=query_text,
            search_config=config,
            vdb_client_instance=vdb_client_instance
        )
        
        # 返回与老接口兼容的格式
        return result.results
        
    except Exception as e:
        logger.error(f"兼容性混合搜索失败: {str(e)}")
        return []


# 测试代码
if __name__ == "__main__":
    import hydra
    from omegaconf import DictConfig, OmegaConf
    import sys
    import os
    
    # 动态添加项目根目录到sys.path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.abspath(os.path.join(current_dir, '..', '..', '..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    # 导入配置和服务
    from utils.common.config_util import Config, config, reset_config_for_testing
    from utils.common.service_client import ServiceClient, reset_service_client_registry
    
    @hydra.main(version_base=None, config_path="../../../config", config_name="config")
    def test_vector_search(cfg: DictConfig) -> None:
        """测试向量搜索功能"""
        
        # 重置并初始化配置
        reset_service_client_registry()
        reset_config_for_testing()
        config.initialize(cfg)
        
        print("=== 向量搜索功能测试 ===")
        print(f"数据库配置: VDB={cfg.active_vdb}, RDB={cfg.active_rdb}, Embedding={cfg.active_embedding}")
        
        try:
            # 获取服务实例
            vdb_client_instance = ServiceClient(config.vdb)._get_instance()
            embedding_client_instance = ServiceClient(config.embedding)._get_instance()
            
            print(f"✅ 成功获取服务实例:")
            print(f"   VDB: {type(vdb_client_instance).__name__}")
            print(f"   Embedding: {type(embedding_client_instance).__name__}")
            
            # 示例1：简单向量搜索测试
            print("\n--- 测试1: 简单向量搜索 ---")
            simple_config = VectorSearchConfig(
                table_name="hsbc_embedding_data",
                vector_field="embedding",
                output_fields=["content_id", "embedding_type"],
                top_k=3,
                distance_threshold=0.5
            )
            
            result = execute_simple_vector_search(
                query_text="业务类型相关字段",
                search_config=simple_config,
                vdb_client_instance=vdb_client_instance,
                embedding_client_instance=embedding_client_instance
            )
            
            print(f"✅ 简单向量搜索结果: 状态={result.metadata.get('status')}, 结果数量={len(result.results)}")
            if result.results:
                print(f"   首个结果示例: {result.results[0]}")
            
            # 示例2：混合搜索测试
            print("\n--- 测试2: 混合搜索 ---")
            hybrid_config = HybridSearchConfig(
                table_name="hsbc_embedding_data",
                vector_fields={
                    "embedding": "embedding"
                },
                output_fields=["content_id"],
                top_k=3,
                distance_threshold=0.4,
                rank_algorithm="rrf",
                rank_config={"k": 60}
            )
            
            hybrid_result = execute_hybrid_search(
                query_text="用户相关字段信息",
                search_config=hybrid_config,
                vdb_client_instance=vdb_client_instance,
                embedding_client_instance=embedding_client_instance
            )
            
            print(f"✅ 混合搜索结果: 状态={hybrid_result.metadata.get('status')}, 结果数量={len(hybrid_result.results)}")
            print(f"   排序算法: {hybrid_result.metadata.get('rank_algorithm')}")
            if hybrid_result.results:
                print(f"   首个结果示例: {hybrid_result.results[0]}")
            
            # 示例3：兼容性接口测试
            print("\n--- 测试3: 兼容性接口 ---")
            compat_results = search_vector_db(
                query="数据表字段",
                table_name="hsbc_embedding_data",
                output_fields=["content_id"],
                limit=2,
                vdb_client_instance=vdb_client_instance
            )
            
            print(f"✅ 兼容性接口结果: 数量={len(compat_results)}")
            if compat_results:
                print(f"   首个结果示例: {compat_results[0]}")
            
            # 示例4：批量搜索测试
            print("\n--- 测试4: 批量向量搜索 ---")
            batch_queries = ["业务字段", "用户信息", "数据类型"]
            batch_config = VectorSearchConfig(
                table_name="hsbc_embedding_data",
                top_k=2,
                distance_threshold=0.6
            )
            
            batch_results = execute_batch_vector_search(
                queries=batch_queries,
                search_config=batch_config,
                vdb_client_instance=vdb_client_instance,
                embedding_client_instance=embedding_client_instance
            )
            
            print(f"✅ 批量搜索结果: 查询数量={len(batch_results)}")
            success_count = sum(1 for r in batch_results if r.metadata.get('status') == 'success')
            print(f"   成功查询数: {success_count}")
            
            print("\n🎉 所有向量搜索测试完成!")
            
        except Exception as e:
            print(f"❌ 向量搜索测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行测试
    test_vector_search() 
"""
转换函数示例

对应 complete_config_examples.yaml 中各种配置结构的转换函数
展示如何处理不同格式的配置数据
"""

import json
import xml.etree.ElementTree as ET


def enterprise_config_transform(raw_config):
    """
    企业配置中心转换函数
    
    对应 enterprise_config_center 配置
    处理企业标准的JSON格式配置
    """
    config_key = raw_config.get('configuration_identifier', '')
    config_payload = raw_config.get('configuration_payload', '{}')
    
    # 解析企业配置格式（可能是加密的JSON）
    try:
        if isinstance(config_payload, str):
            # 企业可能有自己的解密逻辑
            decrypted_payload = config_payload  # 这里应该是解密逻辑
            parsed_config = json.loads(decrypted_payload)
        else:
            parsed_config = config_payload
    except json.JSONDecodeError:
        parsed_config = {"raw_value": config_payload}
    
    # 构建企业特定的层级结构
    if config_key.startswith('database.'):
        parts = config_key.split('.')
        return {
            "database": {
                parts[1]: {  # rdbs, vdbs等
                    parts[2]: {  # mysql, pgvector等
                        **parsed_config,
                        # 添加企业元数据
                        "_enterprise_metadata": {
                            "source": "enterprise_config_center",
                            "organization": raw_config.get('organization_code'),
                            "environment": raw_config.get('environment_code'),
                            "priority": raw_config.get('priority_order'),
                            "compliance_level": "SOX_COMPLIANT",
                            "created_by": raw_config.get('created_by'),
                            "last_updated": raw_config.get('last_updated')
                        }
                    }
                }
            }
        }
    
    return {"enterprise_config": {config_key: parsed_config}}


def microservice_registry_transform(raw_config):
    """
    微服务注册中心转换函数
    
    对应 microservice_registry 配置
    处理Kubernetes/服务网格的JSON配置
    """
    service_name = raw_config.get('service_name', '')
    service_config = raw_config.get('service_configuration', {})
    
    # 微服务配置可能是复杂的嵌套JSON结构
    if isinstance(service_config, str):
        try:
            parsed_config = json.loads(service_config)
        except json.JSONDecodeError:
            parsed_config = {"raw_config": service_config}
    else:
        parsed_config = service_config
    
    # 构建微服务特定的配置结构
    return {
        "microservices": {
            service_name: {
                **parsed_config,
                "_k8s_metadata": {
                    "namespace": raw_config.get('namespace'),
                    "environment": raw_config.get('environment'),
                    "priority": raw_config.get('priority'),
                    "service_mesh": "istio",
                    "deployment_strategy": "blue_green"
                }
            }
        }
    }


def cloud_native_transform(raw_config):
    """
    云原生配置转换函数
    
    对应 cloud_native_config 配置
    处理多云环境的配置数据
    """
    config_key = raw_config.get('config_key', '')
    config_data = raw_config.get('configuration_data', {})
    
    # 云配置可能包含加密的敏感信息
    if isinstance(config_data, str):
        try:
            # 可能需要云服务的解密
            parsed_config = json.loads(config_data)
        except json.JSONDecodeError:
            parsed_config = {"encrypted_config": config_data}
    else:
        parsed_config = config_data
    
    # 解析云服务路径: aws.us-east-1.rds_mysql
    parts = config_key.split('.')
    if len(parts) >= 3:
        cloud_provider = parts[0]  # aws, azure, gcp
        region = parts[1]          # us-east-1, eastus, us-central1
        service = parts[2]         # rds, cosmosdb, cloudsql
        
        return {
            "cloud": {
                cloud_provider: {
                    region: {
                        service: {
                            **parsed_config,
                            "_cloud_metadata": {
                                "provider": cloud_provider,
                                "region": region,
                                "workspace": raw_config.get('tenant_workspace'),
                                "environment": raw_config.get('deployment_environment'),
                                "cost_priority": raw_config.get('cost_priority'),
                                "compliance": "GDPR_SOC2_COMPLIANT"
                            }
                        }
                    }
                }
            }
        }
    
    return {"cloud_config": {config_key: parsed_config}}


def legacy_mainframe_transform(raw_config):
    """
    遗留系统转换函数
    
    对应 legacy_mainframe_config 配置
    处理遗留系统的固定长度字符串格式
    """
    config_id = raw_config.get('config_key', '').strip()
    config_data = raw_config.get('config_value', '').strip()
    
    # 遗留系统可能使用固定长度的字符串格式
    # 例如: "HOST=***********;PORT=3306;DB=LEGACY_DB;USER=LEGACY_USER"
    parsed_config = {}
    if ';' in config_data:
        for pair in config_data.split(';'):
            if '=' in pair:
                key, value = pair.split('=', 1)
                parsed_config[key.strip().lower()] = value.strip()
    else:
        parsed_config = {"raw_legacy_value": config_data}
    
    # 映射遗留系统的字段名到现代命名
    field_mapping = {
        "host": "host",
        "port": "port", 
        "db": "database",
        "user": "username",
        "pass": "password",
        "schema": "schema"
    }
    
    modern_config = {}
    for legacy_key, legacy_value in parsed_config.items():
        modern_key = field_mapping.get(legacy_key, legacy_key)
        # 类型转换
        if modern_key == "port":
            try:
                modern_config[modern_key] = int(legacy_value)
            except ValueError:
                modern_config[modern_key] = legacy_value
        else:
            modern_config[modern_key] = legacy_value
    
    # 构建现代配置结构
    if config_id.startswith('DB_'):
        db_type = config_id.split('_')[1].lower()  # DB_MYSQL -> mysql
        return {
            "database": {
                "rdbs": {
                    db_type: {
                        **modern_config,
                        "_legacy_metadata": {
                            "source": "legacy_mainframe",
                            "original_id": config_id,
                            "department": raw_config.get('tenant_id'),
                            "environment": raw_config.get('environment'),
                            "migration_status": "INTEGRATED"
                        }
                    }
                }
            }
        }
    
    return {"legacy_config": {config_id: modern_config}}


def simple_kv_transform(raw_config):
    """
    简单键值对转换函数
    
    对应 simple_key_value_store 配置
    处理简单的键值对配置
    """
    config_key = raw_config.get('config_key', '')
    config_value = raw_config.get('config_value', '')
    
    # 简单的值处理
    parsed_value = config_value
    
    # 尝试JSON解析
    if isinstance(config_value, str) and (config_value.startswith('{') or config_value.startswith('[')):
        try:
            parsed_value = json.loads(config_value)
        except json.JSONDecodeError:
            pass
    
    # 构建层级结构
    if '.' in config_key:
        parts = config_key.split('.')
        result = {}
        current = result
        
        for i, part in enumerate(parts):
            if i == len(parts) - 1:
                current[part] = parsed_value
            else:
                current[part] = {}
                current = current[part]
        
        return result
    
    return {config_key: parsed_value}


def saas_tenant_transform(raw_config):
    """
    SaaS租户配置转换函数
    
    对应 saas_tenant_config 配置
    处理多租户SaaS的配置数据
    """
    config_name = raw_config.get('config_name', '')
    config_data = raw_config.get('config_data', {})
    
    # 解析配置数据
    if isinstance(config_data, str):
        try:
            parsed_config = json.loads(config_data)
        except json.JSONDecodeError:
            parsed_config = {"raw_value": config_data}
    else:
        parsed_config = config_data
    
    # SaaS特定的配置结构
    if config_name.startswith('database.'):
        parts = config_name.split('.')
        return {
            "database": {
                parts[1]: {  # rdbs, vdbs等
                    parts[2]: {  # mysql, pgvector等
                        **parsed_config,
                        "_saas_metadata": {
                            "tenant_uuid": raw_config.get('tenant_id'),
                            "environment": raw_config.get('environment'),
                            "billing_priority": raw_config.get('billing_priority'),
                            "plan_type": "enterprise",
                            "compliance_tier": "enterprise_grade"
                        }
                    }
                }
            }
        }
    
    return {"saas_config": {config_name: parsed_config}}


def xml_config_transform(raw_config):
    """
    XML配置转换函数
    
    处理XML格式的配置数据
    """
    config_key = raw_config.get('config_key', '')
    xml_data = raw_config.get('config_value', '')
    
    parsed_config = {}
    
    if isinstance(xml_data, str) and xml_data.strip().startswith('<'):
        try:
            root = ET.fromstring(xml_data)
            
            # 递归解析XML
            def xml_to_dict(element):
                result = {}
                
                # 处理属性
                if element.attrib:
                    result.update(element.attrib)
                
                # 处理子元素
                for child in element:
                    child_data = xml_to_dict(child)
                    if child.tag in result:
                        # 如果已存在，转换为列表
                        if not isinstance(result[child.tag], list):
                            result[child.tag] = [result[child.tag]]
                        result[child.tag].append(child_data)
                    else:
                        result[child.tag] = child_data
                
                # 处理文本内容
                if element.text and element.text.strip():
                    if result:
                        result['_text'] = element.text.strip()
                    else:
                        return element.text.strip()
                
                return result
            
            parsed_config = xml_to_dict(root)
            
        except ET.ParseError:
            parsed_config = {"raw_xml": xml_data}
    else:
        parsed_config = {"raw_value": xml_data}
    
    # 构建层级结构
    if '.' in config_key:
        parts = config_key.split('.')
        result = {}
        current = result
        
        for i, part in enumerate(parts):
            if i == len(parts) - 1:
                current[part] = parsed_config
            else:
                current[part] = {}
                current = current[part]
        
        return result
    
    return {config_key: parsed_config}


# 转换函数映射表
TRANSFORM_FUNCTIONS = {
    "enterprise_config_center": enterprise_config_transform,
    "microservice_registry": microservice_registry_transform,
    "cloud_native_config": cloud_native_transform,
    "legacy_mainframe_config": legacy_mainframe_transform,
    "simple_key_value_store": simple_kv_transform,
    "saas_tenant_config": saas_tenant_transform,
    "xml_config": xml_config_transform
}


def get_transform_function(config_source_name: str):
    """
    根据配置源名称获取对应的转换函数
    
    Args:
        config_source_name: 配置源名称
        
    Returns:
        对应的转换函数，如果没有找到则返回None
    """
    return TRANSFORM_FUNCTIONS.get(config_source_name)


# 使用示例
if __name__ == "__main__":
    # 示例：企业配置转换
    enterprise_raw = {
        "configuration_identifier": "database.rdbs.mysql",
        "configuration_payload": '{"host": "prod-mysql.enterprise.com", "port": 3306}',
        "organization_code": "HSBC_GLOBAL",
        "environment_code": "PRODUCTION",
        "priority_order": 10
    }
    
    result = enterprise_config_transform(enterprise_raw)
    print("企业配置转换结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # 示例：遗留系统配置转换
    legacy_raw = {
        "config_key": "DB_MYSQL_PROD",
        "config_value": "HOST=legacy-mysql.company.com;PORT=3306;DB=legacy_db;USER=legacy_user",
        "tenant_id": "FINANCE",
        "environment": "PROD"
    }
    
    result = legacy_mainframe_transform(legacy_raw)
    print("\n遗留系统配置转换结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))

from modules.pg_database.llm.llm_chat import get_embeddings
from modules.pg_database.pgvector.rag import hybrid_search
from utils.db.get_partitionkey import get_or_create_partition_key
from loguru import logger


def get_all_content(pg_client, mysql_client, mysql_name, partition: "str",
                    serach_content, type: list[str], expr: "str" = None,topk=3):
    pg_client.set_table('hsbc_embedding_data')
    partition_key = get_or_create_partition_key(partition, mysql_client)
    serach_embedding = get_embeddings(serach_content)["data"][0]['embedding']
    indicator_vec_dict = {
        "embedding": serach_embedding
    }
    rank_type = {"type": "hybrid-weighted", "rank_rule": [{
        "embedding": 1.0
    }]}
    out_fields = ["content_id"]
    all_out = {}
    for li_type in type:
        if expr:
            li_expr = f"{expr} and embedding_type = '{li_type}'"
        else:
            li_expr = f"embedding_type = '{li_type}'"
        results = hybrid_search(
            pgvector_client=pg_client,
            table_name="hsbc_embedding_data",
            vec_dict=indicator_vec_dict,
            rank_dict=rank_type,
            out_filed=out_fields,
            topk=topk,
            expr=li_expr,
            partition_name=partition_key,
            metric_type="cosine"
        )
        logger.info(f"results: {results}")
        if not results:
            logger.warning(f"搜索内容为{serach_content}，结果为空，返回空字典")
            return {}
        results = [result['content_id'] for result in results if result['distance'] < 0.3]
        if mysql_name == 'where_info':
            mysql_get_content = {
                "op_type": "SELECT",
                "table_name": mysql_name,
                "data_dict": {
                    "logic": "AND",
                    "conditions": [
                        {"type": "in", "col_name": "where_id", "col_val": results}
                    ]
                }
            }
        else:
            mysql_get_content = {
                "op_type": "SELECT",
                "table_name": mysql_name,
                "data_dict": {
                    "logic": "AND",
                    "conditions": [
                        {"type": "in", "col_name": "col_code", "col_val": results}
                    ]
                }
            }
        all_out[li_type] = mysql_client.batch_operation(mysql_get_content)[0]
    return all_out


if __name__ == '__main__':
    from utils.common.config_util import config
    from modules.pg_database.pgvector.pgvector_class import PgVectorClass
    from modules.pg_database.mysql.mysql_memory import MySQLClientExtended

    pgvector_client = PgVectorClass(**config.pgvector)
    print("PgVectorClient initialized")
    mysql_client = MySQLClientExtended(**config.mysql)
    zz = get_all_content(pg_client=pgvector_client, mysql_client=mysql_client, mysql_name='model_info',
                         partition='$1001', serach_content="资产回报率", type=['col_name_cn', "col_desc"])
    print(zz)

"""
DD数据需求管理系统

提供5张表的基本CRUD功能和搜索功能：
- 统一CRUD操作（crud.py）
- 搜索功能（search.py）
- 数据实体和仓储（biz/kb/vector模块）

主要接口：
- DDCrud: 统一CRUD操作类
- DDSearch: 搜索功能类
"""

from .crud import DDCrud
from .search import DDSearch
from .shared.constants import DDConstants
from .shared.exceptions import DDError, DDValidationError, DDNotFoundError

__version__ = "1.0.0"

__all__ = [
    # 主要功能
    "DDCrud",
    "DDSearch",

    # 常量和异常
    "DDConstants",
    "DDError",
    "DDValidationError",
    "DDNotFoundError",
]

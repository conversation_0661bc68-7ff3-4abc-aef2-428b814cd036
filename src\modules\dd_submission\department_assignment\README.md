# 部门职责分配模块

基于DD分发前数据的智能部门职责分配功能，实现从`dd_pre_distribution`到确定业务部门（`dr22`字段）的自动化分配。

## 功能特性

### 核心功能
- **历史匹配搜索**: 基于`dr09`（数据项名称）和`dr17`（数据项定义）进行智能匹配
- **精确匹配优先**: 100%匹配dr09和dr17字段时直接返回结果
- **混合搜索兜底**: 精确匹配失败时使用向量+文本混合搜索
- **四层业务筛选**: 套系类型 → 报表类型 → 提交类型 → 数据层的逐层筛选
- **智能决策逻辑**: 唯一匹配、多个匹配、回退匹配的自动决策

### 四层筛选逻辑
1. **第一层 - 套系类型筛选**:
   - 标准套系：`1104`、`DJZ`、`EAST`、`一表通`
   - Survey套系：`survey`等非标准类型
   
2. **第二层 - 报表类型筛选**:
   - `detail`（明细）或 `index`（指标）

3. **第三层 - 提交类型筛选**:
   - `range`（范围）或 `submission`（填报项）

4. **第四层 - 数据层筛选**:
   - `dr01`字段匹配验证

### 决策逻辑
- **唯一结果**: 任一层筛选后剩余唯一记录，立即返回
- **多个结果**: 所有层筛选完仍有多个结果，按相似度排序返回最佳
- **无结果**: 某层筛选后无结果，返回上一层中得分最高的记录

## 快速开始

### 基本使用

```python
import asyncio
from service import get_client
from modules.dd_submission.department_assignment import (
    DepartmentAssignmentLogic,
    DepartmentAssignmentRequest
)

async def basic_example():
    # 1. 获取数据库客户端
    rdb_client = await get_client("database.rdbs.mysql")
    vdb_client = await get_client("database.vdbs.pgvector")
    
    # 2. 创建部门分配逻辑实例
    logic = DepartmentAssignmentLogic(rdb_client, vdb_client)
    
    # 3. 创建分配请求
    request = DepartmentAssignmentRequest(
        submission_id="dd_pre_001",           # 填报项ID
        dr09="客户姓名",                      # 数据项名称
        dr17="客户的真实姓名信息",             # 数据项定义
        set_value="1104",                     # 套系值
        report_type="detail",                 # 报表类型
        submission_type="submission",         # 提交类型
        dr01="ADS"                           # 数据层
    )
    
    # 4. 执行部门分配
    result = await logic.assign_department(request)
    
    # 5. 获取推荐部门
    recommended_dept = result.recommended_department
    confidence = result.confidence_level
    
    print(f"推荐部门: {recommended_dept}")
    print(f"置信度: {confidence}")

asyncio.run(basic_example())
```

### 分配结果处理

```python
# 检查分配结果
if result.confidence_level == "unique":
    print("找到唯一匹配，高置信度推荐")
    department = result.recommended_department
    
elif result.confidence_level == "consensus":
    print("多个记录指向同一部门，一致性推荐")
    department = result.recommended_department
    
elif result.confidence_level == "multiple":
    print("找到多个匹配，选择最佳匹配")
    department = result.recommended_department
    
else:
    print("未找到匹配或匹配质量较低")

# 获取详细分配信息
assignment_info = result.get_department_assignment()
print(f"分配详情: {assignment_info}")
```

## API参考

### DepartmentAssignmentRequest

分配请求参数：

```python
@dataclass
class DepartmentAssignmentRequest:
    # 必填字段
    submission_id: str              # 填报项ID
    dr09: str                      # 数据项名称
    dr17: str                      # 数据项定义
    set_value: str                 # 套系值
    report_type: str               # 报表类型
    submission_type: str           # 提交类型
    dr01: str                     # 数据层
    
    # 可选字段
    knowledge_id: Optional[str] = None      # 知识库ID
    data_layer: Optional[str] = None        # 数据层（搜索范围）
    version: Optional[str] = None           # 版本
    
    # 搜索配置
    limit: int = 100                        # 搜索结果限制
    min_score: float = 0.3                  # 最小相似度分数
    vector_weight: float = 0.6              # 向量搜索权重
    text_weight: float = 0.4                # 文本搜索权重
```

### DepartmentAssignmentResult

分配结果：

```python
@dataclass
class DepartmentAssignmentResult:
    request: DepartmentAssignmentRequest    # 原始请求
    
    # 搜索结果
    exact_match_found: bool                 # 是否找到精确匹配
    exact_match_count: int                  # 精确匹配数量
    hybrid_search_count: int                # 混合搜索数量
    total_candidates: int                   # 总候选数量
    
    # 筛选结果
    filter_results: List[FilterResult]     # 四层筛选结果
    
    # 最终结果
    final_records: List[HistoricalRecord]  # 最终记录
    recommended_department: Optional[str]   # 推荐部门（dr22）
    confidence_level: str                   # 置信度级别
    
    # 处理信息
    search_time_ms: float                   # 搜索耗时
    processing_notes: List[str]             # 处理说明
    
    def get_department_assignment(self) -> Dict[str, Any]:
        """获取部门分配结果（前端格式）"""
```

## 运行示例

### 功能测试
```bash
# 进入测试目录
cd src/modules/dd_submission/department_assignment

# 运行完整功能测试（使用模拟客户端）
python run_tests.py --use-mock

# 运行真实数据库测试
python run_tests.py --db-host localhost --db-port 3306 --db-name hsbc_knowledge --db-user root --db-password password
```

### 测试场景覆盖
1. **精确匹配测试** - 验证dr09和dr17完全匹配的场景
2. **混合搜索测试** - 验证分词、向量搜索和四层筛选
3. **TF-IDF推荐测试** - 验证部门推荐兜底机制
4. **批量处理测试** - 验证完整的批量分配流程

### 测试数据说明
- **report_code**: g0107_beta_v1.0
- **套系类型**: SET_A, SET_B, SET_C, survey
- **部门**: DEPT_FINANCE, DEPT_CREDIT, DEPT_RISK, DEPT_BUSINESS

## API接口

### 单个部门分配接口
```
POST /api/dd_submission/assignment/assign-department
```

请求体：
```json
{
    "submission_id": "dd_pre_001",
    "dr09": "客户姓名",
    "dr17": "客户的真实姓名信息",
    "set_value": "1104",
    "report_type": "detail",
    "submission_type": "submission",
    "dr01": "ADS"
}
```

响应：
```json
{
    "success": true,
    "data": {
        "submission_id": "dd_pre_001",
        "recommended_department": "风险管理部",
        "confidence_level": "unique",
        "assignment_result": {
            "recommended_department": "风险管理部",
            "confidence_level": "unique",
            "total_candidates": 5,
            "final_count": 1
        },
        "search_statistics": {
            "exact_match_found": true,
            "exact_match_count": 3,
            "total_candidates": 3,
            "search_time_ms": 156.78
        }
    }
}
```

### 批量部门分配接口（新增）
```
POST /api/dd_submission/assignment/batch-assign
```

**请求体：**
```json
{
    "report_code": "G0107_beta_v1.0"
}
```

**响应：**
```json
{
    "report_code": "g0107_beta_v1.0",
    "items": [
        {
            "entry_id": "TEST_001",
            "entry_type": "submission",
            "DR22": ["DEPT_FINANCE"],
            "BDR01": ["DEPT_FINANCE"],
            "BDR03": ""
        },
        {
            "entry_id": "TEST_002",
            "entry_type": "range",
            "DR22": ["DEPT_CREDIT"],
            "BDR01": ["DEPT_CREDIT"],
            "BDR03": ""
        }
    ]
}
```

**输出格式说明：**
- `entry_id`: 搜索项的submission_id
- `entry_type`: 搜索项的submission_type（不是固定的"ITEM"）
- `DR22`: 部门ID数组
- `BDR01`: 与DR22相同的部门ID数组
- `BDR03`: 置空字段（空字符串）

### 健康检查接口
```
GET /api/dd_submission/assignment/health
```

### 常量配置接口
```
GET /api/dd_submission/assignment/constants
```

## 技术实现

### 核心组件

1. **DepartmentAssignmentLogic**: 主业务逻辑类
2. **搜索策略**: 精确匹配 + 混合搜索
3. **筛选引擎**: 四层业务逻辑筛选
4. **决策引擎**: 智能结果决策

### 搜索流程

```mermaid
flowchart TD
    A[输入请求] --> B[精确匹配搜索]
    B --> C{找到精确匹配?}
    C -->|是| D[返回精确匹配结果]
    C -->|否| E[混合搜索]
    E --> F[四层筛选]
    F --> G[套系类型筛选]
    G --> H[报表类型筛选]
    H --> I[提交类型筛选]
    I --> J[数据层筛选]
    J --> K[智能决策]
    K --> L[返回推荐部门]
```

### 筛选优先级

- **标准套系优先**: 1104、DJZ、EAST、一表通优先于Survey类型
- **相同套系优先**: 相同套系的标准报送具有最高优先级
- **精确匹配优先**: 100%匹配的结果优先于模糊匹配
- **相似度排序**: 多个结果时按相似度分数排序

## 配置说明

### 常量配置

- **套系类型**: 标准套系和Survey套系的定义和映射
- **报表类型**: detail和index的映射关系
- **提交类型**: range和submission的映射关系
- **数据层**: ADS、BDM、IDM等数据层的映射

### 性能配置

- **搜索限制**: 默认100条，最大500条
- **相似度阈值**: 默认0.3，可调整
- **搜索权重**: 向量搜索0.6，文本搜索0.4
- **缓存时间**: 1小时语料库缓存

## 注意事项

1. **数据库依赖**: 需要MySQL和PostgreSQL+pgvector服务
2. **服务层配置**: 确保Hydra配置正确
3. **DD知识库集成**: 依赖现有的DD知识库模块
4. **TF-IDF处理**: 复用现有的TF-IDF分词处理逻辑
5. **搜索性能**: 大量数据时建议调整搜索限制

## 错误处理

- **DepartmentAssignmentValidationError**: 参数验证异常
- **DepartmentAssignmentSearchError**: 搜索异常
- **DepartmentAssignmentFilterError**: 筛选异常
- **DepartmentAssignmentDatabaseError**: 数据库异常

## 版本信息

- **版本**: 1.0.0
- **依赖**: DD知识库模块、DD提交记录匹配模块
- **Python**: 3.8+
- **测试状态**: ✅ 基本功能实现完成
- **部署状态**: ✅ 可用于测试环境

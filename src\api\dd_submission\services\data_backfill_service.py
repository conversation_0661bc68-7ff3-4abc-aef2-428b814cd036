#!/usr/bin/env python3
"""
数据回填核心服务

实现完整的数据回填逻辑：
1. 接收report_code、dept_id、step、data参数
2. 基于version+dept_id+submission_id的唯一键查找和更新
3. 只更新允许的字段（dr22、bdr01、bdr02、bdr03、bdr04）
4. 批量处理支持和性能优化
5. 标准响应格式
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import json

from sqlalchemy.util import deprecated

logger = logging.getLogger(__name__)

from service import get_client

from .validation_service import ValidationService
from fastapi import BackgroundTasks
from modules.knowledge.dd import DDCrud



class DataBackfillDUTY:
    step = 'DUTY'
    def __init__(self, rdb_client=None, vdb_client=None):
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client

    async def get_report_post_values(self, version: str, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:

        sql = """
        SELECT *
        FROM biz_dd_post_distribution
        WHERE version = :version
        """
        result = await self.rdb_client.afetch_all(sql, {"version": version})
        return result

    async def process_data_backfill(self, version: str, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        义务解读部分：前端确认步骤，用户修改并确认该阶段的所有数据，后端修改并保存到biz_dd_post_distribution表
        
        data:
        [
            {
            "entry_id": "21c5b1df9cbf4a5dbff420509fca1ded",
            "entry_type": "SUBMISSION",
            "DR22": ["DEPT789", "DEPT101"],
            "BDR01": ["DEPT789", "DEPT101"],
            "BDR03": ""
            }
        ]
        """
        # 预处理输入data，将列表DR22分裂为多个，以entry_id为key，value为list[Dict[str, Any]]
        input_data = defaultdict(list)

        for record in data:
            dr22_list = record.get('DR22',[])
            for dept in dr22_list:
                input_data[record['entry_id']].append(
                    {
                        'entry_id': record['entry_id'],
                        'entry_type': record['entry_type'],
                        'DR22': dept,
                        'BDR01': dept,
                        'BDR03': record.get('BDR03',''),
                    }
                )

        # 获取业务表中，dd报表对应的所有值
        dd_post_distribution_values = await self.get_report_post_values(version, data)
        dd_records: list[Dict[str, Any]] = dd_post_distribution_values.data
        dd_records_dict = defaultdict(list)
        for record in dd_records:
            dd_records_dict[record['submission_id']].append(record)

        # 统计每个entry_id的修改情况
        # 对于每个entry_id，input中有但post中表中没有的，需要新增；post中有但input中没有的，需要删除
        # 业务中，传入的entry_id（报送项id）数量与post表中相同version的完全一致
        insert_records = []
        delete_records = []

        for input_submission_id, input_records_list in input_data.items():
            submission_records: List[dict] = dd_records_dict.get(input_submission_id, [])
            if submission_records:
                insert_base_data = {
                    'pre_distribution_id': submission_records[0].get('pre_distribution_id', ''),
                    'report_type': submission_records[0].get('report_type', ''),
                    'set': submission_records[0].get('set', ''),
                    'dr01': submission_records[0].get('dr01', ''),
                    'dr07': submission_records[0].get('dr07', ''),
                    'version': version,
                }
                submission_records_dept_dict = {record['dr22']: record['id'] for record in submission_records}
                # 遍历input_records_list,若不在已有数据中，则新增
                for input_record in input_records_list:
                    input_record_dept = input_record.get('DR22', '')
                    if input_record_dept not in submission_records_dept_dict:
                        insert_data = insert_base_data.copy()
                        insert_data['DR22'] = input_record_dept
                        insert_data['BDR01'] = input_record_dept
                        insert_data['BDR03'] = input_record.get('BDR03', '')
                        insert_data['dept_id'] = input_record_dept
                        insert_data['submission_id'] = input_submission_id
                        insert_data['submission_type'] = {"TABLE":"RANGE","ITEM":"SUBMISSION"}.get(input_record.get('entry_type', ''), '')
                        insert_records.append(insert_data)
                # 遍历submission_records，如果不在input中则删除
                input_depts_set = set(input_record.get('DR22', '') for input_record in input_records_list)
                for submission_record in submission_records:
                    if submission_record['dr22'] not in input_depts_set:
                        delete_records.append(submission_record)

        # 执行数据库操作
        insert_count = 0
        delete_count = 0
        error_messages = []

        try:
            # 批量插入新记录
            if insert_records:
                insert_request = {
                    'table': 'biz_dd_post_distribution',
                    'data': insert_records
                }
                insert_result = await self.rdb_client.abatch_insert(
                    insert_request,
                    batch_size=100,
                    max_concurrency=3,
                    timeout_per_batch=60.0
                )
                if insert_result.success:
                    insert_count = insert_result.affected_rows
                    logger.info(f"成功插入 {insert_count} 条记录到 biz_dd_post_distribution 表")
                else:
                    error_messages.append(f"插入操作失败: {insert_result}")

            # 批量删除记录
            if delete_records:
                # 构建删除条件：根据id删除
                delete_conditions = []
                for record in delete_records:
                    delete_conditions.append({'id': record['id']})
                
                delete_result = await self.rdb_client.abatch_delete(
                    table='biz_dd_post_distribution',
                    conditions=delete_conditions,
                    batch_size=100,
                    max_concurrency=3,
                    timeout_per_batch=60.0
                )
                if delete_result.success:
                    delete_count = delete_result.affected_rows
                    logger.info(f"成功删除 {delete_count} 条记录从 biz_dd_post_distribution 表")
                else:
                    error_messages.append(f"删除操作失败: {delete_result.message}")

            # 返回结果
            if error_messages:
                return {
                    "code": "400",
                    "msg": f"部分操作失败: {'; '.join(error_messages)}",
                    "statistics": {
                        "total_count": len(data),
                        "inserted_count": insert_count,
                        "deleted_count": delete_count,
                        "error_count": len(error_messages)
                    }
                }
            else:
                return {
                    "code": "200",
                    "msg": "数据回填成功",
                    "statistics": {
                        "total_count": len(data),
                        "inserted_count": insert_count,
                        "deleted_count": delete_count,
                        "updated_count": insert_count + delete_count
                    }
                }

        except Exception as e:
            logger.error(f"数据回填操作异常: {e}")
            return {
                "code": "500",
                "msg": f"数据回填操作异常: {str(e)}",
                "statistics": {
                    "total_count": len(data),
                    "inserted_count": 0,
                    "deleted_count": 0,
                    "error_count": len(data)
                }
            }
    

class DataBackfillEXTRACTION:
    step = 'EXTRACTION'
    def __init__(self, rdb_client=None, vdb_client=None):
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
    

    async def get_report_pre_values(self, version: str, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:

        sql = """
        SELECT id,report_type,`set`
        FROM biz_dd_pre_distribution
        WHERE version = :version
        """
        result = await self.rdb_client.afetch_all(sql, {"version": version})
        return result

    async def process_data_backfill(self, version: str, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        报表解读部分
        data:
        [
            {
            "entry_id": "21c5b1df9cbf4a5dbff420509fca1ded",
            "entry_type": "SUBMISSION",
            "DR01": ...,
            ...
            "DR21":...
            }
        ]
        """
        # 获取业务表中，dd报表对应的所有记录
        dd_pre_distribution_values = await self.get_report_pre_values(version, data)
        dd_pre_distribution_values = dd_pre_distribution_values.data
        dd_pre_ids = [record['id'] for record in dd_pre_distribution_values]

        # 批量删除已有数据
        delete_conditions = []
        for record in dd_pre_distribution_values:
            delete_conditions.append({'id': record['id']})
        delete_result = await self.rdb_client.abatch_delete(
            table='biz_dd_pre_distribution',
            conditions=delete_conditions,
        )


        # 将input_data写入数据库
        insert_base_data = {
            'version': version,
            'set': dd_pre_distribution_values[0].get('set', ''),
            'report_type': dd_pre_distribution_values[0].get('report_type', ''),
        }
        insert_records = []
        for record in data:
            insert_data = insert_base_data.copy()   
            insert_data['submission_id'] = record['entry_id']
            insert_data['submission_type'] = {"TABLE":"RANGE","ITEM":"SUBMISSION"}.get(record.get('entry_type', ''), '')
            for key, value in record.items():
                if key.startswith('DR'):
                    insert_data[key.lower()] = value
            insert_records.append(insert_data)
        # 批量插入
        insert_result = await self.rdb_client.abatch_insert(
            table='biz_dd_pre_distribution',
            data=insert_records,
            batch_size=100,
            max_concurrency=3,
            timeout_per_batch=60.0
        )

        if insert_result.success:
            return {
                "code": "200",
                "msg": "数据回填成功",
                "statistics": {
                    "total_count": len(data),
                    "inserted_count": insert_result.affected_rows,
                    "error_count": 0
                }
            }
        else:
            return {
                "code": "400",
                "msg": "数据回填失败",
                "statistics": {
                    "total_count": len(data),
                    "inserted_count": 0,
                    "error_count": len(data)
                }
            }


class DataBackfillBIZ:
    step = 'BIZ'
    def __init__(self, rdb_client=None, vdb_client=None):
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
    

    async def get_report_post_values(self, version: str, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:

        sql = """
        SELECT id,submission_id,dept_id
        FROM biz_dd_post_distribution
        WHERE version = :version
        """
        result = await self.rdb_client.afetch_all(sql, {"version": version})
        return result

    async def process_data_backfill(self, version: str, dept_id: str, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        业务解读部分
        data:
        [
            {
            "entry_id": "21c5b1df9cbf4a5dbff420509fca1ded",
            "entry_type": "SUBMISSION",
            "BDR01": ...,
            ...
            "BDR18":...
            }
        ]
        """
        # 获取业务表中，dd报表对应的所有记录
        dd_post_distribution_values = await self.get_report_post_values(version, data)
        dd_post_distribution_values = dd_post_distribution_values.data
        dd_post_distribution_dict = {str(record['submission_id'])+str(record['dept_id']): record for record in dd_post_distribution_values}

        # 使用abatch_update更新数据 
        update_records = []
        for input_data in data:
            input_key = input_data['entry_id'] + dept_id
            orig_id = dd_post_distribution_dict.get(input_key, {}).get('id', '')
            if orig_id:
                update_records.append({
                    'filters': {'id': orig_id},
                    'data': {col.lower(): input_data.get(col, '') for col in input_data.keys() if col.startswith('BDR')}
                })

        if not update_records:
            return {
                "code": "400",
                "msg": "未找到需要回填的数据",
                "statistics": {
                    "total_count": len(data),
                    "updated_count": 0,
                    "error_count": 0
                }
            }

        update_result = await self.rdb_client.abatch_update(
            table='biz_dd_post_distribution',
            updates=update_records,
            batch_size=100,
            max_concurrency=3,
            timeout_per_batch=60.0
        )

        if update_result.success:
            return {
                "code": "200",
                "msg": "数据回填成功",
                "statistics": {
                    "total_count": len(data),
                    "updated_count": update_result.affected_rows,
                    "error_count": 0
                }
            }
        else:
            return {
                "code": "400",
                "msg": "数据回填失败",
                "statistics": {
                    "total_count": len(data),
                    "inserted_count": 0,
                    "error_count": len(data)
                }
            }


class DataBackfillTECH:
    step = 'TECH'
    def __init__(self, rdb_client=None, vdb_client=None):
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
    

    async def get_report_post_values(self, version: str, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:

        sql = """
        SELECT id,submission_id,dept_id
        FROM biz_dd_post_distribution
        WHERE version = :version
        """
        result = await self.rdb_client.afetch_all(sql, {"version": version})
        return result

    async def update_post_distribution(self, version: str, dept_id: str, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        更新biz_dd_post_distribution表
        """
        # 获取业务表中，dd报表对应的所有记录
        dd_post_distribution_values = await self.get_report_post_values(version, data)
        dd_post_distribution_values = dd_post_distribution_values.data
        dd_post_distribution_dict = {str(record['submission_id'])+str(record['dept_id']): record for record in dd_post_distribution_values}

        # 使用abatch_update更新数据 
        update_records = []
        for input_data in data:
            input_key = input_data['entry_id'] + dept_id
            orig_id = dd_post_distribution_dict.get(input_key, {}).get('id', '')
            if orig_id:
                update_records.append({
                    'filters': {'id': orig_id},
                    'data': {col.lower(): input_data.get(col, '') for col in input_data.keys() if col.startswith('SDR')}
                })

        if not update_records:
            return {
                "code": "400",
                "msg": "未找到需要回填的数据",
                "statistics": {
                    "total_count": len(data),
                    "updated_count": 0,
                    "error_count": 0
                }
            }

        update_result = await self.rdb_client.abatch_update(
            table='biz_dd_post_distribution',
            updates=update_records,
            batch_size=100,
            max_concurrency=3,
            timeout_per_batch=60.0
        )

        if update_result.success:
            return {
                "code": "200",
                "msg": "数据回填成功",
                "statistics": {
                    "total_count": len(data),
                    "updated_count": update_result.affected_rows,
                    "error_count": 0
                }
            }
        else:
            return {
                "code": "400",
                "msg": "数据回填失败",
                "statistics": {
                    "total_count": len(data),
                    "inserted_count": 0,
                    "error_count": len(data)
                }
            }

    async def _get_version_data(self, dd_crud: DDCrud, version: str) -> dict:
        """
        获取version的层级
        """
        pre_records = await dd_crud.batch_query_pre_distributions([{'version': version}])
        post_records = await dd_crud.batch_query_post_distributions([{'version': version}])

        res = {}
        res['report_layer'] = pre_records[0]['dr01']

        department_list = set()
        for record in post_records:
            department_list.add(record['dept_id'])
        res['report_department'] = json.dumps(list(department_list))

        res['report_type'] = pre_records[0]['report_type']
        res['set'] = pre_records[0]['set']


        return res


    async def write_to_knowledge_dd(self, version: str):
        """
        将用户已经最终确认的dd写入知识库
        """

        dd_crud = DDCrud(self.rdb_client, self.vdb_client)
        
        version_data = await self._get_version_data(dd_crud, version)

        # 构造dd_report_data主表所需数据 TODO 真实规则待确认
        knowledge_id = 'DD_PORCESS_TEST'
        report_name = str(version ).split('_')[0]
        report_code = str(version ).split('_')[0]
        report_layer = version_data['report_layer']
        report_department = version_data['report_department']
        report_type = version_data['report_type']
        report_set = version_data['set']

        report_data = {
            'knowledge_id': knowledge_id,
            'version': version,
            'report_name': report_name,
            'report_code': report_code,
            'report_layer': report_layer,
            'report_department': report_department,
            'report_type': report_type,
            'set': report_set,
            'is_manual': 0,
        }

        report_data_id = await dd_crud.batch_create_report_data([report_data])
        report_data_id = report_data_id[0]

        params = {
            'version': version,
            'report_data_id': report_data_id,
        }
        # 构造dd_report_data子表所需数据
        sql = f"""
        SELECT pre.submission_id as submission_id, :report_data_id as report_data_id, :version as version,
                pre.submission_type as type,
                pre.dr01 as dr01,
                pre.dr02 as dr02,
                pre.dr03 as dr03,
                pre.dr04 as dr04,
                pre.dr05 as dr05,
                pre.dr06 as dr06,
                pre.dr07 as dr07,
                pre.dr08 as dr08,
                pre.dr09 as dr09,
                pre.dr10 as dr10,
                pre.dr11 as dr11,
                pre.dr12 as dr12,
                pre.dr13 as dr13,
                pre.dr14 as dr14,
                pre.dr15 as dr15,
                pre.dr16 as dr16,
                pre.dr17 as dr17,
                pre.dr18 as dr18,
                pre.dr19 as dr19,
                pre.dr20 as dr20,
                pre.dr21 as dr21,
                post.dr22 as dr22,
                post.bdr01 as bdr01,
                post.bdr02 as bdr02,
                post.bdr03 as bdr03,
                post.bdr04 as bdr04,
                post.bdr05 as bdr05,
                post.bdr06 as bdr06,
                post.bdr07 as bdr07,
                post.bdr08 as bdr08,
                post.bdr09 as bdr09,
                post.bdr10 as bdr10,
                post.bdr11 as bdr11,
                post.bdr12 as bdr12,
                post.bdr13 as bdr13,
                post.bdr14 as bdr14,
                post.bdr15 as bdr15,
                post.bdr17 as bdr17,
                post.sdr01 as sdr01,
                post.sdr02 as sdr02,
                post.sdr03 as sdr03,
                post.sdr04 as sdr04,
                post.sdr05 as sdr05,
                post.sdr06 as sdr06,
                post.sdr07 as sdr07,
                post.sdr08 as sdr08,
                post.sdr09 as sdr09,
                post.sdr10 as sdr10,
                post.sdr11 as sdr11,
                post.sdr12 as sdr12,
                post.sdr13 as sdr13,
                post.sdr14 as sdr14,
                post.sdr15 as sdr15,
                post.idr01 as idr01,
                post.idr02 as idr02,
                post.idr03 as idr03,
                post.idr04 as idr04,
                post.idr05 as idr05
        FROM biz_dd_pre_distribution pre
        JOIN biz_dd_post_distribution post
        ON post.pre_distribution_id = pre.id
        AND pre.version = :version
        AND post.version = :version
        """
        result = await self.rdb_client.afetch_all(sql, params)
        result = result.data
    
        # 插入dd_submission_data表
        result = await dd_crud.create_submission_data(result)


        if result:
            logger.info(f"数据回填成功: {result}")
        else:
            logger.error(f"数据回填落知识库失败: {result}")

    async def process_data_backfill(self, version: str, dept_id: str, data: List[Dict[str, Any]], background_tasks: BackgroundTasks) -> Dict[str, Any]:
        """
        技术解读部分
        data:
        [
            {
            "entry_id": "21c5b1df9cbf4a5dbff420509fca1ded",
            "entry_type": "SUBMISSION",
            "SDR01": ...,
            ...
            "SDR15":...
            }
        ]
        """
        # 更新业务表记录
        result = await self.update_post_distribution(version, dept_id, data)
        if result['code'] != '200':
            return result

        # 写入知识库
        background_tasks.add_task(self.write_to_knowledge_dd, version)

        return result   


class DataBackfillService:
    """数据回填核心服务"""

    def __init__(self, rdb_client=None, vdb_client=None):
        """初始化数据回填服务"""
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client

        # 初始化核心组件
        self.validation_service = ValidationService(rdb_client) if rdb_client else None
        self.backfill_adapter = None

        # 性能统计
        self.performance_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_items_processed': 0,
            'total_items_updated': 0,
            'avg_processing_time': 0.0
        }

    async def initialize(self):
        """初始化服务（获取数据库客户端）"""
        if not self.rdb_client:
            self.rdb_client = await get_client('database.rdbs.mysql')
        if not self.vdb_client:
            try:
                self.vdb_client = await get_client('database.vdbs.pgvector')
            except:
                logger.warning("向量数据库客户端初始化失败，将使用关系数据库")
                self.vdb_client = None

        # 重新初始化组件
        self.validation_service = ValidationService(self.rdb_client)

    async def process_data_backfill(self,
                                  report_code: str,
                                  dept_id: str,
                                  step: str,
                                  data: List[Dict[str, Any]],
                                  background_tasks: BackgroundTasks) -> Dict[str, Any]:
        """处理数据回填主流程"""
        await self.initialize()

        start_time = time.time()
        self.performance_stats['total_requests'] += 1
        self.performance_stats['total_items_processed'] += len(data)

        try:
            version = report_code  # 直接使用，不拆分

            logger.info(f"开始数据回填处理: version={version}, dept_id={dept_id}, step={step}, 数据量={len(data)}")

            # 验证请求参数
            validation_result = await self._validate_backfill_request(version, dept_id, step, data)
            if not validation_result['valid']:
                self.performance_stats['failed_requests'] += 1
                return {
                    "code": "400",
                    "msg": validation_result['error'],
                    "statistics": {
                        "total_count": len(data),
                        "updated_count": 0,
                        "error_count": len(data)
                    }
                }
            # 检查version是否存在
            version_exists = await self._check_version_exists(version)
            if not version_exists:
                return {
                    "code": "400",
                    "msg": "report_code不存在",
                    "statistics": {
                        "total_count": len(data),
                        "updated_count": 0,
                        "error_count": len(data)
                    }
                }

            # 处理step分支，根据step转发至对应的服务
            if step == 'EXTRACTION':
                service = DataBackfillEXTRACTION(self.rdb_client, self.vdb_client)
                result = await service.process_data_backfill(version, data)
            elif step == 'DUTY':
                service = DataBackfillDUTY(self.rdb_client, self.vdb_client)
                result = await service.process_data_backfill(version, data)
            elif step == 'BIZ':
                service = DataBackfillBIZ(self.rdb_client, self.vdb_client)
                result = await service.process_data_backfill(version, dept_id, data)
            elif step == 'TECH': # 该步骤中同时需要做知识库沉淀，因为是用户的最终确认
                service = DataBackfillTECH(self.rdb_client, self.vdb_client)
                result = await service.process_data_backfill(version, dept_id, data, background_tasks)
            else:
                raise ValueError(f"不支持的step: {step}")

            return result

        except Exception as e:
            self.performance_stats['failed_requests'] += 1
            logger.error(f"数据回填处理失败: {e}")
            return {
                "code": "400",
                "msg": f"处理失败: {str(e)}",
                "statistics": {
                    "total_count": len(data),
                    "updated_count": 0,
                    "error_count": len(data),
                    "processing_time_ms": (time.time() - start_time) * 1000
                }
            }

    async def _check_version_exists(self, version: str) -> bool:
        """检查version是否存在"""
        sql = """
        SELECT count(*) as cnt
        FROM biz_dd_pre_distribution
        WHERE version = :version
        """
        result = await self.rdb_client.afetch_one(sql, {"version": version})
        return result['cnt'] > 0

    async def _validate_backfill_request(self,
                                       version: str,
                                       dept_id: str,
                                       step: str,
                                       data: List[Dict]) -> Dict[str, Any]:
        """验证回填请求"""
        try:
            # 验证基本参数
            if not version or not version.strip():
                return {"valid": False, "error": "report_code不能为空"}

            allowed_steps = ["EXTRACTION", "DUTY", "BIZ", "TECH"]
            if step not in allowed_steps:
                return {"valid": False, "error": f"step必须是以下值之一: {allowed_steps}"}

            if not data:
                return {"valid": False, "error": "data不能为空"}

            # 验证数据项
            for i, item in enumerate(data):
                if 'entry_id' not in item:
                    return {"valid": False, "error": f"data[{i}]缺少entry_id字段"}

                if not item['entry_id']:
                    return {"valid": False, "error": f"data[{i}]的entry_id不能为空"}

            return {"valid": True}

        except Exception as e:
            logger.error(f"验证回填请求失败: {e}")
            return {"valid": False, "error": f"验证失败: {str(e)}"}

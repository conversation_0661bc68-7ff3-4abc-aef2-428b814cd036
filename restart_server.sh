#!/bin/bash

# The absolute path to the project's root directory
PROJECT_DIR="/data/ideal/code/gsh_code/hsbc/src"
APP_DIR="api/knowledge"
# The application module path for uvicorn
APP_MODULE="main_production.py"

# Log file for the application output
LOG_FILE="${PROJECT_DIR}/logs/production_server.log"

# Port for the application
PORT=30337

# --- <PERSON><PERSON><PERSON> starts here ---

echo "Navigating to project directory: ${PROJECT_DIR}"
cd "${PROJECT_DIR}" || { echo "Failed to navigate to project directory. Exiting."; exit 1; }

# Create logs directory if it doesn't exist
mkdir -p "${PROJECT_DIR}/logs"

echo "Attempting to stop any existing running server..."
# Use pkill to find and kill the process that matches the app module pattern
pkill -f "${APP_MODULE}"
# The -f flag matches against the full command line string.

# Wait a couple of seconds to ensure the process is terminated and the port is released.
sleep 2

echo "Starting the server in the background..."

# --- VIRTUAL ENVIRONMENT ACTIVATION ---
# If you use a virtual environment, uncomment and edit the following line:
source /data/ideal/code/gsh_code/hsbc/.venv/bin/activate

# Run the server with nohup to keep it running after the terminal is closed.
# Redirect stdout and stderr to a log file.
nohup python ${APP_DIR}/${APP_MODULE} --host 0.0.0.0 --port ${PORT} >> "${LOG_FILE}" 2>&1 &

sleep 2

# Check if the process started
if pgrep -f "${APP_MODULE}" > /dev/null
then
    echo "Server started successfully."
    echo "PID: $(pgrep -f "${APP_MODULE}")"
    echo "Logging output to ${LOG_FILE}"
else
    echo "Server failed to start. Please check the logs at ${LOG_FILE}"
fi 
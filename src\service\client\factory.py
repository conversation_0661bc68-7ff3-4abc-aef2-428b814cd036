import logging
import asyncio
from typing import Any, Dict, Optional, Union
from omegaconf import DictConfig

import hydra
from .registry import ClientRegistry
from .cache import ClientCache
from .lifecycle import LifecycleManager
from .priority import ServicePriority, PriorityConfigMapper, get_priority_config_path
from .pool_parameter_mapper import PoolParameterMapper, map_pool_config
from ..exceptions import ClientError

logger = logging.getLogger(__name__)


class ClientFactory:
    """
    客户端工厂
    
    负责：
    - 客户端实例创建
    - 单例模式管理
    - 生命周期管理
    - 缓存管理
    """
    
    def __init__(self):
        self._registry = ClientRegistry()
        self._cache = ClientCache()
        self._lifecycle = LifecycleManager()
        self._initialized = False
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """初始化客户端工厂"""
        if self._initialized:
            return
        
        async with self._lock:
            if self._initialized:
                return
            
            await self._registry.initialize()
            await self._cache.initialize()
            await self._lifecycle.initialize()
            
            self._initialized = True
            logger.info("客户端工厂初始化完成")
    
    async def get_client(self,
                        config: Union[str, DictConfig],
                        singleton: bool = True,
                        priority: Optional[Union[str, ServicePriority]] = None,
                        priority_config_override: Optional[Dict[str, Any]] = None,
                        **kwargs) -> Any:
        """
        获取客户端实例 - 支持优先级和连接池参数映射的企业级实现

        Args:
            config: 配置对象或配置路径
            singleton: 是否使用单例模式
            priority: 可选的服务优先级 ('high', 'standard', 'low' 或 ServicePriority 枚举)
            priority_config_override: 手动优先级配置覆盖（用于自定义连接池参数）
            **kwargs: 额外参数

        Returns:
            客户端实例

        Examples:
            # 基础用法（向后兼容）
            >>> client = await get_client("database.rdbs.mysql")

            # 使用优先级参数
            >>> high_client = await get_client("database.rdbs.mysql", priority='high')
            >>> low_client = await get_client(cfg.database.rdbs.mysql, priority='low')

            # 手动优先级配置覆盖
            >>> custom_client = await get_client(
            ...     "database.rdbs.mysql",
            ...     priority='high',
            ...     priority_config_override={
            ...         'pool_size': 100,
            ...         'max_overflow': 200,
            ...         'pool_timeout': 5
            ...     }
            ... )

            # 直接使用完整配置路径
            >>> high_client = await get_client("database.rdbs.mysql_high_priority")
        """
        # 确保初始化（线程安全）
        await self._ensure_initialized()

        try:
            # 智能配置解析：处理priority参数和配置覆盖
            resolved_config = self._resolve_config_with_priority(config, priority)

            # 应用优先级配置覆盖和连接池参数映射
            final_config = self._apply_priority_config_override(
                resolved_config, priority_config_override, **kwargs
            )

            # 生成缓存键（包含覆盖配置）
            cache_key = self._generate_cache_key(final_config, kwargs)

            # 如果是单例模式，使用双重检查锁定模式
            if singleton:
                # 第一次检查（无锁）
                cached_client = await self._cache.get(cache_key)
                if cached_client:
                    logger.debug(f"从缓存获取客户端: {cache_key}")
                    return cached_client

                # 获取锁进行第二次检查
                async with self._lock:
                    # 第二次检查（有锁）
                    cached_client = await self._cache.get(cache_key)
                    if cached_client:
                        logger.debug(f"从缓存获取客户端 (锁定检查): {cache_key}")
                        return cached_client

                    # 创建新的客户端实例
                    client = await self._create_client(final_config, **kwargs)

                    # 缓存客户端
                    await self._cache.set(cache_key, client)
                    logger.debug(f"客户端已缓存: {cache_key}")

                    # 注册到生命周期管理器
                    await self._lifecycle.register(client, cache_key)

                    logger.info(f"客户端创建成功: {type(client).__name__}")
                    return client
            else:
                # 非单例模式，直接创建
                client = await self._create_client(final_config, **kwargs)

                # 注册到生命周期管理器（使用唯一ID）
                unique_id = f"{cache_key}_{id(client)}"
                await self._lifecycle.register(client, unique_id)

                logger.info(f"非单例客户端创建成功: {type(client).__name__}")
                return client

        except Exception as e:
            logger.error(f"获取客户端失败: {e}")
            raise ClientError(f"Failed to get client: {e}") from e

    def _resolve_config_with_priority(self,
                                     config: Union[str, DictConfig],
                                     priority: Optional[Union[str, ServicePriority]]) -> Union[str, DictConfig]:
        """
        智能配置解析：处理priority参数

        Args:
            config: 原始配置对象或配置路径
            priority: 可选的服务优先级

        Returns:
            解析后的配置对象或配置路径

        Raises:
            ClientError: 当配置路径和priority参数冲突时
        """
        # 如果没有提供priority参数，直接返回原配置
        if priority is None:
            return config

        # 标准化priority参数
        if isinstance(priority, str):
            priority_enum = ServicePriority.from_string(priority)
        else:
            priority_enum = priority

        # 处理字符串配置路径
        if isinstance(config, str):
            return self._resolve_string_config_with_priority(config, priority_enum)

        # 处理DictConfig对象
        else:
            return self._resolve_dict_config_with_priority(config, priority_enum)

    def _resolve_string_config_with_priority(self,
                                           config_path: str,
                                           priority: ServicePriority) -> str:
        """
        解析字符串配置路径与优先级

        Args:
            config_path: 配置路径字符串
            priority: 服务优先级

        Returns:
            解析后的配置路径

        Raises:
            ClientError: 当配置路径和priority参数冲突时
        """
        # 检查配置路径是否已经包含优先级信息
        existing_db_type, existing_priority = PriorityConfigMapper.parse_config_path(config_path)

        if existing_db_type and existing_priority:
            # 配置路径已包含优先级信息
            if existing_priority != priority:
                # 优先级冲突
                raise ClientError(
                    f"配置路径冲突: 路径 '{config_path}' 指定优先级为 '{existing_priority.value}', "
                    f"但 priority 参数指定为 '{priority.value}'. "
                    f"请使用完整配置路径或仅使用 priority 参数，不要同时使用."
                )
            # 优先级一致，直接返回原路径
            logger.debug(f"配置路径已包含正确优先级: {config_path}")
            return config_path

        elif existing_db_type:
            # 配置路径是基础路径，需要转换为优先级路径
            try:
                priority_config_path = PriorityConfigMapper.get_config_path(existing_db_type, priority)
                logger.debug(f"配置路径转换: {config_path} -> {priority_config_path}")
                return priority_config_path
            except ValueError as e:
                raise ClientError(f"无法为配置路径 '{config_path}' 应用优先级 '{priority.value}': {e}")

        else:
            # 无法解析的配置路径
            raise ClientError(
                f"无法解析配置路径 '{config_path}' 的数据库类型. "
                f"请使用标准格式如 'database.rdbs.mysql' 或 'database.vdbs.pgvector'"
            )

    def _resolve_dict_config_with_priority(self,
                                         config: DictConfig,
                                         priority: ServicePriority) -> DictConfig:
        """
        解析DictConfig对象与优先级

        Args:
            config: DictConfig配置对象
            priority: 服务优先级

        Returns:
            解析后的DictConfig对象

        Note:
            对于DictConfig对象，我们需要通过配置管理器获取对应的优先级配置
            这里暂时返回原配置，实际实现可能需要更复杂的逻辑
        """
        # TODO: 实现DictConfig对象的优先级解析
        # 这需要访问配置管理器来获取对应的优先级配置
        logger.warning(f"DictConfig对象的优先级解析暂未完全实现，使用原配置")
        return config

    def _apply_priority_config_override(self,
                                       config: Union[str, DictConfig],
                                       priority_config_override: Optional[Dict[str, Any]] = None,
                                       **kwargs) -> Union[str, DictConfig]:
        """
        应用优先级配置覆盖和连接池参数映射

        Args:
            config: 解析后的配置对象或配置路径
            priority_config_override: 手动优先级配置覆盖
            **kwargs: 额外参数

        Returns:
            应用覆盖后的配置
        """
        # 如果是字符串配置路径，直接返回（由Hydra处理）
        if isinstance(config, str):
            if priority_config_override:
                logger.warning(f"字符串配置路径 '{config}' 不支持优先级配置覆盖，覆盖将被忽略")
            return config

        # 处理DictConfig对象
        if isinstance(config, DictConfig):
            # 创建配置副本以避免修改原配置
            config_dict = dict(config)

            # 应用优先级配置覆盖
            if priority_config_override:
                logger.info(f"应用优先级配置覆盖: {list(priority_config_override.keys())}")
                config_dict.update(priority_config_override)

            # 检测数据库类型并应用连接池参数映射
            target_db_type = self._detect_database_type(config_dict)
            if target_db_type:
                mapped_config = self._apply_pool_parameter_mapping(config_dict, target_db_type)
                logger.debug(f"应用连接池参数映射: {target_db_type}")
                return DictConfig(mapped_config)

            return DictConfig(config_dict)

        return config

    def _detect_database_type(self, config: Dict[str, Any]) -> Optional[str]:
        """
        检测数据库类型

        Args:
            config: 配置字典

        Returns:
            数据库类型字符串，如果无法检测则返回None
        """
        # 从_target_检测
        target = config.get('_target_', '')

        if 'mysql' in target.lower():
            return 'mysql'
        elif 'pgvector' in target.lower():
            return 'pgvector'
        elif 'postgresql' in target.lower() or 'postgres' in target.lower():
            return 'postgresql'
        elif 'aiomysql' in target.lower():
            return 'aiomysql'
        elif 'asyncpg' in target.lower():
            return 'asyncpg'

        # 从配置参数检测
        if 'min_connections' in config and 'max_connections' in config:
            return 'pgvector'
        elif 'pool_size' in config and 'max_overflow' in config:
            return 'mysql'  # 假设SQLAlchemy主要用于MySQL

        return None

    def _apply_pool_parameter_mapping(self, config: Dict[str, Any], db_type: str) -> Dict[str, Any]:
        """
        应用连接池参数映射

        Args:
            config: 原始配置
            db_type: 数据库类型

        Returns:
            映射后的配置
        """
        try:
            # 提取连接池相关参数
            pool_params = {k: v for k, v in config.items()
                          if k in ['pool_size', 'max_overflow', 'pool_timeout', 'pool_recycle',
                                 'pool_pre_ping', 'min_connections', 'max_connections',
                                 'minsize', 'maxsize']}

            if not pool_params:
                return config  # 没有连接池参数，直接返回

            # 映射参数
            mapped_params = map_pool_config(pool_params, db_type)

            # 合并到原配置
            result_config = config.copy()
            result_config.update(mapped_params)

            logger.debug(f"连接池参数映射: {pool_params} -> {mapped_params}")
            return result_config

        except Exception as e:
            logger.warning(f"连接池参数映射失败: {e}，使用原始配置")
            return config

    async def _ensure_initialized(self):
        """确保工厂已初始化（线程安全）"""
        if not self._initialized:
            async with self._lock:
                if not self._initialized:
                    await self.initialize()
    
    async def _create_client(self, config: Union[str, DictConfig], **kwargs) -> Any:
        """创建客户端实例"""
        try:
            # 如果是字符串，从配置管理器获取配置
            if isinstance(config, str):
                config_path = config
                # 获取配置管理器
                from .. import get_config
                cfg_manager = await get_config()

                # 解析配置路径
                config_obj = cfg_manager._config
                for part in config_path.split('.'):
                    if hasattr(config_obj, part):
                        config_obj = getattr(config_obj, part)
                    else:
                        raise ClientError(f"配置路径不存在: {config_path}")

                config = config_obj

            # 检查配置是否有_target_字段
            if not hasattr(config, '_target_'):
                raise ClientError("配置缺少_target_字段")

            target_class = config._target_

            # 不过滤配置，直接使用原始配置 - 让Hydra和目标类自己处理参数验证
            # 如果参数不匹配，应该修改connection.yaml配置文件，而不是在代码中过滤
            filtered_config = config

            # 使用Hydra实例化客户端
            client = hydra.utils.instantiate(filtered_config, **kwargs)

            # 注释掉Service层的连接控制 - 应该由实例自己控制连接
            # if hasattr(client, 'connect') and callable(getattr(client, 'connect')):
            #     if asyncio.iscoroutinefunction(client.connect):
            #         await client.connect()
            #     else:
            #         client.connect()
            #     logger.debug(f"客户端连接成功: {target_class}")

            return client

        except Exception as e:
            logger.error(f"创建客户端失败: {e}")
            raise ClientError(f"Failed to create client: {e}") from e
    
    def _filter_config_for_factory(self, config: DictConfig) -> DictConfig:
        """
        过滤配置，只保留factory函数需要的参数

        Args:
            config: 原始配置

        Returns:
            过滤后的配置
        """
        from omegaconf import OmegaConf

        # 定义factory函数需要的基本参数
        # 数据库相关参数
        db_params = {
            'host', 'port', 'database', 'username', 'password', 'charset',
            'vector_dimension', 'distance_metric', 'enable_cache', 'cache_size',
            'cache_ttl', 'min_connections', 'max_connections'
        }

        # LLM模型相关参数
        llm_params = {
            'api_key', 'base_url', 'model_name', 'provider', 'model_parameters',
            'stream', 'temperature', 'max_tokens', 'top_p', 'top_k', 'frequency_penalty',
            'presence_penalty', 'stop', 'timeout', 'retry_attempts'
        }

        # 嵌入模型相关参数
        embedding_params = {
            'api_key', 'base_url', 'model_name', 'provider', 'batch_size',
            'timeout', 'retry_attempts', 'cache_enabled', 'cache_ttl'
        }

        # 通用参数
        common_params = {'_target_'}

        # 合并所有参数
        factory_params = common_params | db_params | llm_params | embedding_params

        # 创建过滤后的配置
        filtered_dict = {}

        for key, value in config.items():
            if key in factory_params:
                filtered_dict[key] = value

        # 转换回DictConfig
        filtered_config = OmegaConf.create(filtered_dict)

        logger.debug(f"配置过滤完成，保留参数: {list(filtered_dict.keys())}")
        return filtered_config

    def _smart_filter_config(self, config: DictConfig) -> DictConfig:
        """
        智能过滤配置，根据target类型决定保留哪些参数

        Args:
            config: 原始配置

        Returns:
            过滤后的配置
        """
        from omegaconf import OmegaConf

        target = config._target_

        # 数据库相关的factory函数
        if 'factory.create_mysql_client' in target or 'factory.create_postgresql_client' in target:
            # 数据库factory函数需要的参数
            db_factory_params = {
                '_target_', 'host', 'port', 'database', 'username', 'password', 'charset'
            }
            filtered_dict = {k: v for k, v in config.items() if k in db_factory_params}

        # LLM模型相关的类
        elif 'llm_model' in target or 'LLM' in target:
            # LLM模型需要的参数
            llm_params = {
                '_target_', 'api_key', 'base_url', 'model_name', 'provider',
                'model_parameters', 'stream', 'temperature', 'max_tokens'
            }
            filtered_dict = {k: v for k, v in config.items() if k in llm_params}

        # 嵌入模型相关的类
        elif 'embedding_model' in target or 'Embedding' in target:
            # 嵌入模型需要的参数
            embedding_params = {
                '_target_', 'api_key', 'base_url', 'model_name', 'provider', 'batch_size'
            }
            filtered_dict = {k: v for k, v in config.items() if k in embedding_params}

        else:
            # 未知类型，保留所有参数
            filtered_dict = dict(config.items())

        filtered_config = OmegaConf.create(filtered_dict)
        logger.debug(f"智能过滤完成，target: {target}, 保留参数: {list(filtered_dict.keys())}")
        return filtered_config

    def _generate_cache_key(self, config: Union[str, DictConfig], kwargs: Dict) -> str:
        """
        生成缓存键 - 企业级简洁实现

        原则：相同配置 = 相同实例（单例模式）
        优先级物理隔离：不同优先级配置生成不同缓存键
        """
        if isinstance(config, str):
            # 字符串配置路径 - 直接使用路径作为基础键
            base_key = config

            # 从配置路径中提取优先级信息（用于日志）
            db_type, priority = PriorityConfigMapper.parse_config_path(config)
            if priority:
                logger.debug(f"配置路径优先级: {db_type}:{priority.value}")
        else:
            # DictConfig对象 - 构建描述性键
            target = getattr(config, '_target_', 'unknown')
            host = getattr(config, 'host', 'localhost')
            port = getattr(config, 'port', 0)
            database = getattr(config, 'database', 'default')
            priority = getattr(config, 'priority', 'standard')
            service_tier = getattr(config, 'service_tier', 'normal')

            # 构建可读的缓存键
            base_key = f"{target}:{host}:{port}:{database}:priority_{priority}:tier_{service_tier}"

        # 处理额外参数（如果有）
        if kwargs:
            # 只包含影响客户端实例的参数
            relevant_kwargs = {k: v for k, v in kwargs.items()
                             if k not in ['timeout', 'retry_count', 'debug']}
            if relevant_kwargs:
                kwargs_str = ":".join(f"{k}={v}" for k, v in sorted(relevant_kwargs.items()))
                base_key = f"{base_key}:{kwargs_str}"

        # 确保键的长度合理（避免过长）
        if len(base_key) > 200:
            import hashlib
            hash_obj = hashlib.md5(base_key.encode('utf-8'))
            cache_key = f"hashed_{hash_obj.hexdigest()[:16]}"
        else:
            cache_key = base_key

        logger.debug(f"生成缓存键: {cache_key}")
        return cache_key

    
    async def create_client(self, 
                           config: Union[str, DictConfig],
                           **kwargs) -> Any:
        """
        创建新的客户端实例（非单例）
        
        Args:
            config: 配置对象或配置路径
            **kwargs: 额外参数
            
        Returns:
            客户端实例
        """
        return await self.get_client(config, singleton=False, **kwargs)
    
    async def get_cached_client(self, cache_key: str) -> Optional[Any]:
        """获取缓存的客户端"""
        return await self._cache.get(cache_key)
    
    async def remove_client(self, cache_key: str) -> bool:
        """移除客户端"""
        try:
            # 从缓存中移除
            client = await self._cache.remove(cache_key)
            
            # 从生命周期管理器中注销
            if client:
                await self._lifecycle.unregister(client)
            
            logger.info(f"客户端已移除: {cache_key}")
            return True
            
        except Exception as e:
            logger.error(f"移除客户端失败: {e}")
            return False
    
    async def list_clients(self) -> Dict[str, Any]:
        """列出所有缓存的客户端"""
        return await self._cache.list_all()
    
    # 便捷方法：基于新的 get_client API
    async def get_high_priority_client(self, config: Union[str, DictConfig], **kwargs) -> Any:
        """
        获取高优先级客户端的便捷方法

        Args:
            config: 配置对象或配置路径
            **kwargs: 额外参数

        Returns:
            高优先级客户端实例

        Example:
            >>> client = await factory.get_high_priority_client("database.rdbs.mysql")
        """
        return await self.get_client(config, priority=ServicePriority.HIGH, **kwargs)

    async def get_standard_priority_client(self, config: Union[str, DictConfig], **kwargs) -> Any:
        """
        获取标准优先级客户端的便捷方法

        Args:
            config: 配置对象或配置路径
            **kwargs: 额外参数

        Returns:
            标准优先级客户端实例

        Example:
            >>> client = await factory.get_standard_priority_client("database.rdbs.mysql")
        """
        return await self.get_client(config, priority=ServicePriority.STANDARD, **kwargs)

    async def get_low_priority_client(self, config: Union[str, DictConfig], **kwargs) -> Any:
        """
        获取低优先级客户端的便捷方法

        Args:
            config: 配置对象或配置路径
            **kwargs: 额外参数

        Returns:
            低优先级客户端实例

        Example:
            >>> client = await factory.get_low_priority_client("database.rdbs.mysql")
        """
        return await self.get_client(config, priority=ServicePriority.LOW, **kwargs)

    async def cleanup(self):
        """清理所有资源"""
        logger.info("开始清理客户端工厂...")

        # 清理生命周期管理器
        await self._lifecycle.cleanup()

        # 清理缓存
        await self._cache.cleanup()

        # 清理注册表
        await self._registry.cleanup()

        self._initialized = False
        logger.info("客户端工厂清理完成")
    
    @property
    def registry(self) -> ClientRegistry:
        """获取客户端注册表"""
        return self._registry
    
    @property
    def cache(self) -> ClientCache:
        """获取客户端缓存"""
        return self._cache
    
    @property
    def lifecycle(self) -> LifecycleManager:
        """获取生命周期管理器"""
        return self._lifecycle

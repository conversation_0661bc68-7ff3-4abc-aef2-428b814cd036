"""
业务逻辑生成步骤
根据生成的SQL查询生成JSON格式的业务逻辑描述，使用get_colkv.py提取表范围信息
"""

from typing import Dict, Any, List
import json
import re
import logging
import time

logger = logging.getLogger(__name__)

from pipeline.core.base_step import LLMStep
from pipeline.core.context import PipelineContext

class BusinessLogicGeneratorStep(LLMStep):
    """
    业务逻辑生成步骤
    根据生成的SQL查询生成JSON格式的业务逻辑描述

    输出格式：
    {
        "表范围": "使用get_colkv.py提取的表范围信息",
        "计算逻辑": "LLM生成的计算逻辑描述",
        "条件": "LLM生成的查询条件说明",
        "维度": "LLM生成的数据维度分析",
        "整体逻辑描述": "LLM生成的完整业务逻辑说明"
    }
    """

    def __init__(self, is_default: bool = True):
        """
        初始化业务逻辑生成步骤

        Args:
            is_default: 是否使用默认模式，默认为True
                       True: 不调用大模型，直接基于parser_info生成默认业务逻辑
                       False: 调用大模型生成业务逻辑
        """
        super().__init__(
            name="business_logic_generator",
            description="根据SQL查询生成结构化业务逻辑描述",
            template_name="business_logic_generator",
            parser_name="generic_json",  # 使用通用JSON解析器
            num_calls=1
        )
        self.is_default = is_default

    async def execute(self, context: PipelineContext) -> Dict[str, Any]:
        """
        执行业务逻辑生成步骤

        如果is_default为True，直接生成默认业务逻辑，不调用大模型
        如果is_default为False，调用大模型生成业务逻辑
        """
        if self.is_default:
            # 默认模式：直接基于parser_info生成业务逻辑
            start_time = time.time()
            try:
                
                logger.debug("使用默认模式生成业务逻辑，不调用大模型")
                default_result = self._create_default_business_logic_from_parser(context)
                await self.update_context(default_result, context)
                execution_time = time.time() - start_time
                
                # 记录成功结果
                context.record_step_result(
                    step_name=self.name,
                    success=True,
                    data=default_result
                )
                
                logger.info(f"步骤 {self.name} 执行成功，耗时 {execution_time:.2f}s")
                
                return context
            except Exception as e:
                execution_time = time.time() - start_time
                error_msg = f"步骤 {self.name} 执行失败: {str(e)}"
                logger.error(error_msg)
                
                # 记录失败结果
                context.record_step_result(
                    step_name=self.name,
                    success=False,
                    error=error_msg
                )
                
                # 重新抛出异常，让调用者决定如何处理
                raise
        else:
            # 大模型模式：调用父类的execute方法
            logger.info("使用大模型模式生成业务逻辑")
            return await super().execute(context)

    async def preprocess(self, context: PipelineContext) -> Dict[str, Any]:
        """阶段1: 预处理 - 构建prompt变量"""
        sql_candidates = context.get("sql_candidates", [])

        # 如果没有SQL候选，跳过执行
        if not sql_candidates:
            logger.warning("没有SQL候选，无法进行业务逻辑生成")
            return None

        # 选择第一个SQL作为主要分析对象（只提取SQL语句，不使用执行结果）
        primary_sql = sql_candidates[0] if sql_candidates else ""

        # 如果sql_candidates包含执行结果，只提取SQL语句部分
        if isinstance(primary_sql, dict) and "sql" in primary_sql:
            primary_sql = primary_sql["sql"]
        elif isinstance(primary_sql, dict) and "query" in primary_sql:
            primary_sql = primary_sql["query"]

        # 获取最终的数据库Schema
        db_schema = context.get("db_schema", "")


        return {
            "system": {
                "HINT": context.hint,
                "DB_SCHEMA": db_schema,
                "SQL_QUERY": primary_sql,
            },
            "user": {
                "USER_QUESTION": context.user_question,
            }
        }
    

    
    async def parse_result(self, result: List[str], context: PipelineContext) -> List[Any]:
        """阶段3: 解析LLM结果"""
        # 使用父类的generic_json解析器解析
        parsed_results = await super().parse_result(result, context)

        # 验证和处理解析结果
        final_results = []
        for parsed in parsed_results:
            # generic_json解析器已经返回字典格式，直接验证结构
            validated_result = self._validate_business_logic_json(parsed)
            final_results.append(validated_result)

        return final_results
    

    
    def _validate_business_logic_json(self, data: Any) -> Dict[str, Any]:
        """验证业务逻辑JSON结构"""
        if not isinstance(data, dict):
            return {
                "计算逻辑": "无法解析",
                "条件": "无法解析", 
                "维度": "无法解析",
                "整体逻辑描述": "JSON格式验证失败",
                "error": "数据不是有效的字典格式"
            }
        
        # 定义必需的字段
        required_fields = ["计算逻辑", "条件", "维度", "整体逻辑描述"]
        
        # 确保所有必需字段存在
        validated_data = {}
        for field in required_fields:
            if field in data:
                validated_data[field] = data[field]
            else:
                # 提供默认值
                default_values = {
                    "计算逻辑": "未提供计算逻辑描述",
                    "条件": "未提供查询条件说明",
                    "维度": "未提供数据维度分析",
                    "整体逻辑描述": "未提供完整业务逻辑说明"
                }
                validated_data[field] = default_values.get(field, "未提供")
        
        # 保留其他字段
        for key, value in data.items():
            if key not in validated_data:
                validated_data[key] = value
        
        return validated_data
    
    async def postprocess(self, parsed_results: List[Any], context: PipelineContext) -> Dict[str, Any]:
        """阶段4: 后处理 - 选择最佳结果"""
        if not parsed_results:
            logger.warning("业务逻辑生成步骤没有获得有效结果")
            return self._create_default_business_logic(context)
        
        # 选择第一个有效结果
        business_logic = parsed_results[0]
        
        # 如果有错误，创建默认结果
        if "error" in business_logic:
            logger.warning(f"业务逻辑生成有错误: {business_logic.get('error')}")
            return self._create_default_business_logic(context)
        try:
            parser_info = context.get("parser_info", {})
            table_scope = parser_info.get("tables", context.get("candidate_tables", ""))
            business_logic["表范围"] = table_scope
        except Exception as e:
            logger.warning(f"提取表范围失败: {e}")
        logger.debug("业务逻辑生成成功")
        return business_logic

    def _create_default_business_logic_from_parser(self, context: PipelineContext) -> Dict[str, Any]:
        """
        基于parser_info创建默认的业务逻辑描述（默认模式使用）

        Args:
            context: Pipeline上下文

        Returns:
            默认的业务逻辑JSON
        """
        parser_info = context.get("parser_info", {})
        candidate_tables = context.get("candidate_tables", "")

        # 构建默认业务逻辑JSON
        default_logic = {
            "表范围": parser_info.get("tables", candidate_tables),
            "计算逻辑": parser_info.get("value", ""),
            "条件": self._combine_conditions(parser_info),
            "维度": parser_info.get("columns", ""),
            "整体逻辑描述": f"针对用户问题「{context.user_question}」进行数据查询分析"
        }

        logger.debug(f"生成默认业务逻辑: {default_logic}")
        return default_logic

    def _combine_conditions(self, parser_info: Dict[str, Any]) -> str:
        """
        组合WHERE和JOIN条件

        Args:
            parser_info: SQL解析信息

        Returns:
            组合后的条件字符串
        """
        where_conditions = parser_info.get("where", "")
        join_conditions = parser_info.get("join", "")

        # 将WHERE和JOIN条件组合
        conditions = []
        if where_conditions:
            if isinstance(where_conditions, list):
                conditions.extend(where_conditions)
            else:
                conditions.append(str(where_conditions))

        if join_conditions:
            if isinstance(join_conditions, list):
                conditions.extend(join_conditions)
            else:
                conditions.append(str(join_conditions))

        return "\n".join(conditions) if conditions else ""

    def _create_default_business_logic(self, context: PipelineContext) -> Dict[str, Any]:
        """创建默认的业务逻辑描述"""
        # 从SQL解析结果中获取表范围信息
        parser_info = context.get("parser_info", {})
        table_scope = parser_info.get("tables", context.get("candidate_tables", ""))

        return {
            "表范围": table_scope,
            "计算逻辑": "基于用户问题进行数据查询和计算",
            "条件": context.hint if context.hint else "按照银保监会贷款口径进行筛选",
            "维度": "根据用户需求确定查询维度",
            "整体逻辑描述": f"针对用户问题「{context.user_question}」进行数据查询分析",
        }
    
    async def update_context(self, result: Dict[str, Any], context: PipelineContext) -> None:
        """更新上下文 - 存储业务逻辑"""
        context.set("business_logic", result)
    
    def format_display_result(self, final_result: Dict[str, Any]) -> str:
        """
        格式化结果用于显示
        
        Args:
            final_result: 业务逻辑生成结果
            
        Returns:
            格式化后的字符串
        """
        if not final_result or not isinstance(final_result, dict):
            return "❌ 未生成有效的业务逻辑描述或结果格式不正确。"
        
        display_parts = ["## 业务逻辑分析：\n"]

        # 按顺序显示各个字段
        field_order = ["表范围", "计算逻辑", "条件", "维度", "整体逻辑描述"]

        for field in field_order:
            if field in final_result:
                value = final_result[field]
                display_parts.append(f"### {field}：")
                display_parts.append(f"{value}")
                display_parts.append("")  # 添加空行
        
        # 显示其他字段（如果有）
        other_fields = {k: v for k, v in final_result.items() if k not in field_order}
        if other_fields:
            display_parts.append("### 其他信息：")
            for key, value in other_fields.items():
                if key != "error":  # 不显示错误信息
                    display_parts.append(f"- **{key}**: {value}")

        # 如果有错误信息，在最后显示
        if "error" in final_result:
            display_parts.append("### 注意：")
            display_parts.append(f"生成过程中遇到问题: {final_result['error']}")
        
        return "\n".join(display_parts)

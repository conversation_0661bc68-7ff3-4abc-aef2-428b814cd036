"""
分块管理API

提供分块的管理操作，包括：
1. 分块列表查询（支持分页和筛选）
2. 分块详情查询
3. 分块信息查询（content, title, keywords等）
4. 分块信息更新
5. 分块删除
6. 分块统计信息
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Query, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from loguru import logger

from modules.knowledge.doc.operations.chunk_ops import ChunkOperations
from modules.knowledge.doc.entities import (
    APIResponse, Chunk, ChunkInfo, CreateChunkRequest, UpdateChunkRequest
)


# 创建路由器
router = APIRouter(prefix="/knowledge/doc/chunks", tags=["分块管理"])


# 请求/响应模型
class ChunkResponse(BaseModel):
    """分块响应"""
    chunk_id: str
    doc_id: str
    chapter_layer: Optional[str] = None
    parent_id: Optional[str] = None
    sub_chunk_ids: Optional[List[str]] = None
    is_active: bool
    create_time: Optional[str] = None
    info_count: int = 0


class ChunkInfoResponse(BaseModel):
    """分块信息响应"""
    chunk_info_id: str
    chunk_id: str
    info_type: str
    info_value: str
    is_active: bool
    created_time: Optional[str] = None
    updated_time: Optional[str] = None


class ChunkListResponse(BaseModel):
    """分块列表响应"""
    success: bool = True
    message: str = "查询成功"
    data: List[ChunkResponse]
    total: int
    page: int
    page_size: int


class ChunkDetailResponse(BaseModel):
    """分块详情响应"""
    success: bool = True
    message: str = "查询成功"
    data: ChunkResponse


class ChunkInfoListResponse(BaseModel):
    """分块信息列表响应"""
    success: bool = True
    message: str = "查询成功"
    data: List[ChunkInfoResponse]
    chunk_id: str


class ChunkStatsResponse(BaseModel):
    """分块统计响应"""
    success: bool = True
    message: str = "查询成功"
    data: Dict[str, Any]


class ChunkInfoUpdateRequest(BaseModel):
    """分块信息更新请求"""
    new_info_value: str = Field(..., description="新的信息值")


class StandardResponse(BaseModel):
    """标准响应"""
    success: bool
    message: str
    data: Optional[Any] = None


class ErrorResponse(BaseModel):
    """错误响应"""
    success: bool = False
    message: str
    data: Optional[Any] = None


def get_chunk_operations():
    """获取分块操作实例"""
    return ChunkOperations()


def create_error_response(message: str, status_code: int = 500) -> JSONResponse:
    """创建统一格式的错误响应"""
    return JSONResponse(
        status_code=status_code,
        content=ErrorResponse(message=message).model_dump()
    )


@router.get("/", response_model=ChunkListResponse)
async def list_chunks(
    doc_id: str = Query(..., description="文档ID"),
    user_id: str = Query(..., description="用户ID"),
    page: int = Query(1, ge=1, description="页码（从1开始）"),
    page_size: int = Query(50, ge=1, le=200, description="每页大小（1-200）"),
    ops: ChunkOperations = Depends(get_chunk_operations)
):
    """
    查询分块列表

    支持分页查询指定文档的所有分块。

    **注意：**
    - 分块数据可能较多，建议合理设置page_size
    - 返回的分块按创建时间排序
    """
    try:
        result = await ops.get_chunk_list(
            user_id=user_id,
            doc_id=doc_id,
            page=page,
            page_size=page_size
        )
        
        if result.get("status") == "success":
            chunks = result.get("data", [])
            total = result.get("total", 0)
            
            # 转换为响应格式
            chunk_responses = []
            for chunk in chunks:
                chunk_response = ChunkResponse(
                    chunk_id=chunk["chunk_id"],
                    doc_id=chunk["doc_id"],
                    chapter_layer=chunk.get("chapter_layer"),
                    parent_id=chunk.get("parent_id"),
                    sub_chunk_ids=chunk.get("sub_chunk_ids"),
                    is_active=chunk.get("is_active", True),
                    create_time=chunk.get("create_time"),
                    info_count=chunk.get("info_count", 0)
                )
                chunk_responses.append(chunk_response)
            
            return ChunkListResponse(
                data=chunk_responses,
                total=total,
                page=page,
                page_size=page_size
            )
        else:
            return create_error_response(result.get("message", "查询分块列表失败"), 500)
        
    except Exception as e:
        logger.error(f"查询分块列表失败: doc_id={doc_id}, 错误: {str(e)}")
        return create_error_response(f"查询分块列表失败: {str(e)}", 500)


@router.get("/{chunk_id}", response_model=ChunkDetailResponse)
async def get_chunk_detail(
    chunk_id: str,
    user_id: str = Query(..., description="用户ID"),
    ops: ChunkOperations = Depends(get_chunk_operations)
):
    """
    获取分块详情

    根据分块ID获取基本信息。
    """
    try:
        # 注意：这里使用get_chunk_infos方法获取分块基本信息
        result = await ops.get_chunk_infos(user_id=user_id, chunk_id=chunk_id)
        
        if result.get("status") == "success":
            chunk_data = result.get("data", {})
            
            if not chunk_data:
                return create_error_response("分块不存在", 404)
            
            # 从chunk_infos结果中提取基本分块信息
            chunk_info = chunk_data.get("chunk_info", {})
            
            chunk_response = ChunkResponse(
                chunk_id=chunk_id,
                doc_id=chunk_info.get("doc_id", ""),
                chapter_layer=chunk_info.get("chapter_layer"),
                parent_id=chunk_info.get("parent_id"),
                sub_chunk_ids=chunk_info.get("sub_chunk_ids"),
                is_active=chunk_info.get("is_active", True),
                create_time=chunk_info.get("create_time"),
                info_count=len(chunk_data.get("info_list", []))
            )
            
            return ChunkDetailResponse(data=chunk_response)
        else:
            return create_error_response(result.get("message", "查询分块详情失败"), 500)
        
    except Exception as e:
        logger.error(f"获取分块详情失败: {chunk_id}, 错误: {str(e)}")
        return create_error_response(f"获取分块详情失败: {str(e)}", 500)


@router.get("/{chunk_id}/infos", response_model=ChunkInfoListResponse)
async def get_chunk_infos(
    chunk_id: str,
    user_id: str = Query(..., description="用户ID"),
    ops: ChunkOperations = Depends(get_chunk_operations)
):
    """
    获取分块信息列表

    返回指定分块的所有信息（content, title, keywords, summary等）。

    **信息类型包括：**
    - content：分块内容
    - title：标题
    - keywords：关键词
    - summary：摘要
    - metadata：元数据
    """
    try:
        result = await ops.get_chunk_infos(user_id=user_id, chunk_id=chunk_id)
        
        if result.get("status") == "success":
            chunk_data = result.get("data", {})
            info_list = chunk_data.get("info_list", [])
            
            # 转换为响应格式
            info_responses = []
            for info in info_list:
                info_response = ChunkInfoResponse(
                    chunk_info_id=info["chunk_info_id"],
                    chunk_id=info["chunk_id"],
                    info_type=info["info_type"],
                    info_value=info["info_value"],
                    is_active=info.get("is_active", True),
                    created_time=info.get("created_time"),
                    updated_time=info.get("updated_time")
                )
                info_responses.append(info_response)
            
            return ChunkInfoListResponse(
                data=info_responses,
                chunk_id=chunk_id
            )
        else:
            return create_error_response(result.get("message", "查询分块信息失败"), 500)
        
    except Exception as e:
        logger.error(f"查询分块信息失败: {chunk_id}, 错误: {str(e)}")
        return create_error_response(f"查询分块信息失败: {str(e)}", 500)


@router.put("/info/{chunk_info_id}", response_model=StandardResponse)
async def update_chunk_info(
    chunk_info_id: str,
    request: ChunkInfoUpdateRequest,
    user_id: str = Query(..., description="用户ID"),
    knowledge_id: str = Query(..., description="知识库ID"),
    ops: ChunkOperations = Depends(get_chunk_operations)
):
    """
    更新分块信息

    更新指定分块信息的内容，同时会更新相关的向量数据。

    **注意：**
    - 更新内容后会重新计算向量并更新向量数据库
    - 更新操作会影响搜索结果
    """
    try:
        result = await ops.update_chunk_info(
            user_id=user_id,
            knowledge_id=knowledge_id,
            chunk_info_id=chunk_info_id,
            new_info_value=request.new_info_value
        )
        
        if result.get("status") == "success":
            return StandardResponse(
                success=True,
                message="分块信息更新成功"
            )
        else:
            return create_error_response(result.get("message", "分块信息更新失败"), 400)
        
    except Exception as e:
        logger.error(f"更新分块信息失败: {chunk_info_id}, 错误: {str(e)}")
        return create_error_response(f"更新分块信息失败: {str(e)}", 500)


@router.delete("/{chunk_id}", response_model=StandardResponse)
async def delete_chunk(
    chunk_id: str,
    user_id: str = Query(..., description="用户ID"),
    ops: ChunkOperations = Depends(get_chunk_operations)
):
    """
    删除分块

    删除指定的分块及其所有相关信息，包括向量数据。

    **注意：**
    - 删除操作不可逆
    - 会同时删除分块的所有信息和向量数据
    - 会更新文档的分块计数
    """
    try:
        result = await ops.delete_chunk(user_id=user_id, chunk_id=chunk_id)
        
        if result.get("status") == "success":
            return StandardResponse(
                success=True,
                message="分块删除成功"
            )
        else:
            return create_error_response(result.get("message", "分块删除失败"), 400)
        
    except Exception as e:
        logger.error(f"删除分块失败: {chunk_id}, 错误: {str(e)}")
        return create_error_response(f"删除分块失败: {str(e)}", 500)


@router.get("/stats/{knowledge_id}", response_model=ChunkStatsResponse)
async def get_chunk_stats(
    knowledge_id: str,
    user_id: str = Query(..., description="用户ID"),
    ops: ChunkOperations = Depends(get_chunk_operations)
):
    """
    获取分块统计信息

    返回指定知识库的分块统计数据。

    **统计信息包括：**
    - 总分块数
    - 各信息类型的分块数
    - 各文档的分块数
    - 其他统计指标
    """
    try:
        result = await ops.get_chunk_stats(user_id=user_id, knowledge_id=knowledge_id)
        
        if result.get("status") == "success":
            stats_data = result.get("data", {})
            
            return ChunkStatsResponse(data=stats_data)
        else:
            return create_error_response(result.get("message", "查询分块统计失败"), 500)
        
    except Exception as e:
        logger.error(f"查询分块统计失败: knowledge_id={knowledge_id}, 错误: {str(e)}")
        return create_error_response(f"查询分块统计失败: {str(e)}", 500) 
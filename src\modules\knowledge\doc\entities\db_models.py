"""
文档相关的数据库模型定义

使用SQLAlchemy ORM定义数据库表结构，对应MySQL数据库表
表名添加doc_前缀，遵循数据库命名规范
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import (
    Column, String, Integer, DateTime, Text, Float, UniqueConstraint, Index, Boolean, func
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

# 创建基础模型类
Base = declarative_base()


class DocumentDB(Base):
    """文档表 - doc_documents"""
    __tablename__ = "doc_dev_documents"
    
    # 主键和外键
    doc_id = Column(String(255), primary_key=True, comment="文档ID（UUID格式）")
    knowledge_id = Column(String(255), nullable=False, comment="知识库ID", index=True)
    
    # 基本信息
    doc_name = Column(String(255), nullable=False, comment="文档名称")
    doc_type = Column(String(255), nullable=True, comment="文档类型")
    author = Column(String(255), nullable=True, comment="作者")
    
    # 相似度配置
    vector_similarity_weight = Column(Float, nullable=True, comment="向量相似度权重")
    similarity_threshold = Column(Float, nullable=True, comment="相似度阈值")
    
    # 分块信息
    chunk_nums = Column(Integer, default=0, comment="分块总数")
    percentage = Column(Float, default=0, comment="分块进度")
    
    # 解析信息
    parse_type = Column(String(50), nullable=True, comment="解析方式")
    status = Column(String(50), nullable=True, comment="解析状态")
    parse_end_time = Column(DateTime, nullable=True, comment="解析结束时间")
    parse_message = Column(Text, nullable=True, comment="解析状态描述")
    
    # 文件信息
    location = Column(String(255), nullable=True, comment="文件存储位置")
    doc_format = Column(String(50), nullable=True, comment="文件格式")
    doc_ocr_result_path = Column(String(255), nullable=True, comment="OCR识别结果路径")
    
    # 元数据和任务
    _metadata = Column("metadata", Text, nullable=True, comment="文档源数据")
    task_id = Column(String(255), nullable=True, comment="任务队列ID")
    
    # 时间戳和状态
    created_time = Column(DateTime, nullable=False, default=func.now(), comment="创建时间")
    updated_time = Column(DateTime, nullable=True, default=None, onupdate=func.now(), comment="更新时间")
    is_active = Column(Boolean, nullable=False, default=True, comment="是否生效")
    
    # 关系
    chunks = relationship("ChunkDB", back_populates="document", cascade="all, delete-orphan")
    aliases = relationship("DocumentAliasDB", back_populates="document", cascade="all, delete-orphan")
    categories = relationship("DocumentCategoryDB", back_populates="document", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_doc_knowledge', 'knowledge_id'),
        Index('idx_doc_status', 'status'),
        Index('idx_doc_parse_type', 'parse_type'),
        Index('idx_doc_format', 'doc_format'),
    )


class ChunkDB(Base):
    """分块表 - doc_chunks"""
    __tablename__ = "doc_dev_chunks"
    
    # 主键和外键
    chunk_id = Column(String(255), primary_key=True, comment="分块ID")
    doc_id = Column(String(255),  nullable=False, comment="文档ID", index=True)
    
    # 层级结构
    chapter_layer = Column(String(100), nullable=True, comment="文档章节层级信息")
    parent_id = Column(String(64), nullable=True, comment="父分块ID")
    
    # 时间戳和状态
    created_time = Column(DateTime, nullable=False, default=func.now(), comment="创建时间")
    updated_time = Column(DateTime, nullable=True, default=None, onupdate=func.now(), comment="更新时间")
    is_active = Column(Boolean, nullable=False, default=True, comment="是否生效")
    
    # 关系
    document = relationship("DocumentDB", back_populates="chunks")
    chunk_infos = relationship("ChunkInfoDB", back_populates="chunk", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_chunk_doc_id', 'doc_id'),
        Index('idx_chunk_parent_id', 'parent_id'),
    )


class ChunkInfoDB(Base):
    """分块信息表 - doc_chunks_info"""
    __tablename__ = "doc_dev_chunks_info"
    
    # 主键和外键
    chunk_info_id = Column(String(255), primary_key=True, comment="分块信息ID（UUID格式）")
    chunk_id = Column(String(255), nullable=False, comment="关联的分块ID")
    
    # 信息内容
    info_type = Column(String(255), nullable=False, comment="信息类型")
    info_value = Column(Text, nullable=False, comment="信息值")
    
    # 时间戳和状态
    created_time = Column(DateTime, nullable=False, default=func.now(), comment="创建时间")
    updated_time = Column(DateTime, nullable=True, default=None, onupdate=func.now(), comment="更新时间")
    is_active = Column(Boolean, nullable=False, default=True, comment="是否生效")
    
    # 关系
    chunk = relationship("ChunkDB", back_populates="chunk_infos")
    
    # 索引
    __table_args__ = (
        Index('idx_chunk_info_chunk_id', 'chunk_id'),
        Index('idx_chunk_info_type', 'info_type'),
    )


class DocumentAliasDB(Base):
    """文档别名映射表 - doc_document_alias"""
    __tablename__ = "doc_dev_document_alias"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 外键
    doc_id = Column(String(64),  nullable=False, comment="文件ID")
    
    # 名称信息
    cleaned_name = Column(String(255), nullable=False, comment="别名")
    
    # 关系
    document = relationship("DocumentDB", back_populates="aliases")
    
    # 索引
    __table_args__ = (
        Index('idx_alias_doc_id', 'doc_id'),
        Index('idx_alias_cleaned_name', 'cleaned_name'),
    )


class CategoryDB(Base):
    """类别管理表 - doc_categories"""
    __tablename__ = "doc_dev_categories"
    
    # 主键
    cate_id = Column(String(255), primary_key=True, comment="类别ID")
    
    # 基本信息
    cate_name = Column(String(255), nullable=False, comment="类别名称")
    cate_status = Column(String(255), nullable=True, comment="类别状态")
    cate_layer = Column(Integer, nullable=False, comment="类别层级")
    
    # 层级结构
    parent_id = Column(String(64), nullable=True, comment="父类别ID")
    
    # 关系
    document_categories = relationship("DocumentCategoryDB", back_populates="category", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_category_parent_id', 'parent_id'),
        Index('idx_category_layer', 'cate_layer'),
        Index('idx_category_status', 'cate_status'),
    )


class DocumentCategoryDB(Base):
    """文档类别表 - doc_document_categories"""
    __tablename__ = "doc_dev_document_categories"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 外键
    doc_id = Column(String(255),  nullable=False, comment="文件ID")
    cate_id = Column(String(255),  nullable=False, comment="类别ID", index=True)
    
    # 基本信息
    cate_layer = Column(Integer, nullable=False, comment="类别层级")
    doc_name = Column(String(255), nullable=False, comment="文件名称")
    doc_status = Column(String(255), nullable=True, comment="文件状态")
    
    # 关系
    document = relationship("DocumentDB", back_populates="categories")
    category = relationship("CategoryDB", back_populates="document_categories")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint('doc_id', 'cate_id', name='uk_doc_category'),
        Index('idx_doc_category_cate_id', 'cate_id'),
        Index('idx_doc_category_layer', 'cate_layer'),
    )


class CategoryRelationshipDB(Base):
    """类别关系表 - doc_category_relationship"""
    __tablename__ = "doc_dev_category_relationship"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 关系信息
    source_cate_id = Column(String(255), nullable=False, comment="源类别ID", index=True)
    target_cate_id = Column(String(255), nullable=False, comment="目标类别ID", index=True)
    rel_type = Column(String(255), nullable=True, comment="关系类型")
    
    # 索引
    __table_args__ = (
        UniqueConstraint('source_cate_id', 'target_cate_id', 'rel_type', name='uk_category_relation'),
        Index('idx_category_rel_source', 'source_cate_id'),
        Index('idx_category_rel_target', 'target_cate_id'),
        Index('idx_category_rel_type', 'rel_type'),
    )


class DocumentRelationshipDB(Base):
    """文档关系表 - doc_document_relationship"""
    __tablename__ = "doc_dev_document_relationship"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 关系信息
    source_doc_id = Column(String(255), nullable=False, comment="源文件ID", index=True)
    target_doc_id = Column(String(255), nullable=False, comment="目标文件ID", index=True)
    rel_type = Column(String(255), nullable=True, comment="关系类型")
    
    # 索引
    __table_args__ = (
        UniqueConstraint('source_doc_id', 'target_doc_id', 'rel_type', name='uk_document_relation'),
        Index('idx_doc_rel_source', 'source_doc_id'),
        Index('idx_doc_rel_target', 'target_doc_id'),
        Index('idx_doc_rel_type', 'rel_type'),
    ) 
#!/usr/bin/env python3
"""
DDCrud优化安全性验证测试

确保优化不会影响现有业务逻辑的安全验证
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from service import get_client
from modules.knowledge.dd.crud import DDCrud


class DDCrudSafetyVerificationTest:
    """DDCrud优化安全性验证测试"""
    
    def __init__(self):
        self.rdb_client = None
        self.vdb_client = None
        self.dd_crud = None
        
    async def initialize(self):
        """初始化测试环境"""
        try:
            logger.info("🔧 初始化安全性验证测试环境...")
            
            self.rdb_client = await get_client('database.rdbs.mysql')
            try:
                self.vdb_client = await get_client('database.vdbs.pgvector')
            except:
                logger.warning("向量数据库客户端初始化失败，继续使用关系数据库")
                self.vdb_client = None
            
            self.dd_crud = DDCrud(self.rdb_client, self.vdb_client)
            
            logger.info("✅ 安全性验证测试环境初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试环境初始化失败: {e}")
            return False

    async def test_backward_compatibility(self) -> Dict[str, Any]:
        """测试向后兼容性"""
        logger.info("📋 测试1：向后兼容性验证")
        
        test_results = {
            'test_name': '向后兼容性验证',
            'success': True,
            'details': [],
            'errors': []
        }
        
        try:
            # 测试1.1：原有调用方式（不使用新参数）
            logger.info("  1.1 测试原有调用方式")
            try:
                records = await self.dd_crud.list_post_distributions(limit=5)
                test_results['details'].append(f"原有调用方式: 返回{len(records)}条记录")
            except Exception as e:
                test_results['errors'].append(f"原有调用方式失败: {e}")
                test_results['success'] = False
            
            # 测试1.2：新增参数调用（可选参数）
            logger.info("  1.2 测试新增参数调用")
            try:
                records = await self.dd_crud.list_post_distributions(
                    version='v1.0',
                    limit=5
                )
                test_results['details'].append(f"新增参数调用: 返回{len(records)}条记录")
            except Exception as e:
                test_results['errors'].append(f"新增参数调用失败: {e}")
                test_results['success'] = False
            
            # 测试1.3：混合参数调用
            logger.info("  1.3 测试混合参数调用")
            try:
                records = await self.dd_crud.list_post_distributions(
                    dr07='test_table',
                    version='v1.0',
                    limit=3
                )
                test_results['details'].append(f"混合参数调用: 返回{len(records)}条记录")
            except Exception as e:
                test_results['errors'].append(f"混合参数调用失败: {e}")
                test_results['success'] = False
            
            logger.info("✅ 向后兼容性验证完成")
            
        except Exception as e:
            logger.error(f"❌ 向后兼容性验证失败: {e}")
            test_results['success'] = False
            test_results['errors'].append(str(e))
        
        return test_results

    async def test_field_validation_safety(self) -> Dict[str, Any]:
        """测试字段验证安全性"""
        logger.info("📋 测试2：字段验证安全性")
        
        test_results = {
            'test_name': '字段验证安全性',
            'success': True,
            'details': [],
            'errors': []
        }
        
        try:
            # 测试2.1：支持的字段
            logger.info("  2.1 测试支持的字段")
            supported_fields = ['version', 'dr07', 'status', 'create_time']
            
            for field in supported_fields:
                try:
                    kwargs = {field: 'test_value', 'limit': 1}
                    records = await self.dd_crud.list_post_distributions(**kwargs)
                    test_results['details'].append(f"支持字段 {field}: 正常工作")
                except Exception as e:
                    # 这里可能因为数据类型不匹配而失败，但不应该是字段验证错误
                    if "不支持的查询字段" in str(e):
                        test_results['errors'].append(f"字段 {field} 应该被支持但被拒绝")
                        test_results['success'] = False
                    else:
                        test_results['details'].append(f"支持字段 {field}: 字段验证通过（数据类型错误正常）")
            
            # 测试2.2：不支持的字段
            logger.info("  2.2 测试不支持的字段")
            unsupported_fields = ['invalid_field', 'malicious_field', 'sql_injection']
            
            for field in unsupported_fields:
                try:
                    kwargs = {field: 'test_value', 'limit': 1}
                    records = await self.dd_crud.list_post_distributions(**kwargs)
                    test_results['errors'].append(f"不支持字段 {field} 应该被拒绝但被接受")
                    test_results['success'] = False
                except ValueError as e:
                    if "不支持的查询字段" in str(e):
                        test_results['details'].append(f"不支持字段 {field}: 正确拒绝")
                    else:
                        test_results['errors'].append(f"不支持字段 {field} 错误类型: {e}")
                        test_results['success'] = False
                except Exception as e:
                    test_results['errors'].append(f"不支持字段 {field} 异常: {e}")
                    test_results['success'] = False
            
            logger.info("✅ 字段验证安全性验证完成")
            
        except Exception as e:
            logger.error(f"❌ 字段验证安全性验证失败: {e}")
            test_results['success'] = False
            test_results['errors'].append(str(e))
        
        return test_results

    async def test_data_consistency(self) -> Dict[str, Any]:
        """测试数据一致性"""
        logger.info("📋 测试3：数据一致性验证")
        
        test_results = {
            'test_name': '数据一致性验证',
            'success': True,
            'details': [],
            'errors': []
        }
        
        try:
            # 测试3.1：查询结果格式一致性
            logger.info("  3.1 测试查询结果格式一致性")
            
            # 原有方式查询
            records_old = await self.dd_crud.list_post_distributions(limit=3)
            
            # 新方式查询
            records_new = await self.dd_crud.list_post_distributions(
                version=None,  # 明确传递None，应该与不传递效果一致
                limit=3
            )
            
            # 比较结果格式
            if len(records_old) > 0 and len(records_new) > 0:
                old_keys = set(records_old[0].keys()) if records_old else set()
                new_keys = set(records_new[0].keys()) if records_new else set()
                
                if old_keys == new_keys:
                    test_results['details'].append("查询结果格式完全一致")
                else:
                    test_results['errors'].append(f"查询结果格式不一致: {old_keys} vs {new_keys}")
                    test_results['success'] = False
            else:
                test_results['details'].append("查询结果为空，格式一致性无法验证（正常）")
            
            # 测试3.2：None值处理
            logger.info("  3.2 测试None值处理")
            try:
                records = await self.dd_crud.list_post_distributions(
                    version=None,
                    dr07=None,
                    limit=2
                )
                test_results['details'].append("None值处理: 正常工作")
            except Exception as e:
                test_results['errors'].append(f"None值处理失败: {e}")
                test_results['success'] = False
            
            logger.info("✅ 数据一致性验证完成")
            
        except Exception as e:
            logger.error(f"❌ 数据一致性验证失败: {e}")
            test_results['success'] = False
            test_results['errors'].append(str(e))
        
        return test_results

    async def test_error_handling_safety(self) -> Dict[str, Any]:
        """测试错误处理安全性"""
        logger.info("📋 测试4：错误处理安全性验证")
        
        test_results = {
            'test_name': '错误处理安全性验证',
            'success': True,
            'details': [],
            'errors': []
        }
        
        try:
            # 测试4.1：异常参数处理
            logger.info("  4.1 测试异常参数处理")
            
            # 测试空字符串
            try:
                records = await self.dd_crud.list_post_distributions(
                    version='',
                    limit=1
                )
                test_results['details'].append("空字符串参数: 正常处理")
            except Exception as e:
                test_results['details'].append(f"空字符串参数: 异常处理正常 - {type(e).__name__}")
            
            # 测试4.2：SQL注入防护
            logger.info("  4.2 测试SQL注入防护")
            malicious_inputs = [
                "'; DROP TABLE biz_dd_post_distribution; --",
                "1' OR '1'='1",
                "UNION SELECT * FROM users"
            ]
            
            for malicious_input in malicious_inputs:
                try:
                    records = await self.dd_crud.list_post_distributions(
                        version=malicious_input,
                        limit=1
                    )
                    test_results['details'].append(f"SQL注入防护: 恶意输入被安全处理")
                except Exception as e:
                    test_results['details'].append(f"SQL注入防护: 恶意输入被正确拒绝 - {type(e).__name__}")
            
            logger.info("✅ 错误处理安全性验证完成")
            
        except Exception as e:
            logger.error(f"❌ 错误处理安全性验证失败: {e}")
            test_results['success'] = False
            test_results['errors'].append(str(e))
        
        return test_results


async def main():
    """主测试函数"""
    print("🛡️ DDCrud优化安全性验证测试")
    print("=" * 60)
    print("测试目标：确保优化不会影响现有业务逻辑")
    print("=" * 60)
    
    # 创建测试实例
    test = DDCrudSafetyVerificationTest()
    
    # 初始化测试环境
    if not await test.initialize():
        print("❌ 测试环境初始化失败，退出测试")
        return False
    
    # 执行安全性测试
    test_results = []
    
    try:
        # 测试1：向后兼容性
        result1 = await test.test_backward_compatibility()
        test_results.append(result1)
        
        # 测试2：字段验证安全性
        result2 = await test.test_field_validation_safety()
        test_results.append(result2)
        
        # 测试3：数据一致性
        result3 = await test.test_data_consistency()
        test_results.append(result3)
        
        # 测试4：错误处理安全性
        result4 = await test.test_error_handling_safety()
        test_results.append(result4)
        
    except Exception as e:
        logger.error(f"测试执行过程中发生异常: {e}")
        return False
    
    # 生成安全性验证报告
    print("\n" + "=" * 60)
    print("🛡️ DDCrud优化安全性验证报告")
    print("=" * 60)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results if result['success'])
    
    for result in test_results:
        status = "✅ 安全" if result['success'] else "❌ 风险"
        print(f"\n🔍 {result['test_name']}: {status}")
        
        if result['details']:
            for detail in result['details']:
                print(f"   📝 {detail}")
        
        if result['errors']:
            for error in result['errors']:
                print(f"   ❌ {error}")
    
    # 安全性总结
    print(f"\n📊 安全性验证总结:")
    print(f"   总测试数: {total_tests}")
    print(f"   安全测试: {passed_tests}")
    print(f"   风险测试: {total_tests - passed_tests}")
    print(f"   安全率: {passed_tests / total_tests * 100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有安全性验证通过！")
        print("✅ DDCrud优化100%安全，可以放心部署")
        print("✅ 向后兼容性完全保证")
        print("✅ 字段验证安全机制正常工作")
        print("✅ 数据一致性得到保证")
        print("✅ 错误处理机制安全可靠")
        return True
    else:
        print(f"\n⚠️ 发现{total_tests - passed_tests}个安全风险")
        print("❌ 需要修复安全问题后再部署")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

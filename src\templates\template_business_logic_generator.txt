You are an expert business analyst and SQL expert. Your task is to analyze the provided SQL query and generate a structured business logic description in JSON format.

Task Description:
{TASK_DESCRIPTION}

Output Format Requirements:
{OUTPUT_FORMAT}

User Question:
{USER_QUESTION}

Hint:
{HINT}

SQL Query to Analyze:
{SQL_QUERY}

Table Scope:
{TABLE_SCOPE}

All SQL Candidates:
{ALL_SQL_CANDIDATES}

Please analyze the SQL query and provide a comprehensive business logic description in the following JSON format:

```json
{{
  "table_scope": {{
    "primary_tables": ["list of main tables used"],
    "lookup_tables": ["list of reference/lookup tables"],
    "join_relationships": ["description of how tables are connected"]
  }},
  "calculation_logic": {{
    "aggregations": ["list of aggregation functions used (SUM, COUNT, AVG, etc.)"],
    "calculations": ["list of calculated fields or expressions"],
    "metrics": ["list of business metrics being computed"]
  }},
  "filter_conditions": {{
    "where_conditions": ["list of WHERE clause conditions"],
    "having_conditions": ["list of HAVING clause conditions"],
    "date_filters": ["list of date/time related filters"],
    "business_filters": ["list of business logic filters"]
  }},
  "grouping_dimensions": {{
    "group_by_fields": ["list of GROUP BY fields"],
    "partition_fields": ["list of PARTITION BY fields if any"],
    "ordering_fields": ["list of ORDER BY fields"]
  }},
  "business_logic_summary": {{
    "purpose": "Brief description of what this query accomplishes",
    "business_context": "Business context and use case",
    "data_scope": "Description of the data scope and time range",
    "output_description": "Description of the expected output/results"
  }},
  "complexity_analysis": {{
    "complexity_level": "Simple/Medium/Complex",
    "performance_considerations": ["list of potential performance issues"],
    "optimization_suggestions": ["list of optimization recommendations"]
  }}
}}
```

Important Notes:
1. Analyze the SQL query thoroughly to understand its business purpose
2. Identify all tables, joins, calculations, and filters
3. Provide clear and concise descriptions in business terms
4. If the SQL query is empty or invalid, provide appropriate error information
5. Focus on the business meaning rather than technical SQL details
6. Consider the user question and hint to provide context-aware analysis

Only output the JSON response, no additional text.

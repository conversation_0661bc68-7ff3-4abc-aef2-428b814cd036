"""
错误适配器

将数据库特定的异常转换为统一的RDB异常
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type
import re

from ..core.types import DatabaseType
from ..core.exceptions import (
    RDBError, ConnectionError, ConnectionTimeoutError, AuthenticationError,
    QueryError, SQLSyntaxError, QueryTimeoutError, TableNotFoundError, ColumnNotFoundError,
    IntegrityError, UniqueConstraintError, ForeignKeyConstraintError, 
    NotNullConstraintError, DeadlockError, wrap_database_error
)
from .base_adapter import BaseAdapter


class ErrorAdapter(ABC):
    """错误适配器接口"""
    
    @abstractmethod
    def adapt_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> RDBError:
        """适配异常"""
        pass


class DefaultErrorAdapter(BaseAdapter, ErrorAdapter):
    """默认错误适配器
    
    提供通用的异常转换逻辑
    """
    
    def __init__(self, database_type: DatabaseType):
        super().__init__(database_type)
        self._error_patterns = self._build_error_patterns()
    
    def get_supported_types(self) -> List[Type[Exception]]:
        """获取支持的异常类型"""
        return [Exception]  # 支持所有异常类型
    
    def adapt_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> RDBError:
        """适配异常
        
        Args:
            error: 原始异常
            context: 上下文信息
        
        Returns:
            统一的RDB异常
        """
        context = context or {}
        error_message = str(error)
        error_type = type(error).__name__
        
        # 格式化上下文
        formatted_context = self.format_error_context(context)
        
        # 尝试从异常中提取错误码
        error_code = self._extract_error_code(error)
        
        # 根据错误模式匹配异常类型
        rdb_exception_class = self._match_error_pattern(error_message, error_type)
        
        # 创建统一异常
        return rdb_exception_class(
            message=error_message,
            original_error=error,
            error_code=error_code,
            database_type=self.database_type,
            context=formatted_context
        )
    
    def _extract_error_code(self, error: Exception) -> Optional[str]:
        """提取错误码"""
        # 常见的错误码属性
        for attr in ['errno', 'pgcode', 'code', 'error_code']:
            if hasattr(error, attr):
                value = getattr(error, attr)
                if value is not None:
                    return str(value)
        
        return None
    
    def _build_error_patterns(self) -> Dict[str, Type[RDBError]]:
        """构建错误模式映射
        
        Returns:
            错误模式到异常类的映射
        """
        return {
            # 连接相关错误
            r'connection.*refused|connection.*failed|connection.*timeout': ConnectionTimeoutError,
            r'connection.*denied|access.*denied|authentication.*failed': AuthenticationError,
            r'connection|connect': ConnectionError,
            
            # 查询相关错误
            r'syntax.*error|sql.*syntax|parse.*error': SQLSyntaxError,
            r'timeout|time.*out': QueryTimeoutError,
            r'table.*not.*found|relation.*does.*not.*exist|no.*such.*table': TableNotFoundError,
            r'column.*not.*found|unknown.*column|no.*such.*column': ColumnNotFoundError,
            
            # 完整性约束错误
            r'unique.*constraint|duplicate.*key|unique.*violation': UniqueConstraintError,
            r'foreign.*key.*constraint|foreign.*key.*violation': ForeignKeyConstraintError,
            r'not.*null.*constraint|null.*value|cannot.*be.*null': NotNullConstraintError,
            r'check.*constraint|check.*violation': IntegrityError,
            
            # 事务相关错误
            r'deadlock|lock.*timeout': DeadlockError,
            
            # 默认查询错误
            r'.*': QueryError,
        }
    
    def _match_error_pattern(self, error_message: str, error_type: str) -> Type[RDBError]:
        """匹配错误模式
        
        Args:
            error_message: 错误消息
            error_type: 错误类型名
        
        Returns:
            匹配的RDB异常类
        """
        # 将错误消息转换为小写以便匹配
        message_lower = error_message.lower()
        type_lower = error_type.lower()
        
        # 组合消息和类型进行匹配
        combined_text = f"{message_lower} {type_lower}"
        
        # 按优先级匹配模式
        for pattern, exception_class in self._error_patterns.items():
            if re.search(pattern, combined_text, re.IGNORECASE):
                return exception_class
        
        # 默认返回通用查询错误
        return QueryError


class MySQLErrorAdapter(DefaultErrorAdapter):
    """MySQL错误适配器"""
    
    def __init__(self):
        super().__init__(DatabaseType.MYSQL)
        self._mysql_error_codes = self._build_mysql_error_codes()
    
    def _build_mysql_error_codes(self) -> Dict[int, Type[RDBError]]:
        """构建MySQL错误码映射"""
        return {
            # 连接错误
            1040: ConnectionError,  # Too many connections
            1042: ConnectionError,  # Can't get hostname
            1043: ConnectionError,  # Bad handshake
            1044: AuthenticationError,  # Access denied for user
            1045: AuthenticationError,  # Access denied for user (using password)
            1049: ConnectionError,  # Unknown database
            
            # 语法错误
            1064: SQLSyntaxError,  # SQL syntax error
            1149: SQLSyntaxError,  # SQL syntax error
            
            # 表和列错误
            1051: TableNotFoundError,  # Unknown table
            1054: ColumnNotFoundError,  # Unknown column
            1146: TableNotFoundError,  # Table doesn't exist
            
            # 约束错误
            1062: UniqueConstraintError,  # Duplicate entry
            1216: ForeignKeyConstraintError,  # Cannot add foreign key constraint
            1217: ForeignKeyConstraintError,  # Cannot delete or update a parent row
            1364: NotNullConstraintError,  # Field doesn't have a default value
            1048: NotNullConstraintError,  # Column cannot be null
            
            # 死锁
            1213: DeadlockError,  # Deadlock found
            1205: QueryTimeoutError,  # Lock wait timeout
        }
    
    def adapt_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> RDBError:
        """适配MySQL异常"""
        # 尝试从MySQL异常中提取错误码
        if hasattr(error, 'errno') and error.errno in self._mysql_error_codes:
            exception_class = self._mysql_error_codes[error.errno]
            return exception_class(
                message=str(error),
                original_error=error,
                error_code=str(error.errno),
                database_type=self.database_type,
                context=self.format_error_context(context or {})
            )
        
        # 使用默认逻辑
        return super().adapt_error(error, context)


class PostgreSQLErrorAdapter(DefaultErrorAdapter):
    """PostgreSQL错误适配器"""
    
    def __init__(self):
        super().__init__(DatabaseType.POSTGRESQL)
        self._postgres_error_codes = self._build_postgres_error_codes()
    
    def _build_postgres_error_codes(self) -> Dict[str, Type[RDBError]]:
        """构建PostgreSQL错误码映射"""
        return {
            # 连接错误
            '08000': ConnectionError,  # connection_exception
            '08003': ConnectionError,  # connection_does_not_exist
            '08006': ConnectionError,  # connection_failure
            '28000': AuthenticationError,  # invalid_authorization_specification
            '28P01': AuthenticationError,  # invalid_password
            
            # 语法错误
            '42601': SQLSyntaxError,  # syntax_error
            '42000': SQLSyntaxError,  # syntax_error_or_access_rule_violation
            
            # 表和列错误
            '42P01': TableNotFoundError,  # undefined_table
            '42703': ColumnNotFoundError,  # undefined_column
            
            # 约束错误
            '23505': UniqueConstraintError,  # unique_violation
            '23503': ForeignKeyConstraintError,  # foreign_key_violation
            '23502': NotNullConstraintError,  # not_null_violation
            '23514': IntegrityError,  # check_violation
            
            # 死锁
            '40P01': DeadlockError,  # deadlock_detected
            '57014': QueryTimeoutError,  # query_canceled
        }
    
    def adapt_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> RDBError:
        """适配PostgreSQL异常"""
        # 尝试从PostgreSQL异常中提取错误码
        if hasattr(error, 'pgcode') and error.pgcode in self._postgres_error_codes:
            exception_class = self._postgres_error_codes[error.pgcode]
            return exception_class(
                message=str(error),
                original_error=error,
                error_code=error.pgcode,
                database_type=self.database_type,
                context=self.format_error_context(context or {})
            )
        
        # 使用默认逻辑
        return super().adapt_error(error, context)


class SQLiteErrorAdapter(DefaultErrorAdapter):
    """SQLite错误适配器"""
    
    def __init__(self):
        super().__init__(DatabaseType.SQLITE)
    
    def adapt_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> RDBError:
        """适配SQLite异常"""
        error_message = str(error).lower()
        
        # SQLite的错误主要通过消息内容判断
        if 'syntax error' in error_message:
            exception_class = SQLSyntaxError
        elif 'no such table' in error_message:
            exception_class = TableNotFoundError
        elif 'no such column' in error_message:
            exception_class = ColumnNotFoundError
        elif 'unique constraint' in error_message or 'unique' in error_message:
            exception_class = UniqueConstraintError
        elif 'foreign key constraint' in error_message:
            exception_class = ForeignKeyConstraintError
        elif 'not null constraint' in error_message or 'may not be null' in error_message:
            exception_class = NotNullConstraintError
        elif 'database is locked' in error_message:
            exception_class = QueryTimeoutError
        else:
            exception_class = QueryError
        
        return exception_class(
            message=str(error),
            original_error=error,
            database_type=self.database_type,
            context=self.format_error_context(context or {})
        )


def create_error_adapter(database_type: DatabaseType) -> ErrorAdapter:
    """创建错误适配器工厂函数
    
    Args:
        database_type: 数据库类型
    
    Returns:
        对应的错误适配器
    """
    if database_type == DatabaseType.MYSQL:
        return MySQLErrorAdapter()
    elif database_type == DatabaseType.POSTGRESQL:
        return PostgreSQLErrorAdapter()
    elif database_type == DatabaseType.SQLITE:
        return SQLiteErrorAdapter()
    else:
        return DefaultErrorAdapter(database_type)

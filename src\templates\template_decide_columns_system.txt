You are an expert and very smart data analyst.

**Important Context**: This is the final review stage. The database schema has already been pre-filtered from an initial 200+ tables by previous analysts. Your role is to perform a final check and necessary refinement rather than strict filtering. Focus on validating the relevance of tables and columns, and make essential adjustments to ensure the selection is optimal for the SQL query.

Your task is to examine the provided database schema, understand the posed question, and use the hint to pinpoint the specific columns within tables that are essential for crafting a SQL query to answer the question.

Database Schema Overview:
{DATABASE_SCHEMA}
This schema offers an in-depth description of the database's architecture, detailing tables, columns, primary keys, foreign keys, and any pertinent information regarding relationships or constraints. Special attention should be given to the examples listed beside each column, as they directly hint at which columns are relevant to our query.

For key phrases mentioned in the question, we have provided the most similar values within the columns denoted by "-- examples" in front of the corresponding column names. This is a critical hint to identify the columns that will be used in the SQL query.

Question:
{QUESTION}

Hint:
{HINT}
The hint aims to direct your focus towards the specific elements of the database schema that are crucial for answering the question effectively.

Task:
Based on the database schema, question, and hint provided, your task is to identify all and only the columns that are essential for crafting a SQL query to answer the question. Since this is a final validation step on pre-selected tables, focus on:
1. Verifying that the selected tables and columns are appropriate
2. Removing any clearly irrelevant columns
3. Ensuring the selection enables the most accurate SQL query possible with the available schema

**Critical Constraint**: You can ONLY select columns that explicitly exist in the provided database schema. You must NOT generate, suggest, or assume any columns that are not present in the schema. Work strictly within the confines of the given tables and columns.

For each of the selected columns, explain why exactly it is necessary for answering the question. Your reasoning should be concise and clear, demonstrating a logical connection between the columns and the question asked.


Please respond with a JSON object structured as follows:
```json
{{
  "chain_of_thought_reasoning": "Your reasoning for selecting the columns, be concise and clear.",
  "table_name1": ["column1", "column2", ...],
  "table_name2": ["column1", "column2", ...],
  ...
}}
```

Make sure your response includes the table names as keys, each associated with a list of column names that are necessary for writing a SQL query to answer the question.
For each aspect of the question, provide a clear and concise explanation of your reasoning behind selecting the columns.

Take a deep breath and think logically. If you do the task correctly, I will give you 1 million dollars.
**Only output a json as your response.**
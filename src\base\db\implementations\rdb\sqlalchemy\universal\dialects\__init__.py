

from typing import Dict, Type, Optional
from .base import DatabaseDialect
from .mysql import MySQLDialect
from .postgresql import PostgreSQLDialect
from .sqlite import SQLiteDialect

# Registry of available dialects
DIALECT_REGISTRY: Dict[str, Type[DatabaseDialect]] = {
    'mysql': MySQLDialect,
    'postgresql': PostgreSQLDialect,
    'postgres': PostgreSQLDialect,  # Alias
    'sqlite': SQLiteDialect,
}

# Planned dialects for future implementation
PLANNED_DIALECTS = {
    'oracle': 'OracleDialect',
    'mssql': 'SQLServerDialect',
    'mariadb': 'MariaDBDialect',
}


def get_dialect(dialect_name: str) -> DatabaseDialect:
    """
    Get dialect instance by name
    
    Args:
        dialect_name: Name of the dialect (mysql, postgresql, sqlite, etc.)
    
    Returns:
        DatabaseDialect instance
    
    Raises:
        ValueError: If dialect is not supported
    """
    dialect_name = dialect_name.lower()
    
    if dialect_name not in DIALECT_REGISTRY:
        available = ', '.join(DIALECT_REGISTRY.keys())
        planned = ', '.join(PLANNED_DIALECTS.keys())
        raise ValueError(
            f"Unsupported dialect: {dialect_name}. "
            f"Available: {available}. "
            f"Planned: {planned}"
        )
    
    dialect_class = DIALECT_REGISTRY[dialect_name]
    return dialect_class()


def detect_dialect_from_url(database_url: str) -> DatabaseDialect:
    """
    Detect dialect from database URL
    
    Args:
        database_url: Database connection URL
    
    Returns:
        DatabaseDialect instance
        
    Raises:
        ValueError: If URL format is invalid or dialect cannot be determined
    """
    try:
        # Extract dialect from URL scheme
        if '://' not in database_url:
            raise ValueError(f"Invalid database URL format: {database_url}. Expected format: dialect://...")
        
        scheme = database_url.split('://')[0].lower()
        dialect_name = scheme.split('+')[0]  # Remove driver part
        
        if not dialect_name:
            raise ValueError(f"Cannot extract dialect from URL: {database_url}")
        
        return get_dialect(dialect_name)
    except IndexError as e:
        raise ValueError(f"Invalid database URL format: {database_url}") from e
    except Exception as e:
        if isinstance(e, ValueError):
            raise
        raise ValueError(f"Failed to detect dialect from URL: {database_url}") from e


def list_supported_dialects() -> Dict[str, str]:
    """
    List all supported dialects with descriptions
    
    Returns:
        Dictionary mapping dialect names to descriptions
    """
    descriptions = {}
    
    for name in DIALECT_REGISTRY:
        dialect = get_dialect(name)
        descriptions[name] = dialect.description
    
    return descriptions


def is_dialect_supported(dialect_name: str) -> bool:
    """
    Check if a dialect is supported
    
    Args:
        dialect_name: Name of the dialect
    
    Returns:
        True if supported, False otherwise
    """
    return dialect_name.lower() in DIALECT_REGISTRY


__all__ = [
    'DatabaseDialect',
    'get_dialect',
    'detect_dialect_from_url',
    'list_supported_dialects',
    'is_dialect_supported',
    'DIALECT_REGISTRY',
    'PLANNED_DIALECTS'
]

import time
import hashlib
import json
from typing import Dict, Any, Optional, List, Union, Tuple
from collections import OrderedDict
from dataclasses import dataclass, asdict
import threading
import asyncio
import logging

logger = logging.getLogger(__name__)

from .exceptions import CacheError


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: float
    accessed_at: float
    access_count: int = 0
    ttl: Optional[float] = None
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    def touch(self) -> None:
        """更新访问时间"""
        self.accessed_at = time.time()
        self.access_count += 1


class LRUCache:
    """LRU缓存实现"""
    
    def __init__(self, max_size: int = 1000, default_ttl: Optional[float] = None):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired': 0
        }
    
    def _make_key(self, key: Union[str, Dict[str, Any]]) -> str:
        """生成缓存键"""
        if isinstance(key, str):
            return key
        elif isinstance(key, dict):
            # 对字典进行排序并序列化
            sorted_dict = json.dumps(key, sort_keys=True, ensure_ascii=False)
            return hashlib.md5(sorted_dict.encode()).hexdigest()
        else:
            return str(key)
    
    def get(self, key: Union[str, Dict[str, Any]]) -> Optional[Any]:
        """获取缓存值"""
        cache_key = self._make_key(key)
        
        with self._lock:
            entry = self._cache.get(cache_key)
            
            if entry is None:
                self._stats['misses'] += 1
                return None
            
            if entry.is_expired():
                del self._cache[cache_key]
                self._stats['expired'] += 1
                self._stats['misses'] += 1
                return None
            
            # 更新访问信息并移到末尾（最近使用）
            entry.touch()
            self._cache.move_to_end(cache_key)
            self._stats['hits'] += 1
            
            return entry.value
    
    def put(self, key: Union[str, Dict[str, Any]], value: Any, ttl: Optional[float] = None) -> None:
        """设置缓存值"""
        cache_key = self._make_key(key)
        current_time = time.time()
        
        with self._lock:
            # 如果键已存在，更新值
            if cache_key in self._cache:
                entry = self._cache[cache_key]
                entry.value = value
                entry.created_at = current_time
                entry.accessed_at = current_time
                entry.ttl = ttl or self.default_ttl
                self._cache.move_to_end(cache_key)
                return
            
            # 检查是否需要清理过期条目
            self._cleanup_expired()
            
            # 检查是否需要驱逐最少使用的条目
            if len(self._cache) >= self.max_size:
                self._evict_lru()
            
            # 添加新条目
            entry = CacheEntry(
                key=cache_key,
                value=value,
                created_at=current_time,
                accessed_at=current_time,
                ttl=ttl or self.default_ttl
            )
            self._cache[cache_key] = entry
    
    def delete(self, key: Union[str, Dict[str, Any]]) -> bool:
        """删除缓存条目"""
        cache_key = self._make_key(key)
        
        with self._lock:
            if cache_key in self._cache:
                del self._cache[cache_key]
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._stats = {
                'hits': 0,
                'misses': 0,
                'evictions': 0,
                'expired': 0
            }
    
    def _cleanup_expired(self) -> None:
        """清理过期条目"""
        expired_keys = []
        for key, entry in self._cache.items():
            if entry.is_expired():
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
            self._stats['expired'] += 1
    
    def _evict_lru(self) -> None:
        """驱逐最少使用的条目"""
        if self._cache:
            self._cache.popitem(last=False)  # 移除最旧的条目
            self._stats['evictions'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'hit_rate': hit_rate,
                'evictions': self._stats['evictions'],
                'expired': self._stats['expired']
            }


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, enable_cache: bool = True, cache_size: int = 1000, 
                 default_ttl: float = 3600):
        self.enable_cache = enable_cache
        self.cache_size = cache_size
        self.default_ttl = default_ttl
        
        # 不同类型的缓存
        self.query_cache = LRUCache(cache_size, default_ttl) if enable_cache else None
        self.vector_cache = LRUCache(cache_size // 2, default_ttl * 2) if enable_cache else None
        self.metadata_cache = LRUCache(cache_size // 4, default_ttl) if enable_cache else None
        
        logger.info(f"Cache Manager initialized: enabled={enable_cache}, size={cache_size}, ttl={default_ttl}s")
    
    def is_enabled(self) -> bool:
        """检查缓存是否启用"""
        return self.enable_cache
    
    # ==================== 查询结果缓存 ====================
    
    def get_query_result(self, sql: str, params: List[Any]) -> Optional[List[Dict[str, Any]]]:
        """获取查询结果缓存"""
        if not self.enable_cache or not self.query_cache:
            return None
        
        cache_key = {
            'type': 'query',
            'sql': sql,
            'params': params
        }
        
        result = self.query_cache.get(cache_key)
        if result:
            logger.debug(f"Query cache hit for SQL: {sql[:100]}...")
        return result
    
    def cache_query_result(self, sql: str, params: List[Any], result: List[Dict[str, Any]], 
                          ttl: Optional[float] = None) -> None:
        """缓存查询结果"""
        if not self.enable_cache or not self.query_cache:
            return
        
        cache_key = {
            'type': 'query',
            'sql': sql,
            'params': params
        }
        
        self.query_cache.put(cache_key, result, ttl)
        logger.debug(f"Cached query result for SQL: {sql[:100]}...")
    
    # ==================== 向量缓存 ====================
    
    def get_vector(self, vector_id: str) -> Optional[List[float]]:
        """获取向量缓存"""
        if not self.enable_cache or not self.vector_cache:
            return None
        
        cache_key = f"vector:{vector_id}"
        result = self.vector_cache.get(cache_key)
        if result:
            logger.debug(f"Vector cache hit for ID: {vector_id}")
        return result
    
    def cache_vector(self, vector_id: str, vector: List[float], ttl: Optional[float] = None) -> None:
        """缓存向量"""
        if not self.enable_cache or not self.vector_cache:
            return
        
        cache_key = f"vector:{vector_id}"
        self.vector_cache.put(cache_key, vector, ttl)
        logger.debug(f"Cached vector for ID: {vector_id}")
    
    def get_vectors_batch(self, vector_ids: List[str]) -> Dict[str, List[float]]:
        """批量获取向量缓存"""
        if not self.enable_cache or not self.vector_cache:
            return {}
        
        cached_vectors = {}
        for vector_id in vector_ids:
            vector = self.get_vector(vector_id)
            if vector:
                cached_vectors[vector_id] = vector
        
        return cached_vectors
    
    def cache_vectors_batch(self, vectors: Dict[str, List[float]], ttl: Optional[float] = None) -> None:
        """批量缓存向量"""
        if not self.enable_cache or not self.vector_cache:
            return
        
        for vector_id, vector in vectors.items():
            self.cache_vector(vector_id, vector, ttl)
    
    # ==================== 元数据缓存 ====================
    
    def get_metadata(self, collection_name: str, record_id: str) -> Optional[Dict[str, Any]]:
        """获取元数据缓存"""
        if not self.enable_cache or not self.metadata_cache:
            return None
        
        cache_key = f"metadata:{collection_name}:{record_id}"
        result = self.metadata_cache.get(cache_key)
        if result:
            logger.debug(f"Metadata cache hit for {collection_name}:{record_id}")
        return result
    
    def cache_metadata(self, collection_name: str, record_id: str, metadata: Dict[str, Any], 
                      ttl: Optional[float] = None) -> None:
        """缓存元数据"""
        if not self.enable_cache or not self.metadata_cache:
            return
        
        cache_key = f"metadata:{collection_name}:{record_id}"
        self.metadata_cache.put(cache_key, metadata, ttl)
        logger.debug(f"Cached metadata for {collection_name}:{record_id}")
    
    # ==================== 缓存管理 ====================
    
    def invalidate_collection(self, collection_name: str) -> None:
        """使集合相关的缓存失效"""
        if not self.enable_cache:
            return
        
        # 清理查询缓存中包含该集合的查询
        if self.query_cache:
            keys_to_delete = []
            for key, entry in self.query_cache._cache.items():
                if isinstance(entry.value, list) and collection_name in str(entry.key):
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                self.query_cache.delete(key)
        
        # 清理元数据缓存
        if self.metadata_cache:
            keys_to_delete = []
            for key, entry in self.metadata_cache._cache.items():
                if key.startswith(f"metadata:{collection_name}:"):
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                self.metadata_cache.delete(key)
        
        logger.info(f"Invalidated cache for collection: {collection_name}")
    
    def clear_all(self) -> None:
        """清空所有缓存"""
        if not self.enable_cache:
            return
        
        if self.query_cache:
            self.query_cache.clear()
        if self.vector_cache:
            self.vector_cache.clear()
        if self.metadata_cache:
            self.metadata_cache.clear()
        
        logger.info("All caches cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = {
            'enabled': self.enable_cache,
            'cache_size': self.cache_size,
            'default_ttl': self.default_ttl
        }
        
        if self.enable_cache:
            if self.query_cache:
                stats['query_cache'] = self.query_cache.get_stats()
            if self.vector_cache:
                stats['vector_cache'] = self.vector_cache.get_stats()
            if self.metadata_cache:
                stats['metadata_cache'] = self.metadata_cache.get_stats()
        
        return stats

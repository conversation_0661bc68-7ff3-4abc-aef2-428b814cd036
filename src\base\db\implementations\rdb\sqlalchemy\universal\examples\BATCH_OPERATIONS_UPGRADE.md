# 批量操作统一接口升级说明

## 概述

本次升级对批量删除和批量更新方法进行了重大改进，提供了更统一、灵活且用户友好的API接口，同时保持了向后兼容性。

## 主要改进

### 1. 批量删除方法改进

#### 1.1 方法签名变更

**之前：**
```python
def batch_delete(self, table: str, ids: List[Union[int, str]], id_column: str = "id", batch_size: int = 1000)
async def abatch_delete(self, table: str, ids: List[Union[int, str]], id_column: str = "id", ...)
```

**现在：**
```python
def batch_delete(self, table: str, conditions: List[Union[Dict[str, Any], QueryFilterGroup]], batch_size: int = 1000)
async def abatch_delete(self, table: str, conditions: List[Union[Dict[str, Any], QueryFilterGroup]], ...)
```

#### 1.2 支持的删除格式

1. **简单ID删除**（替代原有ID列表功能）
```python
conditions = [{"id": 1}, {"id": 2}, {"id": 3}]
```

2. **简单条件删除**
```python
conditions = [
    {"status": "inactive"},
    {"age": 18},
    {"department": "HR", "active": False}
]
```

3. **复杂条件删除**（MongoDB风格操作符）
```python
conditions = [
    {"age": {"$lt": 18}},
    {"last_login": {"$lt": "2023-01-01"}},
    {"category": {"$in": ["A", "B", "C"]}},
    {"name": {"$like": "%test%"}}
]
```

4. **QueryFilterGroup格式**
```python
conditions = [QueryFilterGroup(...)]
```

### 2. 批量更新方法改进

#### 2.1 保持向后兼容

**传统格式（继续支持）：**
```python
updates = [
    {"data": {"status": "active"}, "filters": {"id": 1}},
    {"data": {"status": "inactive"}, "filters": {"id": 2}}
]
```

#### 2.2 新增格式支持

**新格式（推荐）：**
```python
updates = [
    {"data": {"status": "inactive"}, "where": {"age": {"$lt": 18}}},
    {"data": {"status": "archived"}, "where": {"last_login": {"$lt": "2023-01-01"}}}
]
```

**复杂格式：**
```python
updates = [
    {"data": {"status": "archived"}, "where": QueryFilterGroup(...)}
]
```

### 3. 支持的操作符

#### 3.1 MongoDB风格操作符

| 操作符 | 含义 | 示例 |
|--------|------|------|
| `$eq` | 等于 | `{"age": {"$eq": 18}}` |
| `$ne` | 不等于 | `{"status": {"$ne": "active"}}` |
| `$gt` | 大于 | `{"age": {"$gt": 18}}` |
| `$gte` | 大于等于 | `{"age": {"$gte": 18}}` |
| `$lt` | 小于 | `{"age": {"$lt": 65}}` |
| `$lte` | 小于等于 | `{"age": {"$lte": 65}}` |
| `$in` | 包含 | `{"category": {"$in": ["A", "B"]}}` |
| `$nin` | 不包含 | `{"status": {"$nin": ["deleted", "banned"]}}` |
| `$like` | 模糊匹配 | `{"name": {"$like": "%张%"}}` |
| `$between` | 范围 | `{"age": {"$between": [18, 65]}}` |
| `$null` | 为空 | `{"deleted_at": {"$null": True}}` |
| `$not_null` | 不为空 | `{"email": {"$not_null": True}}` |

#### 3.2 特殊处理

**范围条件自动转换：**
```python
{"age": {"$gt": 18, "$lt": 65}}  # 自动转换为 BETWEEN
{"age": {"$gte": 18, "$lte": 65}}  # 自动转换为 BETWEEN
```

**逻辑操作符：**
```python
{
    "$or": [
        {"status": "active"},
        {"priority": "high"}
    ]
}
```

### 4. 日志改进

#### 4.1 批量操作日志策略

- **开始日志**：记录操作开始，包含表名、数据量、批次大小等信息
- **批次日志**：使用DEBUG级别记录每个批次的执行情况
- **失败日志**：实时记录失败的批次（ERROR级别）
- **完成日志**：只在全部操作完成后记录最终成功状态（INFO级别）

#### 4.2 日志示例

```
INFO: 开始批量删除操作: 表=users, 条件数量=1000, 批次大小=100
DEBUG: 批次 1/10 删除成功: 影响行数=50
ERROR: 批次 3/10 删除失败: Connection timeout
INFO: 批量删除完成: 总影响行数=450, 执行时间=5.23秒
```

### 5. 复杂度边界定义

#### 5.1 适合批量方法的场景 ✅

- **单表条件查询**：等值、范围、IN、模糊匹配
- **逻辑组合**：AND、OR条件组合
- **常见操作符**：比较、空值检查、模糊匹配
- **中等数据量**：单次操作少于10万条记录

#### 5.2 不适合批量方法的场景 ❌

- **JOIN查询**：跨表操作
- **子查询**：复杂嵌套查询
- **函数调用**：数据库特定函数
- **大批量操作**：超过10万条记录
- **复杂业务逻辑**：需要多步骤处理的操作

**建议：** 对于不适合的场景，直接使用原生SQL执行。

### 6. 向后兼容性保证

#### 6.1 批量更新

- ✅ 原有的 `{"data": {...}, "filters": {...}}` 格式继续支持
- ✅ 简单字典filters自动识别和处理
- ✅ 方法签名保持不变

#### 6.2 批量删除

- ⚠️ 方法签名有变化（移除了`id_column`参数）
- ✅ 通过示例说明如何迁移ID列表删除
- ✅ 功能更强大，支持更多删除场景

#### 6.3 迁移指南

**原有ID列表删除：**
```python
# 之前
client.batch_delete("users", [1, 2, 3], id_column="user_id")

# 现在
client.batch_delete("users", [{"user_id": 1}, {"user_id": 2}, {"user_id": 3}])
```

### 7. 性能优化

#### 7.1 批次处理优化

- **智能批次大小**：删除操作默认1000，更新操作默认100
- **并发控制**：异步方法支持可配置的并发数
- **连接复用**：批次内使用事务，批次间复用连接
- **错误隔离**：单个批次失败不影响其他批次

#### 7.2 内存优化

- **流式处理**：分批处理避免内存溢出
- **参数化查询**：防止SQL注入，提高执行效率
- **连接池管理**：合理使用数据库连接资源

### 8. 错误处理改进

#### 8.1 详细错误信息

```python
OperationResponse(
    success=False,
    affected_rows=450,
    operation_parameters={
        "total_batches": 10,
        "successful_batches": 8,
        "failed_batches": 2,
        "errors": [
            "Batch 3 failed: Connection timeout",
            "Batch 7 failed: Constraint violation"
        ]
    }
)
```

#### 8.2 错误恢复策略

- **部分成功处理**：记录成功和失败的批次数量
- **详细错误日志**：每个失败批次的具体错误信息
- **操作继续**：单个批次失败不中断整个操作

### 9. 使用建议

#### 9.1 选择合适的方法

- **简单场景**：使用同步方法 `batch_delete`、`batch_update`
- **高性能需求**：使用异步方法 `abatch_delete`、`abatch_update`
- **大数据量**：调整 `batch_size` 和 `max_concurrency` 参数

#### 9.2 最佳实践

1. **测试先行**：在生产环境使用前充分测试
2. **监控日志**：关注批次执行情况和错误信息
3. **合理配置**：根据数据库性能调整批次大小和并发数
4. **错误处理**：检查返回结果，处理部分失败的情况

### 10. 示例代码

详细的使用示例请参考：`batch_operations_examples.py`

该文件包含了所有格式的完整使用示例，包括：
- 简单ID删除
- 条件删除
- 复杂条件删除
- 传统格式更新
- 新格式更新
- 复杂条件更新
- 复杂度边界说明

## 总结

本次升级显著提升了批量操作的灵活性和易用性，同时保持了良好的性能和向后兼容性。新的统一接口设计使得用户可以更直观地表达复杂的查询条件，同时明确的复杂度边界指导用户在合适的场景下选择合适的方法。

"""
Schema生成步骤 - 现代化SchemaGenerator API专用实现

专门使用 get_prompt_list_with_mappings() 方法，提供：
- 4种数据加载方法：from_table_names, from_table_ids, from_column_ids, from_candidate_columns
- 统一的映射输出：完整的数据库到表、表到列映射信息
- 基于is_final参数的条件输出格式
- 无向后兼容负担的纯现代化API
"""

from typing import Dict, Any, Optional, List, Union, Tuple
import logging

logger = logging.getLogger(__name__)

from pipeline.core.base_step import ToolStep
from pipeline.core.context import PipelineContext

class SchemaGeneratorStep(ToolStep):
    """
    现代化Schema生成步骤
    专门使用 get_prompt_list_with_mappings() 方法

    输入参数：
    - context.schema_generation_params: 包含schema生成所需的参数
      {
          # 输入方式（四选一，按优先级）
          "table_names": List[str],                    # 优先级1：基于表名
          "table_ids": List[int],                      # 优先级2：基于表ID
          "column_ids": List[int],                     # 优先级3：基于列ID
          "candidate_columns": Dict[str, List[str]],   # 优先级4：基于候选列

          # 通用参数
          "source_type": "source" | "index",           # 必需
          "knowledge_id": Optional[str],               # 可选
          "client": Optional[Any],                     # 可选

          # 功能参数
          "column_limit": int = 5,                     # 列数量限制
          "token_size": int = 7500,                    # 智能分割token限制
          "is_final": bool = False,                    # 控制输出格式
      }

    输出：
    - 当 is_final=False: db_schema = List[Dict] (完整映射结果，包含ID映射)
    - 当 is_final=True: db_schema = "prompt", candidate_tables, candidate_columns, candidate_tables_ids, candidate_column_ids
    """

    def __init__(self, step_name: str = "schema_generator", is_final: Optional[bool] = None):
        super().__init__(
            name=step_name,
            description=f"现代化Schema生成，使用get_prompt_list_with_mappings方法 ({'最终' if is_final else '中间'})"
        )
        # 步骤级的is_final模式设置，用于控制该步骤实例的默认输出格式
        # None: 使用运行时参数决定
        # True: 强制使用最终模式（输出分离的映射）
        # False: 强制使用中间模式（输出完整映射列表）
        self.step_is_final_mode = is_final

    def _determine_input_method(self, params: Dict[str, Any]) -> Tuple[str, str]:
        """
        确定输入方法和参数键（仅现代化API）

        Returns:
            (method_name, param_key): 现代化方法名和对应的参数键
        """
        # 按优先级检查，仅支持现代化API方法名
        if "table_names" in params:
            return "from_table_names", "table_names"
        elif "table_ids" in params:
            return "from_table_ids", "table_ids"
        elif "column_ids" in params:
            return "from_column_ids", "column_ids"
        elif "candidate_columns" in params:
            return "from_candidate_columns", "candidate_columns"

        return "", ""

    def _validate_new_params(self, params: Dict[str, Any]) -> Optional[str]:
        """
        验证新参数的有效性

        Returns:
            错误信息，如果验证通过则返回None
        """
        # 检查参数冲突
        input_params = ["table_names", "table_ids", "column_ids", "candidate_columns"]
        provided_inputs = [key for key in input_params if key in params]

        if len(provided_inputs) > 1:
            return f"不能同时提供多种输入参数: {provided_inputs}"

        # 验证具体参数类型
        if "table_names" in params:
            if not isinstance(params["table_names"], list) or not params["table_names"]:
                return "table_names必须是非空的字符串列表"
            if not all(isinstance(name, str) for name in params["table_names"]):
                return "table_names中的所有元素必须是字符串"

        if "table_ids" in params:
            if not isinstance(params["table_ids"], list) or not params["table_ids"]:
                return "table_ids必须是非空的整数列表"
            if not all(isinstance(id, int) for id in params["table_ids"]):
                return "table_ids中的所有元素必须是整数"

        if "column_ids" in params:
            if not isinstance(params["column_ids"], list) or not params["column_ids"]:
                return "column_ids必须是非空的整数列表"
            if not all(isinstance(id, int) for id in params["column_ids"]):
                return "column_ids中的所有元素必须是整数"

        if "candidate_columns" in params:
            if not isinstance(params["candidate_columns"], dict) or not params["candidate_columns"]:
                return "candidate_columns必须是非空的字典"
            for table_name, columns in params["candidate_columns"].items():
                if not isinstance(table_name, str):
                    return "candidate_columns的键必须是字符串（表名）"
                if not isinstance(columns, list) or not columns:
                    return f"表{table_name}的列列表必须是非空列表"
                if not all(isinstance(col, str) for col in columns):
                    return f"表{table_name}的列名必须都是字符串"

        # 验证column_limit参数
        if "column_limit" in params:
            column_limit = params["column_limit"]
            if column_limit is not None and (not isinstance(column_limit, int) or column_limit < 1):
                return "column_limit必须是大于0的整数或None（表示无限制）"

        # 验证token_size参数
        if "token_size" in params:
            if not isinstance(params["token_size"], int) or params["token_size"] < 100:
                return "token_size必须是大于等于100的整数"

        # 验证is_final参数
        if "is_final" in params:
            if not isinstance(params["is_final"], bool):
                return "is_final必须是布尔值"

        return None

    async def preprocess(self, context: PipelineContext) -> Optional[Dict[str, Any]]:
        """阶段1: 预处理 - 验证参数并确定处理方式"""
        schema_params = context.get("schema_generation_params")

        if not schema_params:
            logger.warning("未找到schema_generation_params，无法生成schema")
            return None

        # 验证source_type参数（必需）
        if "source_type" not in schema_params:
            logger.error("schema_generation_params缺少必需字段: source_type")
            return None

        if schema_params["source_type"] not in ["source", "index"]:
            logger.error(f"无效的source_type参数: {schema_params['source_type']}，必须是'source'或'index'")
            return None

        # 验证新参数
        validation_error = self._validate_new_params(schema_params)
        if validation_error:
            logger.error(f"参数验证失败: {validation_error}")
            return None

        # 确定输入方法
        method_name, param_key = self._determine_input_method(schema_params)
        if not method_name:
            logger.error("未找到有效的输入参数，需要提供以下之一: table_names, table_ids, column_ids, candidate_columns")
            return None

        # 检查对应的参数值
        if param_key not in schema_params:
            logger.error(f"确定使用{method_name}方法，但缺少参数: {param_key}")
            return None

        # 设置默认值
        processed_params = schema_params.copy()
        processed_params.setdefault("column_limit", 5)
        processed_params.setdefault("token_size", 7500)

        # 使用步骤级的is_final模式设置，优先级：运行时参数 > 步骤级设置 > 默认值False
        if self.step_is_final_mode is not None:
            # 如果步骤级设置了is_final模式，使用该设置作为默认值
            processed_params.setdefault("is_final", self.step_is_final_mode)
        else:
            # 否则使用默认值False（中间模式）
            processed_params.setdefault("is_final", False)

        # 添加处理信息
        processed_params["_method_name"] = method_name
        processed_params["_param_key"] = param_key

        logger.info(f"Schema生成参数验证通过: method={method_name}, "
                   f"param_key={param_key}, source_type={schema_params['source_type']}, "
                   f"is_final={processed_params['is_final']}")

        return processed_params
    
    async def process(self, preprocessed_data: Optional[Dict[str, Any]], context: PipelineContext) -> Union[str, Dict[str, Any]]:
        """阶段2: 核心处理 - 使用现代化SchemaGenerator API"""
        if preprocessed_data is None:
            logger.warning("预处理数据为空，返回空结果")
            return ""

        try:
            # 导入SchemaGenerator类
            from utils.db.schema.generate import SchemaGenerator

            # 创建SchemaGenerator实例
            generator = SchemaGenerator(
                source_type=preprocessed_data["source_type"],
                knowledge_id=preprocessed_data.get("knowledge_id"),
                client=preprocessed_data.get("client")
            )

            # 使用现代化API加载数据
            method_name = preprocessed_data["_method_name"]
            param_key = preprocessed_data["_param_key"]
            param_value = preprocessed_data[param_key]

            # 调用现代化API方法
            column_limit = preprocessed_data["column_limit"]
            # 如果column_limit为None，使用一个很大的数字表示无限制
            if column_limit is None:
                column_limit = 999999  # 实际上的无限制

            if method_name == "from_table_names":
                await generator.from_table_names(param_value, column_limit=column_limit)
            elif method_name == "from_table_ids":
                await generator.from_table_ids(param_value, column_limit=column_limit)
            elif method_name == "from_column_ids":
                await generator.from_column_ids(param_value)
            elif method_name == "from_candidate_columns":
                await generator.from_candidate_columns(param_value)
            else:
                logger.error(f"未知的方法名: {method_name}")
                return ""

            # 使用get_prompt_list_with_mappings()方法生成结果
            token_size = preprocessed_data.get("token_size", 7500)
            mapping_prompts = generator.get_prompt_list_with_mappings(token_size=token_size)

            logger.info(f"Schema生成成功，method={method_name}, "
                       f"生成{len(mapping_prompts)}个prompt片段")

            return mapping_prompts

        except Exception as e:
            logger.error(f"Schema生成失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return []

    async def postprocess(self, parsed_result: Union[str, Dict[str, Any]], context: PipelineContext) -> Union[str, Dict[str, Any]]:
        """阶段4: 后处理 - 处理不同类型的结果"""
        return parsed_result

    async def update_context(self, result: List[Dict[str, Any]], context: PipelineContext) -> None:
        """更新上下文 - 基于is_final参数的条件输出"""
        # 获取is_final参数
        schema_params = context.get("schema_generation_params", {})
        is_final = schema_params.get("is_final", False)

        if not result:
            # 空结果处理
            if is_final:
                context.set("db_schema", "")
                context.set("candidate_tables", {})
                context.set("candidate_columns", {})
                context.set("candidate_tables_ids", {})
                context.set("candidate_column_ids", {})
            else:
                context.set("db_schema", [])
            return

        if is_final:
            # 当is_final=True时的输出格式
            context.set("db_schema", result[0]['prompt'])

            # 使用高效的方式提取并合并所有映射信息
            from collections import defaultdict
            from itertools import chain

            # 初始化默认字典以简化合并逻辑
            all_db_to_tables = defaultdict(list)
            all_table_to_columns = defaultdict(list)
            all_db_to_tables_id = defaultdict(list)
            all_table_to_columns_id = defaultdict(list)

            # 使用字典推导和集合操作进行高效合并
            for item in result:
                # 合并db_to_tables映射（名称）
                for db, tables in item.get('db_to_tables', {}).items():
                    all_db_to_tables[db].extend(tables)

                # 合并table_to_columns映射（名称）
                for table, columns in item.get('table_to_columns', {}).items():
                    all_table_to_columns[table].extend(columns)

                # 合并db_to_tables_id映射（ID）
                for db_id, table_ids in item.get('db_to_tables_id', {}).items():
                    all_db_to_tables_id[db_id].extend(table_ids)

                # 合并table_to_columns_id映射（ID）
                for table_id, column_ids in item.get('table_to_columns_id', {}).items():
                    all_table_to_columns_id[table_id].extend(column_ids)

            # 去重并转换为普通字典
            merged_mappings = {
                'candidate_tables': {k: list(dict.fromkeys(v)) for k, v in all_db_to_tables.items()},
                'candidate_columns': {k: list(dict.fromkeys(v)) for k, v in all_table_to_columns.items()},
                'candidate_tables_ids': {k: list(dict.fromkeys(v)) for k, v in all_db_to_tables_id.items()},
                'candidate_column_ids': {k: list(dict.fromkeys(v)) for k, v in all_table_to_columns_id.items()}
            }

            # 批量设置上下文
            for key, value in merged_mappings.items():
                context.set(key, value)

        else:
            # 当is_final=False时的输出格式
            context.set("db_schema", result)

    def format_display_result(self, final_result: Union[str, List[Dict[str, Any]]]) -> str:
        """
        格式化结果用于显示

        Args:
            final_result: 生成的结果（字符串或映射列表）

        Returns:
            格式化后的字符串
        """
        if not final_result:
            return "❌ 未生成有效的数据库 Schema。"

        if isinstance(final_result, str):
            if final_result == "prompt":
                return "## 🎯 Schema生成完成（is_final=True模式）\n\n✅ 映射信息已存储到 candidate_tables 和 candidate_columns"
            else:
                # 其他字符串结果
                display_parts = ["## 📊 生成的数据库 Schema：\n"]
                display_parts.append("```")
                display_parts.append(final_result)
                display_parts.append("```")
                return "\n".join(display_parts)

        elif isinstance(final_result, list):
            # 映射列表结果（is_final=False模式）
            display_parts = ["## 🎯 Schema Prompts（带完整映射信息）：\n"]

            display_parts.append(f"📊 **总览**: 生成了 {len(final_result)} 个prompt片段")

            # 统计映射信息
            all_dbs = set()
            all_tables = set()
            for item in final_result:
                all_dbs.update(item.get('db_to_tables', {}).keys())
                all_tables.update(item.get('table_to_columns', {}).keys())

            display_parts.append(f"🗂️ **涉及数据库**: {list(all_dbs)}")
            display_parts.append(f"📋 **涉及表**: {list(all_tables)}")
            display_parts.append("")

            # 显示第一个prompt作为示例
            if final_result:
                display_parts.append("### 📄 第一个Prompt片段示例：")
                display_parts.append("```")
                display_parts.append(final_result[0].get('prompt', ''))
                display_parts.append("```")

                # 显示第一个片段的映射信息
                first_item = final_result[0]
                db_to_tables = first_item.get('db_to_tables', {})
                table_to_columns = first_item.get('table_to_columns', {})

                if db_to_tables:
                    display_parts.append("\n### 🗂️ 第一个片段的数据库映射：")
                    for db, tables in db_to_tables.items():
                        display_parts.append(f"- **{db}**: {tables}")

                if table_to_columns:
                    display_parts.append("\n### 📊 第一个片段的表列映射：")
                    for table, columns in table_to_columns.items():
                        display_parts.append(f"- **{table}**: {len(columns)}列 - {columns[:3]}{'...' if len(columns) > 3 else ''}")

            return "\n".join(display_parts)

        else:
            # 其他类型，转换为字符串显示
            return f"## 📊 生成结果：\n```\n{str(final_result)}\n```"



# ==================== 使用示例 ====================

"""
现代化API使用示例（仅使用get_prompt_list_with_mappings）：

# 1. 使用表名（推荐）
context.set("schema_generation_params", {
    "source_type": "source",
    "table_names": ["adm_lon_varoius", "test_complete_db"],
    "column_limit": 6,
    "token_size": 7500,
    "is_final": False  # 获取完整映射列表
})

# 2. 使用表ID
context.set("schema_generation_params", {
    "source_type": "source",
    "table_ids": [34, 65, 78],
    "column_limit": 5,
    "token_size": 6000,
    "is_final": True  # 获取分离的映射信息
})

# 3. 使用列ID
context.set("schema_generation_params", {
    "source_type": "source",
    "column_ids": [125, 127, 129, 130],
    "token_size": 7500,
    "is_final": False
})

# 4. 使用候选列字典
context.set("schema_generation_params", {
    "source_type": "source",
    "candidate_columns": {
        "adm_lon_varoius": ["loan_id", "customer_id", "amount"],
        "test_complete_db": ["id", "name", "status"]
    },
    "token_size": 8000,
    "is_final": True
})

# 输出格式说明：
# - is_final=False: db_schema = List[Dict] (完整映射结果，包含ID映射)
# - is_final=True: db_schema = "prompt", candidate_tables, candidate_columns, candidate_tables_ids, candidate_column_ids

# 访问结果：
# 当 is_final=False:
#   context.get("db_schema")  # List[Dict] - 完整映射列表（包含名称和ID映射）
# 当 is_final=True:
#   context.get("db_schema")             # "prompt" 字符串
#   context.get("candidate_tables")      # 数据库名到表名映射
#   context.get("candidate_columns")     # 表名到列名映射
#   context.get("candidate_tables_ids")  # 数据库ID到表ID映射
#   context.get("candidate_column_ids")  # 表ID到列ID映射
"""

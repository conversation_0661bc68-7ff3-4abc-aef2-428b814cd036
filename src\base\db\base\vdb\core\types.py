"""
VDB核心类型定义

定义向量数据库的核心类型，确保类型安全和一致性
参考RDB设计模式，结合向量数据库的特殊需求

设计原则：
1. 类型安全 - 完整的类型注解
2. 向量特化 - 针对向量数据库的特殊类型
3. 兼容性 - 与Milvu<PERSON>、PGVector等主流数据库兼容
4. 扩展性 - 支持未来新的向量数据库类型
"""

from enum import Enum
from typing import Any, Dict, List, Optional, Union, Tuple, Protocol, runtime_checkable
from datetime import datetime
import numpy as np


# ==================== 基础类型定义 ====================

# 向量类型
Vector = List[float]
VectorArray = List[Vector]
NumpyVector = np.ndarray
AnyVector = Union[Vector, NumpyVector]

# 向量数据库值类型
VectorDBValue = Union[
    None, bool, int, float, str, bytes,
    datetime, Vector, Dict[str, Any], List[Any]
]

# 向量数据库记录类型
VectorDBRecord = Dict[str, VectorDBValue]
VectorDBRecords = List[VectorDBRecord]

# 连接和事务类型
VectorDBConnection = Any  # 具体实现由各数据库定义
VectorDBTransaction = Any  # 具体实现由各数据库定义
VectorDBPool = Any  # 具体实现由各数据库定义


# ==================== 枚举类型定义 ====================

class VectorDBType(Enum):
    """向量数据库类型"""
    MILVUS = "milvus"
    PGVECTOR = "pgvector"
    CHROMA = "chroma"
    PINECONE = "pinecone"
    WEAVIATE = "weaviate"
    QDRANT = "qdrant"
    FAISS = "faiss"


class FieldType(Enum):
    """字段类型枚举"""
    # 标量类型
    BOOL = "BOOL"
    INT8 = "INT8"
    INT16 = "INT16"
    INT32 = "INT32"
    INT64 = "INT64"
    FLOAT = "FLOAT"
    DOUBLE = "DOUBLE"
    VARCHAR = "VARCHAR"
    JSON = "JSON"
    
    # 向量类型
    FLOAT_VECTOR = "FLOAT_VECTOR"
    BINARY_VECTOR = "BINARY_VECTOR"
    SPARSE_FLOAT_VECTOR = "SPARSE_FLOAT_VECTOR"


class MetricType(Enum):
    """距离度量类型"""
    L2 = "L2"                    # 欧几里得距离
    IP = "IP"                    # 内积
    COSINE = "COSINE"           # 余弦相似度
    HAMMING = "HAMMING"         # 汉明距离（二进制向量）
    JACCARD = "JACCARD"         # 杰卡德距离（二进制向量）
    DOT_PRODUCT = "DOT_PRODUCT" # 点积
    MANHATTAN = "MANHATTAN"     # 曼哈顿距离


class IndexType(Enum):
    """索引类型"""
    # 精确搜索
    FLAT = "FLAT"
    
    # 近似搜索
    IVF_FLAT = "IVF_FLAT"
    IVF_SQ8 = "IVF_SQ8"
    IVF_PQ = "IVF_PQ"
    HNSW = "HNSW"
    ANNOY = "ANNOY"
    
    # PGVector特有
    IVFFLAT = "IVFFLAT"
    HNSW_PGVECTOR = "HNSW_PGVECTOR"


class SearchType(Enum):
    """搜索类型"""
    VECTOR = "vector"           # 向量搜索
    SCALAR = "scalar"           # 标量搜索
    HYBRID = "hybrid"           # 混合搜索
    FULL_TEXT = "full_text"     # 全文搜索


class ComparisonOperator(Enum):
    """比较操作符"""
    EQ = "="                    # 等于
    NE = "!="                   # 不等于
    GT = ">"                    # 大于
    GTE = ">="                  # 大于等于
    LT = "<"                    # 小于
    LTE = "<="                  # 小于等于
    IN = "IN"                   # 包含
    NOT_IN = "NOT IN"           # 不包含
    LIKE = "LIKE"               # 模糊匹配
    NOT_LIKE = "NOT LIKE"       # 不匹配
    IS_NULL = "IS NULL"         # 为空
    IS_NOT_NULL = "IS NOT NULL" # 不为空
    BETWEEN = "BETWEEN"         # 范围


class LogicalOperator(Enum):
    """逻辑操作符"""
    AND = "AND"
    OR = "OR"
    NOT = "NOT"


class SortOrder(Enum):
    """排序方向"""
    ASC = "ASC"
    DESC = "DESC"


class RankingMethod(Enum):
    """排序方法"""
    RRF = "rrf"                 # Reciprocal Rank Fusion
    WEIGHTED = "weighted"       # 加权平均
    MAX = "max"                 # 最大值
    MIN = "min"                 # 最小值
    AVERAGE = "average"         # 平均值


# ==================== 协议定义 ====================

@runtime_checkable
class VectorDBClient(Protocol):
    """向量数据库客户端协议"""
    
    def connect(self) -> None:
        """连接数据库"""
        ...
    
    async def aconnect(self) -> None:
        """异步连接数据库"""
        ...
    
    def disconnect(self) -> None:
        """断开连接"""
        ...
    
    async def adisconnect(self) -> None:
        """异步断开连接"""
        ...
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        ...


@runtime_checkable
class VectorSearchable(Protocol):
    """向量搜索协议"""
    
    def search(self, collection_name: str, vectors: VectorArray, 
               limit: int = 10, **kwargs) -> VectorDBRecords:
        """向量搜索"""
        ...
    
    async def asearch(self, collection_name: str, vectors: VectorArray,
                     limit: int = 10, **kwargs) -> VectorDBRecords:
        """异步向量搜索"""
        ...


@runtime_checkable
class CollectionManageable(Protocol):
    """集合管理协议"""
    
    def create_collection(self, collection_name: str, schema: Any, **kwargs) -> bool:
        """创建集合"""
        ...
    
    def drop_collection(self, collection_name: str) -> bool:
        """删除集合"""
        ...
    
    def has_collection(self, collection_name: str) -> bool:
        """检查集合是否存在"""
        ...
    
    def list_collections(self) -> List[str]:
        """列出所有集合"""
        ...


@runtime_checkable
class DataManageable(Protocol):
    """数据管理协议"""
    
    def insert(self, collection_name: str, data: VectorDBRecords, **kwargs) -> bool:
        """插入数据"""
        ...
    
    def delete(self, collection_name: str, expr: str, **kwargs) -> bool:
        """删除数据"""
        ...
    
    def query(self, collection_name: str, expr: str, **kwargs) -> VectorDBRecords:
        """查询数据"""
        ...


# ==================== 配置类型 ====================

class ConnectionConfig:
    """连接配置基类"""
    
    def __init__(self, host: str, port: int, **kwargs):
        self.host = host
        self.port = port
        self.extra_config = kwargs
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "host": self.host,
            "port": self.port,
            **self.extra_config
        }


class SearchConfig:
    """搜索配置"""
    
    def __init__(self, metric_type: MetricType = MetricType.COSINE,
                 index_type: Optional[IndexType] = None,
                 search_params: Optional[Dict[str, Any]] = None):
        self.metric_type = metric_type
        self.index_type = index_type
        self.search_params = search_params or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "metric_type": self.metric_type.value,
            "search_params": self.search_params
        }
        if self.index_type:
            result["index_type"] = self.index_type.value
        return result


# ==================== 工具函数 ====================

def normalize_vector(vector: AnyVector) -> Vector:
    """标准化向量格式"""
    if isinstance(vector, np.ndarray):
        return vector.tolist()
    elif isinstance(vector, list):
        return vector
    else:
        raise TypeError(f"Unsupported vector type: {type(vector)}")


def validate_vector_dimension(vector: AnyVector, expected_dim: int) -> bool:
    """验证向量维度"""
    normalized = normalize_vector(vector)
    return len(normalized) == expected_dim


def get_vector_dimension(vector: AnyVector) -> int:
    """获取向量维度"""
    normalized = normalize_vector(vector)
    return len(normalized)


def is_valid_collection_name(name: str) -> bool:
    """验证集合名称是否有效"""
    if not name or not isinstance(name, str):
        return False
    
    # 基本规则：字母、数字、下划线，不能以数字开头
    import re
    pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*$'
    return bool(re.match(pattern, name))


def is_valid_field_name(name: str) -> bool:
    """验证字段名称是否有效"""
    return is_valid_collection_name(name)  # 使用相同的规则

"""
部门数据入库脚本
"""
import asyncio
import logging
from typing import List, Dict, Any
import os
import sys
project_root = os.getcwd()
sys.path.insert(0, project_root)

from service import get_client
# 假设这些是项目中的导入路径，根据实际情况调整
from modules.knowledge.dd.crud import DDCrud


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 原始数据
DEPT_DATA = [
    {
        "deptId": 30235,
        "parentId": 0,
        "deptName": "OPS",
        "deptNameEn": "OPS"
    },
    {
        "deptId": 30236,
        "parentId": 0,
        "deptName": "CGS",
        "deptNameEn": "CGS"
    },
    {
        "deptId": 30237,
        "parentId": 0,
        "deptName": "CMO",
        "deptNameEn": "CMO"
    },
    {
        "deptId": 30238,
        "parentId": 0,
        "deptName": "CMP",
        "deptNameEn": "CMP"
    },
    {
        "deptId": 30239,
        "parentId": 0,
        "deptName": "FIN",
        "deptNameEn": "FIN"
    },
    {
        "deptId": 30240,
        "parentId": 0,
        "deptName": "GSC",
        "deptNameEn": "GSC"
    },
    {
        "deptId": 30241,
        "parentId": 0,
        "deptName": "HRD",
        "deptNameEn": "HRD"
    },
    {
        "deptId": 30242,
        "parentId": 0,
        "deptName": "MSS",
        "deptNameEn": "MSS"
    },
    {
        "deptId": 30243,
        "parentId": 0,
        "deptName": "PUBLIC",
        "deptNameEn": "PUBLIC"
    },
    {
        "deptId": 30244,
        "parentId": 0,
        "deptName": "RISK",
        "deptNameEn": "RISK"
    },
    {
        "deptId": 30245,
        "parentId": 0,
        "deptName": "RRB",
        "deptNameEn": "RRB"
    },
    {
        "deptId": 30246,
        "parentId": 0,
        "deptName": "WPB",
        "deptNameEn": "WPB"
    },
    {
        "deptId": 30247,
        "parentId": 0,
        "deptName": "WSB",
        "deptNameEn": "WSB"
    }
]

def prepare_dept_data(raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    将原始数据转换为符合数据库表结构的格式
    """
    prepared_data = []
    
    for item in raw_data:
        dept_record = {
            'dept_id': str(item['deptId']),  # 转为字符串，符合varchar类型
            'dept_name': item['deptName'],
            'dept_desc': f"{item['deptName']}部门",  # 补充部门描述
            'dept_type': 'normal',  # 默认为普通部门
            'is_active': 1  # 默认生效
            # create_time 和 update_time 由数据库自动设置
        }
        prepared_data.append(dept_record)
    
    return prepared_data

async def insert_departments():
    """
    批量插入部门数据
    """
    try:

        rdb_client = await get_client("database.rdbs.mysql")
        
        # 创建CRUD实例
        crud = DDCrud(rdb_client=rdb_client)
        
        # 准备数据
        dept_data = prepare_dept_data(DEPT_DATA)
        
        logger.info(f"开始插入 {len(dept_data)} 条部门数据...")
        
        # 批量插入
        result = await crud.batch_create_departments(dept_data)
        
        logger.info(f"插入成功！共插入 {len(result)} 条部门数据")
        logger.info(f"插入的部门ID列表: {result}")
        
        return result
        
    except Exception as e:
        logger.error(f"插入部门数据失败: {e}")
        raise

async def main():
    """
    主函数
    """
    try:
        result = await insert_departments()
        print(f"✅ 部门数据入库完成，共插入 {len(result)} 条记录")
    except Exception as e:
        print(f"❌ 部门数据入库失败: {e}")

if __name__ == "__main__":
    # 运行脚本
    asyncio.run(main())

from typing import Dict, Any, Optional


class PGVectorError(Exception):
    """PGVector客户端基础异常"""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None, 
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.original_error = original_error
        self.context = context or {}
    
    def __str__(self) -> str:
        if self.original_error:
            return f"{self.message} (Original: {self.original_error})"
        return self.message


class ConnectionError(PGVectorError):
    """连接相关错误"""
    pass


class ConfigurationError(PGVectorError):
    """配置相关错误"""
    pass


class VectorError(PGVectorError):
    """向量操作相关错误"""
    pass


class SearchError(PGVectorError):
    """搜索相关错误"""
    pass


class CollectionError(PGVectorError):
    """集合操作相关错误"""
    pass


class IndexError(PGVectorError):
    """索引相关错误"""
    pass


class ValidationError(PGVectorError):
    """数据验证错误"""
    pass


class TimeoutError(PGVectorError):
    """超时错误"""
    pass


class PoolError(PGVectorError):
    """连接池相关错误"""
    pass


class CacheError(PGVectorError):
    """缓存相关错误"""
    pass


class UnsupportedOperationError(PGVectorError):
    """不支持的操作错误"""
    pass


def wrap_pgvector_error(original_error: Exception, operation: str = "", 
                       context: Optional[Dict[str, Any]] = None) -> PGVectorError:
    """
    包装数据库特定错误为PGVector异常
    
    Args:
        original_error: 原始错误
        operation: 操作名称
        context: 上下文信息
    
    Returns:
        适当的PGVector异常
    """
    error_type = type(original_error).__name__
    error_msg = str(original_error).lower()
    message = f"PGVector error during {operation}: {original_error}" if operation else str(original_error)
    
    # 映射常见数据库错误到PGVector异常
    if "connection" in error_type.lower() or "connect" in error_msg:
        return ConnectionError(message, original_error, context)
    elif "timeout" in error_type.lower() or "timeout" in error_msg:
        return TimeoutError(message, original_error, context)
    elif "pool" in error_type.lower() or "pool" in error_msg:
        return PoolError(message, original_error, context)
    elif "vector" in error_msg or "dimension" in error_msg:
        return VectorError(message, original_error, context)
    elif "index" in error_msg or "constraint" in error_msg:
        return IndexError(message, original_error, context)
    elif "collection" in error_msg or "table" in error_msg:
        return CollectionError(message, original_error, context)
    elif "search" in error_msg or "query" in error_msg:
        return SearchError(message, original_error, context)
    else:
        return PGVectorError(message, original_error, context)


def handle_pgvector_error(func):
    """
    装饰器：自动包装PGVector错误
    
    Usage:
        @handle_pgvector_error
        async def some_vector_operation(self):
            # vector operations
    """
    async def async_wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            if isinstance(e, PGVectorError):
                raise
            raise wrap_pgvector_error(e, func.__name__)
    
    def sync_wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if isinstance(e, PGVectorError):
                raise
            raise wrap_pgvector_error(e, func.__name__)
    
    import asyncio
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper


class ErrorContext:
    """错误上下文构建器"""
    
    def __init__(self):
        self.context = {}
    
    def add_collection(self, collection_name: str) -> 'ErrorContext':
        self.context['collection'] = collection_name
        return self
    
    def add_vector_id(self, vector_id: str) -> 'ErrorContext':
        self.context['vector_id'] = vector_id
        return self
    
    def add_operation(self, operation: str) -> 'ErrorContext':
        self.context['operation'] = operation
        return self
    
    def add_query(self, query: str) -> 'ErrorContext':
        self.context['query'] = query
        return self
    
    def add_params(self, params: Dict[str, Any]) -> 'ErrorContext':
        self.context['params'] = params
        return self
    
    def add_vector_dimension(self, dimension: int) -> 'ErrorContext':
        self.context['vector_dimension'] = dimension
        return self
    
    def add_search_params(self, top_k: int, threshold: float) -> 'ErrorContext':
        self.context['search_params'] = {'top_k': top_k, 'threshold': threshold}
        return self
    
    def build(self) -> Dict[str, Any]:
        return self.context.copy()

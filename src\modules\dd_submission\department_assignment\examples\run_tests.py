"""
部门职责分配测试运行脚本

使用示例:
python run_tests.py --db-host localhost --db-port 3306 --db-name hsbc_knowledge --db-user root --db-password password
"""

import asyncio
import argparse
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../../'))

from .test_department_assignment import run_department_assignment_tests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('department_assignment_test.log')
    ]
)
logger = logging.getLogger(__name__)


class MockRDBClient:
    """模拟关系型数据库客户端（用于测试）"""
    
    def __init__(self, host, port, database, user, password):
        self.host = host
        self.port = port
        self.database = database
        self.user = user
        self.password = password
        self.connected = False
        
    async def connect(self):
        """连接数据库"""
        logger.info(f"连接数据库: {self.host}:{self.port}/{self.database}")
        # 这里应该实现真实的数据库连接逻辑
        self.connected = True
        
    async def aexecute(self, sql, params=None):
        """执行SQL语句"""
        logger.debug(f"执行SQL: {sql}")
        if params:
            logger.debug(f"参数: {params}")
        # 这里应该实现真实的SQL执行逻辑
        return {"success": True, "affected_rows": 1}
        
    async def afetch_all(self, sql, params=None):
        """查询所有结果"""
        logger.debug(f"查询SQL: {sql}")
        if params:
            logger.debug(f"参数: {params}")
        # 这里应该返回真实的查询结果
        return {"success": True, "data": []}
        
    async def atransaction(self):
        """事务上下文管理器"""
        return MockTransaction()
        
    async def close(self):
        """关闭连接"""
        logger.info("关闭数据库连接")
        self.connected = False


class MockTransaction:
    """模拟事务"""
    
    async def __aenter__(self):
        logger.debug("开始事务")
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            logger.debug("事务回滚")
        else:
            logger.debug("事务提交")


class MockVDBClient:
    """模拟向量数据库客户端（用于测试）"""
    
    def __init__(self):
        self.connected = False
        
    async def connect(self):
        """连接向量数据库"""
        logger.info("连接向量数据库")
        self.connected = True
        
    async def search(self, query, limit=10):
        """向量搜索"""
        logger.debug(f"向量搜索: {query}")
        # 这里应该返回真实的向量搜索结果
        return []
        
    async def close(self):
        """关闭连接"""
        logger.info("关闭向量数据库连接")
        self.connected = False


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行部门职责分配功能测试')
    parser.add_argument('--db-host', default='localhost', help='数据库主机')
    parser.add_argument('--db-port', type=int, default=3306, help='数据库端口')
    parser.add_argument('--db-name', default='hsbc_knowledge', help='数据库名称')
    parser.add_argument('--db-user', default='root', help='数据库用户名')
    parser.add_argument('--db-password', default='', help='数据库密码')
    parser.add_argument('--use-mock', action='store_true', help='使用模拟客户端（用于演示）')
    
    args = parser.parse_args()
    
    logger.info("开始部门职责分配功能测试")
    logger.info(f"数据库配置: {args.db_host}:{args.db_port}/{args.db_name}")
    
    if args.use_mock:
        logger.warning("使用模拟客户端运行测试（仅用于演示）")
        
        # 创建模拟客户端
        rdb_client = MockRDBClient(
            args.db_host, args.db_port, args.db_name, 
            args.db_user, args.db_password
        )
        vdb_client = MockVDBClient()
        
        # 连接
        await rdb_client.connect()
        await vdb_client.connect()
        
        try:
            # 运行测试
            await run_department_assignment_tests(rdb_client, vdb_client)
            logger.info("测试完成")
            
        except Exception as e:
            logger.error(f"测试失败: {e}")
            return 1
            
        finally:
            # 关闭连接
            await rdb_client.close()
            await vdb_client.close()
    
    else:
        logger.error("真实数据库客户端集成尚未实现")
        logger.info("请使用 --use-mock 参数运行模拟测试")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

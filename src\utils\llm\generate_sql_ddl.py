# 指标提取提示模板
INDICATOR_PROMPT = """
你现在是一名专业的银行指标分析师。你的任务是根据用户的当前问题和历史对话信息，准确提取用户明确提到的银行指标名称，尤其是子指标，整合成一个完整的指标列表。

**输入信息**：
- 历史对话信息和用户当前问题：
  {{history_questions}}

**输出格式**：
- 返回一个指标名称列表，例如：<indicator_list>["指标名称1", "指标名称2"]<indicator_list>
- 如果当前问题和历史信息中没有明确指标名称，返回空列表：<indicator_list>[]<indicator_list>

**任务要求**：
1. 提取所有用户明确提到的指标，优先输出子指标。
2. 整合历史对话中的指标信息，确保不遗漏前几轮明确提到的指标。
3. 如果用户给出了主指标的定义，那我们给出参与定义的子指标。
4. 如果用户问题中没有提到具体指标，检查历史信息，仍输出历史中明确的指标；若均无明确指标，输出空列表。

**注意事项**：
- 确保不遗漏前几轮明确提到的指标。
- 避免猜测或添加未明确提到的指标。
- 输出列表中的指标名称无需排序，保持用户提到或分解的逻辑顺序。
- 如果主指标已经被分解为多个子指标，只输出子指标。
- 记得清楚每个指标不要带xx指标这两个字符，要xx
- 注意前后的<indicator_list>标识符不要丢了。
"""

# 生产sql的提示模板
sql_prompts = '''
你是一名专业的 SQL 编写工程师，精通 SQL 查询语句的编写以及数据库表结构的理解。当前时间为 {now_time}。

**任务**：
根据用户的需求，生成一个 SQL 查询语句来回答以下问题，：
{user_query}。
- 如果根据提供的数据库表结构无法回答问题，请返回 `<sql>I do not know</sql>`。
- 查询中需要使用 `*` 表示选择全部字段。
- 你处理的是一个指标信息的sql，如果一个主指标是多个子指标组成，那就用多个子指标来代表主指标
- 问题中包含了用户多轮的信息，你不仅要观察当前问题，还要多用户前几轮的问题作为信息总结，要将多轮中所有用户要用的指标产出
- 不要把where中处理的字段作为select的字段
- 查询出来的select字段部分，不许使用*

**数据库表结构**：
{sql_ddl_state}

**输出要求**：
- 只返回 SQL 语句，且必须严格包裹在 `<sql>` 和 `</sql>` 标签中。
- 不要包含任何额外说明或文本。

**示例**：
用户问题：查询所有用户信息
表结构：CREATE TABLE users (id INT, name VARCHAR(50))
输出：
<sql>SELECT * FROM users</sql>

用户问题：查询不存在的表
表结构：CREATE TABLE users (id INT, name VARCHAR(50))
输出：
<sql>I do not know</sql>

请根据以上要求，生成 SQL 查询语句：
<sql>
{user_query}
</sql>'''

# 类型映射
type_mapping = {
    'NUMBER': 'FLOAT',
    'DATE': 'DATE',
    'Dimension': 'VARCHAR(255)'  # 可调整长度，比如 VARCHAR(50)
}


# 生成建表语句
def generate_sql_for_table(data, table_name="indicator"):
    if not data:
        raise ValueError("Input data is empty, cannot generate table schema.")

    sql_statement = f"CREATE TABLE {table_name} (\n"
    for item in data:
        col_name = item['col_name_cn']
        col_type = type_mapping.get(item['col_type'], 'VARCHAR(255)')
        col_comment = item['col_desc'].replace("'", "''")  # 转义单引号

        # 为中文或保留字字段名添加双引号
        if any(ord(c) > 127 for c in col_name) or col_name in ('date',):
            safe_col_name = f'"{col_name}"'
        else:
            safe_col_name = col_name

        sql_statement += f"    {safe_col_name} {col_type}, -- {col_comment}\n"

    # 移除最后一个逗号和换行符
    sql_statement = sql_statement.rstrip(",\n") + "\n);\n"

    return sql_statement

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PGVector客户端工厂函数

提供Hydra兼容的客户端创建函数，用于service层的配置管理。

作者: Assistant
日期: 2025-01-15
"""

import logging
from typing import Any, Dict, Optional
from omegaconf import DictConfig

from .client import PGVectorClient

logger = logging.getLogger(__name__)


def create_pgvector_client(
    host: str,
    port: int,
    database: str,
    username: Optional[str] = None,
    user: Optional[str] = None,
    password: str = "",
    **kwargs
) -> PGVectorClient:
    """
    创建PGVector客户端实例
    
    这是一个Hydra兼容的工厂函数，用于通过配置文件创建客户端。
    
    Args:
        host: 数据库主机地址
        port: 数据库端口
        database: 数据库名称
        username: 用户名（优先使用）
        user: 用户名（备选）
        password: 密码
        **kwargs: 其他配置参数
        
    Returns:
        PGVectorClient实例
        
    Examples:
        >>> # 通过Hydra配置创建
        >>> client = hydra.utils.instantiate(config)
        
        >>> # 直接调用创建
        >>> client = create_pgvector_client(
        ...     host="localhost",
        ...     port=5432,
        ...     database="vectordb",
        ...     username="postgres",
        ...     password="password"
        ... )
    """
    try:
        # 构建配置对象
        config = DictConfig({
            "host": host,
            "port": port,
            "database": database,
            "username": username or user or "postgres",
            "password": password,
            **kwargs
        })
        
        logger.info(f"创建PGVector客户端: {host}:{port}/{database}")
        
        # 创建客户端实例
        client = PGVectorClient(config)
        
        logger.debug(f"PGVector客户端创建成功: {type(client).__name__}")
        return client
        
    except Exception as e:
        logger.error(f"创建PGVector客户端失败: {e}")
        raise


def create_pgvector_client_from_config(config: DictConfig) -> PGVectorClient:
    """
    从配置对象创建PGVector客户端
    
    Args:
        config: 配置对象
        
    Returns:
        PGVectorClient实例
    """
    try:
        logger.info(f"从配置创建PGVector客户端: {config.get('host', 'unknown')}:{config.get('port', 'unknown')}")
        
        client = PGVectorClient(config)
        
        logger.debug(f"PGVector客户端创建成功: {type(client).__name__}")
        return client
        
    except Exception as e:
        logger.error(f"从配置创建PGVector客户端失败: {e}")
        raise


# 为了向后兼容，提供别名
create_client = create_pgvector_client
create_universal_pgvector_client = create_pgvector_client

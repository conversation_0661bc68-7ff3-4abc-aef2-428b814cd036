"""
基础功能测试

验证修复后的向量化功能和CRUD方法是否正常工作
"""

import asyncio
import logging
import time
from typing import Dict, Any, List

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局测试数据存储
test_data_store = {
    'knowledge_id': None,
    'source_db_id': None,
    'subject_id': None
}


async def setup_test_environment():
    """设置测试环境，创建测试知识库"""
    print("🔧 设置测试环境")
    print("-" * 40)

    try:
        # 获取客户端
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
        from service import get_client
        from modules.knowledge.knowledge.crud import KnowledgeCrud

        rdb_client = await get_client("database.rdbs.mysql")
        knowledge_crud = KnowledgeCrud(rdb_client)

        # 创建测试知识库
        timestamp = int(time.time())
        test_kb_data = {
            'knowledge_name': f'基础功能测试知识库_{timestamp}',
            'knowledge_type': 'MetaData',
            'knowledge_desc': '基础功能测试知识库',
            'models': {
                'embedding': 'moka-m3e-base'
            }
        }

        knowledge_id = await knowledge_crud.create_knowledge_base(test_kb_data)
        if not knowledge_id:
            raise Exception("创建测试知识库失败")

        test_data_store['knowledge_id'] = knowledge_id
        print(f"   ✅ 创建测试知识库: {knowledge_id}")

        return rdb_client, knowledge_id

    except Exception as e:
        logger.error(f"设置测试环境失败: {e}")
        raise


async def cleanup_test_environment():
    """清理测试环境"""
    print("\n🧹 清理测试环境")
    print("-" * 40)

    try:
        if test_data_store['knowledge_id']:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            from modules.knowledge.knowledge.crud import KnowledgeCrud

            rdb_client = await get_client("database.rdbs.mysql")
            knowledge_crud = KnowledgeCrud(rdb_client)

            # 删除测试知识库（会级联删除所有相关数据）
            await knowledge_crud.delete_knowledge_base(test_data_store['knowledge_id'])
            print(f"   ✅ 删除测试知识库: {test_data_store['knowledge_id']}")

    except Exception as e:
        logger.error(f"清理测试环境失败: {e}")


async def test_vectorization_functionality(rdb_client, knowledge_id: str):
    """测试向量化功能"""
    print("\n1️⃣ 测试向量化功能:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client

            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
            print(f"   ✅ 向量化客户端初始化成功")
        except Exception as e:
            print(f"   ⚠️  向量化客户端初始化失败: {e}")

        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)

        # 创建源数据库测试向量化
        timestamp = int(time.time())
        test_db_data = {
            'knowledge_id': knowledge_id,
            'db_name': f'test_vector_db_{timestamp}',
            'db_name_cn': f'测试向量化数据库_{timestamp}',
            'data_layer': 'ods',
            'db_desc': '这是一个用于测试向量化功能的数据库描述',
            'is_active': True
        }

        db_id, vector_results = await meta_crud.create_source_database(test_db_data)
        test_data_store['source_db_id'] = db_id
        
        print(f"   ✅ 创建源数据库: {db_id}")
        print(f"   📊 向量化结果: {len(vector_results)} 个向量")

        if vector_results:
            for result in vector_results:
                print(f"      - {result.get('field_code', 'unknown')}: {result.get('status', 'unknown')}")

        # 如果有向量化客户端但没有向量结果，说明向量化功能有问题
        # 如果没有向量化客户端，则认为向量化功能正常（可选功能）
        if vdb_client and embedding_client:
            return len(vector_results) > 0
        else:
            print(f"   ℹ️  向量化客户端未完全配置，跳过向量化功能测试")
            return True

    except Exception as e:
        print(f"   ❌ 向量化功能测试失败: {e}")
        return False


async def test_subject_crud_methods(rdb_client, knowledge_id: str):
    """测试数据主题CRUD方法"""
    print("\n2️⃣ 测试数据主题CRUD方法:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_subjects import MetadataCrudSubjects
        
        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client

            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的
        
        subjects_crud = MetadataCrudSubjects(rdb_client, vdb_client, embedding_client)

        # 测试 create_subject 方法（别名）
        timestamp = int(time.time())
        subject_data = {
            'knowledge_id': knowledge_id,
            'subject_code': f'TEST_SUBJECT_{timestamp}',
            'subject_name': f'测试数据主题_{timestamp}',
            'subject_desc': '测试数据主题描述',
            'is_active': True
        }

        subject_id, vector_results = await subjects_crud.create_subject(subject_data)
        test_data_store['subject_id'] = subject_id
        
        print(f"   ✅ create_subject 方法: {subject_id}")
        print(f"   📊 向量化结果: {len(vector_results)} 个向量")

        # 测试 get_subject 方法（别名）
        subject_info = await subjects_crud.get_subject(subject_id)
        if subject_info and subject_info.get('subject_name'):
            print(f"   ✅ get_subject 方法: {subject_info['subject_name']}")
        else:
            raise Exception("get_subject 方法失败")

        # 测试 list_subjects 方法（别名）
        subject_list = await subjects_crud.list_subjects(knowledge_id=knowledge_id)
        if subject_list and len(subject_list) > 0:
            print(f"   ✅ list_subjects 方法: {len(subject_list)} 个主题")
        else:
            raise Exception("list_subjects 方法失败")

        return True

    except Exception as e:
        print(f"   ❌ 数据主题CRUD方法测试失败: {e}")
        return False


async def test_cleanup_functionality(rdb_client, knowledge_id: str):
    """测试清理功能"""
    print("\n3️⃣ 测试清理功能:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta
        from modules.knowledge.metadata.crud_modules.crud_subjects import MetadataCrudSubjects
        
        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client

            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的
        
        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)
        subjects_crud = MetadataCrudSubjects(rdb_client, vdb_client, embedding_client)

        # 验证数据存在
        if test_data_store['source_db_id']:
            db_exists = await meta_crud.get_source_database(test_data_store['source_db_id'])
            if db_exists:
                print(f"   ✅ 源数据库存在: {test_data_store['source_db_id']}")
            else:
                print(f"   ⚠️  源数据库不存在: {test_data_store['source_db_id']}")

        if test_data_store['subject_id']:
            subject_exists = await subjects_crud.get_subject(test_data_store['subject_id'])
            if subject_exists:
                print(f"   ✅ 数据主题存在: {test_data_store['subject_id']}")
            else:
                print(f"   ⚠️  数据主题不存在: {test_data_store['subject_id']}")

        return True

    except Exception as e:
        print(f"   ❌ 清理功能测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 基础功能测试")
    print("=" * 80)

    try:
        # 1. 设置测试环境
        rdb_client, knowledge_id = await setup_test_environment()

        # 2. 执行所有测试
        test_results = []
        
        # 向量化功能测试
        result1 = await test_vectorization_functionality(rdb_client, knowledge_id)
        test_results.append(("向量化功能", result1))

        # 数据主题CRUD方法测试
        result2 = await test_subject_crud_methods(rdb_client, knowledge_id)
        test_results.append(("数据主题CRUD方法", result2))

        # 清理功能测试
        result3 = await test_cleanup_functionality(rdb_client, knowledge_id)
        test_results.append(("清理功能", result3))

        # 3. 输出测试结果
        print("\n" + "=" * 80)
        print("📊 基础功能测试结果汇总")
        print("=" * 80)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False

        if all_passed:
            print("\n🎉 所有基础功能测试通过！")
        else:
            print("\n⚠️  部分基础功能测试失败")

        return all_passed

    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        print(f"\n❌ 测试执行失败: {e}")
        return False

    finally:
        # 4. 清理测试环境
        await cleanup_test_environment()


if __name__ == "__main__":
    asyncio.run(main())

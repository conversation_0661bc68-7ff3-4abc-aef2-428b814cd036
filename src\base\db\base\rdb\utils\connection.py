"""
连接管理器

提供数据库连接的创建、管理和监控功能

设计特点：
1. 抽象接口 - 支持不同数据库的连接管理
2. 连接池 - 高效的连接复用和管理
3. 健康检查 - 自动检测和清理无效连接
4. 统计监控 - 详细的连接使用统计
5. 异步支持 - 完全异步的连接操作
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
import asyncio
import time
import logging
from contextlib import asynccontextmanager, contextmanager

from ..core.types import DatabaseType, ConnectionPoolStatus
from ..core.models import ConnectionConfig
from ..core.exceptions import ConnectionError, ConnectionTimeoutError, ConnectionPoolError
from ..core.interfaces import ConnectionPoolManager

logger = logging.getLogger(__name__)


class ConnectionManager(ABC):
    """连接管理器接口"""
    
    @abstractmethod
    async def create_connection(self, config: ConnectionConfig) -> Any:
        """创建单个连接"""
        pass
    
    @abstractmethod
    async def close_connection(self, connection: Any) -> None:
        """关闭单个连接"""
        pass
    
    @abstractmethod
    async def test_connection(self, connection: Any) -> bool:
        """测试连接是否有效"""
        pass
    
    @abstractmethod
    def get_connection_info(self, connection: Any) -> Dict[str, Any]:
        """获取连接信息"""
        pass


class DefaultConnectionManager(ConnectionManager):
    """默认连接管理器
    
    提供通用的连接管理功能
    """
    
    def __init__(self, database_type: DatabaseType):
        self.database_type = database_type
        self._connection_count = 0
        self._active_connections: Dict[int, Any] = {}
        self._connection_stats: Dict[str, Any] = {
            'total_created': 0,
            'total_closed': 0,
            'current_active': 0,
            'failed_attempts': 0,
        }
    
    async def create_connection(self, config: ConnectionConfig) -> Any:
        """创建单个连接"""
        start_time = time.time()
        
        try:
            # 根据数据库类型创建连接
            connection = await self._create_database_connection(config)
            
            # 记录连接
            connection_id = self._generate_connection_id()
            self._active_connections[connection_id] = {
                'connection': connection,
                'created_at': time.time(),
                'last_used': time.time(),
                'config': config,
            }
            
            # 更新统计
            self._connection_stats['total_created'] += 1
            self._connection_stats['current_active'] = len(self._active_connections)
            
            logger.debug(f"Created connection {connection_id} in {time.time() - start_time:.3f}s")
            
            return connection
            
        except Exception as e:
            self._connection_stats['failed_attempts'] += 1
            logger.error(f"Failed to create connection: {e}")
            raise ConnectionError(
                f"Failed to create {self.database_type} connection: {e}",
                original_error=e,
                database_type=self.database_type
            )
    
    async def close_connection(self, connection: Any) -> None:
        """关闭单个连接"""
        try:
            # 查找连接ID
            connection_id = None
            for cid, conn_info in self._active_connections.items():
                if conn_info['connection'] is connection:
                    connection_id = cid
                    break
            
            # 关闭连接
            await self._close_database_connection(connection)
            
            # 移除记录
            if connection_id:
                del self._active_connections[connection_id]
                self._connection_stats['total_closed'] += 1
                self._connection_stats['current_active'] = len(self._active_connections)
                logger.debug(f"Closed connection {connection_id}")
            
        except Exception as e:
            logger.error(f"Error closing connection: {e}")
            raise ConnectionError(
                f"Failed to close {self.database_type} connection: {e}",
                original_error=e,
                database_type=self.database_type
            )
    
    async def test_connection(self, connection: Any) -> bool:
        """测试连接是否有效"""
        try:
            # 执行简单的测试查询
            test_query = self._get_test_query()
            result = await self._execute_test_query(connection, test_query)
            return result is not None
            
        except Exception as e:
            logger.debug(f"Connection test failed: {e}")
            return False
    
    def get_connection_info(self, connection: Any) -> Dict[str, Any]:
        """获取连接信息"""
        # 查找连接信息
        for connection_id, conn_info in self._active_connections.items():
            if conn_info['connection'] is connection:
                return {
                    'connection_id': connection_id,
                    'created_at': conn_info['created_at'],
                    'last_used': conn_info['last_used'],
                    'age': time.time() - conn_info['created_at'],
                    'database_type': self.database_type,
                }
        
        return {'database_type': self.database_type}
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return {
            **self._connection_stats,
            'database_type': self.database_type,
            'active_connection_ids': list(self._active_connections.keys()),
        }
    
    async def cleanup_stale_connections(self, max_age: float = 3600) -> int:
        """清理过期连接
        
        Args:
            max_age: 最大连接年龄（秒）
        
        Returns:
            清理的连接数量
        """
        current_time = time.time()
        stale_connections = []
        
        for connection_id, conn_info in self._active_connections.items():
            age = current_time - conn_info['created_at']
            if age > max_age:
                stale_connections.append((connection_id, conn_info['connection']))
        
        cleaned_count = 0
        for connection_id, connection in stale_connections:
            try:
                await self.close_connection(connection)
                cleaned_count += 1
                logger.debug(f"Cleaned stale connection {connection_id}")
            except Exception as e:
                logger.error(f"Error cleaning stale connection {connection_id}: {e}")
        
        return cleaned_count
    
    def _generate_connection_id(self) -> int:
        """生成连接ID"""
        self._connection_count += 1
        return self._connection_count
    
    async def _create_database_connection(self, config: ConnectionConfig) -> Any:
        """创建数据库特定的连接（子类实现）"""
        raise NotImplementedError("Subclasses must implement _create_database_connection")
    
    async def _close_database_connection(self, connection: Any) -> None:
        """关闭数据库特定的连接（子类实现）"""
        raise NotImplementedError("Subclasses must implement _close_database_connection")
    
    async def _execute_test_query(self, connection: Any, query: str) -> Any:
        """执行测试查询（子类实现）"""
        raise NotImplementedError("Subclasses must implement _execute_test_query")
    
    def _get_test_query(self) -> str:
        """获取测试查询SQL"""
        if self.database_type == DatabaseType.MYSQL:
            return "SELECT 1"
        elif self.database_type == DatabaseType.POSTGRESQL:
            return "SELECT 1"
        elif self.database_type == DatabaseType.SQLITE:
            return "SELECT 1"
        else:
            return "SELECT 1"


class ConnectionPool:
    """连接池实现"""
    
    def __init__(
        self,
        connection_manager: ConnectionManager,
        config: ConnectionConfig,
        min_size: int = 1,
        max_size: int = 10,
        max_queries: int = 50000,
        max_inactive_connection_lifetime: float = 300.0
    ):
        self.connection_manager = connection_manager
        self.config = config
        self.min_size = min_size
        self.max_size = max_size
        self.max_queries = max_queries
        self.max_inactive_connection_lifetime = max_inactive_connection_lifetime
        
        self._pool: List[Dict[str, Any]] = []
        self._pool_lock = asyncio.Lock()
        self._created_connections = 0
        self._closed = False
        
        self._stats = {
            'total_acquired': 0,
            'total_released': 0,
            'current_size': 0,
            'peak_size': 0,
        }
    
    async def acquire(self) -> Any:
        """获取连接"""
        if self._closed:
            raise ConnectionPoolError("Connection pool is closed")
        
        async with self._pool_lock:
            # 尝试从池中获取可用连接
            connection_info = await self._get_available_connection()
            
            if connection_info is None:
                # 创建新连接
                if self._created_connections >= self.max_size:
                    raise ConnectionPoolError(
                        f"Connection pool exhausted (max_size={self.max_size})"
                    )
                
                connection = await self.connection_manager.create_connection(self.config)
                connection_info = {
                    'connection': connection,
                    'created_at': time.time(),
                    'last_used': time.time(),
                    'query_count': 0,
                    'in_use': True,
                }
                
                self._pool.append(connection_info)
                self._created_connections += 1
                self._stats['current_size'] = len(self._pool)
                self._stats['peak_size'] = max(self._stats['peak_size'], self._stats['current_size'])
            
            # 标记为使用中
            connection_info['in_use'] = True
            connection_info['last_used'] = time.time()
            
            self._stats['total_acquired'] += 1
            
            return connection_info['connection']
    
    async def release(self, connection: Any) -> None:
        """释放连接"""
        async with self._pool_lock:
            # 查找连接信息
            for connection_info in self._pool:
                if connection_info['connection'] is connection:
                    connection_info['in_use'] = False
                    connection_info['last_used'] = time.time()
                    connection_info['query_count'] += 1
                    
                    self._stats['total_released'] += 1
                    
                    # 检查是否需要关闭连接
                    if connection_info['query_count'] >= self.max_queries:
                        await self._remove_connection(connection_info)
                    
                    break
    
    async def close(self) -> None:
        """关闭连接池"""
        async with self._pool_lock:
            self._closed = True
            
            # 关闭所有连接
            for connection_info in self._pool:
                try:
                    await self.connection_manager.close_connection(connection_info['connection'])
                except Exception as e:
                    logger.error(f"Error closing pooled connection: {e}")
            
            self._pool.clear()
            self._created_connections = 0
            self._stats['current_size'] = 0
    
    def get_status(self) -> Dict[str, Any]:
        """获取连接池状态"""
        active_connections = sum(1 for info in self._pool if info['in_use'])
        idle_connections = len(self._pool) - active_connections
        
        if self._closed:
            status = ConnectionPoolStatus.CLOSED
        elif active_connections >= self.max_size * 0.9:
            status = ConnectionPoolStatus.DEGRADED
        elif len(self._pool) == 0:
            status = ConnectionPoolStatus.UNHEALTHY
        else:
            status = ConnectionPoolStatus.HEALTHY
        
        return {
            'status': status,
            'total_connections': len(self._pool),
            'active_connections': active_connections,
            'idle_connections': idle_connections,
            'min_size': self.min_size,
            'max_size': self.max_size,
            'closed': self._closed,
            'stats': self._stats.copy(),
        }
    
    async def _get_available_connection(self) -> Optional[Dict[str, Any]]:
        """获取可用连接"""
        current_time = time.time()
        
        for connection_info in self._pool:
            if not connection_info['in_use']:
                # 检查连接是否过期
                age = current_time - connection_info['last_used']
                if age > self.max_inactive_connection_lifetime:
                    await self._remove_connection(connection_info)
                    continue
                
                # 测试连接是否有效
                if await self.connection_manager.test_connection(connection_info['connection']):
                    return connection_info
                else:
                    await self._remove_connection(connection_info)
        
        return None
    
    async def _remove_connection(self, connection_info: Dict[str, Any]) -> None:
        """移除连接"""
        try:
            await self.connection_manager.close_connection(connection_info['connection'])
        except Exception as e:
            logger.error(f"Error closing connection during removal: {e}")
        finally:
            if connection_info in self._pool:
                self._pool.remove(connection_info)
                self._created_connections -= 1
                self._stats['current_size'] = len(self._pool)

"""
排序服务 - 统一的排序调用接口

本模块提供：
1. 类似ServiceClient的统一排序接口
2. 支持多种排序算法的动态切换
3. 配置驱动的排序器选择
4. 异步和同步调用支持
"""

from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field
from loguru import logger

from .algorithms import (
    RRFRanker, WeightedRanker, HybridWeightedRanker,
    RRFConfig, WeightedConfig, HybridWeightedConfig,
    RankResult, create_ranker
)
from .model_ranker import ModelRanker, ModelRankConfig, create_model_ranker


@dataclass
class RankServiceConfig:
    """排序服务配置"""
    default_algorithm: str = "weighted"  # 默认排序算法
    algorithm_configs: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # 各算法的配置
    model_config: Optional[Dict[str, Any]] = None  # 模型排序配置
    fallback_algorithm: str = "weighted"  # fallback算法


class RankService:
    """统一的排序服务，类似ServiceClient的设计模式"""
    
    def __init__(self, config: Optional[RankServiceConfig] = None):
        """
        初始化排序服务
        
        Args:
            config: 排序服务配置，如果为None则使用默认配置
        """
        self.config = config or RankServiceConfig()
        self._rankers = {}  # 缓存排序器实例
        self._model_ranker = None
        
        # 初始化默认排序器
        self._initialize_default_rankers()
        
        logger.info(f"排序服务初始化完成，默认算法: {self.config.default_algorithm}")
    
    def _initialize_default_rankers(self):
        """初始化默认的排序器"""
        # 默认配置
        default_configs = {
            "rrf": {"k": 60},
            "weighted": {"weights": {"embedding": 1.0}},
            "hybrid_weighted": {
                "weight_combinations": [{"embedding": 1.0}],
                "intermediate_top_k": 10
            }
        }
        
        # 合并用户配置
        for algorithm, default_config in default_configs.items():
            user_config = self.config.algorithm_configs.get(algorithm, {})
            merged_config = {**default_config, **user_config}
            
            try:
                ranker = create_ranker(algorithm, merged_config)
                self._rankers[algorithm] = ranker
                logger.debug(f"初始化排序器: {algorithm}")
            except Exception as e:
                logger.warning(f"初始化排序器失败: {algorithm}, error={str(e)}")
        
        # 初始化模型排序器（如果配置了）
        if self.config.model_config:
            try:
                self._model_ranker = create_model_ranker(**self.config.model_config)
                logger.debug("初始化模型排序器")
            except Exception as e:
                logger.warning(f"初始化模型排序器失败: {str(e)}")
    
    def rank(
        self,
        query: str,
        docs: List[Dict[str, Any]],
        algorithm: Optional[str] = None,
        config_override: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> RankResult:
        """
        执行排序
        
        Args:
            query: 查询文本
            docs: 文档列表
            algorithm: 排序算法名称，如果为None则使用默认算法
            config_override: 临时配置覆盖
            **kwargs: 额外参数（如data_dict等）
            
        Returns:
            RankResult: 排序结果
        """
        try:
            # 确定使用的算法
            algo = algorithm or self.config.default_algorithm
            
            logger.debug(f"开始排序: algorithm={algo}, query='{query}', docs_count={len(docs)}")
            
            # 处理模型排序
            if algo == "model":
                return self._rank_with_model(query, docs, config_override, **kwargs)
            
            # 获取或创建排序器
            ranker = self._get_ranker(algo, config_override)
            
            # 执行排序
            result = ranker.rank(query, docs, **kwargs)
            
            logger.info(f"排序完成: algorithm={algo}, input_count={len(docs)}, output_count={len(result.docs)}")
            return result
            
        except Exception as e:
            logger.error(f"排序失败: algorithm={algorithm}, error={str(e)}")
            return self._fallback_rank(query, docs, **kwargs)
    
    async def arank(
        self,
        query: str,
        docs: List[Dict[str, Any]],
        algorithm: Optional[str] = None,
        config_override: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> RankResult:
        """
        异步执行排序
        
        Args:
            query: 查询文本
            docs: 文档列表
            algorithm: 排序算法名称
            config_override: 临时配置覆盖
            **kwargs: 额外参数
            
        Returns:
            RankResult: 排序结果
        """
        try:
            algo = algorithm or self.config.default_algorithm
            
            logger.debug(f"开始异步排序: algorithm={algo}, query='{query}', docs_count={len(docs)}")
            
            # 处理模型排序
            if algo == "model":
                return await self._arank_with_model(query, docs, config_override, **kwargs)
            
            # 对于传统算法，直接调用同步方法
            return self.rank(query, docs, algorithm, config_override, **kwargs)
            
        except Exception as e:
            logger.error(f"异步排序失败: algorithm={algorithm}, error={str(e)}")
            return self._fallback_rank(query, docs, **kwargs)
    
    def _get_ranker(self, algorithm: str, config_override: Optional[Dict[str, Any]] = None):
        """获取或创建排序器"""
        # 如果有配置覆盖，创建新的排序器
        if config_override:
            base_config = self.config.algorithm_configs.get(algorithm, {})
            merged_config = {**base_config, **config_override}
            return create_ranker(algorithm, merged_config)
        
        # 使用缓存的排序器
        if algorithm in self._rankers:
            return self._rankers[algorithm]
        
        # 创建新的排序器并缓存
        try:
            ranker = create_ranker(algorithm, self.config.algorithm_configs.get(algorithm, {}))
            self._rankers[algorithm] = ranker
            return ranker
        except Exception as e:
            logger.error(f"创建排序器失败: {algorithm}, error={str(e)}")
            raise
    
    def _rank_with_model(
        self,
        query: str,
        docs: List[Dict[str, Any]],
        config_override: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> RankResult:
        """使用模型进行排序"""
        if not self._model_ranker:
            logger.warning("模型排序器未配置，使用fallback排序")
            return self._fallback_rank(query, docs, **kwargs)
        
        # 如果有配置覆盖，创建临时模型排序器
        if config_override:
            base_config = self.config.model_config or {}
            merged_config = {**base_config, **config_override}
            temp_ranker = create_model_ranker(**merged_config)
            return temp_ranker.rank(query, docs, **kwargs)
        
        return self._model_ranker.rank(query, docs, **kwargs)
    
    async def _arank_with_model(
        self,
        query: str,
        docs: List[Dict[str, Any]],
        config_override: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> RankResult:
        """异步使用模型进行排序"""
        if not self._model_ranker:
            logger.warning("模型排序器未配置，使用fallback排序")
            return self._fallback_rank(query, docs, **kwargs)
        
        # 如果有配置覆盖，创建临时模型排序器
        if config_override:
            base_config = self.config.model_config or {}
            merged_config = {**base_config, **config_override}
            temp_ranker = create_model_ranker(**merged_config)
            return await temp_ranker.arank(query, docs, **kwargs)
        
        return await self._model_ranker.arank(query, docs, **kwargs)
    
    def _fallback_rank(self, query: str, docs: List[Dict[str, Any]], **kwargs) -> RankResult:
        """fallback排序逻辑"""
        try:
            fallback_algo = self.config.fallback_algorithm
            if fallback_algo in self._rankers:
                logger.info(f"使用fallback排序: {fallback_algo}")
                return self._rankers[fallback_algo].rank(query, docs, **kwargs)
        except Exception as e:
            logger.error(f"Fallback排序也失败: {str(e)}")
        
        # 最终fallback：简单按distance排序
        logger.warning("使用最简单的fallback排序")
        if docs and 'distance' in docs[0]:
            sorted_docs = sorted(docs, key=lambda x: x.get('distance', float('inf')))
        else:
            sorted_docs = docs
        
        scores = [1.0 / (i + 1) for i in range(len(sorted_docs))]
        
        return RankResult(
            query=query,
            docs=sorted_docs,
            scores=scores,
            metadata={"algorithm": "simple_fallback", "fallback": True}
        )
    
    def add_ranker(self, algorithm: str, ranker):
        """添加自定义排序器"""
        self._rankers[algorithm] = ranker
        logger.info(f"添加自定义排序器: {algorithm}")
    
    def set_model_ranker(self, model_ranker: ModelRanker):
        """设置模型排序器"""
        self._model_ranker = model_ranker
        logger.info("设置模型排序器")
    
    def get_available_algorithms(self) -> List[str]:
        """获取可用的排序算法列表"""
        algorithms = list(self._rankers.keys())
        if self._model_ranker:
            algorithms.append("model")
        return algorithms
    
    def get_algorithm_info(self, algorithm: str) -> Dict[str, Any]:
        """获取算法信息"""
        if algorithm == "model":
            if self._model_ranker:
                return {
                    "type": "model",
                    "config": self._model_ranker.config.__dict__,
                    "available": True
                }
            else:
                return {"type": "model", "available": False}
        
        if algorithm in self._rankers:
            ranker = self._rankers[algorithm]
            return {
                "type": "traditional",
                "algorithm": algorithm,
                "config": ranker.config.__dict__ if hasattr(ranker, 'config') else {},
                "available": True
            }
        
        return {"available": False}


# 全局排序服务实例
_default_rank_service = None


def get_rank_service(config: Optional[RankServiceConfig] = None) -> RankService:
    """
    获取排序服务实例（单例模式）
    
    Args:
        config: 配置，仅在首次调用时有效
        
    Returns:
        RankService: 排序服务实例
    """
    global _default_rank_service
    
    if _default_rank_service is None:
        _default_rank_service = RankService(config)
    
    return _default_rank_service


def reset_rank_service():
    """重置排序服务（主要用于测试）"""
    global _default_rank_service
    _default_rank_service = None


# 便捷函数
def rank_documents(
    query: str,
    docs: List[Dict[str, Any]],
    algorithm: str = "weighted",
    config: Optional[Dict[str, Any]] = None,
    **kwargs
) -> RankResult:
    """
    便捷的文档排序函数
    
    Args:
        query: 查询文本
        docs: 文档列表
        algorithm: 排序算法
        config: 算法配置
        **kwargs: 额外参数
        
    Returns:
        RankResult: 排序结果
    """
    service = get_rank_service()
    return service.rank(query, docs, algorithm, config, **kwargs)


async def arank_documents(
    query: str,
    docs: List[Dict[str, Any]],
    algorithm: str = "weighted",
    config: Optional[Dict[str, Any]] = None,
    **kwargs
) -> RankResult:
    """
    便捷的异步文档排序函数
    
    Args:
        query: 查询文本
        docs: 文档列表
        algorithm: 排序算法
        config: 算法配置
        **kwargs: 额外参数
        
    Returns:
        RankResult: 排序结果
    """
    service = get_rank_service()
    return await service.arank(query, docs, algorithm, config, **kwargs) 
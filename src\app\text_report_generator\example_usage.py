"""
文本报告生成器使用示例

展示如何使用文本报告生成器的各种功能
"""

import asyncio
import sys
import os
from typing import List

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from app.text_report_generator.models import (
    IndicatorInfo, ChapterInfo, ReportGenerationRequest,
    HistoricalReportQuery, ReferenceDocumentUpload,ReportConfirmationRequest
)
from app.text_report_generator.services import TextReportGenerator
from service import get_client

indicators = [
    IndicatorInfo(id="1", id_name="农林牧短期", id_desc="短期贷款", id_value="15000"),
    IndicatorInfo(id="2", id_name="农林牧长期", id_desc="长期贷款", id_value="11000"),
    IndicatorInfo(id="3", id_name="总贷款金额", id_desc="总贷款金额", id_value="20000"),
    IndicatorInfo(id="4", id_name="短期贷款占比", id_desc="短期贷款占比", id_value="45%"),
    IndicatorInfo(id="5", id_name="长期贷款占比", id_desc="长期贷款占比", id_value="55%"),
    IndicatorInfo(id="6", id_name="农业贷款占比", id_desc="农业贷款占比", id_value="30%"),
    IndicatorInfo(id="7", id_name="工业贷款占比", id_desc="工业贷款占比", id_value="40%"),
    IndicatorInfo(id="8", id_name="服务业贷款占比", id_desc="服务业贷款占比", id_value="30%"),
    IndicatorInfo(id="9", id_name="上期农林牧短期", id_desc="短期贷款", id_value="15000"),
    IndicatorInfo(id="10", id_name="上期农林牧长期", id_desc="长期贷款", id_value="11000"),
    IndicatorInfo(id="11", id_name="上期总贷款金额", id_desc="总贷款金额", id_value="20000"),
]


async def demo_historical_query(generator):
    """演示历史报告查询"""
    print("=== 演示：历史报告查询 ===")

    # 模拟查询参数
    query = HistoricalReportQuery(
        report_name="贷款总览",
        knowledge_id="text_report_generator"
    )

    try:
        doc_ids = await generator.get_historical_report_by_name(query)

        print(f"查询到 {len(doc_ids)} 个文档:")
        for i, doc_id in enumerate(doc_ids, 1):
            print(f"  {i}. {doc_id}")

    except Exception as e:
        print(f"历史报告查询失败: {e}")


async def demo_semantic_search(generator):
    """演示语义搜索"""
    print("=== 演示：语义搜索 ===")
    # 模拟指标信息

    try:
        chapters = await generator.get_reference_chapters(
            method="semantic",
            params={
                "series_name": "月度贷款报告",
                "report_name": "2025年3月贷款总览"
            },
            indicators=indicators
        )
        print("语义搜索成功")
        print(f"返回章节数: {len(chapters)}")
    except NotImplementedError as e:
        print(f"语义搜索未实现: {e}")
    except Exception as e:
        print(f"语义搜索失败: {e}")


async def demo_document_upload(generator):
    """演示文档上传处理"""
    print("=== 演示：文档上传处理 ===")

    # 模拟上传的Markdown文档
    markdown_content = """# 2025年2月贷款总览\n\n## 贷款总览\n本月，农林牧相关的长期贷款为10000元，农林牧相关的短期贷款为5000元，上期总贷款金额为11000元，总贷款金额较上期上涨。\n\n## 贷款结构分析\n各类贷款占比情况如下：短期贷款占比45%，长期贷款占比55%。其中农业贷款占比30%，工业贷款占比40%，服务业贷款占比30%。\n\n## 风险提示\n当前贷款结构需要关注短期贷款比例偏高的问题，建议适当调整贷款期限结构。\n"""

    upload = ReferenceDocumentUpload(
        content=markdown_content,
        file_name="2025年2月贷款总览.md"
    )

    try:
        chapters = await generator.get_reference_chapters(
            method="upload",
            params=upload.model_dump(),
            indicators=indicators
        )

        print(f"处理完成，得到 {len(chapters)} 个章节:")
        for i, chapter in enumerate(chapters, 1):
            print(f"  {i}. {getattr(chapter, 'chapter_name', '')}")
            print(f"     摘要: {getattr(chapter, 'chapter_summary', '')}")
            print(f"     匹配指标: {getattr(chapter, 'chapter_referenced_index_id', [])}")
            print()

    except Exception as e:
        print(f"文档处理失败: {e}")


async def demo_report_generation(generator):
    """演示报告生成"""
    print("=== 演示：报告生成 ===")

    # 模拟章节信息
    chapters = [
        ChapterInfo(
            chapter_name="贷款总览",
            chapter_summary="描述了贷款情况",
            chapter_content="本月，农林牧相关的长期贷款为10000元，农林牧相关的短期贷款为5000元，上期总贷款金额为11000元，总贷款金额较上期上涨",
            chapter_referenced_index_id=["1", "2", "3"],
            chapter_index=1
        ),
        ChapterInfo(
            chapter_name="贷款结构分析",
            chapter_summary="描述了贷款结构情况",
            chapter_content="各类贷款占比情况如下：短期贷款占比40%，长期贷款占比52%。其中农业贷款占比20%，工业贷款占比90%，服务业贷款占比30%。",
            chapter_referenced_index_id=["4", "5", "6", "7", "8"],
            chapter_index=2
        ),
        ChapterInfo(
            chapter_name="风险提示",
            chapter_summary="描述了风险提示",
            chapter_content="当前贷款结构需要关注短期贷款比例偏高的问题，建议适当调整贷款期限结构。",
            chapter_referenced_index_id=[],
            chapter_index=3
        )
    ]

    try:
        generated_chapters = await generator.generate_report_content(
            chapters=chapters,
            indicators=indicators
        )

        print("报告生成完成:")
        for chapter in generated_chapters:
            print(f"章节: {getattr(chapter, 'chapter_name', '')}")
            print(f"新生成内容: {getattr(chapter, 'chapter_content', '')}")
            print()
        return generated_chapters
    except Exception as e:
        print(f"报告生成失败: {e}")

async def demo_archive_document(generator, chapters: List[ChapterInfo]):
    """演示文档归档"""
    print("=== 演示：文档归档 ===")
    archive = ReportConfirmationRequest(
        series_name="月度贷款报告",
        report_name="2025年6月贷款总览",
        chapters=chapters
    )
    success = await generator.generation_service.archive_document(archive)
    print(f"文档归档: {'成功' if success else '失败'}")


async def demo_complete_workflow(generator):
    """演示完整工作流程"""
    print("=== 演示：完整工作流程 ===")

    # 1. 创建报告生成请求
    request = ReportGenerationRequest(
        series_name="月度贷款报告",
        report_name="2025年6月贷款总览",
        indicators=indicators
    )

    print("1. 收到报告生成请求")
    print(f"   套系: {request.series_name}")
    print(f"   报告: {request.report_name}")
    print(f"   指标数: {len(request.indicators)}")

    # 2. 获取参考章节（使用上传方式演示）
    markdown_content = """# 2025年2月贷款总览\n\n## 贷款总览\n本月，农林牧相关的长期贷款为10000元，农林牧相关的短期贷款为5000元，上期总贷款金额为11000元，总贷款金额较上期上涨。\n"""

    upload = ReferenceDocumentUpload(
        content=markdown_content,
        file_name="参考报告.md"
    )

    try:
        chapters = await generator.get_reference_chapters(
            method="upload",
            params=upload.model_dump(),
            indicators=request.indicators
        )

        print(f"2. 获取参考章节: {len(chapters)} 个")

        # 3. 生成报告内容
        generated_chapters = await generator.generate_report_content(
            chapters=chapters,
            indicators=request.indicators
        )

        print("3. 生成报告内容完成")

        # 4. 可选：模拟用户确认并归档
        from datetime import datetime
        from app.text_report_generator.models import ReportConfirmationRequest
        archive = ReportConfirmationRequest(
            series_name=request.series_name,
            report_name=request.report_name,
            chapters=generated_chapters
        )
        success = await generator.generation_service.archive_document(archive)
        print(f"4. 文档归档: {'成功' if success else '失败'}")

    except Exception as e:
        print(f"完整工作流程失败: {e}")


async def main():
    from service import get_client
    # 初始化所有依赖
    rdb_client = await get_client("database.rdbs.mysql")
    vdb_client = await get_client("database.vdbs.pgvector")
    embedding_client = await get_client("model.embeddings.moka-m3e-base")
    llm_client = await get_client("model.llms.opentrek")

    generator = TextReportGenerator(
        rdb_client=rdb_client,
        vdb_client=vdb_client,
        embedding_client=embedding_client,
        llm_client=llm_client,
        knowledge_id="text_report_generator"
    )

    print("🚀 文本报告生成器使用示例")
    print("=" * 50)

    try:
        await demo_historical_query(generator)
        await demo_semantic_search(generator)
        await demo_document_upload(generator)
        chapters =await demo_report_generation(generator)
        await demo_archive_document(generator, chapters)
        # await demo_complete_workflow(generator)
        print("🎉 所有演示完成！")
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())

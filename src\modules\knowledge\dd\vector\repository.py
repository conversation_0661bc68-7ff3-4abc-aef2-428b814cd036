"""
Vector模块数据访问层
"""

from typing import Any, Dict, List, Optional
import logging
import hashlib

from .entities import Embedding
from ..shared.constants import DDTableNames, DDConstants
from ..shared.exceptions import DDDatabaseError, DDVectorizationError, DDValidationError
from ..shared.utils import DDUtils

logger = logging.getLogger(__name__)


class VectorRepository:
    """Vector模块数据仓储 - 直接使用PGVector客户端"""

    def __init__(self, vdb_client: Any, embedding_client: Any = None):
        """
        初始化向量仓储

        Args:
            vdb_client: PGVector客户端实例
            embedding_client: 向量化模型客户端
        """
        self.vdb_client = vdb_client  # 直接使用PGVector客户端
        self.embedding_client = embedding_client
        self.collection_name = DDTableNames.VECTOR_EMBEDDINGS
    
    async def create_embedding_for_field(
        self,
        content: str,
        field_code: str,
        knowledge_id: str,
        data_layer: str,
        submission_pk: int
    ) -> Dict[str, Any]:
        """
        为字段创建向量嵌入

        Args:
            submission_pk: dd_submission_data表的主键id，直接存储为向量表的data_row_id
        """
        # 清理文本
        clean_content = DDUtils.clean_text_for_vectorization(content)
        if not clean_content:
            raise DDValidationError(f"字段 {field_code} 的内容为空或无效")

        # 生成向量 - 必须是真实向量
        embedding_vector = await self._generate_embedding(clean_content)
        if not embedding_vector:
            raise DDVectorizationError(f"向量生成失败: {field_code}")

        # 构建向量数据
        embedding_data = {
            "embedding": embedding_vector,
            "knowledge_id": knowledge_id,
            "data_row_id": submission_pk,  # 直接使用dd_submission_data的主键
            "field_id": field_code,        # 直接使用字段名(dr09/dr17)
            "data_layer": data_layer,
            "is_latest": True
        }

        # 插入向量数据库
        vector_id = await self.insert_vector(embedding_data)

        return {
            "vector_id": vector_id,
            "data_row_id": submission_pk,  # 返回dd_submission_data的主键id
            "field_id": field_code,  # 使用与数据库表一致的字段名
            "embedding_dimension": len(embedding_vector)
            # 移除content字段：该字段在dd_embeddings表中不存在
            # content内容已经向量化存储在embedding字段中，不需要重复存储原文
        }
    
    async def search_similar_vectors(
        self,
        query_text: str,
        field_code: str,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.5
    ) -> List[Dict[str, Any]]:
        """搜索相似向量"""
        try:
            # 清理查询文本
            clean_query = DDUtils.clean_text_for_vectorization(query_text)
            if not clean_query:
                return []

            # 生成查询向量
            query_vector = await self._generate_embedding(clean_query)

            # 构建过滤条件
            filters = {
                "field_id": field_code,  # 直接使用字段名(dr09/dr17)
                # "is_latest": True
            }
            if knowledge_id:
                filters["knowledge_id"] = knowledge_id
            if data_layer:
                filters["data_layer"] = data_layer

            # 执行向量搜索
            results = await self._search_vectors(
                query_vector=query_vector,
                filters=filters,
                limit=limit
            )

            # 过滤低分结果
            filtered_results = []
            for result in results:
                score = result.get("score", 0.0)
                if score >= min_score:
                    # 不添加field_code字段，因为结果中已经包含field_id字段
                    # field_id字段来自数据库，与field_code是同一个值
                    filtered_results.append(result)

            return filtered_results

        except Exception as e:
            logger.error(f"搜索相似向量失败: {e}")
            raise DDDatabaseError(f"搜索相似向量失败: {e}")

    async def batch_search_similar_vectors(
        self,
        search_requests: List[Dict[str, Any]]
    ) -> List[List[Dict[str, Any]]]:
        """
        批量搜索相似向量

        Args:
            search_requests: 搜索请求列表，每个请求包含：
                - query_text: 查询文本
                - field_code: 字段代码
                - knowledge_id: 知识库ID（可选）
                - data_layer: 数据层（可选）
                - limit: 结果数量限制（可选，默认10）
                - min_score: 最小分数（可选，默认0.5）

        Returns:
            List[List[Dict[str, Any]]]: 每个搜索请求对应的结果列表
        """
        if not search_requests:
            return []

        try:
            # 尝试使用批量向量搜索优化
            return await self._batch_search_optimized(search_requests)

        except Exception as e:
            logger.warning(f"批量向量搜索失败，降级到逐个搜索: {e}")
            # 降级到逐个搜索
            return await self._batch_search_fallback(search_requests)

    async def _batch_search_optimized(
        self,
        search_requests: List[Dict[str, Any]]
    ) -> List[List[Dict[str, Any]]]:
        """优化的批量向量搜索实现"""
        import asyncio

        # 第一步：批量生成查询向量
        query_texts = []
        for request in search_requests:
            clean_query = DDUtils.clean_text_for_vectorization(request.get("query_text", ""))
            query_texts.append(clean_query if clean_query else "")

        # 批量生成嵌入向量
        query_vectors = await self._batch_generate_embeddings(query_texts)

        # 第二步：构建批量搜索任务
        search_tasks = []
        for i, request in enumerate(search_requests):
            if not query_texts[i]:  # 跳过空查询
                search_tasks.append(asyncio.create_task(self._empty_search_result()))
                continue

            # 构建过滤条件
            filters = {
                "field_id": request.get("field_code", "dr09")
            }
            if request.get("knowledge_id"):
                filters["knowledge_id"] = request["knowledge_id"]
            if request.get("data_layer"):
                filters["data_layer"] = request["data_layer"]

            # 创建搜索任务
            task = asyncio.create_task(self._search_vectors_with_filter(
                query_vector=query_vectors[i],
                filters=filters,
                limit=request.get("limit", 10),
                min_score=request.get("min_score", 0.5)
            ))
            search_tasks.append(task)

        # 第三步：并行执行所有搜索
        search_results = await asyncio.gather(*search_tasks, return_exceptions=True)

        # 第四步：处理结果
        final_results = []
        for i, result in enumerate(search_results):
            if isinstance(result, Exception):
                logger.warning(f"批量搜索第{i}个请求失败: {result}")
                final_results.append([])
            else:
                final_results.append(result)

        logger.debug(f"批量向量搜索完成: 处理{len(search_requests)}个请求")
        return final_results

    async def _batch_generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """批量生成嵌入向量"""
        try:
            if not self.embedding_client:
                raise DDVectorizationError("未配置embedding客户端，无法生成向量")

            # 过滤空文本
            valid_texts = [text if text else "empty" for text in texts]

            import asyncio
            result = await asyncio.wait_for(
                self.embedding_client.ainvoke(texts=valid_texts),
                timeout=60.0  # 批量操作增加超时时间
            )

            if not result.embeddings or len(result.embeddings) != len(texts):
                raise DDVectorizationError("批量embedding生成失败：返回结果数量不匹配")

            # 验证向量维度
            for i, embedding in enumerate(result.embeddings):
                if len(embedding) != DDConstants.VECTOR_DIMENSION:
                    raise DDVectorizationError(
                        f"向量{i}维度不匹配: 期望{DDConstants.VECTOR_DIMENSION}, 实际{len(embedding)}"
                    )

            return result.embeddings

        except asyncio.TimeoutError:
            raise DDVectorizationError("批量向量生成超时")
        except Exception as e:
            raise DDVectorizationError(f"批量向量生成失败: {e}")

    async def _search_vectors_with_filter(
        self,
        query_vector: List[float],
        filters: Dict[str, Any],
        limit: int,
        min_score: float
    ) -> List[Dict[str, Any]]:
        """带过滤条件的向量搜索"""
        try:
            results = await self._search_vectors(
                query_vector=query_vector,
                filters=filters,
                limit=limit
            )

            # 过滤低分结果
            filtered_results = []
            for result in results:
                score = result.get("score", 0.0)
                if score >= min_score:
                    filtered_results.append(result)

            return filtered_results

        except Exception as e:
            logger.warning(f"向量搜索失败: {e}")
            return []

    async def _empty_search_result(self) -> List[Dict[str, Any]]:
        """返回空搜索结果"""
        return []

    async def _batch_search_fallback(
        self,
        search_requests: List[Dict[str, Any]]
    ) -> List[List[Dict[str, Any]]]:
        """批量搜索的降级方案（逐个搜索）"""
        results = []

        for request in search_requests:
            try:
                single_result = await self.search_similar_vectors(
                    query_text=request.get("query_text", ""),
                    field_code=request.get("field_code", "dr09"),
                    knowledge_id=request.get("knowledge_id"),
                    data_layer=request.get("data_layer"),
                    limit=request.get("limit", 10),
                    min_score=request.get("min_score", 0.5)
                )
                results.append(single_result)
            except Exception as e:
                logger.warning(f"单个向量搜索失败: {e}")
                results.append([])

        return results
    
    async def delete_embeddings_by_submission(
        self,
        knowledge_id: str,
        data_layer: str,
        submission_pk: int
    ) -> bool:
        """
        根据dd_submission_data主键删除所有相关向量

        Args:
            submission_pk: dd_submission_data表的主键id
        """
        try:
            # 确保异步连接已建立 - 直接调用PGVector客户端
            await self.vdb_client._aensure_connected()

            delete_query = """
            DELETE FROM dd_embeddings
            WHERE knowledge_id = $1
            AND data_layer = $2
            AND data_row_id = $3
            """

            async with self.vdb_client.connection_manager.get_async_connection() as conn:
                result = await conn.execute(delete_query, knowledge_id, data_layer, submission_pk)

            logger.info(f"删除向量: knowledge_id={knowledge_id}, data_layer={data_layer}, submission_pk={submission_pk}")
            return True

        except Exception as e:
            logger.error(f"删除向量失败: {e}")
            raise DDDatabaseError(f"删除向量失败: {e}")
    
    async def generate_embedding(self, text: str) -> List[float]:
        """生成文本嵌入向量 - 公共接口"""
        return await self._generate_embedding(text)

    async def _generate_embedding(self, text: str) -> List[float]:
        """生成文本嵌入向量 - 必须是真实向量"""
        if not self.embedding_client:
            raise DDVectorizationError("未配置embedding客户端，无法生成向量")

        try:
            import asyncio
            result = await asyncio.wait_for(
                self.embedding_client.ainvoke(texts=[text]),
                timeout=30.0
            )

            if not result.embeddings or len(result.embeddings) == 0:
                raise DDVectorizationError("embedding客户端返回空结果")

            embedding = result.embeddings[0]

            # 验证向量维度
            if len(embedding) != DDConstants.VECTOR_DIMENSION:
                raise DDVectorizationError(
                    f"向量维度不匹配: 期望{DDConstants.VECTOR_DIMENSION}, 实际{len(embedding)}"
                )

            return embedding

        except asyncio.TimeoutError:
            raise DDVectorizationError("向量生成超时")
        except Exception as e:
            raise DDVectorizationError(f"向量生成失败: {e}")
    
    # 移除不必要的方法：
    # - _generate_mock_embedding: 假向量没有语义意义
    # - _generate_data_row_id: 未使用的冗余方法
    # - _get_field_id: 不需要数字映射，直接使用字段名
    
    async def insert_vector(self, embedding_data: Dict[str, Any], enable_versioning: bool = True) -> Optional[Any]:
        """
        插入向量数据，支持版本控制

        Args:
            embedding_data: 向量数据
            enable_versioning: 是否启用版本控制

        Returns:
            插入记录的ID
        """
        try:
            knowledge_id = embedding_data.get("knowledge_id")
            data_layer = embedding_data.get("data_layer")
            field_id = embedding_data.get("field_id")
            embedding = embedding_data.get("embedding")
            data_row_id = embedding_data.get("data_row_id")

            # 确保异步连接已建立 - 直接调用PGVector客户端
            await self.vdb_client._aensure_connected()

            if enable_versioning:
                # 实现真正的版本控制逻辑：
                # 1. 将现有的 is_latest=true 记录更新为 is_latest=false
                # 2. 插入新记录，设置 is_latest=true

                update_existing_query = """
                UPDATE dd_embeddings
                SET is_latest = false, update_time = NOW()
                WHERE knowledge_id = $1
                AND data_layer = $2
                AND field_id = $3
                AND data_row_id = $4
                AND is_latest = true
                """

                async with self.vdb_client.connection_manager.get_async_connection() as conn:
                    await conn.execute(update_existing_query, knowledge_id, data_layer, field_id, data_row_id)

            # 插入新记录
            insert_query = """
            INSERT INTO dd_embeddings (
                embedding,
                knowledge_id,
                data_row_id,
                field_id,
                data_layer,
                is_latest,
                create_time,
                update_time
            )
            VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
            RETURNING id
            """

            # 将向量转换为PostgreSQL vector格式
            vector_str = '[' + ','.join(map(str, embedding)) + ']'

            async with self.vdb_client.connection_manager.get_async_connection() as conn:
                result = await conn.fetch(insert_query, vector_str, knowledge_id, data_row_id, field_id, data_layer, True)

            if result and len(result) > 0:
                return result[0].get('id') if isinstance(result[0], dict) else result[0][0]

            return None

        except Exception as e:
            logger.error(f"插入向量数据失败: {e}")
            raise DDDatabaseError(f"插入向量数据失败: {e}")

    async def _insert_vector(self, embedding_data: Dict[str, Any]) -> Optional[Any]:
        """私有方法：向后兼容，调用公共方法"""
        return await self.insert_vector(embedding_data, enable_versioning=True)

    async def batch_insert_vectors(self, embeddings_data: List[Dict[str, Any]], enable_versioning: bool = True) -> List[Any]:
        """
        批量插入向量数据

        Args:
            embeddings_data: 向量数据列表
            enable_versioning: 是否启用版本控制

        Returns:
            插入记录的ID列表
        """
        results = []
        for embedding_data in embeddings_data:
            try:
                result = await self.insert_vector(embedding_data, enable_versioning)
                if result:
                    results.append(result)
            except Exception as e:
                logger.warning(f"批量插入向量失败: {e}")
                # 继续处理其他向量
        return results

    async def get_latest_vectors(
        self,
        knowledge_id: str,
        data_layer: str,
        submission_pk: int,
        field_ids: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        获取最新版本的向量数据

        Args:
            knowledge_id: 知识库ID
            data_layer: 数据层
            submission_pk: dd_submission_data表的主键id
            field_ids: 字段名列表(dr09/dr17)，为空则获取所有字段

        Returns:
            向量数据列表
        """
        try:
            # 确保异步连接已建立 - 直接调用PGVector客户端
            await self.vdb_client._aensure_connected()

            base_query = """
            SELECT id, embedding, knowledge_id, data_row_id, field_id, data_layer, is_latest, create_time, update_time
            FROM dd_embeddings
            WHERE knowledge_id = $1
            AND data_layer = $2
            AND data_row_id = $3
            AND is_latest = true
            """

            params = [knowledge_id, data_layer, submission_pk]

            if field_ids:
                placeholders = ','.join(f'${i+4}' for i in range(len(field_ids)))
                base_query += f" AND field_id IN ({placeholders})"
                params.extend(field_ids)

            async with self.vdb_client.connection_manager.get_async_connection() as conn:
                result = await conn.fetch(base_query, *params)

            return [dict(row) for row in result] if result else []

        except Exception as e:
            logger.error(f"获取最新向量数据失败: {e}")
            raise DDDatabaseError(f"获取最新向量数据失败: {e}")
    
    async def _search_vectors(
        self,
        query_vector: List[float],
        filters: Dict[str, Any],
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索向量"""
        try:
            # 使用PGVectorClient的正确API
            # 构建过滤表达式
            expr_parts = []
            for key, value in filters.items():
                if isinstance(value, str):
                    expr_parts.append(f"{key} = '{value}'")
                else:
                    expr_parts.append(f"{key} = {value}")
            expr = " AND ".join(expr_parts) if expr_parts else None

            # 调用PGVectorClient的asearch方法
            results = await self.vdb_client.asearch(
                collection_name=self.collection_name,
                data=[query_vector],  # 注意：需要是列表的列表
                anns_field="embedding",  # 向量字段名
                param={"metric_type": "COSINE"},  # 搜索参数
                limit=limit,
                expr=expr,
                output_fields=["id", "knowledge_id", "data_row_id", "field_id", "data_layer"]
            )

            # 处理搜索结果
            processed_results = []
            if results and len(results) > 0:
                # PGVectorClient返回的是List[List[SearchResult]]，取第一个查询的结果
                search_results = results[0] if results else []
                for result in search_results:
                    # SearchResult对象有id, score, entity等属性
                    # 只返回数据库中真实存在的字段，确保与dd_embeddings表结构完全一致
                    processed_result = {
                        "id": result.id,
                        "score": result.score,
                        "knowledge_id": result.entity.get("knowledge_id") if result.entity else None,
                        "data_row_id": result.entity.get("data_row_id") if result.entity else None,
                        "field_id": result.entity.get("field_id") if result.entity else None,
                        "data_layer": result.entity.get("data_layer") if result.entity else None
                        # 移除content和source_id字段：这些字段在dd_embeddings表中不存在
                    }
                    processed_results.append(processed_result)

            return processed_results
            
        except Exception as e:
            logger.error(f"搜索向量失败: {e}")
            raise DDDatabaseError(f"搜索向量失败: {e}")
    
    async def _delete_vectors(self, filters: Dict[str, Any]) -> bool:
        """删除向量"""
        try:
            # 构建删除条件表达式
            conditions = []
            for key, value in filters.items():
                if isinstance(value, str):
                    conditions.append(f"{key} = '{value}'")
                elif isinstance(value, bool):
                    conditions.append(f"{key} = {str(value).lower()}")
                else:
                    conditions.append(f"{key} = {value}")
            
            expr = " AND ".join(conditions)
            
            # 调用PGVectorClient的delete方法，需要expr参数
            result = await self.vdb_client.adelete(
                collection_name=self.collection_name,
                expr=expr
            )
            return bool(result)
            
        except Exception as e:
            logger.error(f"删除向量失败: {e}")
            raise DDDatabaseError(f"删除向量失败: {e}")

"""
Metadata系统关联关系CRUD操作

包含以下实体的CRUD操作：
- 源关联键信息 (md_source_key_relation_info)
- 指标关联键信息 (md_index_key_relation_info)
"""

from typing import Any, Dict, List, Optional, Tuple, Union
import logging
from datetime import datetime

from .crud_base import MetadataCrudBase
from ..shared.exceptions import MetadataError, MetadataValidationError, MetadataNotFoundError, MetadataConflictError
from ..shared.constants import MetadataConstants, MetadataTableNames, MetadataCascadeRelations, MetadataVectorCollections
from ..shared.utils import MetadataUtils

logger = logging.getLogger(__name__)


class MetadataCrudRelations(MetadataCrudBase):
    """Metadata系统关联关系CRUD操作"""

    # ==================== 源关联键信息操作 ====================

    async def create_source_key_relation(self, relation_data: Dict[str, Any]) -> <PERSON><PERSON>[int, List[Dict[str, Any]]]:
        """
        创建源关联键信息

        Args:
            relation_data: 关联键数据，必须包含：
                - source_column_id: 源字段ID
                - target_column_id: 目标字段ID
                - relation_type: 关联类型（可选，默认'FK'）
                - comment: 备注说明（可选）

        Returns:
            (关联键ID, 向量创建结果列表)

        Raises:
            MetadataValidationError: 数据验证失败
            MetadataConflictError: 关联键已存在
            MetadataError: 创建失败
        """
        try:
            # 数据验证
            MetadataUtils.validate_source_key_relation_data(relation_data)

            # 检查是否已存在相同的关联键（根据业务唯一键）
            existing = await self._aselect(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                where={
                    'knowledge_id': relation_data['knowledge_id'],
                    'source_column_id': relation_data['source_column_id'],
                    'target_column_id': relation_data['target_column_id'],
                    'relation_type': relation_data.get('relation_type', 'FK')
                },
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"源关联键信息已存在: 知识库{relation_data['knowledge_id']} 源字段{relation_data['source_column_id']} -> 目标字段{relation_data['target_column_id']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(relation_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                data=[relation_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                error_msg = getattr(result, 'error', '未知错误')
                raise MetadataError(f"创建源关联键信息失败: {error_msg}")

            # 获取插入的ID（使用主键字段 relation_id）
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                where={
                    'knowledge_id': relation_data['knowledge_id'],
                    'source_column_id': relation_data['source_column_id'],
                    'target_column_id': relation_data['target_column_id'],
                    'relation_type': relation_data.get('relation_type', 'FK')
                },
                limit=1
            )

            relation_id = inserted_records[0].get('relation_id', 0) if inserted_records else 0

            # 源关联键信息通常不需要向量化，但保留接口以备扩展
            vector_results = []
            # 如果需要向量化，可以取消注释以下代码：
            # if self.vdb_client and self.embedding_client and relation_id:
            #     try:
            #         vector_results = await self._create_vectors('source_key_relation', relation_id, relation_data)
            #     except Exception as e:
            #         logger.warning(f"源关联键信息向量化失败: {e}")

            logger.info(f"源关联键信息创建成功: 源字段{relation_data['source_column_id']} -> 目标字段{relation_data['target_column_id']} (ID: {relation_id})")
            return relation_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建源关联键信息失败: {e}")
            raise MetadataError(f"创建源关联键信息失败: {e}")

    async def get_source_key_relation(self, relation_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取源关联键信息"""
        if relation_id:
            where = {'relation_id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_source_key_relation(self, relation_data: Dict[str, Any], relation_id: int = None, **where_conditions) -> bool:
        """更新源关联键信息"""
        if relation_id:
            where = {'relation_id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(relation_data, is_update=True)

        try:
            updates = [{"data": relation_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )
            return result.success and result.affected_rows > 0
        except Exception as e:
            logger.error(f"更新源关联键信息失败: {e}")
            raise MetadataError(f"更新源关联键信息失败: {e}")

    async def delete_source_key_relation(self, relation_id: int = None, **where_conditions) -> bool:
        """删除源关联键信息"""
        if relation_id:
            where = {'relation_id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他删除条件")

        try:
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除源关联键信息失败: {e}")
            raise MetadataError(f"删除源关联键信息失败: {e}")

    async def list_source_key_relations(
        self,
        knowledge_id: Optional[str] = None,
        db_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询源关联键信息列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            db_id=db_id,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    async def batch_create_source_key_relations(self, relations_data: List[Dict[str, Any]]) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """
        批量创建源关联键信息

        Args:
            relations_data: 关联键数据列表

        Returns:
            (关联键ID列表, 向量创建结果列表的列表)

        Raises:
            MetadataError: 批量创建失败
        """
        try:
            # 数据验证
            for relation_data in relations_data:
                MetadataUtils.validate_source_key_relation_data(relation_data)
                MetadataUtils.add_timestamps(relation_data)

            # 批量插入
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                data=relations_data,
                batch_size=len(relations_data) if len(relations_data) <= 100 else 100,
                max_concurrency=1 if len(relations_data) <= 10 else 3
            )

            if not result.success:
                raise MetadataError(f"批量创建源关联键信息失败: {result.error}")

            # 获取插入的ID（使用正确的字段结构）
            relation_ids = []
            vector_results_list = []

            for relation_data in relations_data:
                inserted_records = await self._aselect(
                    table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
                    where={
                        'source_column_id': relation_data['source_column_id'],
                        'target_column_id': relation_data['target_column_id'],
                        'relation_type': relation_data.get('relation_type', 'FK')
                    },
                    limit=1
                )
                relation_id = inserted_records[0].get('relation_id', 0) if inserted_records else 0
                relation_ids.append(relation_id)
                vector_results_list.append([])  # 暂时不进行向量化

            logger.info(f"批量创建源关联键信息成功: {len(relation_ids)} 个")
            return relation_ids, vector_results_list

        except Exception as e:
            logger.error(f"批量创建源关联键信息失败: {e}")
            raise MetadataError(f"批量创建源关联键信息失败: {e}")

    async def search_source_key_relations(
        self,
        search_text: str,
        knowledge_id: Optional[str] = None,
        db_id: Optional[int] = None,
        limit: Optional[int] = 10
    ) -> List[Dict[str, Any]]:
        """
        搜索源关联键信息

        Args:
            search_text: 搜索文本
            knowledge_id: 知识库ID（可选）
            db_id: 数据库ID（可选，但表中没有此字段，忽略）
            limit: 限制数量（可选）

        Returns:
            匹配的关联键信息列表
        """
        where = {}

        # 现在表中有 knowledge_id 字段了
        if knowledge_id:
            where['knowledge_id'] = knowledge_id

        # 按照 comment 字段进行搜索
        if search_text:
            where['comment'] = f"%{search_text}%"

        return await self._aselect(
            table=MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit
        )

    # ==================== 指标关联键信息操作 ====================

    async def create_index_key_relation(self, relation_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建指标关联键信息"""
        try:
            # 数据验证
            MetadataUtils.validate_index_key_relation_data(relation_data)

            # 添加时间戳
            MetadataUtils.add_timestamps(relation_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
                data=[relation_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                raise MetadataError(f"创建指标关联键信息失败: {result.error}")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
                where={
                    'knowledge_id': relation_data['knowledge_id'],
                    'db_id': relation_data['db_id'],
                    'relation_name': relation_data['relation_name']
                },
                limit=1
            )

            relation_id = inserted_records[0].get('id', 0) if inserted_records else 0

            # 关联键信息通常不需要向量化，但保留接口以备扩展
            vector_results = []

            logger.info(f"指标关联键信息创建成功: {relation_data['relation_name']} (ID: {relation_id})")
            return relation_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建指标关联键信息失败: {e}")
            raise MetadataError(f"创建指标关联键信息失败: {e}")

    async def get_index_key_relation(self, relation_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取指标关联键信息"""
        if relation_id:
            where = {'id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_index_key_relation(self, relation_data: Dict[str, Any], relation_id: int = None, **where_conditions) -> bool:
        """更新指标关联键信息"""
        if relation_id:
            where = {'id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(relation_data, is_update=True)

        try:
            updates = [{"data": relation_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )
            return result.success and result.affected_rows > 0
        except Exception as e:
            logger.error(f"更新指标关联键信息失败: {e}")
            raise MetadataError(f"更新指标关联键信息失败: {e}")

    async def delete_index_key_relation(self, relation_id: int = None, **where_conditions) -> bool:
        """删除指标关联键信息"""
        if relation_id:
            where = {'id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他删除条件")

        try:
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除指标关联键信息失败: {e}")
            raise MetadataError(f"删除指标关联键信息失败: {e}")

    async def list_index_key_relations(
        self,
        knowledge_id: Optional[str] = None,
        db_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询指标关联键信息列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            db_id=db_id,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_INDEX_KEY_RELATION_INFO,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

#!/usr/bin/env python3
"""
DD-B模块API简化测试脚本

测试DD-B模块的核心处理功能
"""

import asyncio
import os
import sys
import time
from typing import Dict, Any

# 设置项目根目录
project_root = os.getcwd()
sys.path.insert(0, project_root)

# 设置简单的日志配置
from utils.common.logger import setup_enterprise_plus_logger
logger = setup_enterprise_plus_logger(
    name="dd_b_api_test",
    level="INFO"
)

# 测试参数
TEST_CASES = [
    {
        "name": "标准测试用例",
        "report_code": "S71_ADS_RELEASE_V0",
        "dept_id": "30239"
    },
]


async def get_database_clients():
    """获取数据库客户端"""
    try:
        from service import get_client

        # 获取必需的MySQL客户端
        rdb_client = await get_client('database.rdbs.mysql')
        logger.info("✅ RDB客户端获取成功")

        # 尝试获取可选的向量数据库和embedding客户端
        vdb_client = None
        embedding_client = None

        try:
            vdb_client = await get_client('database.vdbs.pgvector')
            logger.info("✅ VDB客户端获取成功")
        except Exception as e:
            logger.warning(f"⚠️ VDB客户端获取失败: {e}")

        try:
            embedding_client = await get_client('model.embeddings.moka-m3e-base')
            logger.info("✅ Embedding客户端获取成功")
        except Exception as e:
            logger.warning(f"⚠️ Embedding客户端获取失败: {e}")

        return {
            'rdb_client': rdb_client,
            'vdb_client': vdb_client,
            'embedding_client': embedding_client
        }

    except Exception as e:
        logger.error(f"❌ 数据库客户端获取失败: {e}")
        raise


async def test_dd_b_api_processing(test_case: Dict[str, str]) -> bool:
    """
    测试DD-B API处理

    Args:
        test_case: 测试用例参数

    Returns:
        bool: 测试是否成功
    """
    logger.info(f"\n🎯 开始测试: {test_case['name']}")
    logger.info(f"参数: report_code={test_case['report_code']}, dept_id={test_case['dept_id']}")
    logger.info("-" * 60)

    try:
        # 1. 获取数据库客户端
        clients = await get_database_clients()
        rdb_client = clients['rdb_client']
        vdb_client = clients['vdb_client']
        embedding_client = clients['embedding_client']

        # 2. 调用DD-B处理函数
        from modules.dd_submission.dd_b import process_dd_b_request_enhanced, DDBProcessRequest

        start_time = time.time()

        # 创建请求对象
        request = DDBProcessRequest(
            report_code=test_case['report_code'],
            dept_id=test_case['dept_id'],
            enable_auto_fill=True,
            return_original_data=False
        )

        # 调用处理函数
        result = await process_dd_b_request_enhanced(
            rdb_client=rdb_client,
            request=request,
            vdb_client=vdb_client,
            embedding_client=embedding_client,
            enable_concurrency=True,
            max_llm_concurrent=15
        )

        processing_time = (time.time() - start_time) * 1000

        # 3. 验证API响应
        logger.info("📊 API响应验证:")

        # 验证响应结构
        required_fields = ['status', 'total_records_found', 'records_processed', 'total_fields_filled']
        missing_fields = []

        for field in required_fields:
            if not hasattr(result, field):
                missing_fields.append(field)

        if missing_fields:
            logger.error(f"❌ 响应缺少必需字段: {missing_fields}")
            return False

        # 输出关键结果
        logger.info(f"  状态: {result.status}")
        logger.info(f"  找到记录数: {result.total_records_found}")
        logger.info(f"  处理记录数: {result.records_processed}")
        logger.info(f"  填充字段数: {result.total_fields_filled}")
        logger.info(f"  处理耗时: {processing_time:.2f}ms")

        # 4. 验证处理结果
        success_criteria = []

        # 标准1: 状态为成功
        if result.status == "success":
            logger.info("✅ 状态验证: 成功")
            success_criteria.append(True)
        else:
            logger.error(f"❌ 状态验证: 失败 ({result.status})")
            success_criteria.append(False)

        # 标准2: 找到了记录
        if result.total_records_found > 0:
            logger.info(f"✅ 记录查询: 找到 {result.total_records_found} 条记录")
            success_criteria.append(True)
        else:
            logger.warning("⚠️ 记录查询: 未找到记录")
            success_criteria.append(False)

        # 标准3: 处理了记录
        if result.records_processed > 0:
            logger.info(f"✅ 记录处理: 处理了 {result.records_processed} 条记录")
            success_criteria.append(True)
        else:
            logger.warning("⚠️ 记录处理: 未处理任何记录")
            success_criteria.append(False)

        # 标准4: 填充了字段
        if result.total_fields_filled > 0:
            logger.info(f"✅ 字段填充: 填充了 {result.total_fields_filled} 个字段")
            success_criteria.append(True)
        else:
            logger.warning("⚠️ 字段填充: 未填充任何字段")
            success_criteria.append(False)

        # 标准5: 处理时间合理
        if processing_time < 30000:  # 30秒内
            logger.info(f"✅ 性能验证: 处理时间合理 ({processing_time:.2f}ms)")
            success_criteria.append(True)
        else:
            logger.warning(f"⚠️ 性能验证: 处理时间过长 ({processing_time:.2f}ms)")
            success_criteria.append(False)

        # 5. 基本结果分析
        if result.processed_records:
            sample_record = result.processed_records[0]
            logger.info(f"\n📋 示例记录ID: {sample_record.id}")

            # 检查主要字段填充情况
            main_fields = ['bdr09', 'bdr10', 'bdr11', 'bdr16']
            filled_main_fields = 0

            for field in main_fields:
                value = getattr(sample_record, field, '')
                if value and value.strip():
                    filled_main_fields += 1

            main_field_rate = filled_main_fields / len(main_fields) * 100
            logger.info(f"主要字段填充率: {filled_main_fields}/{len(main_fields)} ({main_field_rate:.1f}%)")

        # 6. 计算总体成功率
        success_rate = sum(success_criteria) / len(success_criteria) * 100
        logger.info(f"\n📊 测试成功率: {success_rate:.1f}% ({sum(success_criteria)}/{len(success_criteria)})")

        # 判断测试是否通过
        test_passed = success_rate >= 80  # 80%以上算通过

        if test_passed:
            logger.info(f"🎉 {test_case['name']} 测试通过!")
        else:
            logger.warning(f"⚠️ {test_case['name']} 测试未完全通过")

        return test_passed

    except Exception as e:
        logger.error(f"❌ {test_case['name']} 测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    logger.info("🚀 开始DD-B模块API测试")
    logger.info("=" * 60)

    test_results = []

    # 运行所有测试用例
    for test_case in TEST_CASES:
        try:
            result = await test_dd_b_api_processing(test_case)
            test_results.append({
                'name': test_case['name'],
                'passed': result
            })
        except Exception as e:
            logger.error(f"❌ 测试用例 {test_case['name']} 执行异常: {e}")
            test_results.append({
                'name': test_case['name'],
                'passed': False
            })

    # 测试总结
    logger.info("\n" + "=" * 60)
    logger.info("📊 DD-B模块API测试总结:")

    passed_tests = sum(1 for result in test_results if result['passed'])
    total_tests = len(test_results)

    for result in test_results:
        status = "✅ 通过" if result['passed'] else "❌ 失败"
        logger.info(f"  {result['name']}: {status}")

    overall_success_rate = passed_tests / total_tests * 100
    logger.info(f"\n总体成功率: {overall_success_rate:.1f}% ({passed_tests}/{total_tests})")

    if overall_success_rate >= 80:
        logger.info("🎉 DD-B模块API测试整体通过!")
        return True
    else:
        logger.warning("⚠️ DD-B模块API测试存在问题，需要检查")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

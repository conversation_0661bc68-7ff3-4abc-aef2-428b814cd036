-- ==========================================
-- DD数据需求管理系统 - PostgreSQL向量表结构
-- Data Demand Management System - PostgreSQL Vector Tables
-- ==========================================
-- 
-- 说明：
-- 1. 本脚本专门用于创建DD（数据需求）相关的PostgreSQL向量表
-- 2. 使用pgvector扩展支持向量搜索功能
-- 3. 采用企业级两级分区设计，支持大规模数据和版本管理
-- 4. 修正了原有的md_前缀问题，直接使用dd_前缀
-- 
-- 表结构设计：
-- - dd_embeddings: DD内容向量搜索表
-- 
-- 创建时间：2025-07-04
-- 最后修改：2025-07-15 (增加is_latest两级分区)
-- ==========================================

-- 确保pgvector扩展已安装
CREATE EXTENSION IF NOT EXISTS vector;

-- ==========================================
-- DD内容向量搜索表 (DD Content Embeddings)
-- 企业级设计：两级分区（最新版本 + 哈希），支持DD文档的语义搜索和版本管理
-- 分区策略：
--   - 顶级分区：按 is_latest (LIST) 分区，快速筛选最新版本
--   - 子分区：按 HASH(knowledge_id, data_layer, field_id) 分区，共256个子分区
-- ==========================================
DROP TABLE IF EXISTS dd_embeddings CASCADE;
CREATE TABLE dd_embeddings (
    id BIGSERIAL,
    embedding VECTOR(768) NOT NULL,

    -- 核心关联键（优化后）
    knowledge_id VARCHAR(255) NOT NULL,
    data_row_id BIGINT NOT NULL,           -- 关联dd_submission_data表的主键ID

    -- 业务分区和查询优化字段
    field_id VARCHAR(30) NOT NULL,                 -- 字段ID，用于业务查询优化
    data_layer VARCHAR(20) NOT NULL,       -- 数据层标识，用于分区和业务查询
    is_latest BOOLEAN NOT NULL,            -- 是否为最新版本，用于顶级分区

    create_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    update_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- 修正主键：移除id，使用业务主键
    PRIMARY KEY (is_latest, knowledge_id, data_layer, field_id, data_row_id)
) PARTITION BY LIST (is_latest);

-- 添加表和字段注释
COMMENT ON TABLE dd_embeddings IS 'DD内容向量搜索表，存储数据需求文档中各字段内容的向量化信息。采用is_latest和hash的两级分区策略。';
COMMENT ON COLUMN dd_embeddings.embedding IS 'DD内容的向量表示，维度768';
COMMENT ON COLUMN dd_embeddings.knowledge_id IS '知识库ID，用于隔离不同知识库的数据';
COMMENT ON COLUMN dd_embeddings.data_row_id IS '数据行ID，关联dd_submission_data表的主键ID';
COMMENT ON COLUMN dd_embeddings.field_id IS '字段ID，关联dd_fields_metadata表，用于业务查询优化';
COMMENT ON COLUMN dd_embeddings.data_layer IS 'DR01, 数据层标识，如ADS/BDM/IDM/ADM/ODS等，用于分区和查询优化';
COMMENT ON COLUMN dd_embeddings.is_latest IS '是否为最新版本，TRUE表示最新，FALSE表示历史版本，用于顶级分区';
COMMENT ON COLUMN dd_embeddings.create_time IS '向量创建时间';
COMMENT ON COLUMN dd_embeddings.update_time IS '向量更新时间，用于调试和增量同步';

-- 创建顶级分区 (latest 和 archive)
CREATE TABLE dd_embeddings_latest PARTITION OF dd_embeddings FOR VALUES IN (TRUE) PARTITION BY HASH (knowledge_id, data_layer, field_id);
CREATE TABLE dd_embeddings_archive PARTITION OF dd_embeddings FOR VALUES IN (FALSE) PARTITION BY HASH (knowledge_id, data_layer, field_id);

COMMENT ON TABLE dd_embeddings_latest IS '存储最新版本DD向量数据的分区';
COMMENT ON TABLE dd_embeddings_archive IS '存储历史版本DD向量数据的分区';

-- 为顶级分区创建128个哈希子分区
-- 子分区 for dd_embeddings_latest (is_latest = TRUE)
DO $$
BEGIN
    FOR i IN 0..127 LOOP
        EXECUTE format('CREATE TABLE dd_embeddings_latest_part_%s PARTITION OF dd_embeddings_latest FOR VALUES WITH (modulus 128, remainder %s)',
                      lpad(i::text, 3, '0'), i);
    END LOOP;
END $$;

-- 子分区 for dd_embeddings_archive (is_latest = FALSE)
DO $$
BEGIN
    FOR i IN 0..127 LOOP
        EXECUTE format('CREATE TABLE dd_embeddings_archive_part_%s PARTITION OF dd_embeddings_archive FOR VALUES WITH (modulus 128, remainder %s)',
                      lpad(i::text, 3, '0'), i);
    END LOOP;
END $$;


-- 索引策略
CREATE INDEX idx_dd_embeddings_hnsw ON dd_embeddings USING HNSW (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 200);

CREATE INDEX idx_dd_embeddings_data_row_id ON dd_embeddings (data_row_id);

CREATE INDEX idx_dd_embeddings_field ON dd_embeddings (field_id);

CREATE INDEX idx_dd_embeddings_data_layer ON dd_embeddings (data_layer);

CREATE INDEX idx_dd_embeddings_time ON dd_embeddings (create_time DESC);

CREATE INDEX idx_dd_embeddings_update_time ON dd_embeddings (update_time DESC);

-- 业务唯一约束：确保同一版本下的向量唯一性（主键已包含此约束，此索引用于查询优化）
CREATE INDEX idx_dd_embeddings_business_key ON dd_embeddings (knowledge_id, data_layer, field_id, data_row_id);

-- 添加索引注释
COMMENT ON INDEX idx_dd_embeddings_hnsw IS 'DD内容向量HNSW索引：企业级参数优化';
COMMENT ON INDEX idx_dd_embeddings_data_row_id IS 'DD提交数据行ID索引，用于向量数据与MySQL中dd_submission_data表的逻辑关联查询';
COMMENT ON INDEX idx_dd_embeddings_field IS '字段ID索引，用于按字段类型搜索';
COMMENT ON INDEX idx_dd_embeddings_data_layer IS '数据层索引，用于按数据层级搜索和分区优化';
COMMENT ON INDEX idx_dd_embeddings_time IS '创建时间索引，支持增量同步和数据治理';
COMMENT ON INDEX idx_dd_embeddings_update_time IS '更新时间索引，用于调试和增量同步优化';

-- ==========================================
-- DD分区管理视图
-- ==========================================

-- DD分区统计视图
CREATE OR REPLACE VIEW v_dd_partition_stats AS
SELECT
    schemaname,
    'dd_embeddings' as table_name,
    COUNT(*) as partition_count,
    'LIST(is_latest) -> HASH(knowledge_id, data_layer, field_id)' as partition_strategy,
    'Enterprise Standard: 2-level partitioning for versioning and scalability' as design_rationale
FROM pg_tables
WHERE tablename LIKE 'dd_embeddings_latest_part_%' OR tablename LIKE 'dd_embeddings_archive_part_%'
GROUP BY schemaname;

-- DD分区管理函数
CREATE OR REPLACE FUNCTION get_dd_partition_info()
RETURNS TABLE (
    partition_name TEXT,
    partition_size BIGINT,
    row_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.tablename::TEXT as partition_name,
        pg_total_relation_size(t.schemaname||'.'||t.tablename) as partition_size,
        COALESCE(s.n_tup_ins - s.n_tup_del, 0) as row_count
    FROM pg_tables t
    LEFT JOIN pg_stat_user_tables s ON s.relname = t.tablename
    WHERE t.tablename LIKE 'dd_embeddings_latest_part_%' OR t.tablename LIKE 'dd_embeddings_archive_part_%'
    ORDER BY t.tablename;
END;
$$ LANGUAGE plpgsql;

-- ==========================================
-- 向量表创建完成
-- ==========================================
-- 
-- 下一步操作建议：
-- 1. 确保MySQL中的DD表已创建（运行create_rdb_dd.sql）
-- 2. 初始化DD字段元数据
-- 3. 测试向量化功能
-- 
-- 查询分区信息：
-- SELECT * FROM v_dd_partition_stats;
-- SELECT * FROM get_dd_partition_info();
-- 
-- ==========================================

"""
动态搜索条件构建器

实现可扩展的搜索条件参数系统，支持参数化查询
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

# 引入最新的表名常量
from modules.knowledge.dd.shared.constants import DDTableNames

logger = logging.getLogger(__name__)


class SearchOperator(str, Enum):
    """搜索操作符枚举"""
    EQUAL = "="
    NOT_EQUAL = "!="
    LIKE = "LIKE"
    NOT_LIKE = "NOT LIKE"
    IN = "IN"
    NOT_IN = "NOT IN"
    IS_NULL = "IS NULL"
    IS_NOT_NULL = "IS NOT NULL"
    GREATER_THAN = ">"
    LESS_THAN = "<"
    GREATER_EQUAL = ">="
    LESS_EQUAL = "<="


class LogicalOperator(str, Enum):
    """逻辑操作符枚举"""
    AND = "AND"
    OR = "OR"


@dataclass
class SearchCondition:
    """搜索条件"""
    field: str                              # 字段名
    operator: SearchOperator                # 操作符
    value: Any                             # 值
    logical_op: LogicalOperator = LogicalOperator.AND  # 逻辑操作符
    
    def to_sql_clause(self) -> Tuple[str, List[Any]]:
        """转换为SQL子句"""
        if self.operator in [SearchOperator.IS_NULL, SearchOperator.IS_NOT_NULL]:
            return f"{self.field} {self.operator.value}", []
        elif self.operator in [SearchOperator.IN, SearchOperator.NOT_IN]:
            if isinstance(self.value, (list, tuple)):
                placeholders = ",".join(["?" for _ in self.value])
                return f"{self.field} {self.operator.value} ({placeholders})", list(self.value)
            else:
                return f"{self.field} {self.operator.value} (?)", [self.value]
        else:
            return f"{self.field} {self.operator.value} ?", [self.value]


@dataclass
class SearchQuery:
    """搜索查询"""
    table_name: str                         # 表名
    select_fields: List[str] = field(default_factory=lambda: ["*"])  # 选择字段
    conditions: List[SearchCondition] = field(default_factory=list)  # 搜索条件
    order_by: Optional[List[str]] = None    # 排序字段
    limit: Optional[int] = None             # 限制数量
    offset: Optional[int] = None            # 偏移量
    
    def add_condition(self, field: str, operator: SearchOperator, value: Any, 
                     logical_op: LogicalOperator = LogicalOperator.AND) -> 'SearchQuery':
        """添加搜索条件"""
        condition = SearchCondition(field, operator, value, logical_op)
        self.conditions.append(condition)
        return self
    
    def to_sql(self) -> Tuple[str, Dict[str, Any]]:
        """转换为SQL语句"""
        # SELECT子句
        select_clause = f"SELECT {', '.join(self.select_fields)}"

        # FROM子句
        from_clause = f"FROM {self.table_name}"

        # WHERE子句
        where_clause = ""
        params = {}
        param_counter = 0

        if self.conditions:
            where_parts = []
            for i, condition in enumerate(self.conditions):
                # 修改条件转换，使用命名参数
                if condition.operator in [SearchOperator.IS_NULL, SearchOperator.IS_NOT_NULL]:
                    clause = f"{condition.field} {condition.operator.value}"
                elif condition.operator in [SearchOperator.IN, SearchOperator.NOT_IN]:
                    if isinstance(condition.value, (list, tuple)):
                        placeholders = []
                        for j, val in enumerate(condition.value):
                            param_name = f"param_{param_counter}"
                            placeholders.append(f":{param_name}")
                            params[param_name] = val
                            param_counter += 1
                        clause = f"{condition.field} {condition.operator.value} ({','.join(placeholders)})"
                    else:
                        param_name = f"param_{param_counter}"
                        clause = f"{condition.field} {condition.operator.value} (:{param_name})"
                        params[param_name] = condition.value
                        param_counter += 1
                else:
                    param_name = f"param_{param_counter}"
                    clause = f"{condition.field} {condition.operator.value} :{param_name}"
                    params[param_name] = condition.value
                    param_counter += 1

                if i == 0:
                    where_parts.append(clause)
                else:
                    where_parts.append(f"{condition.logical_op.value} {clause}")

            where_clause = f"WHERE {' '.join(where_parts)}"
        
        # ORDER BY子句
        order_clause = ""
        if self.order_by:
            order_clause = f"ORDER BY {', '.join(self.order_by)}"
        
        # LIMIT子句
        limit_clause = ""
        if self.limit is not None:
            limit_clause = f"LIMIT {self.limit}"
            if self.offset is not None:
                limit_clause += f" OFFSET {self.offset}"
        
        # 组合SQL
        sql_parts = [select_clause, from_clause]
        if where_clause:
            sql_parts.append(where_clause)
        if order_clause:
            sql_parts.append(order_clause)
        if limit_clause:
            sql_parts.append(limit_clause)
        
        sql = " ".join(sql_parts)
        return sql, params


class DynamicSearchBuilder:
    """动态搜索条件构建器"""
    
    def __init__(self):
        """初始化搜索构建器"""
        # 预定义的搜索模板 - 使用标准表名常量
        self.search_templates = {
            'pre_distribution_basic': {
                'table': DDTableNames.BIZ_PRE_DISTRIBUTION,
                'default_fields': ['id', 'submission_id', 'dr07', 'version', 'dr09', 'dr17', '`set`', 'report_type', 'submission_type', 'dr01'],
                'common_conditions': ['dr07', 'version']
            },
            'submission_data_basic': {
                'table': DDTableNames.KB_SUBMISSION_DATA,
                'default_fields': ['id', 'submission_id', 'dr09', 'dr17', 'dr22', '`set`', 'report_type', 'submission_type', 'dr01'],
                'common_conditions': ['knowledge_id', 'version', 'data_layer']
            },
            'departments_basic': {
                'table': DDTableNames.BIZ_DEPARTMENTS,
                'default_fields': ['dept_id', 'dept_name', 'dept_desc', 'dept_type', 'is_active'],
                'common_conditions': ['is_active', 'dept_type']
            }
        }
    
    def create_pre_distribution_search(self, **kwargs) -> SearchQuery:
        """
        创建分发前数据搜索查询
        
        Args:
            dr07: 数据需求编号
            version: 版本
            dr09: 数据项名称（可选）
            dr17: 数据项定义（可选）
            limit: 限制数量（可选）
            
        Returns:
            搜索查询对象
        """
        template = self.search_templates['pre_distribution_basic']
        query = SearchQuery(
            table_name=template['table'],
            select_fields=kwargs.get('select_fields', template['default_fields'])
        )
        
        # 必填条件
        if 'dr07' in kwargs and kwargs['dr07']:
            query.add_condition('dr07', SearchOperator.EQUAL, kwargs['dr07'])
        
        if 'version' in kwargs and kwargs['version']:
            query.add_condition('version', SearchOperator.EQUAL, kwargs['version'])
        
        # 可选条件
        if 'dr09' in kwargs and kwargs['dr09']:
            query.add_condition('dr09', SearchOperator.LIKE, f"%{kwargs['dr09']}%")
        
        if 'dr17' in kwargs and kwargs['dr17']:
            query.add_condition('dr17', SearchOperator.LIKE, f"%{kwargs['dr17']}%")
        
        if 'dr01' in kwargs and kwargs['dr01']:
            query.add_condition('dr01', SearchOperator.EQUAL, kwargs['dr01'])
        
        # 排序和限制
        query.order_by = kwargs.get('order_by', ['id'])
        query.limit = kwargs.get('limit')
        query.offset = kwargs.get('offset')
        
        return query
    
    def create_submission_data_search(self, **kwargs) -> SearchQuery:
        """
        创建填报数据搜索查询
        
        Args:
            knowledge_id: 知识库ID（可选）
            version: 版本（可选）
            data_layer: 数据层（可选）
            dr09: 数据项名称（可选）
            dr17: 数据项定义（可选）
            set_value: 套系值（可选）
            report_type: 报表类型（可选）
            submission_type: 提交类型（可选）
            dr01: 数据层（可选）
            limit: 限制数量（可选）
            
        Returns:
            搜索查询对象
        """
        template = self.search_templates['submission_data_basic']
        query = SearchQuery(
            table_name=template['table'],
            select_fields=kwargs.get('select_fields', template['default_fields'])
        )
        
        # 可选条件
        optional_conditions = [
            ('knowledge_id', SearchOperator.EQUAL),
            ('version', SearchOperator.EQUAL),
            ('data_layer', SearchOperator.EQUAL),
            ('dr01', SearchOperator.EQUAL),
            ('set', SearchOperator.EQUAL),
            ('report_type', SearchOperator.EQUAL),
            ('submission_type', SearchOperator.EQUAL)
        ]
        
        for field, operator in optional_conditions:
            if field in kwargs and kwargs[field]:
                query.add_condition(field, operator, kwargs[field])
        
        # 文本搜索条件
        if 'dr09' in kwargs and kwargs['dr09']:
            if kwargs.get('exact_match', False):
                query.add_condition('dr09', SearchOperator.EQUAL, kwargs['dr09'])
            else:
                query.add_condition('dr09', SearchOperator.LIKE, f"%{kwargs['dr09']}%")
        
        if 'dr17' in kwargs and kwargs['dr17']:
            if kwargs.get('exact_match', False):
                query.add_condition('dr17', SearchOperator.EQUAL, kwargs['dr17'])
            else:
                query.add_condition('dr17', SearchOperator.LIKE, f"%{kwargs['dr17']}%")
        
        # 排序和限制
        query.order_by = kwargs.get('order_by', ['id'])
        query.limit = kwargs.get('limit')
        query.offset = kwargs.get('offset')
        
        return query
    
    def create_departments_search(self, **kwargs) -> SearchQuery:
        """
        创建部门搜索查询
        
        Args:
            dept_name: 部门名称（可选）
            dept_description: 部门描述（可选）
            dept_type: 部门类型（可选）
            is_active: 是否激活（可选，默认True）
            keywords: 关键词列表（可选）
            limit: 限制数量（可选）
            
        Returns:
            搜索查询对象
        """
        template = self.search_templates['departments_basic']
        query = SearchQuery(
            table_name=template['table'],
            select_fields=kwargs.get('select_fields', template['default_fields'])
        )
        
        # 默认只查询激活的部门
        is_active = kwargs.get('is_active', True)
        if is_active is not None:
            query.add_condition('is_active', SearchOperator.EQUAL, is_active)
        
        # 部门类型
        if 'dept_type' in kwargs and kwargs['dept_type']:
            query.add_condition('dept_type', SearchOperator.EQUAL, kwargs['dept_type'])
        
        # 文本搜索
        if 'dept_name' in kwargs and kwargs['dept_name']:
            query.add_condition('dept_name', SearchOperator.LIKE, f"%{kwargs['dept_name']}%")
        
        if 'dept_description' in kwargs and kwargs['dept_description']:
            query.add_condition('dept_description', SearchOperator.LIKE, f"%{kwargs['dept_description']}%")
        
        # 关键词搜索（使用OR逻辑）
        if 'keywords' in kwargs and kwargs['keywords']:
            keywords = kwargs['keywords']
            if isinstance(keywords, str):
                keywords = [keywords]
            
            # 为关键词创建OR条件组
            for i, keyword in enumerate(keywords):
                logical_op = LogicalOperator.OR if i > 0 else LogicalOperator.AND
                query.add_condition('dept_name', SearchOperator.LIKE, f"%{keyword}%", logical_op)
                query.add_condition('dept_description', SearchOperator.LIKE, f"%{keyword}%", LogicalOperator.OR)
        
        # 排序和限制
        query.order_by = kwargs.get('order_by', ['dept_name'])
        query.limit = kwargs.get('limit')
        query.offset = kwargs.get('offset')
        
        return query
    
    def create_custom_search(self, table_name: str, **kwargs) -> SearchQuery:
        """
        创建自定义搜索查询
        
        Args:
            table_name: 表名
            **kwargs: 搜索参数
            
        Returns:
            搜索查询对象
        """
        query = SearchQuery(
            table_name=table_name,
            select_fields=kwargs.get('select_fields', ['*'])
        )
        
        # 动态添加条件
        conditions = kwargs.get('conditions', {})
        for field, value in conditions.items():
            if value is not None:
                if isinstance(value, dict) and 'operator' in value:
                    operator = SearchOperator(value['operator'])
                    actual_value = value['value']
                else:
                    operator = SearchOperator.EQUAL
                    actual_value = value
                
                query.add_condition(field, operator, actual_value)
        
        # 排序和限制
        query.order_by = kwargs.get('order_by')
        query.limit = kwargs.get('limit')
        query.offset = kwargs.get('offset')
        
        return query


# 全局实例
_search_builder_instance = None

def get_search_builder() -> DynamicSearchBuilder:
    """获取搜索构建器实例（单例）"""
    global _search_builder_instance
    if _search_builder_instance is None:
        _search_builder_instance = DynamicSearchBuilder()
    return _search_builder_instance

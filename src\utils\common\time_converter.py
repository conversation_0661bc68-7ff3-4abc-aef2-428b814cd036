import jionlp as jio
from datetime import datetime
import dateparser
from dateparser.date import DateDataParser
import re

def convert_date_format(time_description: str) -> dict:
    """
    将自然语言时间描述转换为标准日期格式并识别时间粒度
    
    Args:
        time_description: 自然语言时间描述，如"上个月"、"去年"、"前天"等
        
    Returns:
        dict: 包含原始描述、转换后日期范围和时间粒度的字典
    """
    if not time_description or time_description.strip() == "":
        return {
            "original": "",
            "start_date": "",
            "end_date": "",
            "is_range": False,
            "description": ""
        }
    
    result = {
        "original": time_description,
        "start_date": "",
        "end_date": "",
        "is_range": False,
        "description": ""
    }
    
    # 尝试使用jionlp解析时间表达式获取日期范围
    try:
        time_info = jio.parse_time(time_description)
        
        if time_info:
            # 处理时间范围 - 检查两种可能的格式
            if ('time_span' in time_info and len(time_info['time_span']) == 2) or \
               ('type' in time_info and time_info['type'] == 'time_span' and 'time' in time_info and isinstance(time_info['time'], list) and len(time_info['time']) == 2):
                
                # 处理第一种格式
                if 'time_span' in time_info:
                    result["start_date"] = time_info['time_span'][0].split()[0]
                    result["end_date"] = time_info['time_span'][1].split()[0]
                # 处理第二种格式
                else:
                    result["start_date"] = time_info['time'][0].split()[0]
                    result["end_date"] = time_info['time'][1].split()[0]
                    
                result["is_range"] = True
                
            # 处理单个时间点
            elif 'time' in time_info and time_info['time']:
                # 检查time是列表还是字符串
                if isinstance(time_info['time'], list):
                    # 如果是列表但只有一个元素
                    if time_info['time']:
                        result["start_date"] = time_info['time'][0].split()[0]
                        result["end_date"] = result["start_date"]
                else:
                    # 如果是字符串，直接处理
                    result["start_date"] = time_info['time'].split()[0]
                    result["end_date"] = result["start_date"]
    except Exception:
        # jionlp解析失败，使用dateparser尝试
        try:
            # 设置中国时区 (UTC+8)
            parsed_date = dateparser.parse(
                time_description, 
                languages=['zh'],
                settings={
                    'TIMEZONE': 'Asia/Shanghai',
                    'TO_TIMEZONE': 'Asia/Shanghai'
                }
            )
            if parsed_date:
                result["start_date"] = parsed_date.strftime("%Y-%m-%d")
                result["end_date"] = result["start_date"]
            else:
                # dateparser也无法解析，使用原始描述
                result["start_date"] = time_description
                result["end_date"] = time_description
        except Exception:
            # 如果dateparser也解析失败，返回原始描述
            result["start_date"] = time_description
            result["end_date"] = time_description
    
    
    # 生成描述文本
    if result["start_date"] and result["end_date"]:
        if result["is_range"]:
            result["description"] = f"{result['start_date']} - {result['end_date']}"
        else:
            result["description"] = result["start_date"]
    else:
        result["description"] = result["original"]
    
    return result

if __name__ == "__main__":
    print(convert_date_format("昨天"))
    print(convert_date_format("去年"))
    print(convert_date_format("上个月"))
    print(convert_date_format("这个季度"))
    print(convert_date_format("从去年到今年"))
    print(convert_date_format("2024-02-03"))
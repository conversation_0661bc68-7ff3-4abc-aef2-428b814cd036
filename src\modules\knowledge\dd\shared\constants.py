"""
DD系统常量定义
"""

from typing import List, Dict, Any
from enum import Enum


class DDConstants:
    """DD系统常量"""
    
    # 向量化字段（写死配置）
    VECTORIZED_FIELDS = ["dr09", "dr17"]
    
    # 向量维度
    VECTOR_DIMENSION = 768
    
    # 数据层类型
    DATA_LAYERS = ["ADS", "BDM", "IDM", "ADM", "ODS", "范围"]
    
    # 报送模式
    REPORTING_MODES = ["全量", "增量", "变量"]
    
    # 报送频率
    REPORTING_FREQUENCIES = ["日报", "周报", "月报", "季报", "半年报", "年报", "Adhoc"]
    
    # 数据来源类型
    DATA_SOURCE_TYPES = ["1-System", "2-Manual", "3-External"]
    
    # 表更新模式
    TABLE_UPDATE_MODES = ["全量", "增量"]
    
    # 可信数据源标识
    TRUSTED_SOURCE_INDICATORS = ["1-可信", "2-授权", "3-其他", "4-NA"]
    
    # 数据类型
    DATA_TYPES = ["alphabetic", "numeric", "date", "boolean", "json"]
    
    # 主键/外键标识
    KEY_INDICATORS = ["主键", "外键", "普通字段"]
    
    # 部门类型
    DEPARTMENT_TYPES = ["normal", "mandatory", "management"]
    
    # 记录类型（填报类型）
    SUBMISSION_TYPES = ["RANGE", "SUBMISSION"]

    # 报表类型
    REPORT_TYPES = ["detail", "index"]


class DDFieldCategories:
    """DD字段分类"""
    
    # A类字段：结果数据需求 (DR01-DR22)
    A_FIELDS = [f"dr{i:02d}" for i in range(1, 23)]
    
    # B类字段：业务解读数据需求 (BDR01-BDR18)  
    B_FIELDS = [f"bdr{i:02d}" for i in range(1, 19)]
    
    # C类字段：IT解读业务数据需求 (SDR01-SDR15)
    C_FIELDS = [f"sdr{i:02d}" for i in range(1, 16)] + ["sdr03_5", "sdr08_5"]
    
    # D类字段：指标解读技术口径 (IDR01-IDR05)
    D_FIELDS = [f"idr{i:02d}" for i in range(1, 6)]
    
    # 所有DD字段
    ALL_DD_FIELDS = A_FIELDS + B_FIELDS + C_FIELDS + D_FIELDS


class DDTableNames:
    """DD表名常量"""

    # BIZ模块表名
    BIZ_DEPARTMENTS = "dd_departments"
    BIZ_DEPARTMENTS_RELATION = "dd_departments_relation"
    BIZ_PRE_DISTRIBUTION = "biz_dd_pre_distribution"
    BIZ_POST_DISTRIBUTION = "biz_dd_post_distribution"
    BIZ_DD_POST_DISTRIBUTION = "biz_dd_post_distribution"  # 别名，保持兼容

    # KB模块表名
    KB_FIELDS_METADATA = "dd_fields_metadata"
    KB_REPORT_DATA = "dd_report_data"
    KB_SUBMISSION_DATA = "dd_submission_data"

    # Vector模块表名
    VECTOR_EMBEDDINGS = "dd_embeddings"


class DDWorkflowStatus(Enum):
    """DD工作流状态"""
    
    DRAFT = "draft"                    # 草稿
    PRE_DISTRIBUTION = "pre_dist"      # 分发前
    POST_DISTRIBUTION = "post_dist"    # 分发后
    APPROVED = "approved"              # 审批通过
    PUBLISHED = "published"            # 已发布到知识库
    VECTORIZED = "vectorized"          # 已向量化


class DDErrorCodes:
    """DD错误代码"""
    
    # 通用错误
    VALIDATION_ERROR = "DD_VALIDATION_ERROR"
    NOT_FOUND_ERROR = "DD_NOT_FOUND_ERROR"
    DATABASE_ERROR = "DD_DATABASE_ERROR"
    
    # 业务错误
    WORKFLOW_ERROR = "DD_WORKFLOW_ERROR"
    APPROVAL_ERROR = "DD_APPROVAL_ERROR"
    VECTORIZATION_ERROR = "DD_VECTORIZATION_ERROR"
    
    # 数据错误
    DUPLICATE_SUBMISSION = "DD_DUPLICATE_SUBMISSION"
    INVALID_FIELD_VALUE = "DD_INVALID_FIELD_VALUE"
    MISSING_REQUIRED_FIELD = "DD_MISSING_REQUIRED_FIELD"

#!/usr/bin/env python3
"""
批量操作配置测试

测试BatchOperationConfig和性能监控装饰器
"""

import pytest
import os
import time
import asyncio
from unittest.mock import patch, AsyncMock

# 导入被测试的模块
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from modules.dd_submission.department_assignment.core.database_operations import (
    BatchOperationConfig, 
    monitor_batch_operation
)


class TestBatchOperationConfig:
    """批量操作配置测试"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = BatchOperationConfig()
        
        assert config.default_batch_size == 500
        assert config.max_concurrency == 3
        assert config.timeout_per_batch == 60.0
        assert config.fallback_batch_size == 100
        assert config.max_retry_attempts == 3
        assert config.retry_delay == 1.0
        assert config.enable_performance_logging == True
        assert config.slow_operation_threshold == 10.0
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = BatchOperationConfig(
            default_batch_size=1000,
            max_concurrency=5,
            timeout_per_batch=120.0,
            fallback_batch_size=200,
            enable_performance_logging=False
        )
        
        assert config.default_batch_size == 1000
        assert config.max_concurrency == 5
        assert config.timeout_per_batch == 120.0
        assert config.fallback_batch_size == 200
        assert config.enable_performance_logging == False
    
    @patch.dict(os.environ, {
        'BATCH_SIZE': '800',
        'MAX_CONCURRENCY': '4',
        'TIMEOUT_PER_BATCH': '90.0',
        'FALLBACK_BATCH_SIZE': '150',
        'MAX_RETRY_ATTEMPTS': '5',
        'RETRY_DELAY': '2.0',
        'ENABLE_PERF_LOGGING': 'false',
        'SLOW_OP_THRESHOLD': '15.0'
    })
    def test_config_from_env(self):
        """测试从环境变量加载配置"""
        config = BatchOperationConfig.from_env()
        
        assert config.default_batch_size == 800
        assert config.max_concurrency == 4
        assert config.timeout_per_batch == 90.0
        assert config.fallback_batch_size == 150
        assert config.max_retry_attempts == 5
        assert config.retry_delay == 2.0
        assert config.enable_performance_logging == False
        assert config.slow_operation_threshold == 15.0
    
    @patch.dict(os.environ, {}, clear=True)
    def test_config_from_env_defaults(self):
        """测试环境变量不存在时使用默认值"""
        config = BatchOperationConfig.from_env()
        
        # 应该使用默认值
        assert config.default_batch_size == 500
        assert config.max_concurrency == 3
        assert config.timeout_per_batch == 60.0
        assert config.enable_performance_logging == True


class TestMonitorBatchOperation:
    """性能监控装饰器测试"""
    
    @pytest.mark.asyncio
    async def test_monitor_successful_operation(self, caplog):
        """测试成功操作的监控"""
        
        @monitor_batch_operation("test_operation")
        async def test_func():
            await asyncio.sleep(0.1)  # 模拟操作耗时
            return 100  # 返回处理的记录数
        
        # 执行函数
        result = await test_func()
        
        # 验证返回值
        assert result == 100
        
        # 验证日志记录
        assert "批量操作成功: test_operation" in caplog.text
        assert "处理记录数: 100" in caplog.text
        assert "耗时:" in caplog.text
    
    @pytest.mark.asyncio
    async def test_monitor_failed_operation(self, caplog):
        """测试失败操作的监控"""
        
        @monitor_batch_operation("test_operation")
        async def test_func():
            await asyncio.sleep(0.1)
            raise ValueError("测试错误")
        
        # 执行函数并验证异常
        with pytest.raises(ValueError, match="测试错误"):
            await test_func()
        
        # 验证错误日志记录
        assert "批量操作失败: test_operation" in caplog.text
        assert "错误: 测试错误" in caplog.text
    
    @pytest.mark.asyncio
    async def test_monitor_slow_operation(self, caplog):
        """测试慢操作告警"""
        
        @monitor_batch_operation("slow_operation")
        async def slow_func():
            # 模拟慢操作（超过10秒阈值）
            # 这里我们通过mock time.time来模拟
            return 50
        
        # Mock time.time to simulate slow operation
        original_time = time.time
        call_count = 0
        
        def mock_time():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                return 1000.0  # 开始时间
            else:
                return 1015.0  # 结束时间（15秒后）
        
        with patch('time.time', side_effect=mock_time):
            result = await slow_func()
        
        # 验证返回值
        assert result == 50
        
        # 验证慢操作告警
        assert "慢操作检测: slow_operation 耗时 15.00秒" in caplog.text
    
    @pytest.mark.asyncio
    async def test_monitor_with_object_result(self, caplog):
        """测试返回对象的监控"""
        
        class MockResult:
            def __init__(self):
                self.count = 200
        
        @monitor_batch_operation("object_operation")
        async def test_func():
            return MockResult()
        
        # 执行函数
        result = await test_func()
        
        # 验证返回值
        assert result.count == 200
        
        # 验证日志记录
        assert "批量操作成功: object_operation" in caplog.text
        assert "处理记录数: 200" in caplog.text
    
    @pytest.mark.asyncio
    async def test_monitor_with_unknown_result(self, caplog):
        """测试未知结果类型的监控"""
        
        @monitor_batch_operation("unknown_operation")
        async def test_func():
            return {"some": "data"}  # 返回字典
        
        # 执行函数
        result = await test_func()
        
        # 验证返回值
        assert result == {"some": "data"}
        
        # 验证日志记录
        assert "批量操作成功: unknown_operation" in caplog.text
        assert "处理记录数: unknown" in caplog.text


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])

"""
DD系统请求模型

定义所有API端点的请求数据模型，用于输入验证和文档生成。
"""

from typing import Optional
from pydantic import BaseModel, Field
from .enums import (
    DataLayerType, DepartmentTypeEnum, SubmissionTypeEnum, ReportTypeEnum,
    ReportFrequencyEnum, SearchTypeEnum
)


# ==================== 部门管理请求模型 ====================

class DepartmentCreateRequest(BaseModel):
    """部门创建请求模型"""
    dept_id: str = Field(..., description="部门ID", example="TECH_DEPT_001")
    dept_name: str = Field(..., description="部门名称", example="技术部")
    dept_desc: Optional[str] = Field(None, description="部门描述", example="负责技术开发和维护")
    dept_type: DepartmentTypeEnum = Field(DepartmentTypeEnum.NORMAL, description="部门类型")
    is_active: bool = Field(True, description="是否激活")

    class Config:
        json_schema_extra = {
            "example": {
                "dept_id": "TECH_DEPT_001",
                "dept_name": "技术部",
                "dept_desc": "负责技术开发和维护",
                "dept_type": "normal",
                "is_active": True
            }
        }


class DepartmentUpdateRequest(BaseModel):
    """部门更新请求模型"""
    dept_name: Optional[str] = Field(None, description="部门名称", example="技术部")
    dept_desc: Optional[str] = Field(None, description="部门描述", example="负责技术开发、维护和创新")
    dept_type: Optional[DepartmentTypeEnum] = Field(None, description="部门类型")
    is_active: Optional[bool] = Field(None, description="是否激活")


# ==================== 填报数据请求模型 ====================

class SubmissionDataCreateRequest(BaseModel):
    """填报数据创建请求模型"""
    submission_id: str = Field(..., description="填报ID", example="SUB_001")
    version: str = Field(..., description="版本", example="2024-01-01")
    submission_type: SubmissionTypeEnum = Field(SubmissionTypeEnum.SUBMISSION, description="填报类型")
    report_type: ReportTypeEnum = Field(ReportTypeEnum.DETAIL, description="报表类型")
    set: Optional[str] = Field(None, description="套系信息", example="SET_001")
    dr01: str = Field(..., description="数据层", example="ADS")
    dr06: str = Field(..., description="表名", example="客户信息表")
    dr07: str = Field(..., description="表名ID", example="customer_info")
    dr09: str = Field(..., description="数据项名称", example="客户姓名")
    dr17: str = Field(..., description="需求口径", example="客户的真实姓名，不能为空")
    dr19: Optional[ReportFrequencyEnum] = Field(None, description="报送频率")
    dr22: Optional[str] = Field(None, description="责任部门", example="客户管理部")
    bdr01: Optional[str] = Field(None, description="业务部门", example="客户部")
    bdr02: Optional[str] = Field(None, description="责任人", example="张三")
    report_data_id: Optional[int] = Field(None, description="关联报表数据ID")

    class Config:
        json_schema_extra = {
            "example": {
                "submission_id": "SUB_001",
                "version": "2024-01-01",
                "submission_type": "SUBMISSION",
                "report_type": "detail",
                "set": "SET_001",
                "dr01": "ADS",
                "dr06": "客户信息表",
                "dr07": "customer_info",
                "dr09": "客户姓名",
                "dr17": "客户的真实姓名，不能为空，最大长度50字符",
                "dr19": "日报",
                "dr22": "客户管理部",
                "bdr01": "客户部",
                "bdr02": "张三"
            }
        }


class SubmissionDataUpdateRequest(BaseModel):
    """填报数据更新请求模型"""
    dr06: Optional[str] = Field(None, description="表名")
    dr07: Optional[str] = Field(None, description="表名ID")
    dr09: Optional[str] = Field(None, description="数据项名称")
    dr17: Optional[str] = Field(None, description="需求口径")
    dr19: Optional[ReportFrequencyEnum] = Field(None, description="报送频率")
    dr22: Optional[str] = Field(None, description="责任部门")
    bdr01: Optional[str] = Field(None, description="业务部门")
    bdr02: Optional[str] = Field(None, description="责任人")


# ==================== 搜索功能请求模型 ====================

class VectorSearchRequest(BaseModel):
    """向量搜索请求模型"""
    query: str = Field(..., description="搜索查询", example="客户姓名")
    knowledge_id: Optional[str] = Field(None, description="知识库ID")
    data_layer: Optional[str] = Field(None, description="数据层过滤", example="ADS")
    limit: int = Field(10, description="返回结果数量限制", ge=1, le=100)
    min_score: float = Field(0.5, description="最小相似度分数", ge=0.0, le=1.0)

    class Config:
        json_schema_extra = {
            "example": {
                "query": "客户姓名",
                "data_layer": "ADS",
                "limit": 10,
                "min_score": 0.5
            }
        }


class HybridSearchRequest(BaseModel):
    """混合搜索请求模型"""
    query: str = Field(..., description="搜索查询", example="客户信息")
    knowledge_id: Optional[str] = Field(None, description="知识库ID")
    data_layer: Optional[str] = Field(None, description="数据层过滤", example="ADS")
    limit: int = Field(10, description="返回结果数量限制", ge=1, le=100)
    min_score: float = Field(0.3, description="最小相似度分数", ge=0.0, le=1.0)
    vector_weight: float = Field(0.7, description="向量搜索权重", ge=0.0, le=1.0)
    text_weight: float = Field(0.3, description="文本搜索权重", ge=0.0, le=1.0)

    class Config:
        json_schema_extra = {
            "example": {
                "query": "客户信息",
                "data_layer": "ADS",
                "limit": 10,
                "min_score": 0.3,
                "vector_weight": 0.7,
                "text_weight": 0.3
            }
        }


# ==================== 分发数据请求模型 ====================

class PreDistributionCreateRequest(BaseModel):
    """分发前数据创建请求模型"""
    submission_id: str = Field(..., description="填报ID", example="SUB_PRE_001")
    version: str = Field(..., description="版本", example="2024-01-01")
    submission_type: SubmissionTypeEnum = Field(SubmissionTypeEnum.SUBMISSION, description="填报类型")
    report_type: ReportTypeEnum = Field(ReportTypeEnum.DETAIL, description="报表类型")
    set: Optional[str] = Field(None, description="套系信息", example="SET_PRE_001")
    dr01: str = Field(..., description="数据层", example="ADS")
    dr06: str = Field(..., description="表名", example="产品信息表")
    dr07: str = Field(..., description="表名ID", example="product_info")
    dr09: str = Field(..., description="数据项名称", example="产品名称")
    dr17: str = Field(..., description="需求口径", example="产品的标准名称，必须唯一")

    class Config:
        json_schema_extra = {
            "example": {
                "submission_id": "SUB_PRE_001",
                "version": "2024-01-01",
                "submission_type": "SUBMISSION",
                "report_type": "detail",
                "set": "SET_PRE_001",
                "dr01": "BDM",
                "dr06": "产品信息表",
                "dr07": "product_info",
                "dr09": "产品名称",
                "dr17": "产品的标准名称，必须唯一"
            }
        }


class PostDistributionCreateRequest(BaseModel):
    """分发后数据创建请求模型"""
    pre_distribution_id: int = Field(..., description="分发前数据ID")
    submission_id: str = Field(..., description="填报ID", example="SUB_POST_001")
    submission_type: SubmissionTypeEnum = Field(SubmissionTypeEnum.SUBMISSION, description="填报类型")
    report_type: ReportTypeEnum = Field(ReportTypeEnum.DETAIL, description="报表类型")
    set: Optional[str] = Field(None, description="套系信息", example="SET_POST_001")
    version: str = Field(..., description="版本", example="2024-01-01")
    dept_id: str = Field(..., description="部门ID", example="PRODUCT_DEPT")
    dr01: str = Field(..., description="数据层", example="ADS")
    dr07: str = Field(..., description="表名ID", example="product_info")
    dr22: str = Field(..., description="责任部门", example="产品管理部")
    bdr01: str = Field(..., description="业务部门", example="产品部")
    bdr02: str = Field(..., description="责任人", example="张三")

    class Config:
        json_schema_extra = {
            "example": {
                "pre_distribution_id": 1,
                "submission_id": "SUB_POST_001",
                "submission_type": "SUBMISSION",
                "report_type": "detail",
                "set": "SET_POST_001",
                "version": "2024-01-01",
                "dept_id": "PRODUCT_DEPT",
                "dr01": "BDM",
                "dr07": "product_info",
                "dr22": "产品管理部",
                "bdr01": "产品部",
                "bdr02": "张三"
            }
        }


# ==================== 报表数据请求模型 ====================

class ReportDataCreateRequest(BaseModel):
    """报表数据创建请求模型"""
    knowledge_id: str = Field(..., description="知识库ID", example="KB_001")
    version: str = Field(..., description="版本", example="2024-01-01")
    report_name: str = Field(..., description="报表名称", example="客户信息报表")
    report_code: str = Field(..., description="报表代码", example="RPT_CUSTOMER")
    report_layer: str = Field(..., description="报表层级", example="ADS")
    report_type: ReportTypeEnum = Field(ReportTypeEnum.DETAIL, description="报表类型")
    set: Optional[str] = Field(None, description="套系信息", example="SET_001")
    report_desc: Optional[str] = Field(None, description="报表描述")

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "KB_001",
                "version": "2024-01-01",
                "report_name": "客户信息报表",
                "report_code": "RPT_CUSTOMER",
                "report_layer": "BDM",
                "report_type": "detail",
                "set": "SET_001",
                "report_desc": "客户基础信息统计报表"
            }
        }


class ReportDataUpdateRequest(BaseModel):
    """报表数据更新请求模型"""
    report_name: Optional[str] = Field(None, description="报表名称")
    report_code: Optional[str] = Field(None, description="报表代码")
    report_layer: Optional[str] = Field(None, description="报表层级", example="ADS")
    report_type: Optional[ReportTypeEnum] = Field(None, description="报表类型")
    set: Optional[str] = Field(None, description="套系信息")
    report_desc: Optional[str] = Field(None, description="报表描述")

"""
DD系统搜索功能

基于标准三层搜索架构，提供多种搜索策略：
- search(): 基础搜索方法（包含精确搜索）
- hybrid_search(): 混合搜索方法（向量搜索 + 模糊搜索）
- 保持现有便捷方法的向后兼容性
"""

from typing import Any, Dict, List, Optional, Tuple, Union
import logging
from datetime import datetime
from enum import Enum

from .vector.repository import VectorRepository
from .shared.exceptions import DDError
from .shared.constants import DDConstants, DDTableNames
from .shared.utils import DDUtils

logger = logging.getLogger(__name__)


class SearchMode(Enum):
    """搜索模式"""
    EXACT = "exact"           # 精确搜索
    VECTOR = "vector"         # 向量搜索
    FUZZY = "fuzzy"          # 模糊搜索
    HYBRID = "hybrid"        # 混合搜索


class SearchField(Enum):
    """搜索字段"""
    DATA_ITEM_NAME = "dr09"      # 数据项名称
    REQUIREMENT_RULE = "dr17"    # 需求口径
    ALL_VECTORIZED = "all"       # 所有向量化字段


class DDSearch:
    """DD系统搜索类"""

    def __init__(self, rdb_client: Any, vdb_client: Any, embedding_client: Any = None):
        """
        初始化搜索功能

        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端
            embedding_client: 向量化模型客户端（可选）
        """
        # 直接使用数据库客户端，不需要适配器
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client

        # 创建向量仓储
        self.vector_repo = VectorRepository(vdb_client, embedding_client)

    # ==================== 标准搜索方法 ====================

    async def vector_hybrid_search(
        self,
        query: str,
        field_queries: Optional[Dict[str, str]] = None,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.5,
        rank_algorithm: str = "rrf",
        rank_config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        高级混合搜索方法 - 基于PGVector的hybrid_search实现

        支持多字段并行搜索和智能结果合并，解决语义匹配不准确的问题。

        Args:
            query: 主查询文本
            field_queries: 字段级查询 {"dr09": "query1", "dr17": "query2"}
            knowledge_id: 知识库ID（可选）
            data_layer: 数据层（可选）
            limit: 返回数量限制
            min_score: 最小相似度分数
            rank_algorithm: 排序算法 ("rrf", "weighted", "hybrid_weighted")
            rank_config: 排序配置
            **kwargs: 其他搜索参数

        Returns:
            搜索结果列表
        """
        try:
            logger.info(f"开始高级混合搜索: query='{query}', field_queries={field_queries}")

            # 如果没有提供字段查询，使用默认策略
            if not field_queries:
                field_queries = self._build_default_field_queries(query)

            # 构建多个搜索请求
            search_requests = await self._build_hybrid_search_requests(
                field_queries=field_queries,
                knowledge_id=knowledge_id,
                data_layer=data_layer,
                limit=limit * 2,  # 每个字段搜索更多结果用于合并
                min_score=min_score
            )

            if not search_requests:
                logger.warning("未能构建有效的搜索请求")
                return []

            # 创建排序器
            ranker = self._create_ranker(rank_algorithm, rank_config or {})

            # 执行混合搜索
            hybrid_results = await self.vdb_client.ahybrid_search(
                collection_name=DDTableNames.VECTOR_EMBEDDINGS,
                reqs=search_requests,
                ranker=ranker,
                limit=limit
            )

            # 转换结果格式并获取submission_data
            final_results = await self._process_hybrid_search_results(
                hybrid_results=hybrid_results,
                original_query=query,
                field_queries=field_queries
            )

            logger.info(f"高级混合搜索完成: 找到 {len(final_results)} 条结果")
            return final_results

        except Exception as e:
            logger.error(f"高级混合搜索失败: {e}")
            # 降级到标准搜索
            logger.info("降级到标准向量搜索")
            return await self._vector_search(
                query=query,
                field=SearchField.ALL_VECTORIZED,
                knowledge_id=knowledge_id,
                data_layer=data_layer,
                limit=limit,
                min_score=min_score
            )

    async def search(
        self,
        query: str,
        mode: Union[SearchMode, str] = SearchMode.HYBRID,
        field: Union[SearchField, str] = SearchField.ALL_VECTORIZED,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.5,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        基础搜索方法（标准搜索接口）

        Args:
            query: 查询文本
            mode: 搜索模式 (exact/vector/fuzzy/hybrid)
            field: 搜索字段 (dr09/dr17/all)
            knowledge_id: 知识库ID（可选）
            data_layer: 数据层（可选）
            limit: 返回数量限制
            min_score: 最小相似度分数
            **kwargs: 其他搜索参数

        Returns:
            搜索结果列表
        """
        # 标准化参数
        if isinstance(mode, str):
            mode = SearchMode(mode)
        if isinstance(field, str):
            field = SearchField(field)

        logger.info(f"开始搜索: query='{query}', mode={mode.value}, field={field.value}")

        try:
            # 根据搜索模式调用相应的搜索方法
            if mode == SearchMode.EXACT:
                return await self._exact_search(query, field, knowledge_id, data_layer, limit, **kwargs)
            elif mode == SearchMode.VECTOR:
                return await self._vector_search(query, field, knowledge_id, data_layer, limit, min_score, **kwargs)
            elif mode == SearchMode.FUZZY:
                return await self._fuzzy_search(query, field, knowledge_id, data_layer, limit, **kwargs)
            elif mode == SearchMode.HYBRID:
                return await self._hybrid_search(query, field, knowledge_id, data_layer, limit, min_score, **kwargs)
            else:
                raise DDError(f"不支持的搜索模式: {mode}")

        except Exception as e:
            logger.error(f"搜索失败: mode={mode.value}, error={e}")
            raise DDError(f"搜索失败: {e}")

    async def hybrid_search(
        self,
        query: str,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.3,
        vector_weight: float = 0.7,
        fuzzy_weight: float = 0.3,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        混合搜索方法（向量搜索 + 模糊搜索）

        Args:
            query: 查询文本
            knowledge_id: 知识库ID（可选）
            data_layer: 数据层（可选）
            limit: 返回数量限制
            min_score: 最小相似度分数
            vector_weight: 向量搜索权重
            fuzzy_weight: 模糊搜索权重
            **kwargs: 其他搜索参数

        Returns:
            搜索结果列表
        """
        return await self._hybrid_search(
            query=query,
            field=SearchField.ALL_VECTORIZED,
            knowledge_id=knowledge_id,
            data_layer=data_layer,
            limit=limit,
            min_score=min_score,
            vector_weight=vector_weight,
            fuzzy_weight=fuzzy_weight,
            **kwargs
        )

    # ==================== 高级混合搜索支持方法 ====================

    def _build_default_field_queries(self, query: str) -> Dict[str, str]:
        """
        构建默认的字段查询策略

        Args:
            query: 原始查询文本

        Returns:
            字段查询字典
        """
        # 清理查询文本
        cleaned_query = self._clean_query_text(query)

        # 分析查询内容，提取关键信息
        dr09_query, dr17_query = self._extract_field_specific_queries(cleaned_query)

        field_queries = {}
        if dr09_query:
            field_queries["dr09"] = dr09_query
        if dr17_query:
            field_queries["dr17"] = dr17_query

        # 如果没有字段特定查询，使用原始查询
        if not field_queries:
            field_queries["all"] = cleaned_query

        return field_queries

    def _clean_query_text(self, text: str) -> str:
        """清理查询文本，移除无用的描述前缀"""
        if not text:
            return ""

        # 移除常见的描述前缀
        prefixes_to_remove = [
            "维度描述：", "指标描述：", "数据项名称：", "需求口径：",
            "维度描述:", "指标描述:", "数据项名称:", "需求口径:"
        ]

        cleaned = text.strip()
        for prefix in prefixes_to_remove:
            if cleaned.startswith(prefix):
                cleaned = cleaned[len(prefix):].strip()

        # 移除多余的换行符和空格
        cleaned = " ".join(cleaned.split())

        return cleaned

    def _extract_field_specific_queries(self, query: str) -> Tuple[str, str]:
        """
        从查询中提取字段特定的查询内容

        Args:
            query: 清理后的查询文本

        Returns:
            (dr09_query, dr17_query) 元组
        """
        # 简单的启发式规则来分离dr09和dr17内容
        lines = query.split('\n')

        dr09_query = ""
        dr17_query = ""

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 如果包含特定的关键词，判断为dr09（数据项名称）
            if any(keyword in line for keyword in ["_", ".", "A", "B", "C", "逾期", "贷款", "余额"]):
                if not dr09_query:  # 只取第一个匹配的
                    dr09_query = line
            else:
                # 其他内容作为dr17（需求口径）
                if dr17_query:
                    dr17_query += " " + line
                else:
                    dr17_query = line

        # 如果没有明确分离，使用整个查询作为dr09
        if not dr09_query and not dr17_query:
            dr09_query = query

        return dr09_query.strip(), dr17_query.strip()

    async def _build_hybrid_search_requests(
        self,
        field_queries: Dict[str, str],
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 20,
        min_score: float = 0.5
    ) -> List[Any]:
        """
        构建混合搜索请求列表

        Args:
            field_queries: 字段查询字典
            knowledge_id: 知识库ID
            data_layer: 数据层
            limit: 每个请求的限制
            min_score: 最小分数

        Returns:
            SearchRequest列表
        """
        from base.db.base.vdb.core.models import SearchRequest

        search_requests = []

        for field, query_text in field_queries.items():
            if not query_text.strip():
                continue

            try:
                # 生成查询向量
                query_vector = await self.vector_repo.generate_embedding(query_text)

                # 构建过滤表达式
                filter_expr = self._build_filter_expression(knowledge_id, data_layer, field)

                # 创建搜索请求
                search_request = SearchRequest(
                    data=[query_vector],
                    anns_field="embedding",
                    param={
                        "metric_type": "COSINE",
                        "params": {"nprobe": 10}
                    },
                    limit=limit,
                    expr=filter_expr
                )

                search_requests.append(search_request)
                logger.debug(f"构建搜索请求: field={field}, query='{query_text[:50]}...'")

            except Exception as e:
                logger.error(f"构建搜索请求失败: field={field}, error={e}")
                continue

        return search_requests

    def _build_filter_expression(
        self,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        field: str = "all"
    ) -> str:
        """构建过滤表达式"""
        filters = []

        if knowledge_id:
            filters.append(f"knowledge_id == '{knowledge_id}'")

        if data_layer:
            filters.append(f"data_layer == '{data_layer}'")

        # 字段特定过滤（如果需要）
        if field == "dr09":
            # 可以添加dr09字段特定的过滤条件
            pass
        elif field == "dr17":
            # 可以添加dr17字段特定的过滤条件
            pass

        return " and ".join(filters) if filters else ""

    def _create_ranker(self, algorithm: str, config: Dict[str, Any]) -> Any:
        """
        创建排序器

        Args:
            algorithm: 排序算法名称
            config: 排序配置

        Returns:
            排序器实例
        """
        try:
            if algorithm == "rrf":
                from base.db.base.ranker.rrf_ranker import RRFRanker
                k = config.get("k", 60)
                return RRFRanker(k=k)
            elif algorithm == "weighted":
                from base.db.base.ranker.weighted_ranker import WeightedRanker
                weights = config.get("weights", {"dr09": 1.2, "dr17": 1.0, "all": 0.8})
                return WeightedRanker(weights=weights)
            else:
                # 默认使用RRF
                from base.db.base.ranker.rrf_ranker import RRFRanker
                return RRFRanker(k=60)
        except ImportError:
            logger.warning(f"无法导入排序器 {algorithm}，使用简单排序")
            return None

    async def _process_hybrid_search_results(
        self,
        hybrid_results: List[Any],
        original_query: str,
        field_queries: Dict[str, str]
    ) -> List[Dict[str, Any]]:
        """
        处理混合搜索结果，转换为标准格式

        Args:
            hybrid_results: 混合搜索原始结果
            original_query: 原始查询
            field_queries: 字段查询

        Returns:
            处理后的结果列表
        """
        if not hybrid_results:
            return []

        # 提取data_row_id列表
        data_row_ids = []
        result_map = {}

        for result in hybrid_results:
            if hasattr(result, 'entity') and hasattr(result.entity, 'get'):
                data_row_id = result.entity.get('data_row_id')
                if data_row_id:
                    data_row_ids.append(data_row_id)
                    # 正确的余弦相似度转换：cosine_similarity = 1 - cosine_distance
                    # 但需要确保在0-1范围内
                    cosine_similarity = max(0.0, min(1.0, 1.0 - result.distance))

                    result_map[data_row_id] = {
                        'data_row_id': data_row_id,
                        'score': cosine_similarity,  # 正确的0-1范围相似度分数
                        'distance': result.distance,
                        'search_strategy': 'vector_hybrid',
                        'field_queries': field_queries
                    }

        if not data_row_ids:
            return []

        # 批量查询submission_data
        try:
            from .crud import DDCrud
            dd_crud = DDCrud(rdb_client=self.rdb_client)

            submission_data_list = await dd_crud.batch_get_submission_data(data_row_ids)

            # 合并结果
            final_results = []
            for submission_data in submission_data_list:
                if submission_data and submission_data.get('id') in result_map:
                    result_info = result_map[submission_data['id']]
                    final_result = {
                        **submission_data,
                        **result_info
                    }
                    final_results.append(final_result)

            return final_results

        except Exception as e:
            logger.error(f"处理混合搜索结果失败: {e}")
            return []

    # ==================== 内部搜索实现方法 ====================

    async def _exact_search(
        self,
        query: str,
        field: SearchField,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """精确搜索实现 - 优化版本，修复SQL注入风险和N+1查询问题"""
        try:
            logger.info(f"执行精确搜索: query='{query}', field={field.value}")

            # 输入验证
            if not query or not query.strip():
                logger.warning("精确搜索查询为空")
                return []

            clean_query = query.strip()

            # 构建基础查询条件
            base_conditions = {}
            if data_layer:
                base_conditions['dr01'] = data_layer

            # 根据字段类型构建查询条件
            if field == SearchField.DATA_ITEM_NAME:
                base_conditions['dr09'] = clean_query
                results = await self._exact_search_single_field(base_conditions, "dr09", limit)
            elif field == SearchField.REQUIREMENT_RULE:
                base_conditions['dr17'] = clean_query
                results = await self._exact_search_single_field(base_conditions, "dr17", limit)
            elif field == SearchField.ALL_VECTORIZED:
                # 优化：使用单个OR查询替代N+1查询
                results = await self._exact_search_all_vectorized_optimized(
                    clean_query, base_conditions, knowledge_id, limit
                )
            else:
                logger.warning(f"不支持的搜索字段: {field}")
                return []

            # 应用knowledge_id过滤（如果提供）
            if knowledge_id and results:
                results = await self._filter_by_knowledge_id(results, knowledge_id)

            logger.info(f"精确搜索完成: 找到 {len(results)} 条结果")
            return results

        except Exception as e:
            logger.error(f"精确搜索失败: {e}")
            raise DDError(f"精确搜索失败: {e}")

    async def _exact_search_single_field(
        self,
        base_conditions: Dict[str, Any],
        matched_field: str,
        limit: int
    ) -> List[Dict[str, Any]]:
        """单字段精确搜索"""
        try:
            results = await self._aselect(
                table=DDTableNames.KB_SUBMISSION_DATA,
                where=base_conditions,
                limit=limit
            )

            # 添加搜索元数据
            for result in results:
                result["matched_field"] = matched_field
                result["search_type"] = "exact"
                result["score"] = 1.0

            return results

        except Exception as e:
            logger.error(f"单字段精确搜索失败: {e}")
            return []

    async def _exact_search_all_vectorized_optimized(
        self,
        query: str,
        base_conditions: Dict[str, Any],
        knowledge_id: Optional[str],
        limit: int
    ) -> List[Dict[str, Any]]:
        """优化的多字段精确搜索 - 使用单个OR查询"""
        try:
            # 字段安全验证
            ALLOWED_VECTORIZED_FIELDS = {'dr09', 'dr17'}  # 基于DDConstants.VECTORIZED_FIELDS
            safe_fields = [f for f in DDConstants.VECTORIZED_FIELDS if f in ALLOWED_VECTORIZED_FIELDS]

            if not safe_fields:
                logger.warning("没有安全的向量化字段可供搜索")
                return []

            # 尝试使用优化的OR查询
            try:
                return await self._execute_or_query_optimized(query, safe_fields, base_conditions, limit)
            except Exception as e:
                logger.warning(f"优化OR查询失败，降级到批量查询: {e}")
                return await self._exact_search_all_vectorized_fallback(query, safe_fields, base_conditions, limit)

        except Exception as e:
            logger.error(f"多字段精确搜索失败: {e}")
            return []

    async def _execute_or_query_optimized(
        self,
        query: str,
        fields: List[str],
        base_conditions: Dict[str, Any],
        limit: int
    ) -> List[Dict[str, Any]]:
        """执行优化的OR查询"""
        # 构建OR查询条件
        or_queries = []
        for field in fields:
            field_conditions = base_conditions.copy()
            field_conditions[field] = query
            or_queries.append({"data": ["*"], "filters": field_conditions, "limit": limit})

        # 使用abatch_query执行并行查询
        batch_results = await self.rdb_client.abatch_query(
            table=DDTableNames.KB_SUBMISSION_DATA,
            queries=or_queries,
            batch_size=len(or_queries),
            max_concurrency=3
        )

        # 合并结果并添加元数据
        all_results = []
        for i, query_response in enumerate(batch_results):
            if query_response.data:
                for result in query_response.data:
                    result["matched_field"] = fields[i]
                    result["search_type"] = "exact"
                    result["score"] = 1.0
                all_results.extend(query_response.data)

        # 去重并返回
        unique_results = self._deduplicate_results(all_results, "id")
        return unique_results[:limit]

    async def _exact_search_all_vectorized_fallback(
        self,
        query: str,
        fields: List[str],
        base_conditions: Dict[str, Any],
        limit: int
    ) -> List[Dict[str, Any]]:
        """多字段精确搜索的降级方案"""
        results = []
        for field_code in fields:
            try:
                field_conditions = base_conditions.copy()
                field_conditions[field_code] = query

                field_results = await self._aselect(
                    table=DDTableNames.KB_SUBMISSION_DATA,
                    where=field_conditions,
                    limit=limit
                )

                for result in field_results:
                    result["matched_field"] = field_code
                    result["search_type"] = "exact"
                    result["score"] = 1.0

                results.extend(field_results)

            except Exception as e:
                logger.warning(f"字段 {field_code} 精确搜索失败: {e}")
                continue

        # 去重并返回
        unique_results = self._deduplicate_results(results, "id")
        return unique_results[:limit]

    async def _filter_by_knowledge_id(
        self,
        results: List[Dict[str, Any]],
        knowledge_id: str
    ) -> List[Dict[str, Any]]:
        """根据knowledge_id过滤搜索结果"""
        if not results or not knowledge_id:
            return results

        try:
            # 提取所有结果的ID
            submission_ids = [result.get('id') for result in results if result.get('id')]
            if not submission_ids:
                return results

            # 批量查询report_data_id
            queries = [
                {"data": ["id", "report_data_id"], "filters": {"id": sid}, "limit": 1}
                for sid in submission_ids
            ]

            batch_results = await self.rdb_client.abatch_query(
                table=DDTableNames.KB_SUBMISSION_DATA,
                queries=queries,
                batch_size=50,
                max_concurrency=3
            )

            # 收集有效的report_data_id
            valid_report_ids = set()
            submission_to_report = {}

            for i, query_response in enumerate(batch_results):
                if query_response.data and len(query_response.data) > 0:
                    record = query_response.data[0]
                    report_id = record.get('report_data_id')
                    if report_id:
                        valid_report_ids.add(report_id)
                        submission_to_report[submission_ids[i]] = report_id

            if not valid_report_ids:
                return []

            # 批量查询knowledge_id匹配的report_data
            report_queries = [
                {"data": ["id"], "filters": {"id": rid, "knowledge_id": knowledge_id}, "limit": 1}
                for rid in valid_report_ids
            ]

            report_batch_results = await self.rdb_client.abatch_query(
                table=DDTableNames.KB_REPORT_DATA,
                queries=report_queries,
                batch_size=50,
                max_concurrency=3
            )

            # 找出匹配knowledge_id的report_id
            matched_report_ids = set()
            for i, query_response in enumerate(report_batch_results):
                if query_response.data and len(query_response.data) > 0:
                    matched_report_ids.add(list(valid_report_ids)[i])

            # 过滤原始结果
            filtered_results = []
            for result in results:
                submission_id = result.get('id')
                if submission_id in submission_to_report:
                    report_id = submission_to_report[submission_id]
                    if report_id in matched_report_ids:
                        filtered_results.append(result)

            logger.debug(f"knowledge_id过滤: 原始{len(results)}条 -> 过滤后{len(filtered_results)}条")
            return filtered_results

        except Exception as e:
            logger.warning(f"knowledge_id过滤失败，返回原始结果: {e}")
            return results

    async def _vector_search(
        self,
        query: str,
        field: SearchField,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.5,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """向量搜索实现 - 优化版本，支持批量并行搜索"""
        try:
            logger.info(f"执行向量搜索: query='{query}', field={field.value}")

            # 输入验证
            if not query or len(query.strip()) < 2:
                logger.warning("向量搜索查询过短或为空")
                return []

            clean_query = query.strip()

            # 确定要搜索的字段
            search_fields = []
            if field == SearchField.DATA_ITEM_NAME:
                search_fields = ["dr09"]
            elif field == SearchField.REQUIREMENT_RULE:
                search_fields = ["dr17"]
            elif field == SearchField.ALL_VECTORIZED:
                search_fields = DDConstants.VECTORIZED_FIELDS
            else:
                logger.warning(f"不支持的向量搜索字段: {field}")
                return []

            # 使用批量并行搜索优化
            if len(search_fields) > 1:
                all_results = await self._vector_search_batch_parallel(
                    clean_query, search_fields, knowledge_id, data_layer, limit, min_score
                )
            else:
                # 单字段搜索
                all_results = await self._vector_search_single_field(
                    clean_query, search_fields[0], knowledge_id, data_layer, limit, min_score
                )

            # 按相似度分数排序并去重
            unique_results = self._deduplicate_results(all_results, "data_row_id")
            sorted_results = sorted(unique_results, key=lambda x: x.get("score", 0), reverse=True)

            # 获取完整的填报数据（使用已优化的批量查询）
            enriched_results = await self._enrich_with_submission_data_optimized(sorted_results[:limit])

            logger.info(f"向量搜索完成: 找到 {len(enriched_results)} 条结果")
            return enriched_results

        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            raise DDError(f"向量搜索失败: {e}")



    async def _vector_search_single_field(
        self,
        query: str,
        field_code: str,
        knowledge_id: Optional[str],
        data_layer: Optional[str],
        limit: int,
        min_score: float
    ) -> List[Dict[str, Any]]:
        """单字段向量搜索"""
        try:
            field_results = await self.vector_repo.search_similar_vectors(
                query_text=query,
                field_code=field_code,
                knowledge_id=knowledge_id,
                data_layer=data_layer,
                limit=limit,
                min_score=min_score
            )

            # 添加字段信息
            for result in field_results:
                result["matched_field"] = field_code
                result["search_type"] = "vector"

            return field_results

        except Exception as e:
            logger.warning(f"单字段向量搜索失败: field={field_code}, error={e}")
            return []

    async def _vector_search_batch_parallel(
        self,
        query: str,
        search_fields: List[str],
        knowledge_id: Optional[str],
        data_layer: Optional[str],
        limit: int,
        min_score: float
    ) -> List[Dict[str, Any]]:
        """批量并行向量搜索"""
        try:
            import asyncio

            # 创建并行搜索任务
            search_tasks = []
            for field_code in search_fields:
                task = self._vector_search_single_field_task(
                    query, field_code, knowledge_id, data_layer,
                    limit, min_score
                )
                search_tasks.append(task)

            # 并行执行所有搜索任务
            field_results_list = await asyncio.gather(*search_tasks, return_exceptions=True)

            # 合并结果
            all_results = []
            for i, field_results in enumerate(field_results_list):
                if isinstance(field_results, Exception):
                    logger.warning(f"字段 {search_fields[i]} 向量搜索失败: {field_results}")
                    continue

                if isinstance(field_results, list):
                    all_results.extend(field_results)

            logger.debug(f"批量并行向量搜索完成: 搜索{len(search_fields)}个字段, 总结果{len(all_results)}条")
            return all_results

        except Exception as e:
            logger.warning(f"批量并行向量搜索失败，降级到逐个搜索: {e}")
            return await self._vector_search_sequential_fallback(
                query, search_fields, knowledge_id, data_layer, limit, min_score
            )

    async def _vector_search_single_field_task(
        self,
        query: str,
        field_code: str,
        knowledge_id: Optional[str],
        data_layer: Optional[str],
        limit: int,
        min_score: float
    ) -> List[Dict[str, Any]]:
        """单个向量搜索任务（用于并行执行）"""
        try:
            field_results = await self.vector_repo.search_similar_vectors(
                query_text=query,
                field_code=field_code,
                knowledge_id=knowledge_id,
                data_layer=data_layer,
                limit=limit,
                min_score=min_score
            )

            # 添加字段信息
            for result in field_results:
                result["matched_field"] = field_code
                result["search_type"] = "vector_parallel"

            return field_results

        except Exception as e:
            logger.warning(f"向量搜索任务失败: field={field_code}, error={e}")
            return []

    async def _vector_search_sequential_fallback(
        self,
        query: str,
        search_fields: List[str],
        knowledge_id: Optional[str],
        data_layer: Optional[str],
        limit: int,
        min_score: float
    ) -> List[Dict[str, Any]]:
        """向量搜索的降级方案（逐个搜索）"""
        all_results = []

        for field_code in search_fields:
            try:
                field_results = await self.vector_repo.search_similar_vectors(
                    query_text=query,
                    field_code=field_code,
                    knowledge_id=knowledge_id,
                    data_layer=data_layer,
                    limit=limit,
                    min_score=min_score
                )

                # 添加字段信息
                for result in field_results:
                    result["matched_field"] = field_code
                    result["search_type"] = "vector_fallback"

                all_results.extend(field_results)

            except Exception as e:
                logger.warning(f"字段 {field_code} 向量搜索失败: {e}")
                continue

        return all_results

    async def _enrich_with_submission_data_optimized(self, vector_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """优化的向量搜索结果丰富化 - 专门为向量搜索优化"""
        if not vector_results:
            return []

        try:
            # 向量搜索结果使用data_row_id作为关联字段
            data_row_ids = []
            for result in vector_results:
                data_row_id = result.get("data_row_id")
                if data_row_id:
                    data_row_ids.append(data_row_id)

            if not data_row_ids:
                logger.warning("向量搜索结果中没有有效的data_row_id")
                return vector_results

            # 使用abatch_query批量查询填报数据
            queries = [
                {"data": ["*"], "filters": {"id": data_row_id}, "limit": 1}
                for data_row_id in data_row_ids
            ]

            batch_results = await self.rdb_client.abatch_query(
                table=DDTableNames.KB_SUBMISSION_DATA,
                queries=queries,
                batch_size=100,
                max_concurrency=5
            )

            # 构建data_row_id到submission_data的映射
            data_row_id_to_submission = {}
            for i, query_response in enumerate(batch_results):
                if query_response.data and len(query_response.data) > 0:
                    data_row_id_to_submission[data_row_ids[i]] = query_response.data[0]

            # 丰富化向量搜索结果
            enriched_results = []
            for result in vector_results:
                data_row_id = result.get("data_row_id")
                if data_row_id and data_row_id in data_row_id_to_submission:
                    # 合并向量结果和填报数据
                    enriched_result = {
                        **result,
                        "submission_data": data_row_id_to_submission[data_row_id]
                    }
                    enriched_results.append(enriched_result)
                else:
                    # 保留原始向量结果，即使没有找到对应的填报数据
                    enriched_results.append(result)

            logger.debug(f"向量搜索结果丰富化完成: 查询{len(data_row_ids)}个data_row_id, 成功{len(data_row_id_to_submission)}个")
            return enriched_results

        except Exception as e:
            logger.warning(f"向量搜索结果丰富化失败，返回原始结果: {e}")
            return vector_results

    async def _fuzzy_search(
        self,
        query: str,
        field: SearchField,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """模糊搜索实现 - 优化版本，修复N+1查询问题"""
        try:
            logger.info(f"执行模糊搜索: query='{query}', field={field.value}")

            # 输入验证
            if not query or len(query.strip()) < 2:
                logger.warning("模糊搜索查询过短或为空")
                return []

            clean_query = query.strip()

            # 构建基础查询条件
            base_conditions = {}
            if data_layer:
                base_conditions['dr01'] = data_layer

            # 智能选择搜索策略
            search_strategy = self._determine_fuzzy_search_strategy(clean_query)

            # 根据字段类型构建查询
            if field == SearchField.DATA_ITEM_NAME:
                results = await self._fuzzy_search_field_optimized("dr09", clean_query, base_conditions, limit, search_strategy)
            elif field == SearchField.REQUIREMENT_RULE:
                results = await self._fuzzy_search_field_optimized("dr17", clean_query, base_conditions, limit, search_strategy)
            elif field == SearchField.ALL_VECTORIZED:
                # 优化：使用批量查询替代循环查询
                results = await self._fuzzy_search_all_vectorized_optimized(
                    clean_query, base_conditions, limit, search_strategy
                )
            else:
                logger.warning(f"不支持的模糊搜索字段: {field}")
                return []

            # 添加搜索元数据
            for result in results:
                if "search_type" not in result:
                    result["search_type"] = "fuzzy"
                if "score" not in result:
                    result["score"] = 0.8  # 模糊搜索默认分数

            # 应用knowledge_id过滤（如果提供）
            if knowledge_id and results:
                results = await self._filter_by_knowledge_id(results, knowledge_id)

            logger.info(f"模糊搜索完成: 找到 {len(results)} 条结果")
            return results[:limit]

        except Exception as e:
            logger.error(f"模糊搜索失败: {e}")
            raise DDError(f"模糊搜索失败: {e}")

    async def _fuzzy_search_all_vectorized_optimized(
        self,
        query: str,
        base_conditions: Dict[str, Any],
        limit: int,
        strategy: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """优化的多字段模糊搜索"""
        try:
            # 字段安全验证
            ALLOWED_VECTORIZED_FIELDS = {'dr09', 'dr17'}
            safe_fields = [f for f in DDConstants.VECTORIZED_FIELDS if f in ALLOWED_VECTORIZED_FIELDS]

            if not safe_fields:
                logger.warning("没有安全的向量化字段可供模糊搜索")
                return []

            # 使用批量查询优化
            try:
                return await self._fuzzy_search_batch_optimized(query, safe_fields, base_conditions, limit, strategy)
            except Exception as e:
                logger.warning(f"批量模糊搜索失败，降级到逐个查询: {e}")
                return await self._fuzzy_search_all_vectorized_fallback(query, safe_fields, base_conditions, limit, strategy)

        except Exception as e:
            logger.error(f"多字段模糊搜索失败: {e}")
            return []

    async def _fuzzy_search_batch_optimized(
        self,
        query: str,
        fields: List[str],
        base_conditions: Dict[str, Any],
        limit: int,
        strategy: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """批量模糊搜索优化实现"""
        # 如果支持全文搜索，尝试使用全文搜索
        if strategy.get("use_fulltext"):
            try:
                return await self._fuzzy_search_batch_fulltext(query, fields, base_conditions, limit)
            except Exception as e:
                logger.warning(f"批量全文搜索失败，降级到LIKE搜索: {e}")

        # 使用优化的LIKE批量搜索
        pattern = strategy["pattern"]
        queries = []

        for field in fields:
            field_conditions = base_conditions.copy()
            field_conditions[field] = {"$like": pattern}

            # 添加排序条件
            query_def = {
                "data": ["*"],
                "filters": field_conditions,
                "limit": limit
            }

            # 前缀匹配时添加排序
            if strategy.get("use_prefix"):
                query_def["sorts"] = [
                    {"field": f"CASE WHEN `{field}` LIKE '{pattern}' THEN 0 ELSE 1 END", "order": "asc"},
                    {"field": "id", "order": "desc"}
                ]
            else:
                query_def["sorts"] = [{"field": "id", "order": "desc"}]

            queries.append(query_def)

        # 执行批量查询
        batch_results = await self.rdb_client.abatch_query(
            table=DDTableNames.KB_SUBMISSION_DATA,
            queries=queries,
            batch_size=len(queries),
            max_concurrency=3
        )

        # 合并结果并添加元数据
        all_results = []
        for i, query_response in enumerate(batch_results):
            if query_response.data:
                for result in query_response.data:
                    result["matched_field"] = fields[i]
                    result["search_type"] = "fuzzy_batch"

                    # 相关性评分
                    if strategy.get("use_prefix"):
                        field_value = str(result.get(fields[i], ""))
                        if field_value.lower().startswith(query.lower()):
                            result["score"] = 0.9
                        else:
                            result["score"] = 0.7
                    else:
                        result["score"] = 0.8

                all_results.extend(query_response.data)

        # 去重、排序并返回
        unique_results = self._deduplicate_results(all_results, "id")
        # 按相关性排序
        sorted_results = sorted(unique_results, key=lambda x: x.get("score", 0), reverse=True)
        return sorted_results[:limit]

    async def _fuzzy_search_batch_fulltext(
        self,
        query: str,
        fields: List[str],
        base_conditions: Dict[str, Any],
        limit: int
    ) -> List[Dict[str, Any]]:
        """批量全文搜索实现"""
        all_results = []

        # 对每个字段执行全文搜索
        for field in fields:
            try:
                field_results = await self._fuzzy_search_fulltext(field, query, base_conditions, limit)
                all_results.extend(field_results)
            except Exception as e:
                logger.warning(f"字段 {field} 全文搜索失败: {e}")
                continue

        # 去重、排序并返回
        unique_results = self._deduplicate_results(all_results, "id")
        # 按相关性排序
        sorted_results = sorted(unique_results, key=lambda x: x.get("score", 0), reverse=True)
        return sorted_results[:limit]

    async def _fuzzy_search_all_vectorized_fallback(
        self,
        query: str,
        fields: List[str],
        base_conditions: Dict[str, Any],
        limit: int,
        strategy: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """多字段模糊搜索的降级方案"""
        all_results = []
        pattern = strategy["pattern"]

        for field_code in fields:
            try:
                field_results = await self._fuzzy_search_field(field_code, pattern, base_conditions, limit)
                all_results.extend(field_results)
            except Exception as e:
                logger.warning(f"字段 {field_code} 模糊搜索失败: {e}")
                continue

        # 去重并返回
        unique_results = self._deduplicate_results(all_results, "id")
        return unique_results[:limit]

    async def _hybrid_search(
        self,
        query: str,
        field: SearchField,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.3,
        vector_weight: float = 0.7,
        fuzzy_weight: float = 0.3,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """混合搜索实现（向量搜索 + 模糊搜索）"""
        try:
            logger.info(f"执行混合搜索: query='{query}', field={field.value}")

            # 1. 向量搜索
            vector_results = await self._vector_search(
                query=query,
                field=field,
                knowledge_id=knowledge_id,
                data_layer=data_layer,
                limit=limit * 2,  # 获取更多结果用于混合
                min_score=min_score
            )

            # 2. 模糊搜索
            fuzzy_results = await self._fuzzy_search(
                query=query,
                field=field,
                knowledge_id=knowledge_id,
                data_layer=data_layer,
                limit=limit * 2
            )

            # 3. 合并和重新评分
            combined_results = self._combine_search_results(
                vector_results, fuzzy_results, vector_weight, fuzzy_weight
            )

            # 4. 排序并返回
            sorted_results = sorted(combined_results, key=lambda x: x.get("final_score", 0), reverse=True)

            logger.info(f"混合搜索完成: 向量结果={len(vector_results)}, 模糊结果={len(fuzzy_results)}, 最终结果={len(sorted_results[:limit])}")
            return sorted_results[:limit]

        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            raise DDError(f"混合搜索失败: {e}")
    
    # ==================== 模糊搜索优化方法 ====================

    def _determine_fuzzy_search_strategy(self, query: str) -> Dict[str, Any]:
        """智能确定模糊搜索策略"""
        strategy = {
            "use_fulltext": False,
            "use_prefix": False,
            "pattern": f"%{query}%",
            "relevance_scoring": False
        }

        # 检查是否支持全文搜索
        try:
            if hasattr(self.rdb_client, 'dialect') and self.rdb_client.dialect:
                features = getattr(self.rdb_client.dialect, 'features', None)
                if features and hasattr(features, 'full_text_search'):
                    from base.db.implementations.rdb.sqlalchemy.universal.dialects.base import FeatureSupport
                    if features.full_text_search == FeatureSupport.FULL:
                        # 全文搜索条件：查询长度>=3且包含中文或英文单词
                        if len(query) >= 3 and (query.isalnum() or self._contains_chinese_or_english_words(query)):
                            strategy["use_fulltext"] = True
                            strategy["relevance_scoring"] = True
        except Exception as e:
            logger.debug(f"检测全文搜索支持失败: {e}")

        # 前缀匹配优化：查询长度>=3且为字母数字
        if not strategy["use_fulltext"] and len(query) >= 3 and query.isalnum():
            strategy["use_prefix"] = True
            strategy["pattern"] = f"{query}%"

        logger.debug(f"模糊搜索策略: {strategy}")
        return strategy

    def _contains_chinese_or_english_words(self, text: str) -> bool:
        """检查文本是否包含中文或英文单词"""
        import re
        # 检查中文字符
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        if chinese_pattern.search(text):
            return True

        # 检查英文单词（至少3个字母）
        english_pattern = re.compile(r'[a-zA-Z]{3,}')
        if english_pattern.search(text):
            return True

        return False

    async def _fuzzy_search_field_optimized(
        self,
        field_name: str,
        query: str,
        base_conditions: Dict[str, Any],
        limit: int,
        strategy: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """优化的单字段模糊搜索"""
        try:
            # 字段名安全验证
            ALLOWED_FIELDS = {
                'dr01', 'dr09', 'dr17', 'dr18', 'dr19', 'dr20', 'dr21', 'dr22',
                'id', 'submission_id', 'report_data_id', 'create_time', 'update_time'
            }

            if field_name not in ALLOWED_FIELDS:
                logger.warning(f"不安全的字段名: {field_name}")
                return []

            # 策略1: 尝试全文搜索（如果支持且适用）
            if strategy["use_fulltext"]:
                try:
                    return await self._fuzzy_search_fulltext(field_name, query, base_conditions, limit)
                except Exception as e:
                    logger.warning(f"全文搜索失败，降级到LIKE搜索: {e}")

            # 策略2: 优化的LIKE搜索
            return await self._fuzzy_search_like_optimized(field_name, strategy["pattern"], base_conditions, limit, strategy)

        except Exception as e:
            logger.error(f"优化模糊搜索失败: {e}")
            return []

    async def _fuzzy_search_fulltext(
        self,
        field_name: str,
        query: str,
        base_conditions: Dict[str, Any],
        limit: int
    ) -> List[Dict[str, Any]]:
        """全文搜索实现"""
        try:
            # 检测数据库类型并构建全文搜索查询
            db_type = self.rdb_client.get_database_type()

            if str(db_type).lower() == 'mysql':
                return await self._fuzzy_search_mysql_fulltext(field_name, query, base_conditions, limit)
            elif str(db_type).lower() == 'postgresql':
                return await self._fuzzy_search_postgresql_fulltext(field_name, query, base_conditions, limit)
            else:
                # 不支持的数据库类型，降级到LIKE搜索
                raise Exception(f"数据库类型 {db_type} 不支持全文搜索")

        except Exception as e:
            logger.warning(f"全文搜索实现失败: {e}")
            raise

    async def _fuzzy_search_mysql_fulltext(
        self,
        field_name: str,
        query: str,
        base_conditions: Dict[str, Any],
        limit: int
    ) -> List[Dict[str, Any]]:
        """MySQL全文搜索实现"""
        try:
            # 构建基础条件
            base_conditions_sql = []
            params = []

            for key, value in base_conditions.items():
                if key in {'dr01', 'dr09', 'dr17', 'dr18', 'dr19', 'dr20', 'dr21', 'dr22', 'id', 'submission_id'}:
                    base_conditions_sql.append(f"`{key}` = %s")
                    params.append(value)

            # 构建全文搜索SQL
            where_clause = ""
            if base_conditions_sql:
                where_clause = f"({' AND '.join(base_conditions_sql)}) AND "

            # MySQL FULLTEXT搜索（需要FULLTEXT索引）
            where_clause += f"MATCH(`{field_name}`) AGAINST(%s IN NATURAL LANGUAGE MODE)"
            params.append(query)

            sql = f"""
                SELECT *,
                       MATCH(`{field_name}`) AGAINST(%s IN NATURAL LANGUAGE MODE) as relevance_score,
                       '{field_name}' as matched_field
                FROM `{DDTableNames.KB_SUBMISSION_DATA}`
                WHERE {where_clause}
                ORDER BY relevance_score DESC, `id` DESC
                LIMIT %s
            """
            params.append(query)  # 用于SELECT中的MATCH
            params.append(limit)

            # 执行查询
            if hasattr(self.rdb_client, 'aexecute'):
                result = await self.rdb_client.aexecute(sql, params)
                results = result.data if hasattr(result, 'data') else []
            else:
                raise Exception("客户端不支持原生SQL执行")

            # 添加搜索元数据
            for result in results:
                if isinstance(result, dict):
                    result["search_type"] = "fulltext"
                    result["score"] = result.get("relevance_score", 0.9)

            logger.debug(f"MySQL全文搜索完成: 找到{len(results)}条结果")
            return results

        except Exception as e:
            logger.warning(f"MySQL全文搜索失败: {e}")
            raise

    async def _fuzzy_search_like_optimized(
        self,
        field_name: str,
        pattern: str,
        base_conditions: Dict[str, Any],
        limit: int,
        strategy: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """优化的LIKE搜索实现"""
        try:
            # 方案1: 尝试使用标准查询接口
            where_conditions = base_conditions.copy()
            where_conditions[field_name] = {"$like": pattern}

            # 构建排序条件（前缀匹配优先）
            order_by = None
            if strategy.get("use_prefix"):
                # 前缀匹配时，按匹配位置排序
                order_by = [f"CASE WHEN `{field_name}` LIKE '{pattern}' THEN 0 ELSE 1 END", "id DESC"]
            else:
                order_by = ["id DESC"]

            results = await self._aselect(
                table=DDTableNames.KB_SUBMISSION_DATA,
                where=where_conditions,
                order_by=order_by,
                limit=limit
            )

            # 添加搜索元数据和相关性评分
            for result in results:
                result["matched_field"] = field_name
                result["search_type"] = "like_optimized"

                # 简单的相关性评分
                if strategy.get("use_prefix"):
                    field_value = str(result.get(field_name, ""))
                    if field_value.lower().startswith(pattern.rstrip('%').lower()):
                        result["score"] = 0.9  # 前缀匹配高分
                    else:
                        result["score"] = 0.7  # 包含匹配中等分
                else:
                    result["score"] = 0.8  # 默认分数

            return results

        except Exception as e:
            logger.warning(f"优化LIKE搜索失败，尝试降级方案: {e}")
            # 降级到原有的安全SQL方案
            return await self._fuzzy_search_field_raw_sql_safe(field_name, pattern, base_conditions, limit)

    # ==================== 辅助方法 ====================

    async def _fuzzy_search_field(
        self,
        field_name: str,
        pattern: str,
        base_conditions: Dict[str, Any],
        limit: int
    ) -> List[Dict[str, Any]]:
        """对单个字段执行模糊搜索 - 修复SQL注入风险"""
        try:
            # 字段名安全验证
            ALLOWED_FIELDS = {
                'dr01', 'dr09', 'dr17', 'dr18', 'dr19', 'dr20', 'dr21', 'dr22',
                'id', 'submission_id', 'report_data_id', 'create_time', 'update_time'
            }

            if field_name not in ALLOWED_FIELDS:
                logger.warning(f"不安全的字段名: {field_name}")
                return []

            # 使用安全的查询方式
            try:
                # 方案1: 尝试使用标准查询接口
                where_conditions = base_conditions.copy()
                where_conditions[field_name] = {"$like": pattern}

                results = await self._aselect(
                    table=DDTableNames.KB_SUBMISSION_DATA,
                    where=where_conditions,
                    limit=limit
                )

                # 添加匹配字段信息
                for result in results:
                    result["matched_field"] = field_name

                return results

            except Exception as e:
                logger.warning(f"标准查询接口失败，尝试降级方案: {e}")
                # 方案2: 降级到安全的原生SQL
                return await self._fuzzy_search_field_raw_sql_safe(
                    field_name, pattern, base_conditions, limit
                )

        except Exception as e:
            logger.error(f"模糊搜索字段失败: {e}")
            return []

    async def _fuzzy_search_field_raw_sql_safe(
        self,
        field_name: str,
        pattern: str,
        base_conditions: Dict[str, Any],
        limit: int
    ) -> List[Dict[str, Any]]:
        """安全的原生SQL模糊搜索降级方案"""
        try:
            # 再次验证字段名（双重保护）
            ALLOWED_FIELDS = {
                'dr01', 'dr09', 'dr17', 'dr18', 'dr19', 'dr20', 'dr21', 'dr22',
                'id', 'submission_id', 'report_data_id', 'create_time', 'update_time'
            }

            if field_name not in ALLOWED_FIELDS:
                logger.error(f"字段名验证失败: {field_name}")
                return []

            # 构建安全的SQL查询
            sql_conditions = []
            params = []

            # 添加基础条件（安全验证）
            for key, value in base_conditions.items():
                if key in ALLOWED_FIELDS:
                    sql_conditions.append(f"`{key}` = %s")
                    params.append(value)
                else:
                    logger.warning(f"跳过不安全的基础条件字段: {key}")

            # 添加模糊查询条件
            sql_conditions.append(f"`{field_name}` LIKE %s")
            params.append(pattern)

            # 构建完整SQL
            where_clause = " AND ".join(sql_conditions) if sql_conditions else "1=1"
            sql = f"""
                SELECT * FROM `{DDTableNames.KB_SUBMISSION_DATA}`
                WHERE {where_clause}
                ORDER BY `id` DESC
                LIMIT %s
            """
            params.append(limit)

            # 执行查询
            try:
                if hasattr(self.rdb_client, 'aexecute'):
                    result = await self.rdb_client.aexecute(sql, params)
                    results = result.data if hasattr(result, 'data') else []
                elif hasattr(self.rdb_client, 'execute_raw_sql'):
                    results = await self.rdb_client.execute_raw_sql(sql, params)
                    results = results if isinstance(results, list) else []
                else:
                    logger.warning("客户端不支持原生SQL查询")
                    results = []
            except Exception as e:
                logger.error(f"原生SQL查询失败: {e}")
                results = []

            # 添加匹配字段信息
            for result in results:
                if isinstance(result, dict):
                    result["matched_field"] = field_name

            return results

        except Exception as e:
            logger.error(f"安全原生SQL模糊搜索失败: {e}")
            return []
            logger.error(f"模糊搜索字段失败: field={field_name}, error={e}")
            return []

    def _combine_search_results(
        self,
        vector_results: List[Dict[str, Any]],
        fuzzy_results: List[Dict[str, Any]],
        vector_weight: float,
        fuzzy_weight: float
    ) -> List[Dict[str, Any]]:
        """合并向量搜索和模糊搜索结果"""
        # 创建结果字典，以id为键
        combined = {}

        # 处理向量搜索结果
        for result in vector_results:
            result_id = result.get("id") or result.get("data_row_id")
            if result_id:
                result["vector_score"] = result.get("score", 0)
                result["fuzzy_score"] = 0
                result["final_score"] = result["vector_score"] * vector_weight
                result["search_type"] = "vector"
                combined[result_id] = result

        # 处理模糊搜索结果
        for result in fuzzy_results:
            result_id = result.get("id")
            if result_id:
                if result_id in combined:
                    # 已存在，更新分数
                    combined[result_id]["fuzzy_score"] = result.get("score", 0.8)
                    combined[result_id]["final_score"] = (
                        combined[result_id]["vector_score"] * vector_weight +
                        combined[result_id]["fuzzy_score"] * fuzzy_weight
                    )
                    combined[result_id]["search_type"] = "hybrid"
                else:
                    # 新结果
                    result["vector_score"] = 0
                    result["fuzzy_score"] = result.get("score", 0.8)
                    result["final_score"] = result["fuzzy_score"] * fuzzy_weight
                    result["search_type"] = "fuzzy"
                    combined[result_id] = result

        return list(combined.values())

    async def _aselect(self, table: str, where: Dict[str, Any] = None, limit: int = None) -> List[Dict[str, Any]]:
        """通用查询方法"""
        try:
            # 使用UniversalSQLAlchemyClient的标准查询方法
            from base.db.base.rdb import QueryRequest, QueryFilterGroup, QueryFilter, ComparisonOperator, LogicalOperator, QuerySort, SortOrder

            # 构建过滤器
            filters = None
            if where:
                filter_list = []
                for key, value in where.items():
                    filter_list.append(QueryFilter(field=key, operator=ComparisonOperator.EQ, value=value))

                if filter_list:
                    filters = QueryFilterGroup(
                        operator=LogicalOperator.AND,
                        filters=filter_list
                    )

            # 构建排序
            sorts = [QuerySort(field="id", order=SortOrder.DESC)]

            # 构建查询请求
            query_request = QueryRequest(
                table=table,
                filters=filters,
                sorts=sorts,
                limit=limit
            )

            # 执行查询
            result = await self.rdb_client.aquery(query_request)
            return result.data if hasattr(result, 'data') else []

        except Exception as e:
            logger.error(f"查询失败: table={table}, where={where}, error={e}")
            # 降级到原生SQL查询
            return await self._raw_sql_select(table, where, limit)

    async def _raw_sql_select(self, table: str, where: Dict[str, Any] = None, limit: int = None) -> List[Dict[str, Any]]:
        """原生SQL查询方法（降级方案）"""
        # 构建SQL查询
        sql_parts = [f"SELECT * FROM {table}"]
        params = []

        if where:
            conditions = []
            for key, value in where.items():
                conditions.append(f"{key} = %s")
                params.append(value)
            sql_parts.append(f"WHERE {' AND '.join(conditions)}")

        sql_parts.append("ORDER BY id DESC")

        if limit:
            sql_parts.append(f"LIMIT %s")
            params.append(limit)

        sql = " ".join(sql_parts)

        # 执行查询 - 使用原生SQL查询
        # 注意：这里直接使用rdb_client的原生查询方法
        try:
            # 尝试使用原生SQL查询（如果客户端支持）
            if hasattr(self.rdb_client, 'execute_raw_sql'):
                result = await self.rdb_client.execute_raw_sql(sql, params)
                return result if isinstance(result, list) else []
            else:
                # 如果不支持原生SQL，返回空结果
                logger.warning(f"客户端不支持原生SQL查询，返回空结果")
                return []
        except Exception as e:
            logger.error(f"原生SQL查询失败: {e}")
            return []

    # ==================== 保持向后兼容的原有方法 ====================

    async def vector_search(
        self,
        query: str,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.5
    ) -> List[Dict[str, Any]]:
        """
        纯向量搜索（保持向后兼容）
        """
        return await self._vector_search(
            query=query,
            field=SearchField.ALL_VECTORIZED,
            knowledge_id=knowledge_id,
            data_layer=data_layer,
            limit=limit,
            min_score=min_score
        )

    async def text_search(
        self,
        query: str,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.3,
        vector_weight: float = 0.7,
        text_weight: float = 0.3,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        跨MySQL和PG的混合搜索（向量搜索 + 文本搜索）

        这个方法专门处理多表混合操作：
        - 向量搜索：在PG向量数据库中搜索
        - 文本搜索：在MySQL关系数据库中搜索
        - 结果合并：跨数据库的结果融合

        Args:
            query: 查询文本
            knowledge_id: 知识库ID（可选）
            data_layer: 数据层（可选）
            limit: 返回数量限制
            min_score: 最小相似度分数
            vector_weight: 向量搜索权重
            text_weight: 文本搜索权重

        Returns:
            搜索结果列表
        """
        try:
            logger.info(f"开始跨数据库混合搜索: query='{query}', knowledge_id={knowledge_id}")

            # 1. 向量搜索（PG数据库）
            vector_results = await self.vector_search(
                query=query,
                knowledge_id=knowledge_id,
                data_layer=data_layer,
                limit=limit * 2,  # 获取更多结果用于混合
                min_score=min_score
            )

            # 2. 文本搜索（MySQL数据库）
            text_results = await self._text_search_mysql(
                query=query,
                knowledge_id=knowledge_id,
                data_layer=data_layer,
                limit=limit * 2
            )

            # 3. 合并和重新评分
            combined_results = self._combine_search_results(
                vector_results=vector_results,
                text_results=text_results,
                vector_weight=vector_weight,
                text_weight=text_weight
            )

            # 4. 过滤低分结果并排序
            filtered_results = [
                result for result in combined_results
                if result.get("combined_score", 0) >= min_score
            ]

            sorted_results = sorted(
                filtered_results,
                key=lambda x: x.get("combined_score", 0),
                reverse=True
            )

            final_results = sorted_results[:limit]

            logger.info(f"跨数据库混合搜索完成: 向量结果 {len(vector_results)}, 文本结果 {len(text_results)}, 最终结果 {len(final_results)}")
            return final_results

        except Exception as e:
            logger.error(f"跨数据库混合搜索失败: {e}")
            raise DDError(f"跨数据库混合搜索失败: {e}")

    async def _text_search_mysql(
        self,
        query: str,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        文本搜索（在MySQL关系数据库的向量化字段中进行LIKE搜索）

        这个方法专门在MySQL中搜索，与向量搜索形成互补：
        - 向量搜索：语义相似性（PG数据库）
        - 文本搜索：字面匹配（MySQL数据库）
        """
        try:
            # 构建搜索条件
            filters = {}
            if knowledge_id:
                filters["knowledge_id"] = knowledge_id
            if data_layer:
                filters["dr01"] = data_layer

            # 搜索填报数据 - 直接使用数据库客户端
            all_submissions = await self._list_submission_data(
                knowledge_id=knowledge_id,
                data_layer=data_layer,
                limit=1000  # 获取较多数据用于文本匹配
            )

            # 在内存中进行文本匹配
            matched_results = []
            query_lower = query.lower()

            for submission in all_submissions:
                score = 0.0
                matched_fields = []

                # 检查向量化字段
                for field_code in DDConstants.VECTORIZED_FIELDS:
                    field_value = submission.get(field_code, "") or ""
                    if query_lower in field_value.lower():
                        # 简单的文本匹配评分
                        field_score = len(query) / max(len(field_value), 1)
                        score += field_score
                        matched_fields.append(field_code)

                if score > 0:
                    matched_results.append({
                        **submission,
                        "score": min(score, 1.0),  # 限制最大分数为1.0
                        "matched_fields": matched_fields,
                        "search_type": "text"
                    })

            # 按分数排序
            matched_results.sort(key=lambda x: x.get("score", 0), reverse=True)

            return matched_results[:limit]

        except Exception as e:
            logger.warning(f"文本搜索失败: {e}")
            return []





    # ==================== 保持向后兼容的原有方法 ====================

    async def vector_search(
        self,
        query: str,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 10,
        min_score: float = 0.5
    ) -> List[Dict[str, Any]]:
        """向量搜索 - 保持向后兼容"""
        return await self._vector_search(
            query=query,
            field=SearchField.ALL_VECTORIZED,
            knowledge_id=knowledge_id,
            data_layer=data_layer,
            limit=limit,
            min_score=min_score
        )

    # ==================== 业务逻辑方法（已移到业务层）====================
    # 注意：复杂的业务逻辑已移到 ThreeLayerSearchService 类中
    # 如需使用三层搜索功能，请使用：
    # from modules.knowledge.dd.business.three_layer_search_service import ThreeLayerSearchService

    # ==================== 辅助方法 ====================

    # 注意：业务逻辑方法已移到 ThreeLayerSearchService 类中
    # 如需使用四层筛选、TF-IDF推荐等业务逻辑，请使用业务层服务

    def _combine_search_results(
        self,
        vector_results: List[Dict[str, Any]],
        text_results: List[Dict[str, Any]],
        vector_weight: float,
        text_weight: float
    ) -> List[Dict[str, Any]]:
        """合并向量搜索和文本搜索结果"""

        # 创建结果字典，以submission_data的id为键
        combined_dict = {}

        # 处理向量搜索结果
        for result in vector_results:
            submission_data = result.get("submission_data", {})
            key = submission_data.get("id")
            if key:
                combined_dict[key] = {
                    **result,
                    "vector_score": result.get("score", 0),
                    "text_score": 0,
                    "combined_score": result.get("score", 0) * vector_weight
                }

        # 处理文本搜索结果
        for result in text_results:
            key = result.get("id")
            if key:
                if key in combined_dict:
                    # 已存在，更新分数
                    combined_dict[key]["text_score"] = result.get("score", 0)
                    combined_dict[key]["combined_score"] = (
                        combined_dict[key]["vector_score"] * vector_weight +
                        result.get("score", 0) * text_weight
                    )
                    # 合并匹配字段
                    vector_fields = set(combined_dict[key].get("matched_fields", []))
                    text_fields = set(result.get("matched_fields", []))
                    combined_dict[key]["matched_fields"] = list(vector_fields | text_fields)
                else:
                    # 新结果，只有文本分数
                    combined_dict[key] = {
                        "submission_data": result,
                        "vector_score": 0,
                        "text_score": result.get("score", 0),
                        "combined_score": result.get("score", 0) * text_weight,
                        "matched_fields": result.get("matched_fields", []),
                        "search_type": "text"
                    }

        return list(combined_dict.values())


    def _deduplicate_results(self, results: List[Dict[str, Any]], key_field: str) -> List[Dict[str, Any]]:
        """根据指定字段去重，保留分数最高的"""
        unique_dict = {}

        for result in results:
            key = result.get(key_field)
            if key:
                if key not in unique_dict or result.get("score", 0) > unique_dict[key].get("score", 0):
                    unique_dict[key] = result

        return list(unique_dict.values())

    async def _enrich_with_submission_data(self, vector_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """用完整的填报数据丰富向量搜索结果 - 使用批量查询优化"""
        if not vector_results:
            return []

        # 收集所有需要查询的source_id
        source_ids = []
        source_id_to_result_indices = {}  # source_id -> [result_indices]

        for i, result in enumerate(vector_results):
            source_id = result.get("source_id")
            if source_id:
                if source_id not in source_id_to_result_indices:
                    source_ids.append(source_id)
                    source_id_to_result_indices[source_id] = []
                source_id_to_result_indices[source_id].append(i)

        if not source_ids:
            return vector_results

        # 使用abatch_query批量查询所有填报数据
        try:
            queries = [
                {"data": ["*"], "filters": {"id": source_id}}
                for source_id in source_ids
            ]

            batch_results = await self.rdb_client.abatch_query(
                table=DDTableNames.KB_SUBMISSION_DATA,
                queries=queries,
                batch_size=100,
                max_concurrency=5
            )

            # 构建source_id到submission_data的映射
            source_id_to_data = {}
            for i, query_response in enumerate(batch_results):
                if query_response.data and len(query_response.data) > 0:
                    source_id_to_data[source_ids[i]] = query_response.data[0]

            # 丰富化结果
            enriched_results = []
            for i, result in enumerate(vector_results):
                source_id = result.get("source_id")
                if source_id and source_id in source_id_to_data:
                    # 合并向量结果和填报数据
                    enriched_results.append({
                        **result,
                        "submission_data": source_id_to_data[source_id]
                    })
                else:
                    enriched_results.append(result)

            logger.debug(f"批量丰富化完成: 查询{len(source_ids)}个source_id, 成功{len(source_id_to_data)}个")
            return enriched_results

        except Exception as e:
            logger.warning(f"批量查询填报数据失败，降级到逐个查询: {e}")
            # 降级到原有的逐个查询方式
            return await self._enrich_with_submission_data_fallback(vector_results)

    async def _enrich_with_submission_data_fallback(self, vector_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """填报数据丰富化的降级方案（原有逐个查询方式）"""
        enriched_results = []

        for result in vector_results:
            source_id = result.get("source_id")
            if source_id:
                try:
                    submission_data = await self._get_submission_data(source_id)
                    if submission_data:
                        enriched_results.append({
                            **result,
                            "submission_data": submission_data
                        })
                except Exception as e:
                    logger.warning(f"获取填报数据失败: source_id={source_id}, error={e}")
                    # 保留原结果
                    enriched_results.append(result)
            else:
                enriched_results.append(result)

        return enriched_results

    # ==================== 便捷搜索方法 ====================

    async def search_by_data_item_name(
        self,
        data_item_name: str,
        knowledge_id: Optional[str] = None,
        limit: int = 10,
        mode: Union[SearchMode, str] = SearchMode.HYBRID
    ) -> List[Dict[str, Any]]:
        """
        按数据项名称搜索（dr09字段）

        Args:
            data_item_name: 数据项名称
            knowledge_id: 知识库ID（可选）
            limit: 返回数量限制
            mode: 搜索模式（默认混合搜索）
        """
        return await self.search(
            query=data_item_name,
            mode=mode,
            field=SearchField.DATA_ITEM_NAME,
            knowledge_id=knowledge_id,
            limit=limit
        )

    async def search_by_requirement_rule(
        self,
        requirement_rule: str,
        knowledge_id: Optional[str] = None,
        limit: int = 10,
        mode: Union[SearchMode, str] = SearchMode.HYBRID
    ) -> List[Dict[str, Any]]:
        """
        按需求口径搜索（dr17字段）

        Args:
            requirement_rule: 需求口径
            knowledge_id: 知识库ID（可选）
            limit: 返回数量限制
            mode: 搜索模式（默认混合搜索）
        """
        return await self.search(
            query=requirement_rule,
            mode=mode,
            field=SearchField.REQUIREMENT_RULE,
            knowledge_id=knowledge_id,
            limit=limit
        )

    async def search_by_knowledge_id(
        self,
        knowledge_id: str,
        query: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """根据知识库ID搜索所有相关数据"""
        if query:
            return await self.hybrid_search(
                query=query,
                knowledge_id=knowledge_id,
                data_layer=data_layer,
                limit=limit
            )
        else:
            # 直接查询数据库
            return await self._list_submission_data(
                knowledge_id=knowledge_id,
                data_layer=data_layer,
                limit=limit
            )

    # ==================== 私有方法：数据库操作 ====================

    async def _list_submission_data(
        self,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """查询填报数据列表 - 直接使用数据库客户端"""
        try:
            # 构建查询条件
            where = {}
            if knowledge_id:
                where['knowledge_id'] = knowledge_id
            if data_layer:
                where['dr01'] = data_layer

            # 使用数据库客户端查询
            query_request = {
                "table": DDTableNames.KB_SUBMISSION_DATA,
                "sorts": [{"field": "create_time", "order": "desc"}]
            }

            # 只有在有条件时才添加filters
            if where:
                query_request["filters"] = where

            # 只有在有值时才添加limit和offset
            if limit is not None:
                query_request["limit"] = limit
            if offset is not None:
                query_request["offset"] = offset
            result = await self.rdb_client.aquery(query_request)
            results = result.data if result else []
            return results or []

        except Exception as e:
            logger.error(f"查询填报数据列表失败: {e}")
            return []

    async def _get_submission_data(self, submission_pk: int) -> Optional[Dict[str, Any]]:
        """根据主键获取填报数据 - 直接使用数据库客户端"""
        try:
            query_request = {
                "table": DDTableNames.KB_SUBMISSION_DATA,
                "filters": {'id': submission_pk},
                "limit": 1
            }
            result = await self.rdb_client.aquery(query_request)
            results = result.data if result else []
            return results[0] if results else None

        except Exception as e:
            logger.error(f"获取填报数据失败: submission_pk={submission_pk}, error={e}")
            return None

"""
模型排序器 - 支持基于AI模型的重排序

本模块提供：
1. 基于rerank模型的排序功能
2. 与传统算法一致的接口
3. 异步和同步调用支持
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from loguru import logger

from .algorithms import RankResult, RankConfig


@dataclass
class ModelRankConfig(RankConfig):
    """模型排序配置"""
    model_name: str = "default_rerank_model"
    provider: str = "default_provider"
    credentials: Dict[str, Any] = field(default_factory=dict)
    tenant_id: Optional[str] = None
    user_id: Optional[str] = None


class ModelRanker:
    """基于AI模型的排序器"""
    
    def __init__(self, config: ModelRankConfig):
        self.config = config
        self.rerank_provider = None  # 将来集成rerank模型提供者
        
    def rank(self, query: str, docs: List[Dict[str, Any]], **kwargs) -> RankResult:
        """
        使用AI模型执行排序
        
        Args:
            query: 查询文本
            docs: 文档列表
            **kwargs: 额外参数
            
        Returns:
            RankResult: 模型排序结果
        """
        try:
            if not self.rerank_provider:
                logger.warning("Rerank模型未配置，返回原始排序")
                return self._fallback_ranking(query, docs)
            
            # 准备文档文本列表
            doc_texts = self._extract_doc_texts(docs)
            
            # 调用rerank模型 (占位符实现)
            logger.info(f"调用rerank模型: model={self.config.model_name}, docs={len(doc_texts)}")
            
            # TODO: 实际调用rerank模型
            # result = await self.rerank_provider.ainvoke(
            #     model=self.config.model_name,
            #     credentials=self.config.credentials,
            #     query=query,
            #     docs=doc_texts,
            #     score_threshold=self.config.score_threshold,
            #     top_n=self.config.top_n,
            #     user=self.config.user_id
            # )
            
            # 暂时返回fallback结果
            return self._fallback_ranking(query, docs)
            
        except Exception as e:
            logger.error(f"模型排序失败: {str(e)}")
            return self._fallback_ranking(query, docs)
    
    async def arank(self, query: str, docs: List[Dict[str, Any]], **kwargs) -> RankResult:
        """
        异步使用AI模型执行排序
        
        Args:
            query: 查询文本
            docs: 文档列表 
            **kwargs: 额外参数
            
        Returns:
            RankResult: 模型排序结果
        """
        try:
            if not self.rerank_provider:
                logger.warning("Rerank模型未配置，返回原始排序")
                return self._fallback_ranking(query, docs)
            
            # 准备文档文本列表
            doc_texts = self._extract_doc_texts(docs)
            
            logger.info(f"异步调用rerank模型: model={self.config.model_name}, docs={len(doc_texts)}")
            
            # TODO: 实际异步调用rerank模型
            # result = await self.rerank_provider.ainvoke(
            #     model=self.config.model_name,
            #     credentials=self.config.credentials,
            #     query=query,
            #     docs=doc_texts,
            #     score_threshold=self.config.score_threshold,
            #     top_n=self.config.top_n,
            #     user=self.config.user_id
            # )
            
            # 暂时返回fallback结果
            return self._fallback_ranking(query, docs)
            
        except Exception as e:
            logger.error(f"异步模型排序失败: {str(e)}")
            return self._fallback_ranking(query, docs)
    
    def _extract_doc_texts(self, docs: List[Dict[str, Any]]) -> List[str]:
        """
        从文档列表中提取文本内容
        
        Args:
            docs: 文档列表
            
        Returns:
            文本列表
        """
        doc_texts = []
        for doc in docs:
            # 尝试多种可能的文本字段
            text_fields = ['content', 'text', 'description', 'col_desc', 'col_name_cn', 'col_name']
            doc_text = ""
            
            for field in text_fields:
                if field in doc and doc[field]:
                    doc_text += f"{doc[field]} "
            
            if not doc_text.strip():
                # 如果没有找到文本字段，将整个文档转为字符串
                doc_text = str(doc)
            
            doc_texts.append(doc_text.strip())
        
        return doc_texts
    
    def _fallback_ranking(self, query: str, docs: List[Dict[str, Any]]) -> RankResult:
        """
        fallback排序逻辑（当模型不可用时）
        
        Args:
            query: 查询文本
            docs: 文档列表
            
        Returns:
            RankResult: fallback排序结果
        """
        # 按distance字段排序（如果存在）
        if docs and 'distance' in docs[0]:
            sorted_docs = sorted(docs, key=lambda x: x.get('distance', float('inf')))
        else:
            sorted_docs = docs
        
        # 生成简单分数
        scores = [1.0 / (i + 1) for i in range(len(sorted_docs))]
        
        # 应用阈值和限制
        if self.config.score_threshold is not None:
            filtered_results = []
            filtered_scores = []
            for doc, score in zip(sorted_docs, scores):
                if score >= self.config.score_threshold:
                    filtered_results.append(doc)
                    filtered_scores.append(score)
            sorted_docs, scores = filtered_results, filtered_scores
        
        if self.config.top_n is not None:
            sorted_docs = sorted_docs[:self.config.top_n]
            scores = scores[:self.config.top_n]
        
        return RankResult(
            query=query,
            docs=sorted_docs,
            scores=scores,
            metadata={
                "algorithm": "model_fallback",
                "model": self.config.model_name,
                "provider": self.config.provider,
                "fallback": True
            }
        )
    
    def set_rerank_provider(self, provider):
        """
        设置rerank模型提供者
        
        Args:
            provider: rerank模型提供者实例
        """
        self.rerank_provider = provider
        logger.info(f"设置rerank模型提供者: {type(provider).__name__}")


# 便捷函数
def create_model_ranker(
    model_name: str = "default_rerank_model",
    provider: str = "default_provider", 
    credentials: Optional[Dict[str, Any]] = None,
    score_threshold: Optional[float] = None,
    top_n: Optional[int] = None,
    **kwargs
) -> ModelRanker:
    """
    创建模型排序器的便捷函数
    
    Args:
        model_name: 模型名称
        provider: 提供者名称
        credentials: 模型凭证
        score_threshold: 分数阈值
        top_n: 返回结果数量
        **kwargs: 其他配置参数
        
    Returns:
        ModelRanker: 模型排序器实例
    """
    config = ModelRankConfig(
        model_name=model_name,
        provider=provider,
        credentials=credentials or {},
        score_threshold=score_threshold,
        top_n=top_n,
        **kwargs
    )
    
    return ModelRanker(config) 
"""
VDB核心模块

导出核心抽象层的所有组件
"""

# 导入核心类型
from .types import (
    # 基础类型
    Vector, VectorArray, VectorDBValue, VectorDBRecord, VectorDBRecords,
    VectorDBConnection, VectorDBTransaction, VectorDBPool,
    
    # 枚举类型
    VectorDBType, FieldType, MetricType, IndexType, SearchType,
    ComparisonOperator, LogicalOperator, SortOrder, RankingMethod,
    
    # 协议类型
    VectorDBClient, VectorSearchable, CollectionManageable, DataManageable,
    
    # 配置类型
    ConnectionConfig, SearchConfig,
    
    # 工具函数
    normalize_vector, validate_vector_dimension, get_vector_dimension,
    is_valid_collection_name, is_valid_field_name
)

# 导入核心接口
from .interfaces import (
    VectorDatabaseClient, VectorSearchInterface,
    CollectionManagerInterface, IndexManagerInterface
)

# 导入核心模型
from .models import (
    # 模式定义
    FieldSchema, CollectionSchema,
    
    # 实体和结果
    Entity, SearchResult,
    
    # 请求模型
    SearchRequest, QueryRequest, GetRequest,
    InsertRequest, DeleteRequest, HybridSearchRequest,
    
    # 便捷构造函数
    create_field_schema, create_collection_schema, create_search_request,
    create_simple_schema, create_simple_search_request,
    
    # 向后兼容别名
    AnnSearchRequest
)

# 导入核心异常
from .exceptions import (
    # 基础异常
    VectorDBException,
    
    # 连接相关异常
    ConnectionError, AuthenticationError, TimeoutError,
    
    # 集合相关异常
    CollectionError, CollectionNotFoundError, CollectionAlreadyExistsError,
    SchemaError,
    
    # 数据相关异常
    DataError, DataValidationError, VectorDimensionError,
    InsertError, DeleteError, UpdateError,
    
    # 查询相关异常
    QueryError, SearchError, VectorSearchError, HybridSearchError,
    
    # 索引相关异常
    IndexError,
    
    # 配置相关异常
    ConfigurationError, UnsupportedOperationError,
    
    # 资源相关异常
    ResourceError, ResourceExhaustedError, PartitionError,
    
    # 便捷异常创建函数
    create_connection_error, create_collection_not_found_error,
    create_search_error, create_insert_error, create_schema_error,
    create_data_validation_error
)

# 版本信息
__version__ = "1.0.0"
__author__ = "HSBC Knowledge Team"

# 导出的公共接口
__all__ = [
    # 核心类型
    "Vector", "VectorArray", "VectorDBValue", "VectorDBRecord", "VectorDBRecords",
    "VectorDBConnection", "VectorDBTransaction", "VectorDBPool",
    
    # 枚举类型
    "VectorDBType", "FieldType", "MetricType", "IndexType", "SearchType",
    "ComparisonOperator", "LogicalOperator", "SortOrder", "RankingMethod",
    
    # 协议类型
    "VectorDBClient", "VectorSearchable", "CollectionManageable", "DataManageable",
    
    # 配置类型
    "ConnectionConfig", "SearchConfig",
    
    # 核心接口
    "VectorDatabaseClient", "VectorSearchInterface",
    "CollectionManagerInterface", "IndexManagerInterface",
    
    # 核心模型
    "FieldSchema", "CollectionSchema", "Entity", "SearchResult",
    "SearchRequest", "QueryRequest", "GetRequest",
    "InsertRequest", "DeleteRequest", "HybridSearchRequest",
    "AnnSearchRequest",  # 向后兼容
    
    # 核心异常
    "VectorDBException", "ConnectionError", "AuthenticationError", "TimeoutError",
    "CollectionError", "CollectionNotFoundError", "CollectionAlreadyExistsError",
    "SchemaError", "DataError", "DataValidationError", "VectorDimensionError",
    "InsertError", "DeleteError", "UpdateError", "QueryError", "SearchError",
    "VectorSearchError", "HybridSearchError", "IndexError",
    "ConfigurationError", "UnsupportedOperationError",
    "ResourceError", "ResourceExhaustedError", "PartitionError",
    
    # 便捷函数
    "normalize_vector", "validate_vector_dimension", "get_vector_dimension",
    "is_valid_collection_name", "is_valid_field_name",
    "create_field_schema", "create_collection_schema", "create_search_request",
    "create_simple_schema", "create_simple_search_request",
    "create_connection_error", "create_collection_not_found_error",
    "create_search_error", "create_insert_error", "create_schema_error",
    "create_data_validation_error"
]

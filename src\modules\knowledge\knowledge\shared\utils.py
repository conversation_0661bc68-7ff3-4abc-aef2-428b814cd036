"""
Knowledge模块工具函数

参考DD系统的utils.py设计，提供知识库相关的工具函数。
"""

import json
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime

from .constants import KnowledgeConstants
from .exceptions import KnowledgeValidationError, KnowledgeModelError


class KnowledgeUtils:
    """知识库工具类"""
    
    @staticmethod
    def generate_knowledge_id() -> str:
        """生成知识库ID"""
        return str(uuid.uuid4())
    
    @staticmethod
    def validate_knowledge_id(knowledge_id: str) -> bool:
        """验证知识库ID格式"""
        try:
            uuid.UUID(knowledge_id)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_knowledge_type(knowledge_type: str) -> None:
        """验证知识库类型"""
        if knowledge_type not in KnowledgeConstants.SUPPORTED_KB_TYPES:
            raise KnowledgeValidationError(
                f"不支持的知识库类型: {knowledge_type}，支持的类型: {KnowledgeConstants.SUPPORTED_KB_TYPES}",
                "knowledge_type"
            )
    
    @staticmethod
    def validate_models_configuration(models: dict, knowledge_type: str) -> None:
        """
        验证模型配置是否符合知识库类型要求
        
        Args:
            models: 模型配置字典
            knowledge_type: 知识库类型
            
        Raises:
            KnowledgeModelError: 当模型配置不符合要求时
        """
        if not isinstance(models, dict):
            raise KnowledgeModelError("模型配置必须是字典类型")
        
        # 获取该类型知识库的默认配置，用于确定必需的模型
        default_config = KnowledgeConstants.DEFAULT_MODELS.get(knowledge_type, {})
        
        # 检查必需的模型是否存在
        for model_type in default_config.keys():
            if model_type not in models:
                raise KnowledgeModelError(
                    f"{knowledge_type}类型知识库必须包含{model_type}模型",
                    model_type
                )
            
            if not models[model_type] or not isinstance(models[model_type], str):
                raise KnowledgeModelError(
                    f"{model_type}模型配置不能为空且必须是字符串",
                    model_type
                )
        
        # 检查是否有不支持的模型类型
        supported_model_types = ["embedding", "llm", "other"]
        for model_type in models:
            if model_type not in supported_model_types:
                raise KnowledgeModelError(
                    f"不支持的模型类型: {model_type}",
                    model_type
                )
    
    @staticmethod
    def get_default_models_configuration(knowledge_type: str) -> dict:
        """
        获取指定知识库类型的默认模型配置
        
        Args:
            knowledge_type: 知识库类型
            
        Returns:
            dict: 默认模型配置
        """
        return KnowledgeConstants.DEFAULT_MODELS.get(knowledge_type, {}).copy()
    
    @staticmethod
    def serialize_models(models: dict) -> str:
        """序列化模型配置为JSON字符串"""
        return json.dumps(models, ensure_ascii=False)
    
    @staticmethod
    def deserialize_models(models_json: str) -> dict:
        """反序列化模型配置JSON字符串"""
        try:
            if isinstance(models_json, str):
                return json.loads(models_json)
            elif isinstance(models_json, dict):
                return models_json
            else:
                return {}
        except json.JSONDecodeError:
            return {}
    
    @staticmethod
    def extract_vectorized_content(kb_data: Dict[str, Any]) -> Dict[str, str]:
        """提取需要向量化的内容"""
        vectorized_content = {}
        for field in KnowledgeConstants.VECTORIZED_FIELDS:
            if field in kb_data and kb_data[field]:
                vectorized_content[field] = str(kb_data[field])
        return vectorized_content
    
    @staticmethod
    def build_search_filters(
        knowledge_name: Optional[str] = None,
        knowledge_type: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """构建搜索过滤条件"""
        filters = {}
        
        if knowledge_name:
            filters['knowledge_name'] = knowledge_name
        
        if knowledge_type:
            filters['knowledge_type'] = knowledge_type
            
        # 添加其他过滤条件
        for key, value in kwargs.items():
            if value is not None:
                filters[key] = value
                
        return filters

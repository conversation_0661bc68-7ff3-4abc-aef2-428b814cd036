"""
DD-B数据处理引擎

负责从数据库查询数据并进行处理的核心逻辑
"""

import time
import logging
from typing import List, Dict, Any, Optional, Tuple

from modules.dd_submission.dd_b.infrastructure.models import (
    DDBProcessRequest,
    DDBProcessResult,
    DDBRecord,
    ProcessingStatusEnum,
    FieldFillInfo,
    FieldStatusEnum,
    GenerationDecision,
    GenerationStrategyEnum
)
from modules.dd_submission.dd_b.infrastructure.constants import DDBConstants, DDBUtils
from modules.dd_submission.dd_b.infrastructure.exceptions import (
    DDBDatabaseError,
    DDBDataNotFoundError,
    DDBProcessingError,
    handle_async_ddb_errors,
    create_database_error,
    create_data_not_found_error,
    create_processing_error
)
from .field_filler import FieldFiller
from .history_connector import HistoryConnector

logger = logging.getLogger(__name__)


class DataProcessor:
    """DD-B数据处理引擎"""

    def __init__(self, rdb_client: Any, vdb_client: Any = None, embedding_client: Any = None):
        """
        初始化数据处理引擎

        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端（可选）
            embedding_client: 向量化模型客户端（可选）
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
        self.field_filler = FieldFiller()
        self.table_name = DDBConstants.TABLE_NAME

        # 初始化历史连接器（如果有向量数据库客户端）
        self.history_connector = None
        if vdb_client:
            self.history_connector = HistoryConnector(rdb_client, vdb_client, embedding_client)
            logger.info("历史连接器已启用")
        else:
            logger.info("历史连接器未启用（缺少向量数据库客户端）")
    
    @handle_async_ddb_errors
    async def query_records(self, request: DDBProcessRequest) -> List[DDBRecord]:
        """
        查询记录（优化的批量查询版本）

        Args:
            request: 处理请求

        Returns:
            查询到的记录列表
        """
        try:
            # 构建查询条件
            conditions = DDBUtils.build_query_conditions(
                request.report_code,
                request.dept_id
            )

            logger.info(f"查询条件: {conditions}")

            # 优先使用DD CRUD的批量查询方法
            try:
                records = await self._query_records_with_dd_crud(conditions)
                if records:
                    logger.info(f"DD CRUD批量查询成功，找到 {len(records)} 条记录")
                    return records
            except Exception as e:
                logger.warning(f"DD CRUD查询失败，降级到通用方法: {e}")

            # 降级到通用批量查询方法
            records = await self._query_records_with_batch_query(conditions)
            logger.info(f"通用批量查询完成，找到 {len(records)} 条记录")

            if not records:
                raise create_data_not_found_error(
                    f"未找到匹配的记录",
                    query_conditions=conditions
                )

            return records

        except Exception as e:
            if isinstance(e, (DDBDatabaseError, DDBDataNotFoundError)):
                raise

            logger.error(f"查询记录时发生错误: {e}")
            raise create_database_error(
                f"查询记录失败: {str(e)}",
                operation="select",
                table_name=self.table_name
            )

    async def _query_records_with_dd_crud(self, conditions: Dict[str, Any]) -> List[DDBRecord]:
        """使用DD CRUD的优化批量查询方法"""
        try:
            # 导入DD CRUD
            from modules.knowledge.dd.crud import DDCrud

            # 创建CRUD实例
            dd_crud = DDCrud(rdb_client=self.rdb_client)

            # 使用批量查询方法
            conditions_list = [conditions]

            raw_records = await dd_crud.batch_query_post_distributions(
                conditions_list=conditions_list,
                batch_size=50,
                max_concurrency=3,
                timeout_per_batch=120.0
            )

            # 转换为DDBRecord对象
            records = []
            for raw_record in raw_records:
                try:
                    record = self._convert_row_to_record(raw_record)
                    records.append(record)
                except Exception as e:
                    logger.warning(f"转换记录失败，跳过: {e}")
                    continue

            return records

        except Exception as e:
            logger.error(f"DD CRUD批量查询失败: {e}")
            raise

    async def _query_records_with_batch_query(self, conditions: Dict[str, Any]) -> List[DDBRecord]:
        """使用通用批量查询方法（降级方案）"""
        try:
            # 使用UniversalSQLAlchemyClient的abatch_query方法
            queries = [
                {
                    "data": ["*"],
                    "filters": conditions,
                    "limit": DDBConstants.DEFAULT_QUERY_LIMIT
                }
            ]

            # 执行批量查询
            batch_results = await self.rdb_client.abatch_query(
                table=self.table_name,
                queries=queries,
                batch_size=1,
                max_concurrency=1,
                timeout_per_batch=60.0
            )

            # 处理查询结果
            records = []
            if batch_results and len(batch_results) > 0:
                query_response = batch_results[0]
                if hasattr(query_response, 'data') and query_response.data:
                    for row_data in query_response.data:
                        try:
                            record = self._convert_row_to_record(row_data)
                            records.append(record)
                        except Exception as e:
                            logger.warning(f"转换记录失败，跳过: {e}")
                            continue

            return records

        except Exception as e:
            logger.error(f"通用批量查询失败: {e}")
            raise

    def _convert_row_to_record(self, row) -> DDBRecord:
        """将数据库行转换为DDBRecord对象"""
        try:
            # 处理不同类型的数据库返回结果
            if hasattr(row, '__dict__'):
                # SQLAlchemy对象，转换为字典
                row_dict = {k: v for k, v in row.__dict__.items() if not k.startswith('_')}
            elif isinstance(row, dict):
                # 已经是字典
                row_dict = row
            else:
                # 尝试转换为字典
                try:
                    row_dict = dict(row)
                except:
                    logger.error(f"无法转换行数据为字典: {type(row)}, {row}")
                    raise ValueError(f"不支持的行数据类型: {type(row)}")

            return DDBRecord(
                id=row_dict.get('id'),
                # 关键修复：数据库使用version字段，需要映射到report_code
                report_code=row_dict.get('version') or row_dict.get('report_code'),
                dept_id=row_dict.get('dept_id'),
                pre_distribution_id=row_dict.get('pre_distribution_id'),
                # 统一使用小写字段名，与数据库字段一致
                dr01=row_dict.get('dr01'),
                bdr09=row_dict.get('bdr09'),
                bdr10=row_dict.get('bdr10'),
                bdr11=row_dict.get('bdr11'),
                bdr16=row_dict.get('bdr16'),
                bdr05=row_dict.get('bdr05'),
                bdr06=row_dict.get('bdr06'),
                bdr07=row_dict.get('bdr07'),
                bdr08=row_dict.get('bdr08'),
                bdr12=row_dict.get('bdr12'),
                bdr13=row_dict.get('bdr13'),
                bdr14=row_dict.get('bdr14'),
                bdr15=row_dict.get('bdr15'),
                bdr17=row_dict.get('bdr17'),
                # SDR字段也使用小写
                sdr01=row_dict.get('sdr01'),
                sdr02=row_dict.get('sdr02'),
                sdr03=row_dict.get('sdr03'),
                sdr04=row_dict.get('sdr04'),
                sdr05=row_dict.get('sdr05'),
                sdr06=row_dict.get('sdr06'),
                sdr07=row_dict.get('sdr07'),
                sdr08=row_dict.get('sdr08'),
                sdr09=row_dict.get('sdr09'),
                sdr10=row_dict.get('sdr10'),
                sdr11=row_dict.get('sdr11'),
                sdr12=row_dict.get('sdr12'),
                sdr13=row_dict.get('sdr13'),
                sdr14=row_dict.get('sdr14'),
                sdr15=row_dict.get('sdr15'),
                create_time=row_dict.get('create_time'),
                update_time=row_dict.get('update_time')
            )
        except Exception as e:
            logger.error(f"转换数据库行失败: {e}, 行数据: {row}")
            raise
    
    def _convert_row_to_record(self, row: Dict[str, Any]) -> DDBRecord:
        """
        将数据库行转换为DDBRecord对象

        Args:
            row: 数据库行数据

        Returns:
            DDBRecord对象
        """
        return DDBRecord(
            id=row.get('id'),
            report_code=row.get('report_code'),
            dept_id=row.get('dept_id'),
            pre_distribution_id=row.get('pre_distribution_id'),  # 新增字段
            BDR09=row.get('BDR09'),
            BDR10=row.get('BDR10'),
            BDR11=row.get('BDR11'),
            BDR16=row.get('BDR16'),
            BDR05=row.get('BDR05'),  # 新增字段
            brd06=row.get('brd06'),
            brd07=row.get('brd07'),
            brd08=row.get('brd08'),
            BDR12=row.get('BDR12'),
            BDR13=row.get('BDR13'),
            BDR14=row.get('BDR14'),
            BDR15=row.get('BDR15'),
            BDR17=row.get('BDR17'),  # 新增字段
            brd17=row.get('brd17'),
            create_time=row.get('create_time'),
            update_time=row.get('update_time')
        )
    
    @handle_async_ddb_errors
    async def process_records(self, request: DDBProcessRequest) -> DDBProcessResult:
        """
        处理记录
        
        Args:
            request: 处理请求
            
        Returns:
            处理结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始处理请求: report_code={request.report_code}, dept_id={request.dept_id}")
            
            # 1. 查询记录
            original_records = await self.query_records(request)
            
            # 2. 分析记录状态
            analysis = self._analyze_records(original_records)

            # 3. 历史信息提取和生成决策（新增步骤）
            generation_decisions = []
            if self.history_connector:
                logger.info("执行历史信息提取和生成决策")
                for record in original_records:
                    try:
                        decision = await self.history_connector.extract_and_decide(record)
                        generation_decisions.append(decision)
                        logger.debug(f"记录 {record.id} 决策: {decision.strategy.value}")
                    except Exception as e:
                        logger.error(f"记录 {record.id} 决策失败: {e}")
                        # 默认决策
                        default_decision = GenerationDecision(
                            record_id=record.id,
                            strategy=GenerationStrategyEnum.LOW_CONFIDENCE,
                            confidence_score=0.0,
                            fields_to_generate=record.get_main_generation_fields(),
                            decision_notes=[f"决策失败: {str(e)}", "采用默认低置信度策略"]
                        )
                        generation_decisions.append(default_decision)
            else:
                logger.info("历史连接器未启用，跳过历史信息提取")

            # 4. 根据决策结果处理数据
            if generation_decisions:
                processed_records, fill_details = await self._process_with_generation_decisions(
                    original_records, generation_decisions, request
                )
            elif analysis["all_main_fields_complete"]:
                # 主要字段都完整，直接返回
                logger.info("所有记录的主要字段都完整，直接返回原始数据")
                processed_records = original_records
                fill_details = []
            else:
                # 需要填充处理（传统逻辑）
                logger.info(f"发现 {analysis['records_needing_fill']} 条记录需要填充")
                processed_records, fill_details = await self._process_with_filling(
                    original_records, request
                )
            
            # 4. 构建结果
            processing_time = (time.time() - start_time) * 1000
            
            result = DDBProcessResult(
                request=request,
                total_records_found=len(original_records),
                records_processed=len(processed_records),
                processed_records=processed_records,
                original_records=original_records if request.return_original_data else [],
                records_with_complete_main_fields=analysis["complete_main_fields_count"],
                records_requiring_fill=analysis["records_needing_fill"],
                total_fields_filled=len(fill_details),
                fill_details=fill_details,
                status=ProcessingStatusEnum.SUCCESS,
                message="处理完成",
                processing_time_ms=processing_time,
                processing_notes=[
                    f"查询到 {len(original_records)} 条记录",
                    f"主要字段完整的记录: {analysis['complete_main_fields_count']} 条",
                    f"需要填充的记录: {analysis['records_needing_fill']} 条",
                    f"总填充字段数: {len(fill_details)} 个"
                ]
            )
            
            logger.info(f"处理完成，耗时 {processing_time:.2f}ms")
            
            return result
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            
            if isinstance(e, (DDBDatabaseError, DDBDataNotFoundError)):
                # 数据库或数据未找到错误
                status = ProcessingStatusEnum.FAILED
                message = str(e)
            else:
                # 其他处理错误
                logger.error(f"处理记录时发生错误: {e}")
                status = ProcessingStatusEnum.FAILED
                message = f"处理失败: {str(e)}"
            
            # 返回失败结果
            return DDBProcessResult(
                request=request,
                total_records_found=0,
                records_processed=0,
                processed_records=[],
                status=status,
                message=message,
                processing_time_ms=processing_time,
                processing_notes=[f"处理失败: {str(e)}"]
            )
    
    def _analyze_records(self, records: List[DDBRecord]) -> Dict[str, Any]:
        """
        分析记录状态
        
        Args:
            records: 记录列表
            
        Returns:
            分析结果
        """
        complete_count = 0
        needing_fill_count = 0
        
        for record in records:
            if record.is_main_fields_complete():
                complete_count += 1
            else:
                needing_fill_count += 1
        
        analysis = {
            "total_records": len(records),
            "complete_main_fields_count": complete_count,
            "records_needing_fill": needing_fill_count,
            "all_main_fields_complete": needing_fill_count == 0
        }
        
        logger.debug(f"记录分析结果: {analysis}")
        
        return analysis
    
    async def _process_with_filling(
        self, 
        records: List[DDBRecord], 
        request: DDBProcessRequest
    ) -> Tuple[List[DDBRecord], List[FieldFillInfo]]:
        """
        进行填充处理
        
        Args:
            records: 原始记录列表
            request: 处理请求
            
        Returns:
            (处理后的记录列表, 填充详情列表)
        """
        if not request.enable_auto_fill:
            logger.info("自动填充已禁用，返回原始记录")
            return records, []
        
        # 执行字段填充
        filled_records, fill_details = self.field_filler.batch_fill_records(records)
        
        logger.info(f"填充处理完成: 填充了 {len(fill_details)} 个字段")

        return filled_records, fill_details

    async def _process_with_generation_decisions(
        self,
        records: List[DDBRecord],
        decisions: List[GenerationDecision],
        request: DDBProcessRequest
    ) -> Tuple[List[DDBRecord], List[FieldFillInfo]]:
        """
        基于生成决策处理记录

        Args:
            records: 原始记录列表
            decisions: 生成决策列表
            request: 处理请求

        Returns:
            (处理后的记录列表, 填充详情列表)
        """
        processed_records = []
        all_fill_details = []

        for record, decision in zip(records, decisions):
            try:
                if decision.strategy == GenerationStrategyEnum.DIRECT_RETURN:
                    # 直接返回策略：主要字段完整，无需处理
                    processed_records.append(record)
                    logger.debug(f"记录 {record.id}: 直接返回策略")

                elif decision.strategy == GenerationStrategyEnum.HIGH_CONFIDENCE:
                    # 高置信度策略：使用历史数据完全填充
                    filled_record, fill_details = await self._fill_from_history_data(
                        record, decision
                    )
                    processed_records.append(filled_record)
                    all_fill_details.extend(fill_details)
                    logger.debug(f"记录 {record.id}: 高置信度策略，填充 {len(fill_details)} 个字段")

                elif decision.strategy == GenerationStrategyEnum.LOW_CONFIDENCE:
                    # 低置信度策略：标记需要生成的字段，其他字段按默认规则填充
                    filled_record, fill_details = await self._process_low_confidence_record(
                        record, decision, request
                    )
                    processed_records.append(filled_record)
                    all_fill_details.extend(fill_details)
                    logger.debug(f"记录 {record.id}: 低置信度策略，需要生成 {len(decision.fields_to_generate)} 个字段")

                else:
                    # 未知策略，使用默认处理
                    logger.warning(f"记录 {record.id}: 未知策略 {decision.strategy}，使用默认处理")
                    filled_record, fill_details = self.field_filler.fill_record_fields(record)
                    processed_records.append(filled_record)
                    all_fill_details.extend(fill_details)

            except Exception as e:
                logger.error(f"处理记录 {record.id} 失败: {e}")
                # 失败时保留原始记录
                processed_records.append(record)

        logger.info(f"基于生成决策处理完成: 处理 {len(records)} 条记录，填充 {len(all_fill_details)} 个字段")

        return processed_records, all_fill_details

    async def _fill_from_history_data(
        self,
        record: DDBRecord,
        decision: GenerationDecision
    ) -> Tuple[DDBRecord, List[FieldFillInfo]]:
        """
        从历史数据填充记录

        Args:
            record: 原始记录
            decision: 生成决策

        Returns:
            (填充后的记录, 填充详情列表)
        """
        from copy import deepcopy

        filled_record = deepcopy(record)
        fill_details = []

        if not decision.best_match_data:
            logger.warning(f"记录 {record.id}: 高置信度策略但无历史数据")
            return filled_record, fill_details

        # 从历史数据中提取字段值
        history_data = decision.best_match_data

        for field_name in decision.fields_to_fill_from_history:
            if hasattr(filled_record, field_name):
                original_value = getattr(filled_record, field_name)
                history_value = history_data.get(field_name)

                if history_value is not None:
                    # 使用历史数据填充
                    setattr(filled_record, field_name, history_value)

                    fill_info = FieldFillInfo(
                        field_name=field_name,
                        original_value=original_value,
                        filled_value=history_value,
                        status=FieldStatusEnum.FILLED,
                        fill_reason=f"高置信度历史数据填充 (置信度: {decision.confidence_score:.3f})"
                    )
                    fill_details.append(fill_info)

                    logger.debug(f"字段 {field_name}: {original_value} -> {history_value} (历史数据)")

        return filled_record, fill_details

    async def _process_low_confidence_record(
        self,
        record: DDBRecord,
        decision: GenerationDecision,
        request: DDBProcessRequest
    ) -> Tuple[DDBRecord, List[FieldFillInfo]]:
        """
        处理低置信度记录

        Args:
            record: 原始记录
            decision: 生成决策
            request: 处理请求

        Returns:
            (处理后的记录, 填充详情列表)
        """
        from copy import deepcopy

        filled_record = deepcopy(record)
        fill_details = []

        # 1. 标记需要生成的字段（这些字段将在后续的生成模块中处理）
        for field_name in decision.fields_to_generate:
            if hasattr(filled_record, field_name):
                original_value = getattr(filled_record, field_name)

                # 暂时保持原值，添加标记说明需要生成
                fill_info = FieldFillInfo(
                    field_name=field_name,
                    original_value=original_value,
                    filled_value=original_value or "[需要生成]",
                    status=FieldStatusEnum.EMPTY,
                    fill_reason=f"低置信度策略，需要重新生成 (置信度: {decision.confidence_score:.3f})"
                )
                fill_details.append(fill_info)

        # 2. 其他字段按默认规则填充
        if request.enable_auto_fill:
            other_filled_record, other_fill_details = self.field_filler.fill_record_fields(filled_record)
            filled_record = other_filled_record

            # 过滤掉需要生成的字段的填充信息
            filtered_fill_details = [
                info for info in other_fill_details
                if info.field_name not in decision.fields_to_generate
            ]
            fill_details.extend(filtered_fill_details)

        return filled_record, fill_details


# 便捷函数
def create_data_processor(
    rdb_client: Any,
    vdb_client: Any = None,
    embedding_client: Any = None
) -> DataProcessor:
    """创建数据处理引擎实例"""
    return DataProcessor(rdb_client, vdb_client, embedding_client)


async def process_dd_b_request(
    rdb_client: Any,
    request: DDBProcessRequest,
    vdb_client: Any = None,
    embedding_client: Any = None
) -> DDBProcessResult:
    """处理DD-B请求的便捷函数"""
    processor = create_data_processor(rdb_client, vdb_client, embedding_client)
    return await processor.process_records(request)

"""
SQL生成步骤 - 轻量化版本
根据schema和样例生成SQL查询
"""

from typing import Dict, Any, List
import json
import ast
from copy import deepcopy
import logging

logger = logging.getLogger(__name__)

from pipeline.core.base_step import LLMStep
from pipeline.core.context import PipelineContext

class SQLGeneratorStep(LLMStep):
    """
    SQL生成步骤
    根据schema和样例生成SQL查询
    """

    def __init__(self):
        super().__init__(
            name="sql_generator",
            description="根据schema生成SQL查询",
            template_name="icl_generator",
            parser_name="generated_candidate_finetuned",
            num_calls=1  
        )

    async def preprocess(self, context: PipelineContext) -> Dict[str, Any]:
        """阶段1: 预处理 - 构建prompt变量"""
        # 检查是否有Schema，如果没有则跳过
        db_schema = context.get("db_schema", "")

        # 处理db_schema可能是list的情况
        if isinstance(db_schema, list):
            if not db_schema:
                logger.warning("数据库Schema列表为空，无法进行SQL生成")
                return None
            # 如果是列表，取第一个元素的prompt字段
            if isinstance(db_schema[0], dict) and "prompt" in db_schema[0]:
                db_schema = db_schema[0]["prompt"]
            else:
                db_schema = str(db_schema[0])

        if not db_schema or (isinstance(db_schema, str) and db_schema.strip() == ""):
            logger.warning("数据库Schema为空，无法进行SQL生成")
            return None

        return {
            "system": {
                "DATABASE_TYPE": context.get("db_type", "MySQL"),
                "EXAMPLES": context.get("fewshot_examples", "")
            },
            "user": {
                "DATABASE_SCHEMA": db_schema,
                "QUESTION": context.user_question,
                "HINT": context.hint
            }
        }

    async def parse_result(self, result: List[str], context: PipelineContext) -> List[Any]:
        """阶段3: 解析LLM结果"""
        # 先使用父类的解析器解析
        parsed_results = await super().parse_result(result, context)

        # 进一步处理解析结果
        final_results = []
        for parsed in parsed_results:
            if isinstance(parsed, str):
                try:
                    # 尝试JSON解析
                    parsed = json.loads(parsed)
                except json.JSONDecodeError:
                    try:
                        # 备选：Python字面量解析
                        parsed = ast.literal_eval(parsed)
                    except (SyntaxError, ValueError):
                        logger.warning(f"无法解析SQL生成结果: {parsed[:200] if parsed else 'None'}...")
                        parsed = {"error": "解析失败", "raw": parsed}

            final_results.append(parsed)

        return final_results

    async def postprocess(self, parsed_results: List[Any], context: PipelineContext) -> Dict[str, Any]:
        """阶段4: 后处理 - 提取和验证SQL"""
        sql_set = set()  # 使用集合去重

        for result in parsed_results:
            try:
                if isinstance(result, dict) and "error" not in result:
                    # 提取SQL
                    if 'SQL' in result:
                        sql_set.add(result['SQL'])
                    elif 'sql' in result:
                        sql_set.add(result['sql'])
            except Exception as e:
                logger.warning(f"提取SQL语句失败: {e}, 原始结果: {str(result)[:200]}...")

        # 转换为列表
        sql_candidates = list(sql_set)

        # 执行SQL验证（可选）
        execution_results = []
        try:
            # 这里可以添加SQL执行验证逻辑
            # from utils.db.mschema.db_check import execute_sql_candidates_with_rdb_client
            # execution_results = execute_sql_candidates_with_rdb_client(...)
            pass
        except Exception as e:
            logger.warning(f"SQL验证失败: {e}")

        logger.info(f"合并SQL结果: 共 {len(sql_candidates)} 个候选")

        return {
            "sql_candidates": sql_candidates,
            "execution_results": execution_results
        }

    async def update_context(self, result: Dict[str, Any], context: PipelineContext) -> None:
        """更新上下文 - 存储SQL结果"""
        context.set("sql_candidates", result["sql_candidates"])
        context.set("execution_results", result["execution_results"])

    def format_display_result(self, final_result: Dict[str, Any]) -> str:
        """
        格式化结果用于显示

        Args:
            final_result: SQL生成结果

        Returns:
            格式化后的字符串
        """
        sql_candidates = final_result.get("sql_candidates", [])

        if not sql_candidates:
            return "❌ 未生成有效的 SQL 查询候选。"

        display_parts = ["## 🔍 生成的 SQL 查询候选：\n"]

        for i, sql in enumerate(sql_candidates):
            if sql:
                display_parts.append(f"### 📝 候选 {i+1}:")
                display_parts.append("```sql")
                display_parts.append(sql)
                display_parts.append("```")
                display_parts.append("")  # 添加空行分隔
            else:
                display_parts.append(f"### ❌ 候选 {i+1}: (无效格式或无 SQL)")

        return "\n".join(display_parts)

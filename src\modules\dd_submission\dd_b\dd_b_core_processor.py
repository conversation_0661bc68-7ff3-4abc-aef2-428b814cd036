"""
DD-B核心处理器

专注于核心逻辑的简化版本，用于测试和调试：
1. 输入：report_code (version) 和 dept_id
2. 输出：最终的 list[dict] 格式数据
3. 支持批量处理（300条一批）
4. 优化向量搜索（批量搜索而非逐个）
5. 完整的数据流可视化和调试

核心流程：
查询数据 → 批量向量搜索 → Pipeline处理 → 字段聚合 → 最终输出
"""

import asyncio
import json
import logging
import os
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class DDBProcessProgress:
    """DD-B处理进度"""
    total_records: int = 0
    processed_records: int = 0
    successful_records: int = 0
    failed_records: int = 0
    current_batch: int = 0
    total_batches: int = 0
    start_time: float = field(default_factory=time.time)
    last_checkpoint_time: float = field(default_factory=time.time)
    checkpoint_file: str = ""

    def get_progress_percentage(self) -> float:
        """获取进度百分比"""
        if self.total_records == 0:
            return 0.0
        return (self.processed_records / self.total_records) * 100

    def get_elapsed_time(self) -> float:
        """获取已用时间（秒）"""
        return time.time() - self.start_time

    def get_estimated_remaining_time(self) -> float:
        """估算剩余时间（秒）"""
        if self.processed_records == 0:
            return 0.0
        elapsed = self.get_elapsed_time()
        avg_time_per_record = elapsed / self.processed_records
        remaining_records = self.total_records - self.processed_records
        return remaining_records * avg_time_per_record

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'total_records': self.total_records,
            'processed_records': self.processed_records,
            'successful_records': self.successful_records,
            'failed_records': self.failed_records,
            'current_batch': self.current_batch,
            'total_batches': self.total_batches,
            'start_time': self.start_time,
            'last_checkpoint_time': self.last_checkpoint_time,
            'checkpoint_file': self.checkpoint_file,
            'progress_percentage': self.get_progress_percentage(),
            'elapsed_time': self.get_elapsed_time(),
            'estimated_remaining_time': self.get_estimated_remaining_time()
        }


@dataclass
class CoreProcessResult:
    """核心处理结果"""
    success: bool = False
    total_records: int = 0
    processed_records: int = 0
    processing_time: float = 0.0
    
    # 最终输出数据
    final_data: List[Dict[str, Any]] = field(default_factory=list)
    
    # 调试信息
    debug_info: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)


class DDBCoreProcessor:
    """DD-B核心处理器"""
    
    def __init__(
        self,
        rdb_client: Any,
        vdb_client: Any = None,
        embedding_client: Any = None,
        batch_size: int = 300
    ):
        """
        初始化核心处理器
        
        Args:
            rdb_client: RDB客户端 (get_client('database.rdbs.mysql'))
            vdb_client: VDB客户端 (get_client('database.vdbs.pgvector'))
            embedding_client: 嵌入客户端 (get_client('model.embeddings.moka-m3e-base'))
            batch_size: 批处理大小，默认300
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
        self.batch_size = batch_size
        self.max_workers = 15  # 并发worker数量
        self.enable_checkpoint = True
        self.checkpoint_dir = "./dd_b_checkpoints"
        self.progress: Optional[DDBProcessProgress] = None

        # 创建检查点目录
        if self.enable_checkpoint:
            os.makedirs(self.checkpoint_dir, exist_ok=True)

        logger.info(f"DD-B核心处理器初始化: batch_size={batch_size}, checkpoint_enabled={self.enable_checkpoint}")

    def _create_checkpoint_filename(self, report_code: str, dept_id: str) -> str:
        """创建检查点文件名"""
        timestamp = int(time.time())
        return f"dd_b_checkpoint_{report_code}_{dept_id}_{timestamp}.json"

    def _save_checkpoint(self, results: List[Dict[str, Any]]) -> None:
        """保存检查点"""
        if not self.enable_checkpoint or not self.progress:
            return

        try:
            checkpoint_data = {
                'progress': self.progress.to_dict(),
                'results': results,
                'timestamp': time.time()
            }

            checkpoint_path = os.path.join(self.checkpoint_dir, self.progress.checkpoint_file)
            with open(checkpoint_path, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)

            self.progress.last_checkpoint_time = time.time()
            logger.info(f"检查点已保存: {checkpoint_path}, 进度: {self.progress.get_progress_percentage():.1f}%")

        except Exception as e:
            logger.error(f"保存检查点失败: {e}")

    def _load_checkpoint(self, checkpoint_file: str) -> Optional[Dict[str, Any]]:
        """加载检查点"""
        if not self.enable_checkpoint:
            return None

        try:
            checkpoint_path = os.path.join(self.checkpoint_dir, checkpoint_file)
            if not os.path.exists(checkpoint_path):
                return None

            with open(checkpoint_path, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)

            logger.info(f"检查点已加载: {checkpoint_path}")
            return checkpoint_data

        except Exception as e:
            logger.error(f"加载检查点失败: {e}")
            return None

    def _update_progress(self, processed: int = 1, successful: bool = True) -> None:
        """更新进度"""
        if not self.progress:
            return

        self.progress.processed_records += processed
        if successful:
            self.progress.successful_records += processed
        else:
            self.progress.failed_records += processed

        # 每10条记录或每5分钟保存一次检查点
        should_save = (
            self.progress.processed_records % 10 == 0 or
            (time.time() - self.progress.last_checkpoint_time) > 300
        )

        if should_save:
            logger.info(f"进度更新: {self.progress.processed_records}/{self.progress.total_records} "
                       f"({self.progress.get_progress_percentage():.1f}%), "
                       f"成功: {self.progress.successful_records}, "
                       f"失败: {self.progress.failed_records}, "
                       f"预计剩余: {self.progress.get_estimated_remaining_time()/60:.1f}分钟")

    async def process_core_logic(
        self,
        report_code: str,
        dept_id: str
    ) -> CoreProcessResult:
        """
        执行核心处理逻辑
        
        Args:
            report_code: 报告代码（version）
            dept_id: 部门ID
            
        Returns:
            CoreProcessResult: 核心处理结果
        """
        start_time = time.time()
        result = CoreProcessResult()

        try:
            process_start_time = time.time()
            logger.info(f"🚀 开始核心处理: report_code={report_code}, dept_id={dept_id}, 开始时间={process_start_time:.3f}")

            # 1. 查询数据
            logger.info("1️⃣ 查询数据...")
            query_start_time = time.time()
            raw_records = await self._query_data(report_code, dept_id)
            query_end_time = time.time()
            logger.info(f"1️⃣ 查询数据完成: 记录数={len(raw_records)}, 耗时={query_end_time-query_start_time:.2f}s")

            # 初始化进度监控
            if self.enable_checkpoint:
                checkpoint_file = self._create_checkpoint_filename(report_code, dept_id)
                self.progress = DDBProcessProgress(
                    total_records=len(raw_records),
                    checkpoint_file=checkpoint_file
                )
                logger.info(f"📊 进度监控已启动: 总记录数={len(raw_records)}, 检查点文件={checkpoint_file}")
            result.total_records = len(raw_records)
            result.debug_info['raw_records_count'] = len(raw_records)
            
            if not raw_records:
                logger.warning("未找到匹配的记录")
                result.success = False
                return result
            
            logger.info(f"查询到 {len(raw_records)} 条记录")
            
            # 2. 批量向量搜索
            logger.info("2️⃣ 批量向量搜索...")
            vector_results = await self._batch_vector_search(raw_records)
            result.debug_info['vector_search_results'] = len(vector_results)
            
            # 3. 批量处理
            logger.info("3️⃣ 批量处理...")
            batch_start_time = time.time()
            processed_data = await self._batch_process_records(raw_records, vector_results)
            batch_end_time = time.time()
            logger.info(f"3️⃣ 批量处理完成: 处理记录数={len(processed_data)}, 耗时={batch_end_time-batch_start_time:.2f}s")
            result.debug_info['processed_batches'] = len(processed_data)

            # 4. 字段聚合
            logger.info("4️⃣ 字段聚合...")
            agg_start_time = time.time()
            aggregated_data = await self._aggregate_fields(processed_data)
            agg_end_time = time.time()
            logger.info(f"4️⃣ 字段聚合完成: 聚合记录数={len(aggregated_data)}, 耗时={agg_end_time-agg_start_time:.2f}s")
            result.debug_info['aggregated_records'] = len(aggregated_data)
            
            # 5. 生成最终输出
            logger.info("5️⃣ 生成最终输出...")
            final_data = await self._generate_final_output(aggregated_data)
            
            # 设置结果
            result.final_data = final_data
            result.processed_records = len(final_data)
            result.success = True
            
            logger.info(f"✅ 核心处理完成: 输出 {len(final_data)} 条记录")
            
        except Exception as e:
            error_msg = f"核心处理失败: {str(e)}"
            logger.error(error_msg)
            result.errors.append(error_msg)
            result.success = False
        
        result.processing_time = time.time() - start_time
        return result
    
    async def _query_data(
        self,
        report_code: str,
        dept_id: str
    ) -> List[Dict[str, Any]]:
        """查询数据"""
        try:
            # 使用DD CRUD查询
            from modules.knowledge.dd.crud import DDCrud
            dd_crud = DDCrud(self.rdb_client)
            
            # 1. 先查询post_distribution数据
            conditions = [{"version": report_code, "dept_id": dept_id}]
            records = await dd_crud.batch_query_post_distributions(
                conditions_list=conditions,
                batch_size=100,
                max_concurrency=5
            )

            logger.info(f"查询到 {len(records)} 条post_distribution记录")

            if not records:
                return []

            # 2. 提取所有的pre_distribution_id
            pre_distribution_ids = []
            for record in records:
                pre_id = record.get('pre_distribution_id')
                if pre_id:
                    pre_distribution_ids.append(pre_id)

            logger.info(f"提取到 {len(pre_distribution_ids)} 个pre_distribution_id")

            if not pre_distribution_ids:
                logger.warning("没有找到有效的pre_distribution_id")
                return records

            # 3. 根据pre_distribution_id查询pre_distribution表
            pre_conditions = [{"id": pre_id} for pre_id in pre_distribution_ids]
            pre_records = await dd_crud.batch_query_pre_distributions(
                conditions_list=pre_conditions,
                batch_size=100,
                max_concurrency=5
            )

            logger.info(f"查询到 {len(pre_records)} 条pre_distribution记录")

            # 4. 创建pre_distribution_id到pre记录的映射
            pre_record_map = {}
            for pre_record in pre_records:
                pre_id = pre_record.get('id')
                if pre_id:
                    pre_record_map[pre_id] = pre_record

            # 5. 将dr09和dr17合并到records中
            merged_records = []
            for record in records:
                pre_id = record.get('pre_distribution_id')
                if pre_id and pre_id in pre_record_map:
                    pre_record = pre_record_map[pre_id]
                    # 合并dr09和dr17
                    record['dr09'] = pre_record.get('dr09', '')
                    record['dr17'] = pre_record.get('dr17', '')
                else:
                    # 如果没有找到对应的pre记录，设置为空
                    record['dr09'] = ''
                    record['dr17'] = ''

                merged_records.append(record)

            logger.info(f"数据合并完成: {len(merged_records)} 条记录")

            # 6. 批量查询所有dept_id对应的table_ids
            unique_dept_ids = list(set(record.get('dept_id') for record in merged_records if record.get('dept_id')))
            logger.info(f"需要查询table_ids的dept_id数量: {len(unique_dept_ids)}")

            # 批量查询department relations
            dept_table_map = await self._batch_query_dept_table_relations(unique_dept_ids)

            # 7. 将table_ids添加到每个记录中
            for record in merged_records:
                dept_id = record.get('dept_id')
                table_ids = dept_table_map.get(dept_id, [str(i) for i in range(1, 95)])  # 默认值
                record['table_ids'] = table_ids

            # 8. 统计有效记录（dr09不为空）
            valid_records = [r for r in merged_records if r.get('dr09', '').strip()]
            logger.info(f"有效记录数（dr09不为空）: {len(valid_records)}")

            return merged_records
            logger.info(f"查询到 {len(records)} 条post_distribution记录")
            return records
            
        except Exception as e:
            logger.error(f"数据查询失败: {e}")
            return []

    async def _batch_query_dept_table_relations(self, dept_ids: List[str]) -> Dict[str, List[str]]:
        """批量查询部门对应的table_ids"""
        try:
            from modules.knowledge.dd.crud import DDCrud
            dd_crud = DDCrud(self.rdb_client)

            # 构建批量查询条件
            conditions_list = [{"dept_id": dept_id} for dept_id in dept_ids]

            # 使用新的批量查询方法
            relations = await dd_crud.batch_query_department_relations(
                conditions_list=conditions_list,
                batch_size=100,
                max_concurrency=5
            )

            logger.info(f"查询到 {len(relations)} 条department relation记录")

            # 构建dept_id到table_ids的映射
            dept_table_map = {}
            for relation in relations:
                dept_id = relation.get('dept_id')
                table_id = relation.get('table_id')

                if dept_id and table_id:
                    if dept_id not in dept_table_map:
                        dept_table_map[dept_id] = []
                    # 安全转换table_id为int，然后转为字符串
                    try:
                        table_id_int = int(table_id)
                        dept_table_map[dept_id].append(table_id_int)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"table_id转换失败: {table_id}, error: {e}")
                        continue

            # 为没有找到关联的dept_id设置默认值
            for dept_id in dept_ids:
                if dept_id not in dept_table_map:
                    logger.warning(f"dept_id={dept_id} 没有找到关联的table_ids，使用默认值")
                    dept_table_map[dept_id] = [str(i) for i in range(1, 95)]
                else:
                    logger.info(f"dept_id={dept_id} 对应的table_ids: {dept_table_map[dept_id]}")

            logger.info(f"批量查询完成: {len(dept_ids)}个dept_id, 总共{sum(len(table_ids) for table_ids in dept_table_map.values())}个关联关系")
            return dept_table_map

        except Exception as e:
            logger.error(f"批量查询department relations失败: {e}")
            # 出错时为所有dept_id设置默认值
            return {dept_id: [str(i) for i in range(1, 95)] for dept_id in dept_ids}
    
    async def _batch_vector_search(
        self,
        records: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """批量向量搜索（优化版本）"""
        if not self.vdb_client or not self.embedding_client:
            logger.warning("向量搜索客户端未配置，跳过向量搜索")
            return []
        
        try:
            # 提取查询文本
            search_queries = []
            for record in records:
                dr09 = record.get('dr09', '')
                dr17 = record.get('dr17', '')
                if dr09 or dr17:
                    search_queries.append({
                        'record_id': record.get('id'),
                        'dr09': dr09,
                        'dr17': dr17,
                        'query_text': f"{dr09} {dr17}".strip()
                    })
            
            if not search_queries:
                logger.warning("没有有效的查询文本")
                return []
            
            logger.info(f"准备批量向量搜索: {len(search_queries)} 个查询")
            
            # 使用现有的向量搜索仓库
            from modules.knowledge.dd.vector.repository import VectorRepository
            vector_repo = VectorRepository(self.vdb_client, self.embedding_client)
            
            # 构建批量搜索请求
            search_requests = []
            for query in search_queries:
                if query['query_text']:
                    search_requests.append({
                        'query_text': query['query_text'],
                        'field_code': 'dr09',  # 主要搜索字段
                        'limit': 10,
                        'min_score': 0.5
                    })
            
            # 执行批量搜索
            batch_results = await vector_repo.batch_search_similar_vectors(search_requests)
            
            logger.info(f"批量向量搜索完成: {len(batch_results)} 个结果集")
            return batch_results
            
        except Exception as e:
            logger.error(f"批量向量搜索失败: {e}")
            return []
    
    async def _batch_process_records(
        self,
        raw_records: List[Dict[str, Any]],
        vector_results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """批量处理记录（300条一批）"""
        processed_data = []
        
        # 分批处理
        total_batches = (len(raw_records) + self.batch_size - 1) // self.batch_size
        logger.info(f"🔄 开始分批处理: 总记录数={len(raw_records)}, 批次大小={self.batch_size}, 总批次数={total_batches}")

        for i in range(0, len(raw_records), self.batch_size):
            batch_num = i // self.batch_size + 1
            batch = raw_records[i:i + self.batch_size]
            batch_vector = vector_results[i:i + self.batch_size] if vector_results else []

            batch_start_time = time.time()
            logger.info(f"🔄 处理批次 {batch_num}/{total_batches}: {len(batch)} 条记录, 开始时间={batch_start_time:.3f}")
            
            # 处理当前批次
            batch_result = await self._process_single_batch(batch, batch_vector)
            processed_data.extend(batch_result)
        
        return processed_data
    
    async def _process_single_batch(
        self,
        batch_records: List[Dict[str, Any]],
        batch_vector_results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """处理单个批次"""
        batch_start_time = time.time()  # 在方法开始时定义
        try:
            # 使用现有的Pipeline集成器
            from modules.dd_submission.dd_b.core.pipeline_integrator import PipelineIntegrator
            from modules.knowledge.metadata.crud import MetadataCrud

            metadata_crud = MetadataCrud(self.rdb_client)
            pipeline_integrator = PipelineIntegrator(metadata_crud)
            
            # 并发处理批次中的记录
            semaphore = asyncio.Semaphore(10)  # 15个并发
            
            async def process_single_record(record: Dict[str, Any]) -> Dict[str, Any]:
                async with semaphore:
                    try:
                        # 创建Pipeline请求
                        from modules.dd_submission.dd_b.core.pipeline_integrator import PipelineRequest
                        
                        # 确保user_question不为空
                        user_question = record.get('dr09', '').strip()
                        hint = record.get('dr17', '').strip()

                        # 如果dr09为空，跳过这条记录
                        if not user_question:
                            logger.warning(f"记录 {record.get('id')} 的dr09为空，跳过处理")
                            return self._create_empty_result(record)

                        # 使用记录中预先查询好的table_ids
                        table_ids = record.get('table_ids', [str(i) for i in range(1, 95)])

                        request = PipelineRequest(
                            record_id=record.get('id'),
                            table_ids=table_ids,
                            user_question=user_question,
                            hint=hint
                        )
                        
                        # 执行Pipeline
                        pipeline_result = await pipeline_integrator.execute_pipeline(request)
                        
                        # 映射字段
                        if pipeline_result.execution_success:
                            return await self._map_pipeline_result(record, pipeline_result)
                        else:
                            logger.warning(f"Pipeline执行失败: record_id={record.get('id')}")
                            return self._create_empty_result(record)
                            
                    except Exception as e:
                        logger.error(f"记录处理失败: record_id={record.get('id')}, error={e}")
                        return self._create_empty_result(record)
            
            # 并发处理所有记录，添加超时保护
            tasks = [process_single_record(record) for record in batch_records]

            # 计算批次超时时间：每条记录最多1小时，但至少2小时
            batch_timeout = max(7200, len(batch_records) * 3600)  # 最少2小时，每条最多1小时

            try:
                logger.info(f"开始批次处理: {len(batch_records)}条记录, 超时限制: {batch_timeout/3600:.1f}小时")
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=batch_timeout
                )

                # 过滤有效结果并更新进度
                valid_results = []
                for i, result in enumerate(results):
                    if isinstance(result, dict):
                        valid_results.append(result)
                        self._update_progress(1, successful=True)
                    elif isinstance(result, Exception):
                        logger.error(f"批次处理异常: {result}")
                        self._update_progress(1, successful=False)
                    else:
                        logger.warning(f"未知结果类型: {type(result)}")
                        self._update_progress(1, successful=False)

                batch_end_time = time.time()
                batch_duration = batch_end_time - batch_start_time
                logger.info(f"✅ 单批次处理完成: 成功={len(valid_results)}/{len(batch_records)}, 耗时={batch_duration:.2f}s, 结束时间={batch_end_time:.3f}")

                # 保存检查点
                if self.enable_checkpoint and len(valid_results) > 0:
                    self._save_checkpoint(valid_results)

                return valid_results

            except asyncio.TimeoutError:
                logger.error(f"批次处理超时: {batch_timeout/3600:.1f}小时, 记录数: {len(batch_records)}")
                # 超时时也要更新进度（标记为失败）
                for record in batch_records:
                    self._update_progress(1, successful=False)
                return []
            
        except Exception as e:
            logger.error(f"批次处理失败: {e}")
            return []
    
    async def _map_pipeline_result(
        self,
        original_record: Dict[str, Any],
        pipeline_result: Any
    ) -> Dict[str, Any]:
        """映射Pipeline结果到字段"""
        try:
            # 使用Pipeline字段映射器
            from modules.dd_submission.dd_b.utils.pipeline_field_mapper import PipelineFieldMapper
            from modules.knowledge.metadata.crud import MetadataCrud

            metadata_crud = MetadataCrud(self.rdb_client)
            field_mapper = PipelineFieldMapper(metadata_crud)
            
            # 准备Pipeline结果数据
            if hasattr(pipeline_result, '__dict__'):
                # 如果是PipelineResult对象，转换为字典
                pipeline_data = pipeline_result.__dict__.copy()

                # 从request中提取record_id
                if hasattr(pipeline_result, 'request') and hasattr(pipeline_result.request, 'record_id'):
                    pipeline_data['record_id'] = pipeline_result.request.record_id

            else:
                # 如果已经是字典，直接使用
                pipeline_data = pipeline_result.copy() if isinstance(pipeline_result, dict) else {}

            # 确保有record_id字段
            if 'record_id' not in pipeline_data:
                pipeline_data['record_id'] = original_record.get('id')

            # 创建一个简单的对象来满足字段映射器的期望
            class RecordWrapper:
                def __init__(self, record_dict):
                    self.id = record_dict.get('id')
                    self.__dict__.update(record_dict)

            record_wrapper = RecordWrapper(original_record)

            # 执行字段映射
            mapping_result = await field_mapper.map_single_record(
                pipeline_result=pipeline_data,
                original_record=record_wrapper,
                keep_raw_format=True
            )
            
            if mapping_result.mapping_success:
                # 转换为字符串格式
                string_result = mapping_result.to_string_format()
                string_result['record_id'] = str(original_record.get('id'))
                string_result['dept_id'] = original_record.get('dept_id')
                string_result['version'] = original_record.get('version')
                return string_result
            else:
                return self._create_empty_result(original_record)
                
        except Exception as e:
            logger.error(f"字段映射失败: {e}")
            return self._create_empty_result(original_record)
    

    def _create_empty_result(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """创建空结果"""
        return {
            'record_id': str(record.get('id')),
            'dept_id': record.get('dept_id'),
            'version': record.get('version'),
            'status': 'failed'
        }
    
    async def _aggregate_fields(
        self,
        processed_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """字段聚合"""
        try:
            # 分离普通记录和RANGE记录
            normal_records = []
            range_records = []
            
            for record in processed_data:
                if record.get('submission_type') == 'RANGE':
                    range_records.append(record)
                else:
                    normal_records.append(record)
            
            logger.info(f"记录分类: 普通={len(normal_records)}, RANGE={len(range_records)}")
            
            # 如果有RANGE记录，进行聚合
            if range_records and normal_records:
                # 使用字段聚合器
                from modules.dd_submission.dd_b.core.field_aggregator import FieldAggregator
                aggregator = FieldAggregator()
                
                # 这里需要将processed_data转换为PipelineFieldMappingResult格式
                # 暂时直接返回所有记录
                pass
            
            return processed_data
            
        except Exception as e:
            logger.error(f"字段聚合失败: {e}")
            return processed_data
    
    async def _generate_final_output(
        self,
        aggregated_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """生成最终输出格式"""
        try:
            final_output = []
            
            for record in aggregated_data:
                # 确保输出格式一致
                output_record = {
                    'record_id': record.get('record_id'),
                    'dept_id': record.get('dept_id'),
                    'version': record.get('version'),
                    'submission_type': record.get('submission_type', 'NORMAL'),

                    # 完整的BDR字段 (BDR05-BDR17)
                    'bdr05': record.get('bdr05', ''),
                    'bdr06': record.get('bdr06', ''),
                    'bdr07': record.get('bdr07', ''),
                    'bdr08': record.get('bdr08', ''),
                    'bdr09': record.get('bdr09', ''),
                    'bdr10': record.get('bdr10', ''),
                    'bdr11': record.get('bdr11', ''),
                    'bdr12': record.get('bdr12', ''),
                    'bdr13': record.get('bdr13', ''),
                    'bdr14': record.get('bdr14', ''),
                    'bdr15': record.get('bdr15', ''),
                    'bdr16': record.get('bdr16', ''),
                    'bdr17': record.get('bdr17', ''),

                    # 完整的SDR字段 (SDR01-SDR15)，包含原始数据映射
                    'sdr01': record.get('sdr01', record.get('dr01', '')),  # dr01映射到sdr01
                    'sdr02': record.get('sdr02', ''),  # dr02映射到sdr02
                    'sdr03': record.get('sdr03', ''),  # dr03映射到sdr03
                    'sdr04': record.get('sdr04', ''),  # dr04映射到sdr04
                    'sdr05': record.get('sdr05', ''),
                    'sdr06': record.get('sdr06', ''),
                    'sdr07': record.get('sdr07', ''),  # dr07映射到sdr07
                    'sdr08': record.get('sdr08', ''),
                    'sdr09': record.get('sdr09', ''),
                    'sdr10': record.get('sdr10', ''),
                    'sdr11': record.get('sdr11', ''),  # dr11映射到sdr11
                    'sdr12': record.get('sdr12', ''),
                    'sdr13': record.get('sdr13', ''),  # dr13映射到sdr13
                    'sdr14': record.get('sdr14', ''),  # dr14映射到sdr14
                    'sdr15': record.get('sdr15', ''),  # dr15映射到sdr15

                    # 状态信息
                    'processing_status': record.get('status', 'success'),
                    'processing_time': record.get('processing_time', 0.0)
                }

                final_output.append(output_record)
            
            logger.info(f"最终输出生成完成: {len(final_output)} 条记录")
            return final_output
            
        except Exception as e:
            logger.error(f"最终输出生成失败: {e}")
            return aggregated_data


async def process_dd_b_core(
    rdb_client: Any,
    report_code: str,
    dept_id: str,
    vdb_client: Any = None,
    embedding_client: Any = None,
    batch_size: int = 300
) -> CoreProcessResult:
    """
    DD-B核心处理便捷函数
    
    Args:
        rdb_client: RDB客户端
        report_code: 报告代码
        dept_id: 部门ID
        vdb_client: VDB客户端
        embedding_client: 嵌入客户端
        batch_size: 批处理大小
        
    Returns:
        CoreProcessResult: 核心处理结果
    """
    processor = DDBCoreProcessor(
        rdb_client=rdb_client,
        vdb_client=vdb_client,
        embedding_client=embedding_client,
        batch_size=batch_size
    )
    
    return await processor.process_core_logic(report_code, dept_id)

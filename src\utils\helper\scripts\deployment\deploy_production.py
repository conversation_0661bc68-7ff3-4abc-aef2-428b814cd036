#!/usr/bin/env python3
"""
生产环境部署脚本

使用方法:
    # 方式1: 设置环境变量后运行
    export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
    python scripts/deployment/deploy_production.py

    # 方式2: 直接运行（脚本会自动设置路径）
    python scripts/deployment/deploy_production.py --auto-path
"""

import argparse
import asyncio
import os
import sys
from pathlib import Path


def setup_environment_path():
    """
    设置环境变量方式的路径
    
    这种方式通过PYTHONPATH环境变量来添加src目录
    """
    # 获取项目根目录
    script_dir = Path(__file__).resolve().parent
    project_root = script_dir.parent.parent
    src_dir = project_root / "src"
    
    # 检查src目录是否存在
    if not src_dir.exists():
        raise FileNotFoundError(f"找不到src目录: {src_dir}")
    
    # 添加到PYTHONPATH环境变量
    current_pythonpath = os.environ.get('PYTHONPATH', '')
    src_path = str(src_dir)
    
    if src_path not in current_pythonpath:
        if current_pythonpath:
            os.environ['PYTHONPATH'] = f"{current_pythonpath}:{src_path}"
        else:
            os.environ['PYTHONPATH'] = src_path
        
        # 同时添加到sys.path（当前进程立即生效）
        sys.path.insert(0, src_path)
        print(f"✅ 已设置PYTHONPATH: {src_path}")


async def deploy_application():
    """
    部署应用程序
    """
    print("🚀 开始生产环境部署...")
    
    try:
        # 导入需要的模块
        from service import get_client
        from utils.common.logger import setup_enterprise_logger
        
        # 设置生产环境日志
        setup_enterprise_logger(
            level="INFO",
            service_name="production-deployment"
        )
        
        # 检查数据库连接
        print("🔍 检查数据库连接...")
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        print("✅ 数据库连接正常")
        
        # 检查所有模块是否可以正常导入
        print("🔍 检查模块导入...")
        from modules.knowledge.dd import DDCrud
        from modules.knowledge.metadata import MetadataCrud
        from pipeline import PipelineManager
        print("✅ 所有模块导入正常")
        
        # 部署步骤
        deployment_steps = [
            "停止旧服务",
            "备份数据库", 
            "更新代码",
            "运行数据库迁移",
            "启动新服务",
            "健康检查"
        ]
        
        for i, step in enumerate(deployment_steps, 1):
            print(f"📋 步骤 {i}/{len(deployment_steps)}: {step}")
            # 这里添加实际的部署逻辑
            await asyncio.sleep(1)  # 模拟部署时间
            print(f"   ✅ {step} 完成")
        
        print("🎉 生产环境部署完成！")
        
    except Exception as e:
        print(f"❌ 部署失败: {e}")
        raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生产环境部署工具')
    parser.add_argument('--auto-path', 
                       action='store_true',
                       help='自动设置Python路径')
    parser.add_argument('--check-only', 
                       action='store_true',
                       help='只检查环境，不执行部署')
    
    args = parser.parse_args()
    
    # 如果指定了auto-path，自动设置路径
    if args.auto_path:
        setup_environment_path()
    
    # 检查PYTHONPATH是否包含src
    pythonpath = os.environ.get('PYTHONPATH', '')
    if 'src' not in pythonpath and not args.auto_path:
        print("⚠️  警告: PYTHONPATH中未包含src目录")
        print("请使用以下方式之一:")
        print("1. export PYTHONPATH=\"${PYTHONPATH}:$(pwd)/src\"")
        print("2. python scripts/deployment/deploy_production.py --auto-path")
        sys.exit(1)
    
    if args.check_only:
        print("🔍 环境检查完成，路径设置正确")
        return
    
    # 执行部署
    asyncio.run(deploy_application())


if __name__ == '__main__':
    main() 
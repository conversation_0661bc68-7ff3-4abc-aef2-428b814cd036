"""
DD-B完整集成示例

这个脚本演示了从Pipeline字段映射到批量处理的完整集成流程。
包含了所有核心组件的协同工作。
"""

import asyncio
import logging
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_complete_integration():
    """示例：完整的集成流程"""
    print("\n=== DD-B完整集成示例 ===")
    
    try:
        # 模拟真实的集成流程
        print("1. 初始化所有组件...")
        
        # 在真实环境中的初始化代码：
        """
        from service import get_client
        from modules.knowledge.dd.crud import DDCRUD
        from modules.knowledge.metadata.crud import MetadataCRUD
        from modules.dd_submission.dd_b.core.pipeline_integrator import PipelineIntegrator
        from modules.dd_submission.dd_b.core.batch_processor import BatchProcessor
        from modules.dd_submission.dd_b.core.field_aggregator import FieldAggregator
        from modules.dd_submission.dd_b.utils.pipeline_field_mapper import PipelineFieldMapper
        
        # 获取客户端
        rdb_client = await get_client("rdb")
        
        # 创建CRUD对象
        dd_crud = DDCRUD(rdb_client)
        metadata_crud = MetadataCRUD(rdb_client)
        
        # 创建Pipeline集成器
        pipeline_integrator = PipelineIntegrator(metadata_crud)
        
        # 创建批量处理器（内置字段映射器和聚合器）
        batch_processor = BatchProcessor(
            rdb_client=rdb_client,
            dd_crud=dd_crud,
            pipeline_integrator=pipeline_integrator,
            metadata_crud=metadata_crud,
            max_workers=15,
            batch_size=100,
            max_concurrency=5
        )
        """
        
        print("2. 执行批量处理...")
        
        # 模拟批量处理调用
        """
        processing_result = await batch_processor.process_batch_records(
            report_code="S71_ADS_RELEASE_V0",
            dept_id="30239"
        )
        """
        
        # 模拟处理结果
        from modules.dd_submission.dd_b.core.batch_processor import BatchProcessingResult
        processing_result = BatchProcessingResult()
        
        # 模拟大批量数据处理结果
        processing_result.total_records = 3500
        processing_result.normal_records = 3499
        processing_result.range_records = 1
        processing_result.processed_records = 3450
        processing_result.failed_records = 49
        processing_result.processing_time = 285.7
        processing_result.pipeline_time = 245.3
        processing_result.aggregation_time = 12.8
        
        # 模拟聚合结果
        processing_result.normal_results = [
            {
                'record_id': str(i),
                'dept_id': '30239',
                'version': 'S71_ADS_RELEASE_V0',
                'bdr09': f"['table_{i%5+1}', 'table_{i%3+1}']",
                'bdr10': f"['表{i%5+1}', '表{i%3+1}']",
                'bdr11': f"{{'col_{i%4+1}': 'table_{i%5+1}', 'col_{i%2+1}': 'table_{i%3+1}'}}",
                'bdr16': f"表范围：['table_{i%5+1}', 'table_{i%3+1}']",
                'sdr05': f"['table_{i%5+1}', 'table_{i%3+1}']",
                'sdr06': f"['表{i%5+1}', '表{i%3+1}']",
                'sdr08': f"{{'col_{i%4+1}': 'table_{i%5+1}', 'col_{i%2+1}': 'table_{i%3+1}'}}",
                'sdr09': f"{{'col_{i%4+1}': '列{i%4+1}', 'col_{i%2+1}': '列{i%2+1}'}}",
                'sdr10': f"SELECT * FROM table_{i%5+1} JOIN table_{i%3+1}",
                'sdr12': f"table_{i%5+1}.id = table_{i%3+1}.id" if i % 2 == 0 else f"table_{i%3+1}.ref = table_{i%5+1}.ref"
            }
            for i in range(1, 3451)
        ]
        
        # 模拟RANGE记录（聚合结果）
        processing_result.range_result = {
            'record_id': '3500',
            'dept_id': '30239',
            'version': 'S71_ADS_RELEASE_V0',
            'submission_type': 'RANGE',
            'bdr09': "['table_1', 'table_2', 'table_3', 'table_4', 'table_5']",
            'bdr10': "['表1', '表2', '表3', '表4', '表5']",
            'bdr11': "{'col_1': 'table_1', 'col_2': 'table_2', 'col_3': 'table_3', 'col_4': 'table_4'}",
            'bdr16': "表范围：['table_1', 'table_2', 'table_3', 'table_4', 'table_5']",
            'sdr05': "['table_1', 'table_2', 'table_3', 'table_4', 'table_5']",
            'sdr06': "['表1', '表2', '表3', '表4', '表5']",
            'sdr08': "{'col_1': 'table_1', 'col_2': 'table_2', 'col_3': 'table_3', 'col_4': 'table_4'}",
            'sdr09': "{'col_1': '列1', 'col_2': '列2', 'col_3': '列3', 'col_4': '列4'}",
            'sdr10': "",  # RANGE记录的sdr10为空
            'sdr12': "['table_1.id = table_2.id']"  # 频次>1/2的JOIN条件
        }
        
        print("3. 处理结果分析...")
        
        # 输出处理统计
        print(f"\n📊 处理统计:")
        print(f"  总记录数: {processing_result.total_records:,}")
        print(f"  普通记录: {processing_result.normal_records:,}")
        print(f"  RANGE记录: {processing_result.range_records}")
        print(f"  成功处理: {processing_result.processed_records:,}")
        print(f"  失败记录: {processing_result.failed_records}")
        print(f"  成功率: {processing_result.processed_records/processing_result.total_records*100:.1f}%")
        
        print(f"\n⏱️ 性能统计:")
        print(f"  总耗时: {processing_result.processing_time:.1f}s")
        print(f"  Pipeline耗时: {processing_result.pipeline_time:.1f}s")
        print(f"  聚合耗时: {processing_result.aggregation_time:.1f}s")
        print(f"  平均每条: {processing_result.processing_time*1000/processing_result.total_records:.1f}ms")
        
        print("4. 获取最终结果...")
        
        # 合并最终结果
        """
        final_results = await batch_processor.get_final_results(processing_result)
        """
        
        final_results = processing_result.normal_results + [processing_result.range_result]
        
        print(f"\n📋 最终结果:")
        print(f"  总记录数: {len(final_results):,}")
        print(f"  普通记录: {len(processing_result.normal_results):,}")
        print(f"  RANGE记录: {1 if processing_result.range_result else 0}")
        
        print("5. 结果验证...")
        
        # 验证字段完整性
        sample_normal = processing_result.normal_results[0]
        range_record = processing_result.range_result
        
        expected_fields = ['bdr09', 'bdr10', 'bdr11', 'bdr16', 'sdr05', 'sdr06', 'sdr08', 'sdr09', 'sdr10', 'sdr12']
        
        print(f"\n✅ 字段验证:")
        for field in expected_fields:
            normal_has = field in sample_normal
            range_has = field in range_record
            print(f"  {field}: 普通记录{'✓' if normal_has else '✗'} RANGE记录{'✓' if range_has else '✗'}")
        
        print("6. 聚合效果展示...")
        
        # 展示聚合效果
        print(f"\n🔄 聚合效果:")
        print(f"  BDR09聚合: 从3450个记录聚合出5个唯一表名")
        print(f"  BDR10聚合: 从3450个记录聚合出5个唯一表中文名")
        print(f"  BDR11聚合: 从3450个记录聚合出4个唯一字段")
        print(f"  SDR09聚合: 从3450个记录聚合出4个唯一字段中文名")
        print(f"  SDR12聚合: 从3450个候选值中筛选出1个高频JOIN条件")
        
        print("7. 数据格式验证...")
        
        # 验证数据格式
        import ast
        
        print(f"\n📝 格式验证:")
        try:
            # 验证list格式
            bdr09_parsed = ast.literal_eval(range_record['bdr09'])
            print(f"  BDR09格式: {'✓' if isinstance(bdr09_parsed, list) else '✗'} (list)")
            
            # 验证dict格式
            bdr11_parsed = ast.literal_eval(range_record['bdr11'])
            print(f"  BDR11格式: {'✓' if isinstance(bdr11_parsed, dict) else '✗'} (dict)")
            
            # 验证SDR10为空
            sdr10_empty = range_record['sdr10'] == ""
            print(f"  SDR10为空: {'✓' if sdr10_empty else '✗'}")
            
        except Exception as e:
            print(f"  格式验证失败: {e}")
        
        print("\n🎉 完整集成示例执行成功!")
        return final_results
        
    except Exception as e:
        logger.error(f"完整集成示例失败: {e}")
        raise


async def example_real_world_usage():
    """示例：真实世界的使用场景"""
    print("\n=== 真实世界使用场景 ===")
    
    # 真实的使用代码模板
    usage_template = '''
# 真实环境中的完整使用流程

async def process_dd_b_batch(report_code: str, dept_id: str):
    """处理DD-B批量数据的完整流程"""
    
    # 1. 初始化组件
    rdb_client = await get_client("rdb")
    dd_crud = DDCRUD(rdb_client)
    metadata_crud = MetadataCRUD(rdb_client)
    pipeline_integrator = PipelineIntegrator(metadata_crud)
    
    batch_processor = BatchProcessor(
        rdb_client=rdb_client,
        dd_crud=dd_crud,
        pipeline_integrator=pipeline_integrator,
        metadata_crud=metadata_crud,
        max_workers=15,
        batch_size=100,
        max_concurrency=5
    )
    
    try:
        # 2. 执行批量处理
        processing_result = await batch_processor.process_batch_records(
            report_code=report_code,
            dept_id=dept_id
        )
        
        # 3. 检查处理结果
        if processing_result.failed_records > 0:
            logger.warning(f"有 {processing_result.failed_records} 条记录处理失败")
        
        # 4. 获取最终结果
        final_results = await batch_processor.get_final_results(processing_result)
        
        # 5. 保存或返回结果
        return {
            "status": "success",
            "total_records": len(final_results),
            "processing_time": processing_result.processing_time,
            "results": final_results
        }
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        return {
            "status": "error",
            "error": str(e),
            "results": []
        }

# 使用示例
result = await process_dd_b_batch("S71_ADS_RELEASE_V0", "30239")
print(f"处理完成: {result['status']}, 记录数: {result['total_records']}")
'''
    
    print("真实使用代码模板:")
    print(usage_template)
    
    print("\n关键要点:")
    print("1. 组件初始化顺序很重要")
    print("2. 错误处理要完善")
    print("3. 资源清理要及时")
    print("4. 日志记录要详细")
    print("5. 性能监控要到位")


async def main():
    """主函数"""
    print("DD-B完整集成示例")
    print("=" * 60)
    
    try:
        await example_complete_integration()
        await example_real_world_usage()
        
        print("\n" + "=" * 60)
        print("所有示例执行完成!")
        
    except Exception as e:
        logger.error(f"示例执行失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())

-- ==========================================
-- 临时测试表结构
-- 创建时间: 2025-07-25
-- 数据库: MySQL
-- 描述: 用于测试的临时表结构
-- ==========================================

-- 删除已存在的表
DROP TABLE IF EXISTS tmptmp2;
DROP TABLE IF EXISTS tmptmp;

-- ==========================================
-- 1. 创建tmptmp表 - 主表
-- ==========================================
CREATE TABLE tmptmp (
    id BIGINT AUTO_INCREMENT COMMENT '主键ID'
);

-- 添加表注释
ALTER TABLE tmptmp COMMENT '临时测试表 - 用于开发和测试目的';

-- ==========================================
-- 2. 创建tmptmp2表 - 关联表
-- ==========================================
CREATE TABLE tmptmp2 (
    id BIGINT AUTO_INCREMENT COMMENT '主键ID'
);

-- 添加表注释
ALTER TABLE tmptmp2 COMMENT '临时测试关联表 - 用于测试关联关系';

-- ==========================================
-- 3. 插入测试数据
-- ==========================================

-- 创建全文索引示例（如果需要）
-- ALTER TABLE tmptmp ADD FULLTEXT INDEX ft_tmptmp_name_desc (name, description);

-- ==========================================
-- 10. 测试查询
-- ==========================================

-- 测试视图查询
-- SELECT * FROM v_tmptmp_detail;

-- 测试函数
-- SELECT id, name, fn_tmptmp_active_detail_count(id) as active_details FROM tmptmp;

-- 测试存储过程
-- CALL sp_tmptmp_insert_with_details('存储过程测试', '通过存储过程插入的记录', 1005, 1, '{"type": "sp_test"}', '{"detail": "test"}');

-- 显示表结构
-- DESCRIBE tmptmp;
-- DESCRIBE tmptmp2;

-- 显示索引信息
-- SHOW INDEX FROM tmptmp;
-- SHOW INDEX FROM tmptmp2;

-- ==========================================
-- 结束
-- ==========================================
"""
DD SQL推荐服务测试脚本
"""

import asyncio
import logging
import sys
import os
from app.dd_sql_recommend.services import DDSQLRecommendService
from app.dd_sql_recommend.models import (
    SQLRecommendRequest,
    QuestionRecommendRequest,
    SQLGenerateRequest,
    SingleSQLGenerateRequest,
    SQLIntegrationRequest
)
from service import get_client

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def dd_sql_recommend_service():
    """测试DD SQL推荐服务"""
    # 使用真实客户端进行测试
    rdb_client = await get_client("database.rdbs.mysql")
    vdb_client = await get_client("database.vdbs.pgvector")
    embedding_client = await get_client("model.embeddings.moka-m3e-base")
    llm_client = await get_client("model.llms.opentrek")
    
    service = DDSQLRecommendService(
        rdb_client=rdb_client,
        vdb_client=vdb_client,
        embedding_client=embedding_client,
        llm_client=llm_client
    )
    
    # 测试数据
    report_code = "G0107_release_v1.0"
    dept_id = "114"
    entry_id = "SUBMIT_001"
    
    try:
        # 测试推荐SQL接口
        logger.info("测试推荐SQL接口...")
        sql_request = SQLRecommendRequest(
            report_code=report_code,
            dept_id=dept_id,
            entry_id=entry_id
        )
        recommended_sql = await service.recommend_sql(sql_request)
        logger.info(f"推荐SQL结果: {recommended_sql}")
        
        # 测试推荐问题接口
        logger.info("测试推荐问题接口...")
        question_request = QuestionRecommendRequest(
            report_code=report_code,
            dept_id=dept_id,
            entry_id=entry_id
        )
        question_list = await service.recommend_questions(question_request)
        logger.info(f"推荐问题列表: {question_list}")
        
        # 测试单个SQL生成接口
        logger.info("测试单个SQL生成接口...")
        question = "需要从客户表获取基本信息"
        single_sql_request = SingleSQLGenerateRequest(
            report_code=report_code,
            dept_id=dept_id,
            entry_id=entry_id,
            question=question
        )
        single_sql = await service.generate_single_sql(single_sql_request)
        logger.info(f"单个SQL生成结果: {single_sql}")
        
        # 测试批量SQL生成接口
        logger.info("测试批量SQL生成接口...")
        if question_list:
            sql_generate_request = SQLGenerateRequest(
                report_code=report_code,
                dept_id=dept_id,
                entry_id=entry_id,
                question_list=question_list
            )
            sql_list = await service.generate_sql_list(sql_generate_request)
            logger.info(f"批量SQL生成结果: {sql_list}")
        
        # 测试SQL整合接口
        logger.info("测试SQL整合接口...")
        if question_list:
            integration_request = SQLIntegrationRequest(
                report_code=report_code,
                dept_id=dept_id,
                entry_id=entry_id,
                question_list=question_list,
                sql_list=[
                    "SELECT * FROM customer",
                    "SELECT * FROM transaction",
                    "SELECT * FROM account"
                ]
            )
            integrated_sql = await service.integrate_sql(integration_request)
            logger.info(f"SQL整合结果: {integrated_sql}")
        
        logger.info("所有测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    # 运行测试
    asyncio.run(dd_sql_recommend_service())
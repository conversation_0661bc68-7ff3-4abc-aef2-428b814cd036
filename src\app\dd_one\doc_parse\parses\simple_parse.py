from doc_parse.base_parse.base_document_parse import BaseDocumentParser
from doc_parse.parses.simple_utils.dd_read_old import execute_processing_logic, extraction_report


class SimpleExcelParser(BaseDocumentParser):
    def document_unparsing(self, metrics_file: str, description_file: str, sheet_names: str):
        output = execute_processing_logic(description_file, metrics_file, sheet_names)
        return output

    def extraction_report(self, metrics_file: str, description_file: str, sheet_names: str) -> dict:
        output = extraction_report(description_file, metrics_file, sheet_names)
        return output

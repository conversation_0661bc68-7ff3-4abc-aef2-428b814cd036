"""
MySQL数据库异常处理

基于Universal架构设计的MySQL专用异常体系
提供完整的错误包装和适配功能
"""

from typing import Optional, Any, Dict


class MySQLError(Exception):
    """MySQL客户端基础异常"""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None, 
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.original_error = original_error
        self.context = context or {}
    
    def __str__(self) -> str:
        base_msg = super().__str__()
        if self.original_error:
            base_msg += f" (Original: {self.original_error})"
        return base_msg


class MySQLConnectionError(MySQLError):
    """MySQL连接相关错误"""
    pass


class MySQLConfigurationError(MySQLError):
    """MySQL配置相关错误"""
    pass


class MySQLQueryError(MySQLError):
    """MySQL查询执行相关错误"""
    pass


class MySQLTransactionError(MySQLError):
    """MySQL事务管理相关错误"""
    pass


class MySQLUnsupportedOperationError(MySQLError):
    """MySQL不支持的操作错误"""
    pass


class MySQLDataValidationError(MySQLError):
    """MySQL数据验证相关错误"""
    pass


class MySQLPerformanceWarning(UserWarning):
    """MySQL性能相关警告"""
    pass


class MySQLTimeoutError(MySQLError):
    """MySQL操作超时错误"""
    pass


class MySQLPoolError(MySQLError):
    """MySQL连接池相关错误"""
    pass


class MySQLCacheError(MySQLError):
    """MySQL缓存操作相关错误"""
    pass


class MySQLAuthenticationError(MySQLError):
    """MySQL认证相关错误"""
    pass


class MySQLPermissionError(MySQLError):
    """MySQL权限相关错误"""
    pass


class MySQLResourceExhaustedError(MySQLError):
    """MySQL资源耗尽错误"""
    pass


def wrap_mysql_error(original_error: Exception, operation: str = "",
                     context: Optional[Dict[str, Any]] = None) -> MySQLError:
    """
    将MySQL特定错误包装为统一异常

    Args:
        original_error: 原始MySQL错误
        operation: 导致错误的操作
        context: 额外的上下文信息

    Returns:
        适当的MySQL异常
    """
    error_type = type(original_error).__name__
    error_msg = str(original_error).lower()
    message = f"MySQL error during {operation}: {original_error}" if operation else str(original_error)

    # 映射常见MySQL错误到统一异常
    if "connection" in error_type.lower() or "connect" in error_msg:
        return MySQLConnectionError(message, original_error, context)
    elif "timeout" in error_type.lower() or "timeout" in error_msg:
        return MySQLTimeoutError(message, original_error, context)
    elif "pool" in error_type.lower() or "pool" in error_msg:
        return MySQLPoolError(message, original_error, context)
    elif "integrity" in error_type.lower() or "constraint" in error_msg or "duplicate" in error_msg:
        return MySQLDataValidationError(message, original_error, context)
    elif "syntax" in error_msg or "sql" in error_type.lower() or "1064" in error_msg:
        return MySQLQueryError(message, original_error, context)
    elif "transaction" in error_type.lower() or "rollback" in error_msg or "deadlock" in error_msg:
        return MySQLTransactionError(message, original_error, context)
    elif "auth" in error_type.lower() or "authentication" in error_msg or "1045" in error_msg:
        return MySQLAuthenticationError(message, original_error, context)
    elif "permission" in error_type.lower() or "access denied" in error_msg or "1142" in error_msg:
        return MySQLPermissionError(message, original_error, context)
    elif "resource" in error_msg or "exhausted" in error_msg or "limit" in error_msg or "1040" in error_msg:
        return MySQLResourceExhaustedError(message, original_error, context)
    elif "table" in error_msg and "exist" in error_msg:
        return MySQLQueryError(message, original_error, context)
    elif "column" in error_msg and ("unknown" in error_msg or "doesn't exist" in error_msg):
        return MySQLQueryError(message, original_error, context)
    else:
        return MySQLError(message, original_error, context)


def handle_mysql_error(func):
    """
    装饰器：自动包装MySQL错误

    Usage:
        @handle_mysql_error
        async def some_mysql_operation(self):
            # MySQL operations
    """
    async def async_wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            if isinstance(e, MySQLError):
                raise
            raise wrap_mysql_error(e, func.__name__)

    def sync_wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if isinstance(e, MySQLError):
                raise
            raise wrap_mysql_error(e, func.__name__)

    import asyncio
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper


class MySQLErrorContext:
    """MySQL错误上下文构建器，用于更好的错误报告"""

    def __init__(self):
        self.context = {}

    def add_table(self, table_name: str) -> 'MySQLErrorContext':
        self.context['table'] = table_name
        return self

    def add_query(self, query: str) -> 'MySQLErrorContext':
        self.context['query'] = query
        return self

    def add_params(self, params: Dict[str, Any]) -> 'MySQLErrorContext':
        self.context['params'] = params
        return self

    def add_operation(self, operation: str) -> 'MySQLErrorContext':
        self.context['operation'] = operation
        return self

    def add_connection_info(self, host: str, database: str) -> 'MySQLErrorContext':
        self.context['connection'] = {'host': host, 'database': database}
        return self

    def add_transaction_info(self, isolation_level: str) -> 'MySQLErrorContext':
        self.context['transaction'] = {'isolation_level': isolation_level}
        return self

    def build(self) -> Dict[str, Any]:
        return self.context.copy()


# MySQL错误码映射
MYSQL_ERROR_CODES = {
    1040: "Too many connections",
    1042: "Can't get hostname for your address",
    1044: "Access denied for user to database",
    1045: "Access denied for user (using password)",
    1049: "Unknown database",
    1050: "Table already exists",
    1051: "Unknown table",
    1054: "Unknown column in field list",
    1062: "Duplicate entry for key",
    1064: "SQL syntax error",
    1142: "Command denied to user for table",
    1146: "Table doesn't exist",
    1213: "Deadlock found when trying to get lock",
    1317: "Query execution was interrupted",
    2003: "Can't connect to MySQL server",
    2006: "MySQL server has gone away",
    2013: "Lost connection to MySQL server during query",
}


def get_mysql_error_description(error_code: int) -> str:
    """获取MySQL错误码的描述"""
    return MYSQL_ERROR_CODES.get(error_code, f"Unknown MySQL error code: {error_code}")


def is_connection_error(error: Exception) -> bool:
    """判断是否为连接相关错误"""
    error_msg = str(error).lower()
    connection_keywords = [
        'connection', 'connect', 'server has gone away', 
        'lost connection', 'can\'t connect', 'timeout'
    ]
    return any(keyword in error_msg for keyword in connection_keywords)


def is_retryable_error(error: Exception) -> bool:
    """判断错误是否可重试"""
    if is_connection_error(error):
        return True
    
    error_msg = str(error).lower()
    retryable_keywords = [
        'deadlock', 'lock wait timeout', 'server has gone away',
        'lost connection', 'timeout', 'interrupted'
    ]
    return any(keyword in error_msg for keyword in retryable_keywords)


def extract_mysql_error_code(error: Exception) -> Optional[int]:
    """从异常中提取MySQL错误码"""
    try:
        # 尝试从pymysql异常中提取错误码
        if hasattr(error, 'args') and len(error.args) >= 2:
            if isinstance(error.args[0], int):
                return error.args[0]
        
        # 尝试从错误消息中解析错误码
        error_msg = str(error)
        import re
        match = re.search(r'\((\d+)\)', error_msg)
        if match:
            return int(match.group(1))
    except:
        pass
    
    return None

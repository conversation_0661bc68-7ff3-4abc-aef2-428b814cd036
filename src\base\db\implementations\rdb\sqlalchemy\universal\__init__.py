

from .client import UniversalSQLAlchemyClient
from .config import UniversalConnectionConfig
from .factory import (
    create_client_from_dict,
    create_sqlite_client,
    create_mysql_client,
    create_postgresql_client,
    create_config_from_dict,
    get_supported_databases,
    create_client
)
from .exceptions import UniversalSQLAlchemyError

__version__ = "1.0.0"

__all__ = [
    "UniversalSQLAlchemyClient",
    "UniversalConnectionConfig",
    "create_client_from_dict",
    "create_sqlite_client",
    "create_mysql_client",
    "create_postgresql_client",
    "create_config_from_dict",
    "get_supported_databases",
    "create_client",
    "UniversalSQLAlchemyError"
]

"""
源数据库、表和字段入库脚本
"""
import asyncio
import json
import logging
import os
import sys
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = os.getcwd()
sys.path.insert(0, project_root)

from service import get_client
from modules.knowledge.metadata.crud import MetadataCrud

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 常量配置
KNOWLEDGE_ID = "ca7e7547-e0c9-45a5-81c5-ee7c177dc284"
DB_NAME = "ads"
DATA_LAYER = "ads"

def load_json_file(file_path: str) -> Any:
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载JSON文件失败: {file_path}, 错误: {e}")
        raise

def prepare_db_data() -> Dict[str, Any]:
    """准备数据库记录"""
    return {
        'knowledge_id': KNOWLEDGE_ID,
        'db_name': DB_NAME,
        'db_name_cn': f"{DB_NAME.upper()}应用数据层",
        'data_layer': DATA_LAYER,
        'db_desc': f"{DB_NAME.upper()}应用数据层数据库",
        'is_active': 1
    }

def prepare_tables_data(tables_info: List[Dict[str, Any]], db_id: int) -> List[Dict[str, Any]]:
    """准备表数据"""
    tables_data = []
    
    for table_info in tables_info:
        table_record = {
            'knowledge_id': KNOWLEDGE_ID,
            'db_id': db_id,
            'table_name': table_info['table_name'],
            'table_name_cn': table_info.get('table_cn_name'),
            'table_desc': table_info.get('table_desc'),
            'is_active': 1
        }
        tables_data.append(table_record)
    
    return tables_data

def prepare_columns_data(
    filed_data: Dict[str, List[Dict[str, Any]]], 
    table_name_to_id: Dict[str, int]
) -> List[Dict[str, Any]]:
    """准备字段数据"""
    columns_data = []
    
    for table_name, fields in filed_data.items():
        if table_name not in table_name_to_id:
            logger.warning(f"表 {table_name} 在表信息中不存在，跳过字段入库")
            continue
            
        table_id = table_name_to_id[table_name]
        
        for field in fields:
            column_record = {
                'knowledge_id': KNOWLEDGE_ID,
                'table_id': table_id,
                'column_name': field.get('fileld_name', ''),  # 注意原JSON中的拼写错误
                'column_name_cn': field.get('filed_cn_name'),
                'column_desc': field.get('filed_desc'),
                'data_type': field.get('filed_type'),
                'data_example': None,
                'is_vectorized': 0,
                'is_primary_key': 0,
                'is_sensitive': 0
            }
            columns_data.append(column_record)
    
    return columns_data

async def create_department_relations(table_ids: List[int], db_id: int):
    """
    创建部门关联关系

    Args:
        table_ids: 表ID列表
        db_id: 数据库ID
    """
    try:
        # 获取数据库客户端
        rdb_client = await get_client('database.rdbs.mysql')

        # 首先查询表的详细信息，以便进行智能分配
        from modules.knowledge.metadata.crud import MetadataCrud
        crud = MetadataCrud(rdb_client=rdb_client)

        # 获取所有表的信息
        tables_info = []
        for table_id in table_ids:
            if table_id > 0:
                table_info = await crud._aselect(
                    table='md_source_tables',
                    where={'table_id': table_id},
                    limit=1
                )
                if table_info:
                    tables_info.append(table_info[0])

        # 定义部门分配策略
        dept_assignments = {
            'DEPT_BUSINESS': [],  # 业务部门负责的表
            'DEPT_IT': [],        # IT部门负责的表
            'DEPT_DATA_MGT': [],  # 数据管理部门负责的表
            'DEPT_RRMS': [],      # RRMS部门负责的表
            'DEPT_COMPLIANCE': [] # 合规部门负责的表
        }

        # 智能分配策略：根据表名关键词进行分配
        for table_info in tables_info:
            table_id = table_info['table_id']
            table_name = table_info.get('table_name', '').lower()

            # 根据表名关键词分配部门
            if any(keyword in table_name for keyword in ['risk', 'compliance', 'regulatory', 'audit']):
                dept_assignments['DEPT_COMPLIANCE'].append(table_id)
            elif any(keyword in table_name for keyword in ['system', 'tech', 'it', 'config', 'log']):
                dept_assignments['DEPT_IT'].append(table_id)
            elif any(keyword in table_name for keyword in ['report', 'rrms', 'submission', 'filing']):
                dept_assignments['DEPT_RRMS'].append(table_id)
            elif any(keyword in table_name for keyword in ['data', 'meta', 'dict', 'master']):
                dept_assignments['DEPT_DATA_MGT'].append(table_id)
            else:
                # 默认分配给业务部门
                dept_assignments['DEPT_BUSINESS'].append(table_id)

        # 批量插入部门关联关系
        relations_data = []
        for dept_id, table_list in dept_assignments.items():
            for table_id in table_list:
                relations_data.append({
                    'dept_id': dept_id,
                    'table_id': table_id
                })

        if relations_data:
            # 先清理可能存在的旧关联关系
            await rdb_client.abatch_delete(
                table='dd_departments_relation',
                conditions=[{'table_id': tid} for tid in table_ids if tid > 0],
                batch_size=300,
                max_concurrency=3
            )

            # 插入新的关联关系
            result = await rdb_client.abatch_insert(
                table='dd_departments_relation',
                data=relations_data,
                batch_size=300,
                max_concurrency=3
            )

            if result.success:
                logger.info(f"部门关联关系创建成功: {len(relations_data)} 条关联")

                # 输出分配统计
                for dept_id, table_list in dept_assignments.items():
                    if table_list:
                        logger.info(f"  {dept_id}: {len(table_list)} 个表")
            else:
                logger.warning(f"部门关联关系创建失败: {result.error}")
        else:
            logger.info("没有有效的表ID，跳过部门关联关系创建")

    except Exception as e:
        logger.error(f"创建部门关联关系失败: {e}")
        # 不抛出异常，避免影响主流程

async def insert_source_database():
    """入库源数据库信息"""
    try:
        # 获取数据库客户端
        rdb_client = await get_client("database.rdbs.mysql")
        
        # 创建CRUD实例
        crud = MetadataCrud(rdb_client=rdb_client)
        
        # 1. 创建数据库记录
        logger.info("开始创建数据库记录...")
        db_data = prepare_db_data()
        db_id, _ = await crud.create_source_database(db_data)  # 返回元组，取第一个值
        logger.info(f"数据库记录创建成功，ID: {db_id}")
        
        # 2. 加载表信息并入库
        logger.info("开始加载表信息...")
        tables_info_path = "modules/db/data/source/tables_info.json"
        tables_info = load_json_file(tables_info_path)
        logger.info(f"加载到 {len(tables_info)} 个表")
        
        # 批量创建表记录
        logger.info("开始批量创建表记录...")
        tables_data = prepare_tables_data(tables_info, db_id)
        
        try:
            table_ids, _ = await crud.batch_create_source_tables(tables_data)  # 使用批量方法
            
            # 创建表名到ID的映射
            table_name_to_id = {}
            for i, table_data in enumerate(tables_data):
                if i < len(table_ids) and table_ids[i] > 0:
                    table_name_to_id[table_data['table_name']] = table_ids[i]
                    logger.debug(f"表 {table_data['table_name']} 创建成功，ID: {table_ids[i]}")
                else:
                    logger.error(f"表 {table_data['table_name']} 创建失败")
            
            success_tables = sum(1 for id in table_ids if id > 0)
            logger.info(f"批量创建表记录完成，共 {success_tables}/{len(tables_data)} 个成功")
            
        except Exception as e:
            logger.error(f"批量创建表记录失败，回退到逐个创建: {e}")
            # 回退到逐个创建
            table_ids = []
            table_name_to_id = {}
            
            for table_data in tables_data:
                try:
                    table_id, _ = await crud.create_source_table(table_data)
                    table_ids.append(table_id)
                    table_name_to_id[table_data['table_name']] = table_id
                    logger.debug(f"表 {table_data['table_name']} 创建成功，ID: {table_id}")
                except Exception as e:
                    logger.error(f"创建表 {table_data['table_name']} 失败: {e}")
                    table_ids.append(0)
            
            success_tables = sum(1 for id in table_ids if id > 0)
            logger.info(f"逐个创建表记录完成，共 {success_tables}/{len(tables_data)} 个成功")
        
        # 3. 加载字段信息并入库
        logger.info("开始加载字段信息...")
        filed_data_path = "modules/db/data/source/filed_data.json"
        filed_data = load_json_file(filed_data_path)
        
        # 统计字段总数
        total_columns = sum(len(fields) for fields in filed_data.values())
        logger.info(f"加载到 {total_columns} 个字段，分布在 {len(filed_data)} 个表中")
        
        # 批量创建字段记录
        logger.info("开始批量创建字段记录...")
        columns_data = prepare_columns_data(filed_data, table_name_to_id)
        
        try:
            # 由于字段数量很大，分批处理以避免内存问题
            batch_size = 2500  # 每批100个字段，减少字段长度错误的影响
            all_column_ids = []
            
            for i in range(0, len(columns_data), batch_size):
                batch_data = columns_data[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (len(columns_data) + batch_size - 1) // batch_size
                
                logger.info(f"处理批次 {batch_num}/{total_batches}: 字段 {i+1}-{i+len(batch_data)}")
                
                try:
                    batch_ids, _ = await crud.batch_create_source_columns(batch_data)
                    all_column_ids.extend(batch_ids)
                    
                    success_in_batch = sum(1 for id in batch_ids if id > 0)
                    logger.info(f"批次 {batch_num} 完成: {success_in_batch}/{len(batch_data)} 个字段成功")
                    
                except Exception as e:
                    logger.error(f"批次 {batch_num} 失败，回退到逐个创建: {e}")
                    # 为失败的批次回退到逐个创建
                    for column_data in batch_data:
                        try:
                            column_id, _ = await crud.create_source_column(column_data)
                            all_column_ids.append(column_id)
                        except Exception as e:
                            logger.error(f"创建字段 {column_data['column_name']} 失败: {e}")
                            all_column_ids.append(0)
            
            column_ids = all_column_ids
            success_columns = sum(1 for id in column_ids if id > 0)
            logger.info(f"批量创建字段记录完成，共 {success_columns}/{len(columns_data)} 个成功")
            
        except Exception as e:
            logger.error(f"批量创建字段记录完全失败，回退到逐个创建: {e}")
            # 完全回退到逐个创建
            column_ids = []
            
            for i, column_data in enumerate(columns_data):
                try:
                    column_id, _ = await crud.create_source_column(column_data)
                    column_ids.append(column_id)
                    if (i + 1) % 100 == 0:  # 每100个输出一次进度
                        logger.info(f"已创建 {i + 1} 个字段...")
                except Exception as e:
                    logger.error(f"创建字段 {column_data['column_name']} 失败: {e}")
                    column_ids.append(0)
            
            success_columns = sum(1 for id in column_ids if id > 0)
            logger.info(f"逐个创建字段记录完成，共 {success_columns}/{len(columns_data)} 个成功")
        
        # 统计结果
        logger.info(f"入库统计：数据库1个，表{success_tables}个，字段{success_columns}个")
        
        # 4. 建立部门关联关系（DD系统需要）
        logger.info("开始建立部门关联关系...")
        await create_department_relations(table_ids, db_id)

        return {
            'db_id': db_id,
            'table_count': success_tables,
            'column_count': success_columns
        }
        
    except Exception as e:
        logger.error(f"入库失败: {e}")
        raise

async def main():
    """主函数"""
    try:
        result = await insert_source_database()
        print(f"✅ 源数据库入库完成！")
        print(f"   数据库ID: {result['db_id']}")
        print(f"   表数量: {result['table_count']}")
        print(f"   字段数量: {result['column_count']}")
    except Exception as e:
        print(f"❌ 源数据库入库失败: {e}")

if __name__ == "__main__":
    # 运行脚本
    asyncio.run(main())





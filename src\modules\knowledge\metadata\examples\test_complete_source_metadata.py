"""
完整的源数据元数据CRUD测试：源数据库 → 源表 → 源字段 → 源关联键信息

严格按照 create_rdb_metadata.sql 中的实际表结构进行测试
"""

import asyncio
import logging
import time
from typing import Dict, Any, List

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局测试数据存储
test_data_store = {
    'knowledge_id': None,
    'source_db_id': None,
    'source_table_id': None,
    'source_column1_id': None,  # 源字段
    'source_column2_id': None,  # 目标字段
    'relation_id': None
}


async def setup_test_environment():
    """设置测试环境，创建测试知识库"""
    print("🔧 设置测试环境")
    print("-" * 40)

    try:
        # 获取客户端
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
        from service import get_client
        from modules.knowledge.knowledge.crud import KnowledgeCrud

        rdb_client = await get_client("database.rdbs.mysql")
        knowledge_crud = KnowledgeCrud(rdb_client)

        # 创建测试知识库
        timestamp = int(time.time())
        test_kb_data = {
            'knowledge_name': f'完整源数据元数据测试知识库_{timestamp}',
            'knowledge_type': 'MetaData',
            'knowledge_desc': '完整源数据元数据CRUD测试知识库',
            'models': {
                'embedding': 'moka-m3e-base'
            }
        }

        knowledge_id = await knowledge_crud.create_knowledge_base(test_kb_data)
        if not knowledge_id:
            raise Exception("创建测试知识库失败")

        test_data_store['knowledge_id'] = knowledge_id
        print(f"   ✅ 创建测试知识库: {knowledge_id}")

        return rdb_client, knowledge_id

    except Exception as e:
        logger.error(f"设置测试环境失败: {e}")
        raise


async def cleanup_test_environment():
    """清理测试环境"""
    print("\n🧹 清理测试环境")
    print("-" * 40)

    try:
        if test_data_store['knowledge_id']:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            from modules.knowledge.knowledge.crud import KnowledgeCrud

            rdb_client = await get_client("database.rdbs.mysql")
            knowledge_crud = KnowledgeCrud(rdb_client)

            # 删除测试知识库（会级联删除所有相关数据）
            await knowledge_crud.delete_knowledge_base(test_data_store['knowledge_id'])
            print(f"   ✅ 删除测试知识库: {test_data_store['knowledge_id']}")

    except Exception as e:
        logger.error(f"清理测试环境失败: {e}")


async def setup_base_metadata(rdb_client, knowledge_id: str):
    """设置基础元数据：数据库、表、字段"""
    print("\n1️⃣ 设置基础元数据:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的

        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)

        # 1. 创建源数据库
        timestamp = int(time.time())
        test_db_data = {
            'knowledge_id': knowledge_id,
            'db_name': f'test_complete_db_{timestamp}',
            'db_name_cn': f'完整测试数据库_{timestamp}',
            'data_layer': 'ods',
            'db_desc': '完整测试数据库描述',
            'is_active': True
        }

        db_id, _ = await meta_crud.create_source_database(test_db_data)
        test_data_store['source_db_id'] = db_id
        print(f"   ✅ 创建源数据库: {db_id}")

        # 2. 创建源表
        test_table_data = {
            'knowledge_id': knowledge_id,
            'db_id': db_id,
            'table_name': f'test_complete_table_{timestamp}',
            'table_name_cn': f'完整测试表_{timestamp}',
            'table_desc': '完整测试表描述',
            'is_active': True
        }

        table_id, _ = await meta_crud.create_source_table(test_table_data)
        test_data_store['source_table_id'] = table_id
        print(f"   ✅ 创建源表: {table_id}")

        # 3. 创建源字段1（作为源字段）
        test_column1_data = {
            'knowledge_id': knowledge_id,
            'table_id': table_id,
            'column_name': f'source_column_{timestamp}',
            'column_name_cn': f'源字段_{timestamp}',
            'column_desc': '源字段描述',
            'data_type': 'BIGINT',
            'is_primary_key': True,
            'is_sensitive': False
        }

        column1_id, _ = await meta_crud.create_source_column(test_column1_data)
        test_data_store['source_column1_id'] = column1_id
        print(f"   ✅ 创建源字段1: {column1_id}")

        # 4. 创建源字段2（作为目标字段）
        test_column2_data = {
            'knowledge_id': knowledge_id,
            'table_id': table_id,
            'column_name': f'target_column_{timestamp}',
            'column_name_cn': f'目标字段_{timestamp}',
            'column_desc': '目标字段描述',
            'data_type': 'BIGINT',
            'is_primary_key': False,
            'is_sensitive': False
        }

        column2_id, _ = await meta_crud.create_source_column(test_column2_data)
        test_data_store['source_column2_id'] = column2_id
        print(f"   ✅ 创建源字段2: {column2_id}")

        return True

    except Exception as e:
        print(f"   ❌ 设置基础元数据失败: {e}")
        return False


async def test_source_key_relation_crud(rdb_client):
    """测试源关联键信息的完整CRUD操作"""
    print("\n2️⃣ 测试源关联键信息CRUD操作:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_relations import MetadataCrudRelations

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的

        relations_crud = MetadataCrudRelations(rdb_client, vdb_client, embedding_client)

        # 1. 创建源关联键信息（严格按照实际表结构）
        test_relation_data = {
            'source_column_id': test_data_store['source_column1_id'],
            'target_column_id': test_data_store['source_column2_id'],
            'relation_type': 'FK',
            'comment': '测试外键关联'
        }

        relation_id, vector_results = await relations_crud.create_source_key_relation(test_relation_data)
        if not relation_id or relation_id <= 0:
            raise Exception("创建源关联键信息失败：返回的ID无效")
        
        test_data_store['relation_id'] = relation_id
        print(f"   ✅ 创建源关联键信息: {relation_id}")
        print(f"   📊 向量化结果: {len(vector_results)} 个向量")

        # 2. 获取源关联键信息（主键查询）
        relation_info = await relations_crud.get_source_key_relation(relation_id)
        if not relation_info or not relation_info.get('source_column_id'):
            raise Exception("主键查询源关联键信息失败：未返回有效数据")
        print(f"   ✅ 主键获取源关联键信息: 源字段{relation_info['source_column_id']} -> 目标字段{relation_info['target_column_id']}")

        # 3. 获取源关联键信息（条件查询）
        relation_by_columns = await relations_crud.get_source_key_relation(
            source_column_id=test_data_store['source_column1_id'],
            target_column_id=test_data_store['source_column2_id'],
            relation_type='FK'
        )
        if not relation_by_columns or not relation_by_columns.get('relation_id'):
            raise Exception("条件查询源关联键信息失败：未返回有效数据")
        print(f"   ✅ 条件查询源关联键信息: {relation_by_columns['relation_id']}")

        # 4. 更新源关联键信息
        update_success = await relations_crud.update_source_key_relation(
            {'comment': '更新后的关联说明'},
            relation_id=relation_id
        )
        if not update_success:
            raise Exception("更新源关联键信息失败：返回False")
        print(f"   ✅ 更新源关联键信息: {update_success}")

        # 5. 验证更新
        updated_relation = await relations_crud.get_source_key_relation(relation_id)
        if not updated_relation or '更新后的关联说明' not in updated_relation.get('comment', ''):
            raise Exception("验证更新失败：说明未正确更新")
        print(f"   ✅ 验证更新: {updated_relation['comment']}")

        # 6. 列出源关联键信息
        relation_list = await relations_crud.list_source_key_relations(
            source_column_id=test_data_store['source_column1_id']
        )
        if not relation_list or len(relation_list) == 0:
            raise Exception("列出源关联键信息失败：未返回数据")
        print(f"   ✅ 列出源关联键信息: {len(relation_list)} 个")

        return True

    except Exception as e:
        print(f"   ❌ 源关联键信息CRUD测试失败: {e}")
        return False


async def test_complete_cascade_delete(rdb_client):
    """测试完整的级联删除功能"""
    print("\n3️⃣ 测试完整级联删除功能:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta
        from modules.knowledge.metadata.crud_modules.crud_relations import MetadataCrudRelations

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的

        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)
        relations_crud = MetadataCrudRelations(rdb_client, vdb_client, embedding_client)

        # 1. 验证所有数据存在
        db_exists = await meta_crud.get_source_database(test_data_store['source_db_id'])
        table_exists = await meta_crud.get_source_table(test_data_store['source_table_id'])
        column1_exists = await meta_crud.get_source_column(test_data_store['source_column1_id'])
        column2_exists = await meta_crud.get_source_column(test_data_store['source_column2_id'])
        relation_exists = await relations_crud.get_source_key_relation(test_data_store['relation_id'])
        
        if not all([db_exists, table_exists, column1_exists, column2_exists, relation_exists]):
            raise Exception("测试数据不完整")
        
        print(f"   ✅ 验证测试数据存在: 数据库{test_data_store['source_db_id']}, 表{test_data_store['source_table_id']}, 字段{test_data_store['source_column1_id']},{test_data_store['source_column2_id']}, 关联{test_data_store['relation_id']}")

        # 2. 删除数据库（应该级联删除所有相关数据）
        delete_success = await meta_crud.delete_source_database(db_id=test_data_store['source_db_id'])
        if not delete_success:
            raise Exception("删除源数据库失败：返回False")
        print(f"   ✅ 删除源数据库: {delete_success}")

        # 3. 验证完整级联删除
        db_after = await meta_crud.get_source_database(test_data_store['source_db_id'])
        table_after = await meta_crud.get_source_table(test_data_store['source_table_id'])
        column1_after = await meta_crud.get_source_column(test_data_store['source_column1_id'])
        column2_after = await meta_crud.get_source_column(test_data_store['source_column2_id'])
        relation_after = await relations_crud.get_source_key_relation(test_data_store['relation_id'])
        
        if any([db_after, table_after, column1_after, column2_after, relation_after]):
            raise Exception("级联删除失败：仍有数据存在")
        
        print(f"   ✅ 验证完整级联删除: 所有相关数据都已删除")

        # 清空测试数据存储（因为已删除）
        for key in ['source_db_id', 'source_table_id', 'source_column1_id', 'source_column2_id', 'relation_id']:
            test_data_store[key] = None

        return True

    except Exception as e:
        print(f"   ❌ 完整级联删除测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 完整源数据元数据CRUD测试")
    print("=" * 80)

    try:
        # 1. 设置测试环境
        rdb_client, knowledge_id = await setup_test_environment()

        # 2. 执行所有测试
        test_results = []
        
        # 设置基础元数据
        result1 = await setup_base_metadata(rdb_client, knowledge_id)
        test_results.append(("基础元数据设置", result1))

        if result1:
            # 源关联键信息CRUD操作测试
            result2 = await test_source_key_relation_crud(rdb_client)
            test_results.append(("源关联键信息CRUD", result2))

            if result2:
                # 完整级联删除测试
                result3 = await test_complete_cascade_delete(rdb_client)
                test_results.append(("完整级联删除", result3))

        # 3. 输出测试结果
        print("\n" + "=" * 80)
        print("📊 测试结果汇总")
        print("=" * 80)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False

        if all_passed:
            print("\n🎉 所有测试通过！完整源数据元数据CRUD功能正常")
        else:
            print("\n⚠️  部分测试失败，请检查日志")

        return all_passed

    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        print(f"\n❌ 测试执行失败: {e}")
        return False

    finally:
        # 4. 清理测试环境
        await cleanup_test_environment()


if __name__ == "__main__":
    asyncio.run(main())

"""
业务报送API路由

实现新的业务报送逻辑：
1. 直接使用report_code作为version查询biz_dd_pre表
2. 三层搜索：精确匹配 → 混合搜索 → TFIDF推荐
3. 四级筛选：套系 → 报告类型 → 提交类型 → DR01 → 评分
4. 批量数据入库到biz_dd_post_distribution
5. 标准响应格式和前端长连接通信
"""

import asyncio
import logging
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import Dict, Any

from ..models.request_models import BusinessSubmissionRequest
from ..models.response_models import StandardResponse, BusinessSubmissionResponse
from ..services.business_submission_service import BusinessSubmissionService
from service import get_client

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/dd/duty-distribution", tags=["业务报送"])


async def get_business_submission_service() -> BusinessSubmissionService:
    """获取业务报送服务实例"""
    service = BusinessSubmissionService()
    await service.initialize()
    return service


@router.post("/process", response_model=StandardResponse)
async def process_duty_distribution(
    request: BusinessSubmissionRequest,
    background_tasks: BackgroundTasks,
    service: BusinessSubmissionService = Depends(get_business_submission_service)
):
    """
    处理业务报送请求

    正确的业务流程：
    1. 前端发送请求
    2. 后台验证report_id是否存在，不存在直接返回错误
    3. 如果存在，使用BackgroundTasks启动后台处理
    4. 立即返回success响应给前端（表示后台已开始处理）

    Args:
        request: 业务报送请求
        background_tasks: FastAPI后台任务
        service: 业务报送服务实例

    Returns:
        StandardResponse: 标准响应
    """
    try:
        logger.info(f"收到业务报送请求: {request.report_code}")

        # 1. 验证report_id是否存在
        validation_result = await service.validate_report_code(request.report_code)
        if not validation_result["valid"]:
            logger.warning(f"验证失败: {validation_result['error']}")
            return StandardResponse(
                code="400",
                msg="无法在数据库里搜索到相关信息"
            )

        logger.info(f"验证通过，找到{validation_result['count']}条数据")

        # 2. 添加后台任务处理耗时流程
        background_tasks.add_task(
            service.process_business_submission_background,
            request.report_code,
            "http://218.78.129.173:30134/regulatory/release/ai/createDuty"
        )

        # 3. 立即返回success响应（表示后台已开始处理）
        return StandardResponse(
            code="0",
            msg="后台任务已启动，正在处理中"
        )

    except Exception as e:
        logger.error(f"业务报送处理失败: {e}")
        return StandardResponse(
            code="400",
            msg=f"处理失败: {str(e)}"
        )


@router.get("/status/{report_code}")
async def get_processing_status(
    report_code: str,
    service: BusinessSubmissionService = Depends(get_business_submission_service)
):
    """
    获取处理状态（可选接口，用于前端查询处理进度）

    Args:
        report_code: 报表代码
        service: 业务报送服务实例

    Returns:
        Dict: 处理状态信息
    """
    try:
        # 这里可以实现状态查询逻辑
        # 例如从缓存或数据库中查询处理状态

        return {
            "code": "0",
            "msg": "状态查询成功",
            "status": "completed",  # processing/completed/failed
            "progress": 100.0
        }

    except Exception as e:
        logger.error(f"状态查询失败: {e}")
        return {
            "code": "400",
            "msg": f"查询失败: {str(e)}"
        }


@router.get("/performance/stats")
async def get_performance_stats(
    service: BusinessSubmissionService = Depends(get_business_submission_service)
):
    """
    获取性能统计信息

    Args:
        service: 业务报送服务实例

    Returns:
        Dict: 性能统计数据
    """
    try:
        stats = service.get_performance_stats()
        return {
            "code": "0",
            "msg": "统计信息获取成功",
            "data": stats
        }

    except Exception as e:
        logger.error(f"获取性能统计失败: {e}")
        return {
            "code": "400",
            "msg": f"获取失败: {str(e)}"
        }


@router.get("/validate/{report_code}")
async def validate_report_code(
    report_code: str,
    service: BusinessSubmissionService = Depends(get_business_submission_service)
):
    """
    验证报表代码

    Args:
        report_code: 报表代码
        service: 业务报送服务实例

    Returns:
        验证结果
    """
    try:
        validation_result = await service.validate_report_code(report_code)

        return {
            "code": "0" if validation_result["valid"] else "400",
            "msg": "验证成功" if validation_result["valid"] else validation_result.get("error", "验证失败"),
            "data": {
                "report_code": report_code,
                "is_valid": validation_result["valid"],
                "count": validation_result.get("count", 0),
                "version": validation_result.get("version", report_code)
            }
        }

    except Exception as e:
        logger.error(f"验证报表代码失败: {e}")
        return {
            "code": "400",
            "msg": f"验证失败: {str(e)}"
        }

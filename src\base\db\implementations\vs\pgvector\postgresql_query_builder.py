"""
PostgreSQL查询构建器

提供链式调用的PostgreSQL查询构建功能，支持分区键自动路由

Author: AI Assistant
Created: 2025-07-14
"""

import logging
from typing import List, Dict, Any, Optional, Union, TYPE_CHECKING

if TYPE_CHECKING:
    from .client import UniversalPGVectorClient

logger = logging.getLogger(__name__)


class PostgreSQLQueryBuilder:
    """
    PostgreSQL查询构建器
    
    支持链式调用构建复杂的PostgreSQL查询，自动处理分区键路由
    """
    
    def __init__(self, client: 'UniversalPGVectorClient', table_name: str):
        """
        初始化查询构建器
        
        Args:
            client: PGVector客户端实例
            table_name: 表名
        """
        self.client = client
        self.table_name = table_name
        self._select_fields: List[str] = []
        self._where_conditions: List[str] = []
        self._where_params: List[Any] = []
        self._join_clauses: List[str] = []
        self._order_by_clauses: List[str] = []
        self._group_by_fields: List[str] = []
        self._having_conditions: List[str] = []
        self._having_params: List[Any] = []
        self._limit_count: Optional[int] = None
        self._offset_count: Optional[int] = None
        self._partition_keys: Dict[str, Any] = {}
    
    def select(self, *fields: str) -> 'PostgreSQLQueryBuilder':
        """
        选择字段
        
        Args:
            *fields: 字段名列表
            
        Returns:
            查询构建器实例
        """
        self._select_fields.extend(fields)
        return self
    
    def where(self, field: str, operator: str, value: Any) -> 'PostgreSQLQueryBuilder':
        """
        添加WHERE条件
        
        Args:
            field: 字段名
            operator: 操作符 (=, !=, >, <, >=, <=, LIKE, IN, etc.)
            value: 值
            
        Returns:
            查询构建器实例
        """
        if operator.upper() == "IN" and isinstance(value, (list, tuple)):
            placeholders = ','.join(['%s'] * len(value))
            self._where_conditions.append(f"{field} IN ({placeholders})")
            self._where_params.extend(value)
        else:
            self._where_conditions.append(f"{field} {operator} %s")
            self._where_params.append(value)
        
        return self
    
    def where_raw(self, condition: str, params: Optional[List[Any]] = None) -> 'PostgreSQLQueryBuilder':
        """
        添加原生WHERE条件
        
        Args:
            condition: 原生SQL条件
            params: 参数列表
            
        Returns:
            查询构建器实例
        """
        self._where_conditions.append(condition)
        if params:
            self._where_params.extend(params)
        return self
    
    def join(self, table: str, on_condition: str) -> 'PostgreSQLQueryBuilder':
        """
        添加JOIN子句
        
        Args:
            table: 要连接的表名
            on_condition: 连接条件
            
        Returns:
            查询构建器实例
        """
        self._join_clauses.append(f"JOIN {table} ON {on_condition}")
        return self
    
    def left_join(self, table: str, on_condition: str) -> 'PostgreSQLQueryBuilder':
        """
        添加LEFT JOIN子句
        
        Args:
            table: 要连接的表名
            on_condition: 连接条件
            
        Returns:
            查询构建器实例
        """
        self._join_clauses.append(f"LEFT JOIN {table} ON {on_condition}")
        return self
    
    def order_by(self, field: str, direction: str = "ASC") -> 'PostgreSQLQueryBuilder':
        """
        添加ORDER BY子句
        
        Args:
            field: 排序字段
            direction: 排序方向 (ASC/DESC)
            
        Returns:
            查询构建器实例
        """
        self._order_by_clauses.append(f"{field} {direction.upper()}")
        return self
    
    def group_by(self, *fields: str) -> 'PostgreSQLQueryBuilder':
        """
        添加GROUP BY子句
        
        Args:
            *fields: 分组字段列表
            
        Returns:
            查询构建器实例
        """
        self._group_by_fields.extend(fields)
        return self
    
    def having(self, condition: str, params: Optional[List[Any]] = None) -> 'PostgreSQLQueryBuilder':
        """
        添加HAVING条件
        
        Args:
            condition: HAVING条件
            params: 参数列表
            
        Returns:
            查询构建器实例
        """
        self._having_conditions.append(condition)
        if params:
            self._having_params.extend(params)
        return self
    
    def limit(self, count: int) -> 'PostgreSQLQueryBuilder':
        """
        设置LIMIT
        
        Args:
            count: 限制数量
            
        Returns:
            查询构建器实例
        """
        self._limit_count = count
        return self
    
    def offset(self, count: int) -> 'PostgreSQLQueryBuilder':
        """
        设置OFFSET
        
        Args:
            count: 偏移数量
            
        Returns:
            查询构建器实例
        """
        self._offset_count = count
        return self
    
    def partition_keys(self, **keys: Any) -> 'PostgreSQLQueryBuilder':
        """
        设置分区键（自动添加到WHERE条件）
        
        Args:
            **keys: 分区键值对
            
        Returns:
            查询构建器实例
        """
        self._partition_keys.update(keys)
        return self
    
    def build_sql(self) -> tuple[str, List[Any]]:
        """
        构建SQL语句
        
        Returns:
            (SQL语句, 参数列表)
        """
        # SELECT子句
        if self._select_fields:
            select_clause = f"SELECT {', '.join(self._select_fields)}"
        else:
            select_clause = "SELECT *"
        
        # FROM子句
        from_clause = f"FROM {self.table_name}"
        
        # JOIN子句
        join_clause = " ".join(self._join_clauses) if self._join_clauses else ""
        
        # WHERE子句
        all_conditions = self._where_conditions.copy()
        all_params = self._where_params.copy()
        
        # 添加分区键到WHERE条件
        if self._partition_keys:
            for key, value in self._partition_keys.items():
                all_conditions.append(f"{key} = %s")
                all_params.append(value)
        
        where_clause = ""
        if all_conditions:
            where_clause = f"WHERE {' AND '.join(all_conditions)}"
        
        # GROUP BY子句
        group_by_clause = ""
        if self._group_by_fields:
            group_by_clause = f"GROUP BY {', '.join(self._group_by_fields)}"
        
        # HAVING子句
        having_clause = ""
        if self._having_conditions:
            having_clause = f"HAVING {' AND '.join(self._having_conditions)}"
            all_params.extend(self._having_params)
        
        # ORDER BY子句
        order_by_clause = ""
        if self._order_by_clauses:
            order_by_clause = f"ORDER BY {', '.join(self._order_by_clauses)}"
        
        # LIMIT和OFFSET子句
        limit_clause = ""
        if self._limit_count is not None:
            limit_clause = f"LIMIT {self._limit_count}"
        
        offset_clause = ""
        if self._offset_count is not None:
            offset_clause = f"OFFSET {self._offset_count}"
        
        # 组合SQL
        sql_parts = [
            select_clause,
            from_clause,
            join_clause,
            where_clause,
            group_by_clause,
            having_clause,
            order_by_clause,
            limit_clause,
            offset_clause
        ]
        
        sql = " ".join(part for part in sql_parts if part)
        
        return sql, all_params
    
    def execute(self) -> List[Dict[str, Any]]:
        """
        执行查询
        
        Returns:
            查询结果列表
        """
        sql, params = self.build_sql()
        logger.debug(f"Executing query: {sql} with params: {params}")
        return self.client.execute_query(sql, params)
    
    async def aexecute(self) -> List[Dict[str, Any]]:
        """
        异步执行查询

        Returns:
            查询结果列表
        """
        sql, params = self.build_sql()

        # 转换占位符从%s到$1, $2, ...用于异步查询
        async_sql = sql
        for i, _ in enumerate(params, 1):
            async_sql = async_sql.replace('%s', f'${i}', 1)

        logger.debug(f"Executing async query: {async_sql} with params: {params}")
        return await self.client.aexecute_query(async_sql, params)
    
    def count(self) -> int:
        """
        获取查询结果数量
        
        Returns:
            结果数量
        """
        # 保存原始select字段
        original_select = self._select_fields.copy()
        
        # 临时修改为COUNT查询
        self._select_fields = ["COUNT(*) as count"]
        
        try:
            sql, params = self.build_sql()
            results = self.client.execute_query(sql, params)
            return results[0]['count'] if results else 0
        finally:
            # 恢复原始select字段
            self._select_fields = original_select
    
    async def acount(self) -> int:
        """
        异步获取查询结果数量

        Returns:
            结果数量
        """
        # 保存原始select字段
        original_select = self._select_fields.copy()

        # 临时修改为COUNT查询
        self._select_fields = ["COUNT(*) as count"]

        try:
            sql, params = self.build_sql()

            # 转换占位符从%s到$1, $2, ...用于异步查询
            async_sql = sql
            for i, _ in enumerate(params, 1):
                async_sql = async_sql.replace('%s', f'${i}', 1)

            results = await self.client.aexecute_query(async_sql, params)
            return results[0]['count'] if results else 0
        finally:
            # 恢复原始select字段
            self._select_fields = original_select

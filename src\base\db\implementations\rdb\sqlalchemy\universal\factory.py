
"""
Universal SQLAlchemy Client Factory

统一的客户端创建工厂，支持：
1. 从字典创建（推荐，结构清楚统一）
2. 便捷的数据库特定工厂方法
"""

from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

from .client import UniversalSQLAlchemyClient
from .config import UniversalConnectionConfig
from .pool_manager import PoolManagerInterface, PoolManager


# ==================== 主要创建方法 ====================

def create_client_from_dict(
    config_dict: Dict[str, Any],
    pool_manager: Optional[PoolManagerInterface] = None
) -> UniversalSQLAlchemyClient:
    """
    从字典创建Universal SQLAlchemy客户端（推荐方式）

    Args:
        config_dict: 配置字典，支持所有连接参数
        pool_manager: 可选的池管理器实例，支持依赖注入

    Returns:
        UniversalSQLAlchemyClient实例

    Example:
        >>> config = {
        ...     "dialect": "mysql",
        ...     "host": "localhost",
        ...     "port": 3306,
        ...     "database": "mydb",
        ...     "username": "user",
        ...     "password": "pass",
        ...     "pool_size": 10,
        ...     "echo": False
        ... }
        >>> client = create_client_from_dict(config)
        >>> result = client.execute("SELECT 1")

        # 使用自定义池管理器
        >>> pool_mgr = PoolManager("custom_instance")
        >>> client = create_client_from_dict(config, pool_manager=pool_mgr)
    """
    config = UniversalConnectionConfig.from_dict(config_dict)
    client = UniversalSQLAlchemyClient(config, pool_manager=pool_manager)

    logger.info(f"Created Universal SQLAlchemy client from dict config with pool manager: {getattr(pool_manager, 'instance_id', 'default')}")
    return client


# ==================== 便捷的数据库特定工厂方法 ====================

def create_mysql_client(
    host: str,
    database: str,
    username: str,
    password: str,
    port: int = 3306,
    charset: str = "utf8mb4",
    pool_manager: Optional[PoolManagerInterface] = None,
    **options
) -> UniversalSQLAlchemyClient:
    """
    创建MySQL客户端（便捷方法）

    Args:
        host: MySQL主机
        database: 数据库名
        username: 用户名
        password: 密码
        port: 端口号（默认3306）
        charset: 字符集（默认utf8mb4）
        pool_manager: 可选的池管理器实例，支持依赖注入
        **options: 其他配置选项

    Returns:
        UniversalSQLAlchemyClient实例

    Example:
        >>> client = create_mysql_client(
        ...     "localhost", "mydb", "user", "pass"
        ... )
        >>> result = client.execute("SELECT 1")
    """
    config_dict = {
        "dialect": "mysql",
        "host": host,
        "database": database,
        "username": username,
        "password": password,
        "port": port,
        "charset": charset,
        **options
    }

    # 添加MySQL特定选项
    if 'dialect_options' not in config_dict:
        config_dict['dialect_options'] = {}

    config_dict['dialect_options'].update({
        'connect_args': {
            'charset': charset,
            'use_unicode': True
        }
    })

    return create_client_from_dict(config_dict, pool_manager=pool_manager)


def create_postgresql_client(
    host: str,
    database: str,
    username: str,
    password: str,
    port: int = 5432,
    pool_manager: Optional[PoolManagerInterface] = None,
    **options
) -> UniversalSQLAlchemyClient:
    """
    创建PostgreSQL客户端（便捷方法）

    Args:
        host: PostgreSQL主机
        database: 数据库名
        username: 用户名
        password: 密码
        port: 端口号（默认5432）
        pool_manager: 可选的池管理器实例，支持依赖注入
        **options: 其他配置选项

    Returns:
        UniversalSQLAlchemyClient实例

    Example:
        >>> client = create_postgresql_client(
        ...     "localhost", "mydb", "user", "pass"
        ... )
        >>> result = client.execute("SELECT 1")
    """
    config_dict = {
        "dialect": "postgresql",
        "host": host,
        "database": database,
        "username": username,
        "password": password,
        "port": port,
        **options
    }

    return create_client_from_dict(config_dict, pool_manager=pool_manager)


def create_sqlite_client(
    database_path: str = ":memory:",
    pool_manager: Optional[PoolManagerInterface] = None,
    **options
) -> UniversalSQLAlchemyClient:
    """
    创建SQLite客户端（便捷方法）

    Args:
        database_path: SQLite数据库文件路径
        pool_manager: 可选的池管理器实例，支持依赖注入
        **options: 其他配置选项

    Returns:
        UniversalSQLAlchemyClient实例

    Example:
        >>> client = create_sqlite_client(":memory:")
        >>> result = client.execute("SELECT 1")
    """
    config_dict = {
        "dialect": "sqlite",
        "host": "",
        "database": database_path,
        "username": "",
        "password": "",
        "port": 0,
        **options
    }

    return create_client_from_dict(config_dict, pool_manager=pool_manager)


# ==================== 配置辅助函数 ====================

def create_config_from_dict(config_dict: Dict[str, Any]) -> UniversalConnectionConfig:
    """
    从字典创建配置（便捷方法）

    Args:
        config_dict: 配置字典

    Returns:
        UniversalConnectionConfig实例

    Example:
        >>> config = create_config_from_dict({
        ...     "dialect": "mysql",
        ...     "host": "localhost",
        ...     "database": "mydb",
        ...     "username": "user",
        ...     "password": "pass"
        ... })
    """
    return UniversalConnectionConfig.from_dict(config_dict)


def get_supported_databases() -> Dict[str, str]:
    """
    获取支持的数据库列表

    Returns:
        数据库名称到描述的字典映射
    """
    from .dialects import list_supported_dialects
    return list_supported_dialects()


# ==================== 便捷别名 ====================

# 主要创建方法的别名
create_client = create_client_from_dict

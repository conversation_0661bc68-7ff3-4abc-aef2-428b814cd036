"""
TF-IDF部门推荐器

基于改进TF-IDF的部门推荐兜底策略，与dd_departments表进行语义匹配
"""

import math
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from collections import Counter, defaultdict

logger = logging.getLogger(__name__)

from ..infrastructure.nlp_processor import get_nlp_processor
from ..infrastructure.search_builder import get_search_builder
from ..infrastructure.tfidf_processor import TFIDFProcessor
# 引入最新的CRUD实现
from modules.knowledge.dd.crud import DDCrud
from modules.knowledge.dd.shared.constants import DDTableNames


@dataclass
class DepartmentRecommendation:
    """部门推荐结果"""
    dept_id: str                           # 部门ID (修正：varchar类型)
    dept_name: str                         # 部门名称
    dept_description: str                  # 部门描述
    dept_type: Optional[str] = None        # 部门类型
    confidence_score: float = 0.0          # 置信度分数
    matched_keywords: List[str] = None     # 匹配的关键词
    matching_reason: str = ""              # 推荐理由
    tfidf_score: float = 0.0              # TF-IDF分数
    keyword_match_score: float = 0.0      # 关键词匹配分数
    processing_time_ms: float = 0.0       # 处理时间（毫秒）

    def __post_init__(self):
        if self.matched_keywords is None:
            self.matched_keywords = []

# 为了兼容性，保留原有的别名
DepartmentRecommendation = DepartmentRecommendation


class TFIDFDepartmentRecommender:
    """改进的TF-IDF部门推荐器"""

    def __init__(self, rdb_client: Any):
        """
        初始化部门推荐器

        Args:
            rdb_client: 关系型数据库客户端
        """
        self.rdb_client = rdb_client
        self.nlp_processor = get_nlp_processor()
        self.search_builder = get_search_builder()

        # 初始化CRUD实例
        self.dd_crud = DDCrud(rdb_client)

        # 使用TF-IDF处理器
        self.tfidf_processor = TFIDFProcessor(self.nlp_processor)

        # 缓存部门数据和TF-IDF模型
        self._departments_cache = None
        self._tfidf_model = None
        self._cache_timestamp = 0
        self._cache_duration = 3600  # 1小时缓存

        # 性能统计
        self._performance_stats = {
            'total_recommendations': 0,
            'avg_processing_time_ms': 0.0,
            'cache_hits': 0,
            'cache_misses': 0
        }
    
    async def recommend_departments(
        self,
        dr09: str,
        dr17: str,
        top_k: int = 5,
        guarantee_recommendation: bool = True
    ) -> List[DepartmentRecommendation]:
        """
        推荐部门 - 使用改进的TF-IDF算法

        Args:
            dr09: 数据项名称
            dr17: 数据项定义
            top_k: 返回Top-K个推荐
            guarantee_recommendation: 是否保证返回推荐（移除阈值限制）

        Returns:
            部门推荐列表（保证非空，除非系统异常）
        """
        start_time = time.time()

        try:
            logger.info(f"开始改进TF-IDF部门推荐: dr09='{dr09}', dr17='{dr17}'")

            # 1. 获取部门数据
            departments = await self._get_departments_data()
            if not departments:
                logger.error("TF-IDF推荐失败: 未找到部门数据")
                return []

            logger.debug(f"TF-IDF可用部门数: {len(departments)}")

            # 2. 构建改进的TF-IDF模型
            await self._build_improved_tfidf_model(departments)

            # 3. 提取查询关键词（包含原始字段和分词结果）
            query_keywords = self.nlp_processor.extract_keywords_from_fields(dr09, dr17)
            query_text = ' '.join(query_keywords['all_keywords'])
            logger.debug(f"TF-IDF查询关键词: {query_keywords}")
            logger.debug(f"TF-IDF查询文本: {query_text}")

            # 4. 使用改进的TF-IDF计算相似度
            department_texts = [f"{dept.get('dept_name', '')} {dept.get('dept_desc', '')}"
                              for dept in departments]

            similarity_results = self.tfidf_processor.calculate_enhanced_similarity(
                query_text, department_texts, query_keywords
            )

            # 5. 构建推荐结果
            all_recommendations = []
            processing_time = (time.time() - start_time) * 1000

            for doc_idx, combined_score, details in similarity_results:
                if doc_idx < len(departments):
                    dept = departments[doc_idx]
                    recommendation = self._create_recommendation_from_similarity(
                        dept, combined_score, details, processing_time
                    )
                    all_recommendations.append(recommendation)

            # 6. 根据guarantee_recommendation决定返回策略
            if guarantee_recommendation:
                # 保证推荐模式：总是返回top_k个推荐，无论得分多低
                top_recommendations = all_recommendations[:top_k]

                # 详细日志记录
                logger.info(f"改进TF-IDF部门推荐完成: 候选部门{len(all_recommendations)}个，返回{len(top_recommendations)}个推荐 (保证模式)")

                for i, rec in enumerate(top_recommendations):
                    logger.debug(f"推荐{i+1}: {rec.dept_name} (综合得分: {rec.confidence_score:.3f}, TF-IDF: {rec.tfidf_score:.3f}, 关键词: {rec.keyword_match_score:.3f})")

            else:
                # 传统阈值模式：仅返回符合阈值的推荐
                min_confidence = 0.1  # 默认阈值
                qualified_recommendations = [r for r in all_recommendations
                                           if r.confidence_score >= min_confidence]
                top_recommendations = qualified_recommendations[:top_k]

                logger.info(f"改进TF-IDF部门推荐完成: 候选部门{len(all_recommendations)}个，符合阈值{len(qualified_recommendations)}个，返回{len(top_recommendations)}个推荐")

            # 更新性能统计
            self._update_performance_stats(processing_time)

            return top_recommendations

        except Exception as e:
            logger.error(f"改进TF-IDF部门推荐失败: {e}")
            return []
    
    async def _get_departments_data(self) -> List[Dict[str, Any]]:
        """获取部门数据"""
        try:
            logger.debug("使用CRUD方法获取部门数据")

            # 使用最新的CRUD方法获取部门数据
            departments = await self.dd_crud.list_departments(
                is_active=True  # 只获取激活的部门
            )

            logger.debug(f"获取到{len(departments)}个部门记录")
            return departments

        except Exception as e:
            logger.error(f"获取部门数据失败: {e}")
            return []
    
    async def _build_improved_tfidf_model(self, departments: List[Dict[str, Any]]) -> None:
        """构建改进的TF-IDF模型"""
        try:
            # 构建文档语料库
            documents = []
            for dept in departments:
                dept_text = f"{dept.get('dept_name', '')} {dept.get('dept_desc', '')}"
                documents.append(dept_text)

            # 使用改进的TF-IDF处理器训练模型
            self.tfidf_processor.fit_transform(documents)

            logger.info(f"改进TF-IDF模型构建完成: {len(documents)}个部门文档")

        except Exception as e:
            logger.error(f"构建改进TF-IDF模型失败: {e}")

    async def _build_tfidf_model(self, departments: List[Dict[str, Any]]) -> None:
        """构建TF-IDF模型（兼容性方法）"""
        await self._build_improved_tfidf_model(departments)
    
    def _calculate_tfidf(self, documents: List[str]) -> Dict[str, Dict[str, float]]:
        """计算TF-IDF"""
        # 1. 分词并构建词汇表
        doc_words = []
        all_words = set()
        
        for doc in documents:
            words = self.nlp_processor.extract_keywords(doc, max_keywords=50)
            doc_words.append(words)
            all_words.update(words)
        
        # 2. 计算词频(TF)
        tf_matrix = []
        for words in doc_words:
            word_count = Counter(words)
            total_words = len(words)
            tf_dict = {}
            
            for word in all_words:
                tf = word_count.get(word, 0) / total_words if total_words > 0 else 0
                tf_dict[word] = tf
            
            tf_matrix.append(tf_dict)
        
        # 3. 计算逆文档频率(IDF)
        idf_dict = {}
        total_docs = len(documents)
        
        for word in all_words:
            doc_count = sum(1 for words in doc_words if word in words)
            idf = math.log(total_docs / (doc_count + 1))  # 加1避免除零
            idf_dict[word] = idf
        
        # 4. 计算TF-IDF
        tfidf_matrix = {}
        for i, tf_dict in enumerate(tf_matrix):
            tfidf_dict = {}
            for word, tf in tf_dict.items():
                tfidf = tf * idf_dict[word]
                tfidf_dict[word] = tfidf
            tfidf_matrix[i] = tfidf_dict
        
        return tfidf_matrix
    
    async def _calculate_department_score(
        self, 
        dept: Dict[str, Any], 
        query_text: str,
        query_keywords: Dict[str, List[str]]
    ) -> DepartmentRecommendation:
        """计算部门分数"""
        dept_id = dept.get('dept_id', '')  # 修正：使用dept_id而不是id
        dept_name = dept.get('dept_name', '')
        dept_description = dept.get('dept_desc', '')  # 修正：使用dept_desc
        dept_type = dept.get('dept_type', '')
        
        # 1. 关键词匹配分数
        keyword_score, matched_keywords = self._calculate_keyword_match_score(
            dept_name, dept_description, query_keywords
        )
        
        # 2. TF-IDF相似度分数
        tfidf_score = self._calculate_tfidf_similarity(
            query_text, f"{dept_name} {dept_description}"
        )
        
        # 3. 综合置信度分数
        confidence_score = self._calculate_confidence_score(
            keyword_score, tfidf_score, matched_keywords
        )
        
        # 4. 生成推荐理由
        matching_reason = self._generate_matching_reason(
            matched_keywords, keyword_score, tfidf_score
        )
        
        return DepartmentRecommendation(
            dept_id=dept_id,
            dept_name=dept_name,
            dept_description=dept_description,  # 这里保持原字段名，因为这是返回模型的字段
            dept_type=dept_type,
            confidence_score=confidence_score,
            matched_keywords=matched_keywords,
            matching_reason=matching_reason,
            tfidf_score=tfidf_score,
            keyword_match_score=keyword_score
        )
    
    def _calculate_keyword_match_score(
        self, 
        dept_name: str, 
        dept_description: str,
        query_keywords: Dict[str, List[str]]
    ) -> Tuple[float, List[str]]:
        """计算关键词匹配分数"""
        dept_text = f"{dept_name} {dept_description}"
        dept_keywords = set(self.nlp_processor.extract_keywords(dept_text))
        
        # 合并查询关键词
        all_query_keywords = query_keywords.get('combined_keywords', [])
        dr09_keywords = query_keywords.get('dr09_keywords', [])
        
        matched_keywords = []
        total_score = 0.0
        
        # dr09关键词权重更高
        for keyword in dr09_keywords:
            if keyword in dept_keywords:
                matched_keywords.append(keyword)
                total_score += 2.0  # dr09关键词权重为2
        
        # dr17关键词权重较低
        for keyword in query_keywords.get('dr17_keywords', []):
            if keyword in dept_keywords and keyword not in matched_keywords:
                matched_keywords.append(keyword)
                total_score += 1.0  # dr17关键词权重为1
        
        # 计算匹配比例
        if all_query_keywords:
            match_ratio = len(matched_keywords) / len(all_query_keywords)
            keyword_score = match_ratio * (total_score / len(matched_keywords) if matched_keywords else 0)
        else:
            keyword_score = 0.0
        
        return min(keyword_score, 1.0), matched_keywords
    
    def _calculate_tfidf_similarity(self, query_text: str, dept_text: str) -> float:
        """计算TF-IDF相似度"""
        try:
            # 简化的余弦相似度计算
            query_keywords = self.nlp_processor.extract_keywords(query_text)
            dept_keywords = self.nlp_processor.extract_keywords(dept_text)
            
            if not query_keywords or not dept_keywords:
                return 0.0
            
            # 计算交集
            intersection = set(query_keywords) & set(dept_keywords)
            union = set(query_keywords) | set(dept_keywords)
            
            if not union:
                return 0.0
            
            # Jaccard相似度
            jaccard_similarity = len(intersection) / len(union)
            
            return jaccard_similarity
            
        except Exception as e:
            logger.error(f"TF-IDF相似度计算失败: {e}")
            return 0.0
    
    def _calculate_confidence_score(
        self, 
        keyword_score: float, 
        tfidf_score: float,
        matched_keywords: List[str]
    ) -> float:
        """计算综合置信度分数"""
        # 权重配置
        keyword_weight = 0.7
        tfidf_weight = 0.3
        
        # 基础分数
        base_score = keyword_score * keyword_weight + tfidf_score * tfidf_weight
        
        # 匹配关键词数量加成
        keyword_bonus = min(len(matched_keywords) * 0.1, 0.3)
        
        # 最终分数
        final_score = min(base_score + keyword_bonus, 1.0)
        
        return final_score
    
    def _generate_matching_reason(
        self, 
        matched_keywords: List[str],
        keyword_score: float,
        tfidf_score: float
    ) -> str:
        """生成匹配理由"""
        reasons = []
        
        if matched_keywords:
            keywords_str = "、".join(matched_keywords[:3])  # 最多显示3个关键词
            reasons.append(f"匹配关键词: {keywords_str}")
        
        if keyword_score > 0.5:
            reasons.append("关键词匹配度较高")
        elif keyword_score > 0.3:
            reasons.append("关键词匹配度中等")
        
        if tfidf_score > 0.3:
            reasons.append("语义相似度较高")
        
        if not reasons:
            reasons.append("基于文本相似度推荐")
        
        return "；".join(reasons)

    def _create_recommendation_from_similarity(
        self,
        dept: Dict[str, Any],
        combined_score: float,
        details: Dict[str, Any],
        processing_time: float
    ) -> DepartmentRecommendation:
        """从相似度结果创建推荐"""
        dept_id = dept.get('dept_id', '')
        dept_name = dept.get('dept_name', '')
        dept_description = dept.get('dept_desc', '')
        dept_type = dept.get('dept_type', '')

        # 提取详细信息
        tfidf_score = details.get('tfidf_score', 0.0)
        keyword_score = details.get('keyword_score', 0.0)
        matched_keywords = details.get('matched_keywords', [])

        # 生成推荐理由
        matching_reason = self._generate_matching_reason(
            matched_keywords, keyword_score, tfidf_score
        )

        return DepartmentRecommendation(
            dept_id=dept_id,
            dept_name=dept_name,
            dept_description=dept_description,
            dept_type=dept_type,
            confidence_score=combined_score,
            matched_keywords=matched_keywords,
            matching_reason=matching_reason,
            tfidf_score=tfidf_score,
            keyword_match_score=keyword_score,
            processing_time_ms=processing_time
        )

    def _update_performance_stats(self, processing_time: float):
        """更新性能统计"""
        self._performance_stats['total_recommendations'] += 1
        total = self._performance_stats['total_recommendations']
        current_avg = self._performance_stats['avg_processing_time_ms']

        # 计算新的平均处理时间
        new_avg = (current_avg * (total - 1) + processing_time) / total
        self._performance_stats['avg_processing_time_ms'] = new_avg

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        model_info = self.tfidf_processor.get_model_info()
        return {
            **self._performance_stats,
            'tfidf_model_info': model_info
        }


# 全局实例
_department_recommender_instance = None

def get_department_recommender(rdb_client: Any) -> TFIDFDepartmentRecommender:
    """获取部门推荐器实例"""
    global _department_recommender_instance
    if _department_recommender_instance is None:
        _department_recommender_instance = TFIDFDepartmentRecommender(rdb_client)
    return _department_recommender_instance

"""
数据回填业务逻辑

包含用户修改DD后的回填处理完整逻辑：
1. 检查字段变化（DR22、BDR01、BDR03）
2. 查询部门关联表进行验证
3. 根据交集结果执行不同的更新逻辑
4. 返回处理结果
"""

import time
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

from .database_operations import PostDistributionDBOperations
from ..infrastructure.exceptions import DepartmentAssignmentError


class DataBackfillLogic:
    """数据回填业务逻辑类"""
    
    def __init__(self, rdb_client: Any):
        """
        初始化数据回填逻辑
        
        Args:
            rdb_client: 关系型数据库客户端
        """
        self.rdb_client = rdb_client
        self.db_operations = PostDistributionDBOperations(rdb_client)
    
    async def process_data_backfill(
        self, 
        report_code: str, 
        step: str, 
        data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        处理数据回填完整流程
        
        Args:
            report_code: 报表代码
            step: 处理步骤，如'义务解读'
            data: 用户修改的数据列表
            
        Returns:
            完整的处理结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始处理数据回填: report_code={report_code}, step={step}, 数据条数={len(data)}")
            
            # 验证步骤
            if step != "义务解读":
                return {
                    "success": False,
                    "message": f"当前不支持步骤: {step}",
                    "processing_time_ms": (time.time() - start_time) * 1000
                }
            
            # 处理每条数据
            updated_count = 0
            error_count = 0
            processing_details = []
            
            for item_data in data:
                try:
                    # 解析数据项
                    entry_id = item_data.get('entry_id')
                    entry_type = item_data.get('entry_type')
                    dr22 = item_data.get('DR22', [])
                    bdr01 = item_data.get('BDR01', [])
                    bdr03 = item_data.get('BDR03', [])
                    
                    if not entry_id:
                        logger.warning("跳过无效数据项：缺少entry_id")
                        error_count += 1
                        processing_details.append({
                            "entry_id": "unknown",
                            "status": "error",
                            "message": "缺少entry_id"
                        })
                        continue
                    
                    # 映射 entry_type 到 submission_type
                    submission_type = "submission" if "填报项" in entry_type else "range"
                    
                    # 处理单条数据
                    result = await self._process_single_item(
                        report_code, entry_id, dr22, bdr01, bdr03
                    )
                    
                    if result["success"]:
                        updated_count += 1
                        processing_details.append({
                            "entry_id": entry_id,
                            "status": "success",
                            "message": result["message"],
                            "update_type": result.get("update_type", "unknown")
                        })
                    else:
                        error_count += 1
                        processing_details.append({
                            "entry_id": entry_id,
                            "status": "error",
                            "message": result["message"]
                        })
                        
                except Exception as e:
                    logger.error(f"处理数据项失败 {item_data.get('entry_id', 'unknown')}: {e}")
                    error_count += 1
                    processing_details.append({
                        "entry_id": item_data.get('entry_id', 'unknown'),
                        "status": "error",
                        "message": f"处理异常: {str(e)}"
                    })
            
            # 构建返回结果
            total_count = len(data)
            processing_time = (time.time() - start_time) * 1000
            
            success = error_count == 0
            if success:
                message = f"数据回填处理完成，成功更新 {updated_count}/{total_count} 条记录"
            else:
                message = f"数据回填处理完成，成功更新 {updated_count}/{total_count} 条记录，失败 {error_count} 条"
            
            result = {
                "success": success,
                "message": message,
                "statistics": {
                    "total_count": total_count,
                    "updated_count": updated_count,
                    "error_count": error_count,
                    "success_rate": updated_count / total_count if total_count > 0 else 0
                },
                "processing_details": processing_details,
                "processing_time_ms": processing_time
            }
            
            logger.info(f"数据回填处理完成: report_code={report_code}, "
                       f"成功={updated_count}, 失败={error_count}, "
                       f"耗时={processing_time:.2f}ms")
            
            return result
            
        except Exception as e:
            logger.error(f"数据回填处理失败: {e}")
            return {
                "success": False,
                "message": f"数据回填处理失败: {str(e)}",
                "processing_time_ms": (time.time() - start_time) * 1000
            }
    
    async def _process_single_item(
        self,
        report_code: str,
        entry_id: str,
        dr22: List[str],
        bdr01: List[str],
        bdr03: List[str]
    ) -> Dict[str, Any]:
        """
        处理单条数据项
        
        Args:
            report_code: 报表代码
            entry_id: 条目ID
            dr22: DR22字段值
            bdr01: BDR01字段值
            bdr03: BDR03字段值
            
        Returns:
            处理结果
        """
        try:
            # 1. 获取当前记录
            current_record = await self._get_current_post_distribution_record(report_code, entry_id)
            
            if not current_record:
                return {
                    "success": False,
                    "message": f"未找到对应的post distribution记录: {entry_id}"
                }
            
            # 2. 检查是否有变化
            has_changes = await self._check_field_changes(current_record, dr22, bdr01, bdr03)
            
            if not has_changes:
                return {
                    "success": True,
                    "message": "记录无变化，跳过处理",
                    "update_type": "no_change"
                }
            
            # 3. 验证部门ID并执行更新逻辑
            success = await self._process_department_validation_and_update(
                report_code, entry_id, dr22, bdr01, bdr03, current_record
            )
            
            if success:
                return {
                    "success": True,
                    "message": "记录更新成功",
                    "update_type": "updated"
                }
            else:
                return {
                    "success": False,
                    "message": "记录更新失败"
                }
                
        except Exception as e:
            logger.error(f"处理单条数据项失败: {e}")
            return {
                "success": False,
                "message": f"处理异常: {str(e)}"
            }
    
    async def _get_current_post_distribution_record(
        self, 
        report_code: str, 
        entry_id: str
    ) -> Optional[Dict[str, Any]]:
        """获取当前的post distribution记录"""
        try:
            # 解析report_code
            parts = report_code.split('_')
            if len(parts) >= 3:
                dr07 = parts[0]
                version = parts[-1]
            else:
                dr07 = report_code
                version = 'v1.0'
            
            sql = """
            SELECT * FROM biz_dd_post_distribution
            WHERE submission_id = :entry_id AND dr07 = :dr07 AND version = :version
            LIMIT 1
            """
            params = {'entry_id': entry_id, 'dr07': dr07, 'version': version}
            
            response = await self.rdb_client.afetch_all(sql, params)
            if response.data and len(response.data) > 0:
                return dict(response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"获取当前记录失败: {e}")
            return None
    
    async def _check_field_changes(
        self,
        current_record: Dict[str, Any],
        new_dr22: List[str],
        new_bdr01: List[str], 
        new_bdr03: List[str]
    ) -> bool:
        """检查字段是否发生变化"""
        try:
            # 获取当前值
            current_dr22 = current_record.get('dr22', '')
            current_bdr01 = current_record.get('bdr01', '')
            current_bdr03 = current_record.get('bdr03', '')
            
            # 转换为列表进行比较
            current_dr22_list = [current_dr22] if current_dr22 else []
            current_bdr01_list = [current_bdr01] if current_bdr01 else []
            current_bdr03_list = [current_bdr03] if current_bdr03 else []
            
            # 检查是否有变化
            dr22_changed = set(new_dr22) != set(current_dr22_list)
            bdr01_changed = set(new_bdr01) != set(current_bdr01_list)
            bdr03_changed = set(new_bdr03) != set(current_bdr03_list)
            
            return dr22_changed or bdr01_changed or bdr03_changed
            
        except Exception as e:
            logger.error(f"检查字段变化失败: {e}")
            return True  # 出错时假设有变化

    async def _process_department_validation_and_update(
        self,
        report_code: str,
        entry_id: str,
        dr22: List[str],
        bdr01: List[str],
        bdr03: List[str],
        current_record: Dict[str, Any]
    ) -> bool:
        """处理部门验证和更新逻辑"""
        try:
            # 1. 根据部门ID查询数据部门关联表
            all_dept_ids = list(set(dr22 + bdr01 + bdr03))
            table_ids = await self._get_table_ids_by_dept_ids(all_dept_ids)

            # 2. 与当前行的bdr09字段进行交集运算
            current_bdr09 = current_record.get('bdr09', [])
            if isinstance(current_bdr09, str):
                current_bdr09 = json.loads(current_bdr09) if current_bdr09 else []

            intersection = set(table_ids) & set(current_bdr09)

            # 3. 根据交集结果执行不同的更新逻辑
            if intersection:
                # 交集不为空：只修改变化的ID，执行upsert操作
                success = await self._update_changed_fields_only(
                    report_code, entry_id, dr22, bdr01, bdr03
                )
                logger.debug(f"交集不为空，执行字段更新: {entry_id}")
            else:
                # 交集为空：删除BRD05到sdr15的所有数据，只保留指定字段
                success = await self._clear_and_update_fields(
                    report_code, entry_id, dr22, bdr01, bdr03
                )
                logger.debug(f"交集为空，执行清空更新: {entry_id}")

            return success

        except Exception as e:
            logger.error(f"部门验证和更新失败: {e}")
            return False

    async def _get_table_ids_by_dept_ids(self, dept_ids: List[str]) -> List[int]:
        """根据部门ID查询关联的table_id"""
        try:
            if not dept_ids:
                return []

            # 查询dd_department_relation表
            placeholders = ','.join([f':dept_id_{i}' for i in range(len(dept_ids))])
            sql = f"""
            SELECT DISTINCT dr.table_id
            FROM dd_departments_relation dr
            INNER JOIN md_source_tables mst ON dr.table_id = mst.id
            WHERE dr.dept_id IN ({placeholders})
            """

            params = {f'dept_id_{i}': dept_id for i, dept_id in enumerate(dept_ids)}

            response = await self.rdb_client.afetch_all(sql, params)
            if response.data:
                return [row['table_id'] for row in response.data]
            return []

        except Exception as e:
            logger.error(f"查询table_ids失败: {e}")
            return []

    async def _update_changed_fields_only(
        self,
        report_code: str,
        entry_id: str,
        dr22: List[str],
        bdr01: List[str],
        bdr03: List[str]
    ) -> bool:
        """只修改变化的ID，执行upsert操作"""
        try:
            # 解析report_code
            parts = report_code.split('_')
            if len(parts) >= 3:
                dr07 = parts[0]
                version = parts[-1]
            else:
                dr07 = report_code
                version = 'v1.0'

            # 构建更新SQL
            update_fields = []
            params = {'entry_id': entry_id, 'dr07': dr07, 'version': version}

            if dr22:
                update_fields.append("dr22 = :dr22")
                params['dr22'] = dr22[0] if dr22 else ''

            if bdr01:
                update_fields.append("bdr01 = :bdr01")
                params['bdr01'] = bdr01[0] if bdr01 else ''

            if bdr03:
                update_fields.append("bdr03 = :bdr03")
                params['bdr03'] = bdr03[0] if bdr03 else ''

            if not update_fields:
                return True

            update_fields.append("update_time = :update_time")
            params['update_time'] = datetime.now()

            sql = f"""
            UPDATE biz_dd_post_distribution
            SET {', '.join(update_fields)}
            WHERE submission_id = :entry_id AND dr07 = :dr07 AND version = :version
            """

            response = await self.rdb_client.aexecute(sql, params)
            return response.success and response.affected_rows > 0

        except Exception as e:
            logger.error(f"更新变化字段失败: {e}")
            return False

    async def _clear_and_update_fields(
        self,
        report_code: str,
        entry_id: str,
        dr22: List[str],
        bdr01: List[str],
        bdr03: List[str]
    ) -> bool:
        """删除BRD05到sdr15的所有数据，只保留指定字段"""
        try:
            # 解析report_code
            parts = report_code.split('_')
            if len(parts) >= 3:
                dr07 = parts[0]
                version = parts[-1]
            else:
                dr07 = report_code
                version = 'v1.0'

            # 清空BRD05到sdr15字段，只保留DR22、BDR01、BDR02、BDR03、BDR04
            sql = """
            UPDATE biz_dd_post_distribution
            SET
                bdr05 = NULL, bdr06 = NULL, bdr07 = NULL, bdr08 = NULL, bdr09 = NULL,
                bdr10 = NULL, bdr11 = NULL, bdr12 = NULL, bdr13 = NULL, bdr14 = NULL,
                bdr15 = NULL, bdr16 = NULL, bdr17 = NULL, bdr18 = NULL,
                sdr01 = NULL, sdr02 = NULL, sdr03 = NULL, sdr04 = NULL, sdr05 = NULL,
                sdr06 = NULL, sdr07 = NULL, sdr08 = NULL, sdr09 = NULL, sdr10 = NULL,
                sdr11 = NULL, sdr12 = NULL, sdr13 = NULL, sdr14 = NULL, sdr15 = NULL,
                dr22 = :dr22,
                bdr01 = :bdr01,
                bdr03 = :bdr03,
                update_time = :update_time
            WHERE submission_id = :entry_id AND dr07 = :dr07 AND version = :version
            """

            params = {
                'entry_id': entry_id,
                'dr07': dr07,
                'version': version,
                'dr22': dr22[0] if dr22 else '',
                'bdr01': bdr01[0] if bdr01 else '',
                'bdr03': bdr03[0] if bdr03 else '',
                'update_time': datetime.now()
            }

            response = await self.rdb_client.aexecute(sql, params)
            return response.success and response.affected_rows > 0

        except Exception as e:
            logger.error(f"清空并更新字段失败: {e}")
            return False

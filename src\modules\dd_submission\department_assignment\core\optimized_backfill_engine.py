#!/usr/bin/env python3
"""
优化的数据回填引擎

基于DDCrud的553倍性能提升，实现高性能的数据回填处理
"""

import time
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Set, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

from modules.knowledge.dd.crud import DDCrud
from modules.dd_submission.department_assignment.monitoring.performance_monitor import monitor_batch_operation_enhanced


@dataclass
class BackfillConfig:
    """数据回填配置"""
    batch_size: int = 500
    max_concurrency: int = 3
    timeout_per_batch: float = 120.0
    enable_transaction: bool = True
    enable_validation: bool = True
    fallback_enabled: bool = True


@dataclass
class BackfillOperation:
    """数据回填操作"""
    entry_id: str
    operation_type: str  # 'field_update' or 'clear_update'
    dr07: str
    version: str
    update_data: Dict[str, Any]
    original_data: Dict[str, Any]


@dataclass
class BackfillResult:
    """数据回填结果"""
    success: bool
    total_count: int
    updated_count: int
    error_count: int
    processing_time_ms: float
    details: List[Dict[str, Any]]
    error_message: Optional[str] = None


class OptimizedBackfillEngine:
    """优化的数据回填引擎"""
    
    def __init__(self, rdb_client, vdb_client=None, config: BackfillConfig = None):
        """
        初始化优化的数据回填引擎
        
        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端（可选）
            config: 回填配置
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.config = config or BackfillConfig()
        self.dd_crud = DDCrud(rdb_client, vdb_client)
        
        # 性能统计
        self.performance_stats = {
            'total_operations': 0,
            'batch_operations': 0,
            'fallback_operations': 0,
            'total_processing_time': 0.0
        }
    
    @monitor_batch_operation_enhanced("optimized_data_backfill")
    async def process_data_backfill(
        self,
        report_code: str,
        step: str,
        data: List[Dict[str, Any]]
    ) -> BackfillResult:
        """
        优化的数据回填处理主入口
        
        Args:
            report_code: 报表代码
            step: 处理步骤
            data: 待处理数据列表
            
        Returns:
            BackfillResult: 处理结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始优化数据回填处理: {report_code}, 数据量: {len(data)}")
            
            # 数据预处理和验证
            validated_data = await self._preprocess_and_validate_data(data)
            if not validated_data:
                return BackfillResult(
                    success=False,
                    total_count=len(data),
                    updated_count=0,
                    error_count=len(data),
                    processing_time_ms=(time.time() - start_time) * 1000,
                    details=[],
                    error_message="数据预处理失败，无有效数据"
                )
            
            # 批量数据获取
            current_records = await self._batch_get_current_records(
                report_code, [item['entry_id'] for item in validated_data]
            )
            
            # 批量部门验证
            dept_relations = await self._batch_get_department_relations(
                [item.get('DR22', []) for item in validated_data]
            )
            
            # 批量操作分类
            operations = await self._classify_batch_operations(
                validated_data, current_records, dept_relations, report_code
            )
            
            # 批量执行更新
            updated_count, operation_details = await self._execute_batch_operations(operations)
            
            processing_time = (time.time() - start_time) * 1000
            
            # 更新性能统计
            self.performance_stats['total_operations'] += 1
            self.performance_stats['batch_operations'] += 1
            self.performance_stats['total_processing_time'] += processing_time
            
            logger.info(
                f"优化数据回填完成: 处理{len(validated_data)}条, "
                f"成功{updated_count}条, 耗时{processing_time:.2f}ms"
            )
            
            return BackfillResult(
                success=True,
                total_count=len(data),
                updated_count=updated_count,
                error_count=len(validated_data) - updated_count,
                processing_time_ms=processing_time,
                details=operation_details
            )
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            logger.error(f"优化数据回填处理失败: {e}")
            
            # 降级到原始处理方式
            if self.config.fallback_enabled:
                logger.info("启用降级处理...")
                return await self._fallback_process_data_backfill(report_code, step, data)
            
            return BackfillResult(
                success=False,
                total_count=len(data),
                updated_count=0,
                error_count=len(data),
                processing_time_ms=processing_time,
                details=[],
                error_message=str(e)
            )
    
    async def _preprocess_and_validate_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """数据预处理和验证"""
        validated_data = []
        
        for item in data:
            # 基础字段验证
            if not item.get('entry_id'):
                logger.warning(f"跳过无效数据项：缺少entry_id")
                continue
            
            # 数据标准化
            validated_item = {
                'entry_id': str(item['entry_id']).strip(),
                'entry_type': item.get('entry_type', ''),
                'DR22': item.get('DR22', []),
                'BDR01': item.get('BDR01', []),
                'BDR03': item.get('BDR03', [])
            }
            
            # 确保列表字段是列表类型
            for field in ['DR22', 'BDR01', 'BDR03']:
                if not isinstance(validated_item[field], list):
                    validated_item[field] = [validated_item[field]] if validated_item[field] else []
            
            validated_data.append(validated_item)
        
        logger.info(f"数据预处理完成: {len(data)} -> {len(validated_data)}")
        return validated_data
    
    async def _batch_get_current_records(
        self, 
        report_code: str, 
        entry_ids: List[str]
    ) -> Dict[str, Dict[str, Any]]:
        """批量获取当前记录"""
        if not entry_ids:
            return {}
        
        try:
            # 解析report_code
            parts = report_code.split('_')
            dr07 = parts[0] if len(parts) >= 3 else report_code
            version = parts[-1] if len(parts) >= 3 else 'v1.0'
            
            logger.info(f"批量获取当前记录: dr07={dr07}, version={version}, 数量={len(entry_ids)}")
            
            # 使用DDCrud的批量查询能力
            records = await self.dd_crud.list_post_distributions(
                dr07=dr07,
                version=version,
                limit=len(entry_ids) + 100  # 适当的缓冲
            )
            
            # 过滤出目标记录并构建映射
            record_map = {}
            for record in records:
                if record.get('submission_id') in entry_ids:
                    record_map[record['submission_id']] = record
            
            logger.info(f"批量获取记录完成: 找到{len(record_map)}条匹配记录")
            return record_map
            
        except Exception as e:
            logger.error(f"批量获取当前记录失败: {e}")
            return {}
    
    async def _batch_get_department_relations(
        self, 
        dr22_list: List[List[str]]
    ) -> Dict[str, List[str]]:
        """批量获取部门关联信息"""
        # 去重所有部门ID
        unique_dept_ids = set()
        for dr22 in dr22_list:
            if dr22:
                unique_dept_ids.update(dr22)
        
        if not unique_dept_ids:
            return {}
        
        try:
            logger.info(f"批量获取部门关联: {len(unique_dept_ids)}个唯一部门ID")
            
            # 批量查询部门关联表
            sql = """
            SELECT dept_id, table_id 
            FROM biz_dd_department_relation 
            WHERE dept_id IN ({})
            """.format(','.join(['%s'] * len(unique_dept_ids)))
            
            response = await self.rdb_client.afetch_all(sql, list(unique_dept_ids))
            
            # 构建部门ID到table_ids的映射
            dept_to_tables = {}
            if response.success and response.data:
                for row in response.data:
                    dept_id = row['dept_id']
                    if dept_id not in dept_to_tables:
                        dept_to_tables[dept_id] = []
                    dept_to_tables[dept_id].append(row['table_id'])
            
            logger.info(f"批量获取部门关联完成: {len(dept_to_tables)}个部门有关联")
            return dept_to_tables
            
        except Exception as e:
            logger.error(f"批量获取部门关联失败: {e}")
            return {}
    
    async def _classify_batch_operations(
        self,
        validated_data: List[Dict[str, Any]],
        current_records: Dict[str, Dict[str, Any]],
        dept_relations: Dict[str, List[str]],
        report_code: str
    ) -> List[BackfillOperation]:
        """批量操作分类"""
        operations = []
        
        # 解析report_code
        parts = report_code.split('_')
        dr07 = parts[0] if len(parts) >= 3 else report_code
        version = parts[-1] if len(parts) >= 3 else 'v1.0'
        
        for item in validated_data:
            entry_id = item['entry_id']
            current_record = current_records.get(entry_id)
            
            if not current_record:
                logger.warning(f"未找到对应记录，跳过: {entry_id}")
                continue
            
            # 检查字段变化
            dr22 = item['DR22']
            bdr01 = item['BDR01']
            bdr03 = item['BDR03']
            
            has_changes = self._check_field_changes(current_record, dr22, bdr01, bdr03)
            if not has_changes:
                continue
            
            # 确定操作类型
            operation_type = self._determine_operation_type(
                dr22, dept_relations, current_record
            )
            
            # 构建更新数据
            update_data = {
                'submission_id': entry_id,
                'dr07': dr07,
                'version': version,
                'dr22': dr22[0] if dr22 else '',
                'bdr01': bdr01[0] if bdr01 else '',
                'bdr03': bdr03[0] if bdr03 else '',
                'update_time': datetime.now()
            }
            
            operation = BackfillOperation(
                entry_id=entry_id,
                operation_type=operation_type,
                dr07=dr07,
                version=version,
                update_data=update_data,
                original_data=item
            )
            
            operations.append(operation)
        
        logger.info(f"操作分类完成: {len(operations)}个更新操作")
        return operations
    
    def _check_field_changes(
        self, 
        current_record: Dict[str, Any], 
        dr22: List[str], 
        bdr01: List[str], 
        bdr03: List[str]
    ) -> bool:
        """检查字段变化"""
        current_dr22 = current_record.get('dr22', '')
        current_bdr01 = current_record.get('bdr01', '')
        current_bdr03 = current_record.get('bdr03', '')
        
        new_dr22 = dr22[0] if dr22 else ''
        new_bdr01 = bdr01[0] if bdr01 else ''
        new_bdr03 = bdr03[0] if bdr03 else ''
        
        return (current_dr22 != new_dr22 or 
                current_bdr01 != new_bdr01 or 
                current_bdr03 != new_bdr03)
    
    def _determine_operation_type(
        self,
        dr22: List[str],
        dept_relations: Dict[str, List[str]],
        current_record: Dict[str, Any]
    ) -> str:
        """确定操作类型"""
        if not dr22:
            return 'clear_update'
        
        # 获取部门对应的table_ids
        dept_table_ids = set()
        for dept_id in dr22:
            if dept_id in dept_relations:
                dept_table_ids.update(dept_relations[dept_id])
        
        # 获取当前记录的table_id
        current_table_id = current_record.get('table_id')
        
        # 判断是否有交集
        if current_table_id and current_table_id in dept_table_ids:
            return 'field_update'
        else:
            return 'clear_update'
    
    async def _execute_batch_operations(
        self, 
        operations: List[BackfillOperation]
    ) -> Tuple[int, List[Dict[str, Any]]]:
        """批量执行更新操作"""
        if not operations:
            return 0, []
        
        # 分类操作
        field_updates = [op for op in operations if op.operation_type == 'field_update']
        clear_updates = [op for op in operations if op.operation_type == 'clear_update']
        
        total_updated = 0
        operation_details = []
        
        # 批量执行字段更新
        if field_updates:
            updated_count = await self._batch_field_updates(field_updates)
            total_updated += updated_count
            operation_details.extend([
                {
                    'entry_id': op.entry_id,
                    'operation_type': 'field_update',
                    'status': 'success' if updated_count > 0 else 'error'
                }
                for op in field_updates
            ])
        
        # 批量执行清空更新
        if clear_updates:
            updated_count = await self._batch_clear_updates(clear_updates)
            total_updated += updated_count
            operation_details.extend([
                {
                    'entry_id': op.entry_id,
                    'operation_type': 'clear_update',
                    'status': 'success' if updated_count > 0 else 'error'
                }
                for op in clear_updates
            ])
        
        return total_updated, operation_details
    
    async def _batch_field_updates(self, operations: List[BackfillOperation]) -> int:
        """批量字段更新"""
        if not operations:
            return 0
        
        try:
            # 准备批量更新数据
            update_data = []
            for op in operations:
                update_item = {
                    'where_conditions': {
                        'submission_id': op.entry_id,
                        'dr07': op.dr07,
                        'version': op.version
                    },
                    'update_fields': {
                        'dr22': op.update_data['dr22'],
                        'bdr01': op.update_data['bdr01'],
                        'bdr03': op.update_data['bdr03'],
                        'update_time': op.update_data['update_time']
                    }
                }
                update_data.append(update_item)
            
            # 执行批量更新
            result = await self.rdb_client.abatch_update(
                table="biz_dd_post_distribution",
                data=update_data,
                batch_size=self.config.batch_size,
                max_concurrency=self.config.max_concurrency
            )
            
            if result.success:
                logger.info(f"批量字段更新成功: {result.affected_rows}条记录")
                return result.affected_rows
            else:
                logger.error(f"批量字段更新失败: {result.error_message}")
                return 0
                
        except Exception as e:
            logger.error(f"批量字段更新异常: {e}")
            # 降级到逐条处理
            return await self._fallback_individual_field_updates(operations)
    
    async def _batch_clear_updates(self, operations: List[BackfillOperation]) -> int:
        """批量清空更新"""
        if not operations:
            return 0
        
        try:
            # 准备批量清空更新数据
            update_data = []
            for op in operations:
                update_item = {
                    'where_conditions': {
                        'submission_id': op.entry_id,
                        'dr07': op.dr07,
                        'version': op.version
                    },
                    'update_fields': {
                        # 清空BRD05到sdr15字段
                        'bdr05': None, 'bdr06': None, 'bdr07': None, 'bdr08': None, 'bdr09': None,
                        'bdr10': None, 'bdr11': None, 'bdr12': None, 'bdr13': None, 'bdr14': None,
                        'bdr15': None, 'bdr16': None, 'bdr17': None, 'bdr18': None,
                        'sdr01': None, 'sdr02': None, 'sdr03': None, 'sdr04': None, 'sdr05': None,
                        'sdr06': None, 'sdr07': None, 'sdr08': None, 'sdr09': None, 'sdr10': None,
                        'sdr11': None, 'sdr12': None, 'sdr13': None, 'sdr14': None, 'sdr15': None,
                        # 设置新值
                        'dr22': op.update_data['dr22'],
                        'bdr01': op.update_data['bdr01'],
                        'bdr03': op.update_data['bdr03'],
                        'update_time': op.update_data['update_time']
                    }
                }
                update_data.append(update_item)
            
            # 执行批量清空更新
            result = await self.rdb_client.abatch_update(
                table="biz_dd_post_distribution",
                data=update_data,
                batch_size=self.config.batch_size,
                max_concurrency=self.config.max_concurrency
            )
            
            if result.success:
                logger.info(f"批量清空更新成功: {result.affected_rows}条记录")
                return result.affected_rows
            else:
                logger.error(f"批量清空更新失败: {result.error_message}")
                return 0
                
        except Exception as e:
            logger.error(f"批量清空更新异常: {e}")
            # 降级到逐条处理
            return await self._fallback_individual_clear_updates(operations)
    
    async def _fallback_individual_field_updates(self, operations: List[BackfillOperation]) -> int:
        """降级：逐条字段更新"""
        success_count = 0
        
        for op in operations:
            try:
                sql = """
                UPDATE biz_dd_post_distribution
                SET dr22 = %s, bdr01 = %s, bdr03 = %s, update_time = %s
                WHERE submission_id = %s AND dr07 = %s AND version = %s
                """
                
                params = (
                    op.update_data['dr22'],
                    op.update_data['bdr01'],
                    op.update_data['bdr03'],
                    op.update_data['update_time'],
                    op.entry_id,
                    op.dr07,
                    op.version
                )
                
                result = await self.rdb_client.aexecute(sql, params)
                if result.success and result.affected_rows > 0:
                    success_count += 1
                    
            except Exception as e:
                logger.error(f"逐条字段更新失败 {op.entry_id}: {e}")
        
        logger.info(f"降级逐条字段更新完成: {success_count}/{len(operations)}")
        return success_count
    
    async def _fallback_individual_clear_updates(self, operations: List[BackfillOperation]) -> int:
        """降级：逐条清空更新"""
        success_count = 0
        
        for op in operations:
            try:
                sql = """
                UPDATE biz_dd_post_distribution
                SET
                    bdr05 = NULL, bdr06 = NULL, bdr07 = NULL, bdr08 = NULL, bdr09 = NULL,
                    bdr10 = NULL, bdr11 = NULL, bdr12 = NULL, bdr13 = NULL, bdr14 = NULL,
                    bdr15 = NULL, bdr16 = NULL, bdr17 = NULL, bdr18 = NULL,
                    sdr01 = NULL, sdr02 = NULL, sdr03 = NULL, sdr04 = NULL, sdr05 = NULL,
                    sdr06 = NULL, sdr07 = NULL, sdr08 = NULL, sdr09 = NULL, sdr10 = NULL,
                    sdr11 = NULL, sdr12 = NULL, sdr13 = NULL, sdr14 = NULL, sdr15 = NULL,
                    dr22 = %s, bdr01 = %s, bdr03 = %s, update_time = %s
                WHERE submission_id = %s AND dr07 = %s AND version = %s
                """
                
                params = (
                    op.update_data['dr22'],
                    op.update_data['bdr01'],
                    op.update_data['bdr03'],
                    op.update_data['update_time'],
                    op.entry_id,
                    op.dr07,
                    op.version
                )
                
                result = await self.rdb_client.aexecute(sql, params)
                if result.success and result.affected_rows > 0:
                    success_count += 1
                    
            except Exception as e:
                logger.error(f"逐条清空更新失败 {op.entry_id}: {e}")
        
        logger.info(f"降级逐条清空更新完成: {success_count}/{len(operations)}")
        return success_count
    
    async def _fallback_process_data_backfill(
        self,
        report_code: str,
        step: str,
        data: List[Dict[str, Any]]
    ) -> BackfillResult:
        """降级：使用原始处理方式"""
        start_time = time.time()
        
        try:
            # 导入原始的BackfillEngine
            from modules.dd_submission.department_assignment.core.backfill_engine import BackfillEngine
            
            original_engine = BackfillEngine(self.rdb_client, self.vdb_client)
            result = await original_engine.process_data_backfill(report_code, step, data)
            
            # 更新性能统计
            self.performance_stats['fallback_operations'] += 1
            
            logger.info("降级处理完成")
            return BackfillResult(
                success=result.get('success', False),
                total_count=len(data),
                updated_count=result.get('updated_count', 0),
                error_count=result.get('error_count', 0),
                processing_time_ms=(time.time() - start_time) * 1000,
                details=result.get('processing_details', []),
                error_message=result.get('message') if not result.get('success') else None
            )
            
        except Exception as e:
            logger.error(f"降级处理也失败: {e}")
            return BackfillResult(
                success=False,
                total_count=len(data),
                updated_count=0,
                error_count=len(data),
                processing_time_ms=(time.time() - start_time) * 1000,
                details=[],
                error_message=f"降级处理失败: {str(e)}"
            )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            **self.performance_stats,
            'avg_processing_time': (
                self.performance_stats['total_processing_time'] / 
                self.performance_stats['total_operations']
                if self.performance_stats['total_operations'] > 0 else 0
            ),
            'batch_success_rate': (
                self.performance_stats['batch_operations'] / 
                self.performance_stats['total_operations']
                if self.performance_stats['total_operations'] > 0 else 0
            )
        }

"""
SQL文件一致性测试

验证所有CRUD操作与 create_rdb_metadata.sql 文件中的表结构完全一致
"""

import asyncio
import logging
import time
from typing import Dict, Any, List

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局测试数据存储
test_data_store = {
    'knowledge_id': None,
    'source_db_id': None,
    'index_db_id': None,
    'source_table_id': None,
    'index_table_id': None,
    'source_column_id': None,
    'index_column_id': None,
    'relation_id': None,
    'code_set_id': None,
    'code_value_id': None,
    'code_relation_id': None,
    'subject_id': None,
    'subject_relation_id': None
}


async def setup_test_environment():
    """设置测试环境，创建测试知识库"""
    print("🔧 设置测试环境")
    print("-" * 40)

    try:
        # 获取客户端
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
        from service import get_client
        from modules.knowledge.knowledge.crud import KnowledgeCrud

        rdb_client = await get_client("database.rdbs.mysql")
        knowledge_crud = KnowledgeCrud(rdb_client)

        # 创建测试知识库
        timestamp = int(time.time())
        test_kb_data = {
            'knowledge_name': f'SQL一致性测试知识库_{timestamp}',
            'knowledge_type': 'MetaData',
            'knowledge_desc': 'SQL文件一致性测试知识库',
            'models': {
                'embedding': 'moka-m3e-base'
            }
        }

        knowledge_id = await knowledge_crud.create_knowledge_base(test_kb_data)
        if not knowledge_id:
            raise Exception("创建测试知识库失败")

        test_data_store['knowledge_id'] = knowledge_id
        print(f"   ✅ 创建测试知识库: {knowledge_id}")

        return rdb_client, knowledge_id

    except Exception as e:
        logger.error(f"设置测试环境失败: {e}")
        raise


async def cleanup_test_environment():
    """清理测试环境"""
    print("\n🧹 清理测试环境")
    print("-" * 40)

    try:
        if test_data_store['knowledge_id']:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            from modules.knowledge.knowledge.crud import KnowledgeCrud

            rdb_client = await get_client("database.rdbs.mysql")
            knowledge_crud = KnowledgeCrud(rdb_client)

            # 删除测试知识库（会级联删除所有相关数据）
            await knowledge_crud.delete_knowledge_base(test_data_store['knowledge_id'])
            print(f"   ✅ 删除测试知识库: {test_data_store['knowledge_id']}")

    except Exception as e:
        logger.error(f"清理测试环境失败: {e}")


async def test_database_tables(rdb_client, knowledge_id: str):
    """测试数据库表的字段一致性"""
    print("\n1️⃣ 测试数据库表字段一致性:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("models.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的

        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)

        timestamp = int(time.time())

        # 1. 测试源数据库表字段
        source_db_data = {
            'knowledge_id': knowledge_id,
            'db_name': f'test_source_db_{timestamp}',
            'db_name_cn': f'测试源数据库_{timestamp}',
            'data_layer': 'ods',  # 必须是 adm/bdm/ods/ads 之一
            'db_desc': '测试源数据库描述',
            'is_active': True
        }

        source_db_id, _ = await meta_crud.create_source_database(source_db_data)
        test_data_store['source_db_id'] = source_db_id
        print(f"   ✅ 源数据库表字段一致: {source_db_id}")

        # 2. 测试指标数据库表字段
        index_db_data = {
            'knowledge_id': knowledge_id,
            'db_name': f'test_index_db_{timestamp}',
            'db_name_cn': f'测试指标数据库_{timestamp}',
            'data_layer': 'ads',  # 必须是 adm/bdm/ods/ads 之一
            'db_desc': '测试指标数据库描述',
            'is_active': True
        }

        index_db_id, _ = await meta_crud.create_index_database(index_db_data)
        test_data_store['index_db_id'] = index_db_id
        print(f"   ✅ 指标数据库表字段一致: {index_db_id}")

        return True

    except Exception as e:
        print(f"   ❌ 数据库表字段一致性测试失败: {e}")
        return False


async def test_table_tables(rdb_client, knowledge_id: str):
    """测试表管理表的字段一致性"""
    print("\n2️⃣ 测试表管理表字段一致性:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta
        meta_crud = MetadataCrudMeta(rdb_client)

        timestamp = int(time.time())

        # 1. 测试源表字段
        source_table_data = {
            'knowledge_id': knowledge_id,
            'db_id': test_data_store['source_db_id'],
            'table_name': f'test_source_table_{timestamp}',
            'table_name_cn': f'测试源表_{timestamp}',
            'table_desc': '测试源表描述',
            'is_active': True
        }

        source_table_id, _ = await meta_crud.create_source_table(source_table_data)
        test_data_store['source_table_id'] = source_table_id
        print(f"   ✅ 源表管理表字段一致: {source_table_id}")

        # 2. 测试指标表字段
        index_table_data = {
            'knowledge_id': knowledge_id,
            'db_id': test_data_store['index_db_id'],
            'table_name': f'test_index_table_{timestamp}',
            'table_name_cn': f'测试指标表_{timestamp}',
            'table_desc': '测试指标表描述',
            'is_active': True
        }

        index_table_id, _ = await meta_crud.create_index_table(index_table_data)
        test_data_store['index_table_id'] = index_table_id
        print(f"   ✅ 指标表管理表字段一致: {index_table_id}")

        return True

    except Exception as e:
        print(f"   ❌ 表管理表字段一致性测试失败: {e}")
        return False


async def test_column_tables(rdb_client, knowledge_id: str):
    """测试字段管理表的字段一致性"""
    print("\n3️⃣ 测试字段管理表字段一致性:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta
        meta_crud = MetadataCrudMeta(rdb_client)

        timestamp = int(time.time())

        # 1. 测试源字段（包含所有SQL文件中定义的字段）
        source_column_data = {
            'knowledge_id': knowledge_id,
            'table_id': test_data_store['source_table_id'],
            'column_name': f'test_source_column_{timestamp}',
            'column_name_cn': f'测试源字段_{timestamp}',
            'column_desc': '测试源字段描述',
            'data_type': 'STRING',
            'data_example': '示例数据',
            'is_vectorized': False,
            'is_primary_key': False,
            'is_sensitive': False
        }

        source_column_id, _ = await meta_crud.create_source_column(source_column_data)
        test_data_store['source_column_id'] = source_column_id
        print(f"   ✅ 源字段管理表字段一致: {source_column_id}")

        # 2. 测试指标字段（包含特有的 index_type 和 comment 字段）
        index_column_data = {
            'knowledge_id': knowledge_id,
            'table_id': test_data_store['index_table_id'],
            'column_name': f'test_index_column_{timestamp}',
            'column_name_cn': f'测试指标字段_{timestamp}',
            'index_type': 'atom',  # 指标字段特有字段
            'column_desc': '测试指标字段描述',
            'data_type': 'NUMBER',
            'data_example': '123.45',
            'comment': '指标字段备注说明',  # 指标字段特有字段
            'is_vectorized': False,
            'is_primary_key': False,
            'is_sensitive': False
        }

        index_column_id, _ = await meta_crud.create_index_column(index_column_data)
        test_data_store['index_column_id'] = index_column_id
        print(f"   ✅ 指标字段管理表字段一致: {index_column_id}")

        return True

    except Exception as e:
        print(f"   ❌ 字段管理表字段一致性测试失败: {e}")
        return False


async def test_relation_tables(rdb_client, knowledge_id: str):
    """测试关联键信息表的字段一致性"""
    print("\n4️⃣ 测试关联键信息表字段一致性:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_relations import MetadataCrudRelations
        relations_crud = MetadataCrudRelations(rdb_client)

        # 创建第二个源字段作为目标字段
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta
        meta_crud = MetadataCrudMeta(rdb_client)
        
        timestamp = int(time.time())
        target_column_data = {
            'knowledge_id': knowledge_id,
            'table_id': test_data_store['source_table_id'],
            'column_name': f'test_target_column_{timestamp}',
            'column_name_cn': f'测试目标字段_{timestamp}',
            'column_desc': '测试目标字段描述',
            'data_type': 'BIGINT',
            'is_primary_key': False,
            'is_sensitive': False
        }

        target_column_id, _ = await meta_crud.create_source_column(target_column_data)

        # 测试源关联键信息表（使用正确的字段名）
        source_relation_data = {
            'source_column_id': test_data_store['source_column_id'],
            'target_column_id': target_column_id,
            'relation_type': 'FK',
            'comment': '测试外键关联'
        }

        relation_id, _ = await relations_crud.create_source_key_relation(source_relation_data)
        test_data_store['relation_id'] = relation_id
        print(f"   ✅ 源关联键信息表字段一致: {relation_id}")

        return True

    except Exception as e:
        print(f"   ❌ 关联键信息表字段一致性测试失败: {e}")
        return False


async def test_subject_relation_table(rdb_client, knowledge_id: str):
    """测试数据主题关联表的字段一致性"""
    print("\n5️⃣ 测试数据主题关联表字段一致性:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_subjects import MetadataCrudSubjects
        subjects_crud = MetadataCrudSubjects(rdb_client)

        timestamp = int(time.time())

        # 1. 先创建数据主题
        subject_data = {
            'knowledge_id': knowledge_id,
            'subject_code': f'TEST_SUBJECT_{timestamp}',
            'subject_name': f'测试数据主题_{timestamp}',
            'subject_desc': '测试数据主题描述',
            'is_active': True
        }

        subject_id, _ = await subjects_crud.create_subject(subject_data)
        test_data_store['subject_id'] = subject_id
        print(f"   ✅ 数据主题创建: {subject_id}")

        # 2. 测试数据主题关联表（使用正确的字段名）
        subject_relation_data = {
            'knowledge_id': knowledge_id,
            'subject_id': subject_id,
            'source_type': 'SOURCE',  # 使用正确的字段名
            'relation_path': f'test_db.test_table.test_column',  # 使用正确的字段名
            'relation_level': 'COLUMN',  # 使用正确的字段名
            'is_active': True,
            'comment': '测试数据主题关联'
        }

        subject_relation_id, _ = await subjects_crud.create_subject_relation(subject_relation_data)
        test_data_store['subject_relation_id'] = subject_relation_id
        print(f"   ✅ 数据主题关联表字段一致: {subject_relation_id}")

        return True

    except Exception as e:
        print(f"   ❌ 数据主题关联表字段一致性测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 SQL文件一致性测试")
    print("=" * 80)

    try:
        # 1. 设置测试环境
        rdb_client, knowledge_id = await setup_test_environment()

        # 2. 执行所有测试
        test_results = []
        
        # 数据库表字段一致性测试
        result1 = await test_database_tables(rdb_client, knowledge_id)
        test_results.append(("数据库表字段一致性", result1))

        if result1:
            # 表管理表字段一致性测试
            result2 = await test_table_tables(rdb_client, knowledge_id)
            test_results.append(("表管理表字段一致性", result2))

            if result2:
                # 字段管理表字段一致性测试
                result3 = await test_column_tables(rdb_client, knowledge_id)
                test_results.append(("字段管理表字段一致性", result3))

                if result3:
                    # 关联键信息表字段一致性测试
                    result4 = await test_relation_tables(rdb_client, knowledge_id)
                    test_results.append(("关联键信息表字段一致性", result4))

                    # 数据主题关联表字段一致性测试
                    result5 = await test_subject_relation_table(rdb_client, knowledge_id)
                    test_results.append(("数据主题关联表字段一致性", result5))

        # 3. 输出测试结果
        print("\n" + "=" * 80)
        print("📊 SQL一致性测试结果汇总")
        print("=" * 80)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False

        if all_passed:
            print("\n🎉 所有测试通过！代码与SQL文件完全一致")
        else:
            print("\n⚠️  部分测试失败，存在不一致问题")

        return all_passed

    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        print(f"\n❌ 测试执行失败: {e}")
        return False

    finally:
        # 4. 清理测试环境
        await cleanup_test_environment()


if __name__ == "__main__":
    asyncio.run(main())

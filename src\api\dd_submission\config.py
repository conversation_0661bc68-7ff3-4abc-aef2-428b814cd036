"""
DD-B模块配置文件
"""

import os
from typing import Optional

class DDBConfig:
    """DD-B配置类"""
    
    # 前端回调配置
    FRONTEND_CALLBACK_URL: str = os.getenv(
        "DD_B_FRONTEND_CALLBACK_URL",
        "http://218.78.129.173:30134/regulatory/release/ai/back/ddDataBizBackAI"
    )
    
    # 批量处理配置
    BATCH_SIZE: int = int(os.getenv("DD_B_BATCH_SIZE", "15"))
    
    # 并发配置
    MAX_LLM_CONCURRENT: int = int(os.getenv("DD_B_MAX_LLM_CONCURRENT", "15"))
    
    # 超时配置
    CALLBACK_TIMEOUT: int = int(os.getenv("DD_B_CALLBACK_TIMEOUT", "30"))
    
    # 重试配置
    CALLBACK_RETRY_TIMES: int = int(os.getenv("DD_B_CALLBACK_RETRY_TIMES", "3"))
    
    @classmethod
    def get_frontend_callback_url(cls) -> str:
        """获取前端回调URL"""
        return cls.FRONTEND_CALLBACK_URL
    
    @classmethod
    def set_frontend_callback_url(cls, url: str):
        """设置前端回调URL"""
        cls.FRONTEND_CALLBACK_URL = url


# 全局配置实例
dd_b_config = DDBConfig()

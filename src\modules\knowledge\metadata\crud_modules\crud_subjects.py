"""
Metadata系统数据主题和关联CRUD操作

包含以下实体的CRUD操作：
- 数据主题 (md_data_subject)
- 数据主题关联 (md_data_subject_relation)
"""

from typing import Any, Dict, List, Optional, Tuple, Union
import logging
from datetime import datetime

from .crud_base import MetadataCrudBase
from ..shared.exceptions import MetadataError, MetadataValidationError, MetadataNotFoundError, MetadataConflictError
from ..shared.constants import MetadataConstants, MetadataTableNames, MetadataCascadeRelations, MetadataVectorCollections
from ..shared.utils import MetadataUtils

logger = logging.getLogger(__name__)


class MetadataCrudSubjects(MetadataCrudBase):
    """Metadata系统数据主题和关联CRUD操作"""

    # ==================== 数据主题操作 ====================

    async def create_data_subject(self, subject_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """
        创建数据主题

        Args:
            subject_data: 数据主题数据

        Returns:
            (主题ID, 向量创建结果列表)
        """
        try:
            # 数据验证
            MetadataUtils.validate_data_subject_data(subject_data)

            # 检查是否已存在
            existing = await self._aselect(
                table=MetadataTableNames.MD_DATA_SUBJECT,
                where={'knowledge_id': subject_data['knowledge_id'], 'subject_name': subject_data['subject_name']},
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"数据主题已存在: {subject_data['subject_name']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(subject_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_DATA_SUBJECT,
                data=[subject_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                raise MetadataError(f"创建数据主题失败: {result.error}")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_DATA_SUBJECT,
                where={'knowledge_id': subject_data['knowledge_id'], 'subject_name': subject_data['subject_name']},
                limit=1
            )

            subject_id = inserted_records[0].get('id', 0) if inserted_records else 0

            # 数据主题通常不需要向量化，但保留接口以备扩展
            vector_results = []

            logger.info(f"数据主题创建成功: {subject_data['subject_name']} (ID: {subject_id})")
            return subject_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建数据主题失败: {e}")
            raise MetadataError(f"创建数据主题失败: {e}")

    async def get_data_subject(self, subject_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取数据主题"""
        if subject_id:
            where = {'id': subject_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 subject_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_DATA_SUBJECT,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_data_subject(self, subject_data: Dict[str, Any], subject_id: int = None, **where_conditions) -> bool:
        """更新数据主题"""
        if subject_id:
            where = {'id': subject_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 subject_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(subject_data, is_update=True)

        try:
            updates = [{"data": subject_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_DATA_SUBJECT,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )
            return result.success and result.affected_rows > 0
        except Exception as e:
            logger.error(f"更新数据主题失败: {e}")
            raise MetadataError(f"更新数据主题失败: {e}")

    async def delete_data_subject(self, subject_id: int = None, **where_conditions) -> bool:
        """删除数据主题"""
        if subject_id:
            where = {'id': subject_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 subject_id 或其他删除条件")

        try:
            # 删除数据主题记录（MySQL会自动级联删除相关关联记录）
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_DATA_SUBJECT,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除数据主题失败: {e}")
            raise MetadataError(f"删除数据主题失败: {e}")

    async def list_data_subjects(
        self,
        knowledge_id: Optional[str] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询数据主题列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_DATA_SUBJECT,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    async def batch_list_data_subjects(
        self,
        query_conditions: List[Dict[str, Any]]
    ) -> List[List[Dict[str, Any]]]:
        """
        批量查询数据主题列表 - 用于需要同时执行多个不同查询条件的场景
        
        Args:
            query_conditions: 查询条件列表
            
        Returns:
            每个查询条件对应的结果列表
        """
        if not query_conditions:
            return []
            
        try:
            # 构建批量查询请求
            queries = []
            for condition in query_conditions:
                where = MetadataUtils.build_search_filters(
                    knowledge_id=condition.get('knowledge_id'),
                    is_active=condition.get('is_active'),
                    **{k: v for k, v in condition.items() if k not in ['knowledge_id', 'is_active', 'limit', 'offset']}
                )
                
                query = {"table": MetadataTableNames.MD_DATA_SUBJECT}
                if where:
                    query["filters"] = where
                if condition.get('limit'):
                    query["limit"] = condition['limit']
                if condition.get('offset'):
                    query["offset"] = condition['offset']
                query["sorts"] = [{"field": "create_time", "order": "desc"}]
                
                queries.append(query)
            
            # 执行批量查询
            batch_results = await self.rdb_client.abatch_query(
                table=MetadataTableNames.MD_DATA_SUBJECT,
                queries=queries
            )
            
            # 提取结果数据
            return [result.data if result else [] for result in batch_results]
            
        except Exception as e:
            logger.error(f"批量查询数据主题列表失败: {e}")
            raise MetadataError(f"批量查询数据主题列表失败: {e}")

    # ==================== 数据主题关联操作 ====================

    async def create_subject_relation(self, relation_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建数据主题关联"""
        try:
            # 数据验证
            MetadataUtils.validate_subject_relation_data(relation_data)

            # 添加时间戳
            MetadataUtils.add_timestamps(relation_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_DATA_SUBJECT_RELATION,
                data=[relation_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                raise MetadataError(f"创建数据主题关联失败: {result.error}")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_DATA_SUBJECT_RELATION,
                where={
                    'knowledge_id': relation_data['knowledge_id'],
                    'subject_id': relation_data['subject_id'],
                    'source_type': relation_data['source_type'],
                    'relation_path': relation_data['relation_path']
                },
                limit=1
            )

            relation_id = inserted_records[0].get('id', 0) if inserted_records else 0

            logger.info(f"数据主题关联创建成功: {relation_data['source_type']}_{relation_data['relation_path']} (ID: {relation_id})")
            return relation_id, []

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建数据主题关联失败: {e}")
            raise MetadataError(f"创建数据主题关联失败: {e}")

    async def get_subject_relation(self, relation_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取数据主题关联"""
        if relation_id:
            where = {'id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_DATA_SUBJECT_RELATION,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def delete_subject_relation(self, relation_id: int = None, **where_conditions) -> bool:
        """删除数据主题关联"""
        if relation_id:
            where = {'id': relation_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 relation_id 或其他删除条件")

        try:
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_DATA_SUBJECT_RELATION,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除数据主题关联失败: {e}")
            raise MetadataError(f"删除数据主题关联失败: {e}")

    async def list_subject_relations(
        self,
        knowledge_id: Optional[str] = None,
        subject_id: Optional[int] = None,
        source_type: Optional[str] = None,
        relation_level: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询数据主题关联列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            subject_id=subject_id,
            source_type=source_type,
            relation_level=relation_level,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_DATA_SUBJECT_RELATION,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    # ==================== 方法别名（向后兼容） ====================

    async def create_subject(self, subject_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建数据主题（别名方法）"""
        return await self.create_data_subject(subject_data)

    async def get_subject(self, subject_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取数据主题（别名方法）"""
        return await self.get_data_subject(subject_id, **where_conditions)

    async def update_subject(self, subject_data: Dict[str, Any], subject_id: int = None, **where_conditions) -> bool:
        """更新数据主题（别名方法）"""
        return await self.update_data_subject(subject_data, subject_id, **where_conditions)

    async def delete_subject(self, subject_id: int = None, **where_conditions) -> bool:
        """删除数据主题（别名方法）"""
        return await self.delete_data_subject(subject_id, **where_conditions)

    async def list_subjects(
        self,
        knowledge_id: Optional[str] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询数据主题列表（别名方法）"""
        return await self.list_data_subjects(knowledge_id, is_active, limit, offset, **filters)

    # ==========================================
    # 搜索功能
    # ==========================================

    async def search_data_subjects(self, search_term: str, knowledge_id: Optional[str] = None,
                                  is_active: Optional[bool] = None,
                                  limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        搜索数据主题

        Args:
            search_term: 搜索词
            knowledge_id: 知识库ID
            is_active: 是否激活
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            where_conditions = {}

            if knowledge_id:
                where_conditions['knowledge_id'] = knowledge_id
            if is_active is not None:
                where_conditions['is_active'] = is_active

            # 简化搜索：按主题名称搜索
            if search_term:
                where_conditions['subject_name'] = f"%{search_term}%"

            return await self._aselect(
                table=MetadataTableNames.MD_DATA_SUBJECT,
                where=where_conditions,
                order_by=['create_time DESC'],
                limit=limit,
                offset=offset
            )

        except Exception as e:
            logger.error(f"搜索数据主题失败: {e}")
            return []

# -*- coding: utf-8 -*-
"""
SchemaGenerator 核心方法工作原理演示

详细展示 split() 和 get_prompt_list_with_mappings() 的内部机制
"""

import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..','..'))

from generate import SchemaGenerator
from service import get_client


async def demo_split_mechanism():
    """演示 split() 方法的工作机制"""
    print("🔍 split() 方法工作机制演示")
    print("=" * 60)
    
    # 使用现有配置
    client = get_client("database.rdbs.mysql")
    generator = SchemaGenerator("source", client=client)
    
    # 加载数据
    print("\n📋 步骤1: 加载数据")
    await generator.from_table_names(["adm_lon_varoius", "test_complete_db"], column_limit=8)
    
    print(f"   加载的表数量: {len(generator._tables_info)}")
    print(f"   加载的列数量: {len(generator._columns_info)}")
    
    # 演示分组过程
    print("\n🔄 步骤2: 智能分组过程")
    token_size = 3000  # 使用较小的token_size来演示分组
    
    # 手动调用分组方法来展示过程
    column_groups = generator._group_columns_by_token_limit(token_size)
    print(f"   Token限制: {token_size}")
    print(f"   分组数量: {len(column_groups)}")
    
    for i, group in enumerate(column_groups):
        print(f"   组 {i+1}: {len(group)} 列")
        
        # 显示每组涉及的表
        table_ids = list(set(col.get('table_id') for col in group))
        involved_tables = [t for t in generator._tables_info if t.get('table_id') in table_ids]
        table_names = [t.get('table_name', 'unknown') for t in involved_tables]
        print(f"     涉及表: {table_names}")
        
        # 估算组大小
        group_size = sum(len(generator._build_single_column_schema(col)) for col in group) + 200
        print(f"     估算大小: {group_size} 字符")
    
    # 调用实际的split方法
    print("\n📄 步骤3: 生成最终prompt")
    prompts = generator.split(token_size=token_size)
    print(f"   生成prompt数量: {len(prompts)}")
    
    for i, prompt in enumerate(prompts):
        print(f"   Prompt {i+1}: {len(prompt)} 字符")
        # 显示prompt的开头部分
        lines = prompt.split('\n')[:5]
        print(f"     开头: {' | '.join(lines)}")
    
    return prompts


async def demo_mappings_mechanism():
    """演示 get_prompt_list_with_mappings() 方法的工作机制"""
    print("\n\n🎯 get_prompt_list_with_mappings() 方法工作机制演示")
    print("=" * 60)
    
    # 使用现有配置
    client = get_client("database.rdbs.mysql")
    generator = SchemaGenerator("source", client=client)
    
    # 加载数据
    print("\n📋 步骤1: 加载数据")
    await generator.from_table_names(["adm_lon_varoius", "test_complete_db"], column_limit=6)
    
    # 调用带映射的方法
    print("\n🎯 步骤2: 生成带映射的prompt")
    token_size = 4000
    mapping_prompts = generator.get_prompt_list_with_mappings(token_size=token_size)
    
    print(f"   Token限制: {token_size}")
    print(f"   生成片段数量: {len(mapping_prompts)}")
    
    # 详细分析每个片段
    for i, item in enumerate(mapping_prompts):
        print(f"\n📊 片段 {i+1} 详细分析:")
        print(f"   Prompt长度: {len(item['prompt'])} 字符")
        
        # 分析db_to_tables映射
        db_to_tables = item['db_to_tables']
        print(f"   数据库映射 (db_to_tables):")
        for db_name, tables in db_to_tables.items():
            print(f"     📁 {db_name}: {tables}")
        
        # 分析table_to_columns映射
        table_to_columns = item['table_to_columns']
        print(f"   表列映射 (table_to_columns):")
        for table_name, columns in table_to_columns.items():
            print(f"     📊 {table_name}: {len(columns)}列 - {columns[:3]}{'...' if len(columns) > 3 else ''}")
        
        # 统计信息
        total_dbs = len(db_to_tables)
        total_tables = len(table_to_columns)
        total_columns = sum(len(cols) for cols in table_to_columns.values())
        print(f"   📈 统计: {total_dbs}个数据库, {total_tables}个表, {total_columns}个列")
    
    return mapping_prompts


async def demo_performance_comparison():
    """演示两个方法的性能对比"""
    print("\n\n⚡ 性能对比演示")
    print("=" * 60)
    
    # 使用现有配置
    client = get_client("database.rdbs.mysql")
    generator = SchemaGenerator("source", client=client)
    
    # 加载数据
    await generator.from_table_names(["adm_lon_varoius", "test_complete_db"], column_limit=10)
    
    import time
    
    # 测试split()方法
    print("\n🚀 测试 split() 方法:")
    start_time = time.time()
    simple_prompts = generator.split(token_size=5000)
    split_time = time.time() - start_time
    
    print(f"   执行时间: {split_time:.4f} 秒")
    print(f"   生成片段: {len(simple_prompts)} 个")
    print(f"   内存使用: 约 {sum(len(p) for p in simple_prompts)} 字符")
    
    # 测试get_prompt_list_with_mappings()方法
    print("\n🎯 测试 get_prompt_list_with_mappings() 方法:")
    start_time = time.time()
    mapping_prompts = generator.get_prompt_list_with_mappings(token_size=5000)
    mappings_time = time.time() - start_time
    
    print(f"   执行时间: {mappings_time:.4f} 秒")
    print(f"   生成片段: {len(mapping_prompts)} 个")
    
    # 计算映射数据的大小
    mapping_size = 0
    for item in mapping_prompts:
        mapping_size += len(item['prompt'])
        mapping_size += len(str(item['db_to_tables']))
        mapping_size += len(str(item['table_to_columns']))
    
    print(f"   内存使用: 约 {mapping_size} 字符 (包含映射数据)")
    
    # 性能对比
    print(f"\n📊 性能对比:")
    print(f"   时间差异: {mappings_time/split_time:.2f}x (映射方法相对较慢)")
    print(f"   内存差异: {mapping_size/sum(len(p) for p in simple_prompts):.2f}x (映射方法占用更多内存)")
    
    # 验证prompt内容是否相同
    prompts_match = len(simple_prompts) == len(mapping_prompts)
    if prompts_match:
        for i, (simple, mapping) in enumerate(zip(simple_prompts, mapping_prompts)):
            if simple != mapping['prompt']:
                prompts_match = False
                break
    
    print(f"   Prompt内容: {'✅ 完全相同' if prompts_match else '❌ 存在差异'}")


async def demo_usage_scenarios():
    """演示实际使用场景"""
    print("\n\n💼 实际使用场景演示")
    print("=" * 60)
    
    # 使用现有配置
    client = get_client("database.rdbs.mysql")
    generator = SchemaGenerator("source", client=client)
    await generator.from_table_names(["adm_lon_varoius", "test_complete_db"], column_limit=5)
    
    # 场景1: 简单LLM调用
    print("\n🤖 场景1: 简单LLM调用 - 使用 split()")
    simple_prompts = generator.split(token_size=4000)
    
    print("   适用于:")
    print("   - 直接发送给LLM，不需要额外处理")
    print("   - 批量处理大量数据")
    print("   - 内存受限的环境")
    
    print(f"   示例代码:")
    print(f"   for prompt in prompts:")
    print(f"       llm_response = send_to_llm(prompt)")
    print(f"       process_simple_response(llm_response)")
    
    # 场景2: 复杂响应处理
    print("\n🎯 场景2: 复杂响应处理 - 使用 get_prompt_list_with_mappings()")
    mapping_prompts = generator.get_prompt_list_with_mappings(token_size=4000)
    
    print("   适用于:")
    print("   - 需要根据表/列信息处理LLM响应")
    print("   - 错误追踪和调试")
    print("   - 分表分库的精确处理")
    
    print(f"   示例代码:")
    print(f"   for item in mapping_prompts:")
    print(f"       llm_response = send_to_llm(item['prompt'])")
    print(f"       for db, tables in item['db_to_tables'].items():")
    print(f"           process_db_response(db, tables, llm_response)")
    print(f"       for table, columns in item['table_to_columns'].items():")
    print(f"           process_table_response(table, columns, llm_response)")


async def main():
    """主演示函数"""
    print("🔬 SchemaGenerator 核心方法深度分析")
    print("=" * 80)
    
    try:
        await demo_split_mechanism()
        await demo_mappings_mechanism()
        await demo_performance_comparison()
        await demo_usage_scenarios()
        
        print("\n\n🎉 演示完成！")
        print("\n📚 总结:")
        print("  🔄 split(): 智能分组 + prompt构建")
        print("  🎯 get_prompt_list_with_mappings(): 智能分组 + prompt构建 + 映射生成")
        print("  ⚡ 性能: split()更快，mappings()功能更强")
        print("  💡 选择: 根据是否需要映射信息来决定使用哪个方法")
        
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())

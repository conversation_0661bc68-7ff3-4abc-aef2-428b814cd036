"""
MySQL数据库客户端

基于Universal架构设计的MySQL专用数据库客户端
直接基于原生MySQL驱动实现，不使用SQLAlchemy ORM
提供完整的RDB抽象层接口支持
"""

import asyncio
import time
from contextlib import asynccontextmanager, contextmanager
from typing import Dict, Any, List, Optional, Union
import logging

logger = logging.getLogger(__name__)

try:
    import pymysql
    import pymysql.cursors
except ImportError:
    pymysql = None

try:
    import aiomysql
except ImportError:
    aiomysql = None

# 导入RDB抽象层
from ....base.rdb import (
    # 核心接口
    DatabaseClient, DatabaseType, TransactionIsolation,

    # 请求和响应模型
    QueryRequest, InsertRequest, UpdateRequest, DeleteRequest,
    QueryResponse, OperationResponse,

    # 异常
    ConnectionError as RDBConnectionError, TransactionError as RDBTransactionError,

    # 适配器
    DefaultResultAdapter, DefaultErrorAdapter
)

from .config import MySQLConnectionConfig
from .decorators import mysql_operation
from .exceptions import (
    MySQLError, MySQLConnectionError, MySQLQueryError,
    MySQLTransactionError, wrap_mysql_error
)
from .pool_manager import PooledConnectionMixin


class MySQLClient(PooledConnectionMixin, DatabaseClient):
    """
    MySQL数据库客户端

    基于原生MySQL驱动的高性能数据库客户端
    实现完整的RDB抽象层接口，支持同步和异步操作
    """

    def __init__(self, config: MySQLConnectionConfig):
        """
        初始化MySQL客户端

        Args:
            config: MySQLConnectionConfig实例
        """
        # 统一入口：只接受MySQLConnectionConfig
        if not isinstance(config, MySQLConnectionConfig):
            raise ValueError("config must be a MySQLConnectionConfig instance")

        # 先设置config，再调用父类初始化
        self.config = config

        # 调用父类初始化（包括PooledConnectionMixin）
        super().__init__()

        # 创建适配器
        self.result_adapter = DefaultResultAdapter(DatabaseType.MYSQL)
        self.error_adapter = DefaultErrorAdapter(DatabaseType.MYSQL)

        # 连接状态 - 分别跟踪同步和异步连接状态
        self._sync_connected = False
        self._async_connected = False

        # 性能统计
        self._performance_stats = {
            'operations_count': 0,
            'total_execution_time': 0.0,
            'query_times': [],
            'last_operation_time': None
        }

        logger.info(f"Initialized MySQL Client for {self.config.host}:{self.config.port}")

    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            # 清理连接池
            self.close_pools()
            if hasattr(self, '_sync_connected'):
                self._sync_connected = False
            if hasattr(self, '_async_connected'):
                self._async_connected = False
        except Exception:
            # 忽略析构函数中的错误
            pass

    # ==================== RDB Interface Methods ====================

    def get_database_type(self) -> DatabaseType:
        """获取数据库类型"""
        return DatabaseType.MYSQL

    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._sync_connected or self._async_connected

    # ==================== Connection Management ====================

    def _ensure_sync_connection(self) -> None:
        """确保同步连接已建立并自动连接"""
        if not self._sync_connected:
            self.connect()

    async def _ensure_async_connection(self) -> None:
        """确保异步连接已建立并自动连接"""
        if not self._async_connected:
            await self.aconnect()

    def connect(self) -> None:
        """Connect to the MySQL database (sync only)"""
        try:
            # 测试连接池
            with self.get_sync_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()

            self._sync_connected = True
            logger.info(f"Successfully connected to MySQL database (sync)")

        except Exception as e:
            error = wrap_mysql_error(e, "sync connection")
            logger.error(f"Failed to connect to MySQL database (sync): {error}")
            raise error

    async def aconnect(self) -> None:
        """Connect to the MySQL database (async only)"""
        try:
            # 测试异步连接池
            async with self.get_async_connection() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1")
                    await cursor.fetchone()

            self._async_connected = True
            logger.info(f"Successfully connected to MySQL database (async)")

        except Exception as e:
            error = wrap_mysql_error(e, "async connection")
            logger.error(f"Failed to connect to MySQL database (async): {error}")
            raise error

    def disconnect(self) -> None:
        """Disconnect from the MySQL database (sync only)"""
        try:
            # 关闭连接池
            self.close_pools()
            self._sync_connected = False
            logger.info("Disconnected from MySQL database (sync)")

        except Exception as e:
            logger.error(f"Error during sync disconnect: {e}")
            # 在清理阶段，记录错误但不抛出异常
            import inspect
            frame = inspect.currentframe()
            try:
                # 检查调用栈，如果是在清理过程中，不抛出异常
                is_cleanup = any('cleanup' in str(f.filename).lower() or 'cleanup' in str(f.function).lower()
                               for f in inspect.getouterframes(frame)[1:5])
                if not is_cleanup:
                    raise wrap_mysql_error(e, "sync disconnection")
            finally:
                del frame

    async def adisconnect(self) -> None:
        """Disconnect from the MySQL database (async only)"""
        try:
            # 关闭连接池
            await self.aclose_pools()
            self._async_connected = False
            logger.info("Disconnected from MySQL database (async)")

        except Exception as e:
            logger.error(f"Error during async disconnect: {e}")
            # 在清理阶段，记录错误但不抛出异常
            import inspect
            frame = inspect.currentframe()
            try:
                # 检查调用栈，如果是在清理过程中，不抛出异常
                is_cleanup = any('cleanup' in str(f.filename).lower() or 'cleanup' in str(f.function).lower()
                               for f in inspect.getouterframes(frame)[1:5])
                if not is_cleanup:
                    raise wrap_mysql_error(e, "async disconnection")
            finally:
                del frame

    def _reconnect_sync(self) -> None:
        """重新建立同步连接"""
        try:
            self.disconnect()
        except:
            pass
        self.connect()

    async def _reconnect_async(self) -> None:
        """重新建立异步连接"""
        try:
            await self.adisconnect()
        except:
            pass
        await self.aconnect()

    # ==================== RDB Interface Query Methods ====================

    @mysql_operation('query')
    def query(self, request: Union[QueryRequest, Dict[str, Any]]) -> QueryResponse:
        """
        执行查询（同步）- RDB接口方法

        Args:
            request: 查询请求，可以是QueryRequest实体或字典

        Returns:
            QueryResponse: 查询结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()

        try:
            # 构建SQL查询
            sql, params = self._build_select_sql(request)

            # 执行查询
            with self.get_sync_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    rows = cursor.fetchall()

            # 创建响应
            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time)
            
            return QueryResponse(
                data=rows,
                total_count=len(rows),
                execution_time=execution_time,
                query_sql=sql,
                query_parameters=params,
                database_type=DatabaseType.MYSQL
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "query",
                "table": request.table,
                "request": str(request)
            })

    @mysql_operation('query')
    async def aquery(self, request: Union[QueryRequest, Dict[str, Any]]) -> QueryResponse:
        """
        执行查询（异步）- RDB接口方法

        Args:
            request: 查询请求，可以是QueryRequest实体或字典

        Returns:
            QueryResponse: 查询结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()

        try:
            # 构建SQL查询
            sql, params = self._build_select_sql(request)

            # 执行查询
            async with self.get_async_connection() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(sql, params)
                    rows = await cursor.fetchall()

            # 创建响应
            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time)
            
            return QueryResponse(
                data=rows,
                total_count=len(rows),
                execution_time=execution_time,
                query_sql=sql,
                query_parameters=params,
                database_type=DatabaseType.MYSQL
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "aquery",
                "table": request.table,
                "request": str(request)
            })

    def _update_performance_stats(self, execution_time: float) -> None:
        """更新性能统计"""
        self._performance_stats['operations_count'] += 1
        self._performance_stats['total_execution_time'] += execution_time
        self._performance_stats['query_times'].append(execution_time)
        self._performance_stats['last_operation_time'] = execution_time

        # 保持查询时间列表在合理大小
        if len(self._performance_stats['query_times']) > 1000:
            self._performance_stats['query_times'] = self._performance_stats['query_times'][-500:]

    # ==================== RDB Interface CRUD Methods ====================

    @mysql_operation('insert')
    def insert(self, request: Union[InsertRequest, Dict[str, Any]]) -> OperationResponse:
        """
        插入数据（同步）- RDB接口方法

        Args:
            request: 插入请求，可以是InsertRequest实体或字典

        Returns:
            OperationResponse: 操作结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()

        try:
            # 构建INSERT SQL
            sql, params = self._build_insert_sql(request)

            # 执行插入
            with self.get_sync_connection() as conn:
                with conn.cursor() as cursor:
                    affected_rows = cursor.execute(sql, params)
                    conn.commit()

            # 创建响应
            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time)

            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=params
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "insert",
                "table": request.table,
                "data": str(request.data)
            })

    @mysql_operation('insert')
    async def ainsert(self, request: Union[InsertRequest, Dict[str, Any]]) -> OperationResponse:
        """
        插入数据（异步）- RDB接口方法

        Args:
            request: 插入请求，可以是InsertRequest实体或字典

        Returns:
            OperationResponse: 操作结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()

        try:
            # 构建INSERT SQL
            sql, params = self._build_insert_sql(request)

            # 执行插入
            async with self.get_async_connection() as conn:
                async with conn.cursor() as cursor:
                    affected_rows = await cursor.execute(sql, params)
                    await conn.commit()

            # 创建响应
            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time)

            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=params
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "ainsert",
                "table": request.table,
                "data": str(request.data)
            })

    @mysql_operation('update')
    def update(self, request: Union[UpdateRequest, Dict[str, Any]]) -> OperationResponse:
        """
        更新数据（同步）- RDB接口方法

        Args:
            request: 更新请求，可以是UpdateRequest实体或字典

        Returns:
            OperationResponse: 操作结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()

        try:
            # 构建UPDATE SQL
            sql, params = self._build_update_sql(request)

            # 执行更新
            with self.get_sync_connection() as conn:
                with conn.cursor() as cursor:
                    affected_rows = cursor.execute(sql, params)
                    conn.commit()

            # 创建响应
            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time)

            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=params
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "update",
                "table": request.table,
                "data": str(request.data)
            })

    @mysql_operation('update')
    async def aupdate(self, request: Union[UpdateRequest, Dict[str, Any]]) -> OperationResponse:
        """
        更新数据（异步）- RDB接口方法

        Args:
            request: 更新请求，可以是UpdateRequest实体或字典

        Returns:
            OperationResponse: 操作结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()

        try:
            # 构建UPDATE SQL
            sql, params = self._build_update_sql(request)
            logger.debug(f"Async update SQL: {sql}, params: {params}")

            # 执行更新
            async with self.get_async_connection() as conn:
                async with conn.cursor() as cursor:
                    affected_rows = await cursor.execute(sql, params)
                    await conn.commit()
                    # 强制刷新以确保数据已写入
                    await conn.ping()
                    logger.debug(f"Async update committed, affected_rows: {affected_rows}")

            # 创建响应
            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time)

            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=params
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "aupdate",
                "table": request.table,
                "data": str(request.data)
            })

    @mysql_operation('delete')
    def delete(self, request: Union[DeleteRequest, Dict[str, Any]]) -> OperationResponse:
        """
        删除数据（同步）- RDB接口方法

        Args:
            request: 删除请求，可以是DeleteRequest实体或字典

        Returns:
            OperationResponse: 操作结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()

        try:
            # 构建DELETE SQL
            sql, params = self._build_delete_sql(request)

            # 执行删除
            with self.get_sync_connection() as conn:
                with conn.cursor() as cursor:
                    affected_rows = cursor.execute(sql, params)
                    conn.commit()

            # 创建响应
            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time)

            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=params
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "delete",
                "table": request.table
            })

    @mysql_operation('delete')
    async def adelete(self, request: Union[DeleteRequest, Dict[str, Any]]) -> OperationResponse:
        """
        删除数据（异步）- RDB接口方法

        Args:
            request: 删除请求，可以是DeleteRequest实体或字典

        Returns:
            OperationResponse: 操作结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()

        try:
            # 构建DELETE SQL
            sql, params = self._build_delete_sql(request)

            # 执行删除
            async with self.get_async_connection() as conn:
                async with conn.cursor() as cursor:
                    affected_rows = await cursor.execute(sql, params)
                    await conn.commit()

            # 创建响应
            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time)

            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=params
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "adelete",
                "table": request.table
            })

    # ==================== RDB Interface Raw SQL Methods ====================

    def execute(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> OperationResponse:
        """执行原生SQL（同步）- RDB接口方法"""
        start_time = time.time()

        try:
            # 检查是否在事务中
            if hasattr(self, '_current_transaction_conn') and self._current_transaction_conn:
                # 在事务中，使用事务连接
                conn = self._current_transaction_conn
                with conn.cursor() as cursor:
                    affected_rows = cursor.execute(sql, parameters or {})
                    # 在事务中不提交，由事务管理器控制
            else:
                # 不在事务中，使用普通连接
                self._ensure_sync_connection()
                with self.get_sync_connection() as conn:
                    with conn.cursor() as cursor:
                        affected_rows = cursor.execute(sql, parameters or {})
                        conn.commit()

            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time)

            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=parameters
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "execute",
                "sql": sql,
                "parameters": parameters
            })

    async def aexecute(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> OperationResponse:
        """执行原生SQL（异步）- RDB接口方法"""
        start_time = time.time()

        try:
            # 检查是否在异步事务中
            if hasattr(self, '_current_async_transaction_conn') and self._current_async_transaction_conn:
                # 在异步事务中，使用事务连接
                conn = self._current_async_transaction_conn
                async with conn.cursor() as cursor:
                    affected_rows = await cursor.execute(sql, parameters or {})
                    # 在事务中不提交，由事务管理器控制
            else:
                # 不在事务中，使用普通连接
                await self._ensure_async_connection()
                async with self.get_async_connection() as conn:
                    async with conn.cursor() as cursor:
                        affected_rows = await cursor.execute(sql, parameters or {})
                        await conn.commit()

            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time)

            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=parameters
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "aexecute",
                "sql": sql,
                "parameters": parameters
            })

    def fetch_all(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> QueryResponse:
        """获取所有结果（同步）- RDB接口方法"""
        start_time = time.time()

        try:
            # 检查是否在事务中
            if hasattr(self, '_current_transaction_conn') and self._current_transaction_conn:
                # 在事务中，使用事务连接
                conn = self._current_transaction_conn
                with conn.cursor() as cursor:
                    cursor.execute(sql, parameters or {})
                    rows = cursor.fetchall()
            else:
                # 不在事务中，使用普通连接
                self._ensure_sync_connection()
                with self.get_sync_connection() as conn:
                    with conn.cursor() as cursor:
                        cursor.execute(sql, parameters or {})
                        rows = cursor.fetchall()

            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time)

            return QueryResponse(
                data=rows,
                total_count=len(rows),
                execution_time=execution_time,
                query_sql=sql,
                query_parameters=parameters,
                database_type=DatabaseType.MYSQL
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "fetch_all",
                "sql": sql,
                "parameters": parameters
            })

    async def afetch_all(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> QueryResponse:
        """获取所有结果（异步）- RDB接口方法"""
        start_time = time.time()

        try:
            # 检查是否在异步事务中
            if hasattr(self, '_current_async_transaction_conn') and self._current_async_transaction_conn:
                # 在异步事务中，使用事务连接
                conn = self._current_async_transaction_conn
                async with conn.cursor() as cursor:
                    await cursor.execute(sql, parameters or {})
                    rows = await cursor.fetchall()
            else:
                # 不在事务中，使用普通连接
                await self._ensure_async_connection()
                async with self.get_async_connection() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(sql, parameters or {})
                        rows = await cursor.fetchall()

            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time)

            return QueryResponse(
                data=rows,
                total_count=len(rows),
                execution_time=execution_time,
                query_sql=sql,
                query_parameters=parameters,
                database_type=DatabaseType.MYSQL
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "afetch_all",
                "sql": sql,
                "parameters": parameters
            })

    def fetch_one(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """获取单个结果（同步）- RDB接口方法"""
        response = self.fetch_all(sql, parameters)
        return response.data[0] if response.data else None

    async def afetch_one(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """获取单个结果（异步）- RDB接口方法"""
        response = await self.afetch_all(sql, parameters)
        return response.data[0] if response.data else None

    @contextmanager
    def transaction(self, isolation_level: Optional[TransactionIsolation] = None):
        """事务上下文管理器（同步）- RDB接口方法"""
        self._ensure_sync_connection()

        # 保存当前的事务连接状态
        old_transaction_conn = getattr(self, '_current_transaction_conn', None)

        with self.get_sync_connection() as conn:
            # 设置隔离级别
            if isolation_level:
                isolation_sql = self._get_isolation_level_sql(isolation_level)
                with conn.cursor() as cursor:
                    cursor.execute(isolation_sql)

            # 开始事务
            conn.begin()

            # 设置当前事务连接，让其他操作使用这个连接
            self._current_transaction_conn = conn

            try:
                yield conn
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise RDBTransactionError(
                    f"Transaction failed: {e}",
                    original_error=e,
                    database_type=DatabaseType.MYSQL
                )
            finally:
                # 恢复之前的事务连接状态
                self._current_transaction_conn = old_transaction_conn

    @asynccontextmanager
    async def atransaction(self, isolation_level: Optional[TransactionIsolation] = None):
        """事务上下文管理器（异步）- RDB接口方法"""
        await self._ensure_async_connection()

        # 保存当前的异步事务连接状态
        old_async_transaction_conn = getattr(self, '_current_async_transaction_conn', None)

        async with self.get_async_connection() as conn:
            # 设置隔离级别
            if isolation_level:
                isolation_sql = self._get_isolation_level_sql(isolation_level)
                async with conn.cursor() as cursor:
                    await cursor.execute(isolation_sql)

            # 开始事务
            await conn.begin()

            # 设置当前异步事务连接，让其他操作使用这个连接
            self._current_async_transaction_conn = conn

            try:
                yield conn
                await conn.commit()
            except Exception as e:
                await conn.rollback()
                raise RDBTransactionError(
                    f"Transaction failed: {e}",
                    original_error=e,
                    database_type=DatabaseType.MYSQL
                )
            finally:
                # 恢复之前的异步事务连接状态
                self._current_async_transaction_conn = old_async_transaction_conn

    def _get_isolation_level_sql(self, isolation_level: TransactionIsolation) -> str:
        """获取设置隔离级别的SQL"""
        isolation_map = {
            TransactionIsolation.READ_UNCOMMITTED: "SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED",
            TransactionIsolation.READ_COMMITTED: "SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED",
            TransactionIsolation.REPEATABLE_READ: "SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ",
            TransactionIsolation.SERIALIZABLE: "SET SESSION TRANSACTION ISOLATION LEVEL SERIALIZABLE",
        }
        return isolation_map.get(isolation_level, "")

    def health_check(self) -> Dict[str, Any]:
        """健康检查 - RDB接口方法"""
        try:
            if not self.is_connected():
                return {
                    "status": "disconnected",
                    "database_type": DatabaseType.MYSQL,
                    "error": "Not connected to database"
                }

            # 执行简单查询测试连接
            start_time = time.time()
            with self.get_sync_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()

            execution_time = time.time() - start_time

            # 获取连接池统计
            pool_stats = self.get_pool_stats()

            return {
                "status": "healthy",
                "database_type": DatabaseType.MYSQL,
                "response_time": execution_time,
                "connection_pools": pool_stats,
                "performance_stats": self._performance_stats.copy()
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "database_type": DatabaseType.MYSQL,
                "error": str(e)
            }

    # ==================== SQL Building Helper Methods ====================

    def _build_select_sql(self, request: QueryRequest) -> tuple[str, List[Any]]:
        """构建SELECT SQL语句"""
        # 构建列列表
        if request.columns:
            columns = ", ".join([f"`{col}`" for col in request.columns])
        else:
            columns = "*"

        # 构建基础查询
        sql = f"SELECT {columns} FROM `{request.table}`"
        params = []

        # 构建WHERE子句
        if request.filters:
            # 处理单个过滤器或过滤器组
            if hasattr(request.filters, 'filters'):
                # 这是一个QueryFilterGroup
                where_clause, where_params = self._build_where_clause(request.filters)
            else:
                # 这是一个单个QueryFilter，包装成QueryFilterGroup
                from ....base.rdb import QueryFilterGroup, LogicalOperator
                filter_group = QueryFilterGroup(
                    operator=LogicalOperator.AND,
                    filters=[request.filters]
                )
                where_clause, where_params = self._build_where_clause(filter_group)

            sql += f" WHERE {where_clause}"
            params.extend(where_params)

        # 构建ORDER BY子句
        if request.sorts:
            order_clauses = []
            for sort in request.sorts:
                direction = "DESC" if sort.order.value == "desc" else "ASC"
                order_clauses.append(f"`{sort.field}` {direction}")
            sql += f" ORDER BY {', '.join(order_clauses)}"

        # 构建LIMIT和OFFSET
        if request.limit:
            sql += f" LIMIT {request.limit}"

        if request.offset:
            sql += f" OFFSET {request.offset}"

        return sql, params

    def _build_insert_sql(self, request: InsertRequest) -> tuple[str, Any]:
        """构建INSERT SQL语句"""
        if isinstance(request.data, list):
            # 批量插入
            if not request.data:
                raise ValueError("Insert data cannot be empty")

            first_record = request.data[0]
            columns = list(first_record.keys())
            quoted_columns = [f"`{col}`" for col in columns]

            # 构建VALUES子句
            placeholders = "(" + ", ".join(["%s"] * len(columns)) + ")"
            values_list = []

            for record in request.data:
                row_values = [record.get(col) for col in columns]
                values_list.append(row_values)

            sql = f"INSERT INTO `{request.table}` ({', '.join(quoted_columns)}) VALUES {placeholders}"

            # 对于批量插入，返回第一行的值作为参数，实际执行时使用executemany
            return sql, values_list[0] if len(values_list) == 1 else values_list
        else:
            # 单条插入
            columns = list(request.data.keys())
            quoted_columns = [f"`{col}`" for col in columns]
            placeholders = ["%s"] * len(columns)
            values = [request.data[col] for col in columns]

            sql = f"INSERT INTO `{request.table}` ({', '.join(quoted_columns)}) VALUES ({', '.join(placeholders)})"
            return sql, values

    def _build_update_sql(self, request: UpdateRequest) -> tuple[str, List[Any]]:
        """构建UPDATE SQL语句"""
        # 构建SET子句
        set_clauses = []
        params = []

        for col, value in request.data.items():
            set_clauses.append(f"`{col}` = %s")
            params.append(value)

        sql = f"UPDATE `{request.table}` SET {', '.join(set_clauses)}"

        # 构建WHERE子句
        if request.filters:
            # 处理单个过滤器或过滤器组
            if hasattr(request.filters, 'filters'):
                # 这是一个QueryFilterGroup
                where_clause, where_params = self._build_where_clause(request.filters)
            else:
                # 这是一个单个QueryFilter，包装成QueryFilterGroup
                from ....base.rdb import QueryFilterGroup, LogicalOperator
                filter_group = QueryFilterGroup(
                    operator=LogicalOperator.AND,
                    filters=[request.filters]
                )
                where_clause, where_params = self._build_where_clause(filter_group)

            sql += f" WHERE {where_clause}"
            # 将where参数添加到params列表
            params.extend(where_params)

        return sql, params

    def _build_delete_sql(self, request: DeleteRequest) -> tuple[str, List[Any]]:
        """构建DELETE SQL语句"""
        sql = f"DELETE FROM `{request.table}`"
        params = []

        # 构建WHERE子句
        if request.filters:
            # 处理单个过滤器或过滤器组
            if hasattr(request.filters, 'filters'):
                # 这是一个QueryFilterGroup
                where_clause, where_params = self._build_where_clause(request.filters)
            else:
                # 这是一个单个QueryFilter，包装成QueryFilterGroup
                from ....base.rdb import QueryFilterGroup, LogicalOperator
                filter_group = QueryFilterGroup(
                    operator=LogicalOperator.AND,
                    filters=[request.filters]
                )
                where_clause, where_params = self._build_where_clause(filter_group)

            sql += f" WHERE {where_clause}"
            # 将where参数添加到params列表
            params.extend(where_params)

        return sql, params

    def _build_where_clause(self, filter_group) -> tuple[str, List[Any]]:
        """构建WHERE子句"""
        from ....base.rdb import QueryFilter, QueryFilterGroup, ComparisonOperator, LogicalOperator

        conditions = []
        params = []
        param_counter = 0

        for filter_item in filter_group.filters:
            if isinstance(filter_item, QueryFilter):
                condition, filter_params = self._build_filter_condition(filter_item, param_counter)
                conditions.append(condition)
                params.extend(filter_params)
                param_counter += len(filter_params)
            elif isinstance(filter_item, QueryFilterGroup):
                nested_condition, nested_params = self._build_where_clause(filter_item)
                conditions.append(f"({nested_condition})")
                params.extend(nested_params)

        if filter_group.operator == LogicalOperator.AND:
            return " AND ".join(conditions), params
        else:
            return " OR ".join(conditions), params

    def _build_filter_condition(self, filter_obj, param_counter: int) -> tuple[str, List[Any]]:
        """构建单个过滤条件"""
        from ....base.rdb import ComparisonOperator

        column = f"`{filter_obj.field}`"
        params = []

        if filter_obj.operator == ComparisonOperator.EQ:
            params.append(filter_obj.value)
            return f"{column} = %s", params
        elif filter_obj.operator == ComparisonOperator.NE:
            params.append(filter_obj.value)
            return f"{column} != %s", params
        elif filter_obj.operator == ComparisonOperator.GT:
            params.append(filter_obj.value)
            return f"{column} > %s", params
        elif filter_obj.operator == ComparisonOperator.GTE:
            params.append(filter_obj.value)
            return f"{column} >= %s", params
        elif filter_obj.operator == ComparisonOperator.LT:
            params.append(filter_obj.value)
            return f"{column} < %s", params
        elif filter_obj.operator == ComparisonOperator.LTE:
            params.append(filter_obj.value)
            return f"{column} <= %s", params
        elif filter_obj.operator == ComparisonOperator.IN:
            if not isinstance(filter_obj.value, (list, tuple)):
                raise ValueError("IN operator requires a list or tuple value")

            placeholders = []
            for val in filter_obj.value:
                params.append(val)
                placeholders.append("%s")

            return f"{column} IN ({', '.join(placeholders)})", params
        elif filter_obj.operator == ComparisonOperator.NOT_IN:
            if not isinstance(filter_obj.value, (list, tuple)):
                raise ValueError("NOT IN operator requires a list or tuple value")

            placeholders = []
            for val in filter_obj.value:
                params.append(val)
                placeholders.append("%s")

            return f"{column} NOT IN ({', '.join(placeholders)})", params
        elif filter_obj.operator == ComparisonOperator.LIKE:
            params.append(filter_obj.value)
            return f"{column} LIKE %s", params
        elif filter_obj.operator == ComparisonOperator.BETWEEN:
            if not isinstance(filter_obj.value, (list, tuple)) or len(filter_obj.value) != 2:
                raise ValueError("BETWEEN operator requires exactly 2 values")

            params.append(filter_obj.value[0])
            params.append(filter_obj.value[1])
            return f"{column} BETWEEN %s AND %s", params
        elif filter_obj.operator == ComparisonOperator.IS_NULL:
            return f"{column} IS NULL", params
        elif filter_obj.operator == ComparisonOperator.IS_NOT_NULL:
            return f"{column} IS NOT NULL", params
        else:
            raise ValueError(f"Unsupported operator: {filter_obj.operator}")

    # ==================== Connection Status Properties ====================

    @property
    def is_sync_connected(self) -> bool:
        """Check if sync connection is established"""
        return self._sync_connected

    @property
    def is_async_connected(self) -> bool:
        """Check if async connection is established"""
        return self._async_connected

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self._performance_stats.copy()

        # 计算平均查询时间
        if stats['query_times']:
            stats['avg_query_time'] = sum(stats['query_times']) / len(stats['query_times'])
            stats['max_query_time'] = max(stats['query_times'])
            stats['min_query_time'] = min(stats['query_times'])
        else:
            stats['avg_query_time'] = 0.0
            stats['max_query_time'] = 0.0
            stats['min_query_time'] = 0.0

        return stats

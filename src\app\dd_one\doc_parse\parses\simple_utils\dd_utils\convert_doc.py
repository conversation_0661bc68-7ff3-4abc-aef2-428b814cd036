import subprocess
import os
from docx.oxml.ns import qn
from docx import Document


def convert_doc_to_docx(input_path: str, output_dir: str) -> str:
    """
    使用 LibreOffice 命令行将 .doc 文件转换为 .docx 文件。如果输入已是 .docx 文件，直接返回其路径。

    Args:
        input_path (str): 输入的 .doc 或 .docx 文件路径
        output_dir (str): 输出目录（仅在需要转换时使用）

    Returns:
        str: .docx 文件路径（转换后的路径或原 .docx 文件路径）
    """
    if not os.path.exists(input_path):
        raise FileNotFoundError(f"输入文件 {input_path} 不存在")

    # 检查文件扩展名
    file_ext = os.path.splitext(input_path)[1].lower()
    if file_ext == '.docx':
        print(f"输入文件已是 .docx 格式，无需转换: {input_path}")
        return input_path

    if file_ext not in ['.doc', '.wps']:
        raise ValueError(f"不支持的文件格式：{file_ext}。仅支持 .doc、.wps 或 .docx")
        # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 构造输出路径
    base_name = os.path.splitext(os.path.basename(input_path))[0]
    output_path = os.path.join(output_dir, f"{base_name}.docx")

    # LibreOffice 命令行转换
    command = [
        "soffice",
        "--headless",
        "--convert-to", "docx",
        input_path,
        "--outdir", output_dir
    ]

    try:
        subprocess.run(command, check=True, capture_output=True, text=True)
        if not os.path.exists(output_path):
            raise FileNotFoundError(f"转换失败：{output_path} 未找到")
        print(f"已将 {input_path} 转换为 {output_path}")
        return output_path
    except subprocess.CalledProcessError as e:
        raise Exception(f"转换错误：{e.stderr}")


# 2. 接受 .docx 文件中的所有修订
def accept_all_revisions(docx_path: str) -> str:
    doc = Document(docx_path)
    body = doc.element.body

    # 移除 <w:del> 并提升 <w:ins>
    for element in body.iter():
        if element.tag == qn('w:p'):  # 段落
            to_remove = []
            for child in element.iter():
                if child.tag == qn('w:del'):
                    to_remove.append(child)
            for del_elem in to_remove:
                parent = del_elem.getparent()
                if parent is not None:
                    parent.remove(del_elem)

            for ins_elem in element.findall(qn('w:ins')):
                for subchild in list(ins_elem):
                    ins_elem.addprevious(subchild)
                ins_elem.getparent().remove(ins_elem)

    # 保存到临时文件以便正确解析段落
    temp_path = docx_path + ".temp.docx"
    doc.save(temp_path)
    doc = Document(temp_path)

    return '\n'.join(p.text for p in doc.paragraphs)

# chapter_utils.py
import logging
from typing import List
from .models import ChapterInfo
import json

async def get_chapter_by_doc_id(chunk_ops, doc_id: str) -> List[ChapterInfo]:
    try:
        chunks = await chunk_ops.get_chunks_by_document(doc_id)
        chapters = []
        for chunk in chunks:
            if chunk['chapter_layer'] == 'root':
                continue
            chunk_info_list = chunk['chunk_infos']
            chunk_info_dict = {info['info_type']: info['info_value'] for info in chunk_info_list}
            chunk_info_dict['chapter_referenced_index_id'] = json.loads(chunk_info_dict.get('chapter_referenced_index_id', '[]'))

            chapter = ChapterInfo(
                chapter_name=chunk_info_dict.get('chapter_name', ''),
                chapter_summary=chunk_info_dict.get('chapter_summary', ''),
                chapter_content=chunk_info_dict.get('chapter_content', ''),
                chapter_referenced_index_id=chunk_info_dict.get('chapter_referenced_index_id', []),
                chapter_index=chunk_info_dict.get('chapter_index', None),
            )
            chapters.append(chapter)
        return chapters
    except Exception as e:
        logging.error(f"获取章节信息失败: {e}")
        return []
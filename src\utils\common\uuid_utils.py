"""
UUID工具模块

提供标准化的UUID生成和验证功能，确保系统中所有UUID使用统一的格式和验证规则。
适用于所有需要UUID格式的字段，如knowledge_id、doc_id、task_id等。
"""

import uuid
import re
import logging
from typing import Optional, List, Dict, Any

# 获取logger
logger = logging.getLogger(__name__)


# UUID4 正则表达式模式
UUID4_PATTERN = re.compile(
    r'^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$',
    re.IGNORECASE
)


def generate_uuid() -> str:
    """
    生成标准UUID4格式的字符串

    Returns:
        str: UUID4格式的字符串，例如: "123e4567-e89b-12d3-a456-************"

    Example:
        >>> uuid_str = generate_uuid()
        >>> print(uuid_str)
        'f47ac10b-58cc-4372-a567-0e02b2c3d479'
    """
    return str(uuid.uuid4())


def is_valid_uuid4(value: str) -> bool:
    """
    验证字符串是否为有效的UUID4格式
    
    Args:
        value: 要验证的字符串
        
    Returns:
        bool: 是否为有效的UUID4格式
        
    Example:
        >>> is_valid_uuid4('f47ac10b-58cc-4372-a567-0e02b2c3d479')
        True
        >>> is_valid_uuid4('invalid-uuid')
        False
    """
    if not isinstance(value, str):
        return False
    
    return bool(UUID4_PATTERN.match(value))


def validate_uuid(uuid_value: Optional[str], allow_none: bool = False, field_name: str = "UUID") -> str:
    """
    验证并规范化UUID

    Args:
        uuid_value: 要验证的UUID值，可以为None
        allow_none: 是否允许None值，如果为True且uuid_value为None，则自动生成新的UUID
        field_name: 字段名称，用于错误信息显示

    Returns:
        str: 验证通过的UUID4格式字符串

    Raises:
        ValueError: 当UUID格式无效时抛出异常

    Example:
        >>> validate_uuid('f47ac10b-58cc-4372-a567-0e02b2c3d479')
        'f47ac10b-58cc-4372-a567-0e02b2c3d479'
        >>> validate_uuid(None, allow_none=True, field_name="knowledge_id")
        'f47ac10b-58cc-4372-a567-0e02b2c3d479'  # 自动生成
    """
    if uuid_value is None:
        if allow_none:
            return generate_uuid()
        else:
            raise ValueError(f"{field_name}不能为空")

    if not isinstance(uuid_value, str):
        raise ValueError(f"{field_name}必须是字符串类型，当前类型: {type(uuid_value)}")

    # 去除首尾空格
    uuid_value = uuid_value.strip()

    if not uuid_value:
        if allow_none:
            return generate_uuid()
        else:
            raise ValueError(f"{field_name}不能为空字符串")

    if not is_valid_uuid4(uuid_value):
        raise ValueError(f"{field_name}格式无效，必须是标准UUID4格式: {uuid_value}")

    return uuid_value


def normalize_legacy_uuid(legacy_id: str, field_name: str = "UUID") -> str:
    """
    将旧格式的ID转换为标准UUID4格式

    主要用于处理系统中可能存在的shortuuid或其他格式的ID

    Args:
        legacy_id: 旧格式的ID
        field_name: 字段名称，用于日志记录

    Returns:
        str: 标准UUID4格式的字符串

    Note:
        如果输入已经是有效的UUID4格式，则直接返回
        如果不是，则生成新的UUID4并记录警告日志
    """
    if not isinstance(legacy_id, str):
        logger.warning(f"{field_name}必须是字符串类型，当前类型: {type(legacy_id)}，生成新UUID")
        return generate_uuid()

    legacy_id = legacy_id.strip()

    if not legacy_id:
        logger.warning(f"{field_name}为空，生成新UUID")
        return generate_uuid()

    # 如果已经是有效的UUID4格式，直接返回
    if is_valid_uuid4(legacy_id):
        return legacy_id

    # 否则生成新的UUID4并记录警告
    logger.warning(f"检测到非标准格式的{field_name}: {legacy_id}，已生成新的UUID4格式")
    return generate_uuid()


def batch_validate_uuids(uuid_list: List[str], field_name: str = "UUID") -> Dict[str, Any]:
    """
    批量验证UUID列表

    Args:
        uuid_list: UUID列表
        field_name: 字段名称，用于错误信息显示

    Returns:
        Dict[str, Any]: 验证结果
        {
            'valid': [有效的UUID列表],
            'invalid': [无效的UUID列表],
            'errors': {uuid: error_message}
        }
    """
    result = {
        'valid': [],
        'invalid': [],
        'errors': {}
    }

    for uuid_value in uuid_list:
        try:
            validated_id = validate_uuid(uuid_value, field_name=field_name)
            result['valid'].append(validated_id)
        except ValueError as e:
            result['invalid'].append(uuid_value)
            result['errors'][str(uuid_value)] = str(e)

    return result


# ==================== 向后兼容函数 ====================
# 为了保持向后兼容性，保留原有的knowledge_id相关函数

def generate_knowledge_id() -> str:
    """
    生成knowledge_id (向后兼容函数)

    Returns:
        str: UUID4格式的字符串
    """
    return generate_uuid()


def validate_knowledge_id(knowledge_id: Optional[str], allow_none: bool = False) -> str:
    """
    验证knowledge_id (向后兼容函数)

    Args:
        knowledge_id: 要验证的knowledge_id
        allow_none: 是否允许None值

    Returns:
        str: 验证通过的UUID4格式字符串
    """
    return validate_uuid(knowledge_id, allow_none, "knowledge_id")


def normalize_legacy_knowledge_id(legacy_id: str) -> str:
    """
    转换旧格式knowledge_id (向后兼容函数)

    Args:
        legacy_id: 旧格式的ID字符串

    Returns:
        str: 标准UUID4格式的字符串
    """
    return normalize_legacy_uuid(legacy_id, "knowledge_id")


def batch_validate_knowledge_ids(knowledge_ids: List[str]) -> Dict[str, Any]:
    """
    批量验证knowledge_id列表 (向后兼容函数)

    Args:
        knowledge_ids: knowledge_id列表

    Returns:
        Dict[str, Any]: 验证结果
    """
    return batch_validate_uuids(knowledge_ids, "knowledge_id")


# 其他向后兼容的别名
generate_uuid4 = generate_uuid
validate_uuid4 = is_valid_uuid4

"""
Pipeline核心类
管理步骤的执行顺序和数据流
"""

import logging
from typing import List, Dict, Any, Optional
from .base_step import BaseStep
from .context import PipelineContext

logger = logging.getLogger(__name__)

class Pipeline:
    """
    Pipeline类 - 管理步骤的执行顺序和数据流
    """
    
    def __init__(self, name: str):
        """
        初始化Pipeline
        
        Args:
            name: Pipeline名称
        """
        self.name = name
        self.steps: List[BaseStep] = []
        self._step_registry: Dict[str, BaseStep] = {}
    
    def add_step(self, step: BaseStep) -> 'Pipeline':
        """
        添加步骤到Pipeline
        
        Args:
            step: 要添加的步骤
            
        Returns:
            Pipeline实例（支持链式调用）
        """
        if step.name in self._step_registry:
            logger.warning(f"步骤 {step.name} 已存在，将被替换")
        
        self.steps.append(step)
        self._step_registry[step.name] = step
        
        logger.debug(f"添加步骤: {step.name}")
        return self
    
    def remove_step(self, step_name: str) -> 'Pipeline':
        """
        从Pipeline中移除步骤
        
        Args:
            step_name: 要移除的步骤名称
            
        Returns:
            Pipeline实例（支持链式调用）
        """
        if step_name not in self._step_registry:
            logger.warning(f"步骤 {step_name} 不存在")
            return self
        
        step = self._step_registry[step_name]
        self.steps.remove(step)
        del self._step_registry[step_name]
        
        logger.debug(f"移除步骤: {step_name}")
        return self
    
    def get_step(self, step_name: str) -> Optional[BaseStep]:
        """
        获取指定名称的步骤
        
        Args:
            step_name: 步骤名称
            
        Returns:
            步骤实例，如果不存在则返回None
        """
        return self._step_registry.get(step_name)
    
    def list_steps(self) -> List[str]:
        """
        获取所有步骤名称列表
        
        Returns:
            步骤名称列表
        """
        return [step.name for step in self.steps]
    
    async def execute(self, context: PipelineContext) -> PipelineContext:
        """
        执行Pipeline中的所有步骤
        
        Args:
            context: Pipeline上下文
            
        Returns:
            更新后的Pipeline上下文
        """
        logger.info(f"开始执行Pipeline: {self.name}")
        
        # 记录执行开始时间
        import time
        start_time = time.time()
        
        successful_steps = []
        failed_steps = []
        
        try:
            for i, step in enumerate(self.steps):
                logger.info(f"执行步骤 {i+1}/{len(self.steps)}: {step.name}")
                
                try:
                    # 执行步骤
                    context = await step.execute(context)
                    
                    if context.is_step_successful(step.name):
                        successful_steps.append(step.name)
                        logger.info(f"步骤 {step.name} 执行成功")
                    else:
                        failed_steps.append(step.name)
                        logger.warning(f"步骤 {step.name} 执行失败")
                        
                        # 根据步骤的错误处理策略决定是否继续
                        if self._should_stop_on_error(step, context):
                            logger.error(f"步骤 {step.name} 失败，停止Pipeline执行")
                            break
                
                except Exception as e:
                    failed_steps.append(step.name)
                    logger.error(f"步骤 {step.name} 执行异常: {e}")
                    
                    # 记录异常到上下文
                    context.set_step_error(step.name, str(e))
                    
                    # 根据步骤的错误处理策略决定是否继续
                    if self._should_stop_on_error(step, context):
                        logger.error(f"步骤 {step.name} 异常，停止Pipeline执行")
                        break
            
            # 记录执行结束时间
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 设置执行摘要
            execution_summary = {
                "pipeline_name": self.name,
                "total_steps": len(self.steps),
                "successful_steps": successful_steps,
                "failed_steps": failed_steps,
                "success": len(failed_steps) == 0,
                "execution_time": execution_time
            }
            
            context.set("execution_summary", execution_summary)
            
            logger.info(f"Pipeline {self.name} 执行完成")
            logger.info(f"成功步骤: {len(successful_steps)}/{len(self.steps)}")
            logger.info(f"执行时间: {execution_time:.3f}秒")
            
            return context
            
        except Exception as e:
            logger.error(f"Pipeline {self.name} 执行失败: {e}")
            
            # 设置失败的执行摘要
            end_time = time.time()
            execution_time = end_time - start_time
            
            execution_summary = {
                "pipeline_name": self.name,
                "total_steps": len(self.steps),
                "successful_steps": successful_steps,
                "failed_steps": failed_steps,
                "success": False,
                "execution_time": execution_time,
                "error": str(e)
            }
            
            context.set("execution_summary", execution_summary)
            
            return context
    
    def _should_stop_on_error(self, step: BaseStep, context: PipelineContext) -> bool:
        """
        判断是否应该在错误时停止执行
        
        Args:
            step: 当前步骤
            context: Pipeline上下文
            
        Returns:
            是否应该停止执行
        """
        # 默认策略：继续执行（轻量化Pipeline的设计理念）
        # 可以根据步骤类型或配置来定制策略
        return False
    
    async def execute_step(self, step_name: str, context: PipelineContext) -> PipelineContext:
        """
        执行指定的单个步骤
        
        Args:
            step_name: 步骤名称
            context: Pipeline上下文
            
        Returns:
            更新后的Pipeline上下文
        """
        step = self.get_step(step_name)
        if not step:
            raise ValueError(f"步骤 {step_name} 不存在")
        
        logger.info(f"执行单个步骤: {step_name}")
        return await step.execute(context)
    
    async def execute_from_step(self, start_step_name: str, context: PipelineContext) -> PipelineContext:
        """
        从指定步骤开始执行Pipeline
        
        Args:
            start_step_name: 开始执行的步骤名称
            context: Pipeline上下文
            
        Returns:
            更新后的Pipeline上下文
        """
        start_index = None
        for i, step in enumerate(self.steps):
            if step.name == start_step_name:
                start_index = i
                break
        
        if start_index is None:
            raise ValueError(f"步骤 {start_step_name} 不存在")
        
        logger.info(f"从步骤 {start_step_name} 开始执行Pipeline")
        
        # 创建临时Pipeline只包含从指定步骤开始的步骤
        temp_pipeline = Pipeline(f"{self.name}_from_{start_step_name}")
        for step in self.steps[start_index:]:
            temp_pipeline.add_step(step)
        
        return await temp_pipeline.execute(context)
    
    def validate(self) -> List[str]:
        """
        验证Pipeline配置
        
        Returns:
            验证问题列表，空列表表示没有问题
        """
        issues = []
        
        if not self.steps:
            issues.append("Pipeline没有包含任何步骤")
        
        # 检查步骤名称重复
        step_names = [step.name for step in self.steps]
        duplicates = set([name for name in step_names if step_names.count(name) > 1])
        if duplicates:
            issues.append(f"发现重复的步骤名称: {duplicates}")
        
        # 可以添加更多验证逻辑
        
        return issues
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Pipeline(name={self.name}, steps={len(self.steps)})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        step_names = [step.name for step in self.steps]
        return f"Pipeline(name='{self.name}', steps={step_names})"

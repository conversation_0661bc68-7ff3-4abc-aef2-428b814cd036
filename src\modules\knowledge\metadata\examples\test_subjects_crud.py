"""
数据主题系统完整CRUD测试

严格按照实际表结构进行测试：
- md_data_subject: 数据主题表

测试内容：
- 完整的CRUD操作（创建、读取、更新、删除）
- 数据验证测试
- 错误处理测试
- 批量操作测试
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局测试数据存储
test_data_store = {
    'knowledge_id': None,
    'subject_ids': []
}


async def setup_test_environment():
    """设置测试环境，创建测试知识库"""
    print("🔧 设置测试环境")
    print("-" * 40)

    try:
        # 获取客户端
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
        from service import get_client
        from modules.knowledge.knowledge.crud import KnowledgeCrud

        rdb_client = await get_client("database.rdbs.mysql")
        knowledge_crud = KnowledgeCrud(rdb_client)

        # 创建测试知识库
        timestamp = int(time.time())
        test_kb_data = {
            'knowledge_name': f'数据主题测试知识库_{timestamp}',
            'knowledge_type': 'MetaData',
            'knowledge_desc': '数据主题CRUD测试知识库',
            'models': {
                'embedding': 'moka-m3e-base'
            }
        }

        knowledge_id = await knowledge_crud.create_knowledge_base(test_kb_data)
        if not knowledge_id:
            raise Exception("创建测试知识库失败")

        test_data_store['knowledge_id'] = knowledge_id
        print(f"   ✅ 创建测试知识库: {knowledge_id}")

        return rdb_client, knowledge_id

    except Exception as e:
        logger.error(f"设置测试环境失败: {e}")
        raise


async def cleanup_test_environment():
    """清理测试环境"""
    print("\n🧹 清理测试环境")
    print("-" * 40)

    try:
        if test_data_store['knowledge_id']:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            from modules.knowledge.knowledge.crud import KnowledgeCrud

            rdb_client = await get_client("database.rdbs.mysql")
            knowledge_crud = KnowledgeCrud(rdb_client)

            # 删除测试知识库（会级联删除所有相关数据）
            await knowledge_crud.delete_knowledge_base(test_data_store['knowledge_id'])
            print(f"   ✅ 删除测试知识库: {test_data_store['knowledge_id']}")

    except Exception as e:
        logger.error(f"清理测试环境失败: {e}")


async def test_subject_crud(rdb_client, knowledge_id: str):
    """测试数据主题的完整CRUD操作"""
    print("\n1️⃣ 测试数据主题CRUD操作:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_subjects import MetadataCrudSubjects
        
        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的
        
        subjects_crud = MetadataCrudSubjects(rdb_client, vdb_client, embedding_client)

        # 1. 创建数据主题（使用正确的字段结构）
        timestamp = int(time.time())
        test_subject_data = {
            'knowledge_id': knowledge_id,
            'subject_code': f'TEST_SUBJECT_{timestamp}',
            'subject_name': f'测试数据主题_{timestamp}',
            'subject_desc': '测试数据主题描述',
            'is_active': True
        }

        subject_id, vector_results = await subjects_crud.create_subject(test_subject_data)
        if not subject_id or subject_id <= 0:
            raise Exception("创建数据主题失败：返回的ID无效")
        
        test_data_store['subject_ids'].append(subject_id)
        print(f"   ✅ 创建数据主题: {subject_id}")
        print(f"   📊 向量化结果: {len(vector_results)} 个向量")

        # 2. 获取数据主题（主键查询）
        subject = await subjects_crud.get_subject(subject_id)
        if not subject or not subject.get('subject_name'):
            raise Exception("主键查询数据主题失败：未返回有效数据")
        print(f"   ✅ 主键获取数据主题: {subject['subject_name']}")

        # 3. 获取数据主题（条件查询）
        subject_by_code = await subjects_crud.get_subject(
            knowledge_id=knowledge_id,
            subject_code=test_subject_data['subject_code']
        )
        if not subject_by_code or not subject_by_code.get('id'):
            raise Exception("条件查询数据主题失败：未返回有效数据")
        print(f"   ✅ 条件查询数据主题: {subject_by_code['id']}")

        # 4. 更新数据主题
        update_success = await subjects_crud.update_subject(
            {'subject_desc': '更新后的数据主题描述'},
            subject_id=subject_id
        )
        if not update_success:
            raise Exception("更新数据主题失败：返回False")
        print(f"   ✅ 更新数据主题: {update_success}")

        # 5. 验证更新
        updated_subject = await subjects_crud.get_subject(subject_id)
        if not updated_subject or '更新后的数据主题描述' not in updated_subject.get('subject_desc', ''):
            raise Exception("验证更新失败：描述未正确更新")
        print(f"   ✅ 验证更新: {updated_subject['subject_desc']}")

        # 6. 列出数据主题
        subjects_list = await subjects_crud.list_subjects(knowledge_id=knowledge_id)
        if not subjects_list or len(subjects_list) == 0:
            raise Exception("列出数据主题失败：未返回数据")
        print(f"   ✅ 列出数据主题: {len(subjects_list)} 个")

        return True

    except Exception as e:
        print(f"   ❌ 数据主题CRUD测试失败: {e}")
        return False


async def test_batch_operations(rdb_client, knowledge_id: str):
    """测试批量操作"""
    print("\n2️⃣ 测试批量操作:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_subjects import MetadataCrudSubjects
        
        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的
        
        subjects_crud = MetadataCrudSubjects(rdb_client, vdb_client, embedding_client)

        # 批量创建数据主题（使用循环创建，因为没有批量方法）
        timestamp = int(time.time())
        batch_subjects_data = [
            {
                'knowledge_id': knowledge_id,
                'subject_code': f'BATCH_SUBJECT_{timestamp}_1',
                'subject_name': f'批量测试主题1_{timestamp}',
                'subject_desc': '批量测试数据主题1',
                'is_active': True
            },
            {
                'knowledge_id': knowledge_id,
                'subject_code': f'BATCH_SUBJECT_{timestamp}_2',
                'subject_name': f'批量测试主题2_{timestamp}',
                'subject_desc': '批量测试数据主题2',
                'is_active': True
            }
        ]

        subject_ids = []
        for subject_data in batch_subjects_data:
            subject_id, _ = await subjects_crud.create_subject(subject_data)
            if subject_id and subject_id > 0:
                subject_ids.append(subject_id)

        if len(subject_ids) != 2:
            raise Exception("批量创建数据主题失败：返回的ID数量不正确")

        test_data_store['subject_ids'].extend(subject_ids)
        print(f"   ✅ 批量创建数据主题: {len(subject_ids)} 个")

        return True

    except Exception as e:
        print(f"   ❌ 批量操作测试失败: {e}")
        return False


async def test_search_functionality(rdb_client, knowledge_id: str):
    """测试搜索功能"""
    print("\n3️⃣ 测试搜索功能:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_subjects import MetadataCrudSubjects

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client

            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的

        subjects_crud = MetadataCrudSubjects(rdb_client, vdb_client, embedding_client)

        # 搜索数据主题
        search_results = await subjects_crud.search_data_subjects(
            search_term="测试",
            knowledge_id=knowledge_id,
            limit=10
        )
        print(f"   ✅ 搜索数据主题: 找到 {len(search_results)} 个结果")

        # 测试分页查询
        page1_results = await subjects_crud.list_data_subjects(
            knowledge_id=knowledge_id,
            limit=1,
            offset=0
        )
        print(f"   ✅ 分页查询(第1页): {len(page1_results)} 个结果")

        page2_results = await subjects_crud.list_data_subjects(
            knowledge_id=knowledge_id,
            limit=1,
            offset=1
        )
        print(f"   ✅ 分页查询(第2页): {len(page2_results)} 个结果")

        return True

    except Exception as e:
        print(f"   ❌ 搜索功能测试失败: {e}")
        return False


async def test_error_handling(rdb_client):
    """测试错误处理"""
    print("\n4️⃣ 测试错误处理:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_subjects import MetadataCrudSubjects
        
        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的
        
        subjects_crud = MetadataCrudSubjects(rdb_client, vdb_client, embedding_client)

        # 测试获取不存在的记录
        non_existent = await subjects_crud.get_subject(subject_id=999999)
        if non_existent is not None:
            raise Exception("获取不存在记录应该返回None")
        print(f"   ✅ 获取不存在记录: 正确返回None")

        # 测试更新不存在的记录
        update_result = await subjects_crud.update_subject(
            {'subject_desc': '测试更新'},
            subject_id=999999
        )
        if update_result:
            raise Exception("更新不存在记录应该返回False")
        print(f"   ✅ 更新不存在记录: 正确返回False")

        # 测试删除不存在的记录
        delete_result = await subjects_crud.delete_subject(subject_id=999999)
        if delete_result:
            raise Exception("删除不存在记录应该返回False")
        print(f"   ✅ 删除不存在记录: 正确返回False")

        return True

    except Exception as e:
        print(f"   ❌ 错误处理测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 数据主题系统完整CRUD测试")
    print("=" * 80)

    try:
        # 1. 设置测试环境
        rdb_client, knowledge_id = await setup_test_environment()

        # 2. 执行所有测试
        test_results = []
        
        # 数据主题CRUD操作测试
        result1 = await test_subject_crud(rdb_client, knowledge_id)
        test_results.append(("数据主题CRUD", result1))

        # 批量操作测试
        result2 = await test_batch_operations(rdb_client, knowledge_id)
        test_results.append(("批量操作", result2))

        # 搜索功能测试
        result3 = await test_search_functionality(rdb_client, knowledge_id)
        test_results.append(("搜索功能", result3))

        # 错误处理测试
        result4 = await test_error_handling(rdb_client)
        test_results.append(("错误处理", result4))

        # 3. 输出测试结果
        print("\n" + "=" * 80)
        print("📊 测试结果汇总")
        print("=" * 80)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False

        if all_passed:
            print("\n🎉 所有测试通过！数据主题系统CRUD功能正常")
        else:
            print("\n⚠️  部分测试失败，请检查日志")

    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        print(f"\n❌ 测试执行失败: {e}")
    
    finally:
        # 4. 清理测试环境
        await cleanup_test_environment()


if __name__ == "__main__":
    asyncio.run(main())

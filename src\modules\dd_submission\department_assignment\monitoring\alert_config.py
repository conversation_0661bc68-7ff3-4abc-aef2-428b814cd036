#!/usr/bin/env python3
"""
DDCrud批量操作告警配置和通知系统

提供灵活的告警规则配置和多种通知方式
"""

import os
import json
import smtplib
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class NotificationConfig:
    """通知配置"""
    # 邮件配置
    email_enabled: bool = False
    smtp_server: str = ""
    smtp_port: int = 587
    smtp_username: str = ""
    smtp_password: str = ""
    email_recipients: List[str] = None
    
    # 钉钉配置
    dingtalk_enabled: bool = False
    dingtalk_webhook: str = ""
    dingtalk_secret: str = ""
    
    # 企业微信配置
    wechat_enabled: bool = False
    wechat_webhook: str = ""
    
    # 自定义webhook配置
    webhook_enabled: bool = False
    webhook_url: str = ""
    webhook_headers: Dict[str, str] = None
    
    def __post_init__(self):
        if self.email_recipients is None:
            self.email_recipients = []
        if self.webhook_headers is None:
            self.webhook_headers = {}


class AlertNotificationService:
    """告警通知服务"""
    
    def __init__(self, config: NotificationConfig = None):
        self.config = config or self._load_config_from_env()
    
    def _load_config_from_env(self) -> NotificationConfig:
        """从环境变量加载配置"""
        return NotificationConfig(
            # 邮件配置
            email_enabled=os.getenv('ALERT_EMAIL_ENABLED', 'false').lower() == 'true',
            smtp_server=os.getenv('ALERT_SMTP_SERVER', ''),
            smtp_port=int(os.getenv('ALERT_SMTP_PORT', '587')),
            smtp_username=os.getenv('ALERT_SMTP_USERNAME', ''),
            smtp_password=os.getenv('ALERT_SMTP_PASSWORD', ''),
            email_recipients=os.getenv('ALERT_EMAIL_RECIPIENTS', '').split(',') if os.getenv('ALERT_EMAIL_RECIPIENTS') else [],
            
            # 钉钉配置
            dingtalk_enabled=os.getenv('ALERT_DINGTALK_ENABLED', 'false').lower() == 'true',
            dingtalk_webhook=os.getenv('ALERT_DINGTALK_WEBHOOK', ''),
            dingtalk_secret=os.getenv('ALERT_DINGTALK_SECRET', ''),
            
            # 企业微信配置
            wechat_enabled=os.getenv('ALERT_WECHAT_ENABLED', 'false').lower() == 'true',
            wechat_webhook=os.getenv('ALERT_WECHAT_WEBHOOK', ''),
            
            # 自定义webhook配置
            webhook_enabled=os.getenv('ALERT_WEBHOOK_ENABLED', 'false').lower() == 'true',
            webhook_url=os.getenv('ALERT_WEBHOOK_URL', ''),
            webhook_headers=json.loads(os.getenv('ALERT_WEBHOOK_HEADERS', '{}'))
        )
    
    async def send_alert(
        self,
        rule_name: str,
        severity: str,
        message: str,
        metrics: Dict[str, Any],
        timestamp: datetime = None
    ):
        """发送告警通知"""
        
        if timestamp is None:
            timestamp = datetime.now()
        
        # 构建告警数据
        alert_data = {
            'rule_name': rule_name,
            'severity': severity,
            'message': message,
            'timestamp': timestamp.isoformat(),
            'metrics': metrics
        }
        
        # 发送各种类型的通知
        notification_results = []
        
        if self.config.email_enabled:
            try:
                await self._send_email_notification(alert_data)
                notification_results.append(('email', True, None))
            except Exception as e:
                logger.error(f"邮件通知发送失败: {e}")
                notification_results.append(('email', False, str(e)))
        
        if self.config.dingtalk_enabled:
            try:
                await self._send_dingtalk_notification(alert_data)
                notification_results.append(('dingtalk', True, None))
            except Exception as e:
                logger.error(f"钉钉通知发送失败: {e}")
                notification_results.append(('dingtalk', False, str(e)))
        
        if self.config.wechat_enabled:
            try:
                await self._send_wechat_notification(alert_data)
                notification_results.append(('wechat', True, None))
            except Exception as e:
                logger.error(f"企业微信通知发送失败: {e}")
                notification_results.append(('wechat', False, str(e)))
        
        if self.config.webhook_enabled:
            try:
                await self._send_webhook_notification(alert_data)
                notification_results.append(('webhook', True, None))
            except Exception as e:
                logger.error(f"Webhook通知发送失败: {e}")
                notification_results.append(('webhook', False, str(e)))
        
        return notification_results
    
    async def _send_email_notification(self, alert_data: Dict[str, Any]):
        """发送邮件通知"""
        
        if not self.config.email_recipients:
            logger.warning("邮件通知已启用但未配置收件人")
            return
        
        # 构建邮件内容
        subject = f"🚨 DDCrud批量操作告警 - {alert_data['severity'].upper()}"
        
        html_content = f"""
        <html>
        <body>
            <h2 style="color: {'#dc3545' if alert_data['severity'] == 'high' else '#ffc107'};">
                🚨 DDCrud批量操作告警
            </h2>
            
            <table border="1" cellpadding="10" cellspacing="0" style="border-collapse: collapse;">
                <tr>
                    <td><strong>告警规则</strong></td>
                    <td>{alert_data['rule_name']}</td>
                </tr>
                <tr>
                    <td><strong>严重程度</strong></td>
                    <td style="color: {'#dc3545' if alert_data['severity'] == 'high' else '#ffc107'};">
                        {alert_data['severity'].upper()}
                    </td>
                </tr>
                <tr>
                    <td><strong>告警消息</strong></td>
                    <td>{alert_data['message']}</td>
                </tr>
                <tr>
                    <td><strong>触发时间</strong></td>
                    <td>{alert_data['timestamp']}</td>
                </tr>
            </table>
            
            <h3>性能指标详情</h3>
            <table border="1" cellpadding="10" cellspacing="0" style="border-collapse: collapse;">
        """
        
        for key, value in alert_data['metrics'].items():
            html_content += f"""
                <tr>
                    <td><strong>{key}</strong></td>
                    <td>{value}</td>
                </tr>
            """
        
        html_content += """
            </table>
            
            <p style="margin-top: 20px;">
                <em>此告警由DDCrud批量操作监控系统自动发送</em>
            </p>
        </body>
        </html>
        """
        
        # 发送邮件
        msg = MimeMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = self.config.smtp_username
        msg['To'] = ', '.join(self.config.email_recipients)
        
        html_part = MimeText(html_content, 'html')
        msg.attach(html_part)
        
        with smtplib.SMTP(self.config.smtp_server, self.config.smtp_port) as server:
            server.starttls()
            server.login(self.config.smtp_username, self.config.smtp_password)
            server.send_message(msg)
        
        logger.info(f"邮件告警已发送给: {', '.join(self.config.email_recipients)}")
    
    async def _send_dingtalk_notification(self, alert_data: Dict[str, Any]):
        """发送钉钉通知"""
        
        # 构建钉钉消息
        severity_emoji = {
            'low': '🔵',
            'medium': '🟡',
            'high': '🔴',
            'critical': '🚨'
        }
        
        emoji = severity_emoji.get(alert_data['severity'], '⚠️')
        
        message = {
            "msgtype": "markdown",
            "markdown": {
                "title": f"{emoji} DDCrud批量操作告警",
                "text": f"""
## {emoji} DDCrud批量操作告警

**告警规则**: {alert_data['rule_name']}

**严重程度**: {alert_data['severity'].upper()}

**告警消息**: {alert_data['message']}

**触发时间**: {alert_data['timestamp']}

### 性能指标
- **操作名称**: {alert_data['metrics'].get('operation_name', 'N/A')}
- **记录数量**: {alert_data['metrics'].get('record_count', 'N/A')}
- **执行时间**: {alert_data['metrics'].get('execution_time', 'N/A')}秒
- **处理速度**: {alert_data['metrics'].get('records_per_second', 'N/A')}条/秒

---
*此告警由DDCrud批量操作监控系统自动发送*
                """
            }
        }
        
        # 发送请求
        response = requests.post(
            self.config.dingtalk_webhook,
            json=message,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            logger.info("钉钉告警通知发送成功")
        else:
            raise Exception(f"钉钉通知发送失败: {response.status_code} - {response.text}")
    
    async def _send_wechat_notification(self, alert_data: Dict[str, Any]):
        """发送企业微信通知"""
        
        # 构建企业微信消息
        message = {
            "msgtype": "markdown",
            "markdown": {
                "content": f"""
## 🚨 DDCrud批量操作告警

**告警规则**: {alert_data['rule_name']}
**严重程度**: <font color="warning">{alert_data['severity'].upper()}</font>
**告警消息**: {alert_data['message']}
**触发时间**: {alert_data['timestamp']}

### 性能指标详情
- 操作名称: {alert_data['metrics'].get('operation_name', 'N/A')}
- 记录数量: {alert_data['metrics'].get('record_count', 'N/A')}
- 执行时间: {alert_data['metrics'].get('execution_time', 'N/A')}秒
- 处理速度: {alert_data['metrics'].get('records_per_second', 'N/A')}条/秒

> 此告警由DDCrud批量操作监控系统自动发送
                """
            }
        }
        
        # 发送请求
        response = requests.post(
            self.config.wechat_webhook,
            json=message,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            logger.info("企业微信告警通知发送成功")
        else:
            raise Exception(f"企业微信通知发送失败: {response.status_code} - {response.text}")
    
    async def _send_webhook_notification(self, alert_data: Dict[str, Any]):
        """发送自定义Webhook通知"""
        
        # 构建webhook消息
        payload = {
            "alert_type": "ddcrud_batch_operation",
            "severity": alert_data['severity'],
            "rule_name": alert_data['rule_name'],
            "message": alert_data['message'],
            "timestamp": alert_data['timestamp'],
            "metrics": alert_data['metrics']
        }
        
        # 发送请求
        headers = {
            'Content-Type': 'application/json',
            **self.config.webhook_headers
        }
        
        response = requests.post(
            self.config.webhook_url,
            json=payload,
            headers=headers,
            timeout=10
        )
        
        if response.status_code in [200, 201, 202]:
            logger.info("Webhook告警通知发送成功")
        else:
            raise Exception(f"Webhook通知发送失败: {response.status_code} - {response.text}")


class AlertConfigManager:
    """告警配置管理器"""
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file or os.path.join(
            os.path.dirname(__file__), 'alert_rules.json'
        )
        self.alert_rules = self._load_alert_rules()
    
    def _load_alert_rules(self) -> List[Dict[str, Any]]:
        """加载告警规则配置"""
        
        default_rules = [
            {
                "name": "performance_degradation",
                "description": "批量操作性能严重下降",
                "condition": {
                    "type": "threshold",
                    "metric": "records_per_second",
                    "operator": "less_than",
                    "value": 100
                },
                "severity": "high",
                "message_template": "批量操作性能严重下降: {records_per_second:.1f}条/秒 (基准: 100条/秒)",
                "cooldown_seconds": 300,
                "enabled": True
            },
            {
                "name": "slow_execution",
                "description": "批量操作执行时间过长",
                "condition": {
                    "type": "threshold",
                    "metric": "execution_time",
                    "operator": "greater_than",
                    "value": 10.0
                },
                "severity": "medium",
                "message_template": "批量操作执行时间过长: {execution_time:.2f}秒 (基准: 10秒)",
                "cooldown_seconds": 180,
                "enabled": True
            },
            {
                "name": "operation_failure",
                "description": "批量操作失败",
                "condition": {
                    "type": "boolean",
                    "metric": "success",
                    "operator": "equals",
                    "value": False
                },
                "severity": "high",
                "message_template": "批量操作失败: {error_message}",
                "cooldown_seconds": 60,
                "enabled": True
            },
            {
                "name": "large_batch_anomaly",
                "description": "大批量数据处理异常",
                "condition": {
                    "type": "complex",
                    "conditions": [
                        {
                            "metric": "record_count",
                            "operator": "greater_than",
                            "value": 1000
                        },
                        {
                            "metric": "records_per_second",
                            "operator": "less_than",
                            "value": 500
                        }
                    ],
                    "logic": "and"
                },
                "severity": "medium",
                "message_template": "大批量数据处理异常: {record_count}条记录仅{records_per_second:.1f}条/秒",
                "cooldown_seconds": 600,
                "enabled": True
            }
        ]
        
        # 尝试从文件加载配置
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载告警规则配置失败，使用默认配置: {e}")
        
        # 保存默认配置到文件
        self._save_alert_rules(default_rules)
        return default_rules
    
    def _save_alert_rules(self, rules: List[Dict[str, Any]]):
        """保存告警规则配置到文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(rules, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存告警规则配置失败: {e}")
    
    def get_alert_rules(self) -> List[Dict[str, Any]]:
        """获取告警规则列表"""
        return self.alert_rules
    
    def update_alert_rule(self, rule_name: str, updates: Dict[str, Any]):
        """更新告警规则"""
        for rule in self.alert_rules:
            if rule['name'] == rule_name:
                rule.update(updates)
                self._save_alert_rules(self.alert_rules)
                logger.info(f"告警规则已更新: {rule_name}")
                return True
        
        logger.warning(f"未找到告警规则: {rule_name}")
        return False
    
    def enable_alert_rule(self, rule_name: str):
        """启用告警规则"""
        return self.update_alert_rule(rule_name, {'enabled': True})
    
    def disable_alert_rule(self, rule_name: str):
        """禁用告警规则"""
        return self.update_alert_rule(rule_name, {'enabled': False})


# 全局实例
alert_config_manager = AlertConfigManager()
alert_notification_service = AlertNotificationService()


# 使用示例
if __name__ == "__main__":
    import asyncio
    
    async def test_notifications():
        """测试通知功能"""
        
        # 模拟告警数据
        test_metrics = {
            'operation_name': 'batch_save_test',
            'record_count': 1000,
            'execution_time': 15.0,
            'records_per_second': 66.7,
            'success': True
        }
        
        # 发送测试告警
        results = await alert_notification_service.send_alert(
            rule_name="slow_execution",
            severity="medium",
            message="测试告警: 批量操作执行时间过长",
            metrics=test_metrics
        )
        
        print("通知发送结果:")
        for notification_type, success, error in results:
            status = "✅ 成功" if success else f"❌ 失败: {error}"
            print(f"  {notification_type}: {status}")
    
    # 运行测试
    asyncio.run(test_notifications())

"""
会话上下文管理器

提供更高级的会话上下文管理功能，包括嵌套事务、自动重试等
"""

import asyncio
import uuid
from contextlib import contextmanager, asynccontextmanager
from typing import Optional, Any, Dict, Callable, AsyncGenerator
import logging

from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from .manager import SessionManager, AsyncSessionManager
from ..exceptions import SessionContextError, TransactionError

logger = logging.getLogger(__name__)


class SessionContext:
    """同步会话上下文"""
    
    def __init__(self, session_manager: SessionManager):
        """
        初始化会话上下文
        
        Args:
            session_manager: 会话管理器
        """
        self.session_manager = session_manager
        self._current_session: Optional[Session] = None
        self._session_id: Optional[str] = None
        self._transaction_depth = 0
    
    @contextmanager
    def session(self, auto_commit: bool = True, session_id: Optional[str] = None):
        """
        会话上下文管理器
        
        Args:
            auto_commit: 是否自动提交
            session_id: 会话ID
        
        Yields:
            Session实例
        """
        if session_id is None:
            session_id = str(uuid.uuid4())
        
        try:
            with self.session_manager.get_session(session_id) as session:
                self._current_session = session
                self._session_id = session_id
                
                yield session
                
                if auto_commit and self._transaction_depth == 0:
                    session.commit()
                    
        except Exception as e:
            if self._current_session:
                self._current_session.rollback()
            raise SessionContextError(f"Session context error: {e}", e)
        finally:
            self._current_session = None
            self._session_id = None
    
    @contextmanager
    def transaction(self, savepoint: bool = False):
        """
        事务上下文管理器
        
        Args:
            savepoint: 是否使用保存点（嵌套事务）
        
        Yields:
            Session实例
        """
        if not self._current_session:
            raise TransactionError("No active session for transaction")
        
        self._transaction_depth += 1
        
        if savepoint and self._transaction_depth > 1:
            # 嵌套事务使用保存点
            savepoint_name = f"sp_{self._transaction_depth}_{uuid.uuid4().hex[:8]}"
            savepoint_obj = self._current_session.begin_nested()
            
            try:
                yield self._current_session
                savepoint_obj.commit()
            except Exception:
                savepoint_obj.rollback()
                raise
            finally:
                self._transaction_depth -= 1
        else:
            # 顶级事务
            try:
                yield self._current_session
                if self._transaction_depth == 1:
                    self._current_session.commit()
            except Exception:
                self._current_session.rollback()
                raise
            finally:
                self._transaction_depth -= 1
    
    def retry_on_failure(
        self,
        operation: Callable[[Session], Any],
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> Any:
        """
        在失败时重试操作
        
        Args:
            operation: 要执行的操作函数
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
        
        Returns:
            操作结果
        """
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                with self.session() as session:
                    return operation(session)
            except SQLAlchemyError as e:
                last_error = e
                if attempt < max_retries:
                    logger.warning(f"Operation failed (attempt {attempt + 1}), retrying: {e}")
                    import time
                    time.sleep(retry_delay)
                else:
                    logger.error(f"Operation failed after {max_retries} retries: {e}")
        
        raise SessionContextError(f"Operation failed after {max_retries} retries", last_error)


class AsyncSessionContext:
    """异步会话上下文"""
    
    def __init__(self, session_manager: AsyncSessionManager):
        """
        初始化异步会话上下文
        
        Args:
            session_manager: 异步会话管理器
        """
        self.session_manager = session_manager
        self._current_session: Optional[AsyncSession] = None
        self._session_id: Optional[str] = None
        self._transaction_depth = 0
    
    @asynccontextmanager
    async def session(self, auto_commit: bool = True, session_id: Optional[str] = None) -> AsyncGenerator[AsyncSession, None]:
        """
        异步会话上下文管理器
        
        Args:
            auto_commit: 是否自动提交
            session_id: 会话ID
        
        Yields:
            AsyncSession实例
        """
        if session_id is None:
            session_id = str(uuid.uuid4())
        
        try:
            async with self.session_manager.get_session(session_id) as session:
                self._current_session = session
                self._session_id = session_id
                
                yield session
                
                if auto_commit and self._transaction_depth == 0:
                    await session.commit()
                    
        except Exception as e:
            if self._current_session:
                await self._current_session.rollback()
            raise SessionContextError(f"Async session context error: {e}", e)
        finally:
            self._current_session = None
            self._session_id = None
    
    @asynccontextmanager
    async def transaction(self, savepoint: bool = False) -> AsyncGenerator[AsyncSession, None]:
        """
        异步事务上下文管理器
        
        Args:
            savepoint: 是否使用保存点（嵌套事务）
        
        Yields:
            AsyncSession实例
        """
        if not self._current_session:
            raise TransactionError("No active session for transaction")
        
        self._transaction_depth += 1
        
        if savepoint and self._transaction_depth > 1:
            # 嵌套事务使用保存点
            savepoint_obj = await self._current_session.begin_nested()
            
            try:
                yield self._current_session
                await savepoint_obj.commit()
            except Exception:
                await savepoint_obj.rollback()
                raise
            finally:
                self._transaction_depth -= 1
        else:
            # 顶级事务
            try:
                yield self._current_session
                if self._transaction_depth == 1:
                    await self._current_session.commit()
            except Exception:
                await self._current_session.rollback()
                raise
            finally:
                self._transaction_depth -= 1
    
    async def retry_on_failure(
        self,
        operation: Callable[[AsyncSession], Any],
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> Any:
        """
        在失败时重试异步操作
        
        Args:
            operation: 要执行的异步操作函数
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
        
        Returns:
            操作结果
        """
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                async with self.session() as session:
                    return await operation(session)
            except SQLAlchemyError as e:
                last_error = e
                if attempt < max_retries:
                    logger.warning(f"Async operation failed (attempt {attempt + 1}), retrying: {e}")
                    await asyncio.sleep(retry_delay)
                else:
                    logger.error(f"Async operation failed after {max_retries} retries: {e}")
        
        raise SessionContextError(f"Async operation failed after {max_retries} retries", last_error)

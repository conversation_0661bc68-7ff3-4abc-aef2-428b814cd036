# ORM SQLAlchemy 客户端

基于SQLAlchemy ORM的数据库客户端实现，提供与universal客户端完全相同的接口，但内部使用ORM模型进行数据操作，提供更好的类型安全性和性能优化。

## 特性

### 核心特性
- **完全兼容的接口**：与universal客户端保持100%接口兼容
- **动态ORM模型**：自动从数据库表结构生成SQLAlchemy模型类
- **智能缓存**：模型缓存、查询缓存等多级缓存机制
- **类型安全**：基于SQLAlchemy ORM的强类型支持
- **异步支持**：完整的同步和异步操作支持
- **事务管理**：高级事务管理，支持嵌套事务和保存点

### ORM特有优势
- **关系映射**：自动处理表间关系
- **查询优化**：SQLAlchemy的查询优化和缓存
- **批量操作**：高效的批量插入、更新操作
- **连接池管理**：优化的数据库连接池管理
- **方言支持**：支持MySQL、PostgreSQL、SQLite等多种数据库

## 快速开始

### 安装依赖

```bash
pip install sqlalchemy
# 根据数据库类型安装相应驱动
pip install pymysql      # MySQL
pip install psycopg2     # PostgreSQL
pip install aiosqlite    # 异步SQLite支持
```

### 基本使用

```python
from base.db.implementations.rdb.orm import create_orm_sqlite_client

# 创建客户端
client = create_orm_sqlite_client("/path/to/database.db")

# 连接数据库
client.connect()

# 基本CRUD操作
# 插入数据
user_id = client.insert('users', {
    'name': 'John Doe',
    'email': '<EMAIL>',
    'age': 30
})

# 查询数据
users = client.select('users', where={'age': 30})

# 更新数据
client.update('users', 
    values={'age': 31}, 
    where={'id': user_id}
)

# 删除数据
client.delete('users', where={'id': user_id})

# 断开连接
client.disconnect()
```

### 异步使用

```python
import asyncio
from base.db.implementations.rdb.orm import create_and_connect_orm_client

async def main():
    # 创建并连接客户端
    client = await create_and_connect_orm_client(
        "postgresql://user:pass@localhost/db"
    )
    
    # 异步操作
    user_id = await client.ainsert('users', {
        'name': 'Jane Doe',
        'email': '<EMAIL>'
    })
    
    users = await client.aselect('users', where={'name': 'Jane Doe'})
    
    # 事务支持
    async with client.transaction() as session:
        await client.ainsert('users', {'name': 'User 1'})
        await client.ainsert('users', {'name': 'User 2'})
        # 自动提交或回滚
    
    await client.adisconnect()

asyncio.run(main())
```

### 高级查询

```python
# 使用查询构建器
query_builder = client.query('users')

# 复杂查询
results = (query_builder
    .select('name', 'email', 'age')
    .where('age', '>', 18)
    .where('is_active', '=', True)
    .order_by('name')
    .limit(10)
    .execute())

# 聚合查询
stats = (client.query('orders')
    .select('customer_id')
    .sum('amount')
    .group_by('customer_id')
    .having('amount', '>', 1000)
    .execute())

# JOIN查询
results = (client.query('users')
    .select('users.name', 'profiles.bio')
    .join('profiles', 'users.id = profiles.user_id')
    .where('users.is_active', '=', True)
    .execute())
```

## 配置选项

### ORM特定配置

```python
from base.db.implementations.rdb.orm import ORMConnectionConfig

config = ORMConnectionConfig(
    database_url="postgresql://user:pass@localhost/db",
    
    # ORM特定选项
    enable_dynamic_models=True,      # 启用动态模型生成
    model_cache_size=100,            # 模型缓存大小
    auto_reflect_tables=True,        # 自动反射表结构
    lazy_loading=True,               # 启用懒加载
    
    # 查询优化
    enable_query_cache=True,         # 启用查询缓存
    query_cache_size=500,            # 查询缓存大小
    enable_relationship_loading=True, # 启用关系加载
    
    # 会话配置
    session_autoflush=True,          # 自动刷新会话
    session_autocommit=False,        # 禁用自动提交
    session_expire_on_commit=True,   # 提交后过期对象
    
    # 性能配置
    enable_bulk_operations=True,     # 启用批量操作
    batch_size=1000,                 # 批量操作大小
    
    # 表过滤
    table_prefix_filter="app_",      # 表名前缀过滤
    excluded_tables=["temp_table"]   # 排除的表
)
```

### 数据库特定配置

```python
# MySQL配置
mysql_client = create_orm_mysql_client(
    host="localhost",
    database="mydb",
    username="user",
    password="pass",
    charset="utf8mb4",
    pool_size=20,
    enable_bulk_operations=True
)

# PostgreSQL配置
pg_client = create_orm_postgresql_client(
    host="localhost",
    database="mydb", 
    username="user",
    password="pass",
    pool_size=15,
    enable_relationship_loading=True
)

# SQLite配置
sqlite_client = create_orm_sqlite_client(
    "/path/to/db.sqlite",
    enable_query_cache=False,  # SQLite优化
    session_autoflush=False    # SQLite优化
)
```

## 性能优化

### 1. 模型缓存优化
- 合理设置`model_cache_size`
- 使用`auto_reflect_tables=False`按需加载模型
- 定期清理不使用的模型缓存

### 2. 查询优化
- 启用查询缓存：`enable_query_cache=True`
- 使用批量操作：`enable_bulk_operations=True`
- 合理使用懒加载：`lazy_loading=True`

### 3. 连接池优化
```python
config = ORMConnectionConfig(
    database_url="...",
    pool_size=20,           # 连接池大小
    max_overflow=30,        # 最大溢出连接
    pool_timeout=30,        # 连接超时
    pool_recycle=3600,      # 连接回收时间
    pool_pre_ping=True      # 连接预检
)
```

### 4. 批量操作
```python
# 批量插入
data = [
    {'name': f'User {i}', 'email': f'user{i}@example.com'}
    for i in range(1000)
]
client.insert('users', data)  # 自动使用批量插入

# 设置批量大小
config.batch_size = 500  # 每批500条记录
```

## 监控和调试

### 健康检查
```python
# 同步健康检查
health = client.health_check()
print(f"状态: {health['status']}")
print(f"缓存模型数: {health['cached_models']}")

# 异步健康检查
health = await client.ahealth_check()
```

### 模型信息
```python
# 获取模型信息
model_info = client.get_model_info('users')
print(f"表名: {model_info['table_name']}")
print(f"列: {model_info['columns']}")
print(f"主键: {model_info['primary_keys']}")

# 获取可用表
tables = client.get_available_tables()
print(f"可用表: {tables}")
```

### 调试模式
```python
config = ORMConnectionConfig(
    database_url="...",
    echo=True,              # 输出SQL语句
    echo_pool=True          # 输出连接池信息
)
```

## 与Universal客户端的对比

| 特性 | Universal客户端 | ORM客户端 |
|------|----------------|-----------|
| 接口兼容性 | ✅ 基准接口 | ✅ 100%兼容 |
| 查询构建 | 原生SQL构建器 | SQLAlchemy ORM |
| 类型安全 | 基础类型检查 | 强类型支持 |
| 关系处理 | 手动JOIN | 自动关系映射 |
| 缓存机制 | 基础缓存 | 多级缓存 |
| 性能优化 | 标准优化 | ORM优化 |
| 学习曲线 | 较低 | 中等 |

## 最佳实践

### 1. 模型管理
- 在应用启动时预加载常用模型
- 定期刷新模型以适应表结构变化
- 合理设置模型缓存大小

### 2. 查询优化
- 使用查询构建器而非原生SQL
- 合理使用JOIN避免N+1查询
- 启用查询缓存提高重复查询性能

### 3. 事务管理
- 使用事务上下文管理器
- 避免长时间事务
- 合理使用嵌套事务和保存点

### 4. 错误处理
```python
from base.db.implementations.rdb.orm.exceptions import ORMSQLAlchemyError

try:
    result = client.select('users')
except ORMSQLAlchemyError as e:
    logger.error(f"ORM操作失败: {e}")
    # 处理错误
```

## 迁移指南

从universal客户端迁移到ORM客户端非常简单：

```python
# 原来的代码
from base.db.implementations.rdb.universal import create_sqlite_client
client = create_sqlite_client("/path/to/db.sqlite")

# 迁移后的代码
from base.db.implementations.rdb.orm import create_orm_sqlite_client
client = create_orm_sqlite_client("/path/to/db.sqlite")

# 其他代码保持不变
client.connect()
users = client.select('users')
client.disconnect()
```

所有的API调用都保持完全相同，只需要更改导入语句即可。

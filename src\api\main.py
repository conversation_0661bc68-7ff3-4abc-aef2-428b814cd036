"""
HSBC知识管理系统主服务
HSBC Knowledge Management System Main Service

集成所有API模块的统一入口服务
端口: 30337
"""

import sys
from pathlib import Path

# 添加项目根目录到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

# 初始化企业级日志配置
from utils.common.logger import setup_enterprise_logger
logger = setup_enterprise_logger(
    level="INFO",
    service_name="hsbc-knowledge-management-main",
    enable_structlog=True
)

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 导入所有API路由
# 1. DD提交相关API
from api.dd_submission.submission_record_matching.routers.match import router as submission_matching_router
from api.dd_submission.department_assignment.routers.assignment import router as department_assignment_router
from api.dd_submission.department_assignment.routers.intelligent_assignment import router as intelligent_assignment_router
from api.dd_submission.department_assignment.routers.websocket import router as websocket_assignment_router

# 1.1. DD-B增强处理API
from api.dd_submission import duty_distribution_router, data_backfill_router, dd_b_enhanced_router


# 2. 知识库管理API（排除doc层级）
# 暂时注释掉有导入问题的模块，专注于DD提交功能
from api.knowledge.metadata.main import router as metadata_router
from api.knowledge.knowledge_base import router as knowledge_base_router
from api.knowledge.dd.main import router as dd_api_router
from api.knowledge.enums import get_all_enums

# 3. 调试API (暂时注释掉不存在的模块)
# from api.debug.table_structure import router as debug_router

# 4. 外规内化API
from app.ier.api import router as ier_router

# 创建FastAPI应用
app = FastAPI(
    title="HSBC知识管理系统",
    description="HSBC Knowledge Management System\n\n"
                "企业级知识管理和数据治理平台，提供完整的API服务。\n\n"
                "## 核心功能模块\n\n"
                "### 📊 DD提交管理\n"
                "• **填报记录匹配**: 智能匹配历史填报数据\n"
                "• **部门职责分配**: 基于AI的智能部门分配\n"
                "• **批量处理**: 高效的批量数据处理\n"
                "• **DD-B增强处理**: 智能字段填充和数据处理\n\n"
                "### 📚 知识库管理\n"
                "• **知识库管理**: 支持灵活的模型配置\n"
                "• **元数据管理**: MetaData类型的模板管理\n"
                "• **DD数据字典**: DD类型的数据需求管理\n"
                "• **向量搜索**: 语义检索和智能搜索\n\n"
                "### 🔧 企业级特性\n"
                "• **数据治理**: 完整的数据管理流程\n"
                "• **性能优化**: 高性能数据库操作\n"
                "• **安全可靠**: 企业级安全和稳定性\n"
                "• **API文档**: 完整的接口文档和测试\n\n"
                "✅ **生产就绪**: 经过充分测试，性能优化，数据稳定\n"
                "🚀 **最新功能**: 智能部门分配和批量处理API",
    version="3.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境建议配置具体的前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册所有API路由
# 1. DD提交管理API - 重构为统一DD业务接口
app.include_router(submission_matching_router, prefix="/api/dd")
app.include_router(department_assignment_router, prefix="/api/dd")
app.include_router(intelligent_assignment_router)  # 已包含prefix
app.include_router(websocket_assignment_router, prefix="/api/dd")

# 1.1. DD-B增强处理API
app.include_router(duty_distribution_router)  # 已包含prefix: /api/dd/duty-distribution
app.include_router(data_backfill_router)      # 已包含prefix: /api/dd/data-backfill
app.include_router(dd_b_enhanced_router, prefix="/api/dd")  # 新增DD-B增强处理


# 2. 知识库管理API（暂时注释）
app.include_router(knowledge_base_router, prefix="/api")
app.include_router(metadata_router, prefix="/api")
app.include_router(dd_api_router, prefix="/api")

# 3. 调试API (暂时注释掉)
# app.include_router(debug_router, prefix="/api")

# 4. 外规内化API
app.include_router(ier_router, prefix="/api")

@app.get("/")
async def root():
    """服务根路径"""
    return {
        "service": "HSBC Knowledge Management System",
        "environment": "Production",
        "version": "3.0.0",
        "port": 30337,
        "status": "stable",
        "docs": "/docs",
        "api_prefix": "/api",
        "modules": {
            "dd": {
                "description": "DD数据需求管理模块",
                "features": [
                    "填报记录智能匹配",
                    "部门职责分配",
                    "批量处理API",
                    "TF-IDF推荐算法",
                    "四层业务筛选",
                    "事务管理保证",
                    "DD-B智能字段填充",
                    "高/低置信度策略",
                    "Pipeline集成处理",
                    "并发优化处理"
                ],
                "endpoints": {
                    "submission_matching": "/api/dd/matching/",
                    "department_assignment": "/api/dd/assignment/",
                    "batch_assignment": "/api/dd/assignment/batch-assign",
                    "async_batch_assignment": "/api/dd/ws/assignment/batch-assign-async",
                    "websocket_progress": "/api/dd/ws/assignment/progress/{connection_id}",
                    "task_status": "/api/dd/ws/assignment/status/{task_id}",
                    "duty_distribution": "/api/dd/duty-distribution/process",
                    "data_backfill": "/api/dd/data-backfill/process",
                    "dd_b_enhanced": "/api/dd/dd-b/process",
                    "dd_b_health": "/api/dd/dd-b/health"
                }
            },
            "knowledge": {
                "description": "知识库管理模块",
                "features": [
                    "知识库CRUD操作",
                    "元数据模板管理",
                    "DD数据字典管理",
                    "向量搜索",
                    "语义检索"
                ],
                "endpoints": {
                    "knowledge_bases": "/api/knowledge/",
                    "metadata_templates": "/api/knowledge/metadata/templates/",
                    "dd_management": "/api/knowledge/dd/"
                }
            }
        },
        "key_features": [
            "🤖 智能部门分配（基于NLP和TF-IDF）",
            "📊 批量数据处理（高性能）",
            "🔍 智能搜索匹配（向量+文本）",
            "📚 知识库管理（多类型支持）",
            "🏢 企业级数据治理",
            "⚡ 高性能API服务",
            "📖 完整API文档",
            "🔒 安全可靠架构"
        ]
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "HSBC Knowledge Management System",
        "version": "3.0.0",
        "timestamp": "2025-07-18",
        "modules": {
            "dd": "active",
            "knowledge": "active",
            "api_docs": "available"
        }
    }

@app.get("/api/enums")
async def get_enums():
    """
    获取所有枚举信息

    返回API中使用的所有枚举类型和可选值，
    便于前端开发时了解可用的选项。
    """
    return {
        "success": True,
        "message": "获取枚举信息成功",
        "data": {},  # 暂时返回空对象
        "description": "API中使用的所有枚举类型和可选值"
    }

@app.get("/api/modules")
async def get_modules_info():
    """获取所有模块信息"""
    return {
        "success": True,
        "message": "获取模块信息成功",
        "data": {
            "dd": {
                "name": "DD数据需求管理",
                "version": "2.1.0",
                "description": "智能填报记录匹配和部门职责分配，支持事务管理",
                "status": "active",
                "endpoints": [
                    "/api/dd/matching/match-records",
                    "/api/dd/assignment/assign-department",
                    "/api/dd/assignment/batch-assign",
                    "/api/dd/assignment/health",
                    "/api/dd/assignment/constants",
                    "/api/dd/ws/assignment/batch-assign-async",
                    "/api/dd/ws/assignment/progress/{connection_id}",
                    "/api/dd/ws/assignment/status/{task_id}",
                    "/api/dd/ws/assignment/tasks",
                    "/api/dd/duty-distribution/process",
                    "/api/dd/data-backfill/process",
                    "/api/dd/dd-b/process",
                    "/api/dd/dd-b/health"
                ],
                "features": [
                    "历史记录智能匹配",
                    "基于NLP的关键词提取",
                    "TF-IDF部门推荐算法",
                    "四层业务逻辑筛选",
                    "批量处理和数据入库",
                    "动态搜索条件构建",
                    "DD-B智能字段填充",
                    "高/低置信度策略处理",
                    "Pipeline集成和字段映射",
                    "15并发量优化处理"
                ]
            },
            "knowledge": {
                "name": "知识库管理",
                "version": "2.0.0",
                "description": "企业级知识库和元数据管理",
                "status": "active",
                "endpoints": [
                    "/api/knowledge/",
                    "/api/knowledge/metadata/templates/",
                    "/api/knowledge/dd/",
                    "/api/metadata/databases",
                    "/api/metadata/tables"
                ],
                "features": [
                    "多类型知识库支持",
                    "元数据模板管理",
                    "DD数据字典管理",
                    "向量搜索和语义检索",
                    "企业级数据治理"
                ]
            }
        }
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动HSBC知识管理系统主服务")
    print("📍 端口: 30337")
    print("🌐 文档: http://localhost:30337/docs")
    print("✅ 环境: Production (生产)")
    print("💼 用途: 企业级知识管理和数据治理")
    print("🔧 模块: DD提交管理 + 知识库管理")
    uvicorn.run(app, host="0.0.0.0", port=30337)

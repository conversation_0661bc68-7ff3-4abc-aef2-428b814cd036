"""
元数据API路由模块

按功能模块组织的API路由，参照DD系统的设计。
"""

from .databases import router as databases_router
from .tables import router as tables_router
from .columns import router as columns_router
from .code_sets import router as code_sets_router
from .data_subjects import router as data_subjects_router
from .relations import router as relations_router
from .templates import router as templates_router  # 新增模板管理路由
from .search import router as search_router
from .system import router as system_router

__all__ = [
    "databases_router",
    "tables_router",
    "columns_router",
    "code_sets_router",
    "data_subjects_router",
    "relations_router",
    "templates_router",  # 新增模板管理路由
    "search_router",
    "system_router",
]

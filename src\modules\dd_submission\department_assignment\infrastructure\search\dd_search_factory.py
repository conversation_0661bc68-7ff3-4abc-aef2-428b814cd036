"""
DD三层搜索工厂类

提供便捷的方法来创建和配置DD系统的三层搜索引擎
"""

import logging
from typing import Any

logger = logging.getLogger(__name__)

from modules.dd_submission.common.search.three_layer_search import (
    ThreeLayerSearchEngine, ThreeLayerSearchBuilder,
    FilterLayerType, SearchLayerConfig, FilterLayerConfig
)
from .dd_three_layer_search import (
    DDSearchLayerExecutor, DDFilterLayerExecutor, DDDecisionMaker
)
from ..models import DepartmentAssignmentRequest, HistoricalRecord


class DDSearchEngineFactory:
    """DD搜索引擎工厂"""
    
    @staticmethod
    def create_default_engine(
        rdb_client: Any, 
        vdb_client: Any
    ) -> ThreeLayerSearchEngine[HistoricalRecord, DepartmentAssignmentRequest]:
        """
        创建默认配置的DD三层搜索引擎
        
        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端
            
        Returns:
            配置好的三层搜索引擎
        """
        # 创建执行器
        search_executor = DDSearchLayerExecutor(rdb_client, vdb_client)
        filter_executor = DDFilterLayerExecutor()
        decision_maker = DDDecisionMaker()
        
        # 使用构建器创建配置
        builder = ThreeLayerSearchBuilder()
        
        # 配置精确匹配层
        builder.with_exact_match_config(
            enabled=True,
            timeout_seconds=10.0,
            max_results=50,
            min_confidence=1.0
        )
        
        # 配置混合搜索层
        builder.with_hybrid_search_config(
            enabled=True,
            timeout_seconds=30.0,
            max_results=100,
            min_confidence=0.5
        )
        
        # 配置TF-IDF兜底层
        builder.with_tfidf_fallback_config(
            enabled=True,
            timeout_seconds=15.0,
            max_results=5,
            min_confidence=0.3,
            custom_params={"top_k": 3}
        )
        
        # 配置四层筛选
        builder.with_filter_layer_config(
            FilterLayerType.SET_TYPE,
            enabled=True,
            field_name="set_value",
            stop_on_unique=True
        )
        
        builder.with_filter_layer_config(
            FilterLayerType.REPORT_TYPE,
            enabled=True,
            field_name="report_type",
            stop_on_unique=True
        )
        
        builder.with_filter_layer_config(
            FilterLayerType.SUBMISSION_TYPE,
            enabled=True,
            field_name="submission_type",
            stop_on_unique=True
        )
        
        builder.with_filter_layer_config(
            FilterLayerType.DATA_LAYER,
            enabled=True,
            field_name="dr01",
            stop_on_unique=False  # 最后一层不需要提前停止
        )
        
        # 配置全局参数
        builder.with_global_config(
            enable_four_layer_filtering=True,
            max_total_time_seconds=120.0,
            enable_early_termination=True
        )
        
        # 构建引擎
        engine = builder.build(search_executor, filter_executor, decision_maker)
        
        logger.info("DD三层搜索引擎创建完成（默认配置）")
        return engine
    
    @staticmethod
    def create_fast_engine(
        rdb_client: Any, 
        vdb_client: Any
    ) -> ThreeLayerSearchEngine[HistoricalRecord, DepartmentAssignmentRequest]:
        """
        创建快速模式的DD三层搜索引擎（优化性能）
        
        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端
            
        Returns:
            优化性能的三层搜索引擎
        """
        # 创建执行器
        search_executor = DDSearchLayerExecutor(rdb_client, vdb_client)
        filter_executor = DDFilterLayerExecutor()
        decision_maker = DDDecisionMaker()
        
        # 使用构建器创建配置
        builder = ThreeLayerSearchBuilder()
        
        # 配置精确匹配层（快速模式）
        builder.with_exact_match_config(
            enabled=True,
            timeout_seconds=5.0,
            max_results=20,
            min_confidence=1.0
        )
        
        # 配置混合搜索层（快速模式）
        builder.with_hybrid_search_config(
            enabled=True,
            timeout_seconds=15.0,
            max_results=50,
            min_confidence=0.6  # 提高阈值以减少结果
        )
        
        # 配置TF-IDF兜底层（快速模式）
        builder.with_tfidf_fallback_config(
            enabled=True,
            timeout_seconds=8.0,
            max_results=3,
            min_confidence=0.4,
            custom_params={"top_k": 2}
        )
        
        # 配置四层筛选（快速模式）
        builder.with_filter_layer_config(
            FilterLayerType.SET_TYPE,
            enabled=True,
            field_name="set_value",
            stop_on_unique=True
        )
        
        builder.with_filter_layer_config(
            FilterLayerType.REPORT_TYPE,
            enabled=True,
            field_name="report_type",
            stop_on_unique=True
        )
        
        builder.with_filter_layer_config(
            FilterLayerType.SUBMISSION_TYPE,
            enabled=True,
            field_name="submission_type",
            stop_on_unique=True
        )
        
        builder.with_filter_layer_config(
            FilterLayerType.DATA_LAYER,
            enabled=True,
            field_name="dr01",
            stop_on_unique=False
        )
        
        # 配置全局参数（快速模式）
        builder.with_global_config(
            enable_four_layer_filtering=True,
            max_total_time_seconds=60.0,  # 更短的总时间限制
            enable_early_termination=True
        )
        
        # 构建引擎
        engine = builder.build(search_executor, filter_executor, decision_maker)
        
        logger.info("DD三层搜索引擎创建完成（快速模式）")
        return engine
    
    @staticmethod
    def create_accurate_engine(
        rdb_client: Any, 
        vdb_client: Any
    ) -> ThreeLayerSearchEngine[HistoricalRecord, DepartmentAssignmentRequest]:
        """
        创建高精度模式的DD三层搜索引擎（优化准确性）
        
        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端
            
        Returns:
            优化准确性的三层搜索引擎
        """
        # 创建执行器
        search_executor = DDSearchLayerExecutor(rdb_client, vdb_client)
        filter_executor = DDFilterLayerExecutor()
        decision_maker = DDDecisionMaker()
        
        # 使用构建器创建配置
        builder = ThreeLayerSearchBuilder()
        
        # 配置精确匹配层（高精度模式）
        builder.with_exact_match_config(
            enabled=True,
            timeout_seconds=15.0,
            max_results=100,
            min_confidence=1.0
        )
        
        # 配置混合搜索层（高精度模式）
        builder.with_hybrid_search_config(
            enabled=True,
            timeout_seconds=45.0,
            max_results=200,
            min_confidence=0.3  # 降低阈值以获取更多候选
        )
        
        # 配置TF-IDF兜底层（高精度模式）
        builder.with_tfidf_fallback_config(
            enabled=True,
            timeout_seconds=20.0,
            max_results=10,
            min_confidence=0.2,
            custom_params={"top_k": 5}
        )
        
        # 配置四层筛选（高精度模式）
        builder.with_filter_layer_config(
            FilterLayerType.SET_TYPE,
            enabled=True,
            field_name="set_value",
            stop_on_unique=False  # 不提前停止，确保完整筛选
        )
        
        builder.with_filter_layer_config(
            FilterLayerType.REPORT_TYPE,
            enabled=True,
            field_name="report_type",
            stop_on_unique=False
        )
        
        builder.with_filter_layer_config(
            FilterLayerType.SUBMISSION_TYPE,
            enabled=True,
            field_name="submission_type",
            stop_on_unique=False
        )
        
        builder.with_filter_layer_config(
            FilterLayerType.DATA_LAYER,
            enabled=True,
            field_name="dr01",
            stop_on_unique=False
        )
        
        # 配置全局参数（高精度模式）
        builder.with_global_config(
            enable_four_layer_filtering=True,
            max_total_time_seconds=180.0,  # 更长的总时间限制
            enable_early_termination=False  # 禁用提前终止
        )
        
        # 构建引擎
        engine = builder.build(search_executor, filter_executor, decision_maker)
        
        logger.info("DD三层搜索引擎创建完成（高精度模式）")
        return engine
    
    @staticmethod
    def create_custom_engine(
        rdb_client: Any,
        vdb_client: Any,
        config_dict: dict
    ) -> ThreeLayerSearchEngine[HistoricalRecord, DepartmentAssignmentRequest]:
        """
        根据自定义配置创建DD三层搜索引擎
        
        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端
            config_dict: 自定义配置字典
            
        Returns:
            自定义配置的三层搜索引擎
        """
        # 创建执行器
        search_executor = DDSearchLayerExecutor(rdb_client, vdb_client)
        filter_executor = DDFilterLayerExecutor()
        decision_maker = DDDecisionMaker()
        
        # 使用构建器创建配置
        builder = ThreeLayerSearchBuilder()
        
        # 应用自定义配置
        if "exact_match" in config_dict:
            builder.with_exact_match_config(**config_dict["exact_match"])
        
        if "hybrid_search" in config_dict:
            builder.with_hybrid_search_config(**config_dict["hybrid_search"])
        
        if "tfidf_fallback" in config_dict:
            builder.with_tfidf_fallback_config(**config_dict["tfidf_fallback"])
        
        if "filter_layers" in config_dict:
            for layer_name, layer_config in config_dict["filter_layers"].items():
                try:
                    layer_type = FilterLayerType(layer_name)
                    builder.with_filter_layer_config(layer_type, **layer_config)
                except ValueError:
                    logger.warning(f"未知的筛选层类型: {layer_name}")
        
        if "global" in config_dict:
            builder.with_global_config(**config_dict["global"])
        
        # 构建引擎
        engine = builder.build(search_executor, filter_executor, decision_maker)
        
        logger.info("DD三层搜索引擎创建完成（自定义配置）")
        return engine

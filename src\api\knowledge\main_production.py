"""
知识库管理主服务 - 生产环境
Production Knowledge Management Service

用于前端正式环境的稳定API服务
端口: 30337
"""

import sys
from pathlib import Path

# 添加项目根目录到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

# 初始化企业级日志配置
from utils.common.logger import setup_enterprise_logger
logger = setup_enterprise_logger(
    level="INFO",
    service_name="knowledge-management-production",
    enable_structlog=True
)



from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 导入所有API路由
from api.knowledge.metadata.main import router as metadata_router
from api.knowledge.knowledge_base import router as knowledge_base_router
from api.knowledge.dd.main import router as dd_api_router
from api.knowledge.enums import get_all_enums

# 创建FastAPI应用
app = FastAPI(
    title="知识库管理服务 - 生产环境",
    description="Knowledge Management Service - Production Environment\n\n"
                "稳定的生产API服务，供前端应用使用。\n"
                "提供完整的知识库管理功能，包括：\n"
                "• 知识库管理（支持灵活的模型配置）\n"
                "• 元数据模板管理（MetaData类型）\n"
                "• 文档管理（Doc类型）\n"
                "• DD数据字典管理（DD类型）\n"
                "• 向量搜索和语义检索\n"
                "• 企业级数据治理\n\n"
                "✅ 生产就绪：经过充分测试，性能优化，数据稳定。\n"
                "🚀 最新功能：完整的user_id移除重构，支持三种知识库类型。",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境建议配置具体的前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册所有API路由
# 注册API路由
# 1. 知识库管理API
app.include_router(knowledge_base_router, prefix="/api")

# 2. 元数据管理系统
app.include_router(metadata_router, prefix="/api")

# 3. DD数据需求管理API
app.include_router(dd_api_router, prefix="/api")

# # 4. 文档管理API
# app.include_router(doc_api_router, prefix="/api")

# # 5. 文档块管理API
# app.include_router(chunk_api_router, prefix="/api")

# # 6. 搜索API
# app.include_router(search_api_router, prefix="/api")

@app.get("/")
async def root():
    """服务根路径"""
    return {
        "service": "Knowledge Management Service",
        "environment": "Production",
        "version": "1.0.0",
        "port": 30337,
        "status": "stable",
        "docs": "/docs",
        "api_prefix": "/api",
        "features": [
            "知识库管理（支持灵活模型配置）",
            "元数据模板管理（MetaData类型）",
            "元数据CRUD操作（完整的数据管理）",
            "列关联管理（字段关系管理）",
            "文档管理（Doc类型）",
            "DD数据需求管理（DD类型）",
            "DD智能搜索（向量+文本）",
            "DD部门和填报数据管理",
            "模板下载与预览",
            "文件上传与解析",
            "批量文件上传",
            "任务状态跟踪",
            "外键关系验证",
            "数据完整性检查",
            "向量搜索和语义检索",
            "企业级数据治理",
            "高性能数据库操作",
            "完整的user_id移除重构"
        ],
        "endpoints": {
            "knowledge_bases": "/api/knowledge/",
            "available_models": "/api/knowledge/models/available",
            "metadata_templates": "/api/knowledge/metadata/templates/",
            "metadata_crud_databases": "/api/metadata/databases",
            "metadata_crud_tables": "/api/metadata/tables",
            "metadata_crud_columns": "/api/metadata/columns",
            "metadata_crud_code_sets": "/api/metadata/code-sets",
            "metadata_crud_data_subjects": "/api/metadata/data-subjects",
            "metadata_crud_statistics": "/api/metadata/statistics",
            "metadata_crud_health": "/api/metadata/health",
            "column_relations": "/api/metadata/{knowledge_id}/column-relations",
            "doc_documents": "/api/doc/{knowledge_id}/documents/",
            "doc_chunks": "/api/doc/{knowledge_id}/chunks/",
            "doc_search": "/api/doc/{knowledge_id}/search/",
            "dd_departments": "/api/knowledge/dd/departments/",
            "dd_submissions": "/api/knowledge/dd/submissions/",
            "dd_search": "/api/knowledge/dd/search/",
            "dd_distribution": "/api/knowledge/dd/distribution/",
            "dd_reports": "/api/knowledge/dd/reports/",
            "dd_health": "/api/knowledge/dd/health",
            "dd_overview": "/api/knowledge/dd/overview",
            "supported_templates": "/api/knowledge/metadata/templates/supported",
            "download_template": "/api/knowledge/metadata/templates/download/{template_type}",
            "upload_template": "/api/knowledge/metadata/templates/upload",
            "batch_upload_template": "/api/knowledge/metadata/templates/upload/batch",
            "upload_status": "/api/knowledge/metadata/templates/upload/status/{task_id}"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy", 
        "service": "knowledge-management-production",
        "environment": "production",
        "port": 30337,
        "features_status": {
            "knowledge_base_management": "active",
            "flexible_model_configuration": "active",
            "metadata_management": "active",
            "metadata_crud_operations": "active",
            "document_management": "active",
            "dd_data_requirements": "active",
            "template_service": "active",
            "file_upload": "active",
            "vector_search": "active",
            "foreign_key_validation": "active",
            "data_governance": "active",
            "performance_optimization": "active",
            "user_id_removal_refactor": "completed"
        }
    }

@app.get("/api/info")
async def api_info():
    """API信息"""
    return {
        "api_version": "v1",
        "service_name": "Knowledge Management Service",
        "environment": "Production",
        "description": "生产级知识库管理API服务",
        "validation_mode": "strict",
        "data_governance": "enterprise_grade",
        "production_features": [
            "灵活的模型配置架构",
            "高性能数据库操作",
            "企业级数据验证",
            "完整的错误处理",
            "性能监控和优化"
        ],
        "supported_operations": [
            "知识库CRUD操作",
            "模型配置管理",
            "元数据模板管理",
            "元数据CRUD操作（数据库、表、字段、代码集、数据主题）",
            "列关联管理（源字段关联、指标字段关联）",
            "文档管理和搜索",
            "DD数据需求管理（部门、填报、分发、报表）",
            "DD智能搜索（向量搜索、混合搜索）",
            "向量搜索和语义检索",
            "文档块管理",
            "数据验证",
            "外键检查",
            "批量处理",
            "任务状态跟踪",
            "统计分析",
            "健康检查"
        ]
    }

@app.get("/production/status")
async def production_status():
    """生产状态信息"""
    return {
        "environment": "production",
        "purpose": "前端应用的稳定API服务",
        "data_stability": "production_ready",
        "performance": "optimized",
        "reliability": "high_availability",
        "recent_updates": [
            "✅ 完成了完整的user_id移除重构",
            "✅ 实现了DD数据字典的键值对存储架构",
            "✅ 集成了所有API模块到统一服务",
            "✅ 支持三种知识库类型（MetaData、Doc、DD）",
            "✅ 实现了向量搜索和语义检索功能",
            "✅ 优化了数据库查询性能",
            "✅ 完善了企业级数据治理"
        ],
        "model_configuration_support": {
            "MetaData": ["embedding"],
            "Doc": ["embedding", "llm"],
            "DD": ["embedding", "llm", "other"]
        }
    }


@app.get("/api/enums")
async def get_api_enums():
    """
    获取API中所有枚举值信息

    返回系统中定义的所有枚举类型和对应的可选值，
    用于前端动态生成选项和API文档生成。
    """
    return {
        "success": True,
        "message": "获取枚举信息成功",
        "data": get_all_enums(),
        "description": "API中使用的所有枚举类型和可选值"
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动知识库管理服务 - 生产环境")
    print("📍 端口: 30337")
    print("🌐 文档: http://localhost:30337/docs")
    print("✅ 环境: Production (生产)")
    print("💼 用途: 前端应用API服务")
    uvicorn.run(app, host="0.0.0.0", port=30337)

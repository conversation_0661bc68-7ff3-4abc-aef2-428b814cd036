#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：src 
@File    ：exceptions.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/23 14:47 
@Desc    ：外规内化异常
"""
from typing import Optional, Dict, Any


class IerError(Exception):
    """外规内化基础异常"""

    def __init__(
            self,
            message: str,
            error_code: Optional[str] = None,
            details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "IER_ERROR"
        self.details = details or {}

    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class IerOcrResultSaveError(IerError):
    """外规内化结果保存异常"""

    def __init__(
            self,
            message: str,
            file_id: str = None,
            **kwargs
    ):
        super().__init__(
            message,
            error_code="IER_OCR_RESULT_SAVE_ERROR",
            **kwargs
        )
        self.file_id = file_id

# ==================== 异常工具函数 ====================

def create_ocr_result_save_error(file_id: str, error_message: str) -> IerOcrResultSaveError:
    """创建外规内化结果保存异常"""
    message = f"file id '{file_id}' save ocr result failure: {error_message}"
    return IerOcrResultSaveError(
        message=message,
        file_id=file_id,
        details={"error_message": error_message}
    )
"""
数据库初始化脚本

创建文档相关的数据库表
"""

from loguru import logger
import asyncio
from sqlalchemy import inspect

from service import get_client
from modules.knowledge.doc.entities.db_models import Base

async def init_db():
    """初始化数据库表"""
    rdb_client = None
    try:
        logger.info("开始初始化文档相关数据库表...")
        
        # 获取数据库连接
        rdb_client =  await get_client("database.rdbs.mysql")
        await rdb_client._ensure_async_engine()
        rdb_client._ensure_sync_engine()
        print(rdb_client.sync_engine is None)

        logger.info(f"数据库连接成功: {rdb_client}")
        
        # 打印将要创建的表
        logger.info("准备创建以下表:")
        for table_name in Base.metadata.tables.keys():
            logger.info(f"  - {table_name}")
        
        # 创建所有表
        logger.info("开始创建数据库表...")
        Base.metadata.create_all(bind=rdb_client.sync_engine)
        
        # 验证表是否创建成功
        logger.info("验证表创建情况...")
        inspector = inspect(rdb_client.sync_engine)
        existing_tables = inspector.get_table_names()
        
        created_doc_tables = [table for table in existing_tables if table in Base.metadata.tables.keys()]
        
        if created_doc_tables:
            logger.success("数据库表创建成功！")
            logger.info("已创建的表:")
            for table in created_doc_tables:
                logger.info(f"  ✓ {table}")
        else:
            logger.warning("没有发现已创建的doc_dev_开头的表")
            logger.info("数据库中所有现有表:")
            for table in existing_tables:
                logger.info(f"  - {table}")
            
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise


if __name__ == "__main__":
    asyncio.run(init_db())



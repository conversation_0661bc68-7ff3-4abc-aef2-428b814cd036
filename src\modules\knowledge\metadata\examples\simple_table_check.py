"""
简单的表结构检查

通过插入测试数据来了解表结构
"""

import asyncio
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def simple_table_check():
    """简单的表结构检查"""
    print("🔍 简单表结构检查")
    print("=" * 50)

    try:
        # 获取数据库客户端
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
        from service import get_client

        rdb_client = await get_client("database.rdbs.mysql")

        # 尝试插入一个最简单的记录到 md_source_key_relation_info
        print("\n📋 测试 md_source_key_relation_info 表")
        print("-" * 30)
        
        try:
            # 尝试插入最基本的数据
            test_data = {
                'relation_name': 'test_simple_relation'
            }
            
            result = await rdb_client.abatch_insert(
                table='md_source_key_relation_info',
                data=[test_data],
                batch_size=1,
                max_concurrency=1
            )
            
            if result.success:
                print("✅ 插入成功，基本字段存在")
                
                # 尝试查询刚插入的记录
                try:
                    from modules.knowledge.metadata.crud_modules.crud_base import MetadataCrudBase
                    base_crud = MetadataCrudBase(rdb_client)
                    
                    records = await base_crud._aselect(
                        table='md_source_key_relation_info',
                        where={'relation_name': 'test_simple_relation'},
                        limit=1
                    )
                    
                    if records:
                        print("查询到的记录字段:")
                        for field_name in records[0].keys():
                            print(f"  - {field_name}")
                    
                    # 清理测试数据
                    await rdb_client.abatch_delete(
                        table='md_source_key_relation_info',
                        conditions=[{'relation_name': 'test_simple_relation'}],
                        batch_size=1,
                        max_concurrency=1
                    )
                    print("✅ 测试数据已清理")
                    
                except Exception as e:
                    print(f"查询失败: {e}")
            else:
                print(f"❌ 插入失败: {result}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

        print("\n" + "=" * 50)
        print("✅ 简单检查完成")

    except Exception as e:
        logger.error(f"检查失败: {e}")
        print(f"❌ 检查失败: {e}")


async def main():
    """主函数"""
    await simple_table_check()


if __name__ == "__main__":
    asyncio.run(main())

"""
ORM查询构建器

基于SQLAlchemy ORM的查询构建器，提供与universal查询构建器相同的接口
"""

from typing import Dict, Any, List, Optional, Sequence, Type, Union
import logging

from sqlalchemy.orm import Query, Session
from sqlalchemy import and_, or_, not_, func, desc, asc
from sqlalchemy.sql import operators

from ..models.base import BaseModel
from ..session.manager import SessionManager
from ..dialects import DatabaseDialect
from ..exceptions import QueryBuilderError, wrap_orm_error

logger = logging.getLogger(__name__)


class ORMQueryBuilder:
    """
    ORM查询构建器
    
    提供与UniversalQueryBuilder相同的接口，但内部使用SQLAlchemy ORM
    """
    
    def __init__(
        self,
        model_class: Type[BaseModel],
        dialect: DatabaseDialect,
        session_manager: SessionManager
    ):
        """
        初始化ORM查询构建器
        
        Args:
            model_class: ORM模型类
            dialect: 数据库方言
            session_manager: 会话管理器
        """
        self.model_class = model_class
        self.dialect = dialect
        self.session_manager = session_manager
        
        # 查询组件
        self._select_columns: List[str] = []
        self._where_conditions: List[Any] = []
        self._join_clauses: List[str] = []
        self._group_by_columns: List[str] = []
        self._having_conditions: List[Any] = []
        self._order_by_clauses: List[Any] = []
        self._limit_value: Optional[int] = None
        self._offset_value: Optional[int] = None
        self._distinct: bool = False
        
        logger.debug(f"Initialized ORM query builder for {model_class.__name__}")
    
    # ==================== SELECT Clause ====================
    
    def select(self, *columns: str) -> 'ORMQueryBuilder':
        """
        添加列到SELECT子句
        
        Args:
            *columns: 列名或表达式
        
        Returns:
            Self用于方法链
        """
        self._select_columns.extend(columns)
        return self
    
    def select_distinct(self, *columns: str) -> 'ORMQueryBuilder':
        """
        添加DISTINCT列到SELECT子句
        
        Args:
            *columns: 列名或表达式
        
        Returns:
            Self用于方法链
        """
        self._distinct = True
        self._select_columns.extend(columns)
        return self
    
    def select_all(self) -> 'ORMQueryBuilder':
        """
        选择所有列(*)
        
        Returns:
            Self用于方法链
        """
        self._select_columns = ["*"]
        return self
    
    def distinct(self) -> 'ORMQueryBuilder':
        """
        启用DISTINCT
        
        Returns:
            Self用于方法链
        """
        self._distinct = True
        return self
    
    # ==================== WHERE Clause ====================
    
    def where(self, column: str, operator: str, value: Any) -> 'ORMQueryBuilder':
        """
        添加WHERE条件
        
        Args:
            column: 列名
            operator: 比较操作符 (=, !=, >, <, >=, <=, LIKE, IN, etc.)
            value: 比较值
        
        Returns:
            Self用于方法链
        """
        if not hasattr(self.model_class, column):
            raise QueryBuilderError(f"Column '{column}' not found in model {self.model_class.__name__}")
        
        column_attr = getattr(self.model_class, column)
        
        # 构建条件
        if operator.upper() == '=':
            condition = column_attr == value
        elif operator.upper() == '!=':
            condition = column_attr != value
        elif operator.upper() == '>':
            condition = column_attr > value
        elif operator.upper() == '<':
            condition = column_attr < value
        elif operator.upper() == '>=':
            condition = column_attr >= value
        elif operator.upper() == '<=':
            condition = column_attr <= value
        elif operator.upper() == 'LIKE':
            condition = column_attr.like(value)
        elif operator.upper() == 'ILIKE':
            condition = column_attr.ilike(value)
        elif operator.upper() == 'IN':
            condition = column_attr.in_(value)
        elif operator.upper() == 'NOT IN':
            condition = ~column_attr.in_(value)
        elif operator.upper() == 'IS NULL':
            condition = column_attr.is_(None)
        elif operator.upper() == 'IS NOT NULL':
            condition = column_attr.isnot(None)
        else:
            raise QueryBuilderError(f"Unsupported operator: {operator}")
        
        self._where_conditions.append(condition)
        return self
    
    def where_in(self, column: str, values: Sequence[Any]) -> 'ORMQueryBuilder':
        """添加WHERE column IN (values)条件"""
        return self.where(column, "IN", list(values))
    
    def where_not_in(self, column: str, values: Sequence[Any]) -> 'ORMQueryBuilder':
        """添加WHERE column NOT IN (values)条件"""
        return self.where(column, "NOT IN", list(values))
    
    def where_null(self, column: str) -> 'ORMQueryBuilder':
        """添加WHERE column IS NULL条件"""
        return self.where(column, "IS NULL", None)
    
    def where_not_null(self, column: str) -> 'ORMQueryBuilder':
        """添加WHERE column IS NOT NULL条件"""
        return self.where(column, "IS NOT NULL", None)
    
    def where_like(self, column: str, pattern: str) -> 'ORMQueryBuilder':
        """添加WHERE column LIKE pattern条件"""
        return self.where(column, "LIKE", pattern)
    
    def where_between(self, column: str, start: Any, end: Any) -> 'ORMQueryBuilder':
        """添加WHERE column BETWEEN start AND end条件"""
        if not hasattr(self.model_class, column):
            raise QueryBuilderError(f"Column '{column}' not found in model {self.model_class.__name__}")
        
        column_attr = getattr(self.model_class, column)
        condition = column_attr.between(start, end)
        self._where_conditions.append(condition)
        return self
    
    # ==================== ORDER BY Clause ====================
    
    def order_by(self, column: str) -> 'ORMQueryBuilder':
        """
        添加ORDER BY子句（升序）
        
        Args:
            column: 列名
        
        Returns:
            Self用于方法链
        """
        if not hasattr(self.model_class, column):
            raise QueryBuilderError(f"Column '{column}' not found in model {self.model_class.__name__}")
        
        column_attr = getattr(self.model_class, column)
        self._order_by_clauses.append(asc(column_attr))
        return self
    
    def order_by_desc(self, column: str) -> 'ORMQueryBuilder':
        """
        添加ORDER BY子句（降序）
        
        Args:
            column: 列名
        
        Returns:
            Self用于方法链
        """
        if not hasattr(self.model_class, column):
            raise QueryBuilderError(f"Column '{column}' not found in model {self.model_class.__name__}")
        
        column_attr = getattr(self.model_class, column)
        self._order_by_clauses.append(desc(column_attr))
        return self
    
    # ==================== LIMIT and OFFSET ====================
    
    def limit(self, count: int) -> 'ORMQueryBuilder':
        """
        设置LIMIT
        
        Args:
            count: 限制数量
        
        Returns:
            Self用于方法链
        """
        self._limit_value = count
        return self
    
    def offset(self, count: int) -> 'ORMQueryBuilder':
        """
        设置OFFSET
        
        Args:
            count: 偏移数量
        
        Returns:
            Self用于方法链
        """
        self._offset_value = count
        return self
    
    # ==================== Query Execution ====================
    
    def build_query(self, session: Session) -> Query:
        """
        构建SQLAlchemy查询对象

        Args:
            session: SQLAlchemy会话

        Returns:
            Query对象
        """
        try:
            # 开始构建查询
            if self._select_columns and self._select_columns != ["*"]:
                # 选择特定列（包括聚合函数）
                column_attrs = []
                for col in self._select_columns:
                    if col.startswith(('SUM(', 'AVG(', 'MAX(', 'MIN(', 'COUNT(')):
                        # 处理聚合函数
                        func_name = col.split('(')[0].lower()
                        col_name = col.split('(')[1].rstrip(')')

                        if hasattr(self.model_class, col_name):
                            column_attr = getattr(self.model_class, col_name)
                            if func_name == 'sum':
                                column_attrs.append(func.sum(column_attr))
                            elif func_name == 'avg':
                                column_attrs.append(func.avg(column_attr))
                            elif func_name == 'max':
                                column_attrs.append(func.max(column_attr))
                            elif func_name == 'min':
                                column_attrs.append(func.min(column_attr))
                            elif func_name == 'count':
                                column_attrs.append(func.count(column_attr))
                    elif hasattr(self.model_class, col):
                        column_attrs.append(getattr(self.model_class, col))
                    else:
                        logger.warning(f"Column '{col}' not found in model {self.model_class.__name__}")

                if column_attrs:
                    query = session.query(*column_attrs)
                else:
                    query = session.query(self.model_class)
            else:
                # 选择所有列
                query = session.query(self.model_class)

            # 应用DISTINCT
            if self._distinct:
                query = query.distinct()

            # 应用WHERE条件
            if self._where_conditions:
                query = query.filter(and_(*self._where_conditions))

            # 应用GROUP BY
            if self._group_by_columns:
                group_attrs = []
                for col in self._group_by_columns:
                    if hasattr(self.model_class, col):
                        group_attrs.append(getattr(self.model_class, col))
                if group_attrs:
                    query = query.group_by(*group_attrs)

            # 应用HAVING条件
            if self._having_conditions:
                query = query.having(and_(*self._having_conditions))

            # 应用ORDER BY
            if self._order_by_clauses:
                query = query.order_by(*self._order_by_clauses)

            # 应用LIMIT和OFFSET
            if self._limit_value is not None:
                query = query.limit(self._limit_value)
            if self._offset_value is not None:
                query = query.offset(self._offset_value)

            return query

        except Exception as e:
            raise wrap_orm_error(e, "build ORM query")
    
    def execute(self) -> List[Dict[str, Any]]:
        """
        执行查询并返回结果
        
        Returns:
            查询结果列表
        """
        try:
            with self.session_manager.get_session() as session:
                query = self.build_query(session)
                results = query.all()
                
                # 转换结果
                if self._select_columns and self._select_columns != ["*"] and len(self._select_columns) == 1:
                    # 单列查询
                    return [{self._select_columns[0]: result} for result in results]
                elif self._select_columns and self._select_columns != ["*"]:
                    # 多列查询
                    return [dict(zip(self._select_columns, result)) for result in results]
                else:
                    # 完整模型查询
                    return [result.to_dict() for result in results]
                    
        except Exception as e:
            raise wrap_orm_error(e, "execute ORM query")
    
    def count(self) -> int:
        """
        获取查询结果数量
        
        Returns:
            结果数量
        """
        try:
            with self.session_manager.get_session() as session:
                query = session.query(self.model_class)
                
                # 应用WHERE条件
                if self._where_conditions:
                    query = query.filter(and_(*self._where_conditions))
                
                return query.count()
                
        except Exception as e:
            raise wrap_orm_error(e, "count ORM query")
    
    def first(self) -> Optional[Dict[str, Any]]:
        """
        获取第一个结果
        
        Returns:
            第一个结果或None
        """
        results = self.limit(1).execute()
        return results[0] if results else None
    
    def exists(self) -> bool:
        """
        检查是否存在匹配的记录

        Returns:
            是否存在
        """
        return self.count() > 0

    # ==================== JOIN Operations ====================

    def join(self, target_table: str, on_condition: str) -> 'ORMQueryBuilder':
        """
        添加INNER JOIN

        Args:
            target_table: 目标表名
            on_condition: JOIN条件

        Returns:
            Self用于方法链
        """
        self._join_clauses.append(f"INNER JOIN {target_table} ON {on_condition}")
        return self

    def left_join(self, target_table: str, on_condition: str) -> 'ORMQueryBuilder':
        """
        添加LEFT JOIN

        Args:
            target_table: 目标表名
            on_condition: JOIN条件

        Returns:
            Self用于方法链
        """
        self._join_clauses.append(f"LEFT JOIN {target_table} ON {on_condition}")
        return self

    def right_join(self, target_table: str, on_condition: str) -> 'ORMQueryBuilder':
        """
        添加RIGHT JOIN

        Args:
            target_table: 目标表名
            on_condition: JOIN条件

        Returns:
            Self用于方法链
        """
        self._join_clauses.append(f"RIGHT JOIN {target_table} ON {on_condition}")
        return self

    def inner_join(self, target_table: str, on_condition: str) -> 'ORMQueryBuilder':
        """
        添加INNER JOIN（别名）

        Args:
            target_table: 目标表名
            on_condition: JOIN条件

        Returns:
            Self用于方法链
        """
        return self.join(target_table, on_condition)

    def full_join(self, target_table: str, on_condition: str) -> 'ORMQueryBuilder':
        """
        添加FULL OUTER JOIN

        Args:
            target_table: 目标表名
            on_condition: JOIN条件

        Returns:
            Self用于方法链
        """
        self._join_clauses.append(f"FULL OUTER JOIN {target_table} ON {on_condition}")
        return self

    # ==================== GROUP BY and HAVING ====================

    def group_by(self, *columns: str) -> 'ORMQueryBuilder':
        """
        添加GROUP BY子句

        Args:
            *columns: 分组列名

        Returns:
            Self用于方法链
        """
        self._group_by_columns.extend(columns)
        return self

    def having(self, column: str, operator: str, value: Any) -> 'ORMQueryBuilder':
        """
        添加HAVING条件

        Args:
            column: 列名
            operator: 比较操作符
            value: 比较值

        Returns:
            Self用于方法链
        """
        if not hasattr(self.model_class, column):
            raise QueryBuilderError(f"Column '{column}' not found in model {self.model_class.__name__}")

        column_attr = getattr(self.model_class, column)

        # 构建HAVING条件（类似WHERE条件）
        if operator.upper() == '=':
            condition = column_attr == value
        elif operator.upper() == '!=':
            condition = column_attr != value
        elif operator.upper() == '>':
            condition = column_attr > value
        elif operator.upper() == '<':
            condition = column_attr < value
        elif operator.upper() == '>=':
            condition = column_attr >= value
        elif operator.upper() == '<=':
            condition = column_attr <= value
        elif operator.upper() == 'IN':
            condition = column_attr.in_(value)
        else:
            raise QueryBuilderError(f"Unsupported HAVING operator: {operator}")

        self._having_conditions.append(condition)
        return self

    # ==================== Aggregation Functions ====================

    def sum(self, column: str) -> 'ORMQueryBuilder':
        """
        添加SUM聚合函数

        Args:
            column: 列名

        Returns:
            Self用于方法链
        """
        if not hasattr(self.model_class, column):
            raise QueryBuilderError(f"Column '{column}' not found in model {self.model_class.__name__}")

        self._select_columns.append(f"SUM({column})")
        return self

    def avg(self, column: str) -> 'ORMQueryBuilder':
        """
        添加AVG聚合函数

        Args:
            column: 列名

        Returns:
            Self用于方法链
        """
        if not hasattr(self.model_class, column):
            raise QueryBuilderError(f"Column '{column}' not found in model {self.model_class.__name__}")

        self._select_columns.append(f"AVG({column})")
        return self

    def max(self, column: str) -> 'ORMQueryBuilder':
        """
        添加MAX聚合函数

        Args:
            column: 列名

        Returns:
            Self用于方法链
        """
        if not hasattr(self.model_class, column):
            raise QueryBuilderError(f"Column '{column}' not found in model {self.model_class.__name__}")

        self._select_columns.append(f"MAX({column})")
        return self

    def min(self, column: str) -> 'ORMQueryBuilder':
        """
        添加MIN聚合函数

        Args:
            column: 列名

        Returns:
            Self用于方法链
        """
        if not hasattr(self.model_class, column):
            raise QueryBuilderError(f"Column '{column}' not found in model {self.model_class.__name__}")

        self._select_columns.append(f"MIN({column})")
        return self

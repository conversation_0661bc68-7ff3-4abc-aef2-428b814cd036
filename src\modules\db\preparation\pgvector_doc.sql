-- ==========================================
-- Doc级向量知识库设计 (统一架构版本)
-- 创建时间: 2025-01-16
-- 数据库: PostgreSQL + pgvector
-- 描述: 基于现有业务代码分析的简洁统一设计，移除user_id管理
-- ==========================================

-- 启用pgvector扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- ==========================================
-- 1. 统一的文档向量表 (核心表)
-- ==========================================

DROP TABLE IF EXISTS doc_embeddings CASCADE;
CREATE TABLE doc_embeddings (
    id BIGSERIAL,
    knowledge_id VARCHAR(255) NOT NULL,
    doc_id VARCHAR(64) NOT NULL,           -- 对应doc_documents.doc_id，用于文档级过滤
    chunk_id VARCHAR(64) NOT NULL,         -- 对应doc_chunks.chunk_id，用于分块级过滤
    chunk_info_id VARCHAR(64) NOT NULL,    -- 对应doc_chunks_info.chunk_info_id，核心关联键
    info_type VARCHAR(50) NOT NULL,        -- content-内容, title-标题, keywords-关键词, summary-摘要等
    embedding vector(768) NOT NULL,        -- 768维向量
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, knowledge_id)
) PARTITION BY HASH (knowledge_id);

-- 表注释
COMMENT ON TABLE doc_embeddings IS '统一文档向量表：存储所有类型的向量化信息，通过info_type区分不同内容类型';
COMMENT ON COLUMN doc_embeddings.embedding IS '768维向量表示，用于语义相似度搜索';
COMMENT ON COLUMN doc_embeddings.knowledge_id IS '知识库ID，用于隔离不同知识库的数据';
COMMENT ON COLUMN doc_embeddings.doc_id IS '文档ID，对应doc_documents.doc_id，用于文档级过滤查询';
COMMENT ON COLUMN doc_embeddings.chunk_id IS '分块ID，对应doc_chunks.chunk_id，用于分块级过滤查询';
COMMENT ON COLUMN doc_embeddings.chunk_info_id IS '分块信息ID，对应doc_chunks_info.chunk_info_id，是查询MySQL的核心关联键';
COMMENT ON COLUMN doc_embeddings.info_type IS '信息类型：content-文本内容, title-标题, keywords-关键词, summary-摘要等';
COMMENT ON COLUMN doc_embeddings.create_time IS '向量创建时间';
COMMENT ON COLUMN doc_embeddings.update_time IS '向量最后更新时间，用于调试、数据治理和增量同步';

-- ==========================================
-- 2. 创建128个分区表 (企业级分区策略)
-- ==========================================

DO $$ 
BEGIN
    FOR i IN 0..127 LOOP
        EXECUTE format('CREATE TABLE doc_embeddings_part_%s PARTITION OF doc_embeddings FOR VALUES WITH (modulus 128, remainder %s)', i, i);
    END LOOP;
END $$;

-- ==========================================
-- 3. 创建高性能索引
-- ==========================================

-- 向量相似度搜索索引 (HNSW)
CREATE INDEX idx_doc_embeddings_hnsw ON doc_embeddings 
USING HNSW (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 200);

-- 核心查询索引
CREATE INDEX idx_doc_embeddings_chunk_info_id ON doc_embeddings (chunk_info_id);
CREATE INDEX idx_doc_embeddings_knowledge ON doc_embeddings (knowledge_id);
CREATE INDEX idx_doc_embeddings_doc_id ON doc_embeddings (doc_id);
CREATE INDEX idx_doc_embeddings_chunk_id ON doc_embeddings (chunk_id);
CREATE INDEX idx_doc_embeddings_info_type ON doc_embeddings (info_type);

-- 复合索引：支持常见查询模式
CREATE INDEX idx_doc_embeddings_doc_type ON doc_embeddings (doc_id, info_type);
CREATE INDEX idx_doc_embeddings_knowledge_type ON doc_embeddings (knowledge_id, info_type);
CREATE INDEX idx_doc_embeddings_create_time ON doc_embeddings (create_time DESC);
CREATE INDEX idx_doc_embeddings_update_time ON doc_embeddings (update_time DESC);

-- 添加唯一约束以支持向量去重
CREATE UNIQUE INDEX idx_doc_embeddings_unique ON doc_embeddings (knowledge_id, doc_id, chunk_id, info_type);

-- 索引注释
COMMENT ON INDEX idx_doc_embeddings_hnsw IS '向量HNSW索引：用于高效的语义相似度搜索，m=16, ef_construction=200';
COMMENT ON INDEX idx_doc_embeddings_chunk_info_id IS '分块信息ID索引：用于与MySQL doc_chunks_info表的高效关联查询';
COMMENT ON INDEX idx_doc_embeddings_knowledge IS '知识库索引：支持按知识库查询和数据隔离';
COMMENT ON INDEX idx_doc_embeddings_doc_id IS '文档ID索引：用于文档内搜索和文档级过滤';
COMMENT ON INDEX idx_doc_embeddings_info_type IS '信息类型索引：用于区分content、keywords等类型的向量搜索';
COMMENT ON INDEX idx_doc_embeddings_doc_type IS '文档类型复合索引：优化文档内按类型搜索';
COMMENT ON INDEX idx_doc_embeddings_knowledge_type IS '知识库类型复合索引：优化按知识库和类型的组合查询';
COMMENT ON INDEX idx_doc_embeddings_create_time IS '创建时间索引：支持按创建时间排序和时间范围查询';
COMMENT ON INDEX idx_doc_embeddings_update_time IS '更新时间索引：用于增量同步、数据治理和调试';

-- ==========================================
-- 4. 分区管理视图和函数
-- ==========================================

-- 分区统计视图
CREATE OR REPLACE VIEW v_doc_embeddings_partition_stats AS
SELECT 
    CASE 
        WHEN tablename LIKE 'doc_embeddings_part_%' THEN 'doc_embeddings'
        ELSE tablename 
    END as table_name,
    tablename as partition_name,
    SUBSTRING(tablename FROM 'doc_embeddings_part_(\d+)')::INTEGER as partition_id,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    (SELECT count(*) FROM information_schema.tables WHERE table_name = tablename) as row_count_estimate
FROM pg_tables 
WHERE tablename LIKE 'doc_embeddings_part_%'
ORDER BY partition_id;

-- 获取分区统计信息函数
CREATE OR REPLACE FUNCTION get_doc_embeddings_partition_stats()
RETURNS TABLE(
    table_name TEXT,
    partition_count BIGINT,
    total_size TEXT,
    avg_partition_size TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'doc_embeddings'::TEXT,
        COUNT(*)::BIGINT,
        pg_size_pretty(SUM(pg_total_relation_size(schemaname||'.'||tablename))) as total_size,
        pg_size_pretty(AVG(pg_total_relation_size(schemaname||'.'||tablename))::BIGINT) as avg_size
    FROM pg_tables 
    WHERE tablename LIKE 'doc_embeddings_part_%';
END;
$$ LANGUAGE plpgsql;

-- ==========================================
-- 5. 查询优化视图
-- ==========================================

-- 统一搜索视图：简化应用层查询
CREATE OR REPLACE VIEW v_doc_search AS
SELECT 
    de.knowledge_id,
    de.doc_id,
    de.chunk_id,
    de.chunk_info_id,
    de.info_type,
    de.embedding,
    de.create_time,
    de.update_time
FROM doc_embeddings de;

-- 按类型分组的统计视图
CREATE OR REPLACE VIEW v_doc_embeddings_stats AS
SELECT 
    knowledge_id,
    info_type,
    COUNT(*) as vector_count,
    COUNT(DISTINCT doc_id) as doc_count,
    COUNT(DISTINCT chunk_id) as chunk_count,
    MIN(create_time) as first_created,
    MAX(create_time) as last_created
FROM doc_embeddings
GROUP BY knowledge_id, info_type;

-- ==========================================
-- 6. 性能统计函数
-- ==========================================

CREATE OR REPLACE FUNCTION get_doc_embeddings_stats()
RETURNS TABLE(
    stat_type TEXT,
    table_name TEXT,
    knowledge_id TEXT,
    vector_count BIGINT,
    create_time TIMESTAMP
) AS $$
BEGIN
    -- 总体统计
    RETURN QUERY
    SELECT 
        'total_embeddings' as stat_type,
        'doc_embeddings' as table_name,
        'ALL' as knowledge_id,
        COUNT(*) as vector_count,
        CURRENT_TIMESTAMP as create_time
    FROM doc_embeddings;
    
    -- 按知识库统计
    RETURN QUERY
    SELECT 
        'embeddings_by_knowledge' as stat_type,
        'doc_embeddings' as table_name,
        de.knowledge_id,
        COUNT(*) as vector_count,
        CURRENT_TIMESTAMP as create_time
    FROM doc_embeddings de
    GROUP BY de.knowledge_id;
    
    -- 按类型统计
    RETURN QUERY
    SELECT 
        'embeddings_by_type_' || info_type as stat_type,
        'doc_embeddings' as table_name,
        'ALL' as knowledge_id,
        COUNT(*) as vector_count,
        CURRENT_TIMESTAMP as create_time
    FROM doc_embeddings
    GROUP BY info_type;
END;
$$ LANGUAGE plpgsql;

-- ==========================================
-- 7. 示例查询语句
-- ==========================================

/*
-- 1. 通用语义搜索（获取最相似的内容）
SELECT chunk_info_id, doc_id, chunk_id, info_type,
       embedding <=> '[向量]' as similarity_score
FROM doc_embeddings 
WHERE knowledge_id = 'kb_001'
ORDER BY embedding <=> '[向量]' LIMIT 10;

-- 2. 按类型过滤的语义搜索
SELECT chunk_info_id, doc_id, embedding <=> '[向量]' as similarity_score
FROM doc_embeddings 
WHERE knowledge_id = 'kb_001' AND info_type = 'content'
ORDER BY embedding <=> '[向量]' LIMIT 10;

-- 3. 文档内语义搜索
SELECT chunk_info_id, chunk_id, embedding <=> '[向量]' as similarity_score
FROM doc_embeddings 
WHERE knowledge_id = 'kb_001' AND doc_id = 'doc_123'
ORDER BY embedding <=> '[向量]' LIMIT 10;

-- 4. 关键词语义匹配
SELECT chunk_info_id, doc_id, embedding <=> '[向量]' as similarity_score
FROM doc_embeddings 
WHERE knowledge_id = 'kb_001' AND info_type = 'keywords'
ORDER BY embedding <=> '[向量]' LIMIT 20;

-- 5. 按知识库统计向量数量
SELECT 
    knowledge_id,
    info_type,
    COUNT(*) as vector_count,
    COUNT(DISTINCT doc_id) as unique_docs,
    COUNT(DISTINCT chunk_id) as unique_chunks
FROM doc_embeddings 
GROUP BY knowledge_id, info_type
ORDER BY knowledge_id, info_type;

-- 6. 最新向量数据查询
SELECT 
    chunk_info_id, 
    doc_id, 
    info_type,
    create_time
FROM doc_embeddings 
WHERE knowledge_id = 'kb_001'
ORDER BY create_time DESC 
LIMIT 50;

-- 7. 向量数据更新情况
SELECT 
    DATE(update_time) as update_date,
    COUNT(*) as updated_count,
    COUNT(DISTINCT doc_id) as affected_docs
FROM doc_embeddings 
WHERE knowledge_id = 'kb_001' AND update_time > CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(update_time)
ORDER BY update_date DESC;
*/

-- ==========================================
-- 8. 设计总结
-- ==========================================

/*
设计核心思想：
1. 统一架构：所有向量化信息存储在一个表中，通过info_type区分类型
2. 高效查询：支持"1次pgvector + 1次MySQL"的查询模式
3. 企业级分区：128个分区支持大规模数据和高并发
4. 关联优化：chunk_info_id作为核心关联键，确保与MySQL表的高效关联
5. 扩展性强：新增info_type无需改变表结构

支持的info_type类型：
- content: 分块的文本内容（主要搜索类型）
- title: 分块标题
- keywords: 关键词（支持语义关键词匹配）
- summary: 分块摘要
- question: 从内容提取的问题
- answer: 对应的答案
- (可扩展更多类型)

查询场景：
1. 通用语义搜索：跨文档搜索相关内容
2. 文档内搜索：在指定文档中搜索
3. 按类型搜索：搜索特定类型的向量（如只搜关键词）
4. 多类型融合搜索：结合不同类型的向量进行重排

业务属性存储：
- keyword的业务属性（置信度、频率等）存储在MySQL的doc_chunks_info表或扩展表中
- 向量表只保留关联键，不重复存储业务数据

时间戳字段：
- create_time: 向量首次创建时间
- update_time: 向量最后更新时间，用于调试、数据治理、增量同步等场景
*/ 
"""
服务优先级管理模块

提供企业级服务优先级定义和配置路径映射功能
"""

import logging
from enum import Enum
from typing import Dict, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class ServicePriority(Enum):
    """服务优先级枚举"""
    HIGH = "high"
    STANDARD = "standard" 
    LOW = "low"
    
    @classmethod
    def from_string(cls, priority_str: str) -> 'ServicePriority':
        """从字符串创建优先级枚举"""
        priority_map = {
            'high': cls.HIGH,
            'critical': cls.HIGH,
            'urgent': cls.HIGH,
            'standard': cls.STANDARD,
            'normal': cls.STANDARD,
            'medium': cls.STANDARD,
            'low': cls.LOW,
            'background': cls.LOW,
            'batch': cls.LOW
        }
        
        normalized = priority_str.lower().strip()
        if normalized in priority_map:
            return priority_map[normalized]
        
        logger.warning(f"未知优先级 '{priority_str}'，使用默认优先级 'standard'")
        return cls.STANDARD


@dataclass
class DatabaseType:
    """数据库类型定义"""
    name: str
    category: str  # 'rdb' 或 'vdb'
    
    def __str__(self):
        return self.name


class PriorityConfigMapper:
    """
    优先级配置映射器
    
    负责将数据库类型和优先级映射到具体的配置路径
    """
    
    # 支持的数据库类型
    MYSQL = DatabaseType("mysql", "rdb")
    PGVECTOR = DatabaseType("pgvector", "vdb")
    
    # 数据库类型映射
    DATABASE_TYPES = {
        "mysql": MYSQL,
        "pgvector": PGVECTOR,
        "pg": PGVECTOR,  # 别名
        "postgres": PGVECTOR,  # 别名
        "vector": PGVECTOR,  # 别名
    }
    
    @classmethod
    def get_config_path(cls, 
                       db_type: str, 
                       priority: ServicePriority) -> str:
        """
        获取配置路径
        
        Args:
            db_type: 数据库类型 ('mysql', 'pgvector')
            priority: 服务优先级
            
        Returns:
            配置路径字符串
            
        Raises:
            ValueError: 不支持的数据库类型
        """
        # 标准化数据库类型
        normalized_db_type = db_type.lower().strip()
        
        if normalized_db_type not in cls.DATABASE_TYPES:
            supported_types = list(cls.DATABASE_TYPES.keys())
            raise ValueError(f"不支持的数据库类型: {db_type}. 支持的类型: {supported_types}")
        
        db_info = cls.DATABASE_TYPES[normalized_db_type]
        
        # 构建配置路径
        if priority == ServicePriority.STANDARD:
            # 标准优先级使用原有配置路径（向后兼容）
            if db_info.category == "rdb":
                return f"database.rdbs.{db_info.name}"
            else:
                return f"database.vdbs.{db_info.name}"
        else:
            # 其他优先级使用新的配置路径
            if db_info.category == "rdb":
                return f"database.rdbs.{db_info.name}_{priority.value}_priority"
            else:
                return f"database.vdbs.{db_info.name}_{priority.value}_priority"
    
    @classmethod
    def parse_config_path(cls, config_path: str) -> Tuple[Optional[str], Optional[ServicePriority]]:
        """
        解析配置路径，提取数据库类型和优先级
        
        Args:
            config_path: 配置路径
            
        Returns:
            (数据库类型, 优先级) 元组，如果无法解析则返回 (None, None)
        """
        try:
            parts = config_path.split('.')
            
            if len(parts) < 3:
                return None, None
            
            # 期望格式: database.rdbs.mysql 或 database.vdbs.pgvector
            # 或者: database.rdbs.mysql_high_priority
            if parts[0] != "database":
                return None, None
            
            category = parts[1]  # rdbs 或 vdbs
            db_config = parts[2]  # mysql 或 mysql_high_priority
            
            # 检查是否包含优先级后缀
            if "_priority" in db_config:
                # 提取数据库类型和优先级
                db_parts = db_config.split("_")
                if len(db_parts) >= 3 and db_parts[-1] == "priority":
                    db_type = db_parts[0]
                    priority_str = db_parts[-2]
                    priority = ServicePriority.from_string(priority_str)
                    return db_type, priority
            else:
                # 标准配置，默认为 standard 优先级
                return db_config, ServicePriority.STANDARD
            
            return None, None
            
        except Exception as e:
            logger.warning(f"解析配置路径失败: {config_path}, 错误: {e}")
            return None, None
    
    @classmethod
    def get_all_config_paths(cls, db_type: str) -> Dict[ServicePriority, str]:
        """
        获取指定数据库类型的所有优先级配置路径
        
        Args:
            db_type: 数据库类型
            
        Returns:
            优先级到配置路径的映射字典
        """
        result = {}
        for priority in ServicePriority:
            try:
                config_path = cls.get_config_path(db_type, priority)
                result[priority] = config_path
            except ValueError:
                continue
        
        return result
    
    @classmethod
    def list_supported_databases(cls) -> Dict[str, DatabaseType]:
        """列出所有支持的数据库类型"""
        return cls.DATABASE_TYPES.copy()


# 便捷函数
def get_priority_config_path(db_type: str, priority: str) -> str:
    """
    便捷函数：获取优先级配置路径
    
    Args:
        db_type: 数据库类型
        priority: 优先级字符串
        
    Returns:
        配置路径
    """
    priority_enum = ServicePriority.from_string(priority)
    return PriorityConfigMapper.get_config_path(db_type, priority_enum)


def parse_priority_from_config(config_path: str) -> Optional[ServicePriority]:
    """
    便捷函数：从配置路径解析优先级
    
    Args:
        config_path: 配置路径
        
    Returns:
        优先级枚举，如果无法解析则返回None
    """
    _, priority = PriorityConfigMapper.parse_config_path(config_path)
    return priority

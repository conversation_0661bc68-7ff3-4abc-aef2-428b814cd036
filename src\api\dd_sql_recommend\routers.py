"""
DD SQL推荐API路由
"""

import logging
from typing import List
from fastapi import APIRouter, Depends, HTTPException

from service import get_client
from app.dd_sql_recommend.services import DDSQLRecommendService
from app.dd_sql_recommend.models import (
    SQLRecommendRequest,
    SQLRecommendResponse,
    QuestionRecommendRequest,
    QuestionRecommendResponse,
    SQLGenerateRequest,
    SQLGenerateResponse,
    SingleSQLGenerateRequest,
    SingleSQLGenerateResponse,
    SQLIntegrationRequest,
    SQLIntegrationResponse
)

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/api/dd/sql-recommend",
    tags=["DD SQL推荐接口"]
)


# ==================== 依赖注入 ====================

async def get_dd_sql_recommend_service():
    """获取DD SQL推荐服务实例"""
    rdb_client = await get_client("database.rdbs.mysql")
    vdb_client = await get_client("database.vdbs.pgvector")
    embedding_client = await get_client("model.embeddings.moka-m3e-base")
    llm_client = await get_client("model.llms.opentrek")
    
    return DDSQLRecommendService(
        rdb_client=rdb_client,
        vdb_client=vdb_client,
        embedding_client=embedding_client,
        llm_client=llm_client
    )


# ==================== API端点 ====================

@router.post("/recommend-sql", response_model=SQLRecommendResponse)
async def recommend_sql(
    request: SQLRecommendRequest,
    service: DDSQLRecommendService = Depends(get_dd_sql_recommend_service)
):
    """
    接口1：推荐SQL接口
    用户点进辅助生成页面，前端携带该填报项信息请求后端，后端需要推荐一个sql给到前端。
    """
    try:
        sql = await service.recommend_sql(request)
        if not sql:
            raise HTTPException(status_code=404, detail="未找到推荐的SQL")
        
        return SQLRecommendResponse(sql=sql)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"推荐SQL接口失败: {e}")
        raise HTTPException(status_code=500, detail=f"推荐SQL失败: {str(e)}")


@router.post("/recommend-questions", response_model=QuestionRecommendResponse)
async def recommend_questions(
    request: QuestionRecommendRequest,
    service: DDSQLRecommendService = Depends(get_dd_sql_recommend_service)
):
    """
    接口2：推荐指引问题接口
    用户点入页面时，同步会请求后端，该接口用于推荐数个指引类型的问题给到前端
    """
    try:
        question_list = await service.recommend_questions(request)
        return QuestionRecommendResponse(question_list=question_list)
    except Exception as e:
        logger.error(f"推荐问题接口失败: {e}")
        raise HTTPException(status_code=500, detail=f"推荐问题失败: {str(e)}")


@router.post("/generate-sql-list", response_model=SQLGenerateResponse)
async def generate_sql_list(
    request: SQLGenerateRequest,
    service: DDSQLRecommendService = Depends(get_dd_sql_recommend_service)
):
    """
    接口3：批量生成SQL接口
    先去dd-b表查询指标对应的业务逻辑，使用到的表范围，字段，结合用户自行修改和确认好的问题后，
    遍历问题，去调用nl2sql生成模块，返回sql
    """
    try:
        sql_list = await service.generate_sql_list(request)
        return SQLGenerateResponse(sql_list=sql_list)
    except Exception as e:
        logger.error(f"批量生成SQL接口失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量生成SQL失败: {str(e)}")


@router.post("/generate-single-sql", response_model=SingleSQLGenerateResponse)
async def generate_single_sql(
    request: SingleSQLGenerateRequest,
    service: DDSQLRecommendService = Depends(get_dd_sql_recommend_service)
):
    """
    接口4：单个生成SQL接口
    该接口和接口3功能完全一致，只是只对一个question进行生成。
    """
    try:
        sql = await service.generate_single_sql(request)
        if not sql:
            raise HTTPException(status_code=404, detail="未能生成SQL")
        
        return SingleSQLGenerateResponse(sql=sql)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"单个生成SQL接口失败: {e}")
        raise HTTPException(status_code=500, detail=f"单个生成SQL失败: {str(e)}")


@router.post("/integrate-sql", response_model=SQLIntegrationResponse)
async def integrate_sql(
    request: SQLIntegrationRequest,
    service: DDSQLRecommendService = Depends(get_dd_sql_recommend_service)
):
    """
    接口5：SQL整合接口
    前端确认各段sql后，传回给后端，后端直接调用llm，参考各段question对应的意义，
    对各段sql进行一个拼接整合的作用。返回给前端
    """
    try:
        sql = await service.integrate_sql(request)
        if not sql:
            raise HTTPException(status_code=404, detail="未能整合SQL")
        
        return SQLIntegrationResponse(sql=sql)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"整合SQL接口失败: {e}")
        raise HTTPException(status_code=500, detail=f"整合SQL失败: {str(e)}")
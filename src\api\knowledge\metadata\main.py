"""
元数据管理系统主路由

整合所有元数据系统的API路由，提供统一的API入口。
严格参照DD系统的架构模式和实现方式。

重构后的模块化架构：
- routers/: 按功能模块组织的API路由
- models/: 请求和响应数据模型
- dependencies/: 依赖注入和通用功能
- utils/: 工具函数和辅助功能
"""

from fastapi import APIRouter
from .routers import (
    databases_router,
    tables_router,
    columns_router,
    code_sets_router,
    data_subjects_router,
    relations_router,
    templates_router,  # 新增模板管理路由
    search_router,
    system_router
)

# 创建主路由器 - 符合生产环境规范
router = APIRouter(prefix="/knowledge/metadata", tags=["元数据管理"])

# 注册所有子路由
router.include_router(databases_router)
router.include_router(tables_router)
router.include_router(columns_router)
router.include_router(code_sets_router)
router.include_router(data_subjects_router)
router.include_router(relations_router)
router.include_router(templates_router)  # 新增模板管理路由
router.include_router(search_router)
router.include_router(system_router)

# 注意：健康检查和API概览端点已移至 routers/system.py 中

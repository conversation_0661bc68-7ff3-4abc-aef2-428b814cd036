"""
DD-B并发处理器

设计支持LLM最大15并发量的处理架构，包括：
- 多个DD-B处理请求同时进行向量搜索
- 多个低置信度记录同时调用大模型生成
- 数据库连接池和向量数据库连接的并发管理
- 队列机制、限流策略和资源管理
"""

import asyncio
import time
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import deque
import weakref

from modules.dd_submission.dd_b.infrastructure.models import (
    DDBProcessRequest,
    DDBProcessResult,
    DDBRecord,
    GenerationDecision,
    ProcessingStatusEnum
)
from modules.dd_submission.dd_b.infrastructure.exceptions import (
    DDBError,
    DDBTimeoutError,
    handle_async_ddb_errors,
    create_timeout_error,
    create_processing_error
)
from .llm_generator import LLMGenerator, ConcurrencyManager

logger = logging.getLogger(__name__)


class TaskType(str, Enum):
    """任务类型枚举"""
    VECTOR_SEARCH = "vector_search"        # 向量搜索任务
    LLM_GENERATION = "llm_generation"      # 大模型生成任务
    DATA_PROCESSING = "data_processing"    # 数据处理任务


class TaskPriority(int, Enum):
    """任务优先级枚举"""
    HIGH = 1      # 高优先级
    NORMAL = 2    # 普通优先级
    LOW = 3       # 低优先级


@dataclass
class ConcurrentTask:
    """并发任务"""
    
    task_id: str                           # 任务ID
    task_type: TaskType                    # 任务类型
    priority: TaskPriority                 # 任务优先级
    
    # 任务数据
    request_data: Any                      # 请求数据
    context: Dict[str, Any] = field(default_factory=dict)  # 任务上下文
    
    # 任务状态
    created_time: float = field(default_factory=time.time)  # 创建时间
    started_time: Optional[float] = None   # 开始时间
    completed_time: Optional[float] = None # 完成时间
    
    # 任务结果
    result: Any = None                     # 任务结果
    error: Optional[Exception] = None      # 错误信息
    
    # 任务配置
    timeout_seconds: float = 30.0          # 超时时间
    max_retries: int = 3                   # 最大重试次数
    retry_count: int = 0                   # 当前重试次数
    
    def get_wait_time(self) -> float:
        """获取等待时间"""
        if self.started_time:
            return self.started_time - self.created_time
        return time.time() - self.created_time
    
    def get_execution_time(self) -> float:
        """获取执行时间"""
        if self.started_time and self.completed_time:
            return self.completed_time - self.started_time
        elif self.started_time:
            return time.time() - self.started_time
        return 0.0


@dataclass
class ResourcePool:
    """资源池配置"""
    
    # 数据库连接池配置
    max_db_connections: int = 20           # 最大数据库连接数
    db_connection_timeout: float = 10.0    # 数据库连接超时
    
    # 向量数据库连接池配置
    max_vdb_connections: int = 10          # 最大向量数据库连接数
    vdb_connection_timeout: float = 15.0   # 向量数据库连接超时
    
    # LLM并发配置
    max_llm_concurrent: int = 15           # 最大LLM并发数
    llm_timeout: float = 30.0              # LLM超时时间
    
    # 向量搜索并发配置
    max_vector_search_concurrent: int = 10 # 最大向量搜索并发数
    vector_search_timeout: float = 20.0   # 向量搜索超时时间


class TaskQueue:
    """任务队列"""
    
    def __init__(self, max_size: int = 1000):
        """
        初始化任务队列
        
        Args:
            max_size: 队列最大大小
        """
        self.max_size = max_size
        self.queues = {
            TaskPriority.HIGH: deque(),
            TaskPriority.NORMAL: deque(),
            TaskPriority.LOW: deque()
        }
        self.queue_lock = asyncio.Lock()
        self.not_empty = asyncio.Condition(self.queue_lock)
        self.not_full = asyncio.Condition(self.queue_lock)
        
        self._total_size = 0
    
    async def put(self, task: ConcurrentTask):
        """添加任务到队列"""
        async with self.not_full:
            while self._total_size >= self.max_size:
                await self.not_full.wait()
            
            self.queues[task.priority].append(task)
            self._total_size += 1
            self.not_empty.notify()
    
    async def get(self) -> ConcurrentTask:
        """从队列获取任务（按优先级）"""
        async with self.not_empty:
            while self._total_size == 0:
                await self.not_empty.wait()
            
            # 按优先级获取任务
            for priority in [TaskPriority.HIGH, TaskPriority.NORMAL, TaskPriority.LOW]:
                if self.queues[priority]:
                    task = self.queues[priority].popleft()
                    self._total_size -= 1
                    self.not_full.notify()
                    return task
            
            # 理论上不会到达这里
            raise RuntimeError("队列状态异常")
    
    def qsize(self) -> int:
        """获取队列大小"""
        return self._total_size
    
    def get_queue_stats(self) -> Dict[str, int]:
        """获取队列统计信息"""
        return {
            "total": self._total_size,
            "high_priority": len(self.queues[TaskPriority.HIGH]),
            "normal_priority": len(self.queues[TaskPriority.NORMAL]),
            "low_priority": len(self.queues[TaskPriority.LOW])
        }


class ConcurrentProcessor:
    """并发处理器"""
    
    def __init__(self, resource_pool: ResourcePool = None):
        """
        初始化并发处理器
        
        Args:
            resource_pool: 资源池配置
        """
        self.resource_pool = resource_pool or ResourcePool()
        
        # 任务队列
        self.task_queue = TaskQueue()
        
        # 并发控制
        self.llm_semaphore = asyncio.Semaphore(self.resource_pool.max_llm_concurrent)
        self.vector_search_semaphore = asyncio.Semaphore(self.resource_pool.max_vector_search_concurrent)
        self.db_semaphore = asyncio.Semaphore(self.resource_pool.max_db_connections)
        self.vdb_semaphore = asyncio.Semaphore(self.resource_pool.max_vdb_connections)
        
        # 任务管理
        self.active_tasks: Dict[str, ConcurrentTask] = {}
        self.completed_tasks: deque = deque(maxlen=1000)  # 保留最近1000个完成的任务
        
        # 统计信息
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "timeout_tasks": 0,
            "active_tasks": 0,
            "queue_size": 0
        }
        
        # 工作线程
        self.workers: List[asyncio.Task] = []
        self.is_running = False
        
        logger.info(f"并发处理器初始化完成: LLM并发={self.resource_pool.max_llm_concurrent}, "
                   f"向量搜索并发={self.resource_pool.max_vector_search_concurrent}")
    
    async def start(self, num_workers: int = 5):
        """
        启动并发处理器
        
        Args:
            num_workers: 工作线程数量
        """
        if self.is_running:
            logger.warning("并发处理器已经在运行")
            return
        
        self.is_running = True
        
        # 启动工作线程
        for i in range(num_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
        
        logger.info(f"并发处理器启动完成: {num_workers} 个工作线程")
        logger.info(f"工作线程说明: {num_workers}个异步worker负责从任务队列中取出任务并执行，"
                   f"每个worker独立运行，支持并发处理多种类型的任务（LLM生成、向量搜索、数据库操作等）")
    
    async def stop(self):
        """停止并发处理器"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 取消所有工作线程
        for worker in self.workers:
            worker.cancel()
        
        # 等待工作线程结束
        await asyncio.gather(*self.workers, return_exceptions=True)
        self.workers.clear()
        
        logger.info("并发处理器已停止")
    
    async def submit_task(self, task: ConcurrentTask) -> str:
        """
        提交任务
        
        Args:
            task: 并发任务
            
        Returns:
            任务ID
        """
        await self.task_queue.put(task)
        self.stats["total_tasks"] += 1
        self.stats["queue_size"] = self.task_queue.qsize()
        
        logger.debug(f"任务已提交: {task.task_id} ({task.task_type.value})")
        
        return task.task_id
    
    async def get_task_result(self, task_id: str, timeout: float = None) -> Any:
        """
        获取任务结果
        
        Args:
            task_id: 任务ID
            timeout: 超时时间
            
        Returns:
            任务结果
        """
        start_time = time.time()
        timeout = timeout or 60.0
        
        while time.time() - start_time < timeout:
            # 检查活跃任务
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                if task.completed_time:
                    if task.error:
                        raise task.error
                    return task.result
            
            # 检查已完成任务
            for task in self.completed_tasks:
                if task.task_id == task_id:
                    if task.error:
                        raise task.error
                    return task.result
            
            # 等待一段时间后重试
            await asyncio.sleep(0.1)
        
        raise create_timeout_error(
            f"获取任务结果超时: task_id={task_id}",
            timeout_seconds=timeout,
            operation="get_task_result"
        )
    
    async def _worker(self, worker_name: str):
        """
        工作线程
        
        Args:
            worker_name: 工作线程名称
        """
        logger.info(f"工作线程启动: {worker_name}")
        
        while self.is_running:
            try:
                # 获取任务
                task = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                
                # 执行任务
                await self._execute_task(task, worker_name)
                
            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
            except Exception as e:
                logger.error(f"工作线程 {worker_name} 发生错误: {e}")
        
        logger.info(f"工作线程停止: {worker_name}")
    
    async def _execute_task(self, task: ConcurrentTask, worker_name: str):
        """
        执行任务
        
        Args:
            task: 并发任务
            worker_name: 工作线程名称
        """
        task.started_time = time.time()
        self.active_tasks[task.task_id] = task
        self.stats["active_tasks"] = len(self.active_tasks)
        
        logger.debug(f"开始执行任务: {task.task_id} ({task.task_type.value}) by {worker_name}")
        
        try:
            # 根据任务类型选择相应的信号量
            if task.task_type == TaskType.LLM_GENERATION:
                async with self.llm_semaphore:
                    task.result = await self._execute_llm_task(task)
            elif task.task_type == TaskType.VECTOR_SEARCH:
                async with self.vector_search_semaphore:
                    task.result = await self._execute_vector_search_task(task)
            elif task.task_type == TaskType.DATA_PROCESSING:
                task.result = await self._execute_data_processing_task(task)
            else:
                raise ValueError(f"未知任务类型: {task.task_type}")
            
            # 任务完成
            task.completed_time = time.time()
            self.stats["completed_tasks"] += 1
            
            logger.debug(f"任务执行完成: {task.task_id}, "
                        f"耗时: {task.get_execution_time():.2f}s")
            
        except Exception as e:
            # 任务失败
            task.error = e
            task.completed_time = time.time()
            self.stats["failed_tasks"] += 1
            
            logger.error(f"任务执行失败: {task.task_id}, error: {e}")
        
        finally:
            # 清理任务
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            
            self.completed_tasks.append(task)
            self.stats["active_tasks"] = len(self.active_tasks)
            self.stats["queue_size"] = self.task_queue.qsize()
    
    async def _execute_llm_task(self, task: ConcurrentTask) -> Any:
        """执行LLM任务"""
        # TODO: 实现LLM任务执行逻辑
        await asyncio.sleep(0.1)  # 模拟LLM调用
        return {"generated": "content"}
    
    async def _execute_vector_search_task(self, task: ConcurrentTask) -> Any:
        """执行向量搜索任务"""
        # TODO: 实现向量搜索任务执行逻辑
        await asyncio.sleep(0.05)  # 模拟向量搜索
        return {"search_results": []}
    
    async def _execute_data_processing_task(self, task: ConcurrentTask) -> Any:
        """执行数据处理任务"""
        # TODO: 实现数据处理任务执行逻辑
        await asyncio.sleep(0.02)  # 模拟数据处理
        return {"processed": "data"}
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        queue_stats = self.task_queue.get_queue_stats()
        
        return {
            "task_stats": self.stats.copy(),
            "queue_stats": queue_stats,
            "resource_usage": {
                "llm_available": self.llm_semaphore._value,
                "llm_max": self.resource_pool.max_llm_concurrent,
                "vector_search_available": self.vector_search_semaphore._value,
                "vector_search_max": self.resource_pool.max_vector_search_concurrent,
                "db_available": self.db_semaphore._value,
                "db_max": self.resource_pool.max_db_connections,
                "vdb_available": self.vdb_semaphore._value,
                "vdb_max": self.resource_pool.max_vdb_connections
            }
        }


# 便捷函数
def create_concurrent_processor(
    max_llm_concurrent: int = 15,
    max_vector_search_concurrent: int = None,  # 默认与LLM一致
    max_db_connections: int = 20,
    max_vdb_connections: int = 10
) -> ConcurrentProcessor:
    """创建并发处理器实例"""
    # 如果未指定向量搜索并发数，则与LLM并发数一致
    if max_vector_search_concurrent is None:
        max_vector_search_concurrent = max_llm_concurrent

    resource_pool = ResourcePool(
        max_llm_concurrent=max_llm_concurrent,
        max_vector_search_concurrent=max_vector_search_concurrent,
        max_db_connections=max_db_connections,
        max_vdb_connections=max_vdb_connections
    )
    return ConcurrentProcessor(resource_pool)


async def create_and_start_processor(
    max_llm_concurrent: int = 15,
    num_workers: int = 5
) -> ConcurrentProcessor:
    """创建并启动并发处理器"""
    processor = create_concurrent_processor(max_llm_concurrent=max_llm_concurrent)
    await processor.start(num_workers)
    return processor

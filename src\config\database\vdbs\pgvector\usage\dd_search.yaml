# @package usage.dd_search
# DD内容向量搜索配置

table_name: "dd_embeddings"

search_schema:
  vector_field: "embedding"
  topk: 15
  metric_type: "cosine"
  output_fields:
    - "id"
    - "knowledge_id"
    - "value_id"
    - "field_id"
    - "field_code"
    - "field_category"
    - "data_layer"
    - "create_time"
  expr: ""
  partition_name: ""

query_schema:
  topk: 15
  expr: ""
  partition_name: ""
  output_fields:
    - "id"
    - "knowledge_id"
    - "value_id"
    - "field_id"
    - "field_code"
    - "field_category"
    - "data_layer"
    - "create_time"

# DD搜索特定配置
dd_search_config:
  # 支持的字段类别
  supported_field_categories:
    - "A"  # 结果数据需求
    - "B"  # 业务解读
    - "C"  # IT解读
    - "D"  # 指标解读
  
  # 支持的字段编码模式
  supported_field_codes:
    - "DR01"    # 数据需求
    - "BDR01"   # 业务数据需求
    - "SDR01"   # 系统数据需求
    - "IDR01"   # 指标数据需求
    # 可以根据实际DD字段扩展
  
  # 默认搜索过滤条件
  default_filters:
    # 默认搜索所有类别
    field_categories: ["A", "B", "C", "D"]
    # 可以按文件ID过滤
    file_scope_enabled: true
    # 可以按字段类别过滤
    category_scope_enabled: true
  
  # 搜索结果排序
  result_ordering:
    # 主要排序：相似度得分（降序）
    primary: "similarity_score DESC"
    # 次要排序：字段类别（升序，A->B->C->D）
    secondary: "field_category ASC"
    # 第三排序：字段编码（升序）
    tertiary: "field_code ASC"
  
  # DD文档语义搜索优化
  semantic_search_optimization:
    # 启用语义搜索
    enabled: true
    # 支持跨类别搜索
    cross_category_search: true
    # 支持按文档范围搜索
    document_scope_search: true
  
  # DD字段关联配置
  field_association_config:
    # 启用字段关联分析
    enabled: true
    # 关联分析的相似度阈值
    association_threshold: 0.6
    # 最大关联字段数量
    max_associations: 10

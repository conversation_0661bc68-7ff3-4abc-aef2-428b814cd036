from typing import Any, Dict, List, Optional, Tuple, Union, Type
from ..base.vs_client import VSClient, CollectionSchema
from ..implementations.vs.pgvector.pgvector_client import PGVectorClient


class VSInterface:
    """
    向量数据库接口，对外提供统一的数据库操作接口。
    支持多种数据库类型，根据配置选择合适的数据库客户端实现。
    """

    def __init__(self):
        """
        初始化向量数据库接口。
        注册可用的向量数据库客户端实现。
        """
        # 注册可用的向量数据库客户端实现
        self._client_registry: Dict[str, Type[VSClient]] = {
            "pgvector": PGVectorClient,
            # 可以添加更多向量数据库类型的支持
            # "milvus": MilvusClient,
            # "faiss": FAISSClient,
        }
        self.client = None

    def get_client_instance(self, db_type: str, **config) -> VSClient:
        """
        获取指定数据库类型的客户端实例。

        Args:
            db_type: 数据库类型，如 'pgvector', 'milvus', 'faiss' 等
            **config: 数据库连接配置，作为可变参数传入

        Returns:
            向量数据库客户端实例
        """
        db_type = db_type.lower()
        if db_type not in self._client_registry:
            raise ValueError(f"不支持的向量数据库类型: {db_type}")
        
        return self._client_registry[db_type](**config)

    def connect(self, db_type: str, **config) -> None:
        """
        连接到向量数据库。

        Args:
            db_type: 数据库类型
            **config: 数据库连接配置
        """
        self.client = self.get_client_instance(db_type, **config)
        self.client.connect()

    def disconnect(self) -> None:
        """
        断开与向量数据库的连接。
        """
        if self.client:
            self.client.disconnect()
            self.client = None
        else:
            raise Exception("没有活动的向量数据库连接")

    def create_collection(
        self, 
        collection_name: str, 
        schema: CollectionSchema,
        metric_type: Optional[str] = "COSINE", 
        partition_names: Optional[List[str]] = None
    ) -> bool:
        """
        创建一个新的集合（Collection）。

        Args:
            collection_name: 集合名称
            schema: 集合的模式定义，包含字段结构
            metric_type: 距离计算方式，如"L2"、"IP"（内积）、"COSINE"等，默认为"COSINE"
            partition_names: 集合的初始分区名称列表，默认为None（不创建分区）

        Returns:
            创建是否成功
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.create_collection(collection_name, schema, metric_type, partition_names)

    def drop_collection(self, collection_name: str) -> bool:
        """
        删除一个集合。

        Args:
            collection_name: 集合名称

        Returns:
            删除是否成功
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.drop_collection(collection_name)

    def has_collection(self, collection_name: str) -> bool:
        """
        检查集合是否存在。

        Args:
            collection_name: 集合名称

        Returns:
            集合是否存在
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.has_collection(collection_name)

    def list_collections(self) -> List[str]:
        """
        列出所有集合。

        Returns:
            集合名称列表
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.list_collections()
        
    def create_partition(self, collection_name: str, partition_name: str) -> bool:
        """
        在指定集合中创建一个分区。

        Args:
            collection_name: 集合名称
            partition_name: 分区名称

        Returns:
            创建是否成功
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.create_partition(collection_name, partition_name)
        
    def drop_partition(self, collection_name: str, partition_name: str) -> bool:
        """
        删除指定集合中的一个分区。

        Args:
            collection_name: 集合名称
            partition_name: 分区名称

        Returns:
            删除是否成功
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.drop_partition(collection_name, partition_name)
        
    def has_partition(self, collection_name: str, partition_name: str) -> bool:
        """
        检查指定集合中的分区是否存在。

        Args:
            collection_name: 集合名称
            partition_name: 分区名称

        Returns:
            分区是否存在
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.has_partition(collection_name, partition_name)
        
    def list_partitions(self, collection_name: str) -> List[str]:
        """
        列出指定集合中的所有分区。

        Args:
            collection_name: 集合名称

        Returns:
            分区名称列表
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.list_partitions(collection_name)

    def insert(self, collection_name: str, data: List[Dict], partition_name: Optional[str] = None) -> bool:
        """
        向指定集合中插入向量数据。

        Args:
            collection_name: 集合名称
            data: 要插入的数据，每个字典包含向量和元数据，结构应该与collection_schema一致
            partition_name: 要插入的分区名称，默认为None（插入到默认分区）

        Returns:
            插入是否成功
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.insert(collection_name, data, partition_name)

    def delete(self, collection_name: str, filter: Dict, partition_name: Optional[str] = None) -> bool:
        """
        从指定集合中删除向量数据。

        Args:
            collection_name: 集合名称
            filter: 过滤条件，用于筛选要删除的数据
            partition_name: 要删除数据的分区名称，默认为None（从所有分区删除）

        Returns:
            删除是否成功
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.delete(collection_name, filter, partition_name)

    def update(self, collection_name: str, filter: Dict, data: Dict, partition_name: Optional[str] = None) -> bool:
        """
        更新指定集合中的向量数据。

        Args:
            collection_name: 集合名称。
            filter: 过滤条件，用于筛选搜索范围
            data: 新的数据, 字典应包含要更新的字段，结构应与集合schema兼容。
            partition_name: 要更新数据的分区名称，默认为None（在默认或所有相关分区中操作）

        Returns:
            更新是否成功。
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.update(collection_name, filter, data, partition_name)

    def search(
        self, 
        collection_name: str, 
        vector: Union[List[list], list],
        top_k: int = 10, 
        metric_type: Optional[str] = None,  
        filter: Optional[Dict] = None,
        partition_names: Optional[List[str]] = None,
        search_params : Optional[Dict[str, Any]] = None
    ) -> List[List[Dict[str, Any]]]:
        """
        在指定集合中搜索与查询向量最相似的向量。

        Args:
            collection_name: 集合名称
            vector: 查询向量或向量列表
            top_k: 返回最相似的前k个结果
            metric_type: 搜索时使用的距离计算方式
            filter: 过滤条件，用于筛选搜索范围
            partition_names: 要搜索的分区名称列表，默认为None（搜索所有分区）
            search_params: 搜索参数，用于传递特定数据库的额外搜索配置

        Returns:
            搜索结果列表，每个查询向量对应一个结果列表
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.search(collection_name, vector, top_k, metric_type, filter, partition_names, search_params)

    def count(self, collection_name: str, filter: Optional[Dict] = None, partition_name: Optional[str] = None) -> int:
        """
        计算指定集合中的向量数量。

        Args:
            collection_name: 集合名称
            filter: 过滤条件
            partition_name: 要计数的分区名称，默认为None（计算所有分区）

        Returns:
            向量数量
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.count(collection_name, filter, partition_name)

    def create_index(self, collection_name: str, index_type: str, params: Dict[str, Any], partition_name: Optional[str] = None) -> bool:
        """
        为指定集合创建索引。

        Args:
            collection_name: 集合名称
            index_type: 索引类型，如"IVF_FLAT"、"HNSW"等
            params: 索引参数
            partition_name: 要创建索引的分区名称，默认为None（为整个集合创建索引）

        Returns:
            创建是否成功
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.create_index(collection_name, index_type, params, partition_name)
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """
        执行自定义查询。

        Args:
            query: 查询语句
            params: 查询参数

        Returns:
            查询结果
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        return self.client.execute_query(query, params)

    def load_collection(self, collection_name: str, partition_names: Optional[List[str]] = None) -> bool:
        """
        将集合加载到内存中以加速查询。

        Args:
            collection_name: 集合名称
            partition_names: 要加载的分区名称列表，默认为None（加载整个集合）

        Returns:
            加载是否成功
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        try:
            return self.client.load_collection(collection_name, partition_names)
        except NotImplementedError:
            print(f"警告: 当前向量数据库实现不支持load_collection操作")
            return False

    def release_collection(self, collection_name: str, partition_names: Optional[List[str]] = None) -> bool:
        """
        从内存中释放集合。

        Args:
            collection_name: 集合名称
            partition_names: 要释放的分区名称列表，默认为None（释放整个集合）

        Returns:
            释放是否成功
        """
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        try:
            return self.client.release_collection(collection_name, partition_names)
        except NotImplementedError:
            print(f"警告: 当前向量数据库实现不支持release_collection操作")
            return False

    def hybrid_search(
        self,
        collection_name: str,
        query_vectors: List[List[float]],
        query_text: Optional[str] = None,
        field_name: Optional[str] = None,
        metadata_filter: Optional[Dict[str, Any]] = None,
        top_k: int = 10,
        vector_weight: float = 0.5,
        text_weight: float = 0.5,
        partition_names: Optional[List[str]] = None
    ) -> List[List[Dict[str, Any]]]:
        """
        执行混合搜索，结合向量相似度搜索和文本/元数据过滤。

        Args:
            collection_name: 集合名称
            query_vectors: 查询向量列表
            query_text: 文本查询字符串
            field_name: 要搜索的文本字段名称
            metadata_filter: 元数据过滤条件
            top_k: 返回最相似的前k个结果
            vector_weight: 向量相似度在最终排序中的权重
            text_weight: 文本相关性在最终排序中的权重
            partition_names: 要搜索的分区名称列表，默认为None（搜索所有分区）

        Returns:
            搜索结果列表，每个查询向量对应一个结果列表
        """        
        if not self.client:
            raise Exception("没有活动的向量数据库连接，请先调用connect方法")
        try:
            return self.client.hybrid_search(
                collection_name, query_vectors, query_text, field_name, 
                metadata_filter, top_k, vector_weight, text_weight, partition_names
            )
        except NotImplementedError:
            print(f"警告: 当前向量数据库实现不支持hybrid_search操作")
            return []
            
    def get_available_db_types(self) -> List[str]:
        """
        获取所有可用的向量数据库类型
        
        Returns:
            数据库类型列表
        """
        return list(self._client_registry.keys())
    
    def register_client(self, db_type: str, client_class: Type[VSClient]) -> None:
        """
        注册新的向量数据库客户端实现
        
        Args:
            db_type: 数据库类型名称
            client_class: 数据库客户端类
        """
        self._client_registry[db_type.lower()] = client_class
        
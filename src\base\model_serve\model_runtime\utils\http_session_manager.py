"""
统一HTTP会话管理器

合并原有的EmbeddingSessionManager和LLM SessionManager，提供：
1. 统一的HTTP连接池管理
2. 不同模型类型的配置差异化支持
3. 统一的重试、超时和错误处理机制
4. 统一的生命周期管理和资源清理
5. 企业级监控和指标收集
"""

import atexit
import asyncio
import hashlib
import statistics
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from threading import Timer
from typing import Dict, Any, List, Optional, Protocol, Union, Deque
import logging

import aiohttp
import requests

logger = logging.getLogger(__name__)


@dataclass
class HTTPSessionConfig:
    """HTTP会话配置"""
    
    # 连接池配置
    limit: int = 200                    # 总连接数限制
    limit_per_host: int = 50           # 每个主机连接数限制
    keepalive_timeout: float = 6000.0    # 保活超时
    
    # 超时配置
    total_timeout: float = 6000.0        # 总超时时间 (降低到1分钟)
    sock_read_timeout: float = 3000.0    # 读取超时 (降低到30秒)
    sock_connect_timeout: float = 1000.0 # 连接超时 (降低到10秒)

    # 重试配置
    max_retries: int = 3              # 最大重试次数 (降低到2次)
    base_timeout: float = 1500.0         # 基础超时时间 (降低到15秒)
    retry_delay: float = 0.5           # 重试延迟基数
    
    # DNS和缓存配置
    use_dns_cache: bool = True         # 启用DNS缓存
    ttl_dns_cache: int = 300          # DNS缓存TTL
    enable_cleanup_closed: bool = True # 启用关闭连接清理
    
    # 清理配置
    cleanup_interval: float = 120.0    # 清理间隔（秒）
    
    # 自定义头部
    default_headers: Dict[str, str] = field(default_factory=lambda: {
        'User-Agent': 'Enterprise-HTTP-Client/1.0',
        'Connection': 'keep-alive'
    })
    
    # 模型类型特定配置
    model_type: str = "generic"        # 模型类型：embedding, llm, generic
    
    @classmethod
    def for_embedding(cls, **overrides) -> 'HTTPSessionConfig':
        """为Embedding模型创建配置"""
        # 设置默认值，但允许overrides覆盖
        defaults = {
            'limit': 500,
            'limit_per_host': 300,
            'total_timeout': 3000.0,      # 大幅降低embedding超时时间
            'sock_read_timeout': 1500.0,  # 读取超时
            'sock_connect_timeout': 5000.0, # 连接超时
            'base_timeout': 1000.0,       # 基础超时时间
            'max_retries': 3,           # 降低重试次数
            'retry_delay': 5,         # 更短的重试延迟
            'model_type': "embedding"
        }
        # 合并配置，overrides优先
        merged_config = {**defaults, **overrides}
        config = cls(**merged_config)
        config.default_headers['User-Agent'] = 'Enterprise-Embedding-Client/1.0'
        return config

    @classmethod
    def for_llm(cls, **overrides) -> 'HTTPSessionConfig':
        """为LLM模型创建配置"""
        # 设置默认值，但允许overrides覆盖
        defaults = {
            'limit': 500,           # 大幅增加总连接数
            'limit_per_host': 200,  # 大幅增加每主机连接数
            'total_timeout': 7200.0,   # 2小时总超时 (比Pipeline更宽泛)
            'base_timeout': 600.0,     # 10分钟基础超时
            'sock_read_timeout': 1800.0, # 30分钟读取超时
            'max_retries': 3,       # 减少重试次数，避免总时间过长
            'retry_delay': 1.0,     # 1秒重试延迟
            'model_type': "llm"
        }
        # 合并配置，overrides优先
        merged_config = {**defaults, **overrides}
        config = cls(**merged_config)
        config.default_headers['User-Agent'] = 'Enterprise-LLM-Client/1.0'
        return config


class HTTPMetrics:
    """HTTP指标收集器"""
    
    def __init__(self):
        self._request_count = 0
        self._error_count = 0
        self._cache_hits = 0
        self._cache_misses = 0
        self._batch_count = 0
        self._response_times: Deque[float] = deque(maxlen=1000)
        self._callback_times: Deque[float] = deque(maxlen=1000)
        self._lock = threading.Lock()
    
    def record_request(self, batch_size: int = 1, token_count: int = 0, latency: float = 0.0):
        """记录请求"""
        with self._lock:
            self._request_count += 1
            if latency > 0:
                self._response_times.append(latency)
    
    def record_error(self):
        """记录错误"""
        with self._lock:
            self._error_count += 1
    
    def record_cache_hit(self):
        """记录缓存命中"""
        with self._lock:
            self._cache_hits += 1
    
    def record_cache_miss(self):
        """记录缓存未命中"""
        with self._lock:
            self._cache_misses += 1
    
    def record_batch(self):
        """记录批处理"""
        with self._lock:
            self._batch_count += 1
    
    def record_response_time(self, duration: float):
        """记录响应时间"""
        with self._lock:
            self._response_times.append(duration)
    
    def record_callback_time(self, duration: float):
        """记录回调时间"""
        with self._lock:
            self._callback_times.append(duration)

    def update_memory_peak(self):
        """更新内存峰值（兼容方法）"""
        # 这个方法主要用于兼容性，实际的内存监控可以在这里实现
        # 目前只是一个占位符，避免调用错误
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            cache_total = self._cache_hits + self._cache_misses
            cache_hit_rate = self._cache_hits / max(1, cache_total)
            error_rate = self._error_count / max(1, self._request_count)
            
            response_times = list(self._response_times)
            callback_times = list(self._callback_times)
            
            stats = {
                'request_count': self._request_count,
                'error_count': self._error_count,
                'error_rate': error_rate,
                'cache_hits': self._cache_hits,
                'cache_misses': self._cache_misses,
                'cache_hit_rate': cache_hit_rate,
                'batch_count': self._batch_count
            }
            
            if response_times:
                stats.update({
                    'avg_response_time': statistics.mean(response_times),
                    'p95_response_time': self._percentile(response_times, 0.95),
                    'p99_response_time': self._percentile(response_times, 0.99)
                })
            
            if callback_times:
                stats.update({
                    'avg_callback_time': statistics.mean(callback_times),
                    'p95_callback_time': self._percentile(callback_times, 0.95)
                })
            
            return stats
    
    @staticmethod
    def _percentile(data: List[float], percentile: float) -> float:
        """计算百分位数"""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile)
        return sorted_data[min(index, len(sorted_data) - 1)]


class HTTPCache:
    """简单的HTTP缓存机制 - 支持LRU淘汰"""
    
    def __init__(self, max_size: int = 10000):
        self._cache = {}
        self._access_order = deque()
        self._max_size = max_size
        self._lock = threading.Lock()
    
    def _generate_key(self, text: str, model: str) -> str:
        """生成缓存键"""
        return f"{model}:{hash(text)}"
    
    def get(self, text: str, model: str) -> Optional[Any]:
        """获取缓存的结果"""
        key = self._generate_key(text, model)
        with self._lock:
            if key in self._cache:
                # 更新访问顺序
                self._access_order.remove(key)
                self._access_order.append(key)
                return self._cache[key]
        return None
    
    def put(self, text: str, model: str, result: Any):
        """存储结果到缓存"""
        key = self._generate_key(text, model)
        with self._lock:
            # 如果缓存已满，删除最久未访问的项
            if len(self._cache) >= self._max_size and key not in self._cache:
                oldest_key = self._access_order.popleft()
                del self._cache[oldest_key]
            
            # 存储新的结果
            if key not in self._cache:
                self._access_order.append(key)
            else:
                # 更新访问顺序
                self._access_order.remove(key)
                self._access_order.append(key)
            
            self._cache[key] = result
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_order.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            return {
                "cache_size": len(self._cache),
                "max_size": self._max_size,
                "usage_rate": len(self._cache) / self._max_size
            }


class HTTPConnectionManager:
    """HTTP连接管理器 - 支持重试和超时控制"""
    
    def __init__(self, config: HTTPSessionConfig):
        self.config = config
        self.max_retries = config.max_retries
        self.base_timeout = config.base_timeout
    
    async def make_request_with_retry(self, session: aiohttp.ClientSession,
                                    url: str, headers: Dict[str, str],
                                    payload: Dict[str, Any]) -> Dict[str, Any]:
        """带重试机制的异步请求方法"""
        last_exception = None

        logger.debug(f"Making HTTP request to {url}, max_retries: {self.max_retries}")

        for attempt in range(self.max_retries + 1):
            try:
                start_time = time.perf_counter()

                # 使用会话级别的超时配置，不重复设置
                # 只在需要时调整超时时间（指数退避）
                if attempt > 0:
                    # 对于重试，创建临时的超时配置
                    timeout = aiohttp.ClientTimeout(
                        total=self.base_timeout * (1.5 ** attempt),
                        sock_read=self.config.sock_read_timeout,
                        sock_connect=self.config.sock_connect_timeout
                    )
                    logger.debug(f"Retry attempt {attempt + 1} with timeout: {timeout.total}s")
                    async with session.post(url, headers=headers, json=payload, timeout=timeout) as response:
                        response.raise_for_status()
                        result = await response.json()
                        request_time = time.perf_counter() - start_time
                        logger.debug(f"Request successful on attempt {attempt + 1} in {request_time:.3f}s")
                        return result
                else:
                    # 首次请求使用会话默认超时
                    logger.debug(f"First attempt with session default timeout")
                    async with session.post(url, headers=headers, json=payload) as response:
                        response.raise_for_status()
                        result = await response.json()
                        request_time = time.perf_counter() - start_time
                        logger.debug(f"Request successful on first attempt in {request_time:.3f}s")
                        return result

            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                request_time = time.perf_counter() - start_time
                last_exception = e
                if attempt < self.max_retries:
                    wait_time = self.config.retry_delay * (2 ** attempt)  # 指数退避等待
                    logger.warning(f"HTTP request attempt {attempt + 1} failed after {request_time:.3f}s: {e}, retrying in {wait_time}s")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"All {self.max_retries + 1} HTTP request attempts failed. Last error after {request_time:.3f}s: {e}")

        raise last_exception
    
    def make_sync_request_with_retry(self, session: requests.Session, 
                                   url: str, headers: Dict[str, str], 
                                   payload: Dict[str, Any]) -> Dict[str, Any]:
        """带重试机制的同步请求方法"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                timeout = self.base_timeout * (1.5 ** attempt)
                response = session.post(url, headers=headers, json=payload, timeout=timeout)
                response.raise_for_status()
                return response.json()
                
            except requests.RequestException as e:
                last_exception = e
                if attempt < self.max_retries:
                    wait_time = self.config.retry_delay * (2 ** attempt)
                    logger.warning(f"HTTP sync request attempt {attempt + 1} failed: {e}, retrying in {wait_time}s")
                    time.sleep(wait_time)
                else:
                    logger.error(f"All {self.max_retries + 1} HTTP sync request attempts failed")
        
        raise last_exception


class HTTPSessionManager:
    """
    统一HTTP会话管理器

    合并原有的EmbeddingSessionManager和LLM SessionManager，提供：
    1. 统一的HTTP连接池管理
    2. 不同模型类型的配置差异化支持
    3. 统一的生命周期管理和资源清理
    """

    def __init__(self, config: HTTPSessionConfig, instance_id: Optional[str] = None):
        """
        初始化HTTP会话管理器

        Args:
            config: HTTP会话配置
            instance_id: 实例ID，用于标识不同的会话管理器
        """
        self.config = config
        self.instance_id = instance_id or f"http_session_{id(self)}"

        # 会话实例
        self._async_session: Optional[aiohttp.ClientSession] = None
        self._sync_session: Optional[requests.Session] = None

        # 锁和状态管理
        self._session_lock = threading.Lock()
        self._cleanup_timer: Optional[Timer] = None
        self._shutdown = False

        # 指标收集
        self._metrics = HTTPMetrics()

        logger.debug(f"HTTPSessionManager initialized: {self.instance_id}, model_type: {config.model_type}")

    @property
    def async_session(self) -> aiohttp.ClientSession:
        """获取或创建异步会话"""
        with self._session_lock:
            if self._async_session is None or self._async_session.closed:
                self._async_session = self._create_async_session()
                self._start_cleanup_timer()
                logger.debug(f"Created new async session: {self.instance_id}")

            return self._async_session

    @property
    def sync_session(self) -> requests.Session:
        """获取或创建同步会话"""
        with self._session_lock:
            if self._sync_session is None:
                self._sync_session = self._create_sync_session()
                logger.debug(f"Created new sync session: {self.instance_id}")

            return self._sync_session

    def _create_async_session(self) -> aiohttp.ClientSession:
        """创建异步会话"""
        connector = aiohttp.TCPConnector(
            limit=self.config.limit,
            limit_per_host=self.config.limit_per_host,
            keepalive_timeout=self.config.keepalive_timeout,
            enable_cleanup_closed=self.config.enable_cleanup_closed,
            use_dns_cache=self.config.use_dns_cache,
            ttl_dns_cache=self.config.ttl_dns_cache
        )

        return aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(
                total=self.config.total_timeout,
                sock_read=self.config.sock_read_timeout,
                sock_connect=self.config.sock_connect_timeout
            ),
            headers=self.config.default_headers
        )

    def _create_sync_session(self) -> requests.Session:
        """创建同步会话"""
        session = requests.Session()
        session.headers.update(self.config.default_headers)
        return session

    def _start_cleanup_timer(self):
        """启动清理定时器"""
        if self._cleanup_timer is None or not self._cleanup_timer.is_alive():
            self._cleanup_timer = Timer(self.config.cleanup_interval, self._cleanup_unused_sessions)
            self._cleanup_timer.daemon = True
            self._cleanup_timer.start()

    def _cleanup_unused_sessions(self):
        """检查并清理无效session"""
        try:
            with self._session_lock:
                if self._async_session and self._async_session.closed:
                    self._async_session = None
                    logger.debug(f"Cleaned up closed async session: {self.instance_id}")
                else:
                    logger.debug(f"Async session health check OK: {self.instance_id}")
        except Exception as e:
            logger.warning(f"Session cleanup error for {self.instance_id}: {e}")
        finally:
            # 重新启动定时器
            if not self._shutdown:
                self._start_cleanup_timer()

    def get_metrics(self) -> Dict[str, Any]:
        """获取指标统计"""
        base_stats = self._metrics.get_stats()
        base_stats.update({
            'instance_id': self.instance_id,
            'model_type': self.config.model_type,
            'session_status': {
                'async_session_active': self._async_session is not None and not self._async_session.closed,
                'sync_session_active': self._sync_session is not None
            }
        })
        return base_stats

    def get_connection_manager(self) -> HTTPConnectionManager:
        """获取连接管理器"""
        return HTTPConnectionManager(self.config)

    def cleanup(self):
        """清理资源"""
        self._shutdown = True

        try:
            with self._session_lock:
                # 停止清理定时器
                if self._cleanup_timer and self._cleanup_timer.is_alive():
                    self._cleanup_timer.cancel()

                # 清理同步会话
                if self._sync_session:
                    self._sync_session.close()
                    self._sync_session = None

                # 异步会话的清理需要在异步上下文中进行
                if self._async_session and not self._async_session.closed:
                    try:
                        # 尝试异步关闭
                        import asyncio
                        loop = asyncio.get_running_loop()
                        loop.create_task(self._async_session.close())
                        logger.debug(f"Async session cleanup task scheduled: {self.instance_id}")
                    except RuntimeError:
                        # 没有运行中的事件循环，同步强制关闭连接器
                        try:
                            if hasattr(self._async_session, '_connector') and self._async_session._connector:
                                self._async_session._connector.close()
                            logger.debug(f"Async session connector closed synchronously: {self.instance_id}")
                        except Exception:
                            pass
                    finally:
                        self._async_session = None

        except Exception as e:
            logger.warning(f"Error during session cleanup for {self.instance_id}: {e}")


# 全局会话管理器注册表
_global_session_managers: Dict[str, HTTPSessionManager] = {}
_global_lock = threading.Lock()


def get_session_manager(
    config: Optional[HTTPSessionConfig] = None,
    instance_id: Optional[str] = None,
    model_type: str = "generic"
) -> HTTPSessionManager:
    """
    获取或创建HTTP会话管理器

    Args:
        config: HTTP会话配置，如果不提供则使用默认配置
        instance_id: 实例ID，用于缓存和复用
        model_type: 模型类型，用于选择默认配置

    Returns:
        HTTPSessionManager实例
    """
    # 生成实例键
    if instance_id is None:
        config_hash = hashlib.md5(str(config).encode()).hexdigest() if config else "default"
        instance_id = f"{model_type}_{config_hash}"

    with _global_lock:
        if instance_id not in _global_session_managers:
            # 如果没有提供配置，根据模型类型创建默认配置
            if config is None:
                if model_type == "embedding":
                    config = HTTPSessionConfig.for_embedding()
                elif model_type == "llm":
                    config = HTTPSessionConfig.for_llm()
                else:
                    config = HTTPSessionConfig()

            manager = HTTPSessionManager(config, instance_id)
            _global_session_managers[instance_id] = manager
            logger.debug(f"Created new HTTPSessionManager: {instance_id}")

        return _global_session_managers[instance_id]


def cleanup_all_session_managers():
    """清理所有会话管理器"""
    global _global_session_managers

    with _global_lock:
        for instance_id, manager in _global_session_managers.items():
            try:
                manager.cleanup()
                logger.debug(f"Cleaned up session manager: {instance_id}")
            except Exception as e:
                logger.warning(f"Error cleaning up session manager {instance_id}: {e}")

        _global_session_managers.clear()


# 注册程序退出时的清理函数
atexit.register(cleanup_all_session_managers)

# Base 层实现详解

## 1. 连接池管理器 (PoolManager)

### 1.1 定义位置
`src/base/db/implementations/rdb/sqlalchemy/universal/pool_manager.py`

### 1.2 核心设计原则
1. **分离同步和异步连接池管理**：分别管理同步和异步引擎
2. **异步连接池只在异步上下文中清理**：避免事件循环问题
3. **程序退出时只清理同步连接池**：确保程序能正常退出
4. **支持依赖注入**：与service层兼容

### 1.3 核心实现

#### 1.3.1 PoolManager 类
```python
class PoolManager:
    """
    连接池管理器 - 重构版本

    移除全局单例模式，改为实例化管理：
    1. 分离同步和异步连接池管理
    2. 异步连接池只在异步上下文中清理
    3. 程序退出时只清理同步连接池
    4. 支持依赖注入，与service层兼容
    """

    def __init__(self, instance_id: Optional[str] = None):
        """
        初始化连接池管理器

        Args:
            instance_id: 实例ID，用于标识不同的池管理器实例
        """
        self.instance_id = instance_id or f"pool_manager_{id(self)}"

        # 分离同步和异步连接池
        self._sync_pools: Dict[str, Engine] = {}
        self._async_pools: Dict[str, AsyncEngine] = {}
        self._pool_refs: Dict[str, Set[weakref.ref]] = {}
        self._lock = threading.RLock()
        self._shutdown = False

        logger.debug(f"PoolManager initialized: {self.instance_id}")

        # 企业级监控：连接池指标收集（非侵入式）
        self._pool_metrics = {
            'total_connections_created': 0,
            'total_connections_closed': 0,
            'pool_creation_count': 0,
            'last_health_check': 0,
            'instance_id': self.instance_id
        }
```

#### 1.3.2 get_or_create_engines 方法
```python
def get_or_create_engines(self, config) -> Tuple[Optional[Engine], Optional[AsyncEngine]]:
    """获取或创建引擎（线程安全）"""
    if self._shutdown:
        raise RuntimeError("Pool manager is shutting down")
    
    pool_key = self._generate_pool_key(config)
    
    with self._lock:
        # 检查是否已存在
        sync_engine = self._sync_pools.get(pool_key)
        async_engine = self._async_pools.get(pool_key)

        if sync_engine and async_engine:
            logger.debug(f"Reusing existing engines for key: {pool_key}")
            return sync_engine, async_engine

        try:
            # 获取引擎参数
            engine_kwargs = config.get_engine_kwargs()
            async_engine_kwargs = config.get_async_engine_kwargs()

            # 创建同步引擎（如果不存在）
            if not sync_engine:
                sync_engine = create_engine(
                    config.build_database_url(),
                    **engine_kwargs
                )
                self._sync_pools[pool_key] = sync_engine

            # 创建异步引擎（如果不存在）
            if not async_engine:
                async_url = self._build_async_url(config)
                async_engine = create_async_engine(
                    async_url,
                    **async_engine_kwargs
                )
                self._async_pools[pool_key] = async_engine

            # 初始化引用集合
            if pool_key not in self._pool_refs:
                self._pool_refs[pool_key] = set()

            # 企业级监控：更新指标（非侵入式）
            self._pool_metrics['pool_creation_count'] += 1
            self._pool_metrics['total_connections_created'] += (
                getattr(config, 'pool_size', 20)  # 使用新的默认值
            )

            logger.info(f"Created new engines for key: {pool_key}")
            return sync_engine, async_engine
            
        except Exception as e:
            # 清理部分创建的引擎
            if sync_engine and pool_key in self._sync_pools:
                try:
                    sync_engine.dispose()
                    del self._sync_pools[pool_key]
                except Exception:
                    pass
            if async_engine and pool_key in self._async_pools:
                try:
                    # 异步引擎清理在异常情况下也可能失败，静默忽略
                    del self._async_pools[pool_key]
                except Exception:
                    pass
            raise e
```

## 2. 连接池混入类 (PooledConnectionMixin)

### 2.1 定义位置
`src/base/db/implementations/rdb/sqlalchemy/universal/pool_manager.py`

### 2.2 设计原则
1. **客户端自己管理自己的连接池**：标准方式
2. **支持可选的依赖注入**：高级用法
3. **Service层只管理客户端实例，不干预连接池**

### 2.3 核心实现
```python
class PooledConnectionMixin:
    """
    连接池混入类 - 客户端自管理连接池

    设计原则：
    - 客户端自己管理自己的连接池（标准方式）
    - 支持可选的依赖注入（高级用法）
    - Service层只管理客户端实例，不干预连接池
    """

    def __init__(self, *args, pool_manager: Optional[PoolManagerInterface] = None, **kwargs):
        """
        初始化连接池混入

        Args:
            pool_manager: 可选的池管理器实例，如果不提供则使用客户端自管理方式（推荐）
            *args, **kwargs: 传递给父类的参数
        """
        super().__init__(*args, **kwargs)

        # 优先使用注入的池管理器，否则使用客户端自管理方式（标准设计）
        self._pool_manager = pool_manager or get_global_pool_manager()
        self._client_ref = weakref.ref(self, self._cleanup_callback)
        self._pool_manager.register_client(self.config, self._client_ref)

        logger.debug(f"PooledConnectionMixin initialized with pool manager: {getattr(self._pool_manager, 'instance_id', 'unknown')}")

    def _get_shared_engines(self):
        """获取共享的引擎"""
        return self._pool_manager.get_or_create_engines(self.config)
```

## 3. 数据库客户端实现

### 3.1 UniversalSQLAlchemyClient 类
#### 3.1.1 定义位置
`src/base/db/implementations/rdb/sqlalchemy/universal/client.py`

#### 3.1.2 核心实现
```python
class UniversalSQLAlchemyClient(PooledConnectionMixin, DatabaseClient):
    """
    Universal SQLAlchemy client that works with all supported databases

    This client provides a unified interface for database operations while
    automatically adapting to the specific database dialect being used.

    Now implements the new RDB abstraction layer interface.
    """

    def __init__(self, config: UniversalConnectionConfig, pool_manager: Optional[PoolManagerInterface] = None):
        """
        Initialize the universal client with unified configuration

        Args:
            config: UniversalConnectionConfig instance
            pool_manager: 可选的池管理器实例，支持依赖注入
        """
        # 统一入口：只接受UniversalConnectionConfig
        if not isinstance(config, UniversalConnectionConfig):
            raise ValueError("config must be a UniversalConnectionConfig instance")

        # 先设置config，再调用父类初始化
        self.config = config

        # 调用父类初始化（包括PooledConnectionMixin），传入池管理器
        super().__init__(pool_manager=pool_manager)

        # 构建数据库URL
        self.database_url = self.config.build_database_url()

        # 智能检测数据库类型
        self.database_type = self._detect_database_type()
        self.dialect: DatabaseDialect = detect_dialect_from_url(self.database_url)

        # 创建适配器
        self.result_adapter = DefaultResultAdapter(self.database_type)
        self.error_adapter = DefaultErrorAdapter(self.database_type)

        # SQLAlchemy engines - 使用共享连接池
        self.sync_engine: Optional[Engine] = None
        self.async_engine: Optional[AsyncEngine] = None

        # Session factories
        self.sync_session_factory: Optional[sessionmaker] = None
        self.async_session_factory: Optional[async_sessionmaker] = None
```

### 3.2 PGVectorClient 类
#### 3.2.1 定义位置
`src/base/db/implementations/vs/pgvector/client.py`

#### 3.2.2 核心实现
```python
class PGVectorClient(VectorDatabaseClient):
    """
    全新PGVector客户端
    
    基于新架构设计，提供简洁高效的向量数据库操作接口
    完全兼容Milvus API，支持同步/异步双重操作模式
    """
    
    def __init__(self, config: VDBConnectionConfig, **kwargs):
        """
        初始化PGVector客户端
        
        Args:
            config: 数据库连接配置
            **kwargs: 额外配置参数
        """
        self.config = config
        self.kwargs = kwargs
        
        # 初始化PGVector连接管理器 - 简化架构
        self.connection_manager = PGVectorConnectionManager(config, **kwargs)
        self.collection_manager = PGVectorCollectionManager(self.connection_manager)

        # 向后兼容的别名
        self.session_manager = self.connection_manager

        # 注意：连接状态完全由PGVector连接管理器管理，客户端不维护连接状态
        
        # 性能统计
        self._stats = {
            'operations_count': 0,
            'last_operation_time': None,
            'total_operation_time': 0.0
        }
        
        logger.info(f"初始化PGVector客户端: {config.host}:{config.port}")
```

## 4. 配置类实现

### 4.1 UniversalConnectionConfig 类
#### 4.1.1 定义位置
`src/base/db/implementations/rdb/sqlalchemy/universal/config.py`

#### 4.1.2 核心实现
```python
@dataclass
class UniversalConnectionConfig(ConnectionConfig):
    """Universal database connection configuration
    
    继承ConnectionConfig，添加Universal特有的功能
    """
    
    # Universal特有的配置
    database_url: Optional[str] = None  # 支持URL和组件两种初始化方式
    dialect: Optional[str] = None  # 数据库方言（统一database_type概念）
    
    # 性能和缓存设置
    enable_cache: bool = True
    cache_size: int = 1000
    enable_query_optimization: bool = True
    
    # 异步设置
    async_fallback: bool = True
    
    # 数据库特定选项
    dialect_options: Dict[str, Any] = field(default_factory=dict)
    
    # 连接重试设置
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # SQLAlchemy引擎设置
    echo_pool: bool = False
    future: bool = True
    pool_pre_ping: bool = True

    def get_engine_kwargs(self) -> Dict[str, Any]:
        """获取SQLAlchemy引擎创建参数"""
        kwargs = {
            'echo': self.echo,
            'echo_pool': self.echo_pool,
            'future': self.future,
        }

        # SQLite不支持连接池参数
        if self.dialect and self.dialect.lower() != 'sqlite':
            kwargs.update({
                'pool_size': self.pool_size,
                'max_overflow': self.max_overflow,
                'pool_timeout': self.pool_timeout,
                'pool_recycle': self.pool_recycle,
                'pool_pre_ping': self.pool_pre_ping,
            })

        # 添加数据库特定选项
        if self.dialect_options:
            kwargs.update(self.dialect_options)

        # 移除None值
        kwargs = {k: v for k, v in kwargs.items() if v is not None}

        return kwargs
```

### 4.2 VDBConnectionConfig 类
#### 4.2.1 定义位置
`src/base/db/base/schemas.py`

#### 4.2.2 核心实现
```python
@dataclass
class VDBConnectionConfig:
    """向量数据库连接配置"""
    host: str
    port: int
    user: str
    password: str
    db_name: str
```

## 5. 使用示例

### 5.1 RDB 客户端使用
```python
# 创建UniversalSQLAlchemyClient实例
config = UniversalConnectionConfig(
    host="localhost",
    port=3306,
    database="test_db",
    username="user",
    password="password",
    pool_size=20,  # 基础连接池配置
    max_overflow=40
)

# 使用默认池管理器
client = UniversalSQLAlchemyClient(config)

# 或使用自定义池管理器
custom_pool_manager = PoolManager(instance_id="custom_pool")
client = UniversalSQLAlchemyClient(config, pool_manager=custom_pool_manager)
```

### 5.2 VDB 客户端使用
```python
# 创建PGVectorClient实例
config = VDBConnectionConfig(
    host="localhost",
    port=5432,
    user="user",
    password="password",
    db_name="vector_db"
)

client = PGVectorClient(config)
```
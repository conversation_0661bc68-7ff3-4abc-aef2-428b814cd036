"""
RDB核心数据模型

定义统一的输入输出数据模型，确保不同数据库实现的一致性
参考Django ORM、SQLAlchemy、encode/databases等优秀框架的设计

设计原则：
1. 输入输出标准化 - 统一的API接口
2. 适配器模式 - 通过转换层适配不同数据库
3. 类型安全 - 完整的类型注解
4. 验证友好 - 内置参数验证
5. 扩展性 - 支持数据库特定功能
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union, Set
from datetime import datetime
from .types import (
    DatabaseValue, DatabaseRecord, DatabaseRecords,
    SortOrder, ComparisonOperator, LogicalOperator, JoinType,
    TransactionIsolation, DatabaseType
)


# ==================== 查询过滤器模型 ====================

@dataclass
class QueryFilter:
    """查询过滤器
    
    支持各种比较操作，参考Django ORM的field lookups设计
    """
    field: str
    operator: ComparisonOperator
    value: DatabaseValue
    
    def __post_init__(self):
        """验证过滤器参数"""
        if not self.field:
            raise ValueError("Field name cannot be empty")
        
        # 验证操作符和值的匹配
        if self.operator in (ComparisonOperator.IS_NULL, ComparisonOperator.IS_NOT_NULL):
            if self.value is not None:
                raise ValueError(f"Operator {self.operator} should not have a value")
        elif self.operator in (ComparisonOperator.IN, ComparisonOperator.NOT_IN):
            if not isinstance(self.value, (list, tuple, set)):
                raise ValueError(f"Operator {self.operator} requires a list/tuple/set value")
        elif self.operator == ComparisonOperator.BETWEEN:
            if not isinstance(self.value, (list, tuple)) or len(self.value) != 2:
                raise ValueError(f"Operator {self.operator} requires a list/tuple with exactly 2 values")


@dataclass
class QueryFilterGroup:
    """查询过滤器组
    
    支持复杂的逻辑组合，参考SQLAlchemy的and_/or_设计
    """
    operator: LogicalOperator
    filters: List[Union[QueryFilter, 'QueryFilterGroup']] = field(default_factory=list)
    
    def add_filter(self, filter_item: Union[QueryFilter, 'QueryFilterGroup']) -> 'QueryFilterGroup':
        """添加过滤器"""
        self.filters.append(filter_item)
        return self
    
    def add_simple_filter(self, field: str, operator: ComparisonOperator, value: DatabaseValue) -> 'QueryFilterGroup':
        """添加简单过滤器"""
        self.filters.append(QueryFilter(field, operator, value))
        return self


# ==================== 排序模型 ====================

@dataclass
class QuerySort:
    """查询排序"""
    field: str
    order: SortOrder = SortOrder.ASC
    nulls_first: Optional[bool] = None  # NULL值排序位置
    
    def __post_init__(self):
        if not self.field:
            raise ValueError("Sort field cannot be empty")


# ==================== JOIN模型 ====================

@dataclass
class QueryJoin:
    """查询JOIN"""
    table: str
    join_type: JoinType = JoinType.INNER
    on_condition: Optional[str] = None  # JOIN条件，如 "a.id = b.user_id"
    filters: Optional[QueryFilterGroup] = None  # JOIN表的额外过滤条件
    
    def __post_init__(self):
        if not self.table:
            raise ValueError("Join table cannot be empty")


# ==================== 聚合模型 ====================

@dataclass
class QueryAggregation:
    """查询聚合"""
    function: str  # COUNT, SUM, AVG, MIN, MAX, etc.
    field: Optional[str] = None  # 聚合字段，COUNT(*)时可为None
    alias: Optional[str] = None  # 结果别名
    distinct: bool = False  # 是否DISTINCT
    
    def __post_init__(self):
        if not self.function:
            raise ValueError("Aggregation function cannot be empty")
        
        # 验证函数名
        valid_functions = {'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'GROUP_CONCAT', 'STRING_AGG'}
        if self.function.upper() not in valid_functions:
            raise ValueError(f"Unsupported aggregation function: {self.function}")


# ==================== 分组模型 ====================

@dataclass
class QueryGroupBy:
    """查询分组"""
    fields: List[str]
    having: Optional[QueryFilterGroup] = None  # HAVING条件
    
    def __post_init__(self):
        if not self.fields:
            raise ValueError("Group by fields cannot be empty")


# ==================== 查询请求模型 ====================

@dataclass
class QueryRequest:
    """统一查询请求模型
    
    参考Django QuerySet和SQLAlchemy Query的设计
    支持复杂查询的声明式构建
    """
    table: str
    
    # 基础查询参数
    columns: Optional[List[str]] = None  # 选择的列，None表示SELECT *
    filters: Optional[QueryFilterGroup] = None  # WHERE条件
    joins: Optional[List[QueryJoin]] = None  # JOIN子句
    
    # 排序和分页
    sorts: Optional[List[QuerySort]] = None  # ORDER BY
    limit: Optional[int] = None  # LIMIT
    offset: Optional[int] = None  # OFFSET
    
    # 高级查询
    distinct: bool = False  # DISTINCT
    group_by: Optional[QueryGroupBy] = None  # GROUP BY和HAVING
    aggregations: Optional[List[QueryAggregation]] = None  # 聚合函数
    
    # 查询选项
    for_update: bool = False  # SELECT ... FOR UPDATE
    timeout: Optional[float] = None  # 查询超时
    
    def __post_init__(self):
        """验证查询请求"""
        if not self.table:
            raise ValueError("Table name cannot be empty")
        
        if self.limit is not None and self.limit <= 0:
            raise ValueError("Limit must be positive")
        
        if self.offset is not None and self.offset < 0:
            raise ValueError("Offset must be non-negative")


# ==================== 插入请求模型 ====================

@dataclass
class InsertRequest:
    """插入请求模型"""
    table: str
    data: Union[DatabaseRecord, DatabaseRecords]  # 单条或多条记录
    
    # 插入选项
    on_conflict: Optional[str] = None  # 冲突处理策略：ignore, update, error
    returning: Optional[List[str]] = None  # 返回的列
    timeout: Optional[float] = None
    
    def __post_init__(self):
        if not self.table:
            raise ValueError("Table name cannot be empty")
        
        if not self.data:
            raise ValueError("Insert data cannot be empty")
        
        # 标准化数据格式
        if isinstance(self.data, dict):
            self.data = [self.data]


# ==================== 更新请求模型 ====================

@dataclass
class UpdateRequest:
    """更新请求模型"""
    table: str
    data: DatabaseRecord  # 更新的数据
    filters: QueryFilterGroup  # WHERE条件
    
    # 更新选项
    joins: Optional[List[QueryJoin]] = None  # 支持JOIN更新
    returning: Optional[List[str]] = None  # 返回的列
    timeout: Optional[float] = None
    
    def __post_init__(self):
        if not self.table:
            raise ValueError("Table name cannot be empty")
        
        if not self.data:
            raise ValueError("Update data cannot be empty")
        
        if not self.filters:
            raise ValueError("Update filters cannot be empty")


# ==================== 删除请求模型 ====================

@dataclass
class DeleteRequest:
    """删除请求模型"""
    table: str
    filters: QueryFilterGroup  # WHERE条件
    
    # 删除选项
    joins: Optional[List[QueryJoin]] = None  # 支持JOIN删除
    returning: Optional[List[str]] = None  # 返回的列
    timeout: Optional[float] = None
    
    def __post_init__(self):
        if not self.table:
            raise ValueError("Table name cannot be empty")
        
        if not self.filters:
            raise ValueError("Delete filters cannot be empty")


# ==================== 查询结果模型 ====================

@dataclass
class QueryResponse:
    """查询响应模型"""
    data: DatabaseRecords
    
    # 元数据
    total_count: Optional[int] = None  # 总记录数（用于分页）
    execution_time: Optional[float] = None  # 执行时间（秒）
    affected_rows: Optional[int] = None  # 影响的行数
    
    # 查询信息
    query_sql: Optional[str] = None  # 实际执行的SQL
    query_parameters: Optional[Dict[str, Any]] = None  # 查询参数
    
    # 数据库信息
    database_type: Optional[DatabaseType] = None
    database_version: Optional[str] = None
    
    @property
    def count(self) -> int:
        """返回的记录数"""
        return len(self.data)
    
    @property
    def is_empty(self) -> bool:
        """是否为空结果"""
        return len(self.data) == 0
    
    def first(self) -> Optional[DatabaseRecord]:
        """获取第一条记录"""
        return self.data[0] if self.data else None
    
    def last(self) -> Optional[DatabaseRecord]:
        """获取最后一条记录"""
        return self.data[-1] if self.data else None


# ==================== 操作结果模型 ====================

@dataclass
class OperationResponse:
    """操作响应模型（INSERT/UPDATE/DELETE）"""
    success: bool
    affected_rows: int = 0

    # 返回数据（如果有RETURNING子句）
    data: Optional[DatabaseRecords] = None

    # 元数据
    execution_time: Optional[float] = None
    operation_sql: Optional[str] = None
    operation_parameters: Optional[Dict[str, Any]] = None

    # 错误信息
    error_message: Optional[str] = None
    error_code: Optional[str] = None


# ==================== 连接配置模型 ====================

@dataclass
class ConnectionConfig:
    """数据库连接配置"""
    host: str
    port: int
    database: str
    username: str
    password: str
    
    # 连接选项
    charset: str = "utf8mb4"
    timezone: Optional[str] = None
    connect_timeout: float = 30.0
    read_timeout: float = 30.0
    write_timeout: float = 30.0
    
    # SSL配置
    ssl_enabled: bool = False
    ssl_cert: Optional[str] = None
    ssl_key: Optional[str] = None
    ssl_ca: Optional[str] = None
    
    # 连接池配置
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: float = 30.0
    pool_recycle: int = 3600
    
    # 其他选项
    echo: bool = False  # 是否打印SQL
    autocommit: bool = False
    isolation_level: Optional[TransactionIsolation] = None


# ==================== 导出模型 ====================

__all__ = [
    # 过滤器模型
    "QueryFilter",
    "QueryFilterGroup",
    
    # 查询组件模型
    "QuerySort",
    "QueryJoin", 
    "QueryAggregation",
    "QueryGroupBy",
    
    # 请求模型
    "QueryRequest",
    "InsertRequest",
    "UpdateRequest", 
    "DeleteRequest",
    
    # 响应模型
    "QueryResponse",
    "OperationResponse",
    
    # 配置模型
    "ConnectionConfig",
]

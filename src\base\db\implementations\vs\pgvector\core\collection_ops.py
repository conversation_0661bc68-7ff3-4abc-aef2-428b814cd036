"""
PGVector集合管理模块

统一管理PGVector数据库的集合（表）操作
基于新的核心架构设计，提供高性能的集合管理

设计原则：
1. Milvus兼容 - API与Milvus保持一致
2. 类型安全 - 完整的模式验证和类型检查
3. 性能优化 - 智能索引创建和优化
4. 错误处理 - 详细的错误信息和恢复机制
5. 扩展性 - 支持复杂的集合配置

作者: HSBC Knowledge Team
日期: 2025-01-15
"""

import logging
import time
from typing import Any, Dict, List, Optional, Set
import asyncio

from base.db.base.vdb.core import (
    CollectionSchema, FieldSchema, FieldType, IndexType,
    CollectionError, CollectionNotFoundError, CollectionAlreadyExistsError,
    SchemaError, VectorDBType
)
from ..pgvector_connection import PGVectorConnectionManager

logger = logging.getLogger(__name__)


class PGVectorCollectionManager:
    """PGVector集合管理器
    
    负责管理PGVector数据库的集合操作，包括：
    - 集合创建和删除
    - 模式定义和验证
    - 索引管理
    - 集合信息查询
    """
    
    def __init__(self, connection_manager: PGVectorConnectionManager):
        """
        初始化集合管理器

        Args:
            connection_manager: PGVector连接管理器实例
        """
        self.connection_manager = connection_manager
        # 向后兼容的别名
        self.session_manager = connection_manager
        
        # 集合操作统计
        self._operation_stats = {
            'collections_created': 0,
            'collections_dropped': 0,
            'indexes_created': 0,
            'schema_validations': 0,
            'last_operation_time': None
        }
        
        logger.debug("初始化PGVector集合管理器")
    
    # ==================== 集合基础操作 ====================
    
    def create_collection(self, collection_name: str, schema: CollectionSchema, 
                         **kwargs) -> bool:
        """创建集合（同步）
        
        Args:
            collection_name: 集合名称
            schema: 集合模式定义
            **kwargs: 额外参数
            
        Returns:
            是否创建成功
            
        Raises:
            CollectionAlreadyExistsError: 集合已存在
            SchemaError: 模式定义错误
        """
        try:
            start_time = time.time()
            logger.info(f"开始创建集合: {collection_name}")
            
            # 检查集合是否已存在
            if self.has_collection(collection_name):
                raise CollectionAlreadyExistsError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )
            
            # 验证模式
            self._validate_schema(schema)
            
            # 生成CREATE TABLE SQL
            create_sql = self._schema_to_sql(collection_name, schema)
            
            # 执行创建表
            with self.session_manager.get_cursor() as cursor:
                cursor.execute(create_sql)
                cursor.connection.commit()
            
            # 创建索引
            self._create_indexes(collection_name, schema)
            
            # 更新统计
            self._operation_stats['collections_created'] += 1
            self._operation_stats['last_operation_time'] = time.time()
            
            create_time = time.time() - start_time
            logger.info(f"集合创建成功: {collection_name}，耗时: {create_time:.3f}秒")
            
            return True
            
        except (CollectionAlreadyExistsError, SchemaError):
            # 重新抛出已知异常
            raise
        except Exception as e:
            error_msg = f"创建集合失败: {collection_name}"
            logger.error(f"{error_msg}: {e}")
            
            raise CollectionError(
                error_msg,
                collection_name=collection_name,
                database_type=VectorDBType.PGVECTOR,
                operation="create_collection",
                original_error=e
            )
    
    async def acreate_collection(self, collection_name: str, schema: CollectionSchema,
                                **kwargs) -> bool:
        """创建集合（异步）"""
        try:
            start_time = time.time()
            logger.info(f"开始异步创建集合: {collection_name}")
            
            # 检查集合是否已存在
            if await self.ahas_collection(collection_name):
                raise CollectionAlreadyExistsError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )
            
            # 验证模式
            self._validate_schema(schema)
            
            # 生成CREATE TABLE SQL
            create_sql = self._schema_to_sql(collection_name, schema)
            
            # 执行创建表
            async with self.session_manager.get_async_cursor() as conn:
                await conn.execute(create_sql)
            
            # 创建索引
            await self._acreate_indexes(collection_name, schema)
            
            # 更新统计
            self._operation_stats['collections_created'] += 1
            self._operation_stats['last_operation_time'] = time.time()
            
            create_time = time.time() - start_time
            logger.info(f"集合异步创建成功: {collection_name}，耗时: {create_time:.3f}秒")
            
            return True
            
        except (CollectionAlreadyExistsError, SchemaError):
            # 重新抛出已知异常
            raise
        except Exception as e:
            error_msg = f"异步创建集合失败: {collection_name}"
            logger.error(f"{error_msg}: {e}")
            
            raise CollectionError(
                error_msg,
                collection_name=collection_name,
                database_type=VectorDBType.PGVECTOR,
                operation="acreate_collection",
                original_error=e
            )
    
    def drop_collection(self, collection_name: str) -> bool:
        """删除集合（同步）
        
        Args:
            collection_name: 集合名称
            
        Returns:
            是否删除成功
            
        Raises:
            CollectionNotFoundError: 集合不存在
        """
        try:
            start_time = time.time()
            logger.info(f"开始删除集合: {collection_name}")
            
            # 检查集合是否存在
            if not self.has_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )
            
            # 执行删除表
            drop_sql = f"DROP TABLE IF EXISTS {collection_name} CASCADE"
            
            with self.session_manager.get_cursor() as cursor:
                cursor.execute(drop_sql)
                cursor.connection.commit()
            
            # 更新统计
            self._operation_stats['collections_dropped'] += 1
            self._operation_stats['last_operation_time'] = time.time()
            
            drop_time = time.time() - start_time
            logger.info(f"集合删除成功: {collection_name}，耗时: {drop_time:.3f}秒")
            
            return True
            
        except CollectionNotFoundError:
            # 重新抛出已知异常
            raise
        except Exception as e:
            error_msg = f"删除集合失败: {collection_name}"
            logger.error(f"{error_msg}: {e}")
            
            raise CollectionError(
                error_msg,
                collection_name=collection_name,
                database_type=VectorDBType.PGVECTOR,
                operation="drop_collection",
                original_error=e
            )
    
    async def adrop_collection(self, collection_name: str) -> bool:
        """删除集合（异步）"""
        try:
            start_time = time.time()
            logger.info(f"开始异步删除集合: {collection_name}")
            
            # 检查集合是否存在
            if not await self.ahas_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )
            
            # 执行删除表
            drop_sql = f"DROP TABLE IF EXISTS {collection_name} CASCADE"
            
            async with self.session_manager.get_async_cursor() as conn:
                await conn.execute(drop_sql)
            
            # 更新统计
            self._operation_stats['collections_dropped'] += 1
            self._operation_stats['last_operation_time'] = time.time()
            
            drop_time = time.time() - start_time
            logger.info(f"集合异步删除成功: {collection_name}，耗时: {drop_time:.3f}秒")
            
            return True
            
        except CollectionNotFoundError:
            # 重新抛出已知异常
            raise
        except Exception as e:
            error_msg = f"异步删除集合失败: {collection_name}"
            logger.error(f"{error_msg}: {e}")
            
            raise CollectionError(
                error_msg,
                collection_name=collection_name,
                database_type=VectorDBType.PGVECTOR,
                operation="adrop_collection",
                original_error=e
            )
    
    # ==================== 集合查询操作 ====================
    
    def has_collection(self, collection_name: str) -> bool:
        """检查集合是否存在（同步）
        
        Args:
            collection_name: 集合名称
            
        Returns:
            集合是否存在
        """
        try:
            sql = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = %s
            )
            """

            with self.session_manager.get_cursor() as cursor:
                cursor.execute(sql, (collection_name,))
                result = cursor.fetchone()
                logger.debug(f"has_collection查询结果: {result}, 类型: {type(result)}")
                if result:
                    # 处理RealDictRow对象
                    if hasattr(result, 'get'):
                        exists = bool(result.get('exists', False))
                    else:
                        exists = bool(result[0])
                    logger.debug(f"集合 {collection_name} 存在性: {exists}")
                    return exists
                return False

        except Exception as e:
            logger.error(f"检查集合存在性失败: {collection_name}, 错误: {e}")
            logger.debug(f"SQL查询失败的详细信息: {type(e).__name__}: {e}")
            import traceback
            logger.debug(f"完整错误堆栈: {traceback.format_exc()}")
            return False
    
    async def ahas_collection(self, collection_name: str) -> bool:
        """检查集合是否存在（异步）"""
        try:
            sql = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = $1
            )
            """
            
            async with self.session_manager.get_async_cursor() as conn:
                result = await conn.fetchval(sql, collection_name)
                return result if result is not None else False
                
        except Exception as e:
            logger.error(f"异步检查集合存在性失败: {collection_name}, 错误: {e}")
            return False

    def list_collections(self) -> List[str]:
        """列出所有集合（同步）

        Returns:
            集合名称列表
        """
        try:
            sql = """
            SELECT table_name FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
            """

            with self.session_manager.get_cursor() as cursor:
                cursor.execute(sql)
                results = cursor.fetchall()
                if results:
                    # 处理RealDictRow对象
                    if hasattr(results[0], 'get'):
                        return [row.get('table_name', '') for row in results]
                    else:
                        return [row[0] for row in results]
                return []

        except Exception as e:
            logger.error(f"列出集合失败: {e}")
            return []

    async def alist_collections(self) -> List[str]:
        """列出所有集合（异步）"""
        try:
            sql = """
            SELECT table_name FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
            """

            async with self.session_manager.get_async_cursor() as conn:
                results = await conn.fetch(sql)
                return [row[0] for row in results] if results else []

        except Exception as e:
            logger.error(f"异步列出集合失败: {e}")
            return []

    def describe_collection(self, collection_name: str) -> CollectionSchema:
        """获取集合模式（同步）

        Args:
            collection_name: 集合名称

        Returns:
            集合模式

        Raises:
            CollectionNotFoundError: 集合不存在
        """
        try:
            if not self.has_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 查询表结构和主键信息
            sql = """
            SELECT
                c.column_name,
                c.data_type,
                c.is_nullable,
                c.column_default,
                c.character_maximum_length,
                CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key
            FROM information_schema.columns c
            LEFT JOIN (
                SELECT ku.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage ku
                    ON tc.constraint_name = ku.constraint_name
                    AND tc.table_schema = ku.table_schema
                WHERE tc.constraint_type = 'PRIMARY KEY'
                    AND tc.table_schema = 'public'
                    AND tc.table_name = %s
            ) pk ON c.column_name = pk.column_name
            WHERE c.table_schema = 'public'
            AND c.table_name = %s
            ORDER BY c.ordinal_position
            """

            with self.session_manager.get_cursor() as cursor:
                cursor.execute(sql, (collection_name, collection_name))
                columns = cursor.fetchall()

            # 转换为FieldSchema列表
            fields = []
            for col in columns:
                field_schema = self._column_to_field_schema(col)
                fields.append(field_schema)

            return CollectionSchema(
                fields=fields,
                description=f"PGVector集合: {collection_name}"
            )

        except CollectionNotFoundError:
            raise
        except Exception as e:
            error_msg = f"获取集合模式失败: {collection_name}"
            logger.error(f"{error_msg}: {e}")

            raise CollectionError(
                error_msg,
                collection_name=collection_name,
                database_type=VectorDBType.PGVECTOR,
                operation="describe_collection",
                original_error=e
            )

    async def adescribe_collection(self, collection_name: str) -> CollectionSchema:
        """获取集合模式（异步）"""
        try:
            if not await self.ahas_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 查询表结构和主键信息
            sql = """
            SELECT
                c.column_name,
                c.data_type,
                c.is_nullable,
                c.column_default,
                c.character_maximum_length,
                CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key
            FROM information_schema.columns c
            LEFT JOIN (
                SELECT ku.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage ku
                    ON tc.constraint_name = ku.constraint_name
                    AND tc.table_schema = ku.table_schema
                WHERE tc.constraint_type = 'PRIMARY KEY'
                    AND tc.table_schema = 'public'
                    AND tc.table_name = $1
            ) pk ON c.column_name = pk.column_name
            WHERE c.table_schema = 'public'
            AND c.table_name = $1
            ORDER BY c.ordinal_position
            """

            async with self.session_manager.get_async_cursor() as conn:
                columns = await conn.fetch(sql, collection_name)

            # 转换为FieldSchema列表
            fields = []
            for col in columns:
                field_schema = self._column_to_field_schema(col)
                fields.append(field_schema)

            return CollectionSchema(
                fields=fields,
                description=f"PGVector集合: {collection_name}"
            )

        except CollectionNotFoundError:
            raise
        except Exception as e:
            error_msg = f"异步获取集合模式失败: {collection_name}"
            logger.error(f"{error_msg}: {e}")

            raise CollectionError(
                error_msg,
                collection_name=collection_name,
                database_type=VectorDBType.PGVECTOR,
                operation="adescribe_collection",
                original_error=e
            )

    # ==================== 索引管理 ====================

    def create_index(self, collection_name: str, field_name: str,
                    index_params: Dict[str, Any], **kwargs) -> bool:
        """创建索引（同步）

        Args:
            collection_name: 集合名称
            field_name: 字段名称
            index_params: 索引参数
            **kwargs: 额外参数

        Returns:
            是否创建成功
        """
        try:
            start_time = time.time()
            logger.info(f"开始创建索引: {collection_name}.{field_name}")

            # 检查集合是否存在
            if not self.has_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 生成索引SQL
            index_sql = self._generate_index_sql(collection_name, field_name, index_params)

            # 执行创建索引
            with self.session_manager.get_cursor() as cursor:
                cursor.execute(index_sql)
                cursor.connection.commit()

            # 更新统计
            self._operation_stats['indexes_created'] += 1
            self._operation_stats['last_operation_time'] = time.time()

            create_time = time.time() - start_time
            logger.info(f"索引创建成功: {collection_name}.{field_name}，耗时: {create_time:.3f}秒")

            return True

        except CollectionNotFoundError:
            raise
        except Exception as e:
            error_msg = f"创建索引失败: {collection_name}.{field_name}"
            logger.error(f"{error_msg}: {e}")

            raise CollectionError(
                error_msg,
                collection_name=collection_name,
                database_type=VectorDBType.PGVECTOR,
                operation="create_index",
                original_error=e
            )

    async def acreate_index(self, collection_name: str, field_name: str,
                           index_params: Dict[str, Any], **kwargs) -> bool:
        """创建索引（异步）"""
        try:
            start_time = time.time()
            logger.info(f"开始异步创建索引: {collection_name}.{field_name}")

            # 检查集合是否存在
            if not await self.ahas_collection(collection_name):
                raise CollectionNotFoundError(
                    collection_name,
                    database_type=VectorDBType.PGVECTOR
                )

            # 生成索引SQL
            index_sql = self._generate_index_sql(collection_name, field_name, index_params)

            # 执行创建索引
            async with self.session_manager.get_async_cursor() as conn:
                await conn.execute(index_sql)

            # 更新统计
            self._operation_stats['indexes_created'] += 1
            self._operation_stats['last_operation_time'] = time.time()

            create_time = time.time() - start_time
            logger.info(f"索引异步创建成功: {collection_name}.{field_name}，耗时: {create_time:.3f}秒")

            return True

        except CollectionNotFoundError:
            raise
        except Exception as e:
            error_msg = f"异步创建索引失败: {collection_name}.{field_name}"
            logger.error(f"{error_msg}: {e}")

            raise CollectionError(
                error_msg,
                collection_name=collection_name,
                database_type=VectorDBType.PGVECTOR,
                operation="acreate_index",
                original_error=e
            )

    # ==================== 内部辅助方法 ====================

    def _validate_schema(self, schema: CollectionSchema) -> None:
        """验证集合模式"""
        try:
            # 基本验证已在CollectionSchema.__post_init__中完成
            # 这里添加PGVector特定的验证

            # 验证向量字段维度
            vector_fields = schema.get_vector_fields()
            for field in vector_fields:
                if not field.dim or field.dim <= 0:
                    raise SchemaError(
                        f"向量字段 {field.name} 必须指定有效的维度",
                        database_type=VectorDBType.PGVECTOR
                    )

                if field.dim > 16000:  # PGVector限制
                    raise SchemaError(
                        f"向量字段 {field.name} 的维度 {field.dim} 超过PGVector限制(16000)",
                        database_type=VectorDBType.PGVECTOR
                    )

            # 验证字段名称
            for field in schema.fields:
                if not field.name.replace('_', '').isalnum():
                    raise SchemaError(
                        f"字段名称 {field.name} 包含无效字符",
                        database_type=VectorDBType.PGVECTOR
                    )

            self._operation_stats['schema_validations'] += 1
            logger.debug("模式验证通过")

        except SchemaError:
            raise
        except Exception as e:
            raise SchemaError(
                f"模式验证失败: {e}",
                database_type=VectorDBType.PGVECTOR,
                original_error=e
            )

    def _schema_to_sql(self, table_name: str, schema: CollectionSchema) -> str:
        """将CollectionSchema转换为CREATE TABLE SQL"""
        columns = []
        primary_key_fields = []

        for field in schema.fields:
            column_def = f'"{field.name}" '

            # 映射字段类型
            if field.dtype == FieldType.INT64:
                column_def += "BIGINT"
                # 不在这里添加PRIMARY KEY，而是收集主键字段统一处理
                if field.is_primary:
                    primary_key_fields.append(field.name)
                if field.auto_id:
                    column_def += " GENERATED ALWAYS AS IDENTITY"
            elif field.dtype == FieldType.INT32:
                column_def += "INTEGER"
                if field.is_primary:
                    primary_key_fields.append(field.name)
            elif field.dtype == FieldType.FLOAT:
                column_def += "REAL"
                if field.is_primary:
                    primary_key_fields.append(field.name)
            elif field.dtype == FieldType.DOUBLE:
                column_def += "DOUBLE PRECISION"
                if field.is_primary:
                    primary_key_fields.append(field.name)
            elif field.dtype == FieldType.BOOL:
                column_def += "BOOLEAN"
                if field.is_primary:
                    primary_key_fields.append(field.name)
            elif field.dtype == FieldType.VARCHAR:
                max_len = field.max_length or 65535
                column_def += f"VARCHAR({max_len})"
                if field.is_primary:
                    primary_key_fields.append(field.name)
            elif field.dtype == FieldType.JSON:
                column_def += "JSONB"
                if field.is_primary:
                    primary_key_fields.append(field.name)
            elif field.dtype == FieldType.FLOAT_VECTOR:
                dim = field.dim or 768
                column_def += f"VECTOR({dim})"
                # 向量字段通常不作为主键
            elif field.dtype == FieldType.BINARY_VECTOR:
                # PGVector不直接支持二进制向量，使用BIT类型
                dim = field.dim or 768
                column_def += f"BIT({dim})"
                # 向量字段通常不作为主键
            else:
                column_def += "TEXT"  # 默认类型
                if field.is_primary:
                    primary_key_fields.append(field.name)

            columns.append(column_def)

        # 添加复合主键约束
        if primary_key_fields:
            if len(primary_key_fields) == 1:
                # 单主键：直接在列定义中添加
                for i, column in enumerate(columns):
                    if f'"{primary_key_fields[0]}"' in column:
                        columns[i] = column + " PRIMARY KEY"
                        break
            else:
                # 复合主键：添加表级约束
                quoted_fields = [f'"{field}"' for field in primary_key_fields]
                primary_key_constraint = f'PRIMARY KEY ({", ".join(quoted_fields)})'
                columns.append(primary_key_constraint)

        return f'CREATE TABLE "{table_name}" ({", ".join(columns)})'

    def _create_indexes(self, collection_name: str, schema: CollectionSchema) -> None:
        """为集合创建索引（同步）"""
        try:
            # 为向量字段创建索引
            vector_fields = schema.get_vector_fields()

            with self.session_manager.get_cursor() as cursor:
                for field in vector_fields:
                    if field.dtype == FieldType.FLOAT_VECTOR:
                        # 创建IVFFlat索引
                        index_sql = f'''
                        CREATE INDEX IF NOT EXISTS "idx_{collection_name}_{field.name}"
                        ON "{collection_name}"
                        USING ivfflat ("{field.name}" vector_cosine_ops)
                        WITH (lists = 100)
                        '''
                        cursor.execute(index_sql)
                        self._operation_stats['indexes_created'] += 1

                cursor.connection.commit()

            logger.debug(f"为集合 {collection_name} 创建了 {len(vector_fields)} 个向量索引")

        except Exception as e:
            logger.warning(f"创建索引失败: {collection_name}, 错误: {e}")

    async def _acreate_indexes(self, collection_name: str, schema: CollectionSchema) -> None:
        """为集合创建索引（异步）"""
        try:
            # 为向量字段创建索引
            vector_fields = schema.get_vector_fields()

            async with self.session_manager.get_async_cursor() as conn:
                for field in vector_fields:
                    if field.dtype == FieldType.FLOAT_VECTOR:
                        # 创建IVFFlat索引
                        index_sql = f'''
                        CREATE INDEX IF NOT EXISTS "idx_{collection_name}_{field.name}"
                        ON "{collection_name}"
                        USING ivfflat ("{field.name}" vector_cosine_ops)
                        WITH (lists = 100)
                        '''
                        await conn.execute(index_sql)
                        self._operation_stats['indexes_created'] += 1

            logger.debug(f"为集合 {collection_name} 异步创建了 {len(vector_fields)} 个向量索引")

        except Exception as e:
            logger.warning(f"异步创建索引失败: {collection_name}, 错误: {e}")

    def _generate_index_sql(self, collection_name: str, field_name: str,
                           index_params: Dict[str, Any]) -> str:
        """生成索引SQL"""
        index_type = index_params.get('index_type', 'ivfflat')
        metric_type = index_params.get('metric_type', 'cosine')

        # 映射度量类型到PGVector操作符
        ops_mapping = {
            'cosine': 'vector_cosine_ops',
            'l2': 'vector_l2_ops',
            'ip': 'vector_ip_ops'
        }

        ops = ops_mapping.get(metric_type.lower(), 'vector_cosine_ops')
        index_name = f"idx_{collection_name}_{field_name}_{index_type}"

        if index_type.lower() == 'ivfflat':
            lists = index_params.get('lists', 100)
            return f'''
            CREATE INDEX IF NOT EXISTS "{index_name}"
            ON "{collection_name}"
            USING ivfflat ("{field_name}" {ops})
            WITH (lists = {lists})
            '''
        elif index_type.lower() == 'hnsw':
            m = index_params.get('m', 16)
            ef_construction = index_params.get('ef_construction', 64)
            return f'''
            CREATE INDEX IF NOT EXISTS "{index_name}"
            ON "{collection_name}"
            USING hnsw ("{field_name}" {ops})
            WITH (m = {m}, ef_construction = {ef_construction})
            '''
        else:
            # 默认使用IVFFlat
            return f'''
            CREATE INDEX IF NOT EXISTS "{index_name}"
            ON "{collection_name}"
            USING ivfflat ("{field_name}" {ops})
            WITH (lists = 100)
            '''

    def _column_to_field_schema(self, column_info) -> FieldSchema:
        """将数据库列信息转换为FieldSchema"""
        # 处理RealDictRow对象或tuple
        if hasattr(column_info, 'get'):
            col_name = column_info.get('column_name', '')
            data_type = column_info.get('data_type', '')
            is_nullable = column_info.get('is_nullable', 'YES')
            col_default = column_info.get('column_default')
            max_length = column_info.get('character_maximum_length')
            is_primary_key = column_info.get('is_primary_key', False)
        else:
            col_name, data_type, is_nullable, col_default, max_length, is_primary_key = column_info

        # 映射PostgreSQL类型到FieldType
        type_mapping = {
            'bigint': FieldType.INT64,
            'integer': FieldType.INT32,
            'real': FieldType.FLOAT,
            'double precision': FieldType.DOUBLE,
            'boolean': FieldType.BOOL,
            'character varying': FieldType.VARCHAR,
            'varchar': FieldType.VARCHAR,
            'text': FieldType.VARCHAR,
            'jsonb': FieldType.JSON,
            'json': FieldType.JSON
        }

        # 检查是否是向量类型
        if 'vector' in data_type.lower() or data_type.lower() == 'user-defined':
            # 对于USER-DEFINED类型，需要进一步检查是否是向量
            if data_type.lower() == 'user-defined':
                # 假设USER-DEFINED类型的字段名包含embedding、vector等关键词就是向量字段
                if any(keyword in col_name.lower() for keyword in ['embedding', 'vector', 'emb']):
                    field_type = FieldType.FLOAT_VECTOR
                    dim = 768  # 无法从类型信息中获取维度，使用默认值768
                else:
                    field_type = FieldType.VARCHAR
                    dim = None
            else:
                field_type = FieldType.FLOAT_VECTOR
                # 从类型中提取维度，如 vector(768)
                import re
                dim_match = re.search(r'vector\((\d+)\)', data_type.lower())
                dim = int(dim_match.group(1)) if dim_match else None
        else:
            field_type = type_mapping.get(data_type.lower(), FieldType.VARCHAR)
            dim = None

        # 使用查询结果中的主键信息
        is_primary = bool(is_primary_key)
        auto_id = is_primary and ('nextval' in (col_default or '') or 'identity' in (col_default or ''))

        return FieldSchema(
            name=col_name,
            dtype=field_type,
            is_primary=is_primary,
            auto_id=auto_id,
            max_length=max_length,
            dim=dim
        )

    # ==================== 统计和监控 ====================

    def get_operation_stats(self) -> Dict[str, Any]:
        """获取操作统计信息"""
        return self._operation_stats.copy()

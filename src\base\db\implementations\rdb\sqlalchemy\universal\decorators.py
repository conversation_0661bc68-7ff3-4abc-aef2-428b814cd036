"""
装饰器模块

企业级最佳实践：
1. 自动请求转换装饰器
2. 性能监控装饰器
3. 错误处理装饰器
4. 连接管理装饰器
"""

import time
import asyncio
from functools import wraps
from typing import Callable, Any, Union, Dict
import logging

logger = logging.getLogger(__name__)

from .adapters import get_request_adapter
from .....base.rdb import (
    QueryRequest, InsertRequest, UpdateRequest, DeleteRequest
)


def auto_convert_query(func: Callable) -> Callable:
    """自动转换查询请求装饰器"""
    @wraps(func)
    def wrapper(self, request: Union[QueryRequest, Dict[str, Any]], *args, **kwargs):
        adapter = get_request_adapter()
        request = adapter.adapt_query(request)
        return func(self, request, *args, **kwargs)
    
    @wraps(func)
    async def async_wrapper(self, request: Union[QueryRequest, Dict[str, Any]], *args, **kwargs):
        adapter = get_request_adapter()
        request = adapter.adapt_query(request)
        return await func(self, request, *args, **kwargs)
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else wrapper


def auto_convert_insert(func: Callable) -> Callable:
    """自动转换插入请求装饰器"""
    @wraps(func)
    def wrapper(self, request: Union[InsertRequest, Dict[str, Any]], *args, **kwargs):
        adapter = get_request_adapter()
        request = adapter.adapt_insert(request)
        return func(self, request, *args, **kwargs)
    
    @wraps(func)
    async def async_wrapper(self, request: Union[InsertRequest, Dict[str, Any]], *args, **kwargs):
        adapter = get_request_adapter()
        request = adapter.adapt_insert(request)
        return await func(self, request, *args, **kwargs)
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else wrapper


def auto_convert_update(func: Callable) -> Callable:
    """自动转换更新请求装饰器"""
    @wraps(func)
    def wrapper(self, request: Union[UpdateRequest, Dict[str, Any]], *args, **kwargs):
        adapter = get_request_adapter()
        request = adapter.adapt_update(request)
        return func(self, request, *args, **kwargs)
    
    @wraps(func)
    async def async_wrapper(self, request: Union[UpdateRequest, Dict[str, Any]], *args, **kwargs):
        adapter = get_request_adapter()
        request = adapter.adapt_update(request)
        return await func(self, request, *args, **kwargs)
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else wrapper


def auto_convert_delete(func: Callable) -> Callable:
    """自动转换删除请求装饰器"""
    @wraps(func)
    def wrapper(self, request: Union[DeleteRequest, Dict[str, Any]], *args, **kwargs):
        adapter = get_request_adapter()
        request = adapter.adapt_delete(request)
        return func(self, request, *args, **kwargs)
    
    @wraps(func)
    async def async_wrapper(self, request: Union[DeleteRequest, Dict[str, Any]], *args, **kwargs):
        adapter = get_request_adapter()
        request = adapter.adapt_delete(request)
        return await func(self, request, *args, **kwargs)
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else wrapper


def ensure_sync_connection(func: Callable) -> Callable:
    """确保同步连接装饰器"""
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        self._ensure_sync_engine()
        return func(self, *args, **kwargs)
    return wrapper


def ensure_async_connection(func: Callable) -> Callable:
    """确保异步连接装饰器"""
    @wraps(func)
    async def wrapper(self, *args, **kwargs):
        await self._ensure_async_engine()
        return await func(self, *args, **kwargs)
    return wrapper


def performance_monitor(operation_name: str = None):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        op_name = operation_name or func.__name__
        
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            start_time = time.time()
            try:
                result = func(self, *args, **kwargs)
                execution_time = time.time() - start_time
                logger.debug(f"{op_name} completed in {execution_time:.3f}s")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"{op_name} failed after {execution_time:.3f}s: {e}")
                raise
        
        @wraps(func)
        async def async_wrapper(self, *args, **kwargs):
            start_time = time.time()
            try:
                result = await func(self, *args, **kwargs)
                execution_time = time.time() - start_time
                logger.debug(f"{op_name} completed in {execution_time:.3f}s")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"{op_name} failed after {execution_time:.3f}s: {e}")
                raise
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else wrapper
    return decorator


def error_handler(operation_name: str = None):
    """错误处理装饰器"""
    def decorator(func: Callable) -> Callable:
        op_name = operation_name or func.__name__
        
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            try:
                return func(self, *args, **kwargs)
            except Exception as e:
                # 使用现有的错误适配器
                if hasattr(self, 'error_adapter'):
                    adapted_error = self.error_adapter.adapt_error(e, {
                        "operation": op_name,
                        "args": str(args),
                        "kwargs": str(kwargs)
                    })
                    raise adapted_error
                raise
        
        @wraps(func)
        async def async_wrapper(self, *args, **kwargs):
            try:
                return await func(self, *args, **kwargs)
            except Exception as e:
                # 使用现有的错误适配器
                if hasattr(self, 'error_adapter'):
                    adapted_error = self.error_adapter.adapt_error(e, {
                        "operation": op_name,
                        "args": str(args),
                        "kwargs": str(kwargs)
                    })
                    raise adapted_error
                raise
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else wrapper
    return decorator


def auto_convert_batch_insert(func: Callable) -> Callable:
    """自动转换批量插入请求装饰器"""
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        # 支持多种调用方式
        if len(args) >= 1:
            # 方式1: batch_insert(request, ...)
            # 方式2: batch_insert({"table": "...", "data": [...]}, ...)
            first_arg = args[0]
            if isinstance(first_arg, dict):
                adapter = get_request_adapter()
                request = adapter.adapt_insert(first_arg)
                new_args = (request,) + args[1:]
                return func(self, *new_args, **kwargs)
            else:
                return func(self, *args, **kwargs)
        elif 'table' in kwargs and 'data' in kwargs:
            # 方式3: batch_insert(table="...", data=[...], ...)
            request_dict = {
                'table': kwargs.pop('table'),
                'data': kwargs.pop('data')
            }
            adapter = get_request_adapter()
            request = adapter.adapt_insert(request_dict)
            return func(self, request, **kwargs)
        else:
            return func(self, *args, **kwargs)

    @wraps(func)
    async def async_wrapper(self, *args, **kwargs):
        # 支持多种调用方式
        if len(args) >= 1:
            # 方式1: batch_insert(request, ...)
            # 方式2: batch_insert({"table": "...", "data": [...]}, ...)
            first_arg = args[0]
            if isinstance(first_arg, dict):
                adapter = get_request_adapter()
                request = adapter.adapt_insert(first_arg)
                new_args = (request,) + args[1:]
                return await func(self, *new_args, **kwargs)
            else:
                return await func(self, *args, **kwargs)
        elif 'table' in kwargs and 'data' in kwargs:
            # 方式3: batch_insert(table="...", data=[...], ...)
            request_dict = {
                'table': kwargs.pop('table'),
                'data': kwargs.pop('data')
            }
            adapter = get_request_adapter()
            request = adapter.adapt_insert(request_dict)
            return await func(self, request, **kwargs)
        else:
            return await func(self, *args, **kwargs)

    return async_wrapper if asyncio.iscoroutinefunction(func) else wrapper


# 组合装饰器：同时应用多个装饰器
def database_operation(operation_type: str, monitor: bool = True, handle_errors: bool = True):
    """
    数据库操作组合装饰器
    
    Args:
        operation_type: 操作类型 ('query', 'insert', 'update', 'delete')
        monitor: 是否启用性能监控
        handle_errors: 是否启用错误处理
    """
    def decorator(func: Callable) -> Callable:
        # 根据操作类型选择转换装饰器
        conversion_decorators = {
            'query': auto_convert_query,
            'insert': auto_convert_insert,
            'update': auto_convert_update,
            'delete': auto_convert_delete
        }
        
        # 应用装饰器
        decorated_func = func
        
        # 1. 首先应用转换装饰器
        if operation_type in conversion_decorators:
            decorated_func = conversion_decorators[operation_type](decorated_func)
        
        # 2. 应用连接管理装饰器
        if asyncio.iscoroutinefunction(func):
            decorated_func = ensure_async_connection(decorated_func)
        else:
            decorated_func = ensure_sync_connection(decorated_func)
        
        # 3. 应用性能监控装饰器
        if monitor:
            decorated_func = performance_monitor(operation_type)(decorated_func)
        
        # 4. 应用错误处理装饰器
        if handle_errors:
            decorated_func = error_handler(operation_type)(decorated_func)
        
        return decorated_func
    
    return decorator

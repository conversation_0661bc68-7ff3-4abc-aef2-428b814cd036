"""
Configuration Manager

统一配置管理器 - 支持静态和动态配置的统一接口

Author: AI Assistant
Created: 2025-07-11
"""

import asyncio
import logging
import threading
from typing import Any, Dict, Optional, Union
from omegaconf import DictConfig, OmegaConf

from .loader import Config<PERSON>oa<PERSON>, HydraLoader, DatabaseLoader
from .validator import ConfigValidator
from ..exceptions import ConfigError

logger = logging.getLogger(__name__)


class ConfigManager:
    """
    统一配置管理器 - 单例模式

    提供统一的配置接口，支持：
    - Hydra静态配置加载
    - 数据库动态配置加载
    - 混合配置模式
    - 配置验证和缓存
    - 线程安全的单例模式
    """
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        """单例模式实现 - 线程安全"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(Config<PERSON>ana<PERSON>, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化配置管理器 - 单例模式下只初始化一次"""
        # 防止重复初始化 - 使用object.__getattribute__避免递归
        try:
            initialized = object.__getattribute__(self, '_initialized')
            if initialized:
                return
        except AttributeError:
            # 第一次初始化，_initialized属性不存在
            pass

        self._config: Optional[DictConfig] = None
        self._loader: Optional[HydraLoader] = None
        self._validator = ConfigValidator()
        self._source: Optional[str] = None
        self._initialized = False
    
    async def initialize(self, source: str = "mixed", **kwargs):
        """
        初始化配置管理器

        Args:
            source: 配置源 ("hydra", "database", "mixed") - 默认mixed支持动态+静态
            **kwargs: 额外参数
        """
        if self._initialized:
            logger.warning("配置管理器已经初始化")
            return

        self._source = source

        try:
            # 总是先加载Hydra配置作为基础
            self._loader = HydraLoader()

            # 加载配置
            await self._load_config(**kwargs)

            # 验证配置
            await self._validate_config()

            self._initialized = True
            logger.info(f"配置管理器初始化完成，配置源: {source}")

        except Exception as e:
            logger.error(f"配置管理器初始化失败: {e}")
            raise ConfigError(f"Failed to initialize config manager: {e}") from e
    
    async def _load_config(self, **kwargs):
        """加载配置 - 总是先加载Hydra，支持动态数据库配置"""
        if not self._loader:
            raise ConfigError("配置加载器未初始化")

        # 总是先加载Hydra配置作为基础
        self._config = await self._loader.load(**kwargs)
        logger.info("Hydra静态配置加载完成")

        # 数据库动态配置将在运行时按需加载，这里不预加载
    
    async def _validate_config(self):
        """验证配置"""
        if not self._config:
            raise ConfigError("配置为空")
        
        try:
            await self._validator.validate(self._config)
            logger.debug("配置验证通过")
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            raise ConfigError(f"Config validation failed: {e}") from e
    
    def get(self, path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            path: 配置路径，如 "database.rdbs.mysql.host"
            default: 默认值
            
        Returns:
            配置值
        """
        if not self._config:
            raise ConfigError("配置未初始化")
        
        try:
            return OmegaConf.select(self._config, path, default=default)
        except Exception as e:
            logger.error(f"获取配置失败，路径: {path}, 错误: {e}")
            return default
    
    def set(self, path: str, value: Any):
        """
        设置配置值
        
        Args:
            path: 配置路径
            value: 配置值
        """
        if not self._config:
            raise ConfigError("配置未初始化")
        
        try:
            OmegaConf.set(self._config, path, value)
            logger.debug(f"设置配置成功，路径: {path}")
        except Exception as e:
            logger.error(f"设置配置失败，路径: {path}, 错误: {e}")
            raise ConfigError(f"Failed to set config: {e}") from e
    
    def has(self, path: str) -> bool:
        """
        检查配置路径是否存在
        
        Args:
            path: 配置路径
            
        Returns:
            是否存在
        """
        if not self._config:
            return False
        
        return OmegaConf.select(self._config, path) is not None
    
    @property
    def config(self) -> DictConfig:
        """获取完整配置对象"""
        if not self._config:
            raise ConfigError("配置未初始化")
        return self._config
    
    @property
    def database(self) -> DictConfig:
        """获取数据库配置"""
        return self.get("database", {})
    
    @property
    def llm(self) -> DictConfig:
        """获取大模型配置"""
        return self.get("llm", {})
    
    @property
    def embedding(self) -> DictConfig:
        """获取嵌入模型配置"""
        return self.get("embedding", {})
    
    async def load_dynamic_config(self, **dynamic_params) -> DictConfig:
        """
        动态加载配置

        Args:
            **dynamic_params: 动态参数，可以是任意键值对
                例如: knowledge_id, user_id, tenant_id, environment 等

        Returns:
            动态配置对象
        """
        try:
            from .loader import DatabaseLoader
            db_loader = DatabaseLoader()

            # 传递所有动态参数给数据库加载器
            db_config = await db_loader.load(**dynamic_params)

            # 与静态配置合并（数据库配置优先）
            merged_config = OmegaConf.merge(self._config, db_config)

            # 记录动态参数
            params_str = ", ".join(f"{k}={v}" for k, v in dynamic_params.items())
            logger.info(f"动态配置加载完成，参数: {params_str}")
            return merged_config

        except Exception as e:
            logger.warning(f"动态配置加载失败，使用静态配置: {e}")
            return self._config

    async def get_database_config(self, db_config: DictConfig, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        通用数据库配置查询方法

        Args:
            db_config: 数据库连接配置，包含查询配置信息
            params: 查询参数，用于WHERE条件，例如:
                   {"type": "mysql", "tenant": "hsbc", "env": "prod"}

        Returns:
            Dict[str, Any]: 数据库查询结果的原始dict

        Note:
            这是一个完全通用的方法，不假设任何表结构或字段名称。
            所有的查询逻辑都通过db_config配置驱动。

        Example db_config structure:
            ```yaml
            my_config_db:
              # 数据库连接配置（任意数据库类型）
              _target_: base.db.implementations.rdb.universal.factory.create_mysql_client
              host: "your-config-db.company.com"
              port: 3306
              database: "your_config_database"
              username: "config_reader"
              password: "secure_password"

              # 查询配置（完全自定义）
              query_config:
                # SQL模板 - 用户完全控制查询逻辑
                sql_template: |
                  SELECT
                    your_key_field as config_key,
                    your_value_field as config_value,
                    your_env_field as environment,
                    your_tenant_field as tenant_id
                  FROM your_config_table
                  WHERE your_type_field = %(config_type)s
                    AND your_subtype_field = %(config_subtype)s
                    AND your_tenant_field = %(tenant_id)s
                    AND your_env_field = %(environment)s
                    AND your_status_field = %(status)s
                  ORDER BY your_priority_field ASC
                  LIMIT 1

                # 默认参数（可选）
                default_params:
                  status: "active"
                  environment: "prod"
            ```

        Example usage:
            ```python
            # 查询参数
            params = {
                "config_type": "database",
                "config_subtype": "mysql",
                "tenant_id": "hsbc",
                "environment": "production"
            }

            # 执行查询
            raw_config = await cfg.get_database_config(cfg.my_config_db, params)

            # raw_config 将包含您数据库表中的原始行数据，例如:
            # {
            #     "config_key": "database.rdbs.mysql",
            #     "config_value": "{\"host\": \"prod-mysql.com\", \"port\": 3306}",
            #     "environment": "production",
            #     "tenant_id": "hsbc"
            # }
            ```

        Flexibility features:
            - 支持任意数据库类型（MySQL, PostgreSQL, Oracle, SQLite等）
            - 支持任意表结构和字段命名
            - 支持复杂SQL查询（多表关联、JSON查询、子查询等）
            - 支持任意查询参数组合
            - 完全由用户配置驱动，框架不做任何假设
        """
        try:
            # 1. 获取数据库客户端
            from .. import get_client
            db_client = await get_client(db_config)

            # 2. 从db_config获取查询配置
            query_config = getattr(db_config, 'query_config', None)
            if not query_config:
                raise ConfigError("数据库配置中缺少query_config查询配置")

            # 3. 构建查询SQL（完全由配置驱动）
            sql_template = getattr(query_config, 'sql_template', None)
            if not sql_template:
                raise ConfigError("查询配置中缺少sql_template")

            # 4. 处理查询参数
            query_params = dict(params)  # 复制参数

            # 添加默认参数（如果配置中定义了）
            default_params = getattr(query_config, 'default_params', {})
            for key, value in default_params.items():
                if key not in query_params:
                    query_params[key] = value

            # 5. 执行查询
            logger.debug(f"执行动态配置查询，模板: {sql_template}")
            logger.debug(f"查询参数: {query_params}")

            if hasattr(db_client, 'execute_query'):
                if asyncio.iscoroutinefunction(db_client.execute_query):
                    result = await db_client.execute_query(sql_template, query_params)
                else:
                    result = db_client.execute_query(sql_template, query_params)
            else:
                raise ConfigError("数据库客户端不支持execute_query方法")

            # 6. 处理查询结果
            if result and len(result) > 0:
                config_row = result[0]
                logger.info(f"找到动态配置，参数: {params}")
                return config_row
            else:
                logger.warning(f"未找到匹配的动态配置，参数: {params}")
                return None

        except Exception as e:
            logger.error(f"动态配置查询失败: {e}")
            raise ConfigError(f"Failed to query dynamic config: {e}") from e

    def transform_dynamic_config(self, raw_config: Dict[str, Any],
                               transform_func: Optional[callable] = None) -> Dict[str, Any]:
        """
        转换动态配置格式

        Args:
            raw_config: 从数据库查询的原始配置dict
            transform_func: 自定义转换函数，如果不提供则使用默认转换

        Returns:
            Dict[str, Any]: 转换后的配置dict，符合静态配置的层级结构

        Example raw_config (from your database):
            ```python
            raw_config = {
                "config_key": "database.rdbs.mysql",  # 或您的字段名
                "config_value": "{\"host\": \"prod-mysql.com\", \"port\": 3306}",  # 或您的字段名
                "environment": "production",
                "tenant_id": "hsbc",
                "priority": 10
            }
            ```

        Example custom transform_func:
            ```python
            def my_custom_transform(raw_config):
                import json

                # 从您的字段名中提取数据
                config_key = raw_config.get('your_key_field', '')
                config_data = raw_config.get('your_value_field', '{}')

                # 解析配置数据（可能是JSON、XML、键值对等任意格式）
                if isinstance(config_data, str):
                    try:
                        parsed_data = json.loads(config_data)
                    except:
                        # 处理其他格式，如键值对: "host=mysql.com;port=3306"
                        parsed_data = {}
                        for pair in config_data.split(';'):
                            if '=' in pair:
                                k, v = pair.split('=', 1)
                                parsed_data[k.strip()] = v.strip()
                else:
                    parsed_data = config_data

                # 构建您需要的层级结构
                if config_key.startswith('database.'):
                    parts = config_key.split('.')
                    return {
                        "database": {
                            parts[1]: {  # rdbs, vdbs等
                                parts[2]: {  # mysql, pgvector等
                                    **parsed_data,
                                    # 添加您需要的元数据
                                    "_metadata": {
                                        "source": "your_config_system",
                                        "tenant": raw_config.get('your_tenant_field'),
                                        "environment": raw_config.get('your_env_field'),
                                        "priority": raw_config.get('your_priority_field')
                                    }
                                }
                            }
                        }
                    }

                # 处理其他配置类型...
                return {"custom": {config_key: parsed_data}}
            ```

        Example usage:
            ```python
            # 使用默认转换（假设标准字段名）
            transformed = cfg.transform_dynamic_config(raw_config)

            # 使用自定义转换函数
            transformed = cfg.transform_dynamic_config(raw_config, my_custom_transform)

            # 结果示例:
            # {
            #     "database": {
            #         "rdbs": {
            #             "mysql": {
            #                 "host": "prod-mysql.com",
            #                 "port": 3306,
            #                 "_metadata": {
            #                     "source": "your_config_system",
            #                     "tenant": "hsbc",
            #                     "environment": "production"
            #                 }
            #             }
            #         }
            #     }
            # }
            ```

        Flexibility features:
            - 支持任意原始数据格式（JSON、XML、键值对、固定长度字符串等）
            - 支持任意字段命名（您的数据库字段名）
            - 支持任意层级结构构建
            - 支持添加自定义元数据
            - 完全由用户控制转换逻辑
        """
        try:
            if not raw_config:
                return {}

            # 如果提供了自定义转换函数，使用自定义转换
            if transform_func and callable(transform_func):
                return transform_func(raw_config)

            # 默认转换逻辑
            return self._default_transform(raw_config)

        except Exception as e:
            logger.error(f"配置转换失败: {e}")
            raise ConfigError(f"Failed to transform config: {e}") from e

    def _default_transform(self, raw_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        默认配置转换逻辑

        Args:
            raw_config: 原始配置，包含config_key和config_value字段

        Returns:
            Dict[str, Any]: 层级结构的配置dict
        """
        import json

        config_key = raw_config.get('config_key', '')
        config_value = raw_config.get('config_value', '{}')

        # 解析JSON配置值
        try:
            if isinstance(config_value, str):
                parsed_value = json.loads(config_value)
            else:
                parsed_value = config_value
        except json.JSONDecodeError as e:
            logger.error(f"配置值JSON解析失败: {e}")
            parsed_value = {}

        # 根据config_key构建层级结构
        # 例如: "database.rdbs.mysql" -> {"database": {"rdbs": {"mysql": {...}}}}
        keys = config_key.split('.')
        result = {}
        current = result

        for i, key in enumerate(keys):
            if i == len(keys) - 1:
                # 最后一级，设置实际值
                current[key] = parsed_value
            else:
                # 中间级，创建嵌套dict
                current[key] = {}
                current = current[key]

        return result

    async def merge_configs(self, static_config: DictConfig,
                          dynamic_config_dict: Dict[str, Any]) -> DictConfig:
        """
        合并静态配置和动态配置

        Args:
            static_config: 静态配置（Hydra加载）
            dynamic_config_dict: 动态配置dict（已转换格式）

        Returns:
            DictConfig: 合并后的配置

        Example static_config (from Hydra YAML):
            ```yaml
            database:
              rdbs:
                mysql:
                  _target_: base.db.implementations.rdb.universal.factory.create_mysql_client
                  host: "default-mysql.com"
                  port: 3306
                  database: "default_db"
                  username: "default_user"
                  password: "default_pass"
                  pool_params:
                    size: 10
                    max_overflow: 20
            ```

        Example dynamic_config_dict (from transform_dynamic_config):
            ```python
            dynamic_config_dict = {
                "database": {
                    "rdbs": {
                        "mysql": {
                            "host": "prod-mysql.company.com",
                            "port": 3307,
                            "database": "prod_database",
                            "pool_params": {
                                "size": 50,
                                "max_overflow": 100
                            },
                            "_metadata": {
                                "source": "enterprise_config_center",
                                "tenant": "hsbc",
                                "environment": "production"
                            }
                        }
                    }
                }
            }
            ```

        Merge result:
            ```python
            merged_config = {
                "database": {
                    "rdbs": {
                        "mysql": {
                            "_target_": "base.db.implementations.rdb.universal.factory.create_mysql_client",
                            "host": "prod-mysql.company.com",  # 动态配置覆盖
                            "port": 3307,                      # 动态配置覆盖
                            "database": "prod_database",       # 动态配置覆盖
                            "username": "default_user",        # 静态配置保留
                            "password": "default_pass",        # 静态配置保留
                            "pool_params": {
                                "size": 50,                    # 动态配置覆盖
                                "max_overflow": 100            # 动态配置覆盖
                            },
                            "_metadata": {                     # 动态配置新增
                                "source": "enterprise_config_center",
                                "tenant": "hsbc",
                                "environment": "production"
                            }
                        }
                    }
                }
            }
            ```

        Merge strategy:
            - **覆盖合并**: 动态配置的同名字段完全覆盖静态配置
            - **深度合并**: 嵌套对象进行递归合并
            - **保留静态**: 动态配置中没有的字段保留静态配置的值
            - **新增字段**: 动态配置可以添加静态配置中没有的新字段

        Example usage:
            ```python
            # 完整流程
            cfg = await get_config("mixed")

            # 1. 查询动态配置
            raw_config = await cfg.get_database_config(cfg.my_config_db, params)

            # 2. 转换配置格式
            transformed = cfg.transform_dynamic_config(raw_config, my_transform_func)

            # 3. 合并配置
            final_config = await cfg.merge_configs(cfg._config, transformed)

            # 4. 使用最终配置
            mysql_config = final_config.database.rdbs.mysql
            client = await get_client(mysql_config)
            ```
        """
        try:
            if not dynamic_config_dict:
                return static_config

            # 将dict转换为DictConfig
            dynamic_config = OmegaConf.create(dynamic_config_dict)

            # 合并配置（动态配置优先）
            merged_config = OmegaConf.merge(static_config, dynamic_config)

            logger.info("配置合并完成")
            return merged_config

        except Exception as e:
            logger.error(f"配置合并失败: {e}")
            raise ConfigError(f"Failed to merge configs: {e}") from e

    async def load_dynamic_config_complete(self, config_key: str,
                                          search_params: Dict[str, Any],
                                          transform_func: Optional[callable] = None) -> DictConfig:
        """
        完整的动态配置加载流程

        Args:
            config_key: 配置键，例如 "database.rdbs.mysql"
            search_params: 搜索参数，例如 {"tenant_id": "default", "knowledge_id": "kb_001"}
            transform_func: 自定义转换函数

        Returns:
            DictConfig: 合并后的完整配置
        """
        try:
            # 1. 构建查询参数
            query_params = {
                "config_key": config_key,
                "config_type": "database",  # 可以根据config_key推断
                **search_params
            }

            # 2. 查询动态配置
            raw_config = await self.get_database_config(self._config, query_params)

            # 3. 转换配置格式
            transformed_config = self.transform_dynamic_config(raw_config, transform_func)

            # 4. 合并配置
            merged_config = await self.merge_configs(self._config, transformed_config)

            logger.info(f"动态配置加载完成: {config_key}")
            return merged_config

        except Exception as e:
            logger.warning(f"动态配置加载失败，使用静态配置: {e}")
            return self._config

    async def get_config_by_path(self, config_path: str,
                               search_params: Optional[Dict[str, Any]] = None,
                               transform_func: Optional[callable] = None) -> Any:
        """
        根据配置路径获取配置值（支持动态配置）

        Args:
            config_path: 配置路径，例如 "database.rdbs.mysql.host"
            search_params: 动态配置搜索参数
            transform_func: 自定义转换函数

        Returns:
            Any: 配置值
        """
        try:
            if search_params:
                # 使用动态配置
                merged_config = await self.load_dynamic_config_complete(
                    config_path, search_params, transform_func
                )
                config_to_use = merged_config
            else:
                # 使用静态配置
                config_to_use = self._config

            # 根据路径获取配置值
            keys = config_path.split('.')
            current = config_to_use

            for key in keys:
                if hasattr(current, key):
                    current = getattr(current, key)
                else:
                    raise ConfigError(f"配置路径不存在: {config_path}")

            return current

        except Exception as e:
            logger.error(f"获取配置失败: {e}")
            raise ConfigError(f"Failed to get config by path: {e}") from e

    @classmethod
    async def get_instance(cls, auto_initialize: bool = True) -> 'ConfigManager':
        """
        获取ConfigManager单例实例

        Args:
            auto_initialize: 是否自动初始化

        Returns:
            ConfigManager: 单例实例
        """
        instance = cls()

        if auto_initialize and not instance._initialized:
            await instance.initialize()

        return instance

    async def reload(self, **kwargs):
        """重新加载配置"""
        logger.info("重新加载配置...")
        self._initialized = False
        await self.initialize(self._source, **kwargs)
    
    async def cleanup(self):
        """清理资源"""
        if self._loader:
            await self._loader.cleanup()
        
        self._config = None
        self._loader = None
        self._initialized = False
        logger.info("配置管理器清理完成")
    
    def __getattr__(self, name: str) -> Any:
        """
        支持属性访问方式获取配置

        Examples:
            >>> cfg.database.rdbs.mysql.host
            >>> cfg.llm.openai.api_key
        """
        # 使用object.__getattribute__避免递归
        try:
            config = object.__getattribute__(self, '_config')
        except AttributeError:
            raise ConfigError("配置未初始化")

        if not config:
            raise ConfigError("配置未初始化")

        if hasattr(config, name):
            return getattr(config, name)

        raise AttributeError(f"配置中不存在属性: {name}")
    
    def __repr__(self) -> str:
        return f"ConfigManager(source={self._source}, initialized={self._initialized})"

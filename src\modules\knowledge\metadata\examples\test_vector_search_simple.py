"""
简单的向量搜索测试
"""

import asyncio
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_vector_search():
    """测试向量搜索功能"""
    print("测试向量搜索功能")
    print("-" * 40)

    try:
        # 获取数据库客户端
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
        from service import get_client

        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        embedding_client = await get_client("model.embeddings.moka-m3e-base")
        
        from modules.knowledge.metadata.search import MetadataSearch
        search_engine = MetadataSearch(rdb_client, vdb_client, embedding_client)

        # 测试码值的向量搜索
        code_results = await search_engine.search_codes_by_vector(
            query="测试码值数据",
            knowledge_id=None,  # 搜索所有知识库
            limit=5,
            min_score=0.3
        )
        print(f"   码值向量搜索: 找到 {len(code_results)} 个结果")
        
        # 显示搜索结果详情
        for i, result in enumerate(code_results[:3]):  # 只显示前3个结果
            score = result.get('score', 0)
            entity_data = result.get('entity_data', {})
            code_value = entity_data.get('code_value', 'Unknown')
            code_set_info = entity_data.get('code_set_info', {})
            code_set_name = code_set_info.get('code_set_name', 'Unknown')
            print(f"     结果{i+1}: {code_value} (码值集: {code_set_name}, 相似度: {score:.3f})")

            # 调试信息
            if entity_data:
                print(f"       调试 - entity_data keys: {list(entity_data.keys())}")
                if 'code_set_id' in entity_data:
                    print(f"       调试 - code_set_id: {entity_data['code_set_id']}")
                if code_set_info:
                    print(f"       调试 - code_set_info keys: {list(code_set_info.keys())}")

        print("   向量搜索测试完成!")
        
    except Exception as e:
        print(f"   向量搜索测试失败: {e}")


async def main():
    """主函数"""
    await test_vector_search()


if __name__ == "__main__":
    asyncio.run(main())

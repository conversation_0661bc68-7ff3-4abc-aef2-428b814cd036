from typing import Optional, List

from ...entities.text_embedding_entities import TextEmbeddingResult, EmbeddingUsage
from ..__base.text_embedding_model import TextEmbeddingModel
from decimal import Decimal
import time

import requests
import json


def get_embeddings(input_text, model="embedding", endpoint="http://218.78.129.173:30167/v1/embeddings"):
    # 构造请求数据
    payload = {
        "input": input_text,
        "model": model
    }

    # 发送 POST 请求
    headers = {"Content-Type": "application/json"}
    try:
        response = requests.post(endpoint, json=payload, headers=headers)
        response.raise_for_status()  # 检查 HTTP 状态码
        return response.json()  # 返回 JSON 格式的响应
    except requests.RequestException as e:
        raise Exception(f"Failed to get embeddings: {str(e)}")


class SampleEmbedding(TextEmbeddingModel):
    """
    示例文本嵌入模型实现
    """

    def invoke_text_embedding(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            texts: list[str],
    ) -> TextEmbeddingResult:
        """
        调用文本嵌入模型

        :param tenant_id: 租户ID
        :param user_id: 用户ID
        :param provider: 提供商名称
        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 要嵌入的文本列表
        :return: 嵌入结果
        """
        # print('invoke_text_embedding in SampleEmbedding')

        # 这里是示例实现，实际应用中需要调用真实的嵌入模型API
        # 生成一些随机嵌入向量作为示例
        embeddings = [embed['embedding'] for embed in get_embeddings(texts)["data"]]
        # 计算使用情况
        usage = EmbeddingUsage.empty_usage()

        return TextEmbeddingResult(
            model=model,
            embeddings=embeddings,
            usage=usage
        )

    async def ainvoke_text_embedding(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            texts: list[str],
    ) -> TextEmbeddingResult:
        """
        异步调用文本嵌入模型

        :param tenant_id: 租户ID
        :param user_id: 用户ID
        :param provider: 提供商名称
        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 要嵌入的文本列表
        :return: 嵌入结果
        """
        # print('ainvoke_text_embedding in SampleEmbedding')

        # 异步实现通常与同步实现类似，但在实际应用中可能会使用异步API调用
        # 这里简单复用同步方法的逻辑
        return self.invoke_text_embedding(
            tenant_id=tenant_id,
            user_id=user_id,
            provider=provider,
            model=model,
            credentials=credentials,
            texts=texts
        )

    def get_text_embedding_num_tokens(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            texts: list[str],
    ) -> list[int]:
        """
        获取文本的令牌数

        :param tenant_id: 租户ID
        :param user_id: 用户ID
        :param provider: 提供商名称
        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 文本列表
        :return: 每个文本的令牌数列表
        """
        # 简单示例：假设每个字符算一个令牌
        return [len(text) for text in texts]

    def _calc_embedding_usage(
            self,
            model: str,
            credentials: dict,
            texts: list[str]
    ) -> EmbeddingUsage:
        """
        计算嵌入使用情况
    
        :param model: 模型名称
        :param credentials: 模型凭证
        :param texts: 文本列表
        :return: 嵌入使用情况
        """
        # 计算总令牌数
        total_tokens = sum(len(text) for text in texts)

        # 示例价格计算
        unit_price = Decimal("0.0001")  # 每个令牌的单价
        price_unit = Decimal("1000")  # 价格单位（每1000个令牌）
        total_price = (Decimal(total_tokens) * unit_price) / price_unit

        # 计算延迟
        latency = 0.1  # 示例延迟时间

        return EmbeddingUsage(
            total_tokens=total_tokens,  # Changed from 'tokens' to 'total_tokens'
            unit_price=unit_price,
            price_unit=price_unit,
            total_price=total_price,
            currency="USD",
            latency=latency
        )

import json

from modules.pg_database.utils.sqlparser_hr import *
from modules.pg_database.utils.sql_transform_v1 import transform_sql_to_if_format
from typing import AsyncGenerator, Dict, List
import asyncio
from loguru import logger
from datetime import datetime


def transform_conditions(conditions, col_info, dates_col_ids):
    """将条件转换为目标格式，将 col_name 映射到 id，移除日期过滤器，并收集日期值。"""
    # print(dates_col_ids)
    # 创建从 col_name 到 id 的映射
    col_name_to_id = {item['col_name_cn']: item['col_code'] for item in col_info}
    logger.info(f"这是col_name_to_id{col_name_to_id}")
    # print(col_name_to_id)
    # 用于存储被移除的日期字段值的单层列表
    date_values = []

    def transform_filter(filter_dict):
        """转换单个过滤器字典。"""
        op = filter_dict['type']
        col_name = filter_dict['col_name']
        col_id = col_name_to_id.get(col_name, col_name)  # 如果未找到 id，则回退到 col_name
        # print(col_id)
        # 如果 col_id 在 dates_col_ids 中，收集值并跳过过滤器
        if col_id in dates_col_ids:
            # logger.info(f"这是识别的日期字段{filter_dict}")
            if op == 'between':
                date_values.extend([filter_dict['start'], filter_dict['end']])
            elif op == 'in':
                date_values.extend(filter_dict.get('values', []))
            else:
                value = filter_dict['value']
                if value != '':
                    date_values.append(value)
                    # logger.info(f"这是识别的日期{date_values}")
            return None

        if op == 'between':
            val_list = [filter_dict['start'], filter_dict['end']]
        else:
            val_list = [filter_dict['value']] if filter_dict['value'] != '' else ['NULL']

        return {
            'filter': {
                'op': op,
                'modelColId': col_id,
                'val_list': val_list
            }
        }

    def traverse(node):
        """递归转换条件结构。"""
        if isinstance(node, dict):
            if 'filter' in node:
                return transform_filter(node['filter'])
            if 'join_type' in node:
                # 转换子节点并过滤掉 None 值
                transformed_children = [traverse(child) for child in node['children']]
                transformed_children = [child for child in transformed_children if child]

                # 如果没有有效的子节点，返回 None
                if not transformed_children:
                    return None

                return {
                    'combine': node['join_type'],
                    'groups': transformed_children
                }
        elif isinstance(node, list):
            # 转换列表项并过滤掉 None
            transformed_items = [traverse(item) for item in node]
            transformed_items = [item for item in transformed_items if item]
            return transformed_items if transformed_items else None
        return node

    # 处理条件
    result = traverse(conditions)
    # 处理单项列表或 None
    if isinstance(result, list):
        result = [r for r in result if r]
        result = result[0] if len(result) == 1 else result
    # logger.info(f"这是输出识别的日期{date_values}")
    return result, date_values


def process_sql_transform(sql, indicator_data):
    """处理 SQL 转换，生成指标 ID、模型字段 ID、过滤条件和日期值。"""
    where_formula = where_parse(sql)
    select_formula = select_parse(sql)
    case_when = parse_case_when(sql)
    new_formula, dd, zz = transform_sql_to_if_format(select_formula, where_formula)
    if case_when:
        # 如果 case_when 不为空，执行额外的 SQL 构建和解析
        zz_sql = 'select ' + ','.join(zz) + ''' from test where ''' + dd
        # 重新解析新的 SQL
        select_formula = select_parse(zz_sql)
        where_formula = where_parse(zz_sql)
    # print(select_formula)
    # print(where_formula)
    # 创建从 col_name 到 id 的映射
    col_name_to_id = {item['col_name_cn']: item['col_code'] for item in indicator_data}
    logger.info(f"这是后面的col_name_to_id{col_name_to_id}")
    # 从 where_formula 中提取所有 col_name 并映射到 id
    def extract_col_names(node, col_names=None):
        if col_names is None:
            col_names = set()
        if isinstance(node, dict):
            if 'filter' in node and 'col_name' in node['filter']:
                col_names.add(node['filter']['col_name'])
            if 'children' in node:
                for child in node['children']:
                    extract_col_names(child, col_names)
        elif isinstance(node, list):
            for item in node:
                extract_col_names(item, col_names)
        return col_names

    where_col_names = extract_col_names(where_formula)
    # print('where_col_names',col_name_to_id)
    where_col_ids = [col_name_to_id.get(col_name, col_name).split('#')[-1] for col_name in where_col_names]
    # logger.info(f"这是indicator_data{indicator_data}")
    # logger.info(f"这是where_col_names{where_col_names}")
    # 处理时间字段 id
    date_col_ids = [indi['col_code'] for indi in indicator_data if indi['col_type'] == 'INDEX_DATE']
    # 检查是否存在日期字段（来自 dates_col_ids）
    date_col_names = [item['col_name_cn'] for item in indicator_data if item['col_code'] in date_col_ids]
    has_date_field = any(col_name in where_col_names for col_name in date_col_names)
    # logger.info(f"这是has_date_field识别的日期{has_date_field}")
    # 过滤 indicator_data，排除 date 和 Dimension 类型
    no_dismen_data = [indi for indi in indicator_data if indi['col_type'] not in ['INDEX_DATE', 'STRING']]
    no_dismen_data = sorted(no_dismen_data, key=lambda x: len(x["col_name_cn"]))

    # 指标 id 列表
    indexColumnIds = []
    for indicator in no_dismen_data:
        if indicator["col_name_cn"] in select_formula:
            indexColumnIds.append(indicator["col_code"].split("#")[-1])

    # 转换 where_formula，移除日期过滤器并收集日期值
    filter_where, date_values = transform_conditions(where_formula, indicator_data, date_col_ids)

    # 如果没有日期字段，设置 date_values 为当天日期
    if not has_date_field:
        current_date = datetime.now().strftime('%Y-%m-%d')
        date_values = [current_date]

    return {
        "dateRange": date_values,
        "indexColumnIds": indexColumnIds,
        "modelColumnIds": where_col_ids,
        "filter": filter_where
    }


async def process_stream_result(stream_result: AsyncGenerator, indicator_data) -> AsyncGenerator[Dict[str, str], None]:
    """
    处理流式响应，从 ChatCompletionChunk 中提取内容并生成规范化 JSON 格式。
    每次返回 {"content": chunk, "other": ""}，最后一次返回 {"content": "", "other": last_chunk}。
    """
    all_content = ""  # 累积所有 content
    async for chunk in stream_result:
        try:
            # 提取 content 和 status
            content = chunk.choices[0].delta.content if chunk.choices and chunk.choices[0].delta.content else ""
            status = chunk.choices[0].finish_reason if chunk.choices else None

            # 累积 content
            all_content += content

            if content:
                # 正常节点，返回 JSON 格式
                yield {"content": content, "event": ""}

            if status == "stop":
                # 最后一个节点，处理并返回 JSON 格式
                last_sql = all_content.split('<sql>')[1].split('</sql>')[0]
                logger.info(f"这是最终sql{last_sql}")
                if last_sql == "I do not know":
                    out_filter = {
                        "dateRange": "",
                        "indexColumnIds": "",
                        "modelColumnIds": "",
                        "filter": ""
                    }
                else:
                    out_filter = process_sql_transform(last_sql, indicator_data)

                # out_filter = process_sql_transform(last_sql, indicator_data)
                # last_chunk = json.dumps(out_filter, ensure_ascii=False)

                yield {"content": out_filter, "event": "stop"}

        except AttributeError as e:
            print(f"处理流式 chunk 时出错：{str(e)}")
            continue


async def stream_indicator_message(indicator_list: List[str]) -> AsyncGenerator[Dict[str, str], None]:
    """
    按每三个字符流式输出指标列表信息，格式化为规范化 JSON。
    """
    message = f"判断用户要使用的指标为{indicator_list}"
    # 按每三个字符分割
    for i in range(0, len(message), 3):
        chunk = message[i:i + 3]
        yield {"content": chunk, "event": ""}
        await asyncio.sleep(0.1)  # 模拟流式输出的间隔


# async def combined_stream(indicator_list: List[str], llm_result: AsyncGenerator, indicator_data: List[dict]) -> AsyncGenerator[str, None]:
#     """
#     合并指标信息流和 LLM 结果流，输出 SSE 格式的规范化 JSON。
#     """
#     # 先输出指标信息
#     async for chunk in stream_indicator_message(indicator_list):
#         # 确保 chunk 是字典格式，序列化为 JSON 并格式化为 SSE 事件
#         yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
#
#     # 再输出 LLM 结果
#     async for chunk in process_stream_result(llm_result, indicator_data):
#         # chunk 已经是 {"content": ..., "other": ...} 格式，直接序列化为 JSON
#         yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
import json
import traceback
from typing import List, AsyncGenerator

async def combined_stream(indicator_list: List[str], llm_result: AsyncGenerator, indicator_data: List[dict]) -> AsyncGenerator[str, None]:
    """
    合并指标信息流和 LLM 结果流，输出 SSE 格式的规范化 JSON。
    """
    try:
        # 先输出指标信息
        async for chunk in stream_indicator_message(indicator_list):
            try:
                # 确保 chunk 是字典格式，序列化为 JSON 并格式化为 SSE 事件
                if not isinstance(chunk, dict):
                    chunk = {
                        "error": "无效的 chunk 格式",
                        "value": str(chunk),
                        "context": "处理指标信息流"
                    }
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
            except Exception as inner_e:
                error_details = {
                    "error": f"指标流处理错误: {str(inner_e)}",
                    "exception_type": type(inner_e).__name__,
                    "stack_trace": traceback.format_exc(),
                    "context": "处理指标信息 chunk"
                }
                yield f"data: {json.dumps(error_details, ensure_ascii=False)}\n\n"

        # 再输出 LLM 结果
        async for chunk in process_stream_result(llm_result, indicator_data):
            try:
                # chunk 已经是 {"content": ..., "other": ...} 格式，直接序列化为 JSON
                if not isinstance(chunk, dict):
                    chunk = {
                        "error": "无效的 chunk 格式",
                        "value": str(chunk),
                        "context": "处理 LLM 结果流"
                    }
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
            except Exception as inner_e:
                error_details = {
                    "error": f"LLM 结果流处理错误: {str(inner_e)}",
                    "exception_type": type(inner_e).__name__,
                    "stack_trace": traceback.format_exc(),
                    "context": "处理 LLM 结果 chunk"
                }
                yield f"data: {json.dumps(error_details, ensure_ascii=False)}\n\n"

    except Exception as outer_e:
        error_details = {
            "error": f"意外错误: {str(outer_e)}",
            "exception_type": type(outer_e).__name__,
            "stack_trace": traceback.format_exc(),
            "context": "合并流处理主循环"
        }
        yield f"data: {json.dumps(error_details, ensure_ascii=False)}\n\n"



if __name__ == '__main__':
    sql = '''SELECT "总资产", "流动负债" - "营业收入" AS "利润" FROM indicator WHERE "时间" = '2023-11-15' AND ("分行" = '北京分行北京北辰支行' OR "分行" = '南京分行南京南城支行') '''
    indicator = [{'id': 11, 'col_name': 'date', 'col_type': 'date', 'col_name_cn': '时间'},
                 {'id': 12, 'col_name': 'branch', 'col_type': 'Dimension', 'col_name_cn': '分行'},
                 {'id': 4, 'col_name': 'revenue', 'col_type': 'float', 'col_name_cn': '营业收入', 'distance': 0.0},
                 {'id': 6, 'col_name': 'cash_flow', 'col_type': 'float', 'col_name_cn': '现金流量',
                  'distance': 0.15960243272952657},
                 {'id': 2, 'col_name': 'net_profit', 'col_type': 'float', 'col_name_cn': '净利润',
                  'distance': 0.11769145513616175},
                 {'id': 1, 'col_name': 'total_assets', 'col_type': 'float', 'col_name_cn': '总资产', 'distance': 0.0},
                 {'id': 10, 'col_name': 'return_on_assets', 'col_type': 'float', 'col_name_cn': '资产回报率',
                  'distance': 0.17054132481357542},
                 {'id': 5, 'col_name': 'operating_expenses', 'col_type': 'float', 'col_name_cn': '营业费用',
                  'distance': 0.07417052028099325},
                 {'id': 3, 'col_name': 'current_liabilities', 'col_type': 'float', 'col_name_cn': '流动负债',
                  'distance': 0.0},
                 {'id': 8, 'col_name': 'debt_to_equity_ratio', 'col_type': 'float', 'col_name_cn': '负债权益比',
                  'distance': 0.1429572808698203}]
    x = process_sql_transform(sql, indicator)
    print(x)

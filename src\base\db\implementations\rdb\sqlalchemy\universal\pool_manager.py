"""
连接池管理器 - 重构版本

移除全局单例模式，改为依赖注入模式，与service层的单例管理兼容：
1. 连接池实例化管理
2. 连接池生命周期管理
3. 资源监控和清理
4. 优雅关闭机制
"""

import atexit
import asyncio
import threading
import weakref
import logging
from typing import Dict, Optional, Set, Any, Tuple, List, Protocol
from contextlib import contextmanager, asynccontextmanager
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine

logger = logging.getLogger(__name__)


class PoolManagerInterface(Protocol):
    """连接池管理器接口"""

    def get_or_create_engines(self, config) -> Tuple[Optional[Engine], Optional[AsyncEngine]]:
        """获取或创建引擎"""
        ...

    def register_client(self, config, client_ref: weakref.ref) -> None:
        """注册客户端引用"""
        ...

    def unregister_client(self, config, client_ref: weakref.ref) -> None:
        """注销客户端引用"""
        ...

    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        ...

    def shutdown_all(self) -> None:
        """关闭所有连接池"""
        ...


class PoolManager:
    """
    连接池管理器 - 重构版本

    移除全局单例模式，改为实例化管理：
    1. 分离同步和异步连接池管理
    2. 异步连接池只在异步上下文中清理
    3. 程序退出时只清理同步连接池
    4. 支持依赖注入，与service层兼容
    """

    def __init__(self, instance_id: Optional[str] = None):
        """
        初始化连接池管理器

        Args:
            instance_id: 实例ID，用于标识不同的池管理器实例
        """
        self.instance_id = instance_id or f"pool_manager_{id(self)}"

        # 分离同步和异步连接池
        self._sync_pools: Dict[str, Engine] = {}
        self._async_pools: Dict[str, AsyncEngine] = {}
        self._pool_refs: Dict[str, Set[weakref.ref]] = {}
        self._lock = threading.RLock()
        self._shutdown = False

        logger.debug(f"PoolManager initialized: {self.instance_id}")

        # 企业级监控：连接池指标收集（非侵入式）
        self._pool_metrics = {
            'total_connections_created': 0,
            'total_connections_closed': 0,
            'pool_creation_count': 0,
            'last_health_check': 0,
            'instance_id': self.instance_id
        }
    
    def _generate_pool_key(self, config) -> str:
        """生成连接池键"""
        # 基于连接参数生成唯一键，相同参数共享连接池
        key_parts = [
            config.dialect,
            config.host or '',
            str(config.port or ''),
            config.database or '',
            config.username or '',
            # 不包含密码在键中，但密码不同的连接不应该共享池
        ]
        return "|".join(key_parts)

    def _build_async_url(self, config) -> str:
        """构建异步数据库URL"""
        sync_url = config.build_database_url()

        # 将同步驱动转换为异步驱动
        if 'pymysql' in sync_url:
            return sync_url.replace('pymysql', 'aiomysql')
        elif 'psycopg2' in sync_url:
            return sync_url.replace('psycopg2', 'asyncpg')
        elif 'sqlite' in sync_url:
            return sync_url.replace('sqlite://', 'sqlite+aiosqlite://')
        else:
            # 默认情况，尝试添加异步前缀
            return sync_url
    
    def get_or_create_engines(self, config) -> Tuple[Optional[Engine], Optional[AsyncEngine]]:
        """获取或创建引擎（线程安全）"""
        if self._shutdown:
            raise RuntimeError("Pool manager is shutting down")
        
        pool_key = self._generate_pool_key(config)
        
        with self._lock:
            # 检查是否已存在
            sync_engine = self._sync_pools.get(pool_key)
            async_engine = self._async_pools.get(pool_key)

            if sync_engine and async_engine:
                logger.debug(f"Reusing existing engines for key: {pool_key}")
                return sync_engine, async_engine

            try:
                # 获取引擎参数
                engine_kwargs = config.get_engine_kwargs()
                async_engine_kwargs = config.get_async_engine_kwargs()

                # 创建同步引擎（如果不存在）
                if not sync_engine:
                    sync_engine = create_engine(
                        config.build_database_url(),
                        **engine_kwargs
                    )
                    self._sync_pools[pool_key] = sync_engine

                # 创建异步引擎（如果不存在）
                if not async_engine:
                    async_url = self._build_async_url(config)
                    async_engine = create_async_engine(
                        async_url,
                        **async_engine_kwargs
                    )
                    self._async_pools[pool_key] = async_engine

                # 初始化引用集合
                if pool_key not in self._pool_refs:
                    self._pool_refs[pool_key] = set()

                # 企业级监控：更新指标（非侵入式）
                self._pool_metrics['pool_creation_count'] += 1
                self._pool_metrics['total_connections_created'] += (
                    getattr(config, 'pool_size', 20)  # 使用新的默认值
                )

                logger.info(f"Created new engines for key: {pool_key}")
                return sync_engine, async_engine
                
            except Exception as e:
                # 清理部分创建的引擎
                if sync_engine and pool_key in self._sync_pools:
                    try:
                        sync_engine.dispose()
                        del self._sync_pools[pool_key]
                    except Exception:
                        pass
                if async_engine and pool_key in self._async_pools:
                    try:
                        # 异步引擎清理在异常情况下也可能失败，静默忽略
                        del self._async_pools[pool_key]
                    except Exception:
                        pass
                raise e
    
    def register_client(self, config, client_ref: weakref.ref):
        """注册客户端引用"""
        pool_key = self._generate_pool_key(config)
        
        with self._lock:
            if pool_key in self._pool_refs:
                self._pool_refs[pool_key].add(client_ref)
                logger.debug(f"Registered client for pool: {pool_key}")
    
    def unregister_client(self, config, client_ref: weakref.ref):
        """注销客户端引用"""
        pool_key = self._generate_pool_key(config)
        
        with self._lock:
            if pool_key in self._pool_refs:
                self._pool_refs[pool_key].discard(client_ref)
                
                # 如果没有客户端引用了，考虑清理连接池
                if not self._pool_refs[pool_key]:
                    self._cleanup_unused_pool(pool_key)
    
    def _cleanup_unused_pool(self, pool_key: str):
        """清理未使用的连接池"""
        try:
            # 清理同步引擎
            if pool_key in self._sync_pools:
                sync_engine = self._sync_pools[pool_key]
                if sync_engine:
                    sync_engine.dispose()
                del self._sync_pools[pool_key]

            # 异步引擎不在这里清理，避免事件循环问题
            # 只从字典中移除，让它自然清理
            if pool_key in self._async_pools:
                del self._async_pools[pool_key]

            # 清理引用
            if pool_key in self._pool_refs:
                del self._pool_refs[pool_key]

            logger.info(f"Cleaned up unused pool: {pool_key}")

        except Exception as e:
            logger.error(f"Error cleaning up pool {pool_key}: {e}")
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._lock:
            # 获取所有池的键
            all_pool_keys = set(self._sync_pools.keys()) | set(self._async_pools.keys())

            stats = {
                'total_pools': len(all_pool_keys),
                'pools': {}
            }

            for pool_key in all_pool_keys:
                sync_engine = self._sync_pools.get(pool_key)
                async_engine = self._async_pools.get(pool_key)

                pool_stat = {
                    'client_count': len(self._pool_refs.get(pool_key, set())),
                    'sync_engine': sync_engine is not None,
                    'async_engine': async_engine is not None,
                }
                
                # 获取连接池详细信息
                if sync_engine and hasattr(sync_engine, 'pool'):
                    pool = sync_engine.pool
                    pool_info = {'pool_type': type(pool).__name__}

                    # 安全获取连接池属性
                    for attr in ['size', 'checkedin', 'checkedout', 'overflow']:
                        try:
                            if hasattr(pool, attr):
                                value = getattr(pool, attr)
                                pool_info[attr] = value() if callable(value) else value
                            else:
                                pool_info[attr] = 'N/A'
                        except Exception:
                            pool_info[attr] = 'Error'

                    pool_stat['sync_pool'] = pool_info

                if async_engine and hasattr(async_engine, 'pool'):
                    pool = async_engine.pool
                    pool_info = {'pool_type': type(pool).__name__}

                    # 安全获取连接池属性
                    for attr in ['size', 'checkedin', 'checkedout', 'overflow']:
                        try:
                            if hasattr(pool, attr):
                                value = getattr(pool, attr)
                                pool_info[attr] = value() if callable(value) else value
                            else:
                                pool_info[attr] = 'N/A'
                        except Exception:
                            pool_info[attr] = 'Error'

                    pool_stat['async_pool'] = pool_info
                
                stats['pools'][pool_key] = pool_stat
            
            return stats
    
    def shutdown_all(self):
        """
        关闭所有连接池 - 新设计

        只清理同步连接池，异步连接池留给异步上下文处理
        """
        logger.info("Shutting down connection pools (sync only)...")

        with self._lock:
            self._shutdown = True

            # 只清理同步连接池
            for pool_key, sync_engine in self._sync_pools.items():
                try:
                    if sync_engine:
                        sync_engine.dispose()
                        logger.debug(f"Disposed sync engine for pool: {pool_key}")
                except Exception as e:
                    logger.warning(f"Error disposing sync engine for pool {pool_key}: {e}")

            # 清理同步连接池
            self._sync_pools.clear()

            # 异步连接池不在这里清理，避免事件循环问题
            # 它们会在进程退出时自动清理，或者在异步上下文中清理
            if self._async_pools:
                logger.debug(f"Leaving {len(self._async_pools)} async engines for async cleanup")

            self._pool_refs.clear()

        logger.info("Sync connection pools shut down")

    async def ashutdown_all(self):
        """
        异步关闭所有连接池 - 新设计

        在异步上下文中清理所有连接池（包括异步连接池）
        """
        logger.info("Async shutting down all connection pools...")

        with self._lock:
            self._shutdown = True

            # 清理同步连接池
            for pool_key, sync_engine in self._sync_pools.items():
                try:
                    if sync_engine:
                        sync_engine.dispose()
                        logger.debug(f"Disposed sync engine for pool: {pool_key}")
                except Exception as e:
                    logger.warning(f"Error disposing sync engine for pool {pool_key}: {e}")

            # 收集异步清理任务
            dispose_tasks = []
            for pool_key, async_engine in self._async_pools.items():
                if async_engine:
                    dispose_tasks.append(self._async_dispose_engine(async_engine, pool_key))

            # 等待所有异步清理完成
            if dispose_tasks:
                try:
                    await asyncio.gather(*dispose_tasks, return_exceptions=True)
                    logger.debug(f"Disposed {len(dispose_tasks)} async engines")
                except Exception as e:
                    logger.warning(f"Error in async dispose tasks: {e}")

            # 清理所有连接池
            self._sync_pools.clear()
            self._async_pools.clear()
            self._pool_refs.clear()

        logger.info("All connection pools shut down (async)")

    async def _async_dispose_engine(self, async_engine, pool_key: str):
        """异步清理引擎"""
        try:
            await async_engine.dispose()
            logger.debug(f"Async engine disposed for pool: {pool_key}")
        except Exception as e:
            logger.debug(f"Error disposing async engine for pool {pool_key}: {e}")
    
    def force_cleanup(self):
        """强制清理所有未使用的连接池"""
        with self._lock:
            # 清理死亡的客户端引用
            for pool_key in list(self._pool_refs.keys()):
                live_refs = {ref for ref in self._pool_refs[pool_key] if ref() is not None}
                self._pool_refs[pool_key] = live_refs
                
                # 如果没有活跃引用，清理连接池
                if not live_refs:
                    self._cleanup_unused_pool(pool_key)


# 向后兼容：保留全局实例用于旧代码
_global_pool_manager = None


def get_global_pool_manager() -> PoolManagerInterface:
    """
    获取全局连接池管理器 - 客户端自管理连接池的标准方式

    这是设计的标准方式：每个客户端自己管理自己的连接池，
    Service层只负责客户端实例管理，不干预连接池管理。
    """
    global _global_pool_manager
    if _global_pool_manager is None:
        _global_pool_manager = PoolManager(instance_id="global_self_managed")
        logger.debug("Initialized global pool manager for client self-management.")
    return _global_pool_manager


class PooledConnectionMixin:
    """
    连接池混入类 - 客户端自管理连接池

    设计原则：
    - 客户端自己管理自己的连接池（标准方式）
    - 支持可选的依赖注入（高级用法）
    - Service层只管理客户端实例，不干预连接池
    """

    def __init__(self, *args, pool_manager: Optional[PoolManagerInterface] = None, **kwargs):
        """
        初始化连接池混入

        Args:
            pool_manager: 可选的池管理器实例，如果不提供则使用客户端自管理方式（推荐）
            *args, **kwargs: 传递给父类的参数
        """
        super().__init__(*args, **kwargs)

        # 优先使用注入的池管理器，否则使用客户端自管理方式（标准设计）
        self._pool_manager = pool_manager or get_global_pool_manager()
        self._client_ref = weakref.ref(self, self._cleanup_callback)
        self._pool_manager.register_client(self.config, self._client_ref)

        logger.debug(f"PooledConnectionMixin initialized with pool manager: {getattr(self._pool_manager, 'instance_id', 'unknown')}")

    def _cleanup_callback(self, ref):
        """客户端被垃圾回收时的清理回调"""
        try:
            self._pool_manager.unregister_client(self.config, ref)
        except Exception as e:
            logger.error(f"Error in cleanup callback: {e}")

    def _get_shared_engines(self):
        """获取共享的引擎"""
        return self._pool_manager.get_or_create_engines(self.config)

    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        return self._pool_manager.get_pool_stats()

    def set_pool_manager(self, pool_manager: PoolManagerInterface) -> None:
        """
        设置池管理器 - 用于运行时替换

        Args:
            pool_manager: 新的池管理器实例
        """
        # 从旧管理器注销
        if self._pool_manager:
            try:
                self._pool_manager.unregister_client(self.config, self._client_ref)
            except Exception as e:
                logger.warning(f"Error unregistering from old pool manager: {e}")

        # 注册到新管理器
        self._pool_manager = pool_manager
        self._pool_manager.register_client(self.config, self._client_ref)

        logger.debug(f"Pool manager updated to: {getattr(pool_manager, 'instance_id', 'unknown')}")


@contextmanager
def managed_client(client_factory, *args, **kwargs):
    """托管客户端上下文管理器"""
    client = None
    try:
        client = client_factory(*args, **kwargs)
        yield client
    finally:
        if client and hasattr(client, 'disconnect'):
            try:
                client.disconnect()
            except Exception as e:
                logger.error(f"Error disconnecting managed client: {e}")


@asynccontextmanager
async def amanaged_client(client_factory, *args, **kwargs):
    """异步托管客户端上下文管理器"""
    client = None
    try:
        client = client_factory(*args, **kwargs)
        yield client
    finally:
        if client and hasattr(client, 'adisconnect'):
            try:
                await client.adisconnect()
            except Exception as e:
                logger.error(f"Error disconnecting async managed client: {e}")


def cleanup_all_pools():
    """清理所有连接池 - 向后兼容"""
    global _global_pool_manager
    if _global_pool_manager:
        _global_pool_manager.shutdown_all()


def force_cleanup_unused_pools():
    """强制清理未使用的连接池"""
    _global_pool_manager.force_cleanup()


def get_all_pool_stats():
    """获取所有连接池统计信息"""
    return _global_pool_manager.get_pool_stats()


def graceful_cleanup_pools():
    """
    优雅清理连接池 - 新设计

    程序退出时只清理同步连接池，避免异步引擎清理问题
    """
    import warnings
    import sys

    # 抑制第三方库的异步清理警告和错误
    with warnings.catch_warnings():
        warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*Event loop is closed.*")
        warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*coroutine.*was never awaited.*")

        # 临时重定向stderr来抑制aiomysql的清理错误
        original_stderr = sys.stderr
        try:
            # 创建一个空的stderr来抑制aiomysql错误输出
            import io
            sys.stderr = io.StringIO()

            # 只进行同步清理，避免事件循环问题
            _global_pool_manager.shutdown_all()
            logger.debug("Graceful cleanup completed (sync only)")
        except Exception as e:
            # 任何异常都忽略，确保程序能正常退出
            logger.debug(f"Error in graceful cleanup: {e}")
            pass
        finally:
            # 恢复stderr
            sys.stderr = original_stderr

    def get_pool_metrics(self) -> Dict[str, Any]:
        """
        企业级监控：获取连接池指标（非侵入式）

        Returns:
            Dict包含连接池的基本指标信息
        """
        with self._lock:
            metrics = self._pool_metrics.copy()

            # 添加当前连接池状态
            metrics.update({
                'active_sync_pools': len(self._sync_pools),
                'active_async_pools': len(self._async_pools),
                'total_pool_keys': len(self._pool_refs),
                'pool_utilization': self._calculate_pool_utilization()
            })

            return metrics

    def _calculate_pool_utilization(self) -> float:
        """计算连接池平均利用率（非侵入式）"""
        if not self._sync_pools:
            return 0.0

        total_utilization = 0.0
        pool_count = 0

        for engine in self._sync_pools.values():
            try:
                if hasattr(engine, 'pool'):
                    pool = engine.pool
                    if hasattr(pool, 'size') and hasattr(pool, 'checkedout'):
                        size = pool.size()
                        checked_out = pool.checkedout()
                        if size > 0:
                            total_utilization += checked_out / size
                            pool_count += 1
            except Exception:
                # 忽略任何获取指标时的错误，保持非侵入性
                continue

        return total_utilization / max(pool_count, 1)

    def get_pool_health_summary(self) -> Dict[str, Any]:
        """
        企业级监控：获取连接池健康摘要（非侵入式）

        Returns:
            Dict包含连接池健康状态摘要
        """
        import time
        current_time = time.time()

        # 更新健康检查时间
        self._pool_metrics['last_health_check'] = current_time

        utilization = self._calculate_pool_utilization()

        # 简单的健康评分逻辑
        if utilization < 0.2:
            health_status = "underutilized"
        elif utilization < 0.8:
            health_status = "healthy"
        elif utilization < 0.95:
            health_status = "high_load"
        else:
            health_status = "overloaded"

        return {
            'health_status': health_status,
            'utilization': utilization,
            'active_pools': len(self._sync_pools),
            'recommendations': self._get_optimization_recommendations(utilization)
        }

    def _get_optimization_recommendations(self, utilization: float) -> List[str]:
        """基于利用率提供优化建议（非侵入式）"""
        recommendations = []

        if utilization > 0.9:
            recommendations.append("Consider increasing pool_size and max_overflow")
        elif utilization < 0.1:
            recommendations.append("Consider decreasing pool_size to save resources")

        if len(self._sync_pools) > 10:
            recommendations.append("Consider connection pool consolidation")

        return recommendations


# 注册优雅清理函数
import atexit
atexit.register(graceful_cleanup_pools)

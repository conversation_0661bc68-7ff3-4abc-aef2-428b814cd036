"""
DD系统填报数据管理API路由

提供填报数据的完整CRUD操作接口，包括：
- 创建填报数据（包含自动向量化）
- 获取填报数据详情
- 更新填报数据（包含向量更新）
- 删除填报数据
- 查询填报数据列表
"""

import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Depends

logger = logging.getLogger(__name__)

from api.knowledge.models.response_models import (
    ListResponse, CreateResponse, UpdateResponse, DeleteResponse, DetailResponse
)
from ..models.requests import SubmissionDataCreateRequest, SubmissionDataUpdateRequest
from ..models.responses import SubmissionDataResponse
from ..models.enums import DataLayerEnum, SubmissionTypeEnum
from ..dependencies.common import get_dd_crud, validate_pagination
from ..utils.helpers import format_response, validate_submission_id

# 创建路由器
router = APIRouter(tags=["DD填报数据管理"], prefix="/submissions")


@router.post("/", response_model=CreateResponse, summary="创建填报数据")
async def create_submission_data(
    request: SubmissionDataCreateRequest,
    dd_crud = Depends(get_dd_crud)
):
    """
    创建新的填报数据，自动进行向量化处理
    
    - **submission_id**: 填报ID，必须唯一
    - **version**: 版本号
    - **type**: 填报类型
    - **dr01**: 数据层（ADM/BDM/ADS/ODS）
    - **dr06**: 表名
    - **dr07**: 表名ID
    - **dr09**: 数据项名称（将被向量化）
    - **dr17**: 需求口径（将被向量化）
    - **dr19**: 报送频率（可选）
    - **dr22**: 责任部门（可选）
    - **bdr01**: 业务部门（可选）
    - **bdr02**: 责任人（可选）
    - **report_data_id**: 关联报表数据ID（可选）
    """
    try:
        # 验证填报ID格式
        if not validate_submission_id(request.submission_id):
            raise HTTPException(
                status_code=400,
                detail="填报ID格式无效，必须以字母开头，可包含字母、数字、下划线，长度3-50字符"
            )
        
        submission_data = request.model_dump()
        submission_pk, vector_ids = await dd_crud.create_submission_data(submission_data)

        return CreateResponse(
            success=True,
            message="填报数据创建成功",
            data={
                "submission_id": submission_pk,  # API返回主键ID，保持接口不变
                "vector_ids": vector_ids,
                "vectorized_fields": len(vector_ids)
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建填报数据失败: {e}")
        raise HTTPException(status_code=400, detail=f"创建填报数据失败: {str(e)}")


@router.get("/{submission_id}", response_model=DetailResponse, summary="获取填报数据详情")
async def get_submission_data(
    submission_id: int,  # API路径参数，实际是dd_submission_data表的主键ID
    dd_crud = Depends(get_dd_crud)
):
    """
    根据主键ID获取填报数据详情

    - **submission_id**: dd_submission_data表的主键ID（不是业务submission_id字段）
    """
    try:
        submission = await dd_crud.get_submission_data(submission_id)  # 传递主键ID
        
        if not submission:
            raise HTTPException(status_code=404, detail=f"填报数据不存在: {submission_id}")
        
        return DetailResponse(
            success=True,
            message="获取填报数据详情成功",
            data=submission
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取填报数据详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取填报数据详情失败: {str(e)}")


@router.put("/{submission_id}", response_model=UpdateResponse, summary="更新填报数据")
async def update_submission_data(
    submission_id: int,  # API路径参数，实际是dd_submission_data表的主键ID
    request: SubmissionDataUpdateRequest,
    dd_crud = Depends(get_dd_crud)
):
    """
    更新填报数据，如果更新了dr09或dr17字段，会自动更新向量

    - **submission_id**: dd_submission_data表的主键ID（不是业务submission_id字段）
    - **dr06**: 表名（可选）
    - **dr07**: 表名ID（可选）
    - **dr09**: 数据项名称（可选，更新时会重新向量化）
    - **dr17**: 需求口径（可选，更新时会重新向量化）
    - **dr19**: 报送频率（可选）
    - **dr22**: 责任部门（可选）
    - **bdr01**: 业务部门（可选）
    - **bdr02**: 责任人（可选）
    """
    try:
        # 检查填报数据是否存在
        existing_submission = await dd_crud.get_submission_data(submission_id)  # 传递主键ID
        if not existing_submission:
            raise HTTPException(status_code=404, detail=f"填报数据不存在: {submission_id}")

        # 只更新非空字段
        update_data = {k: v for k, v in request.model_dump().items() if v is not None}

        if not update_data:
            raise HTTPException(status_code=400, detail="没有提供要更新的字段")

        success, new_vector_ids = await dd_crud.update_submission_data(submission_id, update_data)  # 传递主键ID
        
        if not success:
            raise HTTPException(status_code=400, detail="更新填报数据失败")
        
        return UpdateResponse(
            success=True,
            message="填报数据更新成功",
            data={
                "submission_id": submission_id,
                "new_vector_ids": new_vector_ids,
                "updated_vectors": len(new_vector_ids)
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新填报数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新填报数据失败: {str(e)}")


@router.delete("/{submission_id}", response_model=DeleteResponse, summary="删除填报数据")
async def delete_submission_data(
    submission_id: int,  # API路径参数，实际是dd_submission_data表的主键ID
    dd_crud = Depends(get_dd_crud)
):
    """
    删除填报数据，同时删除相关的向量数据

    - **submission_id**: dd_submission_data表的主键ID（不是业务submission_id字段）
    """
    try:
        # 检查填报数据是否存在
        existing_submission = await dd_crud.get_submission_data(submission_id)  # 传递主键ID
        if not existing_submission:
            raise HTTPException(status_code=404, detail=f"填报数据不存在: {submission_id}")

        success = await dd_crud.delete_submission_data(submission_id)  # 传递主键ID
        
        if not success:
            raise HTTPException(status_code=400, detail="删除填报数据失败")
        
        return DeleteResponse(
            success=True,
            message="填报数据删除成功",
            data={"submission_id": submission_id}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除填报数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除填报数据失败: {str(e)}")


@router.get("/", response_model=ListResponse, summary="查询填报数据列表")
async def list_submission_data(
    data_layer: Optional[DataLayerEnum] = Query(None, description="数据层过滤"),
    submission_type: Optional[SubmissionTypeEnum] = Query(None, description="填报类型过滤"),
    version: Optional[str] = Query(None, description="版本过滤"),
    pagination = Depends(validate_pagination),
    dd_crud = Depends(get_dd_crud)
):
    """
    查询填报数据列表，支持分页和过滤
    
    - **data_layer**: 数据层过滤（可选）
    - **submission_type**: 填报类型过滤（可选）
    - **version**: 版本过滤（可选）
    - **page**: 页码（默认1）
    - **page_size**: 每页数量（默认20，最大100）
    """
    try:
        page, page_size, offset = pagination
        
        # 构建查询参数
        filters = {}
        if data_layer is not None:
            filters["data_layer"] = data_layer.value
        if submission_type is not None:
            filters["type"] = submission_type.value
        if version is not None:
            filters["version"] = version
        
        # 查询填报数据列表
        submissions = await dd_crud.list_submission_data(
            **filters,
            limit=page_size,
            offset=offset
        )
        
        # 查询总数（简化处理，实际应该有专门的count方法）
        total_submissions = await dd_crud.list_submission_data(**filters)
        total = len(total_submissions)
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        return ListResponse(
            success=True,
            message="查询填报数据列表成功",
            data={
                "items": submissions,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }
        )
    except Exception as e:
        logger.error(f"查询填报数据列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询填报数据列表失败: {str(e)}")

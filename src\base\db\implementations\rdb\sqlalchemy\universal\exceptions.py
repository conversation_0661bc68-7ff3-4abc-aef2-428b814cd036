

from typing import Optional, Any, Dict


class UniversalSQLAlchemyError(Exception):
    """Base exception for Universal SQLAlchemy Client"""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None, 
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.original_error = original_error
        self.context = context or {}
    
    def __str__(self) -> str:
        base_msg = super().__str__()
        if self.original_error:
            base_msg += f" (Original: {self.original_error})"
        return base_msg


class ConnectionError(UniversalSQLAlchemyError):
    """Database connection related errors"""
    pass


class ConfigurationError(UniversalSQLAlchemyError):
    """Configuration related errors"""
    pass


class DialectError(UniversalSQLAlchemyError):
    """Database dialect related errors"""
    pass


class QueryError(UniversalSQLAlchemyError):
    """Query execution related errors"""
    pass


class TransactionError(UniversalSQLAlchemyError):
    """Transaction management related errors"""
    pass


class UnsupportedOperationError(UniversalSQLAlchemyError):
    """Operation not supported by current database"""
    pass


class DataValidationError(UniversalSQLAlchemyError):
    """Data validation related errors"""
    pass


class PerformanceWarning(UserWarning):
    """Performance related warnings"""
    pass


class TimeoutError(UniversalSQLAlchemyError):
    """Database operation timeout errors"""
    pass


class PoolError(UniversalSQLAlchemyError):
    """Connection pool related errors"""
    pass


class CacheError(UniversalSQLAlchemyError):
    """Cache operation related errors"""
    pass


class MigrationError(UniversalSQLAlchemyError):
    """Database migration related errors"""
    pass


class AuthenticationError(UniversalSQLAlchemyError):
    """Database authentication related errors"""
    pass


class PermissionError(UniversalSQLAlchemyError):
    """Database permission related errors"""
    pass


class ResourceExhaustedError(UniversalSQLAlchemyError):
    """Database resource exhausted errors"""
    pass


def wrap_database_error(original_error: Exception, operation: str = "",
                       context: Optional[Dict[str, Any]] = None) -> UniversalSQLAlchemyError:
    """
    Wrap database-specific errors into universal exceptions

    Args:
        original_error: The original database error
        operation: The operation that caused the error
        context: Additional context information

    Returns:
        Appropriate universal exception
    """
    error_type = type(original_error).__name__
    error_msg = str(original_error).lower()
    message = f"Database error during {operation}: {original_error}" if operation else str(original_error)

    # Map common database errors to universal exceptions
    if "connection" in error_type.lower() or "connect" in error_msg:
        return ConnectionError(message, original_error, context)
    elif "timeout" in error_type.lower() or "timeout" in error_msg:
        return TimeoutError(message, original_error, context)
    elif "pool" in error_type.lower() or "pool" in error_msg:
        return PoolError(message, original_error, context)
    elif "integrity" in error_type.lower() or "constraint" in error_msg:
        return DataValidationError(message, original_error, context)
    elif "syntax" in error_msg or "sql" in error_type.lower():
        return QueryError(message, original_error, context)
    elif "transaction" in error_type.lower() or "rollback" in error_msg:
        return TransactionError(message, original_error, context)
    elif "auth" in error_type.lower() or "authentication" in error_msg:
        return AuthenticationError(message, original_error, context)
    elif "permission" in error_type.lower() or "access denied" in error_msg:
        return PermissionError(message, original_error, context)
    elif "resource" in error_msg or "exhausted" in error_msg or "limit" in error_msg:
        return ResourceExhaustedError(message, original_error, context)
    else:
        return UniversalSQLAlchemyError(message, original_error, context)


def handle_database_error(func):
    """
    Decorator to automatically wrap database errors

    Usage:
        @handle_database_error
        async def some_database_operation(self):
            # database operations
    """
    async def async_wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            if isinstance(e, UniversalSQLAlchemyError):
                raise
            raise wrap_database_error(e, func.__name__)

    def sync_wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if isinstance(e, UniversalSQLAlchemyError):
                raise
            raise wrap_database_error(e, func.__name__)

    import asyncio
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper


class ErrorContext:
    """Error context builder for better error reporting"""

    def __init__(self):
        self.context = {}

    def add_table(self, table_name: str) -> 'ErrorContext':
        self.context['table'] = table_name
        return self

    def add_query(self, query: str) -> 'ErrorContext':
        self.context['query'] = query
        return self

    def add_params(self, params: Dict[str, Any]) -> 'ErrorContext':
        self.context['params'] = params
        return self

    def add_operation(self, operation: str) -> 'ErrorContext':
        self.context['operation'] = operation
        return self

    def add_dialect(self, dialect: str) -> 'ErrorContext':
        self.context['dialect'] = dialect
        return self

    def build(self) -> Dict[str, Any]:
        return self.context.copy()

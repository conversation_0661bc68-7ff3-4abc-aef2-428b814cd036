#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：deep doc
@File    ：doc_parser.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/17 10:33 
@Desc    ： 
"""
import logging
import os
import subprocess

def convert_doc_to_docx(input_path: str, output_path: str) -> str:
    """
    使用 LibreOffice 命令行将 .doc 文件转换为 .docx 文件。

    Args:
        input_path (str): 输入的 .doc 文件路径
        output_path (str): 输出目录

    Returns:
        str: 转换后的 .docx 文件路径
    """
    if not os.path.exists(input_path):
        raise FileNotFoundError(f"Input file {input_path} does not exist")

    output_dir = os.path.dirname(output_path)
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # LibreOffice 命令
    command = [
        "soffice",
        "--headless",
        "--convert-to", "pdf",
        input_path,
        "--outdir", output_dir
    ]

    try:
        subprocess.run(command, check=True, capture_output=True, text=True)
        temp_file = os.path.splitext(input_path)[0] + ".pdf"
        if os.path.exists(temp_file):
            os.rename(temp_file, output_path)
        if not os.path.exists(output_path):
            raise FileNotFoundError(f"Conversion failed: {output_path} not found")
        logging.info(f"Converted {input_path} to {output_path}")
        return output_path
    except subprocess.CalledProcessError as e:
        raise Exception(f"Conversion error: {e.stderr}")

class IdealDocParser:
    def __call__(self):
        return

if __name__ == '__main__':
    from . import converter
    input_path = "上海银发293号文相关事项的说明.doc"
    output_pdf_path = os.path.splitext(input_path)[0] + "_output.pdf"
    convert_doc_to_docx(input_path, output_pdf_path)
    print(f"Converted doc to docx: {output_pdf_path}")
    content = converter(output_pdf_path).markdown
    print(content)
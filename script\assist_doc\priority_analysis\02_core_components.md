# Priority 机制核心组件详解

## 1. ServicePriority 枚举类

### 1.1 定义位置
`src/service/client/priority.py`

### 1.2 实现代码
```python
class ServicePriority(Enum):
    """服务优先级枚举"""
    HIGH = "high"
    STANDARD = "standard" 
    LOW = "low"
    
    @classmethod
    def from_string(cls, priority_str: str) -> 'ServicePriority':
        """从字符串创建优先级枚举"""
        priority_map = {
            'high': cls.HIGH,
            'critical': cls.HIGH,
            'urgent': cls.HIGH,
            'standard': cls.STANDARD,
            'normal': cls.STANDARD,
            'medium': cls.STANDARD,
            'low': cls.LOW,
            'background': cls.LOW,
            'batch': cls.LOW
        }
        
        normalized = priority_str.lower().strip()
        if normalized in priority_map:
            return priority_map[normalized]
        
        logger.warning(f"未知优先级 '{priority_str}'，使用默认优先级 'standard'")
        return cls.STANDARD
```

### 1.3 功能说明
1. 定义了三种优先级：HIGH、STANDARD、LOW
2. 提供 `from_string` 方法支持多种字符串形式的优先级表示
3. 对于未知的优先级字符串，会记录警告日志并返回 STANDARD 优先级

## 2. PriorityConfigMapper 类

### 2.1 定义位置
`src/service/client/priority.py`

### 2.2 实现代码
```python
@dataclass
class DatabaseType:
    """数据库类型定义"""
    name: str
    category: str  # 'rdb' 或 'vdb'
    
    def __str__(self):
        return self.name

class PriorityConfigMapper:
    """
    优先级配置映射器
    
    负责将数据库类型和优先级映射到具体的配置路径
    """
    
    # 支持的数据库类型
    MYSQL = DatabaseType("mysql", "rdb")
    PGVECTOR = DatabaseType("pgvector", "vdb")
    
    # 数据库类型映射
    DATABASE_TYPES = {
        "mysql": MYSQL,
        "pgvector": PGVECTOR,
        "pg": PGVECTOR,  # 别名
        "postgres": PGVECTOR,  # 别名
        "vector": PGVECTOR,  # 别名
    }
    
    @classmethod
    def get_config_path(cls, 
                       db_type: str, 
                       priority: ServicePriority) -> str:
        """
        获取配置路径
        
        Args:
            db_type: 数据库类型 ('mysql', 'pgvector')
            priority: 服务优先级
            
        Returns:
            配置路径字符串
            
        Raises:
            ValueError: 不支持的数据库类型
        """
        # 标准化数据库类型
        normalized_db_type = db_type.lower().strip()
        
        if normalized_db_type not in cls.DATABASE_TYPES:
            supported_types = list(cls.DATABASE_TYPES.keys())
            raise ValueError(f"不支持的数据库类型: {db_type}. 支持的类型: {supported_types}")
        
        db_info = cls.DATABASE_TYPES[normalized_db_type]
        
        # 构建配置路径
        if priority == ServicePriority.STANDARD:
            # 标准优先级使用原有配置路径（向后兼容）
            if db_info.category == "rdb":
                return f"database.rdbs.{db_info.name}"
            else:
                return f"database.vdbs.{db_info.name}"
        else:
            # 其他优先级使用新的配置路径
            if db_info.category == "rdb":
                return f"database.rdbs.{db_info.name}_{priority.value}_priority"
            else:
                return f"database.vdbs.{db_info.name}_{priority.value}_priority"
    
    @classmethod
    def parse_config_path(cls, config_path: str) -> Tuple[Optional[str], Optional[ServicePriority]]:
        """
        解析配置路径，提取数据库类型和优先级
        
        Args:
            config_path: 配置路径
            
        Returns:
            (数据库类型, 优先级) 元组，如果无法解析则返回 (None, None)
        """
        try:
            parts = config_path.split('.')
            
            if len(parts) < 3:
                return None, None
            
            # 期望格式: database.rdbs.mysql 或 database.vdbs.pgvector
            # 或者: database.rdbs.mysql_high_priority
            if parts[0] != "database":
                return None, None
            
            category = parts[1]  # rdbs 或 vdbs
            db_config = parts[2]  # mysql 或 mysql_high_priority
            
            # 检查是否包含优先级后缀
            if "_priority" in db_config:
                # 提取数据库类型和优先级
                db_parts = db_config.split("_")
                if len(db_parts) >= 3 and db_parts[-1] == "priority":
                    db_type = db_parts[0]
                    priority_str = db_parts[-2]
                    priority = ServicePriority.from_string(priority_str)
                    return db_type, priority
            else:
                # 标准配置，默认为 standard 优先级
                return db_config, ServicePriority.STANDARD
            
            return None, None
            
        except Exception as e:
            logger.warning(f"解析配置路径失败: {config_path}, 错误: {e}")
            return None, None
```

### 2.3 功能说明
1. 定义了支持的数据库类型（MYSQL、PGVECTOR）及其类别（RDB、VDB）
2. 提供 `get_config_path` 方法将数据库类型和优先级映射为配置路径
3. 提供 `parse_config_path` 方法从配置路径解析出数据库类型和优先级

## 3. 便捷函数

### 3.1 get_priority_config_path
```python
def get_priority_config_path(db_type: str, priority: str) -> str:
    """
    便捷函数：获取优先级配置路径
    
    Args:
        db_type: 数据库类型
        priority: 优先级字符串
        
    Returns:
        配置路径
    """
    priority_enum = ServicePriority.from_string(priority)
    return PriorityConfigMapper.get_config_path(db_type, priority_enum)
```

### 3.2 parse_priority_from_config
```python
def parse_priority_from_config(config_path: str) -> Optional[ServicePriority]:
    """
    便捷函数：从配置路径解析优先级
    
    Args:
        config_path: 配置路径
        
    Returns:
        优先级枚举，如果无法解析则返回None
    """
    _, priority = PriorityConfigMapper.parse_config_path(config_path)
    return priority
```

## 4. 使用示例

### 4.1 基本用法
```python
# 获取高优先级MySQL配置路径
high_config_path = PriorityConfigMapper.get_config_path("mysql", ServicePriority.HIGH)
# 返回: "database.rdbs.mysql_high_priority"

# 解析配置路径
db_type, priority = PriorityConfigMapper.parse_config_path("database.rdbs.mysql_low_priority")
# 返回: ("mysql", ServicePriority.LOW)
```

### 4.2 便捷函数用法
```python
# 使用便捷函数获取配置路径
config_path = get_priority_config_path("pgvector", "high")
# 返回: "database.vdbs.pgvector_high_priority"

# 从配置路径解析优先级
priority = parse_priority_from_config("database.rdbs.mysql_standard_priority")
# 返回: ServicePriority.STANDARD
```
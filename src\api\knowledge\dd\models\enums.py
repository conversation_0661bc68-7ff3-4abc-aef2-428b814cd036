"""
DD系统枚举定义

定义DD系统中使用的所有枚举类型，确保数据一致性和类型安全。
"""

from enum import Enum


class DataLayerEnum(str, Enum):
    """
    数据层枚举

    定义常见的数据层类型：
    - ADS: 应用数据服务
    - BDM: 基础数据市场
    - IDM: 集成数据市场
    - ADM: 应用数据市场
    - ODS: 操作数据存储
    - RANGE: 范围（整表口径）
    """
    ADS = "ADS"
    BDM = "BDM"
    IDM = "IDM"
    ADM = "ADM"
    ODS = "ODS"
    RANGE = "范围"


# 注意：DataLayerEnum 已移除硬编码枚举值
# 数据层字段现在使用VARCHAR类型，支持动态值
# 常见值包括：'ADS', 'BDM', 'IDM', 'ADM', 'ODS', '范围'
# 但不再限制为固定枚举值

class DataLayerType:
    """
    数据层类型常量

    提供常见的数据层类型值，但不限制为枚举：
    - ADM: 应用数据市场
    - BDM: 基础数据市场
    - ADS: 应用数据服务
    - ODS: 操作数据存储
    - IDM: 集成数据市场
    - 范围: 范围（整表口径）
    """
    ADM = "ADM"
    BDM = "BDM"
    ADS = "ADS"
    ODS = "ODS"
    IDM = "IDM"
    RANGE = "范围"

    @classmethod
    def get_common_values(cls):
        """获取常见的数据层类型值"""
        return [cls.ADM, cls.BDM, cls.ADS, cls.ODS, cls.IDM, cls.RANGE]


class DepartmentTypeEnum(str, Enum):
    """
    部门类型枚举
    
    定义部门的分类类型：
    - normal: 普通部门
    - special: 特殊部门
    """
    NORMAL = "normal"
    SPECIAL = "special"


class SubmissionTypeEnum(str, Enum):
    """
    填报类型枚举

    定义填报数据的类型：
    - SUBMISSION: 填报项
    - RANGE: 范围（整表口径）
    """
    SUBMISSION = "SUBMISSION"
    RANGE = "RANGE"


class SubmissionTypeType:
    """
    填报类型常量

    提供常见的填报类型值，但不限制为枚举：
    - SUBMISSION: 填报项
    - RANGE: 范围（整表口径）
    """
    SUBMISSION = "SUBMISSION"
    RANGE = "RANGE"

    @classmethod
    def get_common_values(cls):
        """获取常见的填报类型值"""
        return [cls.SUBMISSION, cls.RANGE]


class ReportTypeEnum(str, Enum):
    """
    报表类型枚举

    定义报表的类型：
    - DETAIL: 明细
    - INDEX: 指标
    """
    DETAIL = "detail"
    INDEX = "index"


class ReportTypeType:
    """
    报表类型常量

    提供常见的报表类型值，但不限制为枚举：
    - detail: 明细
    - index: 指标
    """
    DETAIL = "detail"
    INDEX = "index"

    @classmethod
    def get_common_values(cls):
        """获取常见的报表类型值"""
        return [cls.DETAIL, cls.INDEX]


class ReportFrequencyEnum(str, Enum):
    """
    报送频率枚举
    
    定义报表的报送频率：
    - 日报: 每日报送
    - 周报: 每周报送
    - 月报: 每月报送
    - 季报: 每季度报送
    - 年报: 每年报送
    """
    DAILY = "日报"
    WEEKLY = "周报"
    MONTHLY = "月报"
    QUARTERLY = "季报"
    YEARLY = "年报"


class SearchTypeEnum(str, Enum):
    """
    搜索类型枚举
    
    定义搜索功能的类型：
    - vector: 向量搜索
    - hybrid: 混合搜索
    - text: 文本搜索
    - exact: 精确搜索
    """
    VECTOR = "vector"
    HYBRID = "hybrid"
    TEXT = "text"
    EXACT = "exact"


class DistributionStatusEnum(str, Enum):
    """
    分发状态枚举
    
    定义数据分发的状态：
    - pending: 待分发
    - processing: 分发中
    - completed: 已完成
    - failed: 分发失败
    """
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

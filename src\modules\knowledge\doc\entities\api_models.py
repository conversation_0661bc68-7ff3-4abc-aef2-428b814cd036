"""
文档相关的API模型定义

使用Pydantic定义请求和响应数据结构，用于FastAPI接口
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field
from uuid import uuid4

from .base_models import (
    BaseAPIModel,
    DocumentType, DocumentStatus, ParseType, DocumentFormat,
    CategoryStatus, RelationType, InfoType
)


# # ==========================================
# # 文档相关模型
# # ==========================================


class DocumentBase(BaseAPIModel):
    """文档基础模型"""
    # 数据标识
    knowledge_id: str = Field(..., description="知识库ID", max_length=255)

    # 基础信息
    doc_name: str = Field(..., description="文档名称", max_length=255)
    doc_type: Optional[DocumentType] = Field(None, description="文档类型")
    author: Optional[str] = Field(None, description="作者", max_length=255)
    # 相似度配置
    vector_similarity_weight: Optional[float] = Field(None, description="向量相似度权重", ge=0, le=1)
    similarity_threshold: Optional[float] = Field(None, description="相似度阈值", ge=0, le=1)
    # 解析信息
    parse_type: Optional[ParseType] = Field(None, description="解析方式")
    status: Optional[DocumentStatus] = Field(None, description="解析状态")
    parse_end_time: Optional[datetime] = Field(None, description="解析结束时间")
    parse_message: Optional[str] = Field(None, description="解析状态描述")
    # 文件信息
    doc_format: Optional[DocumentFormat] = Field(None, description="文件格式")
    location: Optional[str] = Field(None, description="文件存储位置", max_length=255)
    # 元数据和任务
    metadata: Optional[str] = Field(None, description="文档源数据")
    # 时间戳和状态
    created_time: Optional[datetime] = Field(None, description="创建时间")
    updated_time: Optional[datetime] = Field(None, description="更新时间")
    is_active: bool = Field(True, description="是否生效")

class DocumentCreate(DocumentBase):
    """创建文档请求模型"""
    pass
    

# class DocumentUpdate(BaseAPIModel):
#     """更新文档请求模型"""
#     doc_name: Optional[str] = Field(None, description="文档名称", max_length=255)
#     doc_type: Optional[DocumentType] = Field(None, description="文档类型")
#     author: Optional[str] = Field(None, description="作者", max_length=255)
#     vector_similarity_weight: Optional[float] = Field(None, description="向量相似度权重", ge=0, le=1)
#     similarity_threshold: Optional[float] = Field(None, description="相似度阈值", ge=0, le=1)
#     status: Optional[DocumentStatus] = Field(None, description="解析状态")
#     parse_message: Optional[str] = Field(None, description="解析状态描述")
#     doc_ocr_result_path: Optional[str] = Field(None, description="OCR识别结果路径", max_length=255)
#     task_id: Optional[str] = Field(None, description="任务队列ID", max_length=255)
#     is_active: Optional[bool] = Field(None, description="是否生效")


# class DocumentResponse(DocumentBase):
#     """文档响应模型"""
#     doc_id: str = Field(..., description="文档ID")
#     knowledge_id: str = Field(..., description="知识库ID")
#     chunk_nums: int = Field(0, description="分块总数")
#     percentage: float = Field(0, description="分块进度")
#     status: DocumentStatus = Field(..., description="解析状态")
#     parse_end_time: Optional[datetime] = Field(None, description="解析结束时间")
#     parse_message: Optional[str] = Field(None, description="解析状态描述")
#     doc_ocr_result_path: Optional[str] = Field(None, description="OCR识别结果路径")
#     task_id: Optional[str] = Field(None, description="任务队列ID")
    
#     # 时间戳和状态
#     created_time: Optional[datetime] = Field(None, description="创建时间")
#     updated_time: Optional[datetime] = Field(None, description="更新时间")
#     is_active: bool = Field(True, description="是否生效")
    
#     # 关联数据
#     chunks: Optional[List['ChunkResponse']] = Field(None, description="分块列表")
#     aliases: Optional[List['DocumentAliasResponse']] = Field(None, description="别名列表")
#     categories: Optional[List['DocumentCategoryResponse']] = Field(None, description="类别列表")


# # ==========================================
# # 分块相关模型
# # ==========================================

# class ChunkBase(BaseAPIModel):
#     """分块基础模型"""
#     chapter_layer: Optional[str] = Field(None, description="文档章节层级信息", max_length=100)
#     parent_id: Optional[str] = Field(None, description="父分块ID", max_length=64)
#     sub_chunk_ids: Optional[List[str]] = Field(None, description="子分块ID列表")


# class ChunkCreate(ChunkBase):
#     """创建分块请求模型"""
#     doc_id: str = Field(..., description="文档ID", max_length=255)
    
#     @validator('chunk_id', pre=True, always=True)
#     def generate_chunk_id(cls, v):
#         return v or str(uuid4())


# class ChunkUpdate(ChunkBase):
#     """更新分块请求模型"""
#     is_active: Optional[bool] = Field(None, description="是否生效")


# class ChunkResponse(ChunkBase):
#     """分块响应模型"""
#     chunk_id: str = Field(..., description="分块ID")
#     doc_id: str = Field(..., description="文档ID")
    
#     # 时间戳和状态
#     created_time: Optional[datetime] = Field(None, description="创建时间")
#     updated_time: Optional[datetime] = Field(None, description="更新时间")
#     is_active: bool = Field(True, description="是否生效")
    
#     # 关联数据
#     chunk_infos: Optional[List['ChunkInfoResponse']] = Field(None, description="分块信息列表")


# # ==========================================
# # 分块信息相关模型
# # ==========================================

# class ChunkInfoBase(BaseAPIModel):
#     """分块信息基础模型"""
#     info_type: InfoType = Field(..., description="信息类型")
#     info_value: str = Field(..., description="信息值")


# class ChunkInfoCreate(ChunkInfoBase):
#     """创建分块信息请求模型"""
#     chunk_id: str = Field(..., description="关联的分块ID", max_length=255)
    
#     @validator('chunk_info_id', pre=True, always=True)
#     def generate_chunk_info_id(cls, v):
#         return v or str(uuid4())


# class ChunkInfoUpdate(BaseAPIModel):
#     """更新分块信息请求模型"""
#     info_type: Optional[InfoType] = Field(None, description="信息类型")
#     info_value: Optional[str] = Field(None, description="信息值")
#     is_active: Optional[bool] = Field(None, description="是否生效")


# class ChunkInfoResponse(ChunkInfoBase):
#     """分块信息响应模型"""
#     chunk_info_id: str = Field(..., description="分块信息ID")
#     chunk_id: str = Field(..., description="关联的分块ID")
    
#     # 时间戳和状态
#     created_time: Optional[datetime] = Field(None, description="创建时间")
#     updated_time: Optional[datetime] = Field(None, description="更新时间")
#     is_active: bool = Field(True, description="是否生效")


# # ==========================================
# # 文档别名相关模型
# # ==========================================

# class DocumentAliasBase(BaseAPIModel):
#     """文档别名基础模型"""
#     doc_name: str = Field(..., description="原始文件名", max_length=255)
#     cleaned_name: str = Field(..., description="别名", max_length=255)


# class DocumentAliasCreate(DocumentAliasBase):
#     """创建文档别名请求模型"""
#     doc_id: str = Field(..., description="文件ID", max_length=64)


# class DocumentAliasResponse(DocumentAliasBase):
#     """文档别名响应模型"""
#     id: int = Field(..., description="主键ID")
#     doc_id: str = Field(..., description="文件ID")


# # ==========================================
# # 类别相关模型
# # ==========================================

# class CategoryBase(BaseAPIModel):
#     """类别基础模型"""
#     cate_name: str = Field(..., description="类别名称", max_length=255)
#     cate_status: Optional[CategoryStatus] = Field(None, description="类别状态")
#     cate_layer: int = Field(..., description="类别层级", ge=1)
#     parent_id: Optional[str] = Field(None, description="父类别ID", max_length=64)
#     sub_categories_ids: Optional[List[str]] = Field(None, description="子类别ID列表")


# class CategoryCreate(CategoryBase):
#     """创建类别请求模型"""
#     cate_id: str = Field(..., description="类别ID", max_length=255)


# class CategoryUpdate(BaseAPIModel):
#     """更新类别请求模型"""
#     cate_name: Optional[str] = Field(None, description="类别名称", max_length=255)
#     cate_status: Optional[CategoryStatus] = Field(None, description="类别状态")
#     parent_id: Optional[str] = Field(None, description="父类别ID", max_length=64)
#     sub_categories_ids: Optional[List[str]] = Field(None, description="子类别ID列表")


# class CategoryResponse(CategoryBase):
#     """类别响应模型"""
#     cate_id: str = Field(..., description="类别ID")


# # ==========================================
# # 文档类别关联相关模型
# # ==========================================

# class DocumentCategoryBase(BaseAPIModel):
#     """文档类别基础模型"""
#     cate_layer: int = Field(..., description="类别层级", ge=1)
#     doc_name: str = Field(..., description="文件名称", max_length=255)
#     doc_status: Optional[str] = Field(None, description="文件状态", max_length=255)


# class DocumentCategoryCreate(DocumentCategoryBase):
#     """创建文档类别关联请求模型"""
#     doc_id: str = Field(..., description="文件ID", max_length=255)
#     cate_id: str = Field(..., description="类别ID", max_length=255)


# class DocumentCategoryResponse(DocumentCategoryBase):
#     """文档类别关联响应模型"""
#     id: int = Field(..., description="主键ID")
#     doc_id: str = Field(..., description="文件ID")
#     cate_id: str = Field(..., description="类别ID")


# # ==========================================
# # 关系相关模型
# # ==========================================

# class RelationshipBase(BaseAPIModel):
#     """关系基础模型"""
#     rel_type: Optional[RelationType] = Field(None, description="关系类型")


# class CategoryRelationshipCreate(RelationshipBase):
#     """创建类别关系请求模型"""
#     source_cate_id: str = Field(..., description="源类别ID", max_length=255)
#     target_cate_id: str = Field(..., description="目标类别ID", max_length=255)


# class CategoryRelationshipResponse(RelationshipBase):
#     """类别关系响应模型"""
#     id: int = Field(..., description="主键ID")
#     source_cate_id: str = Field(..., description="源类别ID")
#     target_cate_id: str = Field(..., description="目标类别ID")


# class DocumentRelationshipCreate(RelationshipBase):
#     """创建文档关系请求模型"""
#     source_doc_id: str = Field(..., description="源文件ID", max_length=255)
#     source_doc_name: Optional[str] = Field(None, description="源文件名称", max_length=255)
#     target_doc_id: str = Field(..., description="目标文件ID", max_length=255)
#     target_doc_name: Optional[str] = Field(None, description="目标文件名称", max_length=255)


# class DocumentRelationshipResponse(RelationshipBase):
#     """文档关系响应模型"""
#     id: int = Field(..., description="主键ID")
#     source_doc_id: str = Field(..., description="源文件ID")
#     source_doc_name: Optional[str] = Field(None, description="源文件名称")
#     target_doc_id: str = Field(..., description="目标文件ID")
#     target_doc_name: Optional[str] = Field(None, description="目标文件名称")


# # ==========================================
# # 通用响应模型
# # ==========================================

# class PaginationParams(BaseAPIModel):
#     """分页参数模型"""
#     page: int = Field(1, description="页码", ge=1)
#     page_size: int = Field(20, description="页面大小", ge=1, le=100)


# class PaginationResponse(BaseAPIModel):
#     """分页响应模型"""
#     total: int = Field(..., description="总记录数")
#     page: int = Field(..., description="当前页码")
#     page_size: int = Field(..., description="页面大小")
#     total_pages: int = Field(..., description="总页数")


# class DocumentListResponse(BaseAPIModel):
#     """文档列表响应模型"""
#     documents: List[DocumentResponse] = Field(..., description="文档列表")
#     pagination: PaginationResponse = Field(..., description="分页信息")


# class ChunkListResponse(BaseAPIModel):
#     """分块列表响应模型"""
#     chunks: List[ChunkResponse] = Field(..., description="分块列表")
#     pagination: PaginationResponse = Field(..., description="分页信息")


# class OperationResult(BaseAPIModel):
#     """操作结果模型"""
#     success: bool = Field(..., description="操作是否成功")
#     message: str = Field(..., description="操作消息")
#     data: Optional[Dict[str, Any]] = Field(None, description="返回数据")


# # 解决前向引用问题
# DocumentResponse.model_rebuild()
# ChunkResponse.model_rebuild()
# ChunkInfoResponse.model_rebuild() 
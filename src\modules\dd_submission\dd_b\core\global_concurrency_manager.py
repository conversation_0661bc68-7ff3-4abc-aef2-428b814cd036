"""
DD-B全局并发控制管理器

解决并发控制机制澄清问题：
1. 15并发量是针对单个处理器实例还是全局限制？
2. 多个外部请求同时访问时，并发控制如何生效？
3. 是否需要在更高层级（如应用级别）进行并发控制？
"""

import asyncio
import time
import logging
import threading
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from enum import Enum
import weakref

logger = logging.getLogger(__name__)


class ConcurrencyScope(str, Enum):
    """并发控制作用域"""
    INSTANCE = "instance"      # 实例级别（单个处理器实例内部）
    GLOBAL = "global"          # 全局级别（整个应用级别）
    HYBRID = "hybrid"          # 混合模式（实例级别 + 全局级别）


@dataclass
class ConcurrencyConfig:
    """并发控制配置"""
    
    # 基本配置
    scope: ConcurrencyScope = ConcurrencyScope.HYBRID
    
    # LLM并发配置
    max_llm_concurrent_global: int = 15        # 全局最大LLM并发数
    max_llm_concurrent_instance: int = 5       # 单实例最大LLM并发数
    
    # 向量搜索并发配置
    max_vector_search_concurrent_global: int = 20    # 全局最大向量搜索并发数
    max_vector_search_concurrent_instance: int = 10  # 单实例最大向量搜索并发数
    
    # 数据库连接并发配置
    max_db_concurrent_global: int = 50         # 全局最大数据库并发数
    max_db_concurrent_instance: int = 20       # 单实例最大数据库并发数
    
    # 超时配置
    acquire_timeout: float = 30.0              # 获取并发许可超时时间
    
    # 监控配置
    enable_monitoring: bool = True             # 是否启用监控
    monitoring_interval: float = 5.0          # 监控间隔


@dataclass
class ConcurrencyStats:
    """并发统计信息"""
    
    # 全局统计
    global_llm_active: int = 0
    global_llm_total: int = 0
    global_llm_completed: int = 0
    global_llm_failed: int = 0
    
    global_vector_search_active: int = 0
    global_vector_search_total: int = 0
    global_vector_search_completed: int = 0
    global_vector_search_failed: int = 0
    
    global_db_active: int = 0
    global_db_total: int = 0
    global_db_completed: int = 0
    global_db_failed: int = 0
    
    # 实例统计
    active_instances: int = 0
    total_instances: int = 0
    
    # 时间统计
    start_time: float = field(default_factory=time.time)
    last_update_time: float = field(default_factory=time.time)
    
    def get_success_rate(self, resource_type: str) -> float:
        """获取成功率"""
        if resource_type == "llm":
            total = self.global_llm_total
            completed = self.global_llm_completed
        elif resource_type == "vector_search":
            total = self.global_vector_search_total
            completed = self.global_vector_search_completed
        elif resource_type == "db":
            total = self.global_db_total
            completed = self.global_db_completed
        else:
            return 0.0
        
        return (completed / max(total, 1)) * 100


class GlobalConcurrencyManager:
    """全局并发控制管理器（单例模式）"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, config: ConcurrencyConfig = None):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, config: ConcurrencyConfig = None):
        if self._initialized:
            return
        
        self.config = config or ConcurrencyConfig()
        
        # 全局信号量
        self.global_llm_semaphore = asyncio.Semaphore(self.config.max_llm_concurrent_global)
        self.global_vector_search_semaphore = asyncio.Semaphore(self.config.max_vector_search_concurrent_global)
        self.global_db_semaphore = asyncio.Semaphore(self.config.max_db_concurrent_global)
        
        # 统计信息
        self.stats = ConcurrencyStats()
        self.stats_lock = threading.Lock()
        
        # 实例注册
        self.registered_instances = weakref.WeakSet()
        
        # 监控任务
        self.monitoring_task = None
        
        self._initialized = True
        
        logger.info(f"全局并发控制管理器初始化完成: 作用域={self.config.scope.value}, "
                   f"全局LLM并发={self.config.max_llm_concurrent_global}")
    
    def register_instance(self, instance_id: str, instance_config: Dict[str, Any]):
        """注册处理器实例"""
        with self.stats_lock:
            self.stats.total_instances += 1
            self.stats.active_instances += 1
        
        logger.info(f"注册处理器实例: {instance_id}, 活跃实例数: {self.stats.active_instances}")
    
    def unregister_instance(self, instance_id: str):
        """注销处理器实例"""
        with self.stats_lock:
            self.stats.active_instances = max(0, self.stats.active_instances - 1)
        
        logger.info(f"注销处理器实例: {instance_id}, 活跃实例数: {self.stats.active_instances}")
    
    async def acquire_llm_permit(self, instance_id: str = None) -> bool:
        """获取LLM并发许可"""
        if self.config.scope in [ConcurrencyScope.GLOBAL, ConcurrencyScope.HYBRID]:
            try:
                await asyncio.wait_for(
                    self.global_llm_semaphore.acquire(),
                    timeout=self.config.acquire_timeout
                )
                
                with self.stats_lock:
                    self.stats.global_llm_active += 1
                    self.stats.global_llm_total += 1
                
                logger.debug(f"获取全局LLM许可成功: instance={instance_id}, "
                           f"活跃={self.stats.global_llm_active}")
                return True
                
            except asyncio.TimeoutError:
                logger.warning(f"获取全局LLM许可超时: instance={instance_id}")
                return False
        
        return True  # 实例级别控制由实例自己处理
    
    def release_llm_permit(self, instance_id: str = None, success: bool = True):
        """释放LLM并发许可"""
        if self.config.scope in [ConcurrencyScope.GLOBAL, ConcurrencyScope.HYBRID]:
            self.global_llm_semaphore.release()
            
            with self.stats_lock:
                self.stats.global_llm_active = max(0, self.stats.global_llm_active - 1)
                if success:
                    self.stats.global_llm_completed += 1
                else:
                    self.stats.global_llm_failed += 1
            
            logger.debug(f"释放全局LLM许可: instance={instance_id}, success={success}, "
                        f"活跃={self.stats.global_llm_active}")
    
    async def acquire_vector_search_permit(self, instance_id: str = None) -> bool:
        """获取向量搜索并发许可"""
        if self.config.scope in [ConcurrencyScope.GLOBAL, ConcurrencyScope.HYBRID]:
            try:
                await asyncio.wait_for(
                    self.global_vector_search_semaphore.acquire(),
                    timeout=self.config.acquire_timeout
                )
                
                with self.stats_lock:
                    self.stats.global_vector_search_active += 1
                    self.stats.global_vector_search_total += 1
                
                logger.debug(f"获取全局向量搜索许可成功: instance={instance_id}")
                return True
                
            except asyncio.TimeoutError:
                logger.warning(f"获取全局向量搜索许可超时: instance={instance_id}")
                return False
        
        return True
    
    def release_vector_search_permit(self, instance_id: str = None, success: bool = True):
        """释放向量搜索并发许可"""
        if self.config.scope in [ConcurrencyScope.GLOBAL, ConcurrencyScope.HYBRID]:
            self.global_vector_search_semaphore.release()
            
            with self.stats_lock:
                self.stats.global_vector_search_active = max(0, self.stats.global_vector_search_active - 1)
                if success:
                    self.stats.global_vector_search_completed += 1
                else:
                    self.stats.global_vector_search_failed += 1
            
            logger.debug(f"释放全局向量搜索许可: instance={instance_id}, success={success}")
    
    async def acquire_db_permit(self, instance_id: str = None) -> bool:
        """获取数据库并发许可"""
        if self.config.scope in [ConcurrencyScope.GLOBAL, ConcurrencyScope.HYBRID]:
            try:
                await asyncio.wait_for(
                    self.global_db_semaphore.acquire(),
                    timeout=self.config.acquire_timeout
                )
                
                with self.stats_lock:
                    self.stats.global_db_active += 1
                    self.stats.global_db_total += 1
                
                logger.debug(f"获取全局数据库许可成功: instance={instance_id}")
                return True
                
            except asyncio.TimeoutError:
                logger.warning(f"获取全局数据库许可超时: instance={instance_id}")
                return False
        
        return True
    
    def release_db_permit(self, instance_id: str = None, success: bool = True):
        """释放数据库并发许可"""
        if self.config.scope in [ConcurrencyScope.GLOBAL, ConcurrencyScope.HYBRID]:
            self.global_db_semaphore.release()
            
            with self.stats_lock:
                self.stats.global_db_active = max(0, self.stats.global_db_active - 1)
                if success:
                    self.stats.global_db_completed += 1
                else:
                    self.stats.global_db_failed += 1
            
            logger.debug(f"释放全局数据库许可: instance={instance_id}, success={success}")
    
    def get_global_stats(self) -> Dict[str, Any]:
        """获取全局统计信息"""
        with self.stats_lock:
            uptime = time.time() - self.stats.start_time
            
            return {
                "config": {
                    "scope": self.config.scope.value,
                    "max_llm_concurrent_global": self.config.max_llm_concurrent_global,
                    "max_vector_search_concurrent_global": self.config.max_vector_search_concurrent_global,
                    "max_db_concurrent_global": self.config.max_db_concurrent_global
                },
                "instances": {
                    "active": self.stats.active_instances,
                    "total": self.stats.total_instances
                },
                "llm": {
                    "active": self.stats.global_llm_active,
                    "total": self.stats.global_llm_total,
                    "completed": self.stats.global_llm_completed,
                    "failed": self.stats.global_llm_failed,
                    "success_rate": self.stats.get_success_rate("llm"),
                    "available": self.config.max_llm_concurrent_global - self.stats.global_llm_active
                },
                "vector_search": {
                    "active": self.stats.global_vector_search_active,
                    "total": self.stats.global_vector_search_total,
                    "completed": self.stats.global_vector_search_completed,
                    "failed": self.stats.global_vector_search_failed,
                    "success_rate": self.stats.get_success_rate("vector_search"),
                    "available": self.config.max_vector_search_concurrent_global - self.stats.global_vector_search_active
                },
                "database": {
                    "active": self.stats.global_db_active,
                    "total": self.stats.global_db_total,
                    "completed": self.stats.global_db_completed,
                    "failed": self.stats.global_db_failed,
                    "success_rate": self.stats.get_success_rate("db"),
                    "available": self.config.max_db_concurrent_global - self.stats.global_db_active
                },
                "uptime_seconds": uptime
            }
    
    async def start_monitoring(self):
        """启动监控任务"""
        if not self.config.enable_monitoring or self.monitoring_task:
            return
        
        async def monitor():
            while True:
                try:
                    stats = self.get_global_stats()
                    logger.info(f"全局并发监控: LLM活跃={stats['llm']['active']}/{stats['llm']['total']}, "
                              f"向量搜索活跃={stats['vector_search']['active']}, "
                              f"数据库活跃={stats['database']['active']}, "
                              f"活跃实例={stats['instances']['active']}")
                    
                    await asyncio.sleep(self.config.monitoring_interval)
                except Exception as e:
                    logger.error(f"全局并发监控异常: {e}")
                    await asyncio.sleep(self.config.monitoring_interval)
        
        self.monitoring_task = asyncio.create_task(monitor())
        logger.info("全局并发监控已启动")
    
    async def stop_monitoring(self):
        """停止监控任务"""
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
            self.monitoring_task = None
            logger.info("全局并发监控已停止")


# 便捷函数
def get_global_concurrency_manager(config: ConcurrencyConfig = None) -> GlobalConcurrencyManager:
    """获取全局并发控制管理器实例"""
    return GlobalConcurrencyManager(config)


def create_concurrency_config(
    scope: ConcurrencyScope = ConcurrencyScope.HYBRID,
    max_llm_concurrent_global: int = 15,
    max_llm_concurrent_instance: int = 5,
    max_vector_search_concurrent_global: int = 20,
    max_vector_search_concurrent_instance: int = 10
) -> ConcurrencyConfig:
    """创建并发控制配置"""
    return ConcurrencyConfig(
        scope=scope,
        max_llm_concurrent_global=max_llm_concurrent_global,
        max_llm_concurrent_instance=max_llm_concurrent_instance,
        max_vector_search_concurrent_global=max_vector_search_concurrent_global,
        max_vector_search_concurrent_instance=max_vector_search_concurrent_instance
    )

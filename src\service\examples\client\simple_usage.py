"""
Service Layer - 简单使用示例

展示如何用最简单的方式使用service层
"""

import asyncio
import sys
import os
import aiohttp
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from service import get_client, get_config, cleanup




async def demo_simple_client_usage():
    """演示简单的客户端使用"""
    print("=== 简单客户端使用演示 ===")
    
    try:
        # 方式1：直接使用客户端（最推荐）
        print("\n--- 方式1：直接使用客户端 ---")
        
        # MySQL客户端
        mysql_client = await get_client("database.rdbs.mysql")
        print(f"✅ MySQL客户端获取成功: {type(mysql_client).__name__}")
        
        # 执行真实场景查询
        try:
            # 查询数据库中的表
            if hasattr(mysql_client, 'afetch_all'):
                result = await mysql_client.afetch_all("SHOW TABLES")
                table_count = len(result.data)
                print(f"✅ MySQL查询成功: 数据库中有 {table_count} 个表")
                if table_count > 0:
                    # 显示前3个表名
                    tables = [list(row.values())[0] for row in result.data[:3]]
                    print(f"   前几个表: {', '.join(tables)}")
            else:
                print("⚠️ 客户端不支持原生SQL查询方法")
        except Exception as e:
            print(f"⚠️ MySQL查询失败（可能是连接问题）: {e}")
        
        # PgVector客户端
        try:
            pgvector_client = await get_client("database.vdbs.pgvector")
            print(f"✅ PgVector客户端获取成功: {type(pgvector_client).__name__}")

            # 查询PGVector数据库信息
            try:
                # 检查是否有向量表
                if hasattr(pgvector_client, 'list_collections'):
                    collections = pgvector_client.list_collections()
                    print(f"✅ PGVector查询成功: 数据库中有 {len(collections)} 个向量集合")
                    if collections:
                        print(f"   集合列表: {', '.join(collections[:3])}")
                else:
                    print("✅ PGVector客户端连接成功，但不支持集合列表查询")
            except Exception as e:
                print(f"⚠️ PGVector查询失败: {e}")

        except Exception as e:
            print(f"⚠️ PgVector客户端获取失败（配置可能不存在）: {e}")

        return True
        
    except Exception as e:
        print(f"❌ 客户端使用演示失败: {e}")
        return False


async def demo_config_usage():
    """演示配置管理器使用"""
    print("\n=== 配置管理器使用演示 ===")
    
    try:
        # 方式2：使用配置管理器
        print("\n--- 方式2：配置管理器 ---")
        
        cfg = await get_config("mixed")
        print(f"✅ 配置管理器获取成功: {type(cfg).__name__}")
        
        # 访问配置
        mysql_config = cfg.database.rdbs.mysql
        print(f"✅ MySQL配置: {mysql_config.host}:{mysql_config.port}")
        print(f"   数据库: {mysql_config.database}")
        print(f"   用户名: {mysql_config.username}")
        print(f"   连接池大小: {mysql_config.pool_size}")
        
        # 检查其他配置
        try:
            pgvector_config = cfg.database.vdbs.pgvector
            print(f"✅ PgVector配置: {pgvector_config.host}:{pgvector_config.port}")
        except Exception as e:
            print(f"⚠️ PgVector配置不存在: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器演示失败: {e}")
        return False


async def demo_practical_usage():
    """演示实际使用场景"""
    print("\n=== 实际使用场景演示 ===")
    
    try:
        print("\n--- 场景1：数据查询 ---")
        
        # 获取客户端并执行查询
        mysql_client = await get_client("database.rdbs.mysql")
        
        # 模拟实际业务查询（使用异步方法）
        # 使用真实场景查询
        test_queries = [
            ("数据库信息", "SELECT DATABASE() as current_db, VERSION() as version"),
            ("表统计", "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = DATABASE()"),
            ("表详情", """
                SELECT table_name,
                       COALESCE(table_rows, 0) as table_rows,
                       COALESCE(data_length, 0) as data_length,
                       COALESCE(index_length, 0) as index_length
                FROM information_schema.tables
                WHERE table_schema = DATABASE()
                  AND table_type = 'BASE TABLE'
                ORDER BY COALESCE(data_length, 0) DESC
                LIMIT 5
            """),
            ("连接信息", "SELECT CONNECTION_ID() as connection_id, @@version_comment as server_info")
        ]

        for name, query in test_queries:
            try:
                if hasattr(mysql_client, 'afetch_all'):
                    result = await mysql_client.afetch_all(query.strip())
                    if name == "表详情" and result.data:
                        print(f"✅ {name}: 前5个最大的表")
                        for row in result.data:
                            table_name = row.get('table_name', 'N/A')
                            table_rows = row.get('table_rows', 0) or 0
                            data_length = row.get('data_length', 0) or 0
                            size_mb = data_length / 1024 / 1024
                            print(f"   - {table_name}: {table_rows} 行, {size_mb:.2f}MB")
                    else:
                        print(f"✅ {name}: {result.data}")
                else:
                    print(f"⚠️ 查询跳过: {name} -> 客户端不支持原生SQL方法")
            except Exception as e:
                print(f"⚠️ 查询失败: {name} -> {e}")
        
        print("\n--- 场景2：配置驱动的客户端选择 ---")
        
        # 根据配置选择不同的客户端
        cfg = await get_config("mixed")
        
        # 模拟根据环境选择不同数据库
        database_configs = {
            "mysql": "database.rdbs.mysql",
            # "postgres": "database.rdbs.postgres",  # 如果有的话
            # "pgvector": "database.vdbs.pgvector"   # 如果有的话
        }
        
        for db_name, config_path in database_configs.items():
            try:
                client = await get_client(config_path)
                print(f"✅ {db_name}客户端可用: {type(client).__name__}")
            except Exception as e:
                print(f"⚠️ {db_name}客户端不可用: {e}")
        
        print("\n--- 场景3：连接池信息 ---")
        
        # 检查连接池状态（如果客户端支持）
        mysql_client = await get_client("database.rdbs.mysql")
        if hasattr(mysql_client, 'get_pool_status'):
            try:
                pool_status = mysql_client.get_pool_status()
                print(f"✅ 连接池状态: {pool_status}")
            except Exception as e:
                print(f"⚠️ 连接池状态获取失败: {e}")
        else:
            print("ℹ️ 客户端不支持连接池状态查询")
        
        return True
        
    except Exception as e:
        print(f"❌ 实际使用场景演示失败: {e}")
        return False


async def demo_error_handling():
    """演示错误处理"""
    print("\n=== 错误处理演示 ===")
    
    try:
        print("\n--- 错误处理最佳实践 ---")
        
        # 1. 客户端获取错误处理
        try:
            invalid_client = await get_client("database.invalid.config")
        except Exception as e:
            print(f"✅ 正确捕获无效配置错误: {type(e).__name__}")
        
        # 2. 查询错误处理
        try:
            mysql_client = await get_client("database.rdbs.mysql")
            if hasattr(mysql_client, 'afetch_all'):
                result = await mysql_client.afetch_all("SELECT * FROM non_existent_table")
            else:
                raise Exception("客户端不支持原生SQL查询")
        except Exception as e:
            print(f"✅ 正确捕获查询错误: {type(e).__name__}")
        
        # 3. 配置错误处理
        try:
            cfg = await get_config("invalid_mode")
        except Exception as e:
            print(f"✅ 正确捕获配置模式错误: {type(e).__name__}")
        
        # 4. 推荐的错误处理模式
        print("\n--- 推荐的错误处理模式 ---")
        
        async def safe_database_operation():
            """安全的数据库操作示例"""
            try:
                # 获取客户端
                client = await get_client("database.rdbs.mysql")
                
                # 执行操作（使用异步方法）
                if hasattr(client, 'afetch_all'):
                    result = await client.afetch_all("SELECT 1 as test")
                    return {"success": True, "data": result.data}
                else:
                    return {"success": True, "data": "客户端不支持原生SQL查询"}
                
            except Exception as e:
                # 记录错误并返回友好的错误信息
                print(f"数据库操作失败: {e}")
                return {"success": False, "error": str(e)}
        
        result = await safe_database_operation()
        if result["success"]:
            print(f"✅ 安全操作成功: {result['data']}")
        else:
            print(f"⚠️ 安全操作失败: {result['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理演示失败: {e}")
        return False

async def llm_practical_usage():
    """演示实际使用场景"""
    print("\n=== 模型服务使用场景演示 ===")

    try:
        print("\n--- 场景1：Embedding模型测试 ---")

        # 获取embedding客户端
        try:
            embed_provider = await get_client("model.embeddings.moka-m3e-base")
            print(f"✅ Embedding客户端获取成功: {type(embed_provider).__name__}")

            # 添加超时控制的embedding调用
            print("   正在调用embedding API...")
            try:
                # 使用asyncio.wait_for添加超时控制
                embedding_result = await asyncio.wait_for(
                    embed_provider.ainvoke(texts=["测试文本"]),
                    timeout=30.0  # 30秒超时
                )
                print(f"✅ 嵌入调用成功: 向量维度={len(embedding_result.embeddings[0])}")
                print(f"   前5个向量值: {embedding_result.embeddings[0][:5]}")
            except asyncio.TimeoutError:
                print("⚠️ Embedding API调用超时（30秒），可能是网络问题")
            except Exception as e:
                print(f"⚠️ Embedding API调用失败: {e}")

        except Exception as e:
            print(f"⚠️ Embedding客户端获取失败: {e}")

        print("\n--- 场景2：LLM模型测试 ---")

        # 获取LLM客户端
        try:
            llm_provider = await get_client("model.llms.opentrek")
            print(f"✅ LLM客户端获取成功: {type(llm_provider).__name__}")

            # 测试LLM调用（非流式）
            try:
                from base.model_serve.model_runtime.entities import PromptMessage
                prompt_messages = [PromptMessage(role='user', content='你好，请简单介绍一下自己')]

                print("   正在调用LLM API（非流式）...")
                llm_result = await asyncio.wait_for(
                    llm_provider.ainvoke(prompt_messages=prompt_messages, stream=False),
                    timeout=30.0  # 30秒超时
                )
                print(f"✅ LLM调用成功: {llm_result.message.content[:100]}...")

                # 测试LLM流式调用
                print("\n   正在测试流式调用...")
                stream_result = await asyncio.wait_for(
                    llm_provider.ainvoke(prompt_messages=prompt_messages, stream=True),
                    timeout=30.0
                )
                print(f"✅ LLM流式结果类型: {type(stream_result).__name__}")

                # 如果是流式结果，读取前几个chunk
                if hasattr(stream_result, '__aiter__'):
                    chunk_count = 0
                    try:
                        async for chunk in stream_result:
                            if chunk_count < 3:  # 只读取前3个chunk
                                print(f"   Chunk {chunk_count + 1}: {type(chunk).__name__}")
                                chunk_count += 1
                            else:
                                break
                    except Exception as e:
                        print(f"   ⚠️ 流式读取部分失败: {e}")

            except asyncio.TimeoutError:
                print("⚠️ LLM API调用超时（30秒），可能是网络问题")
            except Exception as e:
                print(f"⚠️ LLM API调用失败: {e}")

        except Exception as e:
            print(f"⚠️ LLM客户端获取失败: {e}")

        print("\n--- 场景3：模型服务状态检查 ---")

        # 检查模型服务的配置和状态
        try:
            cfg = await get_config("mixed")

            # 检查embedding配置
            try:
                embed_config = cfg.model.embeddings
                print(f"✅ Embedding配置检查: 找到 {len(embed_config)} 个embedding模型配置")
            except Exception as e:
                print(f"⚠️ Embedding配置检查失败: {e}")

            # 检查LLM配置
            try:
                llm_config = cfg.model.llms
                print(f"✅ LLM配置检查: 找到 {len(llm_config)} 个LLM模型配置")
            except Exception as e:
                print(f"⚠️ LLM配置检查失败: {e}")

        except Exception as e:
            print(f"⚠️ 配置检查失败: {e}")

        return True

    except Exception as e:
        print(f"❌ 模型服务演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False
async def main():
    """主演示函数"""
    print("🚀 Service Layer - 简单使用演示")
    print("=" * 50)

    try:

        # 运行各项演示
        demo_results = []

        demo_results.append(await demo_simple_client_usage())
        demo_results.append(await demo_config_usage())
        demo_results.append(await demo_practical_usage())
        demo_results.append(await demo_error_handling())

        # 根据网络连接情况决定是否运行模型测试

        demo_results.append(await llm_practical_usage())
        
        # 统计结果
        passed = sum(demo_results)
        total = len(demo_results)
        
        print("\n" + "=" * 50)
        print("📊 演示结果总结:")
        print(f"✅ 简单客户端使用: {'成功' if demo_results[0] else '失败'}")
        print(f"✅ 配置管理器使用: {'成功' if demo_results[1] else '失败'}")
        print(f"✅ 实际使用场景: {'成功' if demo_results[2] else '失败'}")
        print(f"✅ 错误处理: {'成功' if demo_results[3] else '失败'}")
        print(f"✅ 模型服务使用: {'成功' if demo_results[4] else '失败'}")
        
        print(f"\n🎯 总体结果: {passed}/{total} 演示成功")
        
        if passed >= 3:  # 允许一些演示因为配置问题失败
            print("\n🎉 Service Layer使用演示成功！")
            print("💡 核心优势:")
            print("   ✅ 极简API - get_client() 一行代码搞定")
            print("   ✅ 统一接口 - 所有数据库都是相同的使用方式")
            print("   ✅ 自动管理 - 连接池、配置、错误处理都自动处理")
            print("   ✅ 企业级 - 支持多环境、多租户、动态配置")
        else:
            print(f"\n⚠️ 部分演示失败，但核心功能正常")
        
        print("\n📚 更多示例:")
        print("   - service/examples/client/ - 客户端使用示例")
        print("   - service/examples/config/ - 动态配置示例")
        print("   - service/examples/provider/ - 连接池管理示例")

    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源，避免连接池警告
        try:
            await cleanup()
        except Exception as e:
            print(f"⚠️ 资源清理警告: {e}")


if __name__ == "__main__":
    asyncio.run(main())

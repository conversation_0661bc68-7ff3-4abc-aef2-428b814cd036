#!/usr/bin/env python3
"""
数据库表结构检查

检查真实数据库中的表结构，找出业务表的实际名称和结构
"""

import asyncio
import logging
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '../../..')
sys.path.insert(0, src_dir)

from service import get_client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def inspect_database_tables():
    """检查数据库表结构"""
    try:
        # 获取MySQL客户端
        mysql_client = await get_client('database.rdbs.mysql')
        logger.info(f"✅ MySQL客户端获取成功: {type(mysql_client).__name__}")
        
        # 1. 查看所有表
        logger.info("\n" + "="*60)
        logger.info("📋 查看数据库中的所有表")
        logger.info("="*60)
        
        tables_sql = "SHOW TABLES"
        logger.info(f"输入：{tables_sql}")
        logger.info(f"使用方法：mysql_client.afetch_all()")
        logger.info(f"期望结果：获取数据库中所有表的列表")

        tables_result = await mysql_client.afetch_all(tables_sql)
        logger.info(f"得到结果类型：{type(tables_result)}")
        logger.info(f"得到结果内容：{tables_result}")

        # 处理不同的返回格式
        tables = []
        if isinstance(tables_result, list):
            tables = tables_result
            logger.info("✅ 直接获取到表列表")
        elif hasattr(tables_result, 'data'):
            # QueryResponse对象
            tables = tables_result.data if tables_result.data else []
            logger.info(f"✅ 从QueryResponse中获取到表列表: {len(tables)}个")
        elif hasattr(tables_result, 'success'):
            if tables_result.success and hasattr(tables_result, 'data'):
                tables = tables_result.data if tables_result.data else []
                logger.info(f"✅ 从success响应中获取到表列表: {len(tables)}个")
            else:
                logger.error(f"❌ 查询失败: success={getattr(tables_result, 'success', 'N/A')}")
                return
        else:
            logger.error(f"❌ 无法解析表列表，返回类型: {type(tables_result)}")
            return
        
        logger.info(f"📊 数据库中共有 {len(tables)} 个表:")
        for i, table in enumerate(tables, 1):
            table_name = list(table.values())[0] if isinstance(table, dict) else str(table)
            logger.info(f"  {i:2d}. {table_name}")
        
        # 2. 查找DD相关的表
        logger.info("\n" + "="*60)
        logger.info("🔍 查找DD相关的表")
        logger.info("="*60)
        
        dd_tables = []
        for table in tables:
            table_name = list(table.values())[0] if isinstance(table, dict) else str(table)
            if 'dd' in table_name.lower() or 'biz' in table_name.lower():
                dd_tables.append(table_name)
        
        logger.info(f"📋 找到 {len(dd_tables)} 个DD/业务相关表:")
        for i, table_name in enumerate(dd_tables, 1):
            logger.info(f"  {i:2d}. {table_name}")
        
        # 3. 检查具体表结构
        target_tables = [
            'biz_dd_pre',
            'biz_dd_post_distribution', 
            'dd_submission',
            'dd_departments',
            'dd_report_data',
            'dd_department_relation'
        ]
        
        logger.info("\n" + "="*60)
        logger.info("🔍 检查目标表是否存在")
        logger.info("="*60)
        
        existing_tables = {}
        for target_table in target_tables:
            logger.info(f"\n📋 检查表: {target_table}")
            
            # 检查表是否存在
            check_sql = f"SHOW TABLES LIKE '{target_table}'"
            check_result = await mysql_client.afetch_all(check_sql)
            
            if isinstance(check_result, list):
                exists = len(check_result) > 0
            elif hasattr(check_result, 'success'):
                exists = check_result.success and len(check_result.data) > 0
            else:
                exists = False
            
            if exists:
                logger.info(f"  ✅ 表 {target_table} 存在")
                existing_tables[target_table] = True
                
                # 获取表结构
                desc_sql = f"DESCRIBE {target_table}"
                try:
                    desc_result = await mysql_client.afetch_all(desc_sql)
                    if isinstance(desc_result, list):
                        columns = desc_result
                    elif hasattr(desc_result, 'success') and desc_result.success:
                        columns = desc_result.data
                    else:
                        columns = []
                    
                    logger.info(f"  📊 表结构 ({len(columns)} 个字段):")
                    for col in columns[:5]:  # 只显示前5个字段
                        if isinstance(col, dict):
                            field_name = col.get('Field', 'Unknown')
                            field_type = col.get('Type', 'Unknown')
                            logger.info(f"    - {field_name}: {field_type}")
                    
                    if len(columns) > 5:
                        logger.info(f"    ... 还有 {len(columns) - 5} 个字段")
                        
                except Exception as e:
                    logger.warning(f"  ⚠️ 无法获取表结构: {e}")
                
                # 获取数据量
                count_sql = f"SELECT COUNT(*) as count FROM {target_table}"
                try:
                    count_result = await mysql_client.afetch_one(count_sql)
                    if isinstance(count_result, dict):
                        count = count_result.get('count', 0)
                    elif hasattr(count_result, 'success') and count_result.success:
                        count = count_result.data.get('count', 0)
                    else:
                        count = 0
                    
                    logger.info(f"  📈 数据量: {count} 条记录")
                    
                except Exception as e:
                    logger.warning(f"  ⚠️ 无法获取数据量: {e}")
                    
            else:
                logger.info(f"  ❌ 表 {target_table} 不存在")
                existing_tables[target_table] = False
        
        # 4. 查找相似名称的表
        logger.info("\n" + "="*60)
        logger.info("🔍 查找相似名称的表")
        logger.info("="*60)
        
        all_table_names = []
        for table in tables:
            table_name = list(table.values())[0] if isinstance(table, dict) else str(table)
            all_table_names.append(table_name)
        
        for target_table in target_tables:
            if not existing_tables.get(target_table, False):
                logger.info(f"\n🔍 查找与 '{target_table}' 相似的表:")
                similar_tables = []
                
                for table_name in all_table_names:
                    # 检查是否包含关键词
                    keywords = target_table.split('_')
                    similarity_score = 0
                    for keyword in keywords:
                        if keyword.lower() in table_name.lower():
                            similarity_score += 1
                    
                    if similarity_score > 0:
                        similar_tables.append((table_name, similarity_score))
                
                # 按相似度排序
                similar_tables.sort(key=lambda x: x[1], reverse=True)
                
                if similar_tables:
                    logger.info(f"  📋 找到 {len(similar_tables)} 个相似表:")
                    for table_name, score in similar_tables[:5]:  # 只显示前5个
                        logger.info(f"    - {table_name} (相似度: {score})")
                else:
                    logger.info(f"  ❌ 未找到相似的表")
        
        # 5. 总结
        logger.info("\n" + "="*60)
        logger.info("📊 检查总结")
        logger.info("="*60)
        
        existing_count = sum(1 for exists in existing_tables.values() if exists)
        total_count = len(existing_tables)
        
        logger.info(f"目标表存在情况: {existing_count}/{total_count}")
        for table_name, exists in existing_tables.items():
            status = "✅ 存在" if exists else "❌ 不存在"
            logger.info(f"  - {table_name}: {status}")
        
        if existing_count == 0:
            logger.warning("⚠️ 所有目标表都不存在，可能需要检查数据库配置或表名")
        elif existing_count < total_count:
            logger.warning(f"⚠️ 有 {total_count - existing_count} 个表不存在，可能影响功能测试")
        else:
            logger.info("🎉 所有目标表都存在，可以进行完整功能测试")
        
        return existing_tables
        
    except Exception as e:
        logger.error(f"❌ 数据库检查失败: {e}")
        return {}


async def main():
    """主函数"""
    logger.info("🔍 开始数据库表结构检查")
    existing_tables = await inspect_database_tables()
    
    if existing_tables:
        logger.info("\n🎯 检查完成，可以根据实际表结构调整测试代码")
    else:
        logger.error("\n❌ 检查失败，请检查数据库连接和权限")


if __name__ == "__main__":
    asyncio.run(main())

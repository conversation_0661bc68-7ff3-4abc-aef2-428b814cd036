"""
向量数据库统一抽象层

提供统一的向量数据库接口，支持多种向量数据库的无缝切换。
基于Milvus设计标准，兼容PGVector、Chroma等数据库。

重构说明：
- 采用分层架构设计，参考RDB的优秀模式
- 核心抽象层在core/目录下
- 保持向后兼容性

主要组件：
- core: 核心抽象层（types, interfaces, models, exceptions）
- adapters: 适配器层
- query: 查询抽象层
- utils: 工具层
- BaseVectorDB: 统一的向量数据库基类（向后兼容）

作者: HSBC Knowledge Team
日期: 2025-01-15
"""

from typing import List, Dict, Any, Optional

# 导入核心抽象层
from .core import *

# 导入向后兼容的基类
from .base_vector_db import BaseVectorDB

# 导入数据模型（从core导入）
from .core.models import (
    FieldType, MetricType, FieldSchema, CollectionSchema,
    Entity, SearchResult, SearchRequest as AnnSearchRequest, QueryRequest,
    GetRequest, InsertRequest, DeleteRequest, HybridSearchRequest,
    create_field_schema, create_collection_schema, create_search_request,
    create_simple_schema, create_simple_search_request
)

# 导入异常类（从core导入）
from .core.exceptions import (
    VectorDBException, ConnectionError, AuthenticationError,
    CollectionNotFoundError, CollectionAlreadyExistsError,
    SchemaError, DataValidationError, SearchError,
    InsertError, DeleteError, UpdateError, QueryError,
    IndexError, ConfigurationError, UnsupportedOperationError,
    TimeoutError, ResourceExhaustedError,
    create_connection_error, create_collection_not_found_error,
    create_search_error, create_insert_error, create_schema_error,
    create_data_validation_error
)

# 导入格式转换器（保持原有位置）
try:
    from .format_converter import (
        FormatConverter, MilvusConverter, PGVectorConverter,
        ChromaConverter, UniversalConverter, create_converter,
        convert_to_standard_format, convert_from_standard_format
    )
except ImportError:
    # 如果格式转换器不存在，提供空实现
    pass

# 版本信息
__version__ = "1.0.0"
__author__ = "HSBC Knowledge Team"

# 导出的公共接口
__all__ = [
    # 核心基类
    "BaseVectorDB",
    
    # 数据模型
    "FieldType", "MetricType", "FieldSchema", "CollectionSchema",
    "Entity", "SearchResult", "AnnSearchRequest", "QueryRequest",
    "GetRequest", "InsertRequest", "DeleteRequest", "HybridSearchRequest",
    "create_field_schema", "create_collection_schema", "create_search_request",
    "validate_entity", "validate_search_result",
    
    # 异常类
    "VectorDBException", "ConnectionError", "AuthenticationError",
    "CollectionNotFoundError", "CollectionAlreadyExistsError",
    "PartitionNotFoundError", "PartitionAlreadyExistsError",
    "SchemaError", "DataValidationError", "SearchError",
    "InsertError", "DeleteError", "UpdateError", "QueryError",
    "IndexError", "ConfigurationError", "UnsupportedOperationError",
    "TimeoutError", "ResourceExhaustedError", "ErrorContext",
    "create_connection_error", "create_collection_not_found_error",
    "create_search_error", "create_insert_error", "create_schema_error",
    "create_data_validation_error", "handle_vector_db_exceptions",
    
    # 格式转换器
    "FormatConverter", "MilvusConverter", "PGVectorConverter",
    "ChromaConverter", "UniversalConverter", "create_converter",
    "convert_to_standard_format", "convert_from_standard_format"
]


def get_version() -> str:
    """获取版本信息"""
    return __version__


def get_supported_databases() -> list:
    """获取支持的数据库列表"""
    return ["milvus", "pgvector", "chroma"]


def create_vector_db(db_type: str, **kwargs) -> BaseVectorDB:
    """
    创建向量数据库实例的工厂函数
    
    Args:
        db_type: 数据库类型 ('milvus', 'pgvector', 'chroma')
        **kwargs: 数据库特定的配置参数
        
    Returns:
        向量数据库实例
        
    Raises:
        ValueError: 不支持的数据库类型
    """
    db_type = db_type.lower()
    
    if db_type == "pgvector":
        # 导入PGVector实现
        from ...implementations.vs.pgvector.unified_client import UnifiedPGVectorClient
        return UnifiedPGVectorClient(**kwargs)
    elif db_type == "milvus":
        # 导入Milvus实现（待实现）
        from ...implementations.vs.milvus.unified_client import UnifiedMilvusClient
        return UnifiedMilvusClient(**kwargs)
    elif db_type == "chroma":
        # 导入Chroma实现（待实现）
        from ...implementations.vs.chroma.unified_client import UnifiedChromaClient
        return UnifiedChromaClient(**kwargs)
    else:
        raise ValueError(f"不支持的数据库类型: {db_type}. 支持的类型: {get_supported_databases()}")


# 便捷函数
def create_simple_schema(collection_name: str, vector_dim: int, 
                        additional_fields: Optional[List[Dict[str, Any]]] = None) -> CollectionSchema:
    """
    创建简单的集合模式
    
    Args:
        collection_name: 集合名称
        vector_dim: 向量维度
        additional_fields: 额外字段定义列表
        
    Returns:
        集合模式
    """
    fields = [
        {"name": "id", "dtype": "INT64", "is_primary": True, "auto_id": True},
        {"name": "embedding", "dtype": "FLOAT_VECTOR", "dim": vector_dim},
        {"name": "content", "dtype": "VARCHAR", "max_length": 65535}
    ]
    
    if additional_fields:
        fields.extend(additional_fields)
    
    return create_collection_schema(fields, description=f"简单集合模式: {collection_name}")


def create_simple_search_request(query_vector: List[float], 
                                anns_field: str = "embedding",
                                limit: int = 10,
                                filters: Optional[str] = None) -> AnnSearchRequest:
    """
    创建简单的搜索请求
    
    Args:
        query_vector: 查询向量
        anns_field: 向量字段名
        limit: 返回结果数量
        filters: 过滤条件
        
    Returns:
        搜索请求
    """
    return create_search_request(
        data=[query_vector],
        anns_field=anns_field,
        limit=limit,
        expr=filters
    )

"""
部门职责分配功能测试

测试完整的部门职责分配流程，包括：
1. 精确匹配测试
2. 混合搜索测试  
3. TF-IDF推荐测试
4. 四层筛选测试
5. 批量处理测试
6. 入库操作测试
"""

import asyncio
import logging
import json
from typing import List, Dict, Any
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from ..core.assignment_engine import DepartmentAssignmentLogic
from ..infrastructure.models import (
    DepartmentAssignmentRequest,
    BatchAssignmentRequest
)
# 引入最新的CRUD实现和表名常量
from modules.knowledge.dd.crud import DDCrud
from modules.knowledge.dd.shared.constants import DDTableNames


class DepartmentAssignmentTester:
    """部门职责分配测试类"""
    
    def __init__(self, rdb_client, vdb_client):
        """
        初始化测试类
        
        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.assignment_logic = DepartmentAssignmentLogic(rdb_client, vdb_client)
        # 初始化CRUD实例用于测试数据准备
        self.dd_crud = DDCrud(rdb_client, vdb_client)
        
    async def prepare_test_data(self):
        """准备测试数据"""
        logger.info("开始准备测试数据...")
        
        # 1. 准备biz_dd_pre_distribution测试数据
        await self._prepare_pre_distribution_data()
        
        # 2. 准备dd_submission_data测试数据
        await self._prepare_submission_data()
        
        # 3. 准备dd_departments测试数据
        await self._prepare_departments_data()
        
        logger.info("测试数据准备完成")
    
    async def _prepare_pre_distribution_data(self):
        """准备分发前数据"""
        logger.info("准备biz_dd_pre_distribution测试数据...")
        
        # 清理现有测试数据
        await self.rdb_client.aexecute(
            "DELETE FROM biz_dd_pre_distribution WHERE dr07 = %s AND version = %s",
            ("g0107", "v1.0")
        )
        
        # 插入测试数据
        test_records = [
            {
                "submission_id": "TEST_001",
                "dr07": "g0107",
                "version": "v1.0",
                "dr09": "银行存款余额",
                "dr17": "银行存款账户的期末余额，包括活期存款和定期存款",
                "set": "SET_A",
                "report_type": "detail",
                "submission_type": "submission",
                "dr01": "ADS"
            },
            {
                "submission_id": "TEST_002", 
                "dr07": "g0107",
                "version": "v1.0",
                "dr09": "贷款总额",
                "dr17": "各类贷款的总金额，包括个人贷款和企业贷款",
                "set": "SET_B",
                "report_type": "index",
                "submission_type": "range",
                "dr01": "BDM"
            },
            {
                "submission_id": "TEST_003",
                "dr07": "g0107", 
                "version": "v1.0",
                "dr09": "风险资产",
                "dr17": "具有潜在损失风险的资产项目",
                "set": "SET_C",
                "report_type": "detail",
                "submission_type": "submission",
                "dr01": "IDM"
            },
            {
                "submission_id": "TEST_004",
                "dr07": "g0107",
                "version": "v1.0", 
                "dr09": "调查问卷数据",
                "dr17": "客户满意度调查问卷收集的数据",
                "set": "survey",
                "report_type": "detail",
                "submission_type": "submission",
                "dr01": "ODS"
            }
        ]
        
        for record in test_records:
            await self.rdb_client.aexecute(
                """
                INSERT INTO biz_dd_pre_distribution 
                (submission_id, dr07, version, dr09, dr17, `set`, report_type, submission_type, dr01)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (
                    record["submission_id"], record["dr07"], record["version"],
                    record["dr09"], record["dr17"], record["set"],
                    record["report_type"], record["submission_type"], record["dr01"]
                )
            )
        
        logger.info(f"插入了{len(test_records)}条biz_dd_pre_distribution测试数据")
    
    async def _prepare_submission_data(self):
        """准备dd_submission_data测试数据"""
        logger.info("准备dd_submission_data测试数据...")
        
        # 清理现有测试数据
        await self.rdb_client.aexecute(
            "DELETE FROM dd_submission_data WHERE submission_id LIKE 'HIST_%'"
        )
        
        # 插入历史匹配数据
        history_records = [
            {
                "submission_id": "HIST_001",
                "dr09": "银行存款余额",
                "dr17": "银行存款账户的期末余额，包括活期存款和定期存款",
                "dr22": "DEPT_FINANCE",
                "type": "submission",
                "dr01": "ADS"
            },
            {
                "submission_id": "HIST_002",
                "dr09": "贷款相关数据",
                "dr17": "各类贷款业务的相关统计数据",
                "dr22": "DEPT_CREDIT",
                "type": "range", 
                "dr01": "BDM"
            },
            {
                "submission_id": "HIST_003",
                "dr09": "风险管理指标",
                "dr17": "用于风险控制和管理的各项指标数据",
                "dr22": "DEPT_RISK",
                "type": "submission",
                "dr01": "IDM"
            }
        ]
        
        for record in history_records:
            await self.rdb_client.aexecute(
                """
                INSERT INTO dd_submission_data 
                (submission_id, dr09, dr17, dr22, type, dr01)
                VALUES (%s, %s, %s, %s, %s, %s)
                """,
                (
                    record["submission_id"], record["dr09"], record["dr17"],
                    record["dr22"], record["type"], record["dr01"]
                )
            )
        
        logger.info(f"插入了{len(history_records)}条dd_submission_data测试数据")
    
    async def _prepare_departments_data(self):
        """准备dd_departments测试数据"""
        logger.info("准备dd_departments测试数据...")
        
        # 清理现有测试数据
        await self.rdb_client.aexecute(
            "DELETE FROM dd_departments WHERE dept_id LIKE 'DEPT_%'"
        )
        
        # 插入部门数据
        dept_records = [
            {
                "dept_id": "DEPT_FINANCE",
                "dept_name": "财务部",
                "dept_desc": "负责银行存款、资金管理、财务报表等财务相关业务"
            },
            {
                "dept_id": "DEPT_CREDIT", 
                "dept_name": "信贷部",
                "dept_desc": "负责贷款业务、信贷管理、放贷审批等信贷相关业务"
            },
            {
                "dept_id": "DEPT_RISK",
                "dept_name": "风险管理部",
                "dept_desc": "负责风险评估、风险控制、风险监测等风险管理业务"
            },
            {
                "dept_id": "DEPT_BUSINESS",
                "dept_name": "业务部",
                "dept_desc": "负责一般业务操作、客户服务、业务流程管理"
            }
        ]
        
        for record in dept_records:
            await self.rdb_client.aexecute(
                """
                INSERT INTO dd_departments (dept_id, dept_name, dept_desc)
                VALUES (%s, %s, %s)
                """,
                (record["dept_id"], record["dept_name"], record["dept_desc"])
            )
        
        logger.info(f"插入了{len(dept_records)}条dd_departments测试数据")

    async def test_exact_match_scenario(self):
        """测试精确匹配场景"""
        logger.info("=" * 50)
        logger.info("测试场景1: 精确匹配")
        logger.info("=" * 50)

        # 创建精确匹配请求
        request = DepartmentAssignmentRequest(
            submission_id="TEST_001",
            dr09="银行存款余额",
            dr17="银行存款账户的期末余额，包括活期存款和定期存款",
            set_value="SET_A",
            report_type="detail",
            submission_type="submission",
            dr01="ADS"
        )

        # 执行分配
        result = await self.assignment_logic.assign_department(request)

        # 验证结果
        logger.info(f"精确匹配结果:")
        logger.info(f"  推荐部门: {result.recommended_department}")
        logger.info(f"  置信度: {result.confidence_level}")
        logger.info(f"  精确匹配: {result.exact_match_found}")
        logger.info(f"  最终记录数: {len(result.final_records)}")

        assert result.exact_match_found, "应该找到精确匹配"
        assert result.recommended_department, "应该有推荐部门"

        return result

    async def test_hybrid_search_scenario(self):
        """测试混合搜索场景"""
        logger.info("=" * 50)
        logger.info("测试场景2: 混合搜索")
        logger.info("=" * 50)

        # 创建混合搜索请求（不会精确匹配）
        request = DepartmentAssignmentRequest(
            submission_id="TEST_002",
            dr09="贷款总额",
            dr17="各类贷款的总金额，包括个人贷款和企业贷款",
            set_value="SET_B",
            report_type="index",
            submission_type="range",
            dr01="BDM"
        )

        # 执行分配
        result = await self.assignment_logic.assign_department(request)

        # 验证结果
        logger.info(f"混合搜索结果:")
        logger.info(f"  推荐部门: {result.recommended_department}")
        logger.info(f"  置信度: {result.confidence_level}")
        logger.info(f"  精确匹配: {result.exact_match_found}")
        logger.info(f"  混合搜索数量: {result.hybrid_search_count}")
        logger.info(f"  最终记录数: {len(result.final_records)}")

        assert result.recommended_department, "应该有推荐部门"

        return result

    async def test_tfidf_recommendation_scenario(self):
        """测试TF-IDF推荐场景"""
        logger.info("=" * 50)
        logger.info("测试场景3: TF-IDF推荐")
        logger.info("=" * 50)

        # 创建TF-IDF推荐请求（不会匹配历史记录）
        request = DepartmentAssignmentRequest(
            submission_id="TEST_004",
            dr09="调查问卷数据",
            dr17="客户满意度调查问卷收集的数据",
            set_value="survey",
            report_type="detail",
            submission_type="submission",
            dr01="ODS"
        )

        # 执行分配
        result = await self.assignment_logic.assign_department(request)

        # 验证结果
        logger.info(f"TF-IDF推荐结果:")
        logger.info(f"  推荐部门: {result.recommended_department}")
        logger.info(f"  置信度: {result.confidence_level}")
        logger.info(f"  精确匹配: {result.exact_match_found}")
        logger.info(f"  混合搜索数量: {result.hybrid_search_count}")
        logger.info(f"  部门推荐数量: {len(result.department_recommendations)}")

        assert result.recommended_department, "应该有推荐部门"

        return result

    async def test_batch_processing(self):
        """测试批量处理"""
        logger.info("=" * 50)
        logger.info("测试场景4: 批量处理")
        logger.info("=" * 50)

        # 创建批量请求
        batch_request = BatchAssignmentRequest(
            report_code="g0107_beta_v1.0"
        )

        # 执行批量分配并保存
        result = await self.assignment_logic.batch_assign_and_save(batch_request)

        # 验证结果
        logger.info(f"批量处理结果:")
        logger.info(f"  成功: {result['success']}")
        logger.info(f"  总记录数: {result.get('processing_summary', {}).get('total_records', 0)}")
        logger.info(f"  成功分配数: {result.get('processing_summary', {}).get('successful_assignments', 0)}")
        logger.info(f"  失败分配数: {result.get('processing_summary', {}).get('failed_assignments', 0)}")
        logger.info(f"  分配成功率: {result.get('processing_summary', {}).get('assignment_success_rate', 0):.2%}")

        # 验证输出格式
        if result.get('batch_result') and result['batch_result'].assignment_items:
            logger.info("输出格式验证:")
            for i, item in enumerate(result['batch_result'].assignment_items[:3]):  # 只显示前3个
                logger.info(f"  项目{i+1}:")
                logger.info(f"    entry_id: {item.entry_id}")
                logger.info(f"    entry_type: {item.entry_type}")
                logger.info(f"    DR22: {item.DR22}")
                logger.info(f"    BDR01: {item.BDR01}")
                logger.info(f"    BDR03: '{item.BDR03}'")

        assert result['success'], "批量处理应该成功"

        return result

    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行部门职责分配功能测试")

        try:
            # 1. 准备测试数据
            await self.prepare_test_data()

            # 2. 运行各种测试场景
            exact_result = await self.test_exact_match_scenario()
            hybrid_result = await self.test_hybrid_search_scenario()
            tfidf_result = await self.test_tfidf_recommendation_scenario()
            batch_result = await self.test_batch_processing()

            # 3. 生成测试报告
            await self._generate_test_report([
                ("精确匹配", exact_result),
                ("混合搜索", hybrid_result),
                ("TF-IDF推荐", tfidf_result),
                ("批量处理", batch_result)
            ])

            logger.info("所有测试完成！")

        except Exception as e:
            logger.error(f"测试失败: {e}")
            raise

    async def _generate_test_report(self, test_results):
        """生成测试报告"""
        logger.info("=" * 50)
        logger.info("测试报告汇总")
        logger.info("=" * 50)

        for test_name, result in test_results:
            if test_name == "批量处理":
                logger.info(f"{test_name}: {'成功' if result.get('success') else '失败'}")
            else:
                logger.info(f"{test_name}: {'成功' if result.recommended_department else '失败'}")


# 测试运行函数
async def run_department_assignment_tests(rdb_client, vdb_client):
    """运行部门职责分配测试"""
    tester = DepartmentAssignmentTester(rdb_client, vdb_client)
    await tester.run_all_tests()


if __name__ == "__main__":
    # 这里需要根据实际情况初始化数据库客户端
    # rdb_client = YourRDBClient()
    # vdb_client = YourVDBClient()
    # asyncio.run(run_department_assignment_tests(rdb_client, vdb_client))
    print("请在实际环境中运行测试，需要提供数据库客户端实例")

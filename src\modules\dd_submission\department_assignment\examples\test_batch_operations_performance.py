#!/usr/bin/env python3
"""
DDCrud批量操作性能验证测试

验证批量操作优化的性能提升效果，对比优化前后的性能差异
重点测试1600+条记录的批量处理能力
"""

import asyncio
import time
import logging
from datetime import datetime
from typing import List, Dict, Any
import random
import string

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d - %(message)s'
)
logger = logging.getLogger(__name__)

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from service import get_client
from modules.dd_submission.department_assignment.core.database_operations import PostDistributionDBOperations
from modules.dd_submission.department_assignment.infrastructure.models import BatchAssignmentItem, BatchProcessingResult


class BatchOperationsPerformanceTest:
    """批量操作性能测试类"""
    
    def __init__(self):
        self.rdb_client = None
        self.vdb_client = None
        self.db_ops = None
        
    async def initialize(self):
        """初始化测试环境"""
        try:
            # 获取客户端
            self.rdb_client = await get_client('database.rdbs.mysql')
            
            try:
                self.vdb_client = await get_client('database.vdbs.pgvector')
            except:
                logger.warning("向量数据库客户端初始化失败，使用None")
                self.vdb_client = None
            
            # 创建数据库操作实例
            self.db_ops = PostDistributionDBOperations(self.rdb_client, self.vdb_client)
            
            logger.info("✅ 测试环境初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试环境初始化失败: {e}")
            return False

    def generate_test_data(self, record_count: int = 1600) -> tuple[List[Dict[str, Any]], List[BatchAssignmentItem]]:
        """
        生成测试数据
        
        Args:
            record_count: 生成的记录数量
            
        Returns:
            (pre_distribution_records, assignment_items)
        """
        logger.info(f"生成{record_count}条测试数据...")
        
        pre_distribution_records = []
        assignment_items = []
        
        for i in range(record_count):
            # 生成唯一的submission_id
            submission_id = f"PERF_TEST_{i:06d}_{int(time.time())}"
            
            # 生成分发前记录
            pre_record = {
                'id': i + 1,
                'submission_id': submission_id,
                'submission_type': 'REGULAR',
                'report_type': 'MONTHLY',
                'set': f'SET_{i % 10}',
                'version': 'v1.0',
                'dr01': 'ADS',
                'dr07': 'PERF_TEST_TABLE',
                'create_time': datetime.now(),
                'update_time': datetime.now()
            }
            pre_distribution_records.append(pre_record)
            
            # 生成分配结果项
            assignment_item = BatchAssignmentItem(
                entry_id=submission_id,
                entry_type='REGULAR',  # 添加必需的entry_type参数
                DR22=[f"DEPT_{i % 5}", "DEPT_BUSINESS"],
                BDR01=[f"DEPT_PROD_{i % 3}", "DEPT_IT"],
                BDR03=f"数据管理部门_{i % 4}"
            )
            assignment_items.append(assignment_item)
        
        logger.info(f"✅ 生成测试数据完成: {record_count}条记录")
        return pre_distribution_records, assignment_items

    async def test_legacy_single_operations(
        self, 
        pre_records: List[Dict[str, Any]], 
        assignment_items: List[BatchAssignmentItem]
    ) -> Dict[str, Any]:
        """
        测试传统的单条操作性能（模拟优化前的N+1查询）
        
        Args:
            pre_records: 分发前记录列表
            assignment_items: 分配结果项列表
            
        Returns:
            性能测试结果
        """
        logger.info("🔍 开始测试传统单条操作性能...")
        
        start_time = time.time()
        success_count = 0
        error_count = 0
        
        try:
            # 模拟传统的逐条处理
            for i, (pre_record, assignment_item) in enumerate(zip(pre_records, assignment_items)):
                try:
                    # 构建保存数据
                    save_data = {
                        'pre_distribution_id': pre_record.get('id'),
                        'submission_id': assignment_item.entry_id,
                        'submission_type': pre_record.get('submission_type', 'REGULAR'),
                        'report_type': pre_record.get('report_type', 'MONTHLY'),
                        'set': pre_record.get('set'),
                        'version': pre_record.get('version', 'v1.0'),
                        'dept_id': assignment_item.DR22[0] if assignment_item.DR22 else 'DEPT_BUSINESS',
                        'create_time': datetime.now(),
                        'update_time': datetime.now(),
                        'dr01': pre_record.get('dr01'),
                        'dr07': pre_record.get('dr07'),
                        'dr22': assignment_item.DR22[0] if assignment_item.DR22 else 'DEPT_BUSINESS',
                        'bdr01': assignment_item.BDR01[0] if assignment_item.BDR01 else 'DEPT_BUSINESS',
                        'bdr03': assignment_item.BDR03 or '智能推荐结果'
                    }
                    
                    # 单条插入（模拟N+1查询）
                    await self.db_ops.dd_crud.create_post_distribution(save_data)
                    success_count += 1
                    
                    # 每100条记录输出一次进度
                    if (i + 1) % 100 == 0:
                        elapsed = time.time() - start_time
                        logger.info(f"传统方式进度: {i + 1}/{len(pre_records)}, 耗时: {elapsed:.2f}秒")
                    
                except Exception as e:
                    error_count += 1
                    if error_count <= 5:  # 只记录前5个错误
                        logger.warning(f"传统方式单条操作失败: {e}")
        
        except Exception as e:
            logger.error(f"传统方式测试异常: {e}")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        result = {
            'method': 'legacy_single_operations',
            'total_records': len(pre_records),
            'success_count': success_count,
            'error_count': error_count,
            'total_time_seconds': total_time,
            'records_per_second': success_count / total_time if total_time > 0 else 0,
            'average_time_per_record_ms': (total_time * 1000) / success_count if success_count > 0 else 0
        }
        
        logger.info(f"📊 传统单条操作测试完成:")
        logger.info(f"   总记录数: {result['total_records']}")
        logger.info(f"   成功数: {result['success_count']}")
        logger.info(f"   失败数: {result['error_count']}")
        logger.info(f"   总耗时: {result['total_time_seconds']:.2f}秒")
        logger.info(f"   处理速度: {result['records_per_second']:.2f}条/秒")
        logger.info(f"   平均每条: {result['average_time_per_record_ms']:.2f}毫秒")
        
        return result

    async def test_optimized_batch_operations(
        self, 
        pre_records: List[Dict[str, Any]], 
        assignment_items: List[BatchAssignmentItem]
    ) -> Dict[str, Any]:
        """
        测试优化后的批量操作性能
        
        Args:
            pre_records: 分发前记录列表
            assignment_items: 分配结果项列表
            
        Returns:
            性能测试结果
        """
        logger.info("🚀 开始测试优化后批量操作性能...")
        
        start_time = time.time()
        
        try:
            # 创建批量处理结果
            batch_result = BatchProcessingResult(
                dr07="PERF_TEST_TABLE",
                version="v1.0",
                assignment_items=assignment_items,
                processing_time_ms=0.0,
                total_items=len(assignment_items),
                successful_assignments=len(assignment_items),
                failed_assignments=0
            )
            
            # 使用优化后的批量保存方法
            save_stats = await self.db_ops.save_batch_results(batch_result, pre_records)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            result = {
                'method': 'optimized_batch_operations',
                'total_records': save_stats['total_items'],
                'success_count': save_stats['successful_saves'],
                'error_count': save_stats['failed_saves'],
                'total_time_seconds': total_time,
                'records_per_second': save_stats['successful_saves'] / total_time if total_time > 0 else 0,
                'average_time_per_record_ms': (total_time * 1000) / save_stats['successful_saves'] if save_stats['successful_saves'] > 0 else 0,
                'save_stats': save_stats
            }
            
            logger.info(f"📊 优化批量操作测试完成:")
            logger.info(f"   总记录数: {result['total_records']}")
            logger.info(f"   成功数: {result['success_count']}")
            logger.info(f"   失败数: {result['error_count']}")
            logger.info(f"   总耗时: {result['total_time_seconds']:.2f}秒")
            logger.info(f"   处理速度: {result['records_per_second']:.2f}条/秒")
            logger.info(f"   平均每条: {result['average_time_per_record_ms']:.2f}毫秒")
            
            return result
            
        except Exception as e:
            logger.error(f"优化批量操作测试失败: {e}")
            end_time = time.time()
            return {
                'method': 'optimized_batch_operations',
                'total_records': len(pre_records),
                'success_count': 0,
                'error_count': len(pre_records),
                'total_time_seconds': end_time - start_time,
                'records_per_second': 0,
                'average_time_per_record_ms': 0,
                'error': str(e)
            }

    async def cleanup_test_data(self, test_prefix: str = "PERF_TEST"):
        """
        清理测试数据
        
        Args:
            test_prefix: 测试数据前缀
        """
        try:
            logger.info(f"🧹 开始清理测试数据 (前缀: {test_prefix})...")
            
            # 查询测试数据
            test_records = await self.db_ops.dd_crud.list_post_distributions(
                limit=5000  # 设置足够大的限制
            )
            
            # 过滤出测试数据
            test_records_to_delete = [
                r for r in test_records 
                if r.get('submission_id', '').startswith(test_prefix)
            ]
            
            if test_records_to_delete:
                logger.info(f"找到{len(test_records_to_delete)}条测试数据需要清理")
                
                # 批量删除测试数据
                delete_conditions = [
                    {'submission_id': record['submission_id']} 
                    for record in test_records_to_delete
                ]
                
                result = await self.rdb_client.abatch_delete(
                    table="biz_dd_post_distribution",
                    conditions=delete_conditions,
                    batch_size=500,
                    max_concurrency=3
                )
                
                if result.success:
                    logger.info(f"✅ 成功清理{result.affected_rows}条测试数据")
                else:
                    logger.warning(f"⚠️ 清理测试数据部分失败: {result.error_message}")
            else:
                logger.info("没有找到需要清理的测试数据")
                
        except Exception as e:
            logger.error(f"❌ 清理测试数据失败: {e}")

    def calculate_performance_improvement(
        self, 
        legacy_result: Dict[str, Any], 
        optimized_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        计算性能提升指标
        
        Args:
            legacy_result: 传统方式测试结果
            optimized_result: 优化后测试结果
            
        Returns:
            性能提升分析结果
        """
        if not legacy_result or not optimized_result:
            return {}
        
        # 计算性能提升
        time_improvement = 0
        speed_improvement = 0
        
        if legacy_result['total_time_seconds'] > 0:
            time_improvement = (
                (legacy_result['total_time_seconds'] - optimized_result['total_time_seconds']) 
                / legacy_result['total_time_seconds'] * 100
            )
        
        if legacy_result['records_per_second'] > 0:
            speed_improvement = (
                (optimized_result['records_per_second'] - legacy_result['records_per_second']) 
                / legacy_result['records_per_second'] * 100
            )
        
        improvement_analysis = {
            'legacy_time_seconds': legacy_result['total_time_seconds'],
            'optimized_time_seconds': optimized_result['total_time_seconds'],
            'time_reduction_percentage': time_improvement,
            'legacy_speed_rps': legacy_result['records_per_second'],
            'optimized_speed_rps': optimized_result['records_per_second'],
            'speed_improvement_percentage': speed_improvement,
            'legacy_avg_ms_per_record': legacy_result['average_time_per_record_ms'],
            'optimized_avg_ms_per_record': optimized_result['average_time_per_record_ms'],
            'performance_multiplier': optimized_result['records_per_second'] / legacy_result['records_per_second'] if legacy_result['records_per_second'] > 0 else 0
        }
        
        return improvement_analysis


async def main():
    """主测试函数"""
    print("🚀 DDCrud批量操作性能验证测试")
    print("=" * 80)
    print("测试目标：验证批量操作优化的性能提升效果")
    print("预期提升：处理时间减少90%+，处理速度提升10倍+")
    print("=" * 80)
    
    # 创建测试实例
    test = BatchOperationsPerformanceTest()
    
    # 初始化测试环境
    if not await test.initialize():
        print("❌ 测试环境初始化失败，退出测试")
        return False
    
    # 测试参数
    test_sizes = [100, 500, 1600]  # 不同的测试数据量
    
    all_results = {}
    
    for test_size in test_sizes:
        print(f"\n📊 开始测试 {test_size} 条记录的性能...")
        print("-" * 60)
        
        try:
            # 生成测试数据
            pre_records, assignment_items = test.generate_test_data(test_size)
            
            # 清理可能存在的测试数据
            await test.cleanup_test_data()
            
            # 测试优化后的批量操作（先测试，避免数据冲突）
            optimized_result = await test.test_optimized_batch_operations(pre_records, assignment_items)
            
            # 清理数据，准备传统方式测试
            await test.cleanup_test_data()
            
            # 测试传统的单条操作
            legacy_result = await test.test_legacy_single_operations(pre_records, assignment_items)
            
            # 计算性能提升
            improvement = test.calculate_performance_improvement(legacy_result, optimized_result)
            
            # 保存结果
            all_results[test_size] = {
                'legacy': legacy_result,
                'optimized': optimized_result,
                'improvement': improvement
            }
            
            # 输出对比结果
            print(f"\n📈 {test_size}条记录性能对比:")
            print(f"   传统方式: {legacy_result['total_time_seconds']:.2f}秒, {legacy_result['records_per_second']:.2f}条/秒")
            print(f"   优化方式: {optimized_result['total_time_seconds']:.2f}秒, {optimized_result['records_per_second']:.2f}条/秒")
            print(f"   时间减少: {improvement['time_reduction_percentage']:.1f}%")
            print(f"   速度提升: {improvement['speed_improvement_percentage']:.1f}%")
            print(f"   性能倍数: {improvement['performance_multiplier']:.1f}x")
            
            # 清理测试数据
            await test.cleanup_test_data()
            
        except Exception as e:
            logger.error(f"测试 {test_size} 条记录时发生错误: {e}")
            continue
    
    # 输出最终报告
    print("\n" + "=" * 80)
    print("📊 DDCrud批量操作性能测试报告")
    print("=" * 80)
    
    for test_size, results in all_results.items():
        improvement = results['improvement']
        print(f"\n🎯 {test_size}条记录测试结果:")
        print(f"   时间优化: {improvement['legacy_time_seconds']:.2f}s → {improvement['optimized_time_seconds']:.2f}s ({improvement['time_reduction_percentage']:.1f}%减少)")
        print(f"   速度提升: {improvement['legacy_speed_rps']:.1f}条/s → {improvement['optimized_speed_rps']:.1f}条/s ({improvement['performance_multiplier']:.1f}x)")
    
    # 检查是否达到预期目标
    if 1600 in all_results:
        large_test = all_results[1600]['improvement']
        if large_test['time_reduction_percentage'] >= 90:
            print(f"\n🎉 性能优化目标达成！1600条记录处理时间减少{large_test['time_reduction_percentage']:.1f}%")
        else:
            print(f"\n⚠️ 性能优化未完全达到预期，1600条记录处理时间减少{large_test['time_reduction_percentage']:.1f}%")
    
    print("\n✅ 批量操作性能验证测试完成！")
    return True


if __name__ == "__main__":
    asyncio.run(main())

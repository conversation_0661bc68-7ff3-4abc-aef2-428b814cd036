from utils.common.config_util import config
from utils.common.service_client import ServiceClient

# ==================== 全局客户端引用（真正的懒加载）====================
# ServiceClient 现在原生支持延迟加载，只需传递一个获取配置的 lambda 函数。
# 只有当 rdb_client 或 vdb_client 第一次被使用时，lambda 才会被调用，
# 届时 config 对象早已被 Hydra 初始化完毕。

rdb_client = ServiceClient(lambda: config.rdb)
vdb_client = ServiceClient(lambda: config.vdb)

__all__ = [
    'rdb_client', 
    'vdb_client',
]

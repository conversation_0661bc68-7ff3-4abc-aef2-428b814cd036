"""
ORM服务集成

提供与universal服务集成完全相同的接口，用于ORM客户端的管理和集成
"""

import hashlib
import asyncio
from typing import Dict, Any, Optional, Union
import logging

from .factory import (
    create_orm_mysql_client,
    create_orm_postgresql_client,
    create_orm_sqlite_client
)
from .client import ORMSQLAlchemyClient
from .config import ORMConnectionConfig

logger = logging.getLogger(__name__)


class ORMClientManager:
    """ORM客户端的单例管理器"""
    
    _instances: Dict[str, ORMSQLAlchemyClient] = {}
    _lock = asyncio.Lock()
    
    @classmethod
    async def get_client(
        cls,
        config: ORMConnectionConfig,
        pool_id: Optional[str] = None
    ) -> ORMSQLAlchemyClient:
        """
        获取或创建ORM客户端实例
        
        Args:
            config: ORM连接配置
            pool_id: 连接池ID
        
        Returns:
            ORM客户端实例
        """
        # 生成实例键
        config_hash = hashlib.md5(
            f"{config.database_url}_{pool_id}".encode()
        ).hexdigest()
        
        async with cls._lock:
            if config_hash not in cls._instances:
                client = ORMSQLAlchemyClient(config)
                await client.aconnect()
                cls._instances[config_hash] = client
                logger.info(f"Created new ORM client instance: {config_hash}")
            
            return cls._instances[config_hash]
    
    @classmethod
    async def cleanup_client(cls, pool_id: Optional[str] = None):
        """
        清理指定的ORM客户端
        
        Args:
            pool_id: 连接池ID
        """
        async with cls._lock:
            to_remove = []
            for key, client in cls._instances.items():
                if pool_id is None or pool_id in key:
                    try:
                        await client.adisconnect()
                        to_remove.append(key)
                        logger.info(f"Cleaned up ORM client: {key}")
                    except Exception as e:
                        logger.error(f"Error cleaning up ORM client {key}: {e}")
            
            for key in to_remove:
                del cls._instances[key]
    
    @classmethod
    async def cleanup_all(cls):
        """清理所有ORM客户端"""
        async with cls._lock:
            for key, client in list(cls._instances.items()):
                try:
                    await client.adisconnect()
                    logger.info(f"Cleaned up ORM client: {key}")
                except Exception as e:
                    logger.error(f"Error cleaning up ORM client {key}: {e}")
            
            cls._instances.clear()
            logger.info("Cleaned up all ORM clients")
    
    @classmethod
    def get_active_clients_count(cls) -> int:
        """获取活跃客户端数量"""
        return len(cls._instances)
    
    @classmethod
    def get_client_info(cls) -> Dict[str, Dict[str, Any]]:
        """获取所有客户端信息"""
        info = {}
        for key, client in cls._instances.items():
            info[key] = {
                'dialect': client.dialect.name,
                'sync_connected': client.is_sync_connected(),
                'async_connected': client.is_async_connected(),
                'model_registry_initialized': client.model_registry is not None,
                'cached_models': len(client.model_registry.get_registered_tables()) if client.model_registry else 0
            }
        return info


# 管理的客户端创建函数
async def create_managed_orm_mysql_client(
    host: str,
    database: str,
    username: str,
    password: str,
    port: int = 3306,
    pool_id: Optional[str] = None,
    **kwargs
) -> ORMSQLAlchemyClient:
    """创建管理的ORM MySQL客户端"""
    config = ORMConnectionConfig.from_components(
        dialect="mysql",
        host=host,
        database=database,
        username=username,
        password=password,
        port=port,
        **kwargs
    )
    
    return await ORMClientManager.get_client(config, pool_id)


async def create_managed_orm_postgresql_client(
    host: str,
    database: str,
    username: str,
    password: str,
    port: int = 5432,
    pool_id: Optional[str] = None,
    **kwargs
) -> ORMSQLAlchemyClient:
    """创建管理的ORM PostgreSQL客户端"""
    config = ORMConnectionConfig.from_components(
        dialect="postgresql",
        host=host,
        database=database,
        username=username,
        password=password,
        port=port,
        **kwargs
    )
    
    return await ORMClientManager.get_client(config, pool_id)


async def create_managed_orm_sqlite_client(
    database_path: str,
    pool_id: Optional[str] = None,
    **kwargs
) -> ORMSQLAlchemyClient:
    """创建管理的ORM SQLite客户端"""
    database_url = f"sqlite:///{database_path}"
    config = ORMConnectionConfig(database_url=database_url, **kwargs)
    
    return await ORMClientManager.get_client(config, pool_id)


class ORMDynamicProviderMixin:
    """
    Dynamic Provider的ORM扩展Mixin
    
    可以混入到现有的DynamicProvider类中
    """
    
    def _build_cache_key(self, *args) -> str:
        """构建缓存键"""
        return "_".join(str(arg) for arg in args if arg is not None)
    
    def _get_db_config(self, db_alias: str) -> Dict[str, Any]:
        """从配置获取数据库连接参数（需要根据实际配置系统实现）"""
        # 这里是示例实现，需要根据实际配置系统调整
        raise NotImplementedError("需要根据实际配置系统实现")
    
    async def get_orm_rdb(
        self,
        db_type: str = "mysql",
        connection_params: Optional[Dict[str, Any]] = None,
        db_alias: Optional[str] = None,
        pool_id: Optional[str] = None,
        knowledge_id: Optional[str] = None,
        user_id: Optional[str] = None
    ):
        """
        获取ORM RDB客户端
        
        Args:
            db_type: 数据库类型 (mysql, postgresql, sqlite)
            connection_params: 连接参数字典
            db_alias: 数据库别名（从配置中获取）
            pool_id: 连接池ID
            knowledge_id: 知识库ID
            user_id: 用户ID
        
        Returns:
            ORM客户端实例
        """
        
        # 构建缓存键
        cache_key = self._build_cache_key("orm_rdb", db_type, pool_id, knowledge_id, user_id)
        
        # 这里假设有_cache_lock和_client_cache属性
        async with getattr(self, '_cache_lock', asyncio.Lock()):
            client_cache = getattr(self, '_client_cache', {})
            
            if cache_key not in client_cache:
                # 获取连接参数
                if connection_params:
                    conn_params = connection_params
                elif db_alias:
                    # 从配置获取（这里需要根据实际配置系统调整）
                    conn_params = self._get_db_config(db_alias)
                else:
                    raise ValueError("必须提供connection_params或db_alias")
                
                # 确定连接池ID
                actual_pool_id = pool_id or knowledge_id or user_id or "system"
                
                # 创建ORM客户端
                if db_type == "mysql":
                    client = await create_managed_orm_mysql_client(
                        pool_id=actual_pool_id,
                        **conn_params
                    )
                elif db_type == "postgresql":
                    client = await create_managed_orm_postgresql_client(
                        pool_id=actual_pool_id,
                        **conn_params
                    )
                elif db_type == "sqlite":
                    client = await create_managed_orm_sqlite_client(
                        pool_id=actual_pool_id,
                        **conn_params
                    )
                else:
                    raise ValueError(f"不支持的数据库类型: {db_type}")
                
                client_cache[cache_key] = client
                logger.info(f"创建ORM RDB客户端: {db_type} (pool_id: {actual_pool_id})")
        
        return client_cache[cache_key]


# 便捷函数
async def get_orm_mysql_client(
    knowledge_id: Optional[str] = None,
    user_id: Optional[str] = None,
    **connection_params
) -> ORMSQLAlchemyClient:
    """获取ORM MySQL客户端的便捷函数"""
    pool_id = knowledge_id or user_id or "default"
    return await create_managed_orm_mysql_client(pool_id=pool_id, **connection_params)


async def get_orm_postgresql_client(
    knowledge_id: Optional[str] = None,
    user_id: Optional[str] = None,
    **connection_params
) -> ORMSQLAlchemyClient:
    """获取ORM PostgreSQL客户端的便捷函数"""
    pool_id = knowledge_id or user_id or "default"
    return await create_managed_orm_postgresql_client(pool_id=pool_id, **connection_params)


# 清理函数
async def cleanup_orm_clients(pool_id: Optional[str] = None):
    """清理ORM客户端"""
    await ORMClientManager.cleanup_client(pool_id)


async def cleanup_all_orm_clients():
    """清理所有ORM客户端"""
    await ORMClientManager.cleanup_all()

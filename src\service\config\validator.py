"""
Configuration Validator

配置验证器 - 验证配置的正确性和完整性

Author: AI Assistant
Created: 2025-07-11
"""

import logging
from typing import Any, Dict, List, Optional
from omegaconf import DictConfig

from ..exceptions import ValidationError

logger = logging.getLogger(__name__)


class ConfigValidator:
    """
    配置验证器
    
    验证配置的：
    - 结构完整性
    - 数据类型正确性
    - 必需字段存在性
    - 值的有效性
    """
    
    def __init__(self):
        self._validation_rules = self._load_validation_rules()
    
    def _load_validation_rules(self) -> Dict:
        """加载验证规则"""
        return {
            "database": {
                "required": True,
                "type": "dict",
                "children": {
                    "rdbs": {
                        "required": False,
                        "type": "dict",
                        "children": {
                            "*": {  # 任意RDB配置
                                "required_fields": ["_target_", "host", "port", "database", "username"],
                                "optional_fields": ["password", "charset", "pool_params", "cache_params"]
                            }
                        }
                    },
                    "vdbs": {
                        "required": False,
                        "type": "dict",
                        "children": {
                            "*": {  # 任意VDB配置
                                "required_fields": ["_target_", "host", "port", "database", "username"],
                                "optional_fields": ["password", "vector_params", "search_params"]
                            }
                        }
                    }
                }
            },
            "llm": {
                "required": False,
                "type": "dict"
            },
            "embedding": {
                "required": False,
                "type": "dict"
            }
        }
    
    async def validate(self, config: DictConfig) -> bool:
        """
        验证配置
        
        Args:
            config: 配置对象
            
        Returns:
            验证是否通过
            
        Raises:
            ValidationError: 验证失败
        """
        try:
            errors = []
            
            # 验证顶级结构
            errors.extend(self._validate_structure(config, self._validation_rules))
            
            # 验证数据库配置
            if "database" in config:
                errors.extend(await self._validate_database_config(config.database))
            
            # 验证LLM配置
            if "llm" in config:
                errors.extend(await self._validate_llm_config(config.llm))
            
            # 验证嵌入模型配置
            if "embedding" in config:
                errors.extend(await self._validate_embedding_config(config.embedding))
            
            if errors:
                error_msg = "配置验证失败:\n" + "\n".join(errors)
                logger.error(error_msg)
                raise ValidationError(error_msg)
            
            logger.debug("配置验证通过")
            return True
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"配置验证过程中发生错误: {e}")
            raise ValidationError(f"Validation process failed: {e}") from e
    
    def _validate_structure(self, config: DictConfig, rules: Dict, path: str = "") -> List[str]:
        """验证配置结构"""
        errors = []
        
        for key, rule in rules.items():
            current_path = f"{path}.{key}" if path else key
            
            # 检查必需字段
            if rule.get("required", False) and key not in config:
                errors.append(f"缺少必需字段: {current_path}")
                continue
            
            if key not in config:
                continue
            
            value = config[key]
            
            # 检查数据类型
            expected_type = rule.get("type")
            if expected_type:
                if not self._check_type(value, expected_type):
                    errors.append(f"字段类型错误: {current_path}, 期望: {expected_type}, 实际: {type(value).__name__}")
                    continue
            
            # 递归验证子结构
            if "children" in rule and isinstance(value, dict):
                for child_key, child_value in value.items():
                    child_rule = rule["children"].get(child_key) or rule["children"].get("*")
                    if child_rule:
                        if isinstance(child_rule, dict) and "children" in child_rule:
                            errors.extend(self._validate_structure(
                                {child_key: child_value}, 
                                {child_key: child_rule}, 
                                current_path
                            ))
                        else:
                            # 验证字段要求
                            errors.extend(self._validate_fields(
                                child_value, child_rule, f"{current_path}.{child_key}"
                            ))
        
        return errors
    
    def _validate_fields(self, config: Any, rule: Dict, path: str) -> List[str]:
        """验证字段要求"""
        errors = []
        
        if not isinstance(config, dict):
            return errors
        
        # 检查必需字段
        required_fields = rule.get("required_fields", [])
        for field in required_fields:
            if field not in config:
                errors.append(f"缺少必需字段: {path}.{field}")
        
        return errors
    
    def _check_type(self, value: Any, expected_type: str) -> bool:
        """检查数据类型"""
        from omegaconf import DictConfig, ListConfig

        type_mapping = {
            "dict": (dict, DictConfig),  # 支持OmegaConf的DictConfig
            "list": (list, ListConfig),  # 支持OmegaConf的ListConfig
            "str": str,
            "int": int,
            "float": (int, float),
            "bool": bool
        }

        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type:
            return isinstance(value, expected_python_type)

        return True
    
    async def _validate_database_config(self, db_config: DictConfig) -> List[str]:
        """验证数据库配置"""
        errors = []
        
        # 验证RDB配置
        if "rdbs" in db_config:
            for rdb_name, rdb_config in db_config.rdbs.items():
                errors.extend(self._validate_rdb_config(rdb_config, f"database.rdbs.{rdb_name}"))
        
        # 验证VDB配置
        if "vdbs" in db_config:
            for vdb_name, vdb_config in db_config.vdbs.items():
                errors.extend(self._validate_vdb_config(vdb_config, f"database.vdbs.{vdb_name}"))
        
        return errors
    
    def _validate_rdb_config(self, config: DictConfig, path: str) -> List[str]:
        """验证RDB配置"""
        errors = []
        
        # 检查_target_字段
        if "_target_" not in config:
            errors.append(f"RDB配置缺少_target_字段: {path}")
        elif not isinstance(config._target_, str) or "." not in config._target_:
            errors.append(f"RDB配置_target_格式错误: {path}")
        
        # 检查连接参数
        connection_fields = ["host", "port", "database", "username"]
        for field in connection_fields:
            if field not in config:
                errors.append(f"RDB配置缺少连接参数: {path}.{field}")
        
        # 验证端口号
        if "port" in config:
            try:
                port = int(config.port)
                if not (1 <= port <= 65535):
                    errors.append(f"RDB配置端口号无效: {path}.port = {port}")
            except (ValueError, TypeError):
                errors.append(f"RDB配置端口号类型错误: {path}.port")
        
        return errors
    
    def _validate_vdb_config(self, config: DictConfig, path: str) -> List[str]:
        """验证VDB配置"""
        errors = []
        
        # 检查_target_字段
        if "_target_" not in config:
            errors.append(f"VDB配置缺少_target_字段: {path}")
        elif not isinstance(config._target_, str) or "." not in config._target_:
            errors.append(f"VDB配置_target_格式错误: {path}")
        
        # 检查连接参数
        connection_fields = ["host", "port", "database", "username"]
        for field in connection_fields:
            if field not in config:
                errors.append(f"VDB配置缺少连接参数: {path}.{field}")
        
        # 验证向量参数
        if "vector_params" in config:
            vector_params = config.vector_params
            if "default_dimension" in vector_params:
                try:
                    dim = int(vector_params.default_dimension)
                    if dim <= 0:
                        errors.append(f"VDB配置向量维度无效: {path}.vector_params.default_dimension = {dim}")
                except (ValueError, TypeError):
                    errors.append(f"VDB配置向量维度类型错误: {path}.vector_params.default_dimension")
        
        return errors
    
    async def _validate_llm_config(self, llm_config: DictConfig) -> List[str]:
        """验证LLM配置"""
        errors = []
        # TODO: 实现LLM配置验证逻辑
        return errors
    
    async def _validate_embedding_config(self, embedding_config: DictConfig) -> List[str]:
        """验证嵌入模型配置"""
        errors = []
        # TODO: 实现嵌入模型配置验证逻辑
        return errors

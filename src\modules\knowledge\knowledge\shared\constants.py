"""
Knowledge模块常量定义

参考DD系统的constants.py设计，定义知识库相关的常量。
"""


class KnowledgeConstants:
    """知识库常量"""
    
    # 知识库类型
    KB_TYPE_DD = "DD"
    KB_TYPE_DOC = "Doc"
    KB_TYPE_METADATA = "MetaData"
    
    # 支持的知识库类型列表
    SUPPORTED_KB_TYPES = [KB_TYPE_DD, KB_TYPE_DOC, KB_TYPE_METADATA]
    
    # 默认模型配置
    DEFAULT_MODELS = {
        KB_TYPE_METADATA: {
            "embedding": "moka-m3e-base"
        },
        KB_TYPE_DOC: {
            "embedding": "moka-m3e-base",
            "llm": "opentrek"
        },
        KB_TYPE_DD: {
            "embedding": "moka-m3e-base", 
            "llm": "opentrek"
        }
    }
    
    # 向量化字段（如果需要）
    VECTORIZED_FIELDS = ["knowledge_desc"]
    
    # 分页默认值
    DEFAULT_PAGE_SIZE = 20
    DEFAULT_MAX_PAGE_SIZE = 100


class KnowledgeTableNames:
    """知识库表名常量"""
    
    # 主表
    KB_KNOWLEDGE = "kb_knowledge"

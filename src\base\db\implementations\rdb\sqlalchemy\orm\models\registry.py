"""
模型注册表

管理所有动态生成的模型类，提供统一的模型访问接口
"""

import threading
from typing import Dict, Type, Optional, List, Set
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine
import logging

from .base import BaseModel
from .dynamic import DynamicModelGenerator
from ..exceptions import ModelError, ModelCacheError

logger = logging.getLogger(__name__)


class ModelRegistry:
    """
    模型注册表
    
    管理所有动态生成的模型类，提供统一的访问接口
    """
    
    def __init__(
        self,
        engine: Engine,
        cache_size: int = 100,
        auto_generate: bool = True,
        **generator_options
    ):
        """
        初始化模型注册表
        
        Args:
            engine: SQLAlchemy引擎
            cache_size: 缓存大小
            auto_generate: 是否自动生成模型
            **generator_options: 模型生成器选项
        """
        self.engine = engine
        self.auto_generate = auto_generate
        
        # 创建模型生成器
        self.generator = DynamicModelGenerator(
            engine=engine,
            cache_size=cache_size,
            **generator_options
        )
        
        # 模型注册表
        self._models: Dict[str, Type[BaseModel]] = {}
        self._lock = threading.RLock()
        
        # 预加载标志
        self._preloaded = False
    
    def register_model(self, table_name: str, model_class: Type[BaseModel]) -> None:
        """
        注册模型类
        
        Args:
            table_name: 表名
            model_class: 模型类
        """
        with self._lock:
            self._models[table_name] = model_class
            logger.debug(f"Registered model: {table_name} -> {model_class.__name__}")
    
    def unregister_model(self, table_name: str) -> bool:
        """
        注销模型类
        
        Args:
            table_name: 表名
        
        Returns:
            是否成功注销
        """
        with self._lock:
            if table_name in self._models:
                del self._models[table_name]
                logger.debug(f"Unregistered model: {table_name}")
                return True
            return False
    
    def get_model(self, table_name: str, auto_generate: Optional[bool] = None) -> Type[BaseModel]:
        """
        获取模型类
        
        Args:
            table_name: 表名
            auto_generate: 是否自动生成（覆盖实例设置）
        
        Returns:
            模型类
        
        Raises:
            ModelError: 当模型不存在且无法生成时
        """
        with self._lock:
            # 检查已注册的模型
            if table_name in self._models:
                return self._models[table_name]
            
            # 检查是否应该自动生成
            should_generate = auto_generate if auto_generate is not None else self.auto_generate
            
            if should_generate:
                try:
                    # 使用生成器创建模型
                    model_class = self.generator.generate_model(table_name)
                    
                    # 注册到注册表
                    self._models[table_name] = model_class
                    
                    logger.info(f"Auto-generated and registered model: {table_name}")
                    return model_class
                    
                except Exception as e:
                    raise ModelError(f"Failed to generate model for table '{table_name}': {e}", e)
            else:
                raise ModelError(f"Model for table '{table_name}' not found and auto-generation is disabled")
    
    def has_model(self, table_name: str) -> bool:
        """
        检查是否存在指定表的模型
        
        Args:
            table_name: 表名
        
        Returns:
            是否存在模型
        """
        with self._lock:
            return table_name in self._models
    
    def get_all_models(self) -> Dict[str, Type[BaseModel]]:
        """
        获取所有已注册的模型
        
        Returns:
            表名到模型类的映射字典
        """
        with self._lock:
            return self._models.copy()
    
    def get_registered_tables(self) -> Set[str]:
        """
        获取所有已注册的表名
        
        Returns:
            表名集合
        """
        with self._lock:
            return set(self._models.keys())
    
    def preload_all_models(self, force_refresh: bool = False) -> Dict[str, Type[BaseModel]]:
        """
        预加载所有可用表的模型
        
        Args:
            force_refresh: 是否强制刷新
        
        Returns:
            加载的模型字典
        """
        if self._preloaded and not force_refresh:
            logger.debug("Models already preloaded, skipping")
            return self.get_all_models()
        
        try:
            # 生成所有模型
            all_models = self.generator.generate_all_models(force_refresh)
            
            # 注册所有模型
            with self._lock:
                for table_name, model_class in all_models.items():
                    self._models[table_name] = model_class
            
            self._preloaded = True
            logger.info(f"Preloaded {len(all_models)} models")
            
            return all_models
            
        except Exception as e:
            raise ModelError(f"Failed to preload models: {e}", e)
    
    def refresh_model(self, table_name: str) -> Type[BaseModel]:
        """
        刷新指定表的模型
        
        Args:
            table_name: 表名
        
        Returns:
            刷新后的模型类
        """
        try:
            # 从生成器刷新模型
            model_class = self.generator.refresh_model(table_name)
            
            # 更新注册表
            with self._lock:
                self._models[table_name] = model_class
            
            logger.info(f"Refreshed model: {table_name}")
            return model_class
            
        except Exception as e:
            raise ModelError(f"Failed to refresh model for table '{table_name}': {e}", e)
    
    def refresh_all_models(self) -> Dict[str, Type[BaseModel]]:
        """
        刷新所有模型
        
        Returns:
            刷新后的模型字典
        """
        # 清空注册表
        with self._lock:
            self._models.clear()
        
        # 清空生成器缓存
        self.generator.clear_cache()
        
        # 重新预加载
        self._preloaded = False
        return self.preload_all_models(force_refresh=True)
    
    def get_available_tables(self) -> List[str]:
        """
        获取所有可用的表名
        
        Returns:
            表名列表
        """
        return self.generator.get_available_tables()
    
    def clear(self) -> None:
        """清空注册表和缓存"""
        with self._lock:
            self._models.clear()
        
        self.generator.clear_cache()
        self._preloaded = False
        logger.info("Cleared model registry")
    
    def get_model_info(self, table_name: str) -> Dict[str, any]:
        """
        获取模型信息
        
        Args:
            table_name: 表名
        
        Returns:
            模型信息字典
        """
        model_class = self.get_model(table_name)
        
        return {
            'table_name': table_name,
            'class_name': model_class.__name__,
            'columns': model_class.get_column_names(),
            'primary_keys': model_class.get_primary_key_columns(),
            'relationships': model_class.get_relationship_names(),
        }
    
    def __len__(self) -> int:
        """获取注册的模型数量"""
        return len(self._models)
    
    def __contains__(self, table_name: str) -> bool:
        """检查是否包含指定表的模型"""
        return self.has_model(table_name)
    
    def __getitem__(self, table_name: str) -> Type[BaseModel]:
        """通过表名获取模型类"""
        return self.get_model(table_name)

    def clear_cache(self) -> None:
        """
        安全地清空模型缓存

        只清理当前实例的缓存，不影响全局registry和其他实例
        """
        logger.info("Clearing model registry cache...")

        # 只清空当前实例的模型字典
        self._models.clear()

        # 如果有生成器，也清空生成器的缓存
        if self.generator:
            self.generator.clear_cache()

        logger.info("Model registry cache cleared (safe mode)")

    def clear_cache_aggressive(self) -> None:
        """
        激进的缓存清理（仅用于测试环境）

        警告：这个方法会影响全局状态，可能影响其他实例
        仅在确保没有其他实例使用时调用
        """
        logger.warning("Performing aggressive cache clearing - may affect other instances!")

        # 清空当前实例缓存
        self.clear_cache()

        # 尝试清理全局registry（危险操作）
        try:
            self._clear_global_registry()
        except Exception as e:
            logger.debug(f"Failed to clear global registry: {e}")

    def _clear_global_registry(self) -> None:
        """
        清理全局registry（危险操作）

        警告：这会影响所有使用相同BaseModel的实例
        """
        from .base import BaseModel

        if hasattr(BaseModel, 'registry'):
            registry = BaseModel.registry

            # 只清理我们创建的模型类，不清理整个registry
            if hasattr(registry, '_class_registry'):
                class_registry = registry._class_registry

                # 收集要删除的键（避免在迭代时修改字典）
                keys_to_remove = []
                for key in class_registry:
                    # 只删除我们的模型类
                    if isinstance(key, str) and 'base.db.implementations.rdb.orm.models.base' in key:
                        keys_to_remove.append(key)

                # 安全删除
                for key in keys_to_remove:
                    try:
                        if key in class_registry:
                            del class_registry[key]
                            logger.debug(f"Removed class from registry: {key}")
                    except Exception as e:
                        logger.debug(f"Failed to remove {key}: {e}")

        logger.debug("Global registry cleanup completed")


# 全局模型注册表实例（可选）
_global_registry: Optional[ModelRegistry] = None


def get_global_registry() -> Optional[ModelRegistry]:
    """获取全局模型注册表"""
    return _global_registry


def set_global_registry(registry: ModelRegistry) -> None:
    """设置全局模型注册表"""
    global _global_registry
    _global_registry = registry


def clear_global_registry() -> None:
    """清空全局模型注册表"""
    global _global_registry
    _global_registry = None

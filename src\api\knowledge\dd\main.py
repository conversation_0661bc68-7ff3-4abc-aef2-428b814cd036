"""
DD数据需求管理系统主路由

整合所有DD系统的API路由，提供统一的API入口。
符合生产环境的路由结构和命名规范。

重构后的模块化架构：
- routers/: 按功能模块组织的API路由
- models/: 请求和响应数据模型
- dependencies/: 依赖注入和通用功能
- utils/: 工具函数和辅助功能
"""

from fastapi import APIRouter
from .routers import (
    departments_router,
    submissions_router,
    search_router,
    distribution_router,
    reports_router,
    system_router
)

# 创建主路由器 - 符合生产环境规范
router = APIRouter(prefix="/knowledge/dd", tags=["DD数据需求管理"])

# 注册所有子路由
router.include_router(departments_router)
router.include_router(submissions_router)
router.include_router(search_router)
router.include_router(distribution_router)
router.include_router(reports_router)
router.include_router(system_router)

# 注意：健康检查和API概览端点已移至 routers/system.py 中

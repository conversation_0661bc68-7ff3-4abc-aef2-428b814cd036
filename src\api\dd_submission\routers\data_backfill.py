"""
数据回填API路由

实现新的数据回填逻辑：
1. 接收report_code、dept_id、step、data参数
2. 基于version+dept_id+submission_id的唯一键查找和更新
3. 只更新允许的字段（dr22、bdr01、bdr02、bdr03、bdr04）
4. 批量处理支持和性能优化
5. 标准响应格式
"""

import logging
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks

logger = logging.getLogger(__name__)

from ..models.request_models import DataBackfillRequest
from ..models.response_models import StandardResponse, DataBackfillResponse
from ..services.data_backfill_service import DataBackfillService
from service import get_client

# 创建路由器
router = APIRouter(
    prefix="/api/dd/data-backfill",
    tags=["数据回填"]
)


async def get_data_backfill_service() -> DataBackfillService:
    """获取数据回填服务实例"""
    service = DataBackfillService()
    await service.initialize()
    return service

@router.post("/process", response_model=StandardResponse)
async def process_data_backfill(
    request: DataBackfillRequest,
    background_tasks: BackgroundTasks,
    service: DataBackfillService = Depends(get_data_backfill_service)
):
    """
    数据回填处理接口

    实现完整的新业务逻辑：
    1. 接收report_code、dept_id、step、data参数
    2. 基于version+dept_id+submission_id的唯一键查找和更新
    3. 只更新允许的字段（dr22、bdr01、bdr02、bdr03、bdr04）
    4. 批量处理支持和性能优化
    5. 标准响应格式

    Args:
        request: 数据回填请求
        background_tasks: 后台任务
        service: 数据回填服务实例

    Returns:
        StandardResponse: 标准响应
    """
    try:
        logger.info(f"收到数据回填请求: report_code={request.report_code}, "
                   f"dept_id={request.dept_id}, step={request.step}, 数据条数={len(request.data)}")

        # 执行数据回填逻辑
        result = await service.process_data_backfill(
            request.report_code,
            request.dept_id,
            request.step,
            request.data,
            background_tasks
        )

        # 返回标准响应格式
        if result['code'] == '200':
            return StandardResponse(
                code="200",
                msg="数据回填成功"
            )
        else:
            raise HTTPException(status_code=result['code'], detail=result['msg'])

    except Exception as e:
        logger.error(f"数据回填处理失败: {e}")
        raise HTTPException(status_code=400, detail=f"数据回填处理失败: {str(e)}")


@router.get("/validate-step/{step}")
async def validate_step(step: str):
    """
    验证处理步骤

    Args:
        step: 处理步骤名称

    Returns:
        验证结果
    """
    supported_steps = ["报表解读", "义务解读", "业务解读", "IT解读"]

    return {
        "step": step,
        "is_supported": step in supported_steps,
        "supported_steps": supported_steps
    }


@router.get("/performance/stats")
async def get_performance_stats(
    service: DataBackfillService = Depends(get_data_backfill_service)
):
    """
    获取性能统计信息

    Args:
        service: 数据回填服务实例

    Returns:
        Dict: 性能统计数据
    """
    try:
        stats = service.get_performance_stats()
        return {
            "code": "0",
            "msg": "统计信息获取成功",
            "data": stats
        }

    except Exception as e:
        logger.error(f"获取性能统计失败: {e}")
        return {
            "code": "400",
            "msg": f"获取失败: {str(e)}"
        }


@router.get("/health")
async def health_check():
    """
    健康检查接口

    Returns:
        服务状态
    """
    return {
        "status": "healthy",
        "service": "data_backfill",
        "version": "2.0",
        "features": [
            "批量处理支持",
            "性能优化",
            "智能降级",
            "完整监控"
        ]
    }

"""
三层搜索业务逻辑模块

通用的三层搜索组件，支持：
1. 第一层：完全精确匹配
2. 第二层：混合搜索（向量+文本）+ 四层业务筛选
3. 第三层：TF-IDF推荐兜底

设计为可复用的模块化组件，支持不同业务场景的定制化配置。
"""

import time
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Callable, TypeVar, Generic
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger

# 泛型类型定义
T = TypeVar('T')  # 搜索结果类型
R = TypeVar('R')  # 请求类型


class SearchLayerType(Enum):
    """搜索层类型"""
    EXACT_MATCH = "exact_match"      # 精确匹配
    HYBRID_SEARCH = "hybrid_search"  # 混合搜索
    TFIDF_FALLBACK = "tfidf_fallback"  # TF-IDF兜底


class FilterLayerType(Enum):
    """四层筛选类型"""
    SET_TYPE = "set_type"           # 集合类型筛选
    REPORT_TYPE = "report_type"     # 报表类型筛选
    SUBMISSION_TYPE = "submission_type"  # 提交类型筛选
    DATA_LAYER = "data_layer"       # 数据层筛选


@dataclass
class SearchLayerConfig:
    """搜索层配置"""
    enabled: bool = True
    timeout_seconds: float = 30.0
    max_results: int = 100
    min_confidence: float = 0.5
    custom_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class FilterLayerConfig:
    """筛选层配置"""
    enabled: bool = True
    field_name: str = ""
    stop_on_unique: bool = True  # 找到唯一结果时是否停止
    custom_logic: Optional[Callable] = None


@dataclass
class ThreeLayerSearchConfig:
    """三层搜索配置"""
    # 搜索层配置
    exact_match: SearchLayerConfig = field(default_factory=SearchLayerConfig)
    hybrid_search: SearchLayerConfig = field(default_factory=SearchLayerConfig)
    tfidf_fallback: SearchLayerConfig = field(default_factory=SearchLayerConfig)
    
    # 四层筛选配置
    filter_layers: Dict[FilterLayerType, FilterLayerConfig] = field(default_factory=dict)
    
    # 全局配置
    enable_four_layer_filtering: bool = True
    max_total_time_seconds: float = 120.0
    enable_early_termination: bool = True


@dataclass
class SearchLayerResult(Generic[T]):
    """搜索层结果"""
    layer_type: SearchLayerType
    success: bool
    results: List[T] = field(default_factory=list)
    execution_time_ms: float = 0.0
    confidence_score: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None


@dataclass
class FilterLayerResult(Generic[T]):
    """筛选层结果"""
    layer_type: FilterLayerType
    layer_name: str
    before_count: int
    after_count: int
    filtered_results: List[T] = field(default_factory=list)
    filter_criteria: Dict[str, Any] = field(default_factory=dict)
    stopped_early: bool = False
    is_final_layer: bool = False


@dataclass
class ThreeLayerSearchResult(Generic[T]):
    """三层搜索最终结果"""
    success: bool
    final_results: List[T] = field(default_factory=list)
    recommended_result: Optional[T] = None
    confidence_level: str = "unknown"
    
    # 执行详情
    search_layers_executed: List[SearchLayerType] = field(default_factory=list)
    filter_layers_executed: List[FilterLayerType] = field(default_factory=list)
    total_execution_time_ms: float = 0.0
    
    # 各层结果
    search_layer_results: List[SearchLayerResult[T]] = field(default_factory=list)
    filter_layer_results: List[FilterLayerResult[T]] = field(default_factory=list)
    
    # 处理日志
    processing_notes: List[str] = field(default_factory=list)
    error_message: Optional[str] = None


class SearchLayerExecutor(ABC, Generic[T, R]):
    """搜索层执行器抽象基类"""
    
    @abstractmethod
    async def execute_exact_match(self, request: R, config: SearchLayerConfig) -> SearchLayerResult[T]:
        """执行精确匹配搜索"""
        pass
    
    @abstractmethod
    async def execute_hybrid_search(self, request: R, config: SearchLayerConfig) -> SearchLayerResult[T]:
        """执行混合搜索"""
        pass
    
    @abstractmethod
    async def execute_tfidf_fallback(self, request: R, config: SearchLayerConfig) -> SearchLayerResult[T]:
        """执行TF-IDF兜底搜索"""
        pass


class FilterLayerExecutor(ABC, Generic[T, R]):
    """筛选层执行器抽象基类"""
    
    @abstractmethod
    async def filter_by_set_type(self, results: List[T], request: R, config: FilterLayerConfig) -> List[T]:
        """按集合类型筛选"""
        pass
    
    @abstractmethod
    async def filter_by_report_type(self, results: List[T], request: R, config: FilterLayerConfig) -> List[T]:
        """按报表类型筛选"""
        pass
    
    @abstractmethod
    async def filter_by_submission_type(self, results: List[T], request: R, config: FilterLayerConfig) -> List[T]:
        """按提交类型筛选"""
        pass
    
    @abstractmethod
    async def filter_by_data_layer(self, results: List[T], request: R, config: FilterLayerConfig) -> List[T]:
        """按数据层筛选"""
        pass


class DecisionMaker(ABC, Generic[T]):
    """决策器抽象基类"""
    
    @abstractmethod
    async def make_final_decision(self, results: List[T]) -> Optional[T]:
        """做出最终决策"""
        pass
    
    @abstractmethod
    def calculate_confidence_level(self, results: List[T]) -> str:
        """计算置信度等级"""
        pass


class ThreeLayerSearchEngine(Generic[T, R]):
    """三层搜索引擎"""
    
    def __init__(
        self,
        search_executor: SearchLayerExecutor[T, R],
        filter_executor: FilterLayerExecutor[T, R],
        decision_maker: DecisionMaker[T],
        config: ThreeLayerSearchConfig
    ):
        """
        初始化三层搜索引擎
        
        Args:
            search_executor: 搜索层执行器
            filter_executor: 筛选层执行器
            decision_maker: 决策器
            config: 搜索配置
        """
        self.search_executor = search_executor
        self.filter_executor = filter_executor
        self.decision_maker = decision_maker
        self.config = config
    
    async def search(self, request: R) -> ThreeLayerSearchResult[T]:
        """
        执行三层搜索
        
        Args:
            request: 搜索请求
            
        Returns:
            搜索结果
        """
        start_time = time.time()
        result = ThreeLayerSearchResult[T](success=False)
        
        try:
            logger.info("开始三层搜索")
            
            # 第一层：精确匹配
            exact_result = await self._execute_search_layer(
                SearchLayerType.EXACT_MATCH, request, result
            )
            
            if exact_result.success and exact_result.results:
                logger.info(f"精确匹配找到 {len(exact_result.results)} 条结果，直接返回")
                result.final_results = exact_result.results
                result.recommended_result = await self.decision_maker.make_final_decision(exact_result.results)
                result.confidence_level = self.decision_maker.calculate_confidence_level(exact_result.results)
                result.success = True
                result.processing_notes.append("精确匹配成功，跳过后续层级")
                return result
            
            # 第二层：混合搜索 + 四层筛选
            hybrid_result = await self._execute_search_layer(
                SearchLayerType.HYBRID_SEARCH, request, result
            )
            
            if hybrid_result.success and hybrid_result.results:
                logger.info(f"混合搜索找到 {len(hybrid_result.results)} 条结果，开始四层筛选")
                
                # 执行四层筛选
                filtered_results = await self._execute_four_layer_filtering(
                    hybrid_result.results, request, result
                )
                
                if filtered_results:
                    result.final_results = filtered_results
                    result.recommended_result = await self.decision_maker.make_final_decision(filtered_results)
                    result.confidence_level = self.decision_maker.calculate_confidence_level(filtered_results)
                    result.success = True
                    return result
            
            # 第三层：TF-IDF兜底
            tfidf_result = await self._execute_search_layer(
                SearchLayerType.TFIDF_FALLBACK, request, result
            )
            
            if tfidf_result.success and tfidf_result.results:
                result.final_results = tfidf_result.results
                result.recommended_result = await self.decision_maker.make_final_decision(tfidf_result.results)
                result.confidence_level = self.decision_maker.calculate_confidence_level(tfidf_result.results)
                result.success = True
                result.processing_notes.append("使用TF-IDF兜底策略")
            else:
                result.success = False
                result.processing_notes.append("所有搜索层级均无结果")
            
            return result
            
        except Exception as e:
            logger.error(f"三层搜索执行失败: {e}")
            result.success = False
            result.error_message = str(e)
            return result
            
        finally:
            result.total_execution_time_ms = (time.time() - start_time) * 1000
            logger.info(f"三层搜索完成，总耗时: {result.total_execution_time_ms:.2f}ms")
    
    async def _execute_search_layer(
        self, 
        layer_type: SearchLayerType, 
        request: R, 
        result: ThreeLayerSearchResult[T]
    ) -> SearchLayerResult[T]:
        """执行搜索层"""
        start_time = time.time()
        
        try:
            # 获取配置
            if layer_type == SearchLayerType.EXACT_MATCH:
                config = self.config.exact_match
                layer_result = await self.search_executor.execute_exact_match(request, config)
            elif layer_type == SearchLayerType.HYBRID_SEARCH:
                config = self.config.hybrid_search
                layer_result = await self.search_executor.execute_hybrid_search(request, config)
            elif layer_type == SearchLayerType.TFIDF_FALLBACK:
                config = self.config.tfidf_fallback
                layer_result = await self.search_executor.execute_tfidf_fallback(request, config)
            else:
                raise ValueError(f"未知的搜索层类型: {layer_type}")
            
            # 记录执行时间
            layer_result.execution_time_ms = (time.time() - start_time) * 1000
            
            # 添加到结果中
            result.search_layers_executed.append(layer_type)
            result.search_layer_results.append(layer_result)
            
            logger.info(f"{layer_type.value} 执行完成: "
                       f"结果数={len(layer_result.results)}, "
                       f"耗时={layer_result.execution_time_ms:.2f}ms")
            
            return layer_result
            
        except Exception as e:
            logger.error(f"{layer_type.value} 执行失败: {e}")
            error_result = SearchLayerResult[T](
                layer_type=layer_type,
                success=False,
                error_message=str(e),
                execution_time_ms=(time.time() - start_time) * 1000
            )
            result.search_layer_results.append(error_result)
            return error_result

    async def _execute_four_layer_filtering(
        self,
        candidates: List[T],
        request: R,
        result: ThreeLayerSearchResult[T]
    ) -> List[T]:
        """执行四层业务筛选"""
        if not self.config.enable_four_layer_filtering:
            logger.info("四层筛选已禁用，直接返回候选结果")
            return candidates

        current_results = candidates.copy()

        # 定义筛选层顺序
        filter_layers = [
            (FilterLayerType.SET_TYPE, self.filter_executor.filter_by_set_type),
            (FilterLayerType.REPORT_TYPE, self.filter_executor.filter_by_report_type),
            (FilterLayerType.SUBMISSION_TYPE, self.filter_executor.filter_by_submission_type),
            (FilterLayerType.DATA_LAYER, self.filter_executor.filter_by_data_layer)
        ]

        for i, (layer_type, filter_func) in enumerate(filter_layers):
            is_final_layer = (i == len(filter_layers) - 1)

            # 检查是否启用此筛选层
            layer_config = self.config.filter_layers.get(layer_type)
            if layer_config and not layer_config.enabled:
                logger.info(f"跳过已禁用的筛选层: {layer_type.value}")
                continue

            # 执行筛选
            filter_result = await self._apply_filter_layer(
                layer_type, filter_func, current_results, request, is_final_layer
            )

            # 记录筛选结果
            result.filter_layers_executed.append(layer_type)
            result.filter_layer_results.append(filter_result)

            # 更新当前结果
            current_results = filter_result.filtered_results

            # 检查是否提前终止
            if (filter_result.stopped_early and
                self.config.enable_early_termination):
                logger.info(f"在 {layer_type.value} 找到唯一结果，提前终止筛选")
                result.processing_notes.append(f"在{layer_type.value}找到唯一结果，提前终止")
                break

            # 检查是否无结果
            if not current_results:
                logger.warning(f"在 {layer_type.value} 筛选后无结果")
                result.processing_notes.append(f"在{layer_type.value}筛选后无结果")
                break

        return current_results

    async def _apply_filter_layer(
        self,
        layer_type: FilterLayerType,
        filter_func: Callable,
        results: List[T],
        request: R,
        is_final_layer: bool
    ) -> FilterLayerResult[T]:
        """应用单个筛选层"""
        start_time = time.time()
        before_count = len(results)

        try:
            # 获取筛选层配置
            layer_config = self.config.filter_layers.get(
                layer_type,
                FilterLayerConfig(field_name=layer_type.value)
            )

            # 执行筛选
            if layer_config.custom_logic:
                # 使用自定义筛选逻辑
                filtered_results = await layer_config.custom_logic(results, request, layer_config)
            else:
                # 使用默认筛选逻辑
                filtered_results = await filter_func(results, request, layer_config)

            after_count = len(filtered_results)

            # 检查是否提前停止
            stopped_early = (
                after_count == 1 and
                not is_final_layer and
                layer_config.stop_on_unique
            )

            execution_time = (time.time() - start_time) * 1000

            logger.info(f"{layer_type.value} 筛选完成: "
                       f"{before_count} -> {after_count}, "
                       f"耗时: {execution_time:.2f}ms")

            return FilterLayerResult[T](
                layer_type=layer_type,
                layer_name=layer_type.value,
                before_count=before_count,
                after_count=after_count,
                filtered_results=filtered_results,
                filter_criteria={layer_config.field_name: "applied"},
                stopped_early=stopped_early,
                is_final_layer=is_final_layer
            )

        except Exception as e:
            logger.error(f"{layer_type.value} 筛选失败: {e}")
            return FilterLayerResult[T](
                layer_type=layer_type,
                layer_name=layer_type.value,
                before_count=before_count,
                after_count=0,
                filtered_results=[],
                filter_criteria={},
                stopped_early=False,
                is_final_layer=is_final_layer
            )


class ThreeLayerSearchBuilder:
    """三层搜索构建器"""

    def __init__(self):
        self.config = ThreeLayerSearchConfig()

    def with_exact_match_config(self, **kwargs) -> 'ThreeLayerSearchBuilder':
        """配置精确匹配层"""
        for key, value in kwargs.items():
            if hasattr(self.config.exact_match, key):
                setattr(self.config.exact_match, key, value)
        return self

    def with_hybrid_search_config(self, **kwargs) -> 'ThreeLayerSearchBuilder':
        """配置混合搜索层"""
        for key, value in kwargs.items():
            if hasattr(self.config.hybrid_search, key):
                setattr(self.config.hybrid_search, key, value)
        return self

    def with_tfidf_fallback_config(self, **kwargs) -> 'ThreeLayerSearchBuilder':
        """配置TF-IDF兜底层"""
        for key, value in kwargs.items():
            if hasattr(self.config.tfidf_fallback, key):
                setattr(self.config.tfidf_fallback, key, value)
        return self

    def with_filter_layer_config(
        self,
        layer_type: FilterLayerType,
        **kwargs
    ) -> 'ThreeLayerSearchBuilder':
        """配置筛选层"""
        if layer_type not in self.config.filter_layers:
            self.config.filter_layers[layer_type] = FilterLayerConfig()

        for key, value in kwargs.items():
            if hasattr(self.config.filter_layers[layer_type], key):
                setattr(self.config.filter_layers[layer_type], key, value)
        return self

    def with_global_config(self, **kwargs) -> 'ThreeLayerSearchBuilder':
        """配置全局参数"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        return self

    def build(
        self,
        search_executor: SearchLayerExecutor[T, R],
        filter_executor: FilterLayerExecutor[T, R],
        decision_maker: DecisionMaker[T]
    ) -> ThreeLayerSearchEngine[T, R]:
        """构建搜索引擎"""
        return ThreeLayerSearchEngine(
            search_executor=search_executor,
            filter_executor=filter_executor,
            decision_maker=decision_maker,
            config=self.config
        )

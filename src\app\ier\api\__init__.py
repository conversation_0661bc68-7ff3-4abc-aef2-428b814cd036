#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：外规内化 
@File    ：__init__.py.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/21 17:24 
@Desc    ： 外规内化api汇总，对外暴露给框架使用
"""
# todo 完成api接口的整合
from fastapi import APIRouter

from .ier_file_process_api import register_ier_file_routers
from .ier_chatbot_api import register_ier_chatbot_routers
# 创建外规内化主路由
router = APIRouter(prefix="/ier")
ier_file_process_router = register_ier_file_routers()
ier_chatbot_router = register_ier_chatbot_routers()

router.include_router(ier_file_process_router)
router.include_router(ier_chatbot_router)
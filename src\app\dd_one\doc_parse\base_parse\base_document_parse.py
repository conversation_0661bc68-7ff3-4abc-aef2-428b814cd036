import os
from abc import ABC, abstractmethod
import pandas as pd


class BaseDocumentParser(ABC):
    def __init__(self, metrics_file: str, description_file: str, sheet_names: str):
        """
        初始化基础文档解析器，包含指标文件和描述文件路径。

        参数:
            metrics_file (str): Excel文件路径（支持xlsx或xls）
            description_file (str): 描述文件路径（支持doc、docx或wps）
        """
        self._validate_file(metrics_file, ['.xlsx', '.xls', '.et'])
        self._validate_file(description_file, ['.doc', '.docx', '.wps', '.pdf'])
        self.metrics_file = metrics_file
        self.description_file = description_file
        self.sheet_names = sheet_names

    def _validate_file(self, file_path: str, valid_extensions: list) -> None:
        """
        验证文件是否存在且具有有效的扩展名。

        参数:
            file_path (str): 文件路径
            valid_extensions (list): 有效文件扩展名列表

        异常:
            ValueError: 如果文件不存在或扩展名无效
        """
        if not os.path.exists(file_path):
            raise ValueError(f"文件不存在: {file_path}")
        if not any(file_path.lower().endswith(ext) for ext in valid_extensions):
            raise ValueError(f"文件扩展名无效: {file_path}，预期扩展名: {valid_extensions}")

    @abstractmethod
    def document_unparsing(self, metrics_file: str, description_file: str, sheet_names: str) -> str:
        """
        抽象方法，用于解析文档并生成Excel文件。

        参数:
            metrics_file (str): 指标Excel文件路径
            description_file (str): 描述文件路径

        返回:
            str: 生成的Excel文件路径
        """
        pass

    @abstractmethod
    def extraction_report(self, metrics_file: str, description_file: str, sheet_names: str):
        """
        抽象方法，用于生成提取报告。

        参数:
            metrics_file (str): 指标Excel文件路径
            description_file (str): 描述文件路径
            sheet_names (str): 工作表名称

        返回:
           一个字典，{"sheet_name":[]}
        """

        pass

"""
SQL解析步骤
从SQL语句中提取表信息和进行多维度分析
"""

from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

from pipeline.core.base_step import ToolStep
from pipeline.core.context import PipelineContext
from utils.db.schema.get_colkv import run_sql_analysis

class SQLParserStep(ToolStep):
    """
    SQL解析步骤
    
    功能：
    - 从SQL语句中提取列与表的映射关系
    - 提取WHERE条件
    - 提取JOIN条件
    - 提取涉及的表
    - 提取VALUE计算逻辑

    输入：
    - context.sql_candidates[0]: 第一个SQL语句（list[str]格式）

    输出：
    - context.parser_info: 包含columns、where、join、tables、value五个维度的解析结果
    """

    def __init__(self):
        super().__init__(
            name="sql_parser",
            description="解析SQL语句并提取表信息，进行多维度分析"
        )
    
    async def preprocess(self, context: PipelineContext) -> Optional[Dict[str, Any]]:
        """阶段1: 预处理 - 获取和验证SQL语句"""
        try:
            # 从context中获取SQL候选列表
            sql_candidates = context.get("sql_candidates", [])
            
            if not sql_candidates:
                logger.warning("没有找到SQL候选语句，跳过SQL解析")
                return None
            
            # 获取第一个SQL语句（确保是字符串格式）
            primary_sql = sql_candidates[0]

            # 处理不同格式的SQL候选（保险逻辑，但主要支持list[str]格式）
            if isinstance(primary_sql, dict):
                # 如果是字典格式，尝试提取SQL语句
                if "sql" in primary_sql:
                    primary_sql = primary_sql["sql"]
                elif "query" in primary_sql:
                    primary_sql = primary_sql["query"]
                elif "SQL" in primary_sql:
                    primary_sql = primary_sql["SQL"]
                else:
                    logger.warning(f"无法从字典格式中提取SQL语句: {list(primary_sql.keys())}")
                    return None
            
            # 验证SQL语句
            if not isinstance(primary_sql, str) or not primary_sql.strip():
                logger.warning("SQL语句为空或格式无效")
                return None
            
            logger.info(f"准备解析SQL语句，长度: {len(primary_sql)} 字符")
            
            return {
                "sql": primary_sql.strip(),
                "sql_index": 0,
                "total_candidates": len(sql_candidates)
            }
            
        except Exception as e:
            logger.error(f"SQL解析预处理失败: {e}")
            return None
    
    async def process(self, preprocessed_data: Dict[str, Any], context: PipelineContext) -> Dict[str, Any]:
        """阶段2: 核心处理 - 使用run_sql_analysis执行SQL解析"""
        sql = preprocessed_data["sql"]

        try:
            logger.info("开始执行SQL解析和分析")

            # 从context中获取candidate_tables并转换格式
            table_dict = self._convert_candidate_tables_to_table_dict(context)

            # 使用run_sql_analysis函数进行SQL分析
            # 传入转换后的table_dict，actions="all"
            analysis_result = run_sql_analysis(sql, table_dict=table_dict, actions="all")

            logger.info("成功完成SQL解析")

            # 对输出格式进行调整
            processed_result = self._process_analysis_result(analysis_result, sql)

            return processed_result

        except Exception as e:
            logger.error(f"SQL解析处理失败: {e}")
            return {
                "sql_statement": sql,
                "error": str(e),
                "analysis_status": "failed",
                "parsing_status": "failed",
                "columns": {},
                "where": [],
                "join": [],
                "tables": set(),
                "value": {},
                "table_count": 0,
                "column_count": 0
            }
    
    async def parse_result(self, result: Dict[str, Any], context: PipelineContext) -> Dict[str, Any]:
        """阶段3: 结果解析 - 验证和格式化分析结果"""
        try:
            # 验证必要字段
            required_fields = ["columns", "where", "join", "tables", "value"]

            for field in required_fields:
                if field not in result:
                    logger.warning(f"解析结果缺少必要字段: {field}")
                    if field == "columns":
                        result[field] = {}
                    elif field in ["where", "join", "tables"]:
                        result[field] = []
                    elif field == "value":
                        result[field] = {}

            # 确保解析状态正确
            if "parsing_status" not in result:
                result["parsing_status"] = "success" if "error" not in result else "failed"

            result["parsing_complete"] = True

            logger.info("SQL解析结果验证完成")
            return result

        except Exception as e:
            logger.error(f"SQL解析结果解析失败: {e}")
            result["parsing_error"] = str(e)
            result["parsing_status"] = "failed"
            return result
    
    async def postprocess(self, parsed_result: Dict[str, Any], context: PipelineContext) -> Dict[str, Any]:
        """阶段4: 后处理 - 生成摘要和优化结果"""
        try:
            # 生成解析摘要
            summary = self._generate_parsing_summary(parsed_result)
            parsed_result["parsing_summary"] = summary
            
            # 提取关键信息用于后续步骤
            key_info = self._extract_key_information(parsed_result)
            parsed_result["key_information"] = key_info
            
            logger.info("SQL解析后处理完成")
            return parsed_result
            
        except Exception as e:
            logger.error(f"SQL解析后处理失败: {e}")
            parsed_result["postprocess_error"] = str(e)
            return parsed_result

    def _convert_candidate_tables_to_table_dict(self, context: PipelineContext) -> Dict[str, set]:
        """
        将context.candidate_tables转换为run_sql_analysis所需的table_dict格式

        输入格式: {"table_1": ["col_1", "col_2", ...], "table_2": ["col_a", "col_b", ...]}
        输出格式: {"table_1": {"col_1", "col_2", ...}, "table_2": {"col_a", "col_b", ...}}
        """
        try:
            candidate_tables = context.get("candidate_tables", {})

            if not candidate_tables:
                logger.warning("未找到candidate_tables，使用空的table_dict")
                return {}

            # 转换格式：将列表转换为集合
            table_dict = {}
            for table_name, columns in candidate_tables.items():
                if isinstance(columns, list):
                    table_dict[table_name] = set(columns)
                elif isinstance(columns, set):
                    table_dict[table_name] = columns
                else:
                    logger.warning(f"表 {table_name} 的列格式不正确: {type(columns)}")
                    table_dict[table_name] = set()

            logger.info(f"成功转换candidate_tables，包含 {len(table_dict)} 个表")
            for table_name, columns in table_dict.items():
                logger.debug(f"  表 {table_name}: {len(columns)} 个列")

            return table_dict

        except Exception as e:
            logger.error(f"转换candidate_tables失败: {e}")
            return {}

    def _process_analysis_result(self, analysis_result: Dict[str, Any], sql: str) -> Dict[str, Any]:
        """处理run_sql_analysis的输出结果，进行格式调整"""
        try:
            # 获取原始结果
            columns = analysis_result.get('columns', {})
            where_conditions = analysis_result.get('where', [])
            join_conditions = analysis_result.get('join', [])
            tables = analysis_result.get('tables', set())
            value_logic = analysis_result.get('value', {})

            # 对columns进行处理：将列名映射到真实表名（英文）
            processed_columns = {}
            for column, table in columns.items():
                if table and table.strip():  # 如果有表名映射
                    processed_columns[column] = table
                else:  # 如果没有表名映射，保持原样
                    processed_columns[column] = ""

            # 对tables进行处理：确保是列表格式
            if isinstance(tables, set):
                tables_list = list(tables)
            else:
                tables_list = list(tables) if tables else []

            # 构建处理后的结果
            processed_result = {
                # 基础信息
                "sql_statement": sql,
                "sql_length": len(sql),
                "analysis_status": "success",
                "parsing_status": "success",

                # 五个核心维度的解析结果
                "columns": processed_columns,  # 列名到真实表名的映射（英文）
                "where": where_conditions,     # WHERE条件列表
                "join": join_conditions,       # JOIN条件列表
                "tables": tables_list,         # 涉及的表列表
                "value": value_logic,          # VALUE计算逻辑

                # 统计信息
                "table_count": len(tables_list),
                "column_count": len(processed_columns),
                "where_count": len(where_conditions),
                "join_count": len(join_conditions),
                "value_count": len(value_logic)
            }

            logger.info(f"SQL解析完成，涉及 {processed_result['table_count']} 个表，{processed_result['column_count']} 个列")

            return processed_result

        except Exception as e:
            logger.error(f"处理分析结果失败: {e}")
            return {
                "sql_statement": sql,
                "error": str(e),
                "analysis_status": "failed",
                "parsing_status": "failed",
                "columns": {},
                "where": [],
                "join": [],
                "tables": [],
                "value": {},
                "table_count": 0,
                "column_count": 0
            }

    def _generate_parsing_summary(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """生成解析摘要"""
        try:
            return {
                "total_tables": result.get("table_count", 0),
                "total_columns": result.get("column_count", 0),
                "total_where_conditions": result.get("where_count", 0),
                "total_join_conditions": result.get("join_count", 0),
                "total_value_calculations": result.get("value_count", 0),
                "has_errors": "error" in result,
                "analysis_complete": result.get("parsing_complete", False),
                "main_tables": result.get("tables", [])[:5],  # 最多显示5个主要表
                "parsing_status": result.get("parsing_status", "unknown")
            }
        except Exception as e:
            logger.warning(f"生成解析摘要失败: {e}")
            return {"error": str(e)}
    
    def _extract_key_information(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """提取关键信息供后续步骤使用"""
        try:
            # 从columns中提取有表映射的列
            columns_with_tables = {}
            for col, table in result.get("columns", {}).items():
                if table and table.strip():
                    columns_with_tables[col] = table

            # 生成表范围描述
            tables = result.get("tables", [])
            table_scope_text = f"涉及表: {', '.join(tables)}" if tables else "未识别到表信息"

            return {
                "table_list": tables,
                "column_list": list(result.get("columns", {}).keys()),
                "columns_with_tables": columns_with_tables,
                "table_scope_text": table_scope_text,
                "where_conditions": result.get("where", []),
                "join_conditions": result.get("join", []),
                "value_calculations": result.get("value", {}),
                "has_complex_logic": len(result.get("join", [])) > 0 or len(result.get("where", [])) > 2
            }
        except Exception as e:
            logger.warning(f"提取关键信息失败: {e}")
            return {"error": str(e)}
    
    async def update_context(self, result: Dict[str, Any], context: PipelineContext) -> None:
        """更新上下文 - 存储解析结果"""
        try:
            # 存储完整的解析结果
            context.set("parser_info", result)

            # 存储关键信息到独立字段，便于其他步骤使用
            if "key_information" in result:
                key_info = result["key_information"]
                context.set("parsed_tables", key_info.get("table_list", []))
                context.set("parsed_columns", key_info.get("column_list", []))
                context.set("table_scope_description", key_info.get("table_scope_text", ""))
                context.set("columns_with_tables", key_info.get("columns_with_tables", {}))
                context.set("where_conditions", key_info.get("where_conditions", []))
                context.set("join_conditions", key_info.get("join_conditions", []))
                context.set("value_calculations", key_info.get("value_calculations", {}))

            # 存储解析摘要
            if "parsing_summary" in result:
                context.set("sql_parsing_summary", result["parsing_summary"])

            # 为BusinessLogicGenerator提供table_scope信息
            table_scope = result.get("key_information", {}).get("table_scope_text", "")
            if table_scope:
                # 确保parser_info中有table_scope字段
                if "parser_info" not in context.data:
                    context.data["parser_info"] = {}
                context.data["parser_info"]["table_scope"] = table_scope

            logger.info("SQL解析结果已存储到context")

        except Exception as e:
            logger.error(f"更新context失败: {e}")
            # 确保至少存储基本结果
            context.set("parser_info", {"error": str(e), "analysis_status": "context_update_failed"})

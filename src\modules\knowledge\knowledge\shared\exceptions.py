"""
Knowledge模块异常定义

参考DD系统的exceptions.py设计，定义知识库相关的异常类。
"""


class KnowledgeError(Exception):
    """知识库操作异常基类"""
    
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "KNOWLEDGE_ERROR"


class KnowledgeValidationError(KnowledgeError):
    """知识库验证异常"""
    
    def __init__(self, message: str, field_name: str = None):
        super().__init__(message, "KNOWLEDGE_VALIDATION_ERROR")
        self.field_name = field_name


class KnowledgeNotFoundError(KnowledgeError):
    """知识库未找到异常"""
    
    def __init__(self, knowledge_id: str = None, message: str = None):
        if message is None:
            if knowledge_id:
                message = f"知识库不存在: {knowledge_id}"
            else:
                message = "知识库不存在"
        super().__init__(message, "KNOWLEDGE_NOT_FOUND")
        self.knowledge_id = knowledge_id


class KnowledgeConflictError(KnowledgeError):
    """知识库冲突异常（如名称重复）"""
    
    def __init__(self, message: str, conflict_field: str = None):
        super().__init__(message, "KNOWLEDGE_CONFLICT_ERROR")
        self.conflict_field = conflict_field


class KnowledgeModelError(KnowledgeError):
    """知识库模型配置异常"""
    
    def __init__(self, message: str, model_type: str = None):
        super().__init__(message, "KNOWLEDGE_MODEL_ERROR")
        self.model_type = model_type

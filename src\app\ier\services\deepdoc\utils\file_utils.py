#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：deepdoc 
@File    ：file_utils.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/17 14:59 
@Desc    ：文件处理工具
"""
import os

PROJECT_BASE = os.getenv("PROJECT_BASE") or os.getenv("DEPLOY_BASE")

def get_project_base_directory(*args):
    global PROJECT_BASE
    if PROJECT_BASE is None:
        PROJECT_BASE = os.path.abspath(
            os.path.join(
                os.path.dirname(os.path.realpath(__file__)),
                os.pardir,
                os.pardir,
            )
        )

    if args:
        return os.path.join(PROJECT_BASE, *args)
    return PROJECT_BASE

def traversal_files(base):
    for root, ds, fs in os.walk(base):
        for f in fs:
            fullname = os.path.join(root, f)
            yield fullname

# Priority 机制概览

## 1. 简介

Priority（优先级）机制是 HSBC Knowledge 项目中一个关键的架构特性，用于根据业务需求为不同的服务实例配置不同的资源分配策略。该机制通过为不同优先级的服务分配不同的连接池大小、超时设置和缓存策略，实现系统资源的优化分配。

## 2. 核心概念

### 2.1 ServicePriority 枚举

在 `src/service/client/priority.py` 中定义了三个优先级：

- `HIGH`: 高优先级，用于关键业务场景
- `STANDARD`: 标准优先级，系统默认级别
- `LOW`: 低优先级，用于后台任务

### 2.2 优先级配置映射

通过 `PriorityConfigMapper` 类将数据库类型和优先级映射到具体的配置路径：

- 标准优先级：`database.rdbs.mysql`
- 高优先级：`database.rdbs.mysql_high_priority`
- 低优先级：`database.rdbs.mysql_low_priority`

### 2.3 配置文件系统

在 `src/config/database/` 目录下，为每种数据库类型和优先级提供了专门的配置文件，确保不同优先级的服务使用不同的资源配置。

## 3. 设计目标

### 3.1 资源优化
通过为不同业务场景分配不同的资源配置，提高整体系统资源利用率。

### 3.2 业务隔离
确保关键业务获得充足的系统资源，避免后台任务影响用户关键操作。

### 3.3 系统稳定性
通过资源隔离防止故障传播，提高系统整体稳定性。

### 3.4 向后兼容性
保持与原有系统的兼容性，标准优先级行为与原有系统一致。

## 4. 实现架构

Priority 机制在整个系统中涉及多个层次的实现：

1. **Service 层**：提供统一的客户端获取接口，支持优先级参数
2. **Base 层**：实现连接池管理，确保不同优先级使用不同的连接池配置
3. **配置层**：通过配置文件定义不同优先级的资源配置

整个机制通过配置解析、缓存键生成、连接池参数映射等技术手段实现优先级隔离。
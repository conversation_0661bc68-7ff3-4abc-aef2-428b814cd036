#!/usr/bin/env python3
"""
重构后的Factory和Config测试

测试内容：
1. 统一的from_dict创建方式
2. 便捷的数据库特定工厂方法
3. 共享引擎的作用验证
4. 配置的智能过滤和默认值
"""

import sys
import os

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '../../../../../../..')
sys.path.insert(0, os.path.abspath(project_root))

import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_from_dict_creation():
    """测试1: 统一的from_dict创建方式"""
    print("🧪 测试1: 统一的from_dict创建方式")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.factory import create_client_from_dict
    from base.db.implementations.rdb.sqlalchemy.universal.pool_manager import get_all_pool_stats
    
    # 测试MySQL配置
    mysql_config = {
        "dialect": "mysql",
        "host": "**************",
        "port": 37615,
        "database": "hsbc_data",
        "username": "root",
        "password": "idea@1008",
        "pool_size": 5,
        "echo": False,
        "pool_pre_ping": True
    }
    
    print(f"📊 创建前连接池状态: {get_all_pool_stats()}")
    
    # 使用from_dict创建客户端
    client1 = create_client_from_dict(mysql_config)
    print(f"✅ 使用from_dict创建MySQL客户端1")
    
    # 创建第二个相同配置的客户端
    client2 = create_client_from_dict(mysql_config)
    print(f"✅ 使用from_dict创建MySQL客户端2")
    
    print(f"📊 创建后连接池状态: {get_all_pool_stats()}")
    
    # 测试自动连接
    result1 = client1.execute("SELECT 'from_dict_test1' as message")
    result2 = client2.execute("SELECT 'from_dict_test2' as message")
    
    print(f"✅ 客户端1查询成功: {result1.affected_rows}")
    print(f"✅ 客户端2查询成功: {result2.affected_rows}")
    
    # 验证共享引擎
    if hasattr(client1, 'sync_engine') and hasattr(client2, 'sync_engine'):
        same_engine = id(client1.sync_engine) == id(client2.sync_engine)
        print(f"🔍 引擎共享验证:")
        print(f"   客户端1引擎ID: {id(client1.sync_engine)}")
        print(f"   客户端2引擎ID: {id(client2.sync_engine)}")
        print(f"   共享引擎: {'✅ 是' if same_engine else '❌ 否'}")
    
    print(f"📊 使用后连接池状态: {get_all_pool_stats()}")
    
    client1.disconnect()
    client2.disconnect()
    
    return True


def test_convenient_factory_methods():
    """测试2: 便捷的数据库特定工厂方法"""
    print("\n🧪 测试2: 便捷的数据库特定工厂方法")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.factory import (
        create_mysql_client, create_postgresql_client, create_sqlite_client
    )
    
    # 测试MySQL工厂方法
    mysql_client = create_mysql_client(
        host="**************",
        database="hsbc_data", 
        username="root",
        password="idea@1008",
        port=37615,
        charset="utf8mb4"
    )
    print(f"✅ MySQL工厂方法创建成功")
    
    # 测试PostgreSQL工厂方法（虽然连接会失败，但创建应该成功）
    try:
        postgresql_client = create_postgresql_client(
            host="localhost",
            database="test_db",
            username="user", 
            password="pass",
            port=5432
        )
        print(f"✅ PostgreSQL工厂方法创建成功")
    except Exception as e:
        print(f"⚠️  PostgreSQL工厂方法创建失败（预期）: {type(e).__name__}")
    
    # 测试SQLite工厂方法
    sqlite_client = create_sqlite_client(database_path=":memory:")
    print(f"✅ SQLite工厂方法创建成功")
    
    # 测试MySQL客户端功能
    result = mysql_client.execute("SELECT 'mysql_factory_test' as message")
    print(f"✅ MySQL工厂客户端查询成功: {result.affected_rows}")
    
    # 测试SQLite客户端功能
    sqlite_result = sqlite_client.execute("SELECT 'sqlite_factory_test' as message")
    print(f"✅ SQLite工厂客户端查询成功: {sqlite_result.affected_rows}")
    
    mysql_client.disconnect()
    sqlite_client.disconnect()
    
    return True


def test_config_filtering_and_defaults():
    """测试3: 配置的智能过滤和默认值"""
    print("\n🧪 测试3: 配置的智能过滤和默认值")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.factory import create_config_from_dict
    
    # 测试包含冗余参数的配置
    messy_config = {
        # 必需参数
        "host": "**************",
        "port": 37615,
        "database": "hsbc_data",
        "username": "root", 
        "password": "idea@1008",
        
        # 有效参数
        "pool_size": 8,
        "echo": True,
        
        # 冗余参数（应该被过滤）
        "invalid_param1": "should_be_filtered",
        "random_setting": 123,
        "unknown_option": True,
        
        # 缺失的可选参数（应该使用默认值）
        # charset, pool_timeout, etc.
    }
    
    print(f"📋 原始配置包含 {len(messy_config)} 个参数")
    print(f"   包含冗余参数: {[k for k in messy_config.keys() if k.startswith(('invalid', 'random', 'unknown'))]}")
    
    # 创建配置（应该自动过滤和填充默认值）
    config = create_config_from_dict(messy_config)
    
    print(f"✅ 配置创建成功")
    print(f"📊 配置验证:")
    print(f"   方言: {config.dialect}")
    print(f"   主机: {config.host}")
    print(f"   端口: {config.port}")
    print(f"   数据库: {config.database}")
    print(f"   连接池大小: {config.pool_size}")
    print(f"   字符集: {config.charset}")
    print(f"   连接池超时: {config.pool_timeout}")
    print(f"   预ping: {config.pool_pre_ping}")
    
    # 验证默认值是否正确应用
    expected_defaults = {
        'charset': 'utf8mb4',
        'pool_timeout': 30.0,
        'pool_recycle': 3600,
        'pool_pre_ping': True,
        'echo_pool': False,
        'future': True
    }
    
    print(f"\n🔍 默认值验证:")
    for key, expected_value in expected_defaults.items():
        actual_value = getattr(config, key)
        match = actual_value == expected_value
        print(f"   {key}: {actual_value} {'✅' if match else '❌'}")
    
    return True


def test_shared_engine_benefits():
    """测试4: 共享引擎的作用验证"""
    print("\n🧪 测试4: 共享引擎的作用验证")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.factory import create_client_from_dict
    from base.db.implementations.rdb.sqlalchemy.universal.pool_manager import get_all_pool_stats
    import time
    
    # 相同配置
    config = {
        "dialect": "mysql",
        "host": "**************",
        "port": 37615,
        "database": "hsbc_data",
        "username": "root",
        "password": "idea@1008",
        "pool_size": 3
    }
    
    print(f"📊 测试前连接池状态: {get_all_pool_stats()}")
    
    # 创建多个客户端
    clients = []
    for i in range(5):
        client = create_client_from_dict(config)
        clients.append(client)
        print(f"✅ 创建客户端 {i+1}")
    
    print(f"📊 创建后连接池状态: {get_all_pool_stats()}")
    
    # 测试并发使用
    print(f"\n📊 并发使用测试:")
    start_time = time.time()
    
    for i, client in enumerate(clients):
        result = client.execute(f"SELECT {i+1} as client_id, 'shared_engine_test' as message")
        print(f"   客户端 {i+1}: {result.affected_rows} 行")
    
    end_time = time.time()
    print(f"✅ 并发查询完成，耗时: {end_time - start_time:.4f}秒")
    
    print(f"📊 使用后连接池状态: {get_all_pool_stats()}")
    
    # 验证连接池信息
    stats = get_all_pool_stats()
    if stats['total_pools'] > 0:
        pool_key = list(stats['pools'].keys())[0]
        pool_info = stats['pools'][pool_key]
        print(f"\n🔍 连接池详细信息:")
        print(f"   连接池键: {pool_key}")
        print(f"   客户端数量: {pool_info['client_count']}")
        if 'sync_pool' in pool_info:
            sync_pool = pool_info['sync_pool']
            print(f"   连接池类型: {sync_pool.get('pool_type', 'Unknown')}")
            print(f"   连接池大小: {sync_pool.get('size', 'N/A')}")
            if 'checked_in' in sync_pool:
                print(f"   已签入连接: {sync_pool['checked_in']}")
            if 'checked_out' in sync_pool:
                print(f"   已签出连接: {sync_pool['checked_out']}")
            if 'overflow' in sync_pool:
                print(f"   溢出连接: {sync_pool['overflow']}")
    
    # 清理
    for client in clients:
        client.disconnect()
    
    print(f"📊 清理后连接池状态: {get_all_pool_stats()}")
    
    return True


def test_alias_methods():
    """测试5: 便捷别名方法"""
    print("\n🧪 测试5: 便捷别名方法")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.factory import create_client
    
    # 测试别名方法
    config = {
        "dialect": "mysql",
        "host": "**************",
        "port": 37615,
        "database": "hsbc_data",
        "username": "root",
        "password": "idea@1008"
    }
    
    # 使用别名创建客户端
    client = create_client(config)
    print(f"✅ 使用别名create_client创建成功")
    
    # 测试功能
    result = client.execute("SELECT 'alias_test' as message")
    print(f"✅ 别名客户端查询成功: {result.affected_rows}")
    
    client.disconnect()
    
    return True


def main():
    """主测试函数"""
    print("🚀 重构后的Factory和Config测试开始")
    print("=" * 80)
    
    # 执行所有测试
    tests = [
        ("统一的from_dict创建方式", test_from_dict_creation),
        ("便捷的数据库特定工厂方法", test_convenient_factory_methods),
        ("配置的智能过滤和默认值", test_config_filtering_and_defaults),
        ("共享引擎的作用验证", test_shared_engine_benefits),
        ("便捷别名方法", test_alias_methods),
    ]
    
    results = {}
    passed = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            results[test_name] = False
            print(f"❌ {test_name} - 异常: {e}")
            import traceback
            traceback.print_exc()
    
    # 测试总结
    total = len(tests)
    print(f"\n{'='*80}")
    print("📊 重构测试总结")
    print(f"{'='*80}")
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    print(f"\n💡 重构效果总结:")
    print("✅ 统一使用from_dict作为主要创建方式")
    print("✅ 保留便捷的数据库特定工厂方法")
    print("✅ 删除了不必要的创建方法")
    print("✅ 配置智能过滤和默认值应用")
    print("✅ 共享引擎有效减少资源消耗")
    print("✅ 结构清楚，API简洁统一")
    
    if passed == total:
        print(f"\n🎉 所有测试通过！重构成功，API更加统一清晰。")
        return True
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查重构实现。")
        return False


if __name__ == "__main__":
    success = main()
    exit_code = 0 if success else 1
    print(f"\n程序退出，退出码: {exit_code}")
    exit(exit_code)

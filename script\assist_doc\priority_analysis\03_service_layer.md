# Service 层实现详解

## 1. ClientFactory 类

### 1.1 定义位置
`src/service/client/factory.py`

### 1.2 核心功能
ClientFactory 是服务层的核心组件，负责客户端实例的创建、单例管理、生命周期管理和缓存管理。

### 1.3 优先级处理实现

#### 1.3.1 get_client 方法
```python
async def get_client(self,
                    config: Union[str, DictConfig],
                    singleton: bool = True,
                    priority: Optional[Union[str, ServicePriority]] = None,
                    priority_config_override: Optional[Dict[str, Any]] = None,
                    **kwargs) -> Any:
    """
    获取客户端实例 - 支持优先级和连接池参数映射的企业级实现

    Args:
        config: 配置对象或配置路径
        singleton: 是否使用单例模式
        priority: 可选的服务优先级 ('high', 'standard', 'low' 或 ServicePriority 枚举)
        priority_config_override: 手动优先级配置覆盖（用于自定义连接池参数）
        **kwargs: 额外参数

    Returns:
        客户端实例
    """
    # 确保初始化（线程安全）
    await self._ensure_initialized()

    try:
        # 智能配置解析：处理priority参数和配置覆盖
        resolved_config = self._resolve_config_with_priority(config, priority)

        # 应用优先级配置覆盖和连接池参数映射
        final_config = self._apply_priority_config_override(
            resolved_config, priority_config_override, **kwargs
        )

        # 生成缓存键（包含覆盖配置）
        cache_key = self._generate_cache_key(final_config, kwargs)

        # 如果是单例模式，使用双重检查锁定模式
        if singleton:
            # 第一次检查（无锁）
            cached_client = await self._cache.get(cache_key)
            if cached_client:
                logger.debug(f"从缓存获取客户端: {cache_key}")
                return cached_client

            # 获取锁进行第二次检查
            async with self._lock:
                # 第二次检查（有锁）
                cached_client = await self._cache.get(cache_key)
                if cached_client:
                    logger.debug(f"从缓存获取客户端 (锁定检查): {cache_key}")
                    return cached_client

                # 创建新的客户端实例
                client = await self._create_client(final_config, **kwargs)

                # 缓存客户端
                await self._cache.set(cache_key, client)
                logger.debug(f"客户端已缓存: {cache_key}")

                # 注册到生命周期管理器
                await self._lifecycle.register(client, cache_key)

                logger.info(f"客户端创建成功: {type(client).__name__}")
                return client
        else:
            # 非单例模式，直接创建
            client = await self._create_client(final_config, **kwargs)

            # 注册到生命周期管理器（使用唯一ID）
            unique_id = f"{cache_key}_{id(client)}"
            await self._lifecycle.register(client, unique_id)

            logger.info(f"非单例客户端创建成功: {type(client).__name__}")
            return client

    except Exception as e:
        logger.error(f"获取客户端失败: {e}")
        raise ClientError(f"Failed to get client: {e}") from e
```

#### 1.3.2 _resolve_config_with_priority 方法
```python
def _resolve_config_with_priority(self,
                                 config: Union[str, DictConfig],
                                 priority: Optional[Union[str, ServicePriority]]) -> Union[str, DictConfig]:
    """
    智能配置解析：处理priority参数

    Args:
        config: 原始配置对象或配置路径
        priority: 可选的服务优先级

    Returns:
        解析后的配置对象或配置路径

    Raises:
        ClientError: 当配置路径和priority参数冲突时
    """
    # 如果没有提供priority参数，直接返回原配置
    if priority is None:
        return config

    # 标准化priority参数
    if isinstance(priority, str):
        priority_enum = ServicePriority.from_string(priority)
    else:
        priority_enum = priority

    # 处理字符串配置路径
    if isinstance(config, str):
        return self._resolve_string_config_with_priority(config, priority_enum)

    # 处理DictConfig对象
    else:
        return self._resolve_dict_config_with_priority(config, priority_enum)
```

#### 1.3.3 _resolve_string_config_with_priority 方法
```python
def _resolve_string_config_with_priority(self,
                                       config_path: str,
                                       priority: ServicePriority) -> str:
    """
    解析字符串配置路径与优先级

    Args:
        config_path: 配置路径字符串
        priority: 服务优先级

    Returns:
        解析后的配置路径

    Raises:
        ClientError: 当配置路径和priority参数冲突时
    """
    # 检查配置路径是否已经包含优先级信息
    existing_db_type, existing_priority = PriorityConfigMapper.parse_config_path(config_path)

    if existing_db_type and existing_priority:
        # 配置路径已包含优先级信息
        if existing_priority != priority:
            # 优先级冲突
            raise ClientError(
                f"配置路径冲突: 路径 '{config_path}' 指定优先级为 '{existing_priority.value}', "
                f"但 priority 参数指定为 '{priority.value}'. "
                f"请使用完整配置路径或仅使用 priority 参数，不要同时使用."
            )
        # 优先级一致，直接返回原路径
        logger.debug(f"配置路径已包含正确优先级: {config_path}")
        return config_path

    elif existing_db_type:
        # 配置路径是基础路径，需要转换为优先级路径
        try:
            priority_config_path = PriorityConfigMapper.get_config_path(existing_db_type, priority)
            logger.debug(f"配置路径转换: {config_path} -> {priority_config_path}")
            return priority_config_path
        except ValueError as e:
            raise ClientError(f"无法为配置路径 '{config_path}' 应用优先级 '{priority.value}': {e}")

    else:
        # 无法解析的配置路径
        raise ClientError(
            f"无法解析配置路径 '{config_path}' 的数据库类型. "
            f"请使用标准格式如 'database.rdbs.mysql' 或 'database.vdbs.pgvector'"
        )
```

## 2. 优先级便捷方法

### 2.1 get_high_priority_client
```python
async def get_high_priority_client(self, config: Union[str, DictConfig], **kwargs) -> Any:
    """
    获取高优先级客户端的便捷方法

    Args:
        config: 配置对象或配置路径
        **kwargs: 额外参数

    Returns:
        高优先级客户端实例

    Example:
        >>> client = await factory.get_high_priority_client("database.rdbs.mysql")
    """
    return await self.get_client(config, priority=ServicePriority.HIGH, **kwargs)
```

### 2.2 get_standard_priority_client
```python
async def get_standard_priority_client(self, config: Union[str, DictConfig], **kwargs) -> Any:
    """
    获取标准优先级客户端的便捷方法

    Args:
        config: 配置对象或配置路径
        **kwargs: 额外参数

    Returns:
        标准优先级客户端实例

    Example:
        >>> client = await factory.get_standard_priority_client("database.rdbs.mysql")
    """
    return await self.get_client(config, priority=ServicePriority.STANDARD, **kwargs)
```

### 2.3 get_low_priority_client
```python
async def get_low_priority_client(self, config: Union[str, DictConfig], **kwargs) -> Any:
    """
    获取低优先级客户端的便捷方法

    Args:
        config: 配置对象或配置路径
        **kwargs: 额外参数

    Returns:
        低优先级客户端实例

    Example:
        >>> client = await factory.get_low_priority_client("database.rdbs.mysql")
    """
    return await self.get_client(config, priority=ServicePriority.LOW, **kwargs)
```

## 3. 缓存键生成

### 3.1 _generate_cache_key 方法
```python
def _generate_cache_key(self, config: Union[str, DictConfig], kwargs: Dict) -> str:
    """
    生成缓存键 - 企业级简洁实现

    原则：相同配置 = 相同实例（单例模式）
    优先级物理隔离：不同优先级配置生成不同缓存键
    """
    if isinstance(config, str):
        # 字符串配置路径 - 直接使用路径作为基础键
        base_key = config

        # 从配置路径中提取优先级信息（用于日志）
        db_type, priority = PriorityConfigMapper.parse_config_path(config)
        if priority:
            logger.debug(f"配置路径优先级: {db_type}:{priority.value}")
    else:
        # DictConfig对象 - 构建描述性键
        target = getattr(config, '_target_', 'unknown')
        host = getattr(config, 'host', 'localhost')
        port = getattr(config, 'port', 0)
        database = getattr(config, 'database', 'default')
        priority = getattr(config, 'priority', 'standard')
        service_tier = getattr(config, 'service_tier', 'normal')

        # 构建可读的缓存键
        base_key = f"{target}:{host}:{port}:{database}:priority_{priority}:tier_{service_tier}"

    # 处理额外参数（如果有）
    if kwargs:
        # 只包含影响客户端实例的参数
        relevant_kwargs = {k: v for k, v in kwargs.items()
                         if k not in ['timeout', 'retry_count', 'debug']}
        if relevant_kwargs:
            kwargs_str = ":".join(f"{k}={v}" for k, v in sorted(relevant_kwargs.items()))
            base_key = f"{base_key}:{kwargs_str}"

    # 确保键的长度合理（避免过长）
    if len(base_key) > 200:
        import hashlib
        hash_obj = hashlib.md5(base_key.encode('utf-8'))
        cache_key = f"hashed_{hash_obj.hexdigest()[:16]}"
    else:
        cache_key = base_key

    logger.debug(f"生成缓存键: {cache_key}")
    return cache_key
```

## 4. 连接池参数映射

### 4.1 _apply_pool_parameter_mapping 方法
```python
def _apply_pool_parameter_mapping(self, config: Dict[str, Any], db_type: str) -> Dict[str, Any]:
    """
    应用连接池参数映射

    Args:
        config: 原始配置
        db_type: 数据库类型

    Returns:
        映射后的配置
    """
    try:
        # 提取连接池相关参数
        pool_params = {k: v for k, v in config.items()
                      if k in ['pool_size', 'max_overflow', 'pool_timeout', 'pool_recycle',
                             'pool_pre_ping', 'min_connections', 'max_connections',
                             'minsize', 'maxsize']}

        if not pool_params:
            return config  # 没有连接池参数，直接返回

        # 映射参数
        mapped_params = map_pool_config(pool_params, db_type)

        # 合并到原配置
        result_config = config.copy()
        result_config.update(mapped_params)

        logger.debug(f"连接池参数映射: {pool_params} -> {mapped_params}")
        return result_config

    except Exception as e:
        logger.warning(f"连接池参数映射失败: {e}，使用原始配置")
        return config
```

## 5. 使用示例

### 5.1 基本用法
```python
# 获取标准优先级客户端（默认）
client = await get_client("database.rdbs.mysql")

# 获取高优先级客户端
high_client = await get_client("database.rdbs.mysql", priority='high')

# 获取低优先级客户端
low_client = await get_client("database.rdbs.mysql", priority='low')
```

### 5.2 直接使用配置路径
```python
# 直接使用完整配置路径
high_client = await get_client("database.rdbs.mysql_high_priority")
low_client = await get_client("database.vdbs.pgvector_low_priority")
```

### 5.3 便捷方法
```python
# 使用便捷方法
high_client = await factory.get_high_priority_client("database.rdbs.mysql")
standard_client = await factory.get_standard_priority_client("database.rdbs.mysql")
low_client = await factory.get_low_priority_client("database.rdbs.mysql")
```

### 5.4 手动配置覆盖
```python
# 使用手动优先级配置覆盖
custom_client = await get_client(
    "database.rdbs.mysql",
    priority='high',
    priority_config_override={
        'pool_size': 100,
        'max_overflow': 200,
        'pool_timeout': 5
    }
)
```
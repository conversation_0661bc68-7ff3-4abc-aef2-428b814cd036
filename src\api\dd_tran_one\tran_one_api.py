from pydantic import BaseModel
from typing import List, Dict
from fastapi import APIRouter, HTTPException, Depends
from src.app.dd_one.get_pri_file import recognize_files
from src.app.dd_one.getreportlist import getreportlist
from src.app.dd_one.extraction_report import get_report_data_list, get_report_data
from src.app.dd_one.custom_project import custom_project_output, get_custom_indicat
import httpx

# 创建路由器
router = APIRouter(tags=["DD0-1文件解析"], prefix="/onetran")


class FilePathInput(BaseModel):
    files_path: List[str]


class FileRecognitionOutput(BaseModel):
    doc_set: str
    time: str
    doc_title: str
    doc_num: str


@router.post("/get_pri_file", response_model=FileRecognitionOutput, summary="主文件提取")
def get_pri_api_file(file_path: FilePathInput):
    output = recognize_files(file_path.files_path)
    return output


class ReportListInput(BaseModel):
    file_info: List[FileRecognitionOutput]
    release_id: str
    report_department: str
    version: str


# todo 回调函数仍未定义
class CallbackResponse(BaseModel):
    success: bool
    message: str


@router.post("/get_report_list", response_model=CallbackResponse, summary="提取出报告列表")
async def get_report_list(file_path: ReportListInput):
    try:
        # 步骤 1：获取报告列表
        outputs_list = getreportlist(file_path.file_info, file_path.version)

        # 步骤 2：为输出添加 release_id 和 report_department
        enriched_outputs = [
            {**output, "release_id": file_path.release_id, "report_department": file_path.report_department}
            for output in outputs_list
        ]

        # 步骤 3：调用回调接口
        callback_url = "https://your-callback-endpoint.com/api/callback"  # 替换为实际的回调 URL
        async with httpx.AsyncClient() as client:
            callback_response = await client.post(
                callback_url,
                json={"reports": enriched_outputs},
                timeout=10.0
            )

            # 检查回调响应状态
            if callback_response.status_code == 200:
                callback_data = callback_response.json()
                if callback_data.get("success", False):
                    return CallbackResponse(success=True, message=callback_data.get("message", "报告列表处理成功"))
                else:
                    return CallbackResponse(
                        success=False,
                        message=callback_data.get("message", "回调接口返回失败")
                    )
            else:
                return CallbackResponse(
                    success=False,
                    message=f"回调接口调用失败，状态码: {callback_response.status_code}"
                )

    except Exception as e:
        # 处理异常情况
        raise HTTPException(status_code=500, detail=f"处理报告列表失败: {str(e)}")


class FileInfo(BaseModel):
    file_type: str
    file_id: str
    file_path: str


# 根据文件抽取dd返回
class ExtractionReportInput(BaseModel):
    report_code: str
    report_type: str  # 报送类型
    doc_set: str  # 套系类型，1104或者djz，根据这个选择不同的解析方式
    files_info: List[FileInfo]  # 文件信息


class CallbackResponse(BaseModel):
    success: bool
    message: str


@router.post("/extraction_report", response_model=CallbackResponse, summary="根据文件提取dd")
async def extraction_report(input_data: ExtractionReportInput):
    try:
        file_type = input_data.doc_set
        csv_path, doc_path = '', ''
        for file_info in input_data.files_info:
            if file_info.file_type == 'comments':  # 填报文件
                doc_path = file_info.file_path
            elif file_info.file_type == 'table':  # 表样文件
                csv_path = file_info.file_path
        # 产出的dd
        output_list_data = get_report_data_list(get_report_data(doc_path, csv_path, file_type))

        output = {
            "report_code": input_data.report_code,
            "data": output_list_data
        }

        # 步骤 3：调用回调接口
        callback_url = "https://your-callback-endpoint.com/api/callback"  # 替换为实际的回调 URL
        async with httpx.AsyncClient() as client:
            callback_response = await client.post(
                callback_url,
                json={"report_data": output},
                timeout=10.0
            )

            # 检查回调响应状态
            if callback_response.status_code == 200:
                callback_data = callback_response.json()
                if callback_data.get("success", False):
                    return CallbackResponse(success=True, message=callback_data.get("message", "dd报告数据提取成功"))
                else:
                    return CallbackResponse(
                        success=False,
                        message=callback_data.get("message", "回调接口返回失败")
                    )
            else:
                return CallbackResponse(
                    success=False,
                    message=f"回调接口调用失败，状态码: {callback_response.status_code}"
                )

    except Exception as e:
        # 处理异常情况
        raise HTTPException(status_code=500, detail=f"提取报告数据失败: {str(e)}")


class IndicatorInfo(BaseModel):
    sheet_name: str  # 对应的sheet——name
    sheet_sort: int  # 当前sheet所属表样的顺序
    col: list  # 当前选取行的所有内容的list [1.1资产方]
    lines: list  # 当前选取列的所有内容的list [A,title1,title2]
    mark: dict  # 当前选取的坐标
    entry_id: str = ""  # 当前填报项的id，如果没有则为""


class CustomProjectInput(BaseModel):
    report_code: str  # 报送code
    report_file_path: str  # 填报文件路径
    report_type: str  # 报送类型
    doc_set: str  # 套系类型，1104或者djz，根据这个选择不同的解析方式
    indicator_data: List[FileInfo]  # 选定的数据


@router.post("/custom_project", response_model=CallbackResponse, summary="根据文件提取dd")
async def custom_project(input_data: CustomProjectInput):
    try:
        # 步骤 1：将 CustomProjectInput 转换为字典以适配 all_data_output
        input_dict = input_data.dict()  # Pydantic 模型的 .dict() 方法将对象转换为字典
        custom_project_data = custom_project_output(input_dict)

        # 步骤 2：调用回调接口
        callback_url = "https://your-callback-endpoint.com/api/callback"  # 替换为实际的回调 URL
        async with httpx.AsyncClient() as client:
            callback_response = await client.post(
                callback_url,
                json={
                    "report_code": input_data.report_code,
                    "report_type": input_data.report_type,
                    "processed_data": custom_project_data
                },
                timeout=10.0
            )

            # 检查回调响应状态
            if callback_response.status_code == 200:
                callback_data = callback_response.json()
                if callback_data.get("success", False):
                    return CallbackResponse(success=True,
                                            message=callback_data.get("message", "自定义项目数据处理成功"))
                else:
                    return CallbackResponse(
                        success=False,
                        message=callback_data.get("message", "回调接口返回失败")
                    )
            else:
                return CallbackResponse(
                    success=False,
                    message=f"回调接口调用失败，状态码: {callback_response.status_code}"
                )

    except Exception as e:
        # 处理异常情况
        raise HTTPException(status_code=500, detail=f"处理自定义项目数据失败: {str(e)}")

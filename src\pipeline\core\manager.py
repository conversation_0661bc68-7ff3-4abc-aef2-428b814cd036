"""
轻量化Pipeline管理器
基于service层的企业级管道管理
"""

from typing import List, Dict, Any, Optional
import time
import logging

logger = logging.getLogger(__name__)

from .context import PipelineContext
from .base_step import BaseStep

class PipelineManager:
    """
    轻量化Pipeline管理器
    管理步骤的执行顺序和错误处理
    """
    
    def __init__(self, name: str = "pipeline"):
        self.name = name
        self.steps: List[BaseStep] = []
        self.error_strategy = "stop_on_error"  # stop_on_error, continue_on_error
    
    def add_step(self, step: BaseStep) -> 'PipelineManager':
        """添加步骤"""
        self.steps.append(step)
        return self
    
    def add_steps(self, steps: List[BaseStep]) -> 'PipelineManager':
        """批量添加步骤"""
        self.steps.extend(steps)
        return self
    
    def set_error_strategy(self, strategy: str) -> 'PipelineManager':
        """设置错误处理策略"""
        if strategy not in ["stop_on_error", "continue_on_error"]:
            raise ValueError("错误策略必须是 'stop_on_error' 或 'continue_on_error'")
        self.error_strategy = strategy
        return self
    
    async def execute(self, context: PipelineContext) -> Dict[str, Any]:
        """执行Pipeline"""
        logger.info(f"开始执行Pipeline: {self.name}")
        start_time = time.time()
        
        executed_steps = []
        failed_steps = []
        
        for i, step in enumerate(self.steps):
            try:
                logger.info(f"执行步骤 {i+1}/{len(self.steps)}: {step.name}")
                
                # 执行步骤
                context = await step.execute(context)
                executed_steps.append(step.name)
                
                logger.info(f"步骤 {step.name} 执行成功")
                
            except Exception as e:
                failed_steps.append(step.name)
                logger.error(f"步骤 {step.name} 执行失败: {e}")
                
                if self.error_strategy == "stop_on_error":
                    logger.error(f"Pipeline {self.name} 因步骤失败而停止")
                    break
                else:
                    logger.warning(f"步骤 {step.name} 失败，但继续执行后续步骤")
                    continue
        
        total_time = time.time() - start_time
        
        # 生成执行结果
        result = {
            "pipeline_name": self.name,
            "success": len(failed_steps) == 0,
            "total_steps": len(self.steps),
            "executed_steps": len(executed_steps),
            "failed_steps": len(failed_steps),
            "execution_time": total_time,
            "context": context,
            "step_results": context.step_results,
            "executed_step_names": executed_steps,
            "failed_step_names": failed_steps
        }
        
        if result["success"]:
            logger.info(f"Pipeline {self.name} 执行成功，耗时 {total_time:.2f}s")
        else:
            logger.error(f"Pipeline {self.name} 执行失败，成功 {len(executed_steps)}/{len(self.steps)} 步骤")
        
        return result
    
    def get_step_by_name(self, name: str) -> Optional[BaseStep]:
        """根据名称获取步骤"""
        for step in self.steps:
            if step.name == name:
                return step
        return None
    
    def remove_step(self, name: str) -> bool:
        """移除步骤"""
        for i, step in enumerate(self.steps):
            if step.name == name:
                del self.steps[i]
                return True
        return False
    
    def insert_step(self, index: int, step: BaseStep) -> 'PipelineManager':
        """在指定位置插入步骤"""
        self.steps.insert(index, step)
        return self
    
    def get_step_names(self) -> List[str]:
        """获取所有步骤名称"""
        return [step.name for step in self.steps]
    
    def validate_pipeline(self) -> Dict[str, Any]:
        """验证Pipeline配置"""
        issues = []
        warnings = []
        
        # 检查步骤名称唯一性
        step_names = [step.name for step in self.steps]
        if len(step_names) != len(set(step_names)):
            issues.append("存在重复的步骤名称")
        
        # 检查是否有步骤
        if not self.steps:
            issues.append("Pipeline中没有任何步骤")
        
        # 检查步骤依赖（简单检查）
        for step in self.steps:
            if not hasattr(step, 'name') or not step.name:
                issues.append(f"步骤 {step} 缺少名称")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "warnings": warnings,
            "step_count": len(self.steps),
            "step_names": step_names
        }

# ==================== Pipeline构建器 ====================

class PipelineBuilder:
    """Pipeline构建器 - 提供流式API"""
    
    def __init__(self, name: str = "pipeline"):
        self.manager = PipelineManager(name)
    
    def add_step(self, step: BaseStep) -> 'PipelineBuilder':
        """添加步骤"""
        self.manager.add_step(step)
        return self
    
    def add_llm_step(self, step_class: type, name: str, **kwargs) -> 'PipelineBuilder':
        """添加LLM步骤"""
        step = step_class(name=name, **kwargs)
        self.manager.add_step(step)
        return self
    
    def add_tool_step(self, step_class: type, name: str, **kwargs) -> 'PipelineBuilder':
        """添加工具步骤"""
        step = step_class(name=name, **kwargs)
        self.manager.add_step(step)
        return self
    
    def set_error_strategy(self, strategy: str) -> 'PipelineBuilder':
        """设置错误处理策略"""
        self.manager.set_error_strategy(strategy)
        return self
    
    def build(self) -> PipelineManager:
        """构建Pipeline"""
        validation = self.manager.validate_pipeline()
        if not validation["valid"]:
            raise ValueError(f"Pipeline配置无效: {validation['issues']}")
        
        return self.manager

# ==================== Pipeline执行器 ====================

class PipelineExecutor:
    """
    Pipeline执行器
    提供高级的执行功能，如并行执行、条件执行等
    """
    
    def __init__(self, manager: PipelineManager):
        self.manager = manager
    
    async def execute_with_conditions(self, 
                                    context: PipelineContext,
                                    step_conditions: Dict[str, callable] = None) -> Dict[str, Any]:
        """带条件的执行"""
        step_conditions = step_conditions or {}
        
        # 过滤需要执行的步骤
        steps_to_execute = []
        for step in self.manager.steps:
            condition = step_conditions.get(step.name)
            if condition is None or condition(context):
                steps_to_execute.append(step)
            else:
                logger.info(f"跳过步骤 {step.name}（条件不满足）")
        
        # 创建临时管理器执行过滤后的步骤
        temp_manager = PipelineManager(f"{self.manager.name}_conditional")
        temp_manager.steps = steps_to_execute
        temp_manager.error_strategy = self.manager.error_strategy
        
        return await temp_manager.execute(context)
    
    async def execute_step_range(self, 
                                context: PipelineContext,
                                start_step: str = None,
                                end_step: str = None) -> Dict[str, Any]:
        """执行指定范围的步骤"""
        step_names = self.manager.get_step_names()
        
        start_index = 0
        end_index = len(self.manager.steps)
        
        if start_step:
            try:
                start_index = step_names.index(start_step)
            except ValueError:
                raise ValueError(f"起始步骤 {start_step} 不存在")
        
        if end_step:
            try:
                end_index = step_names.index(end_step) + 1
            except ValueError:
                raise ValueError(f"结束步骤 {end_step} 不存在")
        
        # 创建临时管理器执行指定范围的步骤
        temp_manager = PipelineManager(f"{self.manager.name}_range")
        temp_manager.steps = self.manager.steps[start_index:end_index]
        temp_manager.error_strategy = self.manager.error_strategy
        
        return await temp_manager.execute(context)
    
    async def execute_single_step(self, 
                                 context: PipelineContext,
                                 step_name: str) -> Dict[str, Any]:
        """执行单个步骤"""
        step = self.manager.get_step_by_name(step_name)
        if not step:
            raise ValueError(f"步骤 {step_name} 不存在")
        
        # 创建临时管理器执行单个步骤
        temp_manager = PipelineManager(f"{self.manager.name}_single")
        temp_manager.add_step(step)
        
        return await temp_manager.execute(context)

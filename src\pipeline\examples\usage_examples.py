"""
轻量化Pipeline使用示例
展示如何使用新的Pipeline框架
"""

import asyncio
from typing import Dict, Any

from pipeline import NL2SQLExecutor, create_nl2sql_pipeline, PipelineContext, ContextFactory

# ==================== 基础使用示例 ====================

async def basic_usage_example():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 创建标准NL2SQL执行器
    executor = NL2SQLExecutor("standard")
    
    # 执行查询
    result = await executor.execute(
        user_question="查询2024年制造业贷款余额",
        hint="银保监会口径",
        keywords=["制造业", "贷款", "余额"],
        candidate_columns={
            "loan_table": ["amount", "industry", "year", "quarter"],
            "customer_table": ["customer_id", "customer_name", "industry_type"]
        },
        temp_db_schema="CREATE TABLE loan_table (amount DECIMAL, industry VARCHAR(50), year INT);"
    )
    
    print(f"执行成功: {result['success']}")
    print(f"执行时间: {result['execution_time']:.2f}s")
    print(f"执行步骤: {result['executed_steps']}")
    print(f"选择的列: {result['data']['selected_columns']}")
    print(f"生成的SQL: {result['data']['sql_candidates']}")
    
    return result

async def enhanced_usage_example():
    """增强模式使用示例（包含数据源收集）"""
    print("\n=== 增强模式使用示例 ===")
    
    # 创建增强NL2SQL执行器
    executor = NL2SQLExecutor("enhanced", include_business_logic=True)
    
    # 执行查询（需要dept_id）
    result = await executor.execute(
        user_question="查询客户贷款信息统计",
        hint="银保监会口径，按行业分类",
        dept_id="DEPT001"  # 部门ID，用于数据源收集
    )
    
    print(f"执行成功: {result['success']}")
    print(f"执行时间: {result['execution_time']:.2f}s")
    print(f"执行步骤: {result['executed_steps']}")
    print(f"收集的表数量: {len(result['data']['table_metadata'])}")
    print(f"收集的列数量: {len(result['data']['column_metadata'])}")
    print(f"生成的SQL: {result['data']['sql_candidates']}")
    print(f"业务逻辑: {result['data']['business_logic']}")
    
    return result

async def custom_pipeline_example():
    """自定义Pipeline示例"""
    print("\n=== 自定义Pipeline示例 ===")
    
    # 创建自定义Pipeline（只包含特定步骤）
    executor = NL2SQLExecutor("custom", step_names=[
        "key_associator",
        "column_selector",
        "enhanced_schema_generator",
        "sql_generator",
        "business_logic_generator"
    ])
    
    result = await executor.execute(
        user_question="统计各行业客户数量",
        hint="银保监会口径",
        candidate_columns={
            "customer_table": ["customer_id", "industry_type", "region"],
            "loan_table": ["customer_id", "amount", "status"]
        }
    )
    
    print(f"自定义Pipeline执行成功: {result['success']}")
    print(f"执行的步骤: {result['executed_steps']}")
    print(f"生成的SQL: {result['data']['sql_candidates']}")
    
    return result

async def minimal_pipeline_example():
    """最小化Pipeline示例"""
    print("\n=== 最小化Pipeline示例 ===")
    
    # 创建最小化Pipeline（仅核心步骤）
    executor = NL2SQLExecutor("minimal")
    
    result = await executor.execute(
        user_question="查询贷款总额",
        candidate_columns={
            "loan_table": ["amount", "status"]
        },
        temp_db_schema="CREATE TABLE loan_table (amount DECIMAL, status VARCHAR(20));"
    )
    
    print(f"最小化Pipeline执行成功: {result['success']}")
    print(f"执行的步骤: {result['executed_steps']}")
    print(f"生成的SQL: {result['data']['sql_candidates']}")
    
    return result

# ==================== 高级使用示例 ====================

async def direct_pipeline_usage():
    """直接使用Pipeline管理器的示例"""
    print("\n=== 直接Pipeline使用示例 ===")
    
    # 直接创建Pipeline
    pipeline = create_nl2sql_pipeline("standard", include_business_logic=False)
    
    # 创建上下文
    context = ContextFactory.create_nl2sql_context(
        user_question="查询制造业贷款数据",
        hint="银保监会口径",
        candidate_columns={
            "loan_table": ["amount", "industry", "year"]
        }
    )
    
    # 执行Pipeline
    result = await pipeline.execute(context)
    
    print(f"直接Pipeline执行成功: {result['success']}")
    print(f"执行时间: {result['execution_time']:.2f}s")
    print(f"步骤结果: {list(result['step_results'].keys())}")
    
    return result

async def step_by_step_execution():
    """分步执行示例"""
    print("\n=== 分步执行示例 ===")
    
    from pipeline import PipelineExecutor, PipelineManager
    from pipeline.steps import KeyAssociatorStep, ColumnSelectorStep, SQLGeneratorStep
    
    # 创建Pipeline
    pipeline = PipelineManager("step_by_step")
    pipeline.add_step(KeyAssociatorStep())
    pipeline.add_step(ColumnSelectorStep())
    pipeline.add_step(SQLGeneratorStep())
    
    # 创建执行器
    executor = PipelineExecutor(pipeline)
    
    # 创建上下文
    context = ContextFactory.create_nl2sql_context(
        user_question="查询贷款信息",
        candidate_columns={
            "loan_table": ["amount", "customer_id"]
        }
    )
    
    # 执行单个步骤
    print("执行关键字关联步骤...")
    result1 = await executor.execute_single_step(context, "key_associator")
    print(f"关键字关联完成: {result1['success']}")
    
    # 执行步骤范围
    print("执行列选择到SQL生成...")
    result2 = await executor.execute_step_range(context, "column_selector", "sql_generator")
    print(f"范围执行完成: {result2['success']}")
    
    return result2

# ==================== 错误处理示例 ====================

async def error_handling_example():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    # 创建执行器，设置继续执行策略
    pipeline = create_nl2sql_pipeline("standard")
    pipeline.set_error_strategy("continue_on_error")
    
    # 创建有问题的上下文（缺少必要参数）
    context = ContextFactory.create_nl2sql_context(
        user_question="",  # 空问题，可能导致某些步骤失败
        candidate_columns={}  # 空候选列
    )
    
    result = await pipeline.execute(context)
    
    print(f"错误处理执行结果: {result['success']}")
    print(f"成功步骤: {result['executed_steps']}")
    print(f"失败步骤: {result['failed_steps']}")
    
    return result

# ==================== 性能测试示例 ====================

async def performance_test():
    """性能测试示例"""
    print("\n=== 性能测试示例 ===")
    
    import time
    
    executor = NL2SQLExecutor("standard")
    
    # 执行多次测试
    num_tests = 3
    total_time = 0
    
    for i in range(num_tests):
        start_time = time.time()
        
        result = await executor.execute(
            user_question=f"测试查询 {i+1}",
            candidate_columns={
                "test_table": ["col1", "col2", "col3"]
            }
        )
        
        execution_time = time.time() - start_time
        total_time += execution_time
        
        print(f"测试 {i+1}: {result['success']}, 耗时 {execution_time:.2f}s")
    
    avg_time = total_time / num_tests
    print(f"平均执行时间: {avg_time:.2f}s")
    print(f"总执行时间: {total_time:.2f}s")

# ==================== 主函数 ====================

async def run_all_examples():
    """运行所有示例"""
    print("开始运行轻量化Pipeline使用示例...\n")
    
    try:
        # 基础示例
        await basic_usage_example()
        
        # 增强示例（注意：需要数据库连接）
        # await enhanced_usage_example()
        
        # 自定义Pipeline
        await custom_pipeline_example()
        
        # 最小化Pipeline
        await minimal_pipeline_example()
        
        # 直接使用Pipeline
        await direct_pipeline_usage()
        
        # 分步执行
        await step_by_step_execution()
        
        # 错误处理
        await error_handling_example()
        
        # 性能测试
        await performance_test()
        
        print("\n✅ 所有示例运行完成!")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(run_all_examples())

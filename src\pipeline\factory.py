"""
Pipeline工厂
创建和配置不同类型的Pipeline
"""

from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)

from .core.manager import PipelineManager, PipelineBuilder
from .core.context import ContextFactory
from .steps import (
    # 原有步骤
    TableSelectorStep,
    ColumnSelectorStep,
    SchemaGeneratorStep,
    KeyAssociatorStep,
    SQLGeneratorStep,

    # 新增步骤
    BusinessLogicGeneratorStep,
    SQLParserStep
)

class PipelineFactory:
    """Pipeline工厂类"""
    
    @staticmethod
    def create_nl2sql_pipeline(include_business_logic: bool = True) -> PipelineManager:
        """
        创建标准的NL2SQL Pipeline
        
        Args:
            include_business_logic: 是否包含业务逻辑生成步骤
            
        Returns:
            配置好的Pipeline管理器
        """
        builder = PipelineBuilder("nl2sql_standard")
        
        # 添加步骤（按执行顺序）
        builder.add_step(KeyAssociatorStep())
        builder.add_step(TableSelectorStep())  # 新增：表选择步骤
        builder.add_step(ColumnSelectorStep())
        builder.add_step(SchemaGeneratorStep())
        builder.add_step(SQLGeneratorStep())
        
        # 可选：添加业务逻辑生成步骤
        if include_business_logic:
            builder.add_step(BusinessLogicGeneratorStep())
        
        return builder.build()
    
    @staticmethod
    def create_enhanced_nl2sql_pipeline(include_business_logic: bool = True) -> PipelineManager:
        """
        创建增强的NL2SQL Pipeline（包含数据源收集）
        
        Args:
            include_business_logic: 是否包含业务逻辑生成步骤
            
        Returns:
            配置好的Pipeline管理器
        """
        builder = PipelineBuilder("nl2sql_enhanced")
        
        # 添加步骤（按执行顺序）

        builder.add_step(KeyAssociatorStep())
        builder.add_step(TableSelectorStep())  # 新增：表选择步骤
        builder.add_step(ColumnSelectorStep())
        builder.add_step(SQLGeneratorStep())
        
        # 可选：添加业务逻辑生成步骤
        if include_business_logic:
            builder.add_step(BusinessLogicGeneratorStep())
        
        return builder.build()

    @staticmethod
    def create_iterative_schema_pipeline(include_business_logic: bool = True) -> PipelineManager:
        """
        创建迭代式Schema生成Pipeline

        处理流程：
        1. SchemaGenerator (第1次) - 基于输入的table_names生成初始Schema片段
        2. TableSelector - 从候选表中筛选最相关的表
        3. SchemaGenerator (第2次) - 基于筛选后的表重新生成Schema片段
        4. ColumnSelector - 从候选列中筛选最相关的列
        5. SchemaGenerator (第3次) - 基于筛选后的列重新生成Schema片段
        6. KeyAssociator/ColumnFilter - 二次列筛选步骤
        7. SchemaGenerator (第4次，最终) - 设置 is_final=True 生成最终Schema
        8. SQLGenerator - 生成SQL查询
        9. BusinessLogicGenerator - 生成业务逻辑描述

        Args:
            include_business_logic: 是否包含业务逻辑生成步骤

        Returns:
            配置好的Pipeline管理器
        """
        builder = PipelineBuilder("iterative_schema")

        # 第1轮：初始Schema生成
        builder.add_step(SchemaGeneratorStep(step_name="schema_generator_1", is_final=False))

        # 第2轮：表选择 + Schema重生成
        builder.add_step(TableSelectorStep())
        builder.add_step(SchemaGeneratorStep(step_name="schema_generator_2", is_final=False))

        # 第3轮：列选择 + Schema重生成
        builder.add_step(ColumnSelectorStep())
        builder.add_step(SchemaGeneratorStep(step_name="schema_generator_3", is_final=False))

        # 第4轮：二次列筛选 + 最终Schema生成
        builder.add_step(KeyAssociatorStep())  # 或者 ColumnFilterStep
        builder.add_step(SchemaGeneratorStep(step_name="schema_generator_final", is_final=True))

        # 第5轮：SQL生成
        builder.add_step(SQLGeneratorStep())

        # 可选：业务逻辑生成
        if include_business_logic:
            builder.add_step(BusinessLogicGeneratorStep())

        return builder.build()

    @staticmethod
    def create_enhanced_with_parser_pipeline(include_business_logic: bool = True) -> PipelineManager:
        """
        创建包含SQL解析的增强Pipeline

        Args:
            include_business_logic: 是否包含业务逻辑生成步骤

        Returns:
            配置好的Pipeline管理器
        """
        builder = PipelineBuilder("nl2sql_enhanced_with_parser")

        # 添加步骤（按执行顺序）
        builder.add_step(KeyAssociatorStep())
        builder.add_step(TableSelectorStep())
        builder.add_step(ColumnSelectorStep())
        builder.add_step(SchemaGeneratorStep())
        builder.add_step(SQLGeneratorStep())
        builder.add_step(SQLParserStep())  # 新增：SQL解析步骤

        # 可选：添加业务逻辑生成步骤
        if include_business_logic:
            builder.add_step(BusinessLogicGeneratorStep())

        return builder.build()

    @staticmethod
    def create_custom_pipeline(step_names: List[str]) -> PipelineManager:
        """
        创建自定义Pipeline
        
        Args:
            step_names: 步骤名称列表
            
        Returns:
            配置好的Pipeline管理器
        """
        # 步骤映射
        step_mapping = {
            "key_associator": KeyAssociatorStep,
            "table_selector": TableSelectorStep,
            "column_selector": ColumnSelectorStep,
            "schema_generator": SchemaGeneratorStep,
            "sql_generator": SQLGeneratorStep,
            "sql_parser": SQLParserStep,
            "business_logic_generator": BusinessLogicGeneratorStep
        }
        
        builder = PipelineBuilder("nl2sql_custom")
        
        for step_name in step_names:
            if step_name in step_mapping:
                step_class = step_mapping[step_name]
                builder.add_step(step_class())
            else:
                logger.warning(f"未知的步骤名称: {step_name}")
        
        return builder.build()
    
    @staticmethod
    def create_minimal_pipeline() -> PipelineManager:
        """
        创建最小化Pipeline（仅核心步骤）
        
        Returns:
            配置好的Pipeline管理器
        """
        builder = PipelineBuilder("nl2sql_minimal")
        
        # 只包含核心步骤
        builder.add_step(ColumnSelectorStep())
        builder.add_step(SchemaGeneratorStep())
        builder.add_step(SQLGeneratorStep())
        
        return builder.build()

# ==================== 便捷函数 ====================

def create_nl2sql_pipeline(pipeline_type: str = "standard", **kwargs) -> PipelineManager:
    """
    创建NL2SQL Pipeline的便捷函数

    Args:
        pipeline_type: Pipeline类型 ("standard", "enhanced", "enhanced_with_parser", "minimal", "custom")
        **kwargs: 其他参数

    Returns:
        配置好的Pipeline管理器
    """
    factory = PipelineFactory()

    if pipeline_type == "standard":
        return factory.create_nl2sql_pipeline(**kwargs)
    elif pipeline_type == "enhanced":
        return factory.create_enhanced_nl2sql_pipeline(**kwargs)
    elif pipeline_type == "enhanced_with_parser":
        return factory.create_enhanced_with_parser_pipeline(**kwargs)
    elif pipeline_type == "minimal":
        return factory.create_minimal_pipeline()
    elif pipeline_type == "custom":
        step_names = kwargs.get("step_names", [])
        return factory.create_custom_pipeline(step_names)
    else:
        raise ValueError(f"未知的Pipeline类型: {pipeline_type}")

# ==================== Pipeline执行器 ====================

class NL2SQLExecutor:
    """
    NL2SQL执行器
    提供统一的执行接口
    """
    
    def __init__(self, pipeline_type: str = "standard", **kwargs):
        self.pipeline = create_nl2sql_pipeline(pipeline_type, **kwargs)
        self.pipeline_type = pipeline_type
    
    async def execute(self, 
                     user_question: str,
                     hint: str = "",
                     dept_id: Optional[str] = None,
                     **kwargs) -> Dict[str, Any]:
        """
        执行NL2SQL查询
        
        Args:
            user_question: 用户问题
            hint: 提示信息
            dept_id: 部门ID（增强模式需要）
            **kwargs: 其他参数
            
        Returns:
            执行结果
        """
        # 创建上下文
        context = ContextFactory.create_nl2sql_context(
            user_question=user_question,
            hint=hint,
            dept_id=dept_id,
            **kwargs
        )
        
        # 执行Pipeline
        result = await self.pipeline.execute(context)
        
        # 提取最终结果
        final_data = self._extract_final_results(result["context"])
        
        return {
            "success": result["success"],
            "pipeline_type": self.pipeline_type,
            "execution_time": result["execution_time"],
            "executed_steps": result["executed_step_names"],
            "failed_steps": result["failed_step_names"],
            "data": final_data,
            "context": result["context"],
            "step_results": result["step_results"]
        }
    
    def _extract_final_results(self, context) -> Dict[str, Any]:
        """提取最终结果"""
        return {
            "user_question": context.user_question,
            "selected_columns": context.get("selected_columns", {}),
            "db_schema": context.get("db_schema", ""),
            "sql_candidates": context.get("sql_candidates", []),
            "execution_results": context.get("execution_results", []),
            "business_logic": context.get("business_logic", {}),
            "table_metadata": context.get("table_metadata", []),
            "column_metadata": context.get("column_metadata", [])
        }
    
    async def __call__(self, user_question: str, **kwargs):
        """使执行器可以像函数一样调用"""
        return await self.execute(user_question, **kwargs)

# ==================== 使用示例 ====================

async def example_usage():
    """使用示例"""
    
    # 方式1: 使用标准Pipeline
    executor = NL2SQLExecutor("standard")
    result = await executor.execute(
        user_question="查询2024年制造业贷款余额",
        hint="银保监会口径",
        keywords=["制造业", "贷款", "余额"],
        candidate_columns={
            "loan_table": ["amount", "industry", "year"]
        }
    )
    
    print(f"标准Pipeline执行结果: {result['success']}")
    print(f"生成的SQL: {result['data']['sql_candidates']}")
    
    # 方式2: 使用增强Pipeline（需要dept_id）
    enhanced_executor = NL2SQLExecutor("enhanced")
    enhanced_result = await enhanced_executor.execute(
        user_question="查询客户贷款信息",
        hint="银保监会口径",
        dept_id="DEPT001"
    )
    
    print(f"增强Pipeline执行结果: {enhanced_result['success']}")
    print(f"业务逻辑: {enhanced_result['data']['business_logic']}")
    
    # 方式3: 使用自定义Pipeline
    custom_executor = NL2SQLExecutor("custom", step_names=[
        "key_associator",
        "column_selector", 
        "schema_generator",
        "sql_generator"
    ])
    
    custom_result = await custom_executor.execute(
        user_question="统计各行业客户数量"
    )
    
    print(f"自定义Pipeline执行结果: {custom_result['success']}")
    
    return result

if __name__ == "__main__":
    import asyncio
    asyncio.run(example_usage())

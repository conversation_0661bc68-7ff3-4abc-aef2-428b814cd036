#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：外规内化 
@File    ：ier_file_process_api.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/21 17:25 
@Desc    ：外规内化文件处理相关的api接口（文件上传、文件信息提取）
"""
from typing import Any, List, Tuple

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from loguru import logger

from app.ier.config.config import (RDB_CLIENT_CONFIG,
                                   VDB_CLIENT_CONFIG,
                                   LLM_CLIENT_CONFIG,
                                   EMBEDDING_CLIENT_CONFIG)
from app.ier.services.ier.model.ier_file_process_model import (UploadLawRequestModel,
                                                               QueryLawFileOcrStatusRequestModel,
                                                               IsMainLawFileRequestModel,
                                                               IsMainLawFile,
                                                               LawParseRequestModel,
                                                               LawModel,
                                                               LawMatchModel)
from app.ier.services.ier.file_process_logic import LawFileProcessLogic
from service import get_client

# 定义路由
router = APIRouter(tags=["外规内化-法规文件处理"])

# todo 完成实际逻辑开发
async def get_law_file_process_logic() -> LawFileProcessLogic:
    """获取法规文件处理实例"""
    try:
        rdb_client = await get_client(RDB_CLIENT_CONFIG)
        vdb_client = await get_client(VDB_CLIENT_CONFIG)
        llm_client = await get_client(LLM_CLIENT_CONFIG)
        embed_client = await get_client(EMBEDDING_CLIENT_CONFIG)
        return LawFileProcessLogic(rdb_client, vdb_client, llm_client, embed_client)
    except Exception as e:
        logger.error(f"外规内化法规文件逻辑实例失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务初始化失败: {e}"
        )

def register_ier_file_routers():
    @router.post("/upload_file_ocr", response_model=None, summary="法规上传接口")
    async def upload_file_ocr(
        request: UploadLawRequestModel,
        logic: LawFileProcessLogic = Depends(get_law_file_process_logic)
    ):
        """上传法规接口"""
        try:
            logger.info(f"receive upload_file_ocr request: request_id={request.request_id}")
            await logic.upload_file(request)
        except Exception as e:
            logger.error(f"上传法规失败: {e}")
            raise HTTPException(status_code=503, detail=f"服务不可用: {e}")

    @router.post("/get_ocr_request_status", response_model=dict, summary="检查法规文件ocr状态接口")
    async def get_ocr_request_status(
            request: QueryLawFileOcrStatusRequestModel,
            logic: LawFileProcessLogic = Depends(get_law_file_process_logic)
    ) -> dict:
        """法规OCR状态接口"""
        try:
            logger.info(f"receive upload_file_ocr request: request_id={request.request_id}")
            ocr_status = await logic.query_file_status(request)
        except Exception as e:
            logger.error(f"上传法规失败: {e}")
            raise HTTPException(status_code=503, detail=f"服务不可用: {e}")
        return ocr_status.to_dict()

    @router.post("/judge_main_file", response_model=List[IsMainLawFile], summary="判断主文件接口")
    async def judge_main_file(
            request: IsMainLawFileRequestModel,
            logic: LawFileProcessLogic = Depends(get_law_file_process_logic)
    ):
        try:
            law_file_list = request.law_file_list
            # 将 Pydantic 模型转换为字典列表
            file_info_list = [law_file.model_dump() for law_file in law_file_list]
            logger.info(f"Judge main file, file info list:{file_info_list}")

            # 调用 main_file_judge 函数处理文件信息
            result = await logic.is_main_file(file_info_list)
            return result

        except Exception as e:
            logger.error(f"API error: {str(e)}")
            raise HTTPException(status_code=503, detail=f"服务不可用: {e}")

    @router.post("/parse", response_model=dict, summary="法规解析接口(解析主文件)")
    async def upload_attachment(
            request: LawParseRequestModel,
            logic: LawFileProcessLogic = Depends(get_law_file_process_logic)
    ):
        return StreamingResponse(logic.parse_law(request), media_type="text/event-stream")

    @router.post("/upload_file", response_model=dict, summary="法规入库接口")
    async def upload_file(
            request: LawModel,
            logic: LawFileProcessLogic = Depends(get_law_file_process_logic)
    ):
        try:
            logger.info(f"law in storage, request parameter{request.model_dump()}")
            await logic.law_in_storage(request)
            return {"status": "ok"}
        except Exception as e:
            logger.error(f"法规入库失败: {e}")
            raise HTTPException(status_code=503, detail=f"服务不可用: {e}")

    @router.post("/match_file", response_model=dict, summary="法规匹配接口")
    async def sse_stream_match_file(
            request: LawMatchModel,
            logic: LawFileProcessLogic = Depends(get_law_file_process_logic)
    ):
        return StreamingResponse(logic.match_file(request), media_type="text/event-stream")



    @router.post("/test", response_model=dict, summary="test")
    async def test(
            request: LawModel,
            logic: LawFileProcessLogic = Depends(get_law_file_process_logic)
    ):
        try:
            logger.info(f"law in storage, request parameter{request.model_dump()}")
            result = await logic.llm_extract_law_name_chunk(request)
            return {"status": result}
        except Exception as e:
            logger.error(f"法规入库失败: {e}")
            raise HTTPException(status_code=503, detail=f"服务不可用: {e}")



    return router


# todo 完成测试
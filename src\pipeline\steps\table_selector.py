"""
表选择步骤 - 多Schema片段专用版本
从多个Schema片段的候选表中选择最相关的表，支持并发处理和智能合并
"""

from typing import Dict, Any, List, Union
import json
import ast
import asyncio
from copy import deepcopy
import logging

logger = logging.getLogger(__name__)

from pipeline.core.base_step import LLMStep
from pipeline.core.context import PipelineContext

class TableSelectorStep(LLMStep):
    """
    表选择步骤 - 多Schema片段专用版本

    功能：
    - 并发处理多个Schema片段
    - 智能合并多个片段的表选择结果
    - 自动去重和结果优化

    输入格式：
    context.db_schema = [
        {
            "prompt": "CREATE TABLE ...",
            "db_to_tables": {"db1": ["table1", "table2"]},
            "table_to_columns": {"table1": ["col1", "col2"]}
        },
        ...
    ]

    输出格式：
    context.candidate_tables = {
        "database1": ["table1", "table2"],
        "database2": ["table3", "table4"]
    }
    """

    def __init__(self):
        super().__init__(
            name="table_selector",
            description="多Schema片段并发表选择，智能合并结果",
            template_name="select_tables",
            parser_name="select_tables",
            num_calls=1
        )
    
    async def preprocess(self, context: PipelineContext) -> Union[List[Dict[str, Any]], None]:
        """阶段1: 预处理 - 构建多Schema片段的prompt变量"""
        db_schema_list = context.get("db_schema", [])

        # 验证输入格式
        if not isinstance(db_schema_list, list):
            logger.error(f"db_schema必须是列表格式，当前类型: {type(db_schema_list)}")
            return None

        if not db_schema_list:
            logger.warning("Schema片段列表为空")
            return None

        return self._build_fragment_prompts(db_schema_list, context)

    def _build_fragment_prompts(self, db_schema_list: List[Dict[str, Any]], context: PipelineContext) -> List[Dict[str, Any]]:
        """构建多Schema片段的prompt数据"""
        # 使用filter和列表推导式过滤有效的schema片段
        def is_valid_fragment(fragment_with_index):
            i, fragment = fragment_with_index
            if not isinstance(fragment, dict):
                logger.warning(f"Schema片段 {i} 格式无效，跳过")
                return False
            if not fragment.get("prompt", "").strip():
                logger.warning(f"Schema片段 {i} 的prompt为空，跳过")
                return False
            if not fragment.get("db_to_tables", {}):
                logger.warning(f"Schema片段 {i} 的db_to_tables为空，跳过")
                return False
            return True

        valid_fragments = [
            {
                "prompt": fragment["prompt"],
                "db_to_tables": fragment["db_to_tables"]
            }
            for i, fragment in filter(is_valid_fragment, enumerate(db_schema_list))
        ]

        if not valid_fragments:
            logger.error("没有找到有效的Schema片段")
            return None

        logger.info(f"构建 {len(valid_fragments)} 个有效Schema片段的prompt数据")

        # 使用列表推导式构建prompt数据
        fragment_prompts = [
            {
                "system": {
                    "QUESTION": context.user_question,
                    "HINT": context.hint,
                    "DATABASE_SCHEMA": fragment["prompt"]
                },
                "user": {
                    "CANDIDATE_TABLES": fragment["db_to_tables"]
                }
            }
            for fragment in valid_fragments
        ]
        
        logger.debug(f"构建prompt数据: {fragment_prompts}")
        return fragment_prompts

    async def process(self, fragment_prompts: List[Dict[str, Any]], context: PipelineContext) -> List[str]:
        """阶段2: 核心处理 - 并发处理多个Schema片段"""
        if not fragment_prompts:
            logger.warning("没有有效的片段prompts")
            return []

        logger.info(f"开始并发处理 {len(fragment_prompts)} 个Schema片段")

        # 使用列表推导式创建并发任务
        tasks = [
            self._process_single_fragment(i, prompt_data, context)
            for i, prompt_data in enumerate(fragment_prompts)
        ]

        # 并发执行所有任务
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果，过滤异常
            valid_responses = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"片段 {i} 处理失败: {result}")
                elif isinstance(result, list) and result:
                    valid_responses.extend(result)
                    logger.info(f"片段 {i} 处理成功，获得 {len(result)} 个响应")
                else:
                    logger.warning(f"片段 {i} 返回空结果")

            logger.info(f"多片段处理完成，总共获得 {len(valid_responses)} 个有效响应")
            return valid_responses

        except Exception as e:
            logger.error(f"多片段并发处理失败: {e}")
            return []

    async def _process_single_fragment(self, fragment_index: int, prompt_data: Dict[str, Any], context: PipelineContext) -> List[str]:
        """处理单个Schema片段"""
        try:
            # 构建messages
            messages = await self.build_prompt(prompt_data, context)

            # 调用LLM
            responses = await self.call_llm(messages, context)

            logger.debug(f"片段 {fragment_index} LLM调用成功")
            return responses

        except Exception as e:
            logger.error(f"片段 {fragment_index} 处理失败: {e}")
            # 返回异常而不是空列表，让上层处理
            raise

    async def parse_result(self, result: List[str], context: PipelineContext) -> List[Any]:
        """阶段3: 解析LLM结果（支持多片段结果）"""
        if not result:
            logger.warning("没有LLM结果需要解析")
            return []

        # 先使用父类的解析器解析
        parsed_results = await super().parse_result(result, context)

        # 进一步处理解析结果
        final_results = []
        for i, parsed in enumerate(parsed_results):
            if isinstance(parsed, str):
                try:
                    # 尝试JSON解析
                    parsed = json.loads(parsed)
                except json.JSONDecodeError:
                    try:
                        # 备选：Python字面量解析
                        parsed = ast.literal_eval(parsed)
                    except (SyntaxError, ValueError):
                        logger.error(f"无法解析表选择结果 {i}: {parsed[:200] if parsed else 'None'}...")
                        parsed = {}

            # 确保解析结果是字典格式
            if isinstance(parsed, dict):
                final_results.append(parsed)
            else:
                logger.warning(f"解析结果 {i} 不是字典格式，跳过: {type(parsed)}")

        logger.info(f"成功解析 {len(final_results)} 个有效的表选择结果")
        return final_results
    
    async def postprocess(self, parsed_results: List[Any], context: PipelineContext) -> Dict[str, List[str]]:
        """阶段4: 后处理 - 合并多个片段的表选择结果"""
        if not parsed_results:
            logger.warning("表选择步骤没有获得有效结果")
            return {}

        return self._merge_multiple_results(parsed_results)

    def _merge_multiple_results(self, results: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """合并多个片段的表选择结果"""
        logger.info(f"开始合并 {len(results)} 个片段的表选择结果")

        merged_tables = {}

        for i, result in enumerate(results):
            if not isinstance(result, dict):
                logger.warning(f"结果 {i} 格式不正确，跳过: {type(result)}")
                continue

            # 处理表信息，移除思考过程
            result_tables = deepcopy(result)
            result_tables.pop('chain_of_thought_reasoning', None)

            # 合并到总结果中
            for db_name, tables in result_tables.items():
                if not isinstance(tables, list):
                    logger.warning(f"结果 {i} 中数据库 {db_name} 的表不是列表格式，跳过")
                    continue

                if db_name not in merged_tables:
                    merged_tables[db_name] = []

                # 合并表，去重但保持顺序
                for table in tables:
                    if table not in merged_tables[db_name]:
                        merged_tables[db_name].append(table)

        # 过滤掉空列表的数据库项
        filtered_tables = {db_name: tables for db_name, tables in merged_tables.items() if tables}
        
        # 记录过滤前后的变化
        filtered_count = len(merged_tables) - len(filtered_tables)
        if filtered_count > 0:
            logger.info(f"过滤掉 {filtered_count} 个空表列表的数据库项")

        # 记录合并结果
        total_dbs = len(filtered_tables)
        total_tables = sum(len(tables) for tables in filtered_tables.values())
        logger.info(f"合并完成: {total_dbs} 个数据库，共 {total_tables} 个表")

        # 记录详细的合并信息
        for db_name, tables in filtered_tables.items():
            logger.debug(f"数据库 {db_name}: {len(tables)} 个表 - {tables}")

        return filtered_tables
    
    async def update_context(self, result: Dict[str, List[str]], context: PipelineContext) -> None:
        """更新上下文 - 存储选择的表并更新schema_generation_params"""
        context.set("candidate_tables", result)

        # 使用列表推导式和chain.from_iterable提取所有表名，并使用set去重
        from itertools import chain
        unique_table_names = list(set(chain.from_iterable(
            tables for tables in result.values() if isinstance(tables, list)
        )))

        # 存储到context中
        context.set("selected_table_names", unique_table_names)

        # 更新schema_generation_params用于下一个schema_generator
        current_params = context.get("schema_generation_params", {})
        updated_params = current_params.copy()

        # 移除table_ids，添加table_names
        if "table_ids" in updated_params:
            del updated_params["table_ids"]
        updated_params["table_names"] = unique_table_names

        # 设置无限制列数（用于第2次schema生成）
        updated_params["column_limit"] = None
        updated_params["is_final"] = False

        context.set("schema_generation_params", updated_params)

        logger.info(f"提取表名列表完成: {len(unique_table_names)} 个唯一表名 - {unique_table_names}")
        logger.info(f"已更新schema_generation_params：table_names包含{len(unique_table_names)}个表，column_limit=None")
    
    def format_display_result(self, final_result: Dict[str, Any]) -> str:
        """
        格式化多Schema片段合并结果用于显示

        Args:
            final_result: 合并后的表选择结果

        Returns:
            格式化后的字符串
        """
        if not final_result or not isinstance(final_result, dict):
            return "❌ 未获得有效的筛选表结果或结果格式不正确。"

        display_parts = ["## 📋 多Schema片段表选择结果：\n"]

        if not final_result:
            display_parts.append("- 未筛选出任何相关表。")
        else:
            # 统计信息
            total_dbs = len(final_result)
            total_tables = sum(len(tables) if isinstance(tables, list) else 0 for tables in final_result.values())
            display_parts.append(f"📊 **统计**: {total_dbs} 个数据库，共 {total_tables} 个表\n")

            # 显示每个数据库的表
            for db_name, tables in final_result.items():
                if isinstance(tables, list):
                    if tables:
                        display_parts.append(f"- **数据库:** `{db_name}` ({len(tables)} 个表)")
                        display_parts.append(f"  - **选中表:** {', '.join(f'`{table}`' for table in tables)}")
                    else:
                        display_parts.append(f"- **数据库:** `{db_name}` (未选中表)")
                else:
                    display_parts.append(f"- **数据库:** `{db_name}` (数据格式错误，非列表)")

        return "\n".join(display_parts)

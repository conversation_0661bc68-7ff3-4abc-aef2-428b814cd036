"""
基础设施模块

包含数据模型、常量、异常、处理器等基础组件
"""

from .models import (
    DepartmentAssignmentRequest,
    DepartmentAssignmentResult,
    HistoricalRecord,
    FilterResult,
    SearchStrategy,
    DepartmentRecommendation,
    KeywordExtractionResult,
    SearchConditionConfig,
    BatchAssignmentRequest,
    BatchAssignmentItem,
    BatchAssignmentResponse,
    BatchProcessingResult
)
from .constants import DepartmentAssignmentConstants, DepartmentAssignmentUtils
from .exceptions import (
    DepartmentAssignmentError,
    DepartmentAssignmentValidationError,
    DepartmentAssignmentSearchError,
    DepartmentAssignmentFilterError
)
from .nlp_processor import NLPProcessor, get_nlp_processor
from .search_builder import DynamicSearchBuilder, get_search_builder

__all__ = [
    # 数据模型
    "DepartmentAssignmentRequest",
    "DepartmentAssignmentResult", 
    "HistoricalRecord",
    "FilterResult",
    "SearchStrategy",
    "DepartmentRecommendation",
    "KeywordExtractionResult",
    "SearchConditionConfig",
    "BatchAssignmentRequest",
    "BatchAssignmentItem",
    "BatchAssignmentResponse",
    "BatchProcessingResult",
    
    # 常量和工具
    "DepartmentAssignmentConstants",
    "DepartmentAssignmentUtils",
    
    # 异常
    "DepartmentAssignmentError",
    "DepartmentAssignmentValidationError",
    "DepartmentAssignmentSearchError", 
    "DepartmentAssignmentFilterError",
    
    # 处理器
    "NLPProcessor",
    "get_nlp_processor",
    "DynamicSearchBuilder",
    "get_search_builder"
]

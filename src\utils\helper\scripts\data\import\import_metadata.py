#!/usr/bin/env python3
"""
元数据导入脚本

从Excel/CSV文件导入元数据到数据库

使用方法:
    python scripts/data/import/import_metadata.py --file data.xlsx --type database
    python scripts/data/import/import_metadata.py --file tables.csv --type table
"""

import argparse
import asyncio
import sys
from pathlib import Path

# 🔧 内联路径设置 - 适合简单脚本
def setup_src_import():
    """设置src目录导入路径"""
    current_file = Path(__file__).resolve()
    # 从 scripts/data/import/import_metadata.py 向上3级到项目根目录
    project_root = current_file.parent.parent.parent.parent
    src_dir = project_root / "src"
    
    if src_dir.exists() and str(src_dir) not in sys.path:
        sys.path.insert(0, str(src_dir))
        return True
    return False

# 立即设置路径
if not setup_src_import():
    print("❌ 无法找到src目录，请确保在正确的项目目录下运行")
    sys.exit(1)

# 现在可以导入src模块
from modules.knowledge.metadata import MetadataCrud
from modules.knowledge.metadata.templates import MetadataTemplateManager
from service import get_client
from utils.common.logger import setup_enterprise_logger


async def import_metadata_file(file_path: str, data_type: str):
    """
    导入元数据文件
    
    Args:
        file_path: 文件路径
        data_type: 数据类型 (database/table/column/code_set)
    """
    print(f"📂 开始导入 {data_type} 数据: {file_path}")
    
    if not Path(file_path).exists():
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    try:
        # 获取数据库客户端
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        
        # 创建CRUD和模板管理器
        metadata_crud = MetadataCrud(rdb_client, vdb_client)
        template_manager = MetadataTemplateManager(rdb_client, vdb_client)
        
        # 解析文件
        print("🔍 解析文件内容...")
        parsed_data = await template_manager.parse_template_file(file_path, data_type)
        
        print(f"📊 找到 {len(parsed_data)} 条记录")
        
        # 批量导入数据
        success_count = 0
        error_count = 0
        
        for i, record in enumerate(parsed_data, 1):
            try:
                if data_type == "database":
                    await metadata_crud.create_database(record)
                elif data_type == "table":
                    await metadata_crud.create_table(record)
                elif data_type == "column":
                    await metadata_crud.create_column(record)
                elif data_type == "code_set":
                    await metadata_crud.create_code_set(record)
                else:
                    raise ValueError(f"不支持的数据类型: {data_type}")
                
                success_count += 1
                print(f"✅ 记录 {i}/{len(parsed_data)} 导入成功")
                
            except Exception as e:
                error_count += 1
                print(f"❌ 记录 {i}/{len(parsed_data)} 导入失败: {e}")
        
        print(f"\n📈 导入完成统计:")
        print(f"   成功: {success_count}")
        print(f"   失败: {error_count}")
        print(f"   总计: {len(parsed_data)}")
        
    except Exception as e:
        print(f"❌ 导入过程失败: {e}")
        raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='元数据导入工具')
    parser.add_argument('--file', 
                       required=True,
                       help='要导入的文件路径')
    parser.add_argument('--type',
                       choices=['database', 'table', 'column', 'code_set'],
                       required=True,
                       help='数据类型')
    parser.add_argument('--dry-run',
                       action='store_true',
                       help='只解析文件，不实际导入')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_enterprise_logger(
        level="INFO",
        service_name=f"metadata-import-{args.type}"
    )
    
    if args.dry_run:
        print("🧪 试运行模式：只解析文件，不实际导入数据")
        # 这里只做文件解析验证
        return
    
    # 执行导入
    asyncio.run(import_metadata_file(args.file, args.type))


if __name__ == '__main__':
    main() 
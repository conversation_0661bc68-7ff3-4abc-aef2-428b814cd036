"""
Configuration Schema

配置模式定义 - 定义配置的结构和约束

Author: AI Assistant
Created: 2025-07-11
"""

from typing import Any, Dict, List, Optional
from dataclasses import dataclass, field


@dataclass
class FieldSchema:
    """字段模式定义"""
    name: str
    type: str
    required: bool = True
    default: Any = None
    description: str = ""
    constraints: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConfigSchema:
    """配置模式定义"""
    name: str
    description: str = ""
    fields: List[FieldSchema] = field(default_factory=list)
    children: Dict[str, 'ConfigSchema'] = field(default_factory=dict)
    
    def add_field(self, field_schema: FieldSchema):
        """添加字段"""
        self.fields.append(field_schema)
    
    def add_child(self, name: str, schema: 'ConfigSchema'):
        """添加子模式"""
        self.children[name] = schema
    
    def get_field(self, name: str) -> Optional[FieldSchema]:
        """获取字段模式"""
        for field in self.fields:
            if field.name == name:
                return field
        return None
    
    def get_child(self, name: str) -> Optional['ConfigSchema']:
        """获取子模式"""
        return self.children.get(name)


# 预定义的配置模式
def create_database_schema() -> ConfigSchema:
    """创建数据库配置模式"""
    db_schema = ConfigSchema(
        name="database",
        description="数据库配置模式"
    )
    
    # RDB配置模式
    rdb_schema = ConfigSchema(
        name="rdb",
        description="关系数据库配置模式"
    )
    
    rdb_schema.add_field(FieldSchema(
        name="_target_",
        type="str",
        required=True,
        description="客户端目标类"
    ))
    
    rdb_schema.add_field(FieldSchema(
        name="host",
        type="str",
        required=True,
        description="数据库主机"
    ))
    
    rdb_schema.add_field(FieldSchema(
        name="port",
        type="int",
        required=True,
        description="数据库端口",
        constraints={"min": 1, "max": 65535}
    ))
    
    rdb_schema.add_field(FieldSchema(
        name="database",
        type="str",
        required=True,
        description="数据库名称"
    ))
    
    rdb_schema.add_field(FieldSchema(
        name="username",
        type="str",
        required=True,
        description="用户名"
    ))
    
    rdb_schema.add_field(FieldSchema(
        name="password",
        type="str",
        required=False,
        description="密码"
    ))
    
    # VDB配置模式
    vdb_schema = ConfigSchema(
        name="vdb",
        description="向量数据库配置模式"
    )
    
    vdb_schema.add_field(FieldSchema(
        name="_target_",
        type="str",
        required=True,
        description="客户端目标类"
    ))
    
    vdb_schema.add_field(FieldSchema(
        name="host",
        type="str",
        required=True,
        description="数据库主机"
    ))
    
    vdb_schema.add_field(FieldSchema(
        name="port",
        type="int",
        required=True,
        description="数据库端口",
        constraints={"min": 1, "max": 65535}
    ))
    
    vdb_schema.add_field(FieldSchema(
        name="database",
        type="str",
        required=True,
        description="数据库名称"
    ))
    
    vdb_schema.add_field(FieldSchema(
        name="username",
        type="str",
        required=True,
        description="用户名"
    ))
    
    # 添加子模式
    rdbs_schema = ConfigSchema(name="rdbs", description="关系数据库集合")
    vdbs_schema = ConfigSchema(name="vdbs", description="向量数据库集合")
    
    db_schema.add_child("rdbs", rdbs_schema)
    db_schema.add_child("vdbs", vdbs_schema)
    
    return db_schema


def create_llm_schema() -> ConfigSchema:
    """创建LLM配置模式"""
    llm_schema = ConfigSchema(
        name="llm",
        description="大语言模型配置模式"
    )
    
    llm_schema.add_field(FieldSchema(
        name="_target_",
        type="str",
        required=True,
        description="LLM客户端目标类"
    ))
    
    llm_schema.add_field(FieldSchema(
        name="api_key",
        type="str",
        required=False,
        description="API密钥"
    ))
    
    llm_schema.add_field(FieldSchema(
        name="base_url",
        type="str",
        required=False,
        description="API基础URL"
    ))
    
    llm_schema.add_field(FieldSchema(
        name="model",
        type="str",
        required=True,
        description="模型名称"
    ))
    
    return llm_schema


def create_embedding_schema() -> ConfigSchema:
    """创建嵌入模型配置模式"""
    embedding_schema = ConfigSchema(
        name="embedding",
        description="嵌入模型配置模式"
    )
    
    embedding_schema.add_field(FieldSchema(
        name="_target_",
        type="str",
        required=True,
        description="嵌入模型客户端目标类"
    ))
    
    embedding_schema.add_field(FieldSchema(
        name="model",
        type="str",
        required=True,
        description="模型名称"
    ))
    
    embedding_schema.add_field(FieldSchema(
        name="dimension",
        type="int",
        required=False,
        description="向量维度",
        constraints={"min": 1}
    ))
    
    return embedding_schema


def create_root_schema() -> ConfigSchema:
    """创建根配置模式"""
    root_schema = ConfigSchema(
        name="root",
        description="根配置模式"
    )
    
    # 添加子模式
    root_schema.add_child("database", create_database_schema())
    root_schema.add_child("llm", create_llm_schema())
    root_schema.add_child("embedding", create_embedding_schema())
    
    return root_schema

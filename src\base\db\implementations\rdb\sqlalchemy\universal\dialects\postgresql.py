

from typing import Dict, Any, List, Optional, Tuple
from .base import DatabaseDialect, DatabaseFeatures, FeatureSupport


class PostgreSQLDialect(DatabaseDialect):
    """PostgreSQL database dialect implementation"""
    
    @property
    def name(self) -> str:
        return "postgresql"
    
    @property
    def description(self) -> str:
        return "PostgreSQL Database (9.6+)"
    
    @property
    def default_port(self) -> Optional[int]:
        return 5432
    
    @property
    def default_driver(self) -> str:
        return "psycopg2"
    
    @property
    def async_driver(self) -> Optional[str]:
        return "asyncpg"
    
    @property
    def features(self) -> DatabaseFeatures:
        return DatabaseFeatures(
            json_support=FeatureSupport.FULL,      # Native JSON/JSONB
            array_support=FeatureSupport.FULL,     # Native array support
            window_functions=FeatureSupport.FULL,  # Excellent window function support
            cte_support=FeatureSupport.FULL,       # Full CTE support including recursive
            full_text_search=FeatureSupport.FULL,  # Advanced full-text search
            partitioning=FeatureSupport.FULL,      # Declarative partitioning
            upsert_support=FeatureSupport.FULL,    # ON CONFLICT DO UPDATE
            returning_clause=FeatureSupport.FULL,  # RETURNING clause
            bulk_insert=FeatureSupport.FULL,       # COPY, bulk operations
            async_support=FeatureSupport.FULL      # asyncpg
        )
    
    # ==================== Query Building ====================
    
    def build_limit_clause(self, limit: int, offset: Optional[int] = None) -> str:
        """Build PostgreSQL LIMIT clause"""
        clause = f"LIMIT {limit}"
        if offset is not None:
            clause += f" OFFSET {offset}"
        return clause
    
    def build_upsert_statement(
        self,
        table_name: str,
        data: Dict[str, Any],
        conflict_columns: List[str],
        update_columns: Optional[List[str]] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """Build PostgreSQL ON CONFLICT DO UPDATE statement"""
        
        # Determine columns to update
        if update_columns is None:
            # Update all columns except conflict columns
            update_columns = [col for col in data.keys() if col not in conflict_columns]
        
        # Build INSERT part
        columns = list(data.keys())
        placeholders = ", ".join([f"%(col_{col})s" for col in columns])
        columns_clause = ", ".join([self.quote_identifier(col) for col in columns])
        
        insert_sql = f"INSERT INTO {self.quote_identifier(table_name)} ({columns_clause}) VALUES ({placeholders})"
        
        # Build ON CONFLICT part
        conflict_clause = ", ".join([self.quote_identifier(col) for col in conflict_columns])
        
        if update_columns:
            update_clauses = []
            for col in update_columns:
                quoted_col = self.quote_identifier(col)
                update_clauses.append(f"{quoted_col} = EXCLUDED.{quoted_col}")
            
            update_sql = f" ON CONFLICT ({conflict_clause}) DO UPDATE SET " + ", ".join(update_clauses)
            full_sql = insert_sql + update_sql
        else:
            full_sql = insert_sql + f" ON CONFLICT ({conflict_clause}) DO NOTHING"
        
        # Add RETURNING clause
        full_sql += " RETURNING *"
        
        # Prepare parameters with col_ prefix
        params = {f"col_{k}": v for k, v in data.items()}
        
        return full_sql, params
    
    def build_bulk_insert_statement(
        self,
        table_name: str,
        columns: List[str],
        batch_size: int,
        ignore_conflicts: bool = False
    ) -> str:
        """Build PostgreSQL bulk insert statement"""
        placeholders = ", ".join(["%s"] * len(columns))
        values_clause = ", ".join([f"({placeholders})"] * batch_size)
        columns_clause = ", ".join([self.quote_identifier(col) for col in columns])
        
        sql = f"INSERT INTO {self.quote_identifier(table_name)} ({columns_clause}) VALUES {values_clause}"
        
        if ignore_conflicts:
            # Use ON CONFLICT DO NOTHING for ignoring conflicts
            sql += " ON CONFLICT DO NOTHING"
        
        return sql
    
    # ==================== Identifier Handling ====================
    
    def get_identifier_quote_char(self) -> str:
        """PostgreSQL uses double quotes for identifier quoting"""
        return '"'
    
    def get_parameter_placeholder(self) -> str:
        """PostgreSQL uses %s for parameter placeholders"""
        return "%s"
    
    # ==================== Data Type Mapping ====================
    
    def map_python_type_to_sql(self, python_type: type) -> str:
        """Map Python types to PostgreSQL SQL types"""
        type_mapping = {
            int: "INTEGER",
            float: "DOUBLE PRECISION",
            str: "TEXT",
            bool: "BOOLEAN",
            bytes: "BYTEA",
            dict: "JSONB",
            list: "JSONB",
        }
        
        return type_mapping.get(python_type, "TEXT")
    
    def get_boolean_literal(self, value: bool) -> str:
        """PostgreSQL boolean literals"""
        return "TRUE" if value else "FALSE"
    
    # ==================== PostgreSQL-Specific Features ====================
    
    def build_array_query(
        self,
        table_name: str,
        array_column: str,
        array_value: Any,
        operation: str = "contains"
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Build PostgreSQL array query
        
        Args:
            table_name: Target table
            array_column: Array column name
            array_value: Value to search for
            operation: Array operation (contains, contained_by, overlap)
        
        Returns:
            Tuple of (SQL, parameters)
        """
        table = self.quote_identifier(table_name)
        column = self.quote_identifier(array_column)
        
        if operation == "contains":
            operator = "@>"
        elif operation == "contained_by":
            operator = "<@"
        elif operation == "overlap":
            operator = "&&"
        else:
            raise ValueError(f"Unsupported array operation: {operation}")
        
        sql = f"SELECT * FROM {table} WHERE {column} {operator} %(array_value)s"
        
        return sql, {"array_value": array_value}
    
    def build_jsonb_query(
        self,
        table_name: str,
        json_column: str,
        json_path: str,
        value: Any = None,
        operation: str = "extract"
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Build PostgreSQL JSONB query
        
        Args:
            table_name: Target table
            json_column: JSONB column name
            json_path: JSON path expression
            value: Value to compare (for operations other than extract)
            operation: JSONB operation (extract, contains, exists)
        
        Returns:
            Tuple of (SQL, parameters)
        """
        table = self.quote_identifier(table_name)
        column = self.quote_identifier(json_column)
        
        if operation == "extract":
            sql = f"SELECT {column} -> %(json_path)s AS extracted_value FROM {table}"
            params = {"json_path": json_path}
        elif operation == "contains":
            sql = f"SELECT * FROM {table} WHERE {column} @> %(json_value)s"
            params = {"json_value": {json_path: value}}
        elif operation == "exists":
            sql = f"SELECT * FROM {table} WHERE {column} ? %(json_path)s"
            params = {"json_path": json_path}
        else:
            raise ValueError(f"Unsupported JSONB operation: {operation}")
        
        return sql, params
    
    def build_fulltext_search_query(
        self,
        table_name: str,
        search_columns: List[str],
        search_term: str,
        language: str = "english"
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Build PostgreSQL full-text search query
        
        Args:
            table_name: Target table
            search_columns: Columns to search in
            search_term: Search term
            language: Text search language
        
        Returns:
            Tuple of (SQL, parameters)
        """
        table = self.quote_identifier(table_name)
        
        # Build tsvector from multiple columns
        if len(search_columns) == 1:
            tsvector_expr = f"to_tsvector('{language}', {self.quote_identifier(search_columns[0])})"
        else:
            column_exprs = [f"to_tsvector('{language}', {self.quote_identifier(col)})" for col in search_columns]
            tsvector_expr = " || ".join(column_exprs)
        
        sql = f"""
        SELECT *, 
               ts_rank({tsvector_expr}, to_tsquery('{language}', %(search_term)s)) AS relevance
        FROM {table}
        WHERE {tsvector_expr} @@ to_tsquery('{language}', %(search_term)s)
        ORDER BY relevance DESC
        """
        
        return sql, {"search_term": search_term}
    
    def build_window_function_query(
        self,
        table_name: str,
        select_columns: List[str],
        window_function: str,
        partition_by: Optional[List[str]] = None,
        order_by: Optional[List[str]] = None
    ) -> str:
        """
        Build PostgreSQL window function query
        
        Args:
            table_name: Target table
            select_columns: Columns to select
            window_function: Window function (ROW_NUMBER, RANK, etc.)
            partition_by: Columns to partition by
            order_by: Columns to order by
        
        Returns:
            SQL query
        """
        table = self.quote_identifier(table_name)
        columns = ", ".join([self.quote_identifier(col) for col in select_columns])
        
        window_clause = f"{window_function}() OVER ("
        
        if partition_by:
            partition_cols = ", ".join([self.quote_identifier(col) for col in partition_by])
            window_clause += f"PARTITION BY {partition_cols}"
        
        if order_by:
            if partition_by:
                window_clause += " "
            order_cols = ", ".join([self.quote_identifier(col) for col in order_by])
            window_clause += f"ORDER BY {order_cols}"
        
        window_clause += ") AS window_result"
        
        return f"SELECT {columns}, {window_clause} FROM {table}"
    
    def get_table_size_info(self, table_name: str) -> str:
        """Get table size information"""
        return f"""
        SELECT 
            pg_size_pretty(pg_total_relation_size('{table_name}')) AS total_size,
            pg_size_pretty(pg_relation_size('{table_name}')) AS table_size,
            pg_size_pretty(pg_total_relation_size('{table_name}') - pg_relation_size('{table_name}')) AS index_size
        """
    
    def get_table_stats_info(self, table_name: str) -> str:
        """Get table statistics information"""
        return f"""
        SELECT 
            schemaname,
            tablename,
            n_tup_ins AS inserts,
            n_tup_upd AS updates,
            n_tup_del AS deletes,
            n_live_tup AS live_tuples,
            n_dead_tup AS dead_tuples,
            last_vacuum,
            last_autovacuum,
            last_analyze,
            last_autoanalyze
        FROM pg_stat_user_tables 
        WHERE tablename = '{table_name}'
        """
    
    # ==================== Connection Handling ====================
    
    def build_connection_url(
        self,
        host: str,
        database: str,
        username: str,
        password: str,
        port: Optional[int] = None,
        use_async: bool = True,
        **kwargs
    ) -> str:
        """Build PostgreSQL connection URL"""
        if port is None:
            port = self.default_port
        
        driver = self.async_driver if (use_async and self.async_driver) else self.default_driver
        scheme = f"{self.name}+{driver}"
        
        url = f"{scheme}://{username}:{password}@{host}:{port}/{database}"
        
        # Add query parameters
        if kwargs:
            params = "&".join([f"{k}={v}" for k, v in kwargs.items()])
            url += f"?{params}"
        
        return url
    
    def get_recommended_engine_options(self) -> Dict[str, Any]:
        """Get recommended SQLAlchemy engine options for PostgreSQL"""
        return {
            'pool_recycle': 3600,
            'pool_pre_ping': True,
            'isolation_level': 'READ_COMMITTED',
            'connect_args': {
                'application_name': 'universal_sqlalchemy_client',
                'connect_timeout': 10
            }
        }

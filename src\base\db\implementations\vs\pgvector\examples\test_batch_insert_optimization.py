#!/usr/bin/env python3
"""
PGVector批量插入RETURNING优化测试脚本

测试目标：
1. 验证修改后的批量插入功能正常工作
2. 确认有/无RETURNING时都使用批量插入
3. 验证向量数据处理正确
4. 性能对比测试

作者: HSBC Knowledge Team
日期: 2025-01-24
"""

import asyncio
import time
import logging
from typing import List, Dict, Any
import random

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from base.db.base.vdb.core import CollectionSchema, FieldSchema, DataType
from base.db.base.schemas import VDBConnectionConfig
from base.db.implementations.vs.pgvector.client import PGVectorClient


class BatchInsertOptimizationTester:
    """批量插入优化测试器"""

    def __init__(self):
        self.config = VDBConnectionConfig(
            host="localhost",
            port=5432,
            database="test_db",
            username="postgres",
            password="password"
        )
        self.client = None
        self.test_collection_prefix = "test_batch_insert"

    def create_test_client(self) -> PGVectorClient:
        """创建测试客户端"""
        return PGVectorClient(self.config)

    def create_schema_with_auto_id(self, vector_dim: int = 128) -> CollectionSchema:
        """创建包含自增主键的测试模式"""
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="title", dtype=DataType.VARCHAR, max_length=200),
            FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=1000),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=vector_dim),
            FieldSchema(name="score", dtype=DataType.FLOAT),
        ]
        return CollectionSchema(fields=fields, description="测试集合（包含自增主键）")

    def create_schema_without_auto_id(self, vector_dim: int = 128) -> CollectionSchema:
        """创建不包含自增主键的测试模式"""
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=False),
            FieldSchema(name="title", dtype=DataType.VARCHAR, max_length=200),
            FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=1000),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=vector_dim),
            FieldSchema(name="score", dtype=DataType.FLOAT),
        ]
        return CollectionSchema(fields=fields, description="测试集合（无自增主键）")

    def generate_test_data(self, count: int, vector_dim: int = 128, with_id: bool = False) -> List[Dict[str, Any]]:
        """生成测试数据"""
        data = []
        for i in range(count):
            record = {
                "title": f"测试标题_{i}",
                "content": f"这是第{i}条测试内容，用于验证批量插入功能",
                "embedding": [random.random() for _ in range(vector_dim)],
                "score": random.uniform(0.1, 1.0)
            }
            if with_id:
                record["id"] = i + 1
            data.append(record)
        return data

    def test_sql_generation(self):
        """测试SQL生成逻辑"""
        print("\n🔍 测试SQL生成逻辑...")

        try:
            client = self.create_test_client()

            # 测试有RETURNING的SQL生成
            schema_with_auto_id = self.create_schema_with_auto_id(64)
            test_data = self.generate_test_data(3, 64, with_id=False)

            sql, values = client._build_insert_sql("test_table", test_data, schema_with_auto_id)

            print("✅ 有RETURNING的SQL:")
            print(f"   SQL: {sql}")
            print(f"   参数数量: {len(values)}")
            print(f"   预期参数数量: {len(test_data) * len(test_data[0])}")

            # 测试无RETURNING的SQL生成
            schema_without_auto_id = self.create_schema_without_auto_id(64)
            test_data_with_id = self.generate_test_data(3, 64, with_id=True)

            sql2, values2 = client._build_insert_sql("test_table", test_data_with_id, schema_without_auto_id)

            print("\n✅ 无RETURNING的SQL:")
            print(f"   SQL: {sql2}")
            print(f"   参数数量: {len(values2)}")
            print(f"   预期参数数量: {len(test_data_with_id) * len(test_data_with_id[0])}")

            # 验证SQL格式
            has_returning = 'RETURNING' in sql
            has_multiple_values = sql.count('(') > 1  # 多个VALUES子句

            print(f"\n📊 SQL验证:")
            print(f"   - 包含RETURNING: {has_returning}")
            print(f"   - 包含多个VALUES: {has_multiple_values}")
            print(f"   - 向量格式正确: {any('[' in str(v) and ']' in str(v) for v in values if isinstance(v, str))}")

            return has_returning and has_multiple_values

        except Exception as e:
            logger.error(f"SQL生成测试失败: {e}")
            return False

    def test_sync_batch_insert_with_returning(self):
        """测试同步批量插入（有RETURNING）"""
        print("\n🔍 测试同步批量插入（有RETURNING）...")

        collection_name = f"{self.test_collection_prefix}_sync_with_returning"

        try:
            client = self.create_test_client()
            client.connect()

            # 清理并创建集合
            if client.has_collection(collection_name):
                client.drop_collection(collection_name)

            schema = self.create_schema_with_auto_id(64)
            client.create_collection(collection_name, schema)

            # 生成测试数据
            test_data = self.generate_test_data(10, 64, with_id=False)

            # 执行插入
            start_time = time.time()
            result = client.insert(collection_name, test_data)
            duration = time.time() - start_time

            # 验证结果
            success = (
                result.get('insert_count') == len(test_data) and
                len(result.get('ids', [])) == len(test_data) and
                all(isinstance(id_, int) for id_ in result.get('ids', []))
            )

            print(f"✅ 同步批量插入（有RETURNING）: {'成功' if success else '失败'}")
            print(f"   - 插入记录数: {result.get('insert_count')}")
            print(f"   - 返回ID数: {len(result.get('ids', []))}")
            print(f"   - 执行时间: {duration:.3f}秒")
            print(f"   - 返回的ID: {result.get('ids', [])[:5]}...")

            # 验证数据确实插入了
            query_result = client.query(collection_name, "1=1", limit=20)
            print(f"   - 数据库中实际记录数: {len(query_result)}")

            # 清理
            client.drop_collection(collection_name)
            client.disconnect()

            return success and len(query_result) == len(test_data)

        except Exception as e:
            logger.error(f"同步批量插入（有RETURNING）测试失败: {e}")
            return False

    def test_sync_batch_insert_without_returning(self):
        """测试同步批量插入（无RETURNING）"""
        print("\n🔍 测试同步批量插入（无RETURNING）...")

        collection_name = f"{self.test_collection_prefix}_sync_without_returning"

        try:
            client = self.create_test_client()
            client.connect()

            # 清理并创建集合
            if client.has_collection(collection_name):
                client.drop_collection(collection_name)

            schema = self.create_schema_without_auto_id(64)
            client.create_collection(collection_name, schema)

            # 生成测试数据
            test_data = self.generate_test_data(10, 64, with_id=True)

            # 执行插入
            start_time = time.time()
            result = client.insert(collection_name, test_data)
            duration = time.time() - start_time

            # 验证结果
            success = (
                result.get('insert_count') == len(test_data) and
                len(result.get('ids', [])) == len(test_data)
            )

            print(f"✅ 同步批量插入（无RETURNING）: {'成功' if success else '失败'}")
            print(f"   - 插入记录数: {result.get('insert_count')}")
            print(f"   - 返回ID数: {len(result.get('ids', []))}")
            print(f"   - 执行时间: {duration:.3f}秒")

            # 验证数据确实插入了
            query_result = client.query(collection_name, "1=1", limit=20)
            print(f"   - 数据库中实际记录数: {len(query_result)}")

            # 清理
            client.drop_collection(collection_name)
            client.disconnect()

            return success and len(query_result) == len(test_data)

        except Exception as e:
            logger.error(f"同步批量插入（无RETURNING）测试失败: {e}")
            return False

    async def test_async_batch_insert_with_returning(self):
        """测试异步批量插入（有RETURNING）"""
        print("\n🔍 测试异步批量插入（有RETURNING）...")

        collection_name = f"{self.test_collection_prefix}_async_with_returning"

        try:
            client = self.create_test_client()
            await client.aconnect()

            # 清理并创建集合
            if await client.ahas_collection(collection_name):
                await client.adrop_collection(collection_name)

            schema = self.create_schema_with_auto_id(64)
            await client.acreate_collection(collection_name, schema)

            # 生成测试数据
            test_data = self.generate_test_data(10, 64, with_id=False)

            # 执行插入
            start_time = time.time()
            result = await client.ainsert(collection_name, test_data)
            duration = time.time() - start_time

            # 验证结果
            success = (
                result.get('insert_count') == len(test_data) and
                len(result.get('ids', [])) == len(test_data) and
                all(isinstance(id_, int) for id_ in result.get('ids', []))
            )

            print(f"✅ 异步批量插入（有RETURNING）: {'成功' if success else '失败'}")
            print(f"   - 插入记录数: {result.get('insert_count')}")
            print(f"   - 返回ID数: {len(result.get('ids', []))}")
            print(f"   - 执行时间: {duration:.3f}秒")
            print(f"   - 返回的ID: {result.get('ids', [])[:5]}...")

            # 验证数据确实插入了
            query_result = await client.aquery(collection_name, "1=1", limit=20)
            print(f"   - 数据库中实际记录数: {len(query_result)}")

            # 清理
            await client.adrop_collection(collection_name)
            await client.adisconnect()

            return success and len(query_result) == len(test_data)

        except Exception as e:
            logger.error(f"异步批量插入（有RETURNING）测试失败: {e}")
            return False

    async def test_async_batch_insert_without_returning(self):
        """测试异步批量插入（无RETURNING）"""
        print("\n🔍 测试异步批量插入（无RETURNING）...")

        collection_name = f"{self.test_collection_prefix}_async_without_returning"

        try:
            client = self.create_test_client()
            await client.aconnect()

            # 清理并创建集合
            if await client.ahas_collection(collection_name):
                await client.adrop_collection(collection_name)

            schema = self.create_schema_without_auto_id(64)
            await client.acreate_collection(collection_name, schema)

            # 生成测试数据
            test_data = self.generate_test_data(10, 64, with_id=True)

            # 执行插入
            start_time = time.time()
            result = await client.ainsert(collection_name, test_data)
            duration = time.time() - start_time

            # 验证结果
            success = (
                result.get('insert_count') == len(test_data) and
                len(result.get('ids', [])) == len(test_data)
            )

            print(f"✅ 异步批量插入（无RETURNING）: {'成功' if success else '失败'}")
            print(f"   - 插入记录数: {result.get('insert_count')}")
            print(f"   - 返回ID数: {len(result.get('ids', []))}")
            print(f"   - 执行时间: {duration:.3f}秒")

            # 验证数据确实插入了
            query_result = await client.aquery(collection_name, "1=1", limit=20)
            print(f"   - 数据库中实际记录数: {len(query_result)}")

            # 清理
            await client.adrop_collection(collection_name)
            await client.adisconnect()

            return success and len(query_result) == len(test_data)

        except Exception as e:
            logger.error(f"异步批量插入（无RETURNING）测试失败: {e}")
            return False

    def test_performance_comparison(self):
        """性能对比测试"""
        print("\n🚀 性能对比测试...")

        collection_name = f"{self.test_collection_prefix}_performance"
        batch_sizes = [100, 500, 1000]

        try:
            client = self.create_test_client()
            client.connect()

            for batch_size in batch_sizes:
                print(f"\n📊 测试批量大小: {batch_size}")

                # 测试有RETURNING的情况
                if client.has_collection(collection_name):
                    client.drop_collection(collection_name)

                schema = self.create_schema_with_auto_id(128)
                client.create_collection(collection_name, schema)

                test_data = self.generate_test_data(batch_size, 128, with_id=False)

                start_time = time.time()
                result = client.insert(collection_name, test_data)
                with_returning_time = time.time() - start_time

                print(f"   - 有RETURNING: {with_returning_time:.3f}秒 ({batch_size/with_returning_time:.1f} 记录/秒)")

                # 测试无RETURNING的情况
                client.drop_collection(collection_name)
                schema = self.create_schema_without_auto_id(128)
                client.create_collection(collection_name, schema)

                test_data = self.generate_test_data(batch_size, 128, with_id=True)

                start_time = time.time()
                result = client.insert(collection_name, test_data)
                without_returning_time = time.time() - start_time

                print(f"   - 无RETURNING: {without_returning_time:.3f}秒 ({batch_size/without_returning_time:.1f} 记录/秒)")

                # 性能比较
                ratio = with_returning_time / without_returning_time
                print(f"   - 性能比率: {ratio:.2f}x (理想情况下应该接近1.0)")

            client.disconnect()
            return True

        except Exception as e:
            logger.error(f"性能对比测试失败: {e}")
            return False

    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始PGVector批量插入RETURNING优化测试")
        print("=" * 60)

        # SQL生成测试
        sql_generation_test = self.test_sql_generation()

        # 同步测试
        sync_with_returning = self.test_sync_batch_insert_with_returning()
        sync_without_returning = self.test_sync_batch_insert_without_returning()

        # 异步测试
        async_with_returning = asyncio.run(self.test_async_batch_insert_with_returning())
        async_without_returning = asyncio.run(self.test_async_batch_insert_without_returning())

        # 性能测试
        performance_test = self.test_performance_comparison()

        # 汇总结果
        print("\n📋 测试结果汇总")
        print("=" * 60)
        print(f"✅ SQL生成逻辑测试: {'通过' if sql_generation_test else '失败'}")
        print(f"✅ 同步批量插入（有RETURNING）: {'通过' if sync_with_returning else '失败'}")
        print(f"✅ 同步批量插入（无RETURNING）: {'通过' if sync_without_returning else '失败'}")
        print(f"✅ 异步批量插入（有RETURNING）: {'通过' if async_with_returning else '失败'}")
        print(f"✅ 异步批量插入（无RETURNING）: {'通过' if async_without_returning else '失败'}")
        print(f"✅ 性能对比测试: {'通过' if performance_test else '失败'}")

        all_passed = all([
            sql_generation_test, sync_with_returning, sync_without_returning,
            async_with_returning, async_without_returning, performance_test
        ])

        print(f"\n🎯 总体结果: {'全部通过' if all_passed else '存在失败'}")

        if all_passed:
            print("\n🎉 优化验证成功！")
            print("   - 批量插入RETURNING功能正常工作")
            print("   - 有/无RETURNING时都使用真正的批量插入")
            print("   - 向量数据处理正确")
            print("   - 性能得到显著提升")
        else:
            print("\n❌ 存在问题，需要进一步调试")

        return all_passed


if __name__ == "__main__":
    tester = BatchInsertOptimizationTester()
    success = tester.run_all_tests()
    exit(0 if success else 1)
    
    def test_sync_batch_insert_with_returning(self):
        """测试同步批量插入（有RETURNING）"""
        print("\n🔍 测试同步批量插入（有RETURNING）...")
        
        collection_name = f"{self.test_collection_prefix}_sync_with_returning"
        
        try:
            client = self.create_test_client()
            client.connect()
            
            # 清理并创建集合
            if client.has_collection(collection_name):
                client.drop_collection(collection_name)
            
            schema = self.create_schema_with_auto_id(64)
            client.create_collection(collection_name, schema)
            
            # 生成测试数据
            test_data = self.generate_test_data(10, 64, with_id=False)  # 不包含ID，让数据库自动生成
            
            # 执行插入
            start_time = time.time()
            result = client.insert(collection_name, test_data)
            duration = time.time() - start_time
            
            # 验证结果
            success = (
                result.get('insert_count') == len(test_data) and
                len(result.get('ids', [])) == len(test_data) and
                all(isinstance(id_, int) for id_ in result.get('ids', []))
            )
            
            print(f"✅ 同步批量插入（有RETURNING）: {'成功' if success else '失败'}")
            print(f"   - 插入记录数: {result.get('insert_count')}")
            print(f"   - 返回ID数: {len(result.get('ids', []))}")
            print(f"   - 执行时间: {duration:.3f}秒")
            print(f"   - 返回的ID: {result.get('ids', [])[:5]}...")  # 显示前5个ID
            
            # 清理
            client.drop_collection(collection_name)
            client.disconnect()
            
            return success
            
        except Exception as e:
            logger.error(f"同步批量插入（有RETURNING）测试失败: {e}")
            return False
    
    def test_sync_batch_insert_without_returning(self):
        """测试同步批量插入（无RETURNING）"""
        print("\n🔍 测试同步批量插入（无RETURNING）...")
        
        collection_name = f"{self.test_collection_prefix}_sync_without_returning"
        
        try:
            client = self.create_test_client()
            client.connect()
            
            # 清理并创建集合
            if client.has_collection(collection_name):
                client.drop_collection(collection_name)
            
            schema = self.create_schema_without_auto_id(64)
            client.create_collection(collection_name, schema)
            
            # 生成测试数据
            test_data = self.generate_test_data(10, 64, with_id=True)  # 包含ID
            
            # 执行插入
            start_time = time.time()
            result = client.insert(collection_name, test_data)
            duration = time.time() - start_time
            
            # 验证结果
            success = (
                result.get('insert_count') == len(test_data) and
                len(result.get('ids', [])) == len(test_data)
            )
            
            print(f"✅ 同步批量插入（无RETURNING）: {'成功' if success else '失败'}")
            print(f"   - 插入记录数: {result.get('insert_count')}")
            print(f"   - 返回ID数: {len(result.get('ids', []))}")
            print(f"   - 执行时间: {duration:.3f}秒")
            
            # 清理
            client.drop_collection(collection_name)
            client.disconnect()
            
            return success
            
        except Exception as e:
            logger.error(f"同步批量插入（无RETURNING）测试失败: {e}")
            return False
    
    async def test_async_batch_insert_with_returning(self):
        """测试异步批量插入（有RETURNING）"""
        print("\n🔍 测试异步批量插入（有RETURNING）...")
        
        collection_name = f"{self.test_collection_prefix}_async_with_returning"
        
        try:
            client = self.create_test_client()
            await client.aconnect()
            
            # 清理并创建集合
            if await client.ahas_collection(collection_name):
                await client.adrop_collection(collection_name)
            
            schema = self.create_schema_with_auto_id(64)
            await client.acreate_collection(collection_name, schema)
            
            # 生成测试数据
            test_data = self.generate_test_data(10, 64, with_id=False)  # 不包含ID，让数据库自动生成
            
            # 执行插入
            start_time = time.time()
            result = await client.ainsert(collection_name, test_data)
            duration = time.time() - start_time
            
            # 验证结果
            success = (
                result.get('insert_count') == len(test_data) and
                len(result.get('ids', [])) == len(test_data) and
                all(isinstance(id_, int) for id_ in result.get('ids', []))
            )
            
            print(f"✅ 异步批量插入（有RETURNING）: {'成功' if success else '失败'}")
            print(f"   - 插入记录数: {result.get('insert_count')}")
            print(f"   - 返回ID数: {len(result.get('ids', []))}")
            print(f"   - 执行时间: {duration:.3f}秒")
            print(f"   - 返回的ID: {result.get('ids', [])[:5]}...")  # 显示前5个ID
            
            # 清理
            await client.adrop_collection(collection_name)
            await client.adisconnect()
            
            return success
            
        except Exception as e:
            logger.error(f"异步批量插入（有RETURNING）测试失败: {e}")
            return False
    
    async def test_async_batch_insert_without_returning(self):
        """测试异步批量插入（无RETURNING）"""
        print("\n🔍 测试异步批量插入（无RETURNING）...")
        
        collection_name = f"{self.test_collection_prefix}_async_without_returning"
        
        try:
            client = self.create_test_client()
            await client.aconnect()
            
            # 清理并创建集合
            if await client.ahas_collection(collection_name):
                await client.adrop_collection(collection_name)
            
            schema = self.create_schema_without_auto_id(64)
            await client.acreate_collection(collection_name, schema)
            
            # 生成测试数据
            test_data = self.generate_test_data(10, 64, with_id=True)  # 包含ID
            
            # 执行插入
            start_time = time.time()
            result = await client.ainsert(collection_name, test_data)
            duration = time.time() - start_time
            
            # 验证结果
            success = (
                result.get('insert_count') == len(test_data) and
                len(result.get('ids', [])) == len(test_data)
            )
            
            print(f"✅ 异步批量插入（无RETURNING）: {'成功' if success else '失败'}")
            print(f"   - 插入记录数: {result.get('insert_count')}")
            print(f"   - 返回ID数: {len(result.get('ids', []))}")
            print(f"   - 执行时间: {duration:.3f}秒")
            
            # 清理
            await client.adrop_collection(collection_name)
            await client.adisconnect()
            
            return success
            
        except Exception as e:
            logger.error(f"异步批量插入（无RETURNING）测试失败: {e}")
            return False
    
    def test_performance_comparison(self):
        """性能对比测试"""
        print("\n🚀 性能对比测试...")
        
        collection_name = f"{self.test_collection_prefix}_performance"
        batch_sizes = [100, 500, 1000]
        
        try:
            client = self.create_test_client()
            client.connect()
            
            for batch_size in batch_sizes:
                print(f"\n📊 测试批量大小: {batch_size}")
                
                # 测试有RETURNING的情况
                if client.has_collection(collection_name):
                    client.drop_collection(collection_name)
                
                schema = self.create_schema_with_auto_id(128)
                client.create_collection(collection_name, schema)
                
                test_data = self.generate_test_data(batch_size, 128, with_id=False)
                
                start_time = time.time()
                result = client.insert(collection_name, test_data)
                with_returning_time = time.time() - start_time
                
                print(f"   - 有RETURNING: {with_returning_time:.3f}秒 ({batch_size/with_returning_time:.1f} 记录/秒)")
                
                # 测试无RETURNING的情况
                client.drop_collection(collection_name)
                schema = self.create_schema_without_auto_id(128)
                client.create_collection(collection_name, schema)
                
                test_data = self.generate_test_data(batch_size, 128, with_id=True)
                
                start_time = time.time()
                result = client.insert(collection_name, test_data)
                without_returning_time = time.time() - start_time
                
                print(f"   - 无RETURNING: {without_returning_time:.3f}秒 ({batch_size/without_returning_time:.1f} 记录/秒)")
                
                # 性能比较
                ratio = with_returning_time / without_returning_time
                print(f"   - 性能比率: {ratio:.2f}x (理想情况下应该接近1.0)")
            
            client.disconnect()
            return True
            
        except Exception as e:
            logger.error(f"性能对比测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始PGVector批量插入RETURNING优化测试")
        print("=" * 60)
        
        # 同步测试
        sync_with_returning = self.test_sync_batch_insert_with_returning()
        sync_without_returning = self.test_sync_batch_insert_without_returning()
        
        # 异步测试
        async_with_returning = asyncio.run(self.test_async_batch_insert_with_returning())
        async_without_returning = asyncio.run(self.test_async_batch_insert_without_returning())
        
        # 性能测试
        performance_test = self.test_performance_comparison()
        
        # 汇总结果
        print("\n📋 测试结果汇总")
        print("=" * 60)
        print(f"✅ 同步批量插入（有RETURNING）: {'通过' if sync_with_returning else '失败'}")
        print(f"✅ 同步批量插入（无RETURNING）: {'通过' if sync_without_returning else '失败'}")
        print(f"✅ 异步批量插入（有RETURNING）: {'通过' if async_with_returning else '失败'}")
        print(f"✅ 异步批量插入（无RETURNING）: {'通过' if async_without_returning else '失败'}")
        print(f"✅ 性能对比测试: {'通过' if performance_test else '失败'}")
        
        all_passed = all([
            sync_with_returning, sync_without_returning,
            async_with_returning, async_without_returning,
            performance_test
        ])
        
        print(f"\n🎯 总体结果: {'全部通过' if all_passed else '存在失败'}")
        return all_passed


if __name__ == "__main__":
    tester = BatchInsertOptimizationTester()
    success = tester.run_all_tests()
    exit(0 if success else 1)

"""
ORM MySQL方言实现

基于Universal的MySQL方言，适配ORM的SQLAlchemy实现
"""

from typing import Dict, Any, List, Optional, Tuple
from .base import DatabaseDialect, DatabaseFeatures, FeatureSupport


class MySQLDialect(DatabaseDialect):
    """MySQL database dialect implementation for ORM"""
    
    @property
    def name(self) -> str:
        return "mysql"
    
    @property
    def description(self) -> str:
        return "MySQL Database (5.6+)"
    
    @property
    def default_port(self) -> Optional[int]:
        return 3306
    
    @property
    def default_driver(self) -> str:
        return "pymysql"
    
    @property
    def async_driver(self) -> Optional[str]:
        return "aiomysql"
    
    @property
    def features(self) -> DatabaseFeatures:
        return DatabaseFeatures(
            json_support=FeatureSupport.FULL,      # MySQL 5.7+
            array_support=FeatureSupport.NONE,     # No native array support
            window_functions=FeatureSupport.FULL,  # MySQL 8.0+
            cte_support=FeatureSupport.FULL,       # MySQL 8.0+
            full_text_search=FeatureSupport.FULL,  # Native FULLTEXT
            partitioning=FeatureSupport.FULL,      # Native partitioning
            upsert_support=FeatureSupport.FULL,    # ON DUPLICATE KEY UPDATE
            returning_clause=FeatureSupport.NONE,  # No RETURNING clause
            bulk_insert=FeatureSupport.FULL,       # INSERT IGNORE, bulk inserts
            async_support=FeatureSupport.FULL      # aiomysql
        )
    
    def quote_identifier(self, identifier: str) -> str:
        """Quote MySQL identifier with backticks"""
        return f"`{identifier.replace('`', '``')}`"
    
    def escape_string(self, value: str) -> str:
        """Escape string value for MySQL"""
        # Basic escaping - in practice, use parameterized queries
        return value.replace("'", "''").replace("\\", "\\\\")
    
    def format_limit_offset(self, limit: Optional[int], offset: Optional[int]) -> str:
        """Format MySQL LIMIT/OFFSET clause"""
        if limit is None:
            return ""
        
        if offset is not None:
            return f"LIMIT {offset}, {limit}"
        else:
            return f"LIMIT {limit}"
    
    def get_table_exists_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to check if table exists in MySQL"""
        if schema:
            return f"""
            SELECT COUNT(*) as count 
            FROM information_schema.tables 
            WHERE table_schema = '{schema}' 
            AND table_name = '{table_name}'
            """
        else:
            return f"""
            SELECT COUNT(*) as count 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = '{table_name}'
            """
    
    def get_column_info_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to retrieve column information"""
        if schema:
            return f"""
            SELECT 
                column_name,
                data_type,
                is_nullable,
                column_default,
                character_maximum_length,
                numeric_precision,
                numeric_scale,
                column_comment
            FROM information_schema.columns 
            WHERE table_schema = '{schema}' 
            AND table_name = '{table_name}'
            ORDER BY ordinal_position
            """
        else:
            return f"""
            SELECT 
                column_name,
                data_type,
                is_nullable,
                column_default,
                character_maximum_length,
                numeric_precision,
                numeric_scale,
                column_comment
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = '{table_name}'
            ORDER BY ordinal_position
            """
    
    def get_primary_key_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to retrieve primary key information"""
        if schema:
            return f"""
            SELECT column_name
            FROM information_schema.key_column_usage
            WHERE table_schema = '{schema}'
            AND table_name = '{table_name}'
            AND constraint_name = 'PRIMARY'
            ORDER BY ordinal_position
            """
        else:
            return f"""
            SELECT column_name
            FROM information_schema.key_column_usage
            WHERE table_schema = DATABASE()
            AND table_name = '{table_name}'
            AND constraint_name = 'PRIMARY'
            ORDER BY ordinal_position
            """
    
    def get_foreign_key_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to retrieve foreign key information"""
        if schema:
            return f"""
            SELECT 
                column_name,
                referenced_table_name,
                referenced_column_name,
                constraint_name
            FROM information_schema.key_column_usage
            WHERE table_schema = '{schema}'
            AND table_name = '{table_name}'
            AND referenced_table_name IS NOT NULL
            """
        else:
            return f"""
            SELECT 
                column_name,
                referenced_table_name,
                referenced_column_name,
                constraint_name
            FROM information_schema.key_column_usage
            WHERE table_schema = DATABASE()
            AND table_name = '{table_name}'
            AND referenced_table_name IS NOT NULL
            """
    
    def get_index_info_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to retrieve index information"""
        if schema:
            return f"""
            SELECT 
                index_name,
                column_name,
                non_unique,
                seq_in_index
            FROM information_schema.statistics
            WHERE table_schema = '{schema}'
            AND table_name = '{table_name}'
            ORDER BY index_name, seq_in_index
            """
        else:
            return f"""
            SELECT 
                index_name,
                column_name,
                non_unique,
                seq_in_index
            FROM information_schema.statistics
            WHERE table_schema = DATABASE()
            AND table_name = '{table_name}'
            ORDER BY index_name, seq_in_index
            """
    
    def get_schema_query(self, schema: Optional[str] = None) -> str:
        """Get query to list all tables in schema"""
        if schema:
            return f"""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = '{schema}'
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
            """
        else:
            return """
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
            """
    
    def get_upsert_query(self, table: str, columns: List[str], 
                        conflict_columns: List[str], 
                        update_columns: Optional[List[str]] = None) -> str:
        """Generate MySQL UPSERT query using ON DUPLICATE KEY UPDATE"""
        if update_columns is None:
            update_columns = [col for col in columns if col not in conflict_columns]
        
        quoted_table = self.quote_identifier(table)
        quoted_columns = [self.quote_identifier(col) for col in columns]
        placeholders = [self.format_placeholder(col) for col in columns]
        
        # Build the base INSERT
        insert_clause = f"INSERT INTO {quoted_table} ({', '.join(quoted_columns)}) VALUES ({', '.join(placeholders)})"
        
        # Build the ON DUPLICATE KEY UPDATE clause
        if update_columns:
            update_pairs = [f"{self.quote_identifier(col)} = VALUES({self.quote_identifier(col)})" 
                          for col in update_columns]
            update_clause = f"ON DUPLICATE KEY UPDATE {', '.join(update_pairs)}"
            return f"{insert_clause} {update_clause}"
        else:
            return insert_clause
    
    def get_version_query(self) -> str:
        """Get query to retrieve MySQL version"""
        return "SELECT VERSION() as version"
    
    def format_datetime(self, dt_format: str = "YYYY-MM-DD HH:MI:SS") -> str:
        """Get datetime format string for MySQL"""
        return "%Y-%m-%d %H:%i:%s"
    
    def get_connection_params(self) -> Dict[str, Any]:
        """Get MySQL-specific connection parameters"""
        return {
            'charset': 'utf8mb4',
            'use_unicode': True,
            'autocommit': False
        }

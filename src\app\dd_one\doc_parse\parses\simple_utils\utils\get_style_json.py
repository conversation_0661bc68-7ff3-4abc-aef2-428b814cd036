from openpyxl import load_workbook
import json


def get_color_hex(color, is_font=True):
    """
    将 openpyxl 的颜色对象转为标准 aRGB hex 字符串
    支持 RGB、主题色、sRGB 表达式等格式

    参数:
        color: openpyxl 的 Color 或 RGB 类型对象
        is_font: 是否是字体颜色（True）还是背景颜色（False）
    """
    if color is None:
        return "FF000000" if is_font else "FFFFFFFF"  # 字体默认黑，背景默认白

    try:
        if hasattr(color, 'rgb'):
            rgb_value = color.rgb
            if isinstance(rgb_value, str):
                return rgb_value.upper()
            elif hasattr(rgb_value, 'rgb'):
                return rgb_value.rgb.upper()
    except Exception as e:
        pass

    return "FF000000" if is_font else "FFFFFFFF"


# 加载Excel
wb = load_workbook(r"C:\Users\<USER>\Desktop\数据\汇丰测试数据\DD文档 - 样例.xlsx")
ws = wb.active

data_with_style = {
    "rows": [],
    "column_widths": {},
    "row_heights": {},
    "merged_cells": []
}

# 读取合并单元格范围
for merged_range in ws.merged_cells.ranges:
    data_with_style["merged_cells"].append(str(merged_range))

# 合法对齐方式白名单
VALID_HORIZONTAL = {'left', 'right', 'distributed', 'justify', 'centerContinuous', 'center', 'general', 'fill'}
VALID_VERTICAL = {'top', 'bottom', 'center', 'justify', 'distributed'}


def sanitize_alignment(value, valid_values, default='left'):
    if value in valid_values:
        return value
    return default


# 读取前五行数据
for row in ws.iter_rows(max_row=4):
    row_data = []
    for cell in row:
        fill_info = cell.fill
        print(fill_info.fgColor)
        print("这是字体的颜色",cell.font.color)
        bg_color = get_color_hex(fill_info.fgColor, is_font=False)  # 背景颜色
        font_color = get_color_hex(cell.font.color, is_font=True)  # 字体颜色

        h_align = sanitize_alignment(cell.alignment.horizontal, VALID_HORIZONTAL, 'left')
        v_align = sanitize_alignment(cell.alignment.vertical, VALID_VERTICAL, 'top')

        row_data.append({
            "value": cell.value,
            "font": {
                "name": cell.font.name,
                "size": cell.font.size,
                "bold": cell.font.bold,
                "italic": cell.font.italic,
                "color": font_color
            },
            "fill": {
                "bgColor": bg_color
            },
            "alignment": {
                "horizontal": h_align,
                "vertical": v_align,
                "wrap_text": cell.alignment.wrap_text
            }
        })
    data_with_style["rows"].append(row_data)

# 列宽
for col_letter, dim in ws.column_dimensions.items():
    data_with_style["column_widths"][col_letter] = dim.width

# 行高
for row_number in range(1, 6):
    if row_number in ws.row_dimensions:
        data_with_style["row_heights"][str(row_number)] = ws.row_dimensions[row_number].height

# 保存JSON
with open("excel_data_with_style.json", "w", encoding="utf-8") as f:
    json.dump(data_with_style, f, indent=4, ensure_ascii=False)

print("✅ Excel 数据已成功保存为 JSON 文件。")

from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill
import json

# 读取JSON
with open("excel_data_with_style.json", "r", encoding="utf-8") as f:
    data = json.load(f)

# 创建新工作簿
new_wb = Workbook()
new_ws = new_wb.active

# 恢复数据
for row_idx, row_data in enumerate(data["rows"], start=1):
    for col_idx, cell_data in enumerate(row_data, start=1):
        cell = new_ws.cell(row=row_idx, column=col_idx, value=cell_data["value"])

        # 设置字体
        font_info = cell_data["font"]
        cell.font = Font(
            name=font_info.get("name", "Calibri"),
            size=int(font_info.get("size", 11)),
            bold=font_info.get("bold", False),
            italic=font_info.get("italic", False),
            color=font_info.get("color", "FF000000")
        )

        # 设置背景色
        fill_info = cell_data["fill"]
        bg_color = fill_info.get("bgColor", "FFFFFFFF")
        cell.fill = PatternFill(patternType="solid", fgColor=bg_color)

        # 设置对齐
        align_info = cell_data["alignment"]
        h_align = align_info.get("horizontal", "left")
        v_align = align_info.get("vertical", "top")
        wrap_text = align_info.get("wrap_text", False)
        cell.alignment = Alignment(horizontal=h_align, vertical=v_align, wrap_text=wrap_text)

# 设置列宽
for col_letter, width in data["column_widths"].items():
    new_ws.column_dimensions[col_letter].width = float(width)

# 设置行高
for row_number, height in data["row_heights"].items():
    new_ws.row_dimensions[int(row_number)].height = float(height)

# 恢复合并单元格
for merged_range in data["merged_cells"]:
    new_ws.merge_cells(merged_range)

# 保存新文件
new_wb.save("restored_excel_file.xlsx")

print("✅ 新的 Excel 文件已成功生成，包含原始样式和合并单元格。")

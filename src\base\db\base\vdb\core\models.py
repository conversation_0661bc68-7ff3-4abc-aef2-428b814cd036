"""
VDB核心数据模型

定义统一的输入输出数据模型，确保不同向量数据库实现的一致性
参考Milvus API设计，结合RDB的优秀架构模式

设计原则：
1. Milvus兼容 - API和数据格式与Milvus保持一致
2. 类型安全 - 完整的类型注解和验证
3. 适配器友好 - 支持不同数据库的格式转换
4. 扩展性 - 支持数据库特定功能
5. 验证完整 - 内置参数验证和错误处理
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union, Set
from datetime import datetime
import logging

from .types import (
    VectorDBValue, VectorDBRecord, VectorDBRecords, Vector, VectorArray,
    FieldType, MetricType, IndexType, SearchType, ComparisonOperator,
    LogicalOperator, SortOrder, RankingMethod, VectorDBType
)

logger = logging.getLogger(__name__)


# ==================== 字段和模式定义 ====================

@dataclass
class FieldSchema:
    """字段模式定义（Milvus兼容）"""
    name: str
    dtype: FieldType
    description: str = ""
    is_primary: bool = False
    auto_id: bool = False
    max_length: Optional[int] = None  # VARCHAR字段的最大长度
    dim: Optional[int] = None         # 向量字段的维度
    
    def __post_init__(self):
        """验证字段定义"""
        if not self.name:
            raise ValueError("字段名称不能为空")
        
        # 验证向量字段必须有维度
        if self.dtype in [FieldType.FLOAT_VECTOR, FieldType.BINARY_VECTOR, FieldType.SPARSE_FLOAT_VECTOR]:
            if not self.dim or self.dim <= 0:
                raise ValueError(f"向量字段 {self.name} 必须指定有效的维度")
        
        # 验证VARCHAR字段的最大长度
        if self.dtype == FieldType.VARCHAR:
            if not self.max_length or self.max_length <= 0:
                self.max_length = 65535  # 默认最大长度
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "name": self.name,
            "dtype": self.dtype.value,
            "description": self.description,
            "is_primary": self.is_primary,
            "auto_id": self.auto_id
        }
        if self.max_length is not None:
            result["max_length"] = self.max_length
        if self.dim is not None:
            result["dim"] = self.dim
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FieldSchema':
        """从字典创建字段模式"""
        dtype = FieldType(data["dtype"]) if isinstance(data["dtype"], str) else data["dtype"]
        return cls(
            name=data["name"],
            dtype=dtype,
            description=data.get("description", ""),
            is_primary=data.get("is_primary", False),
            auto_id=data.get("auto_id", False),
            max_length=data.get("max_length"),
            dim=data.get("dim")
        )


@dataclass
class CollectionSchema:
    """集合模式定义（Milvus兼容）"""
    fields: List[FieldSchema]
    description: str = ""
    enable_dynamic_field: bool = True  # 是否启用动态字段
    
    def __post_init__(self):
        """验证模式定义"""
        if not self.fields:
            raise ValueError("集合必须至少包含一个字段")
        
        # 验证字段名称唯一性
        field_names = [field.name for field in self.fields]
        if len(field_names) != len(set(field_names)):
            raise ValueError("字段名称必须唯一")
        
        # 验证主键字段
        primary_fields = [field for field in self.fields if field.is_primary]
        if len(primary_fields) < 1:
            raise ValueError("必须至少有一个主键字段")
        
        # 验证至少有一个向量字段
        vector_fields = self.get_vector_fields()
        if not vector_fields:
            raise ValueError("集合必须至少包含一个向量字段")
    
    def get_primary_field(self) -> FieldSchema:
        """获取主键字段（返回第一个主键字段，用于向后兼容）"""
        for field in self.fields:
            if field.is_primary:
                return field
        raise ValueError("未找到主键字段")

    def get_primary_fields(self) -> List[FieldSchema]:
        """获取所有主键字段（支持复合主键）"""
        primary_fields = [field for field in self.fields if field.is_primary]
        if not primary_fields:
            raise ValueError("未找到主键字段")
        return primary_fields

    def get_vector_fields(self) -> List[FieldSchema]:
        """获取所有向量字段"""
        return [field for field in self.fields 
                if field.dtype in [FieldType.FLOAT_VECTOR, FieldType.BINARY_VECTOR, FieldType.SPARSE_FLOAT_VECTOR]]
    
    def get_scalar_fields(self) -> List[FieldSchema]:
        """获取所有标量字段"""
        return [field for field in self.fields 
                if field.dtype not in [FieldType.FLOAT_VECTOR, FieldType.BINARY_VECTOR, FieldType.SPARSE_FLOAT_VECTOR]]
    
    def get_field_by_name(self, name: str) -> Optional[FieldSchema]:
        """根据名称获取字段"""
        for field in self.fields:
            if field.name == name:
                return field
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "fields": [field.to_dict() for field in self.fields],
            "description": self.description,
            "enable_dynamic_field": self.enable_dynamic_field
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CollectionSchema':
        """从字典创建集合模式"""
        fields = [FieldSchema.from_dict(field_data) for field_data in data["fields"]]
        return cls(
            fields=fields,
            description=data.get("description", ""),
            enable_dynamic_field=data.get("enable_dynamic_field", True)
        )


# ==================== 实体和结果定义 ====================

@dataclass
class Entity:
    """实体定义（Milvus兼容）"""
    data: Dict[str, Any] = field(default_factory=dict)
    
    def __getitem__(self, key: str) -> Any:
        """支持字典式访问"""
        return self.data[key]
    
    def __setitem__(self, key: str, value: Any) -> None:
        """支持字典式设置"""
        self.data[key] = value
    
    def __contains__(self, key: str) -> bool:
        """支持in操作符"""
        return key in self.data
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取字段值"""
        return self.data.get(key, default)
    
    def keys(self):
        """获取所有键"""
        return self.data.keys()
    
    def values(self):
        """获取所有值"""
        return self.data.values()
    
    def items(self):
        """获取所有键值对"""
        return self.data.items()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self.data.copy()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Entity':
        """从字典创建实体"""
        return cls(data=data.copy())


@dataclass
class SearchResult:
    """搜索结果（Milvus兼容格式）"""
    id: Any                           # 主键ID
    distance: float                   # 距离分数（越小越相似）
    entity: Entity                    # 实体数据
    
    def __post_init__(self):
        """验证搜索结果"""
        if self.distance < 0:
            logger.warning(f"距离分数为负数: {self.distance}")
        
        if not isinstance(self.entity, Entity):
            # 自动转换为Entity
            if isinstance(self.entity, dict):
                self.entity = Entity(data=self.entity)
            else:
                raise ValueError("entity必须是Entity类型或字典")
    
    @property
    def score(self) -> float:
        """获取相似度分数（余弦相似度：1-distance，限制在0-1范围）"""
        # 对于余弦距离，相似度 = 1 - 距离，并确保在0-1范围内
        cosine_similarity = 1.0 - self.distance
        return max(0.0, min(1.0, cosine_similarity))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "distance": self.distance,
            "score": self.score,
            "entity": self.entity.to_dict()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchResult':
        """从字典创建搜索结果"""
        entity_data = data.get("entity", {})
        entity = Entity.from_dict(entity_data) if isinstance(entity_data, dict) else entity_data
        
        return cls(
            id=data["id"],
            distance=data["distance"],
            entity=entity
        )


# ==================== 请求模型定义 ====================

@dataclass
class SearchRequest:
    """向量搜索请求（Milvus兼容）"""
    data: List[List[float]]           # 查询向量列表
    anns_field: str                   # 向量字段名
    param: Dict[str, Any]             # 搜索参数
    limit: int = 10                   # 返回结果数量
    expr: Optional[str] = None        # 过滤表达式
    
    def __post_init__(self):
        """验证搜索请求"""
        if not self.data:
            raise ValueError("查询向量列表不能为空")
        
        if not self.anns_field:
            raise ValueError("向量字段名不能为空")
        
        if self.limit <= 0:
            raise ValueError("返回结果数量必须大于0")
        
        # 验证向量维度一致性
        if len(self.data) > 1:
            first_dim = len(self.data[0])
            for i, vector in enumerate(self.data[1:], 1):
                if len(vector) != first_dim:
                    raise ValueError(f"向量 {i} 的维度 {len(vector)} 与第一个向量的维度 {first_dim} 不一致")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "data": self.data,
            "anns_field": self.anns_field,
            "param": self.param,
            "limit": self.limit
        }
        if self.expr:
            result["expr"] = self.expr
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchRequest':
        """从字典创建搜索请求"""
        return cls(
            data=data["data"],
            anns_field=data["anns_field"],
            param=data["param"],
            limit=data.get("limit", 10),
            expr=data.get("expr")
        )


# 为了向后兼容，保留原有的别名
AnnSearchRequest = SearchRequest


@dataclass
class QueryRequest:
    """标量查询请求（Milvus兼容）"""
    expr: str                         # 查询表达式
    output_fields: Optional[List[str]] = None  # 输出字段
    limit: Optional[int] = None       # 结果数量限制
    offset: Optional[int] = None      # 偏移量

    def __post_init__(self):
        """验证查询请求"""
        if not self.expr:
            raise ValueError("查询表达式不能为空")

        if self.limit is not None and self.limit <= 0:
            raise ValueError("结果数量限制必须大于0")

        if self.offset is not None and self.offset < 0:
            raise ValueError("偏移量不能为负数")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {"expr": self.expr}
        if self.output_fields:
            result["output_fields"] = self.output_fields
        if self.limit is not None:
            result["limit"] = self.limit
        if self.offset is not None:
            result["offset"] = self.offset
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QueryRequest':
        """从字典创建查询请求"""
        return cls(
            expr=data["expr"],
            output_fields=data.get("output_fields"),
            limit=data.get("limit"),
            offset=data.get("offset")
        )


@dataclass
class GetRequest:
    """主键查询请求（Milvus兼容）"""
    ids: List[Any]                    # 主键ID列表
    output_fields: Optional[List[str]] = None  # 输出字段

    def __post_init__(self):
        """验证获取请求"""
        if not self.ids:
            raise ValueError("主键ID列表不能为空")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {"ids": self.ids}
        if self.output_fields:
            result["output_fields"] = self.output_fields
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GetRequest':
        """从字典创建获取请求"""
        return cls(
            ids=data["ids"],
            output_fields=data.get("output_fields")
        )


@dataclass
class InsertRequest:
    """插入请求"""
    data: List[Dict[str, Any]]        # 要插入的数据
    partition_name: Optional[str] = None  # 分区名称

    def __post_init__(self):
        """验证插入请求"""
        if not self.data:
            raise ValueError("插入数据不能为空")

        # 验证数据格式一致性
        if len(self.data) > 1:
            first_keys = set(self.data[0].keys())
            for i, record in enumerate(self.data[1:], 1):
                record_keys = set(record.keys())
                if record_keys != first_keys:
                    logger.warning(f"记录 {i} 的字段与第一条记录不一致")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {"data": self.data}
        if self.partition_name:
            result["partition_name"] = self.partition_name
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'InsertRequest':
        """从字典创建插入请求"""
        return cls(
            data=data["data"],
            partition_name=data.get("partition_name")
        )


@dataclass
class DeleteRequest:
    """删除请求"""
    expr: str                         # 删除条件表达式
    partition_name: Optional[str] = None  # 分区名称

    def __post_init__(self):
        """验证删除请求"""
        if not self.expr:
            raise ValueError("删除条件表达式不能为空")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {"expr": self.expr}
        if self.partition_name:
            result["partition_name"] = self.partition_name
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DeleteRequest':
        """从字典创建删除请求"""
        return cls(
            expr=data["expr"],
            partition_name=data.get("partition_name")
        )


@dataclass
class HybridSearchRequest:
    """混合搜索请求"""
    reqs: List[SearchRequest]         # 搜索请求列表
    ranker: Any                       # 排序器实例
    limit: int = 10                   # 最终返回结果数量

    def __post_init__(self):
        """验证混合搜索请求"""
        if not self.reqs:
            raise ValueError("搜索请求列表不能为空")

        if self.limit <= 0:
            raise ValueError("返回结果数量必须大于0")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "reqs": [req.to_dict() for req in self.reqs],
            "ranker": str(self.ranker),
            "limit": self.limit
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any], ranker: Any) -> 'HybridSearchRequest':
        """从字典创建混合搜索请求"""
        reqs = [SearchRequest.from_dict(req_data) for req_data in data["reqs"]]
        return cls(
            reqs=reqs,
            ranker=ranker,
            limit=data.get("limit", 10)
        )


# ==================== 配置模型 ====================

@dataclass
class ConnectionConfig:
    """连接配置基类"""
    host: str
    port: int
    database: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    extra_config: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """验证连接配置"""
        if not self.host:
            raise ValueError("主机地址不能为空")

        if not isinstance(self.port, int) or self.port <= 0:
            raise ValueError("端口必须是正整数")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "host": self.host,
            "port": self.port
        }
        if self.database:
            result["database"] = self.database
        if self.username:
            result["username"] = self.username
        if self.password:
            result["password"] = self.password
        result.update(self.extra_config)
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConnectionConfig':
        """从字典创建连接配置"""
        # 提取已知字段
        known_fields = {"host", "port", "database", "username", "password"}
        extra_config = {k: v for k, v in data.items() if k not in known_fields}

        return cls(
            host=data["host"],
            port=data["port"],
            database=data.get("database"),
            username=data.get("username"),
            password=data.get("password"),
            extra_config=extra_config
        )


# ==================== 便捷构造函数 ====================

def create_field_schema(name: str, dtype: str, **kwargs) -> FieldSchema:
    """便捷创建字段模式"""
    field_type = FieldType(dtype.upper())
    return FieldSchema(name=name, dtype=field_type, **kwargs)


def create_collection_schema(fields: List[Dict[str, Any]], **kwargs) -> CollectionSchema:
    """便捷创建集合模式"""
    field_schemas = []
    for field_def in fields:
        field_schemas.append(create_field_schema(**field_def))
    return CollectionSchema(fields=field_schemas, **kwargs)


def create_search_request(data: List[List[float]], anns_field: str,
                         metric_type: str = "COSINE", **kwargs) -> SearchRequest:
    """便捷创建搜索请求"""
    param = {"metric_type": metric_type}
    param.update(kwargs.get("param", {}))

    return SearchRequest(
        data=data,
        anns_field=anns_field,
        param=param,
        limit=kwargs.get("limit", 10),
        expr=kwargs.get("expr")
    )


def create_simple_schema(collection_name: str, vector_dim: int,
                        additional_fields: Optional[List[Dict[str, Any]]] = None) -> CollectionSchema:
    """创建简单的集合模式"""
    fields = [
        {"name": "id", "dtype": "INT64", "is_primary": True, "auto_id": True},
        {"name": "embedding", "dtype": "FLOAT_VECTOR", "dim": vector_dim},
        {"name": "content", "dtype": "VARCHAR", "max_length": 65535}
    ]

    if additional_fields:
        fields.extend(additional_fields)

    return create_collection_schema(fields, description=f"简单集合模式: {collection_name}")


def create_simple_search_request(query_vector: List[float],
                                anns_field: str = "embedding",
                                limit: int = 10,
                                filters: Optional[str] = None) -> SearchRequest:
    """创建简单的搜索请求"""
    return create_search_request(
        data=[query_vector],
        anns_field=anns_field,
        limit=limit,
        expr=filters
    )

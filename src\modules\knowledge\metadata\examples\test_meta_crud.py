"""
元数据系统完整CRUD测试 (crud_meta)

严格按照实际表结构进行测试：
- md_source_database: 源数据库表
- md_source_table: 源表表
- md_source_columns: 源字段表

测试内容：
- 完整的CRUD操作（创建、读取、更新、删除）
- 依赖关系处理（数据库 → 表 → 字段）
- 级联删除处理
- 数据验证测试
- 错误处理测试
- 批量操作测试
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局测试数据存储
test_data_store = {
    'knowledge_id': None,
    'db_id': None,
    'table_id': None,
    'column_ids': []
}


async def setup_test_environment():
    """设置测试环境，创建测试知识库"""
    print("🔧 设置测试环境")
    print("-" * 40)

    try:
        # 获取客户端
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
        from service import get_client
        from modules.knowledge.knowledge.crud import KnowledgeCrud

        rdb_client = await get_client("database.rdbs.mysql")
        knowledge_crud = KnowledgeCrud(rdb_client)

        # 创建测试知识库
        timestamp = int(time.time())
        test_kb_data = {
            'knowledge_name': f'元数据测试知识库_{timestamp}',
            'knowledge_type': 'MetaData',
            'knowledge_desc': '元数据CRUD测试知识库',
            'models': {
                'embedding': 'moka-m3e-base'
            }
        }

        knowledge_id = await knowledge_crud.create_knowledge_base(test_kb_data)
        if not knowledge_id:
            raise Exception("创建测试知识库失败")

        test_data_store['knowledge_id'] = knowledge_id
        print(f"   ✅ 创建测试知识库: {knowledge_id}")

        return rdb_client, knowledge_id

    except Exception as e:
        logger.error(f"设置测试环境失败: {e}")
        raise


async def cleanup_test_environment():
    """清理测试环境"""
    print("\n🧹 清理测试环境")
    print("-" * 40)

    try:
        if test_data_store['knowledge_id']:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            from modules.knowledge.knowledge.crud import KnowledgeCrud

            rdb_client = await get_client("database.rdbs.mysql")
            knowledge_crud = KnowledgeCrud(rdb_client)

            # 删除测试知识库（会级联删除所有相关数据）
            await knowledge_crud.delete_knowledge_base(test_data_store['knowledge_id'])
            print(f"   ✅ 删除测试知识库: {test_data_store['knowledge_id']}")

    except Exception as e:
        logger.error(f"清理测试环境失败: {e}")


async def test_source_database_crud(rdb_client, knowledge_id: str):
    """测试源数据库的完整CRUD操作"""
    print("\n1️⃣ 测试源数据库CRUD操作:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta
        
        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的
        
        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)

        # 1. 创建源数据库
        timestamp = int(time.time())
        test_db_data = {
            'knowledge_id': knowledge_id,
            'db_name': f'test_meta_db_{timestamp}',
            'db_name_cn': f'元数据测试数据库_{timestamp}',
            'data_layer': 'ods',
            'db_desc': '元数据测试数据库',
            'is_active': True
        }

        db_id, vector_results = await meta_crud.create_source_database(test_db_data)
        if not db_id or db_id <= 0:
            raise Exception("创建源数据库失败：返回的ID无效")
        
        test_data_store['db_id'] = db_id
        print(f"   ✅ 创建源数据库: {db_id}")
        print(f"   📊 向量化结果: {len(vector_results)} 个向量")

        # 2. 获取源数据库（主键查询）
        database = await meta_crud.get_source_database(db_id)
        if not database or not database.get('db_name'):
            raise Exception("主键查询源数据库失败：未返回有效数据")
        print(f"   ✅ 主键获取源数据库: {database['db_name']}")

        # 3. 获取源数据库（条件查询）
        database_by_name = await meta_crud.get_source_database(
            knowledge_id=knowledge_id,
            db_name=test_db_data['db_name']
        )
        if not database_by_name or not database_by_name.get('db_id'):
            raise Exception("条件查询源数据库失败：未返回有效数据")
        print(f"   ✅ 条件查询源数据库: {database_by_name['db_id']}")

        # 4. 更新源数据库
        update_success = await meta_crud.update_source_database(
            {'db_desc': '更新后的数据库描述'},
            db_id=db_id
        )
        if not update_success:
            raise Exception("更新源数据库失败：返回False")
        print(f"   ✅ 更新源数据库: {update_success}")

        # 5. 验证更新
        updated_database = await meta_crud.get_source_database(db_id)
        if not updated_database or '更新后的数据库描述' not in updated_database.get('db_desc', ''):
            raise Exception("验证更新失败：描述未正确更新")
        print(f"   ✅ 验证更新: {updated_database['db_desc']}")

        # 6. 列出源数据库
        databases_list = await meta_crud.list_source_databases(knowledge_id=knowledge_id)
        if not databases_list or len(databases_list) == 0:
            raise Exception("列出源数据库失败：未返回数据")
        print(f"   ✅ 列出源数据库: {len(databases_list)} 个")

        return True

    except Exception as e:
        print(f"   ❌ 源数据库CRUD测试失败: {e}")
        return False


async def test_source_table_crud(rdb_client, knowledge_id: str, db_id: int):
    """测试源表的完整CRUD操作"""
    print("\n2️⃣ 测试源表CRUD操作:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta
        
        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的
        
        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)

        # 1. 创建源表
        timestamp = int(time.time())
        test_table_data = {
            'knowledge_id': knowledge_id,
            'db_id': db_id,
            'table_name': f'test_meta_table_{timestamp}',
            'table_name_cn': f'元数据测试表_{timestamp}',
            'table_desc': '元数据测试表',
            'is_active': True
        }

        table_id, vector_results = await meta_crud.create_source_table(test_table_data)
        if not table_id or table_id <= 0:
            raise Exception("创建源表失败：返回的ID无效")
        
        test_data_store['table_id'] = table_id
        print(f"   ✅ 创建源表: {table_id}")
        print(f"   📊 向量化结果: {len(vector_results)} 个向量")

        # 2. 获取源表（主键查询）
        table = await meta_crud.get_source_table(table_id)
        if not table or not table.get('table_name'):
            raise Exception("主键查询源表失败：未返回有效数据")
        print(f"   ✅ 主键获取源表: {table['table_name']}")

        # 3. 更新源表
        update_success = await meta_crud.update_source_table(
            {'table_desc': '更新后的表描述'},
            table_id=table_id
        )
        if not update_success:
            raise Exception("更新源表失败：返回False")
        print(f"   ✅ 更新源表: {update_success}")

        # 4. 列出源表
        tables_list = await meta_crud.list_source_tables(db_id=db_id)
        if not tables_list or len(tables_list) == 0:
            raise Exception("列出源表失败：未返回数据")
        print(f"   ✅ 列出源表: {len(tables_list)} 个")

        return True

    except Exception as e:
        print(f"   ❌ 源表CRUD测试失败: {e}")
        return False


async def test_source_column_crud(rdb_client, knowledge_id: str, table_id: int):
    """测试源字段的完整CRUD操作"""
    print("\n3️⃣ 测试源字段CRUD操作:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta
        
        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的
        
        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)

        # 1. 创建源字段（使用正确的字段结构）
        timestamp = int(time.time())
        test_column_data = {
            'knowledge_id': knowledge_id,
            'table_id': table_id,
            'column_name': f'test_meta_column_{timestamp}',
            'column_name_cn': f'元数据测试字段_{timestamp}',
            'column_desc': '元数据测试字段',
            'data_type': 'VARCHAR',
            'data_example': '示例数据',
            'is_primary_key': False,
            'is_sensitive': False
        }

        column_id, vector_results = await meta_crud.create_source_column(test_column_data)
        if not column_id or column_id <= 0:
            raise Exception("创建源字段失败：返回的ID无效")
        
        test_data_store['column_ids'].append(column_id)
        print(f"   ✅ 创建源字段: {column_id}")
        print(f"   📊 向量化结果: {len(vector_results)} 个向量")

        # 2. 获取源字段（主键查询）
        column = await meta_crud.get_source_column(column_id)
        if not column or not column.get('column_name'):
            raise Exception("主键查询源字段失败：未返回有效数据")
        print(f"   ✅ 主键获取源字段: {column['column_name']}")

        # 3. 更新源字段
        update_success = await meta_crud.update_source_column(
            {'column_desc': '更新后的字段描述'},
            column_id=column_id
        )
        if not update_success:
            raise Exception("更新源字段失败：返回False")
        print(f"   ✅ 更新源字段: {update_success}")

        # 4. 列出源字段
        columns_list = await meta_crud.list_source_columns(table_id=table_id)
        if not columns_list or len(columns_list) == 0:
            raise Exception("列出源字段失败：未返回数据")
        print(f"   ✅ 列出源字段: {len(columns_list)} 个")

        return True

    except Exception as e:
        print(f"   ❌ 源字段CRUD测试失败: {e}")
        return False


async def test_search_functionality(rdb_client, knowledge_id: str):
    """测试搜索功能"""
    print("\n4️⃣ 测试搜索功能:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client

            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的

        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)

        # 搜索源数据库
        search_results = await meta_crud.search_source_databases(
            search_term="test",
            knowledge_id=knowledge_id,
            limit=10
        )
        print(f"   ✅ 搜索源数据库: 找到 {len(search_results)} 个结果")

        # 搜索源表
        if test_data_store['db_id']:
            table_results = await meta_crud.search_source_tables(
                search_term="test",
                knowledge_id=knowledge_id,
                db_id=test_data_store['db_id'],
                limit=10
            )
            print(f"   ✅ 搜索源表: 找到 {len(table_results)} 个结果")

        # 搜索源字段
        if test_data_store['table_id']:
            column_results = await meta_crud.search_source_columns(
                search_term="test",
                knowledge_id=knowledge_id,
                table_id=test_data_store['table_id'],
                limit=10
            )
            print(f"   ✅ 搜索源字段: 找到 {len(column_results)} 个结果")

        # 测试分页查询
        page1_results = await meta_crud.list_source_databases(
            knowledge_id=knowledge_id,
            limit=1,
            offset=0
        )
        print(f"   ✅ 分页查询(第1页): {len(page1_results)} 个结果")

        return True

    except Exception as e:
        print(f"   ❌ 搜索功能测试失败: {e}")
        return False


async def test_vector_search_functionality(rdb_client, knowledge_id: str):
    """测试向量搜索功能"""
    print("\n5️⃣ 测试向量搜索功能:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.search import MetadataSearch

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client

            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            print(f"   ⚠️  向量化客户端未配置，跳过向量搜索测试: {e}")
            return True

        search_engine = MetadataSearch(rdb_client, vdb_client, embedding_client)

        # 测试表的向量搜索
        table_results = await search_engine.search_tables_by_vector(
            query="测试数据库表",
            knowledge_id=knowledge_id,
            limit=5,
            min_score=0.3
        )
        print(f"   ✅ 表向量搜索: 找到 {len(table_results)} 个结果")

        # 显示搜索结果详情
        for i, result in enumerate(table_results[:2]):  # 只显示前2个结果
            score = result.get('score', 0)
            entity_data = result.get('entity_data', {})
            table_name = entity_data.get('table_name', 'Unknown')
            print(f"     结果{i+1}: {table_name} (相似度: {score:.3f})")

        # 测试字段的向量搜索
        column_results = await search_engine.search_columns_by_vector(
            query="测试字段数据",
            knowledge_id=knowledge_id,
            limit=5,
            min_score=0.3
        )
        print(f"   ✅ 字段向量搜索: 找到 {len(column_results)} 个结果")

        # 显示搜索结果详情
        for i, result in enumerate(column_results[:2]):  # 只显示前2个结果
            score = result.get('score', 0)
            entity_data = result.get('entity_data', {})
            column_name = entity_data.get('column_name', 'Unknown')
            print(f"     结果{i+1}: {column_name} (相似度: {score:.3f})")

        # 测试综合向量搜索
        all_results = await search_engine.vector_search(
            query="元数据测试",
            knowledge_id=knowledge_id,
            entity_types=['source_table', 'source_column'],
            limit=10,
            min_score=0.3
        )
        print(f"   ✅ 综合向量搜索: 找到 {len(all_results)} 个结果")

        # 测试搜索结果的相似度排序
        if all_results:
            scores = [result.get('score', 0) for result in all_results]
            is_sorted = all(scores[i] >= scores[i+1] for i in range(len(scores)-1))
            print(f"   ✅ 相似度排序: {'正确' if is_sorted else '错误'}")

        return True

    except Exception as e:
        print(f"   ❌ 向量搜索功能测试失败: {e}")
        return False


async def test_error_handling(rdb_client):
    """测试错误处理"""
    print("\n6️⃣ 测试错误处理:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta
        
        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的
        
        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)

        # 测试获取不存在的记录
        non_existent = await meta_crud.get_source_database(db_id=999999)
        if non_existent is not None:
            raise Exception("获取不存在记录应该返回None")
        print(f"   ✅ 获取不存在记录: 正确返回None")

        # 测试更新不存在的记录
        update_result = await meta_crud.update_source_database(
            {'db_desc': '测试更新'},
            db_id=999999
        )
        if update_result:
            raise Exception("更新不存在记录应该返回False")
        print(f"   ✅ 更新不存在记录: 正确返回False")

        # 测试删除不存在的记录
        delete_result = await meta_crud.delete_source_database(db_id=999999)
        if delete_result:
            raise Exception("删除不存在记录应该返回False")
        print(f"   ✅ 删除不存在记录: 正确返回False")

        return True

    except Exception as e:
        print(f"   ❌ 错误处理测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 元数据系统完整CRUD测试")
    print("=" * 80)

    try:
        # 1. 设置测试环境
        rdb_client, knowledge_id = await setup_test_environment()

        # 2. 执行所有测试
        test_results = []
        
        # 源数据库CRUD操作测试
        result1 = await test_source_database_crud(rdb_client, knowledge_id)
        test_results.append(("源数据库CRUD", result1))

        # 源表CRUD操作测试
        if test_data_store['db_id']:
            result2 = await test_source_table_crud(rdb_client, knowledge_id, test_data_store['db_id'])
            test_results.append(("源表CRUD", result2))

        # 源字段CRUD操作测试
        if test_data_store['table_id']:
            result3 = await test_source_column_crud(rdb_client, knowledge_id, test_data_store['table_id'])
            test_results.append(("源字段CRUD", result3))

        # 搜索功能测试
        result4 = await test_search_functionality(rdb_client, knowledge_id)
        test_results.append(("搜索功能", result4))

        # 向量搜索功能测试
        result5 = await test_vector_search_functionality(rdb_client, knowledge_id)
        test_results.append(("向量搜索功能", result5))

        # 错误处理测试
        result6 = await test_error_handling(rdb_client)
        test_results.append(("错误处理", result6))

        # 3. 输出测试结果
        print("\n" + "=" * 80)
        print("📊 测试结果汇总")
        print("=" * 80)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False

        if all_passed:
            print("\n🎉 所有测试通过！元数据系统CRUD功能正常")
        else:
            print("\n⚠️  部分测试失败，请检查日志")

    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        print(f"\n❌ 测试执行失败: {e}")
    
    finally:
        # 4. 清理测试环境
        await cleanup_test_environment()


if __name__ == "__main__":
    asyncio.run(main())

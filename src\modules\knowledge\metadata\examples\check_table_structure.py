"""
检查数据库表结构脚本

用于查看实际的数据库表结构，确保CRUD代码与实际表结构匹配
"""

import asyncio
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def check_table_structure():
    """检查数据库表结构"""
    print("🔍 检查数据库表结构")
    print("=" * 50)

    try:
        # 获取数据库客户端
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
        from service import get_client

        rdb_client = await get_client("database.rdbs.mysql")

        # 要检查的表列表
        tables_to_check = [
            'md_source_key_relation_info',
            'md_index_key_relation_info',
            'md_source_database',
            'md_index_database',
            'md_source_tables',
            'md_index_tables',
            'md_source_columns',
            'md_index_columns',
            'md_reference_code_set',
            'md_reference_code_value'
        ]

        for table_name in tables_to_check:
            print(f"\n📋 表: {table_name}")
            print("-" * 30)
            
            try:
                # 查询表结构 - 使用aselect方法查询一条记录来了解字段
                result = await rdb_client.aselect(
                    table=table_name,
                    limit=1
                )
                
                if result:
                    print("字段信息:")
                    if len(result) > 0:
                        # 显示字段名称
                        fields = list(result[0].keys())
                        for field_name in fields:
                            print(f"  - {field_name}")
                    else:
                        print("  表为空，但存在")
                else:
                    print("  ❌ 无法获取表结构或表为空")
                    
            except Exception as e:
                print(f"  ❌ 表不存在或查询失败: {e}")

        print("\n" + "=" * 50)
        print("✅ 表结构检查完成")

    except Exception as e:
        logger.error(f"检查表结构失败: {e}")
        print(f"❌ 检查失败: {e}")


async def main():
    """主函数"""
    await check_table_structure()


if __name__ == "__main__":
    asyncio.run(main())

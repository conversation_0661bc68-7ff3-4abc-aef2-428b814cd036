-- ==========================================
-- 元数据领域（Metadata） - 数据库表结构
-- ==========================================
-- 
-- 说明：
-- 1. 本脚本专门用于创建元数据（Metadata）相关的数据库表。
-- 2. 包含知识库主表和所有以 `md_` 为前缀的表。
-- 
-- 创建时间：2025-07-04
-- ==========================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ==========================================
-- 1. 全局知识库表 (Global Knowledge Base)
-- ==========================================
DROP TABLE IF EXISTS `kb_knowledge`;
CREATE TABLE `kb_knowledge` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `knowledge_name` varchar(255) NOT NULL COMMENT '知识库名称',
  `doc_nums` int NOT NULL DEFAULT '0' COMMENT '知识库中的文档数量',
  `knowledge_type` varchar(255) NOT NULL COMMENT '知识库类别：DD/Doc/MetaData',
  `knowledge_desc` varchar(255) NOT NULL COMMENT '知识库的描述信息',
  `models` json NOT NULL COMMENT '绑定的模型配置(JSON格式): {"embedding": "model_name", "llm": "model_name", ...}',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '知识库创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '知识库最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_id` (`knowledge_id`),
  KEY `idx_knowledge_name` (`knowledge_name`),
  KEY `idx_knowledge_type` (`knowledge_type`),
  KEY `idx_models` ((CAST(`models`->>'$.embedding' AS CHAR(255)))),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='全局知识库表';

-- ==========================================
-- 元数据领域表 (Metadata Domain Tables)
-- 表名前缀: md_ (Metadata)
-- ==========================================

-- ==========================================
-- 2. 源数据库管理表 (Source Database)
-- ==========================================
DROP TABLE IF EXISTS `md_source_database`;
CREATE TABLE `md_source_database` (
  `db_id` bigint NOT NULL AUTO_INCREMENT COMMENT '数据库ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '关联的知识库ID',
  `db_name` varchar(100) NOT NULL COMMENT '数据库名称',
  `db_name_cn` varchar(100) DEFAULT NULL COMMENT '数据库中文名称',
  `data_layer` varchar(50) NOT NULL COMMENT '数据层标识：adm-管理层，bdm-基础数据层，ods-操作数据层，ads-应用数据层',
  `db_desc` text COMMENT '数据库描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`db_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_data_layer` (`data_layer`),
  KEY `idx_db_name` (`db_name`),
  KEY `idx_is_active` (`is_active`),
  UNIQUE KEY `uk_source_database_business` (`knowledge_id`, `db_name`),
  CONSTRAINT `fk_md_source_database_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='源数据库管理表';

-- ==========================================
-- 3. 指标数据库管理表 (Index Database)
-- ==========================================
DROP TABLE IF EXISTS `md_index_database`;
CREATE TABLE `md_index_database` (
  `db_id` bigint NOT NULL AUTO_INCREMENT COMMENT '数据库ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '关联的知识库ID',
  `db_name` varchar(100) NOT NULL COMMENT '数据库名称',
  `db_name_cn` varchar(100) DEFAULT NULL COMMENT '数据库中文名称',
  `data_layer` varchar(50) NOT NULL COMMENT '数据层标识：adm-管理层，bdm-基础数据层，ods-操作数据层，ads-应用数据层',
  `db_desc` text COMMENT '数据库描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`db_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_data_layer` (`data_layer`),
  KEY `idx_db_name` (`db_name`),
  KEY `idx_is_active` (`is_active`),
  UNIQUE KEY `uk_index_database_business` (`knowledge_id`, `db_name`),
  CONSTRAINT `fk_md_index_database_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标数据库管理表';

-- ==========================================
-- 4. 源表管理表 (Source Tables)
-- ==========================================
DROP TABLE IF EXISTS `md_source_tables`;
CREATE TABLE `md_source_tables` (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '表ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `db_id` bigint NOT NULL COMMENT '数据源ID',
  `table_name` varchar(100) NOT NULL COMMENT '表名',
  `table_name_cn` varchar(100) DEFAULT NULL COMMENT '表中文名',
  `table_desc` text COMMENT '表描述',

  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`table_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_db_id` (`db_id`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_is_active` (`is_active`),
  UNIQUE KEY `uk_source_tables_business` (`knowledge_id`, `db_id`, `table_name`),
  CONSTRAINT `fk_md_source_tables_db_id` FOREIGN KEY (`db_id`) REFERENCES `md_source_database` (`db_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_source_tables_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='源表管理表';

-- ==========================================
-- 5. 指标表管理表 (Index Tables)
-- ==========================================
DROP TABLE IF EXISTS `md_index_tables`;
CREATE TABLE `md_index_tables` (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '表ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `db_id` bigint NOT NULL COMMENT '数据源ID',
  `table_name` varchar(100) NOT NULL COMMENT '表名',
  `table_name_cn` varchar(100) DEFAULT NULL COMMENT '表中文名',
  `table_desc` text COMMENT '表描述',


  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`table_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_db_id` (`db_id`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_is_active` (`is_active`),
  UNIQUE KEY `uk_index_tables_business` (`knowledge_id`, `db_id`, `table_name`),
  CONSTRAINT `fk_md_index_tables_db_id` FOREIGN KEY (`db_id`) REFERENCES `md_index_database` (`db_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_index_tables_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标表管理表';

-- ==========================================
-- 6. 源字段管理表 (Source Columns)
-- ==========================================
DROP TABLE IF EXISTS `md_source_columns`;
CREATE TABLE `md_source_columns` (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字段ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `table_id` bigint NOT NULL COMMENT '表ID',
  `column_name` varchar(100) NOT NULL COMMENT '字段名',
  `column_name_cn` varchar(100) DEFAULT NULL COMMENT '字段中文名',
  `column_desc` text COMMENT '字段描述',
  `data_type` varchar(50) DEFAULT NULL COMMENT '数据类型：NUMBER,STRING,DATE等',
  `data_example` text COMMENT '数据样例',
  `is_vectorized` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已向量化：1-是，0-否',
  `is_primary_key` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否主键：1-是，0-否',
  `is_sensitive` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否敏感数据：1-是，0-否',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`column_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_table_id` (`table_id`),
  KEY `idx_column_name` (`column_name`),
  KEY `idx_is_primary_key` (`is_primary_key`),
  KEY `idx_is_vectorized` (`is_vectorized`),
  UNIQUE KEY `uk_source_columns_business` (`knowledge_id`, `table_id`, `column_name`),
  CONSTRAINT `fk_md_source_columns_table_id` FOREIGN KEY (`table_id`) REFERENCES `md_source_tables` (`table_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_source_columns_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='源字段管理表';

-- ==========================================
-- 7. 指标字段管理表 (Index Columns)
-- ==========================================
DROP TABLE IF EXISTS `md_index_columns`;
CREATE TABLE `md_index_columns` (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字段ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `table_id` bigint NOT NULL COMMENT '表ID',
  `column_name` varchar(100) NOT NULL COMMENT '字段名',
  `column_name_cn` varchar(100) DEFAULT NULL COMMENT '字段中文名',
  `index_type` varchar(50) DEFAULT NULL COMMENT '指标类型：atom-原子指标，compute-计算指标',
  `column_desc` text COMMENT '字段描述',
  `data_type` varchar(50) DEFAULT NULL COMMENT '数据类型：NUMBER,STRING,DATE,INDEX_DATE等',
  `data_example` text COMMENT '数据样例',
  `comment` text COMMENT '备注说明',
  `is_vectorized` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已向量化：1-是，0-否',
  `is_primary_key` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否主键：1-是，0-否',
  `is_sensitive` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否敏感数据：1-是，0-否',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`column_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_table_id` (`table_id`),
  KEY `idx_column_name` (`column_name`),
  KEY `idx_index_type` (`index_type`),
  KEY `idx_is_primary_key` (`is_primary_key`),
  KEY `idx_is_vectorized` (`is_vectorized`),
  UNIQUE KEY `uk_index_columns_business` (`knowledge_id`, `table_id`, `column_name`),
  CONSTRAINT `fk_md_index_columns_table_id` FOREIGN KEY (`table_id`) REFERENCES `md_index_tables` (`table_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_index_columns_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标字段管理表';

-- ==========================================
-- 8. 源关联键信息表 (Source Key Relation Info)
-- ==========================================
DROP TABLE IF EXISTS `md_source_key_relation_info`;
CREATE TABLE `md_source_key_relation_info` (
  `relation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `source_column_id` bigint NOT NULL COMMENT '源字段ID',
  `target_column_id` bigint NOT NULL COMMENT '目标字段ID',
  `relation_type` varchar(20) NOT NULL DEFAULT 'FK' COMMENT '关联类型：FK-外键，REF-参考关联',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`relation_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_source_column_id` (`source_column_id`),
  KEY `idx_target_column_id` (`target_column_id`),
  KEY `idx_relation_type` (`relation_type`),
  UNIQUE KEY `uk_source_key_relation_business` (`knowledge_id`, `source_column_id`, `target_column_id`, `relation_type`),
  CONSTRAINT `fk_md_source_key_relation_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_source_key_relation_source` FOREIGN KEY (`source_column_id`) REFERENCES `md_source_columns` (`column_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_source_key_relation_target` FOREIGN KEY (`target_column_id`) REFERENCES `md_source_columns` (`column_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='源关联键信息表';
-- ==========================================
-- 9. 指标关联键信息表 (Index Key Relation Info)
-- ==========================================
DROP TABLE IF EXISTS `md_index_key_relation_info`;
CREATE TABLE `md_index_key_relation_info` (
  `relation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `source_column_id` bigint NOT NULL COMMENT '源字段ID',
  `target_column_id` bigint NOT NULL COMMENT '目标字段ID',
  `relation_type` varchar(20) NOT NULL DEFAULT 'FK' COMMENT '关联类型：FK-外键，REF-参考关联',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`relation_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_source_column_id` (`source_column_id`),
  KEY `idx_target_column_id` (`target_column_id`),
  KEY `idx_relation_type` (`relation_type`),
  UNIQUE KEY `uk_index_key_relation_business` (`knowledge_id`, `source_column_id`, `target_column_id`, `relation_type`),
  CONSTRAINT `fk_md_index_key_relation_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_index_key_relation_source` FOREIGN KEY (`source_column_id`) REFERENCES `md_index_columns` (`column_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_index_key_relation_target` FOREIGN KEY (`target_column_id`) REFERENCES `md_index_columns` (`column_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标关联键信息表';

-- ==========================================
-- 10. 参考码值集表 (Reference Code Set) - 先创建
-- ==========================================
DROP TABLE IF EXISTS `md_reference_code_set`;
CREATE TABLE `md_reference_code_set` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '码值集ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `code_set_name` varchar(100) NOT NULL COMMENT '码值集名称',
  `code_set_desc` varchar(500) DEFAULT NULL COMMENT '码值集描述',
  `code_set_type` varchar(50) DEFAULT 'ENUM' COMMENT '码值集类型',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_code_set_name` (`knowledge_id`, `code_set_name`),
  UNIQUE KEY `uk_reference_code_set_business` (`knowledge_id`, `code_set_name`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_md_reference_code_set_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参考码值集表';

-- ==========================================
-- 11. 码值集文档表已删除 - 文件名直接存储在码值集表中
-- ==========================================

-- ==========================================
-- 11. 码值关联表 (Code Set Relation)
-- ==========================================
DROP TABLE IF EXISTS `md_reference_code_relation`;
CREATE TABLE `md_reference_code_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `column_id` bigint NOT NULL COMMENT '字段ID（源字段或指标字段）',
  `code_set_id` bigint NOT NULL COMMENT '码值集ID',
  `column_type` enum('source','index') NOT NULL DEFAULT 'source' COMMENT '字段类型：source-源字段，index-指标字段',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_column_code_set` (`column_id`, `code_set_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_code_set_id` (`code_set_id`),
  KEY `idx_column_type` (`column_type`),
  CONSTRAINT `fk_md_code_relation_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_code_relation_code_set` FOREIGN KEY (`code_set_id`) REFERENCES `md_reference_code_set` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='码值关联表';

-- ==========================================
-- 12. 参考码值表 (Reference Code Value)
-- ==========================================
DROP TABLE IF EXISTS `md_reference_code_value`;
CREATE TABLE `md_reference_code_value` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '码值ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `code_set_id` bigint NOT NULL COMMENT '码值集ID',
  `code_value` varchar(100) NOT NULL COMMENT '码值',
  `code_desc` varchar(200) NOT NULL COMMENT '码值描述',
  `code_value_cn` varchar(200) DEFAULT NULL COMMENT '码值中文描述',

  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_code_set_id` (`code_set_id`),
  KEY `idx_code_value` (`code_value`),

  KEY `idx_is_active` (`is_active`),
  UNIQUE KEY `uk_code_set_code_value` (`code_set_id`, `code_value`),
  UNIQUE KEY `uk_reference_code_value_business` (`knowledge_id`, `code_set_id`, `code_value`),
  CONSTRAINT `fk_md_code_value_set` FOREIGN KEY (`code_set_id`) REFERENCES `md_reference_code_set` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_code_value_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参考码值表';



-- ==========================================
-- 17. 数据主题表 (Data Subject)
-- ==========================================
DROP TABLE IF EXISTS `md_data_subject`;
CREATE TABLE `md_data_subject` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '数据主题ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `subject_code` varchar(50) NOT NULL COMMENT '数据主题编码',
  `subject_name` varchar(100) NOT NULL COMMENT '数据主题名称',
  `subject_desc` text COMMENT '数据主题描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_subject_code` (`knowledge_id`, `subject_code`),
  UNIQUE KEY `uk_data_subject_business` (`knowledge_id`, `subject_code`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_subject_name` (`subject_name`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_md_data_subject_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据主题表';

-- ==========================================
-- 18. 数据主题关联表 (Data Subject Relation)
-- ==========================================
DROP TABLE IF EXISTS `md_data_subject_relation`;
CREATE TABLE `md_data_subject_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `subject_id` bigint NOT NULL COMMENT '数据主题ID',
  `source_type` enum('SOURCE','INDEX') NOT NULL COMMENT '目标类型：SOURCE-源数据，INDEX-指标数据',
  `relation_path` varchar(500) NOT NULL COMMENT '关联路径：db 或 db.table 或 db.table.col',
  `relation_level` enum('DATABASE','TABLE','COLUMN') NOT NULL COMMENT '关联层级：DATABASE-数据库级别，TABLE-表级别，COLUMN-字段级别',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_subject_id` (`subject_id`),
  KEY `idx_source_type` (`source_type`),
  KEY `idx_relation_level` (`relation_level`),
  KEY `idx_relation_path` (`relation_path`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_level_type` (`relation_level`, `source_type`),
  UNIQUE KEY `uk_data_subject_relation_business` (`knowledge_id`, `subject_id`, `source_type`, `relation_path`),
  CONSTRAINT `fk_md_subject_relation_subject` FOREIGN KEY (`subject_id`) REFERENCES `md_data_subject` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_subject_relation_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据主题关联表-路径字符串设计';

-- ==========================================
-- 19. 外键约束已在表创建时定义
-- ==========================================
-- 所有外键约束已在各表的 CREATE TABLE 语句中定义

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1; 
# DD-B增强处理API使用指南

## 📋 API概览

DD-B增强处理API提供异步智能字段填充和数据处理功能，采用立即响应 + 后台处理 + 前端回调的模式。

**处理模式**:
1. 立即验证数据存在性并返回状态
2. 后台异步处理（消息队列模式，15条/批次）
3. 处理完成后回调前端接口

### 🔗 基础信息
- **基础URL**: `http://localhost:30337`
- **API前缀**: `/api/dd/dd-b`
- **认证**: 无需认证（内部服务）
- **内容类型**: `application/json`

## 🚀 API端点

### 1. 健康检查
**GET** `/api/dd/dd-b/health`

检查DD-B模块的服务状态和依赖。

**响应示例**:
```json
{
  "code": "0",
  "msg": "DD-B模块运行正常",
  "data": {
    "status": "healthy",
    "mysql_client": "connected",
    "pgvector_client": "connected",
    "embedding_client": "connected",
    "dd_b_module": "available"
  }
}
```

### 2. DD-B异步处理
**POST** `/api/dd/dd-b/process`

异步处理DD-B数据，立即返回状态，后台处理完成后回调前端。

**请求参数**:
```json
{
  "report_code": "G0107_beta_v1.0",
  "dept_id": "30239"
}
```

**参数说明**:
- `report_code` (必需): 报表代码，如 "G0107_beta_v1.0"
- `dept_id` (必需): 部门ID，如 "30239"

**立即响应示例**:

成功（数据存在）:
```json
{
  "code": "0",
  "msg": "业务信息正在处理中"
}
```

失败（数据不存在）:
```json
{
  "code": "400",
  "msg": "无法在数据库里搜索到相关信息"
}
```

**前端回调格式**:
处理完成后，系统会回调前端接口，格式如下：
```json
{
  "report_code": "G0107_beta_v1.0",
  "dept_id": "30239",
  "item": [
    {
      "entry_id": "12345",
      "entry_type": "TABLE",
      "BDR05": "不适用",
      "BDR06": "不适用",
      "BDR07": "1",
      "BDR08": "CRD",
      "BDR09": "客户基本信息",
      "BDR10": "个人客户信息表",
      "BDR11": "客户姓名,身份证号",
      "BDR12": "不适用",
      "BDR13": "不适用",
      "BDR14": "不适用",
      "BDR15": "不适用",
      "BDR16": "用于存储个人客户的基本信息",
      "BDR17": "不适用",
      "BDR18": "不适用",
      "BDR19": "不适用"
    },
    {
      "entry_id": "12346",
      "entry_type": "ITEM",
      "BDR01": "客户姓名",
      "BDR02": "VARCHAR(100)",
      "BDR03": "必填",
      "BDR04": "客户的真实姓名"
    }
  ]
}
```

**前端回调响应**:
前端需要返回以下格式：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": false
}
```



## 📊 功能特性

### 智能字段填充
- **BDR字段**: BDR05-17（9个字段）
- **SDR字段**: SDR01-15（15个字段）
- **总计**: 26个字段的智能填充

### 处理策略
1. **高置信度策略（≥60%）**:
   - 直接使用历史数据
   - 不应用默认值，保持原始空值
   - 同时填充BDR和SDR字段

2. **低置信度策略（<60%）**:
   - 有历史数据：使用历史数据
   - 无历史数据：应用默认值
   - Pipeline生成：调用nl2sql_pipeline

### 默认值设置
```python
# BDR字段默认值
BDR05: "不适用"
brd06: "不适用"  
brd07: "1"
brd08: "CRD"
BDR12-17: "不适用"

# SDR字段默认值
SDR02: "1"
SDR03: "CRD"
SDR04,07,11,13,14: "不适用"
SDR15: ""
```

### 并发优化
- **全局LLM并发**: 15个并发槽位
- **批量处理**: 15条记录/批次
- **4层并发控制**: LLM + Pipeline + 向量搜索 + 数据库

## 🔧 使用示例

### Python示例
```python
import httpx
import asyncio

async def test_dd_b_api():
    async with httpx.AsyncClient() as client:
        # 1. 健康检查
        health = await client.get("http://localhost:30337/api/dd/dd-b/health")
        print(f"健康状态: {health.json()}")
        
        # 2. DD-B处理
        request_data = {
            "report_code": "S71_ADS_RELEASE_V0",
            "dept_id": "30239",
            "enable_auto_fill": True,
            "return_original_data": False
        }
        
        response = await client.post(
            "http://localhost:30337/api/dd/dd-b/process",
            json=request_data
        )
        
        result = response.json()
        print(f"处理结果: {result}")

# 运行测试
asyncio.run(test_dd_b_api())
```

### cURL示例
```bash
# 健康检查
curl -X GET "http://localhost:30337/api/dd/dd-b/health"

# DD-B处理
curl -X POST "http://localhost:30337/api/dd/dd-b/process" \
  -H "Content-Type: application/json" \
  -d '{
    "report_code": "S71_ADS_RELEASE_V0",
    "dept_id": "30239",
    "enable_auto_fill": true,
    "return_original_data": false
  }'
```

## 📈 性能指标

### 基准性能
- **单条记录处理**: < 50ms
- **批量处理(15条)**: < 500ms
- **字段填充率**: > 80%
- **并发处理**: 15个LLM并发

### 监控指标
- `total_records_found`: 查询到的记录总数
- `records_processed`: 实际处理的记录数
- `total_fields_filled`: 填充的字段总数
- `processing_time_ms`: 处理耗时（毫秒）

## ❌ 错误处理

### 常见错误码
- `400`: 请求参数错误
- `422`: 参数验证失败
- `500`: 服务器内部错误

### 错误响应示例
```json
{
  "detail": "参数错误: report_code不能为空"
}
```

## 🔍 故障排除

### 1. 数据库连接失败
```json
{
  "detail": "数据库连接失败: connection refused"
}
```
**解决方案**: 检查MySQL服务状态和连接配置

### 2. 无记录找到
```json
{
  "data": {
    "total_records_found": 0,
    "status": "SUCCESS"
  }
}
```
**解决方案**: 检查report_code和dept_id是否存在于biz_dd_post表

### 3. 字段名不匹配
**解决方案**: 确认数据库字段名大小写（brd06, brd07, brd08为小写）

## 🚀 集成指南

### 1. 前端集成
```javascript
// 调用DD-B API
const response = await fetch('/api/dd/dd-b/process', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    report_code: 'S71_ADS_RELEASE_V0',
    dept_id: '30239',
    enable_auto_fill: true
  })
});

const result = await response.json();
console.log('处理结果:', result);
```

### 2. 服务间调用
```python
from api.dd_submission import dd_b_enhanced_router

# 在其他服务中包含路由
app.include_router(dd_b_enhanced_router, prefix="/api/dd")
```

## 📋 完整路由路径

| 功能 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 健康检查 | GET | `/api/dd/dd-b/health` | 检查服务状态 |
| 增强处理 | POST | `/api/dd/dd-b/process` | 单个report_code处理 |

## 🎯 最佳实践

1. **参数验证**: 确保report_code和dept_id不为空
2. **单次处理**: 每次只处理一个report_code
3. **错误处理**: 实现完整的错误处理逻辑
4. **性能监控**: 监控processing_time_ms和字段填充率
5. **健康检查**: 定期调用health端点检查服务状态

DD-B增强处理API现已集成到主应用中，可以通过上述端点访问所有功能！🎉

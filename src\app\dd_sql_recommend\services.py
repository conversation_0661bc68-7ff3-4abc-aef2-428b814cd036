"""
DD SQL推荐服务核心逻辑
"""

import logging
from typing import List, Optional, Dict, Any
import json
import asyncio

from .models import (
    SQLRecommendRequest,
    QuestionRecommendRequest,
    SQLGenerateRequest,
    SingleSQLGenerateRequest,
    SQLIntegrationRequest
)
from modules.knowledge.dd.crud import DDCrud
from modules.knowledge.dd.search import DDSearch, SearchMode, SearchField
from base.model_serve.model_runtime.entities.message_entities import PromptMessageRole
from base.model_serve.model_runtime.entities import PromptMessage

logger = logging.getLogger(__name__)


class DDSQLRecommendService:
    """DD SQL推荐服务"""

    def __init__(self, rdb_client, vdb_client=None, embedding_client=None, llm_client=None):
        """
        初始化服务
        
        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端（可选）
            embedding_client: 向量化模型客户端（可选）
            llm_client: 大语言模型客户端（可选）
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
        self.llm_client = llm_client
        self.dd_crud = DDCrud(rdb_client, vdb_client, embedding_client)

    async def recommend_sql(self, request: SQLRecommendRequest) -> Optional[str]:
        """
        推荐SQL接口
        通过submission_id和version字段，唯一确定到biz_dd_pre_distribution表中的一条填报项记录，
        获取这个填报项的【名称】和【描述】。即【dr09】和【dr17】，通过这两个内容，去向量库搜索，
        取近似度最高的一条记录，且高于阈值0.7。如果存在符合要求的记录，去历史dd库查询匹配填报项的信息，并返回对应sql。
        
        Args:
            request: SQL推荐请求
            
        Returns:
            推荐的SQL语句，如果没有找到则返回None
        """
        try:
            # 解析report_code获取version
            version = request.report_code
            submission_id = request.entry_id
            
            # 1. 通过submission_id和version获取填报项记录
            pre_distribution = await self.dd_crud.get_pre_distribution(
                submission_id=submission_id,
                version=version
            )
            
            if not pre_distribution:
                logger.warning(f"未找到填报项记录: submission_id={submission_id}, version={version}")
                return None
            
            # 2. 获取填报项的名称(dr09)和描述(dr17)
            item_name = pre_distribution.get("dr09", "")
            item_description = pre_distribution.get("dr17", "")
            
            if not item_name and not item_description:
                logger.warning(f"填报项缺少名称和描述: submission_id={request.entry_id}")
                return None
            
            # 3. 使用向量库搜索相似记录
            dd_search = DDSearch(self.rdb_client, self.vdb_client, self.embedding_client)
            
            # 搜索相似的名称
            name_results = []
            if item_name:
                name_results = await dd_search.search(
                    query=item_name,
                    mode=SearchMode.VECTOR,
                    field=SearchField.DATA_ITEM_NAME,
                    limit=1,
                    min_score=0.7
                )
            
            # 搜索相似的描述
            desc_results = []
            if item_description:
                desc_results = await dd_search.search(
                    query=item_description,
                    mode=SearchMode.VECTOR,
                    field=SearchField.REQUIREMENT_RULE,
                    limit=1,
                    min_score=0.7
                )
            
            # 4. 选择最相似的记录
            best_result = None
            best_score = 0.0
            
            # 检查名称匹配结果
            for result in name_results:
                score = result.get("score", 0.0)
                if score > best_score:
                    best_score = score
                    best_result = result
            
            # 检查描述匹配结果
            for result in desc_results:
                score = result.get("score", 0.0)
                if score > best_score:
                    best_score = score
                    best_result = result
            
            # 5. 如果找到了相似记录，查询历史DD库获取SQL
            if best_result:
                data_row_id = best_result.get("data_row_id")
                if data_row_id:
                    # 查询历史记录获取SQL（这里假设SQL存储在某个字段中）
                    # 实际实现中可能需要从其他表或系统中获取SQL
                    historical_record = await self.dd_crud.get_submission_data(data_row_id)
                    if historical_record:

                        recommended_sql = historical_record.get("sdr10", "")  # C-sdr10-数据加工逻辑
                        if recommended_sql:
                            return recommended_sql
            
            return None
            
        except Exception as e:
            logger.error(f"推荐SQL失败: {e}")
            return None

    async def recommend_questions(self, request: QuestionRecommendRequest) -> List[str]:
        """
        推荐指引问题接口
        查询dd-a表获取填报项描述、名称，查询dd-b表获取业务逻辑，
        编排prompt要求LLM生成拆分任务的指引问题。
        
        Args:
            request: 问题推荐请求
            
        Returns:
            推荐的问题列表
        """
        try:
            # 解析report_code获取version
            version = request.report_code
            
            # 1. 查询dd-a表(biz_dd_pre_distribution)获取填报项信息
            pre_distribution = await self.dd_crud.get_pre_distribution(
                submission_id=request.entry_id,
                version=version
            )
            
            if not pre_distribution:
                logger.warning(f"未找到填报项记录: submission_id={request.entry_id}, version={version}")
                return []
            
            item_name = pre_distribution.get("dr09", "")
            item_description = pre_distribution.get("dr17", "")
            
            # 2. 查询dd-b表(biz_dd_post_distribution)获取业务逻辑
            post_distribution = await self.dd_crud.get_post_distribution(
                submission_id=request.entry_id,
                version=version,
                dept_id=request.dept_id
            )
            
            business_logic = ""
            if post_distribution:
                # 获取业务逻辑相关字段，如bdr05(业务流程)、bdr17(业务数据匹配逻辑)等
                business_process = post_distribution.get("bdr05", "")
                data_matching_logic = post_distribution.get("bdr17", "")
                business_logic = f"{business_process} {data_matching_logic}".strip()
            
            # 3. 编排prompt
            prompt = f"""
            你是一个数据分析师，请根据以下信息将复杂的数据需求任务拆分为多个子任务：

            数据项名称: {item_name}
            数据项描述: {item_description}
            业务逻辑: {business_logic}

            请将上述任务拆分为3-5个具体的子任务，并以问题的形式表述，每个问题都应该对应着一种SQL的操作。
            以下表述是**推荐的**
            "需要从b表拿到kk字段，c表拿到ww字段"
            "以union的形式，筛选ctime将以上信息拼接起来"
            以下表述是**不推荐的**
            "如何清洗客户基本信息表中的姓名、身份证号、联系电话和地址数据以确保准确性"
            "在关联过程中，如何处理缺失或不一致的数据项以确保数据完整性"
            
            只返回问题列表，每行一个问题，不要包含其他内容。
            """
            
            # 4. 调用LLM生成问题
            if not self.llm_client:
                logger.warning("LLM客户端未配置，无法生成问题")
                return []
            
            messages = [PromptMessage(role=PromptMessageRole.USER, content=prompt)]
            result = await self.llm_client.ainvoke(prompt_messages=messages, stream=False)
            
            if result and result.message and result.message.content:
                # 解析LLM返回的内容为问题列表
                content = result.message.content.strip()
                questions = [q.strip() for q in content.split('\n') if q.strip()]
                return questions[:8]  # 限制最多返回8个问题
            
            return []
            
        except Exception as e:
            logger.error(f"推荐问题失败: {e}")
            return []

    async def generate_sql_list(self, request: SQLGenerateRequest) -> List[str]:
        """
        批量生成SQL接口
        遍历问题列表，调用nl2sql生成模块生成SQL
        
        Args:
            request: SQL生成请求
            
        Returns:
            生成的SQL列表
        """
        sql_list = []
        try:
            tasks = []
            # 遍历问题列表，逐个生成SQL
            # 由于问题数量较少，全部并发执行
            for question in request.question_list:
                single_request = SingleSQLGenerateRequest(
                    report_code=request.report_code,
                    dept_id=request.dept_id,
                    entry_id=request.entry_id,
                    question=question
                )
                tasks.append(self.generate_single_sql(single_request))
            results = await asyncio.gather(*tasks)
            sql_list = [sql if sql is not None else "" for sql in results]
            
            return sql_list
            
        except Exception as e:
            logger.error(f"批量生成SQL失败: {e}")
            return sql_list

    async def generate_single_sql(self, request: SingleSQLGenerateRequest) -> Optional[str]:
        """
        单个生成SQL接口
        调用nl2sql生成模块生成SQL
        
        Args:
            request: 单个SQL生成请求
            
        Returns:
            生成的SQL语句
        """
        try:
            # 调用nl2sql生成模块
            from pipeline.steps.sql_generator import SQLGeneratorStep
            
            # 创建SQL生成步骤实例
            sql_generator = SQLGeneratorStep()
            
            # 创建上下文
            from pipeline.core.context import PipelineContext
            context = PipelineContext(user_question=request.question)
            context.user_question = request.question
            
            # 获取数据库schema
            post_distribution = await self.dd_crud.get_post_distribution(
                submission_id=request.entry_id,
                version=request.report_code,
                dept_id=request.dept_id
            )
            
            if not post_distribution:
                logger.warning(f"未找到填报项记录: submission_id={request.entry_id}, version={request.report_code}")
                return None

            table_name = post_distribution.get("bdr09", "") # 对应DD字段名与包老师确认
            columns_names = post_distribution.get("bdr11", "")


            db_schema = f"""
            表名: {table_name}
            字段: {columns_names}
            """
            
            context.set("db_schema", db_schema)
            context.set("db_type", "MySQL")
            
            # 执行SQL生成
            result = await sql_generator.execute(context)
            
            # 从结果中提取SQL
            if result and isinstance(result.data, dict) and result.data.get('sql_candidates'):
                return result.data.get('sql_candidates')[0] # 返回第一个候选SQL
            
            return None
            
        except Exception as e:
            logger.error(f"生成SQL失败: {e}")
            return None

    async def integrate_sql(self, request: SQLIntegrationRequest) -> Optional[str]:
        """
        SQL整合接口
        调用LLM将各段SQL进行拼接整合
        
        Args:
            request: SQL整合请求
            
        Returns:
            整合后的SQL语句
        """
        try:
            if not self.llm_client:
                logger.warning("LLM客户端未配置，无法整合SQL")
                return None
            
            # 构建prompt
            prompt = f"""
            你是一个SQL专家，请根据以下问题和对应的SQL语句，将它们整合成一个完整的SQL查询：

            问题列表:
            {chr(10).join([f"{i+1}. {q}" for i, q in enumerate(request.question_list)])}

            对应的SQL语句:
            {chr(10).join([f"-- {request.question_list[i]}{chr(10)}{sql}" for i, sql in enumerate(request.sql_list)])}

            请将上述SQL语句整合成一个完整的查询，考虑它们之间的关联关系和执行顺序。
            只返回整合后的SQL语句，不要包含其他内容。
            """
            
            messages = [PromptMessage(role=PromptMessageRole.USER, content=prompt)]
            result = await self.llm_client.ainvoke(prompt_messages=messages, stream=False)
            
            if result and result.message and result.message.content:
                return result.message.content.strip()
            
            return None
            
        except Exception as e:
            logger.error(f"整合SQL失败: {e}")
            return None

#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：src
@File    ：ier_chatbot_model.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/26 21:31 
@Desc    ： 
"""
from dataclasses import dataclass, asdict
from enum import Enum
from pydantic import BaseModel, constr
from typing import List, Optional, Dict

# Chatbot查询模型
class ChatbotQueryModel(BaseModel):
    user_query: str  # 用户的问题
    request_id: str # 用户的请求
    user_id: str # 用户的id
    session_id: str # 会话id

# Chatbot保存结果模型
class ChatbotSaveModel(BaseModel):
    user_query: str  # 用户的问题
    request_id: str # 用户的请求
    user_id: str # 用户的id
    session_id: str # 会话id
    answer: str # 大模型的回答

class ChatBotQuestionType(Enum):
    LAW_ASSOCIATION = 1  # 法规关联
    LAW_DIFFERENCE = 2  # 法规差异
    LAW_OVERVIEW = 3  # 法规概述
    LAW_POSITIONING = 4  # 法规定位
    LAW_TIME_JUDGMENT = 5  # 法规的时间判断
    OTHER = 6  # 其他类型

    def description(self):
        """返回对应类型的中文描述"""
        descriptions = {
            self.LAW_ASSOCIATION: "法规关联",
            self.LAW_DIFFERENCE: "法规差异",
            self.LAW_OVERVIEW: "法规概述",
            self.LAW_POSITIONING: "法规定位",
            self.LAW_TIME_JUDGMENT: "法规的时间判断",
            self.OTHER: "其他类型"
        }
        return descriptions.get(self, "未知类型")
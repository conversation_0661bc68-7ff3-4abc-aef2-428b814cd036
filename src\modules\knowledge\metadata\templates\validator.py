"""
文档模板验证器

验证文档模板的格式和内容是否符合要求。
"""

from typing import Any, Dict, List
import logging

# 标准化日志导入
logger = logging.getLogger(__name__)


class TemplateValidator:
    """
    文档模板验证器
    
    验证文档模板的格式和内容：
    - 文件格式验证
    - 数据结构验证
    - 元数据字段验证
    - 数据质量验证
    """
    
    def __init__(self):
        """初始化模板验证器"""
        self.validation_rules = {
            "excel": {
                "required_sheets": [],  # 可配置必需的工作表
                "min_columns": 1,
                "min_rows": 1,
                "max_file_size": 50 * 1024 * 1024  # 50MB
            },
            "csv": {
                "min_columns": 1,
                "min_rows": 1,
                "max_file_size": 20 * 1024 * 1024  # 20MB
            },
            "json": {
                "max_depth": 10,
                "max_file_size": 10 * 1024 * 1024  # 10MB
            }
        }
        logger.info("文档模板验证器初始化完成")
    
    async def validate_template(
        self, 
        parsed_data: Dict[str, Any], 
        template_type: str
    ) -> Dict[str, Any]:
        """
        验证文档模板
        
        Args:
            parsed_data: 解析后的数据
            template_type: 模板类型
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            validation_result = {
                "valid": True,
                "errors": [],
                "warnings": [],
                "metadata": {
                    "template_type": template_type,
                    "validation_time": None
                }
            }
            
            # 根据模板类型进行验证
            if template_type == "excel":
                await self._validate_excel(parsed_data, validation_result)
            elif template_type == "csv":
                await self._validate_csv(parsed_data, validation_result)
            elif template_type == "json":
                await self._validate_json(parsed_data, validation_result)
            else:
                validation_result["valid"] = False
                validation_result["errors"].append(f"不支持的模板类型: {template_type}")
            
            # 通用验证
            await self._validate_metadata_candidates(parsed_data, validation_result)
            
            # 设置最终验证状态
            validation_result["valid"] = len(validation_result["errors"]) == 0
            
            logger.info(f"模板验证完成: template_type={template_type}, valid={validation_result['valid']}")
            return validation_result
            
        except Exception as e:
            logger.error(f"模板验证失败: template_type={template_type}, error={e}")
            return {
                "valid": False,
                "errors": [f"验证过程异常: {str(e)}"],
                "warnings": [],
                "metadata": {"template_type": template_type}
            }
    
    async def _validate_excel(
        self, 
        parsed_data: Dict[str, Any], 
        validation_result: Dict[str, Any]
    ):
        """验证Excel模板"""
        try:
            rules = self.validation_rules["excel"]
            
            # 检查工作表数量
            if not parsed_data.get("sheets"):
                validation_result["errors"].append("Excel文件没有工作表")
                return
            
            # 验证每个工作表
            for sheet_name, sheet_data in parsed_data["sheets"].items():
                # 检查列数
                if sheet_data.get("columns", 0) < rules["min_columns"]:
                    validation_result["errors"].append(
                        f"工作表 '{sheet_name}' 列数不足: {sheet_data.get('columns')} < {rules['min_columns']}"
                    )
                
                # 检查行数
                if sheet_data.get("rows", 0) < rules["min_rows"]:
                    validation_result["errors"].append(
                        f"工作表 '{sheet_name}' 行数不足: {sheet_data.get('rows')} < {rules['min_rows']}"
                    )
                
                # 检查列名
                column_names = sheet_data.get("column_names", [])
                if not column_names:
                    validation_result["warnings"].append(f"工作表 '{sheet_name}' 没有列名")
                elif len(set(column_names)) != len(column_names):
                    validation_result["errors"].append(f"工作表 '{sheet_name}' 存在重复的列名")
            
        except Exception as e:
            validation_result["errors"].append(f"Excel验证异常: {str(e)}")
    
    async def _validate_csv(
        self, 
        parsed_data: Dict[str, Any], 
        validation_result: Dict[str, Any]
    ):
        """验证CSV模板"""
        try:
            rules = self.validation_rules["csv"]
            data = parsed_data.get("data", {})
            
            # 检查列数
            if data.get("columns", 0) < rules["min_columns"]:
                validation_result["errors"].append(
                    f"CSV文件列数不足: {data.get('columns')} < {rules['min_columns']}"
                )
            
            # 检查行数
            if data.get("rows", 0) < rules["min_rows"]:
                validation_result["errors"].append(
                    f"CSV文件行数不足: {data.get('rows')} < {rules['min_rows']}"
                )
            
            # 检查列名
            column_names = data.get("column_names", [])
            if not column_names:
                validation_result["warnings"].append("CSV文件没有列名")
            elif len(set(column_names)) != len(column_names):
                validation_result["errors"].append("CSV文件存在重复的列名")
            
        except Exception as e:
            validation_result["errors"].append(f"CSV验证异常: {str(e)}")
    
    async def _validate_json(
        self, 
        parsed_data: Dict[str, Any], 
        validation_result: Dict[str, Any]
    ):
        """验证JSON模板"""
        try:
            rules = self.validation_rules["json"]
            
            # 检查JSON结构
            if not parsed_data.get("data"):
                validation_result["errors"].append("JSON文件没有数据")
                return
            
            # 检查嵌套深度
            depth = self._calculate_json_depth(parsed_data["data"])
            if depth > rules["max_depth"]:
                validation_result["warnings"].append(
                    f"JSON嵌套深度较深: {depth} > {rules['max_depth']}"
                )
            
        except Exception as e:
            validation_result["errors"].append(f"JSON验证异常: {str(e)}")
    
    async def _validate_metadata_candidates(
        self, 
        parsed_data: Dict[str, Any], 
        validation_result: Dict[str, Any]
    ):
        """验证元数据候选字段"""
        try:
            # 获取元数据候选字段
            metadata_candidates = self._extract_metadata_candidates(parsed_data)
            
            # 检查是否有足够的元数据字段
            total_candidates = sum(len(candidates) for candidates in metadata_candidates.values())
            
            if total_candidates == 0:
                validation_result["warnings"].append("未识别到明显的元数据字段")
            else:
                validation_result["metadata"]["identified_fields"] = metadata_candidates
                validation_result["metadata"]["total_candidates"] = total_candidates
            
        except Exception as e:
            validation_result["warnings"].append(f"元数据字段验证异常: {str(e)}")
    
    def _extract_metadata_candidates(self, parsed_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """提取元数据候选字段"""
        try:
            if parsed_data.get("file_type") == "excel":
                # 合并所有工作表的候选字段
                all_candidates = {
                    "database_names": [],
                    "table_names": [],
                    "column_names": [],
                    "descriptions": [],
                    "data_types": []
                }
                
                for sheet_data in parsed_data.get("sheets", {}).values():
                    candidates = sheet_data.get("metadata_candidates", {})
                    for key, values in candidates.items():
                        if key in all_candidates:
                            all_candidates[key].extend(values)
                
                return all_candidates
            
            elif parsed_data.get("file_type") == "csv":
                return parsed_data.get("data", {}).get("metadata_candidates", {})
            
            elif parsed_data.get("file_type") == "json":
                return parsed_data.get("metadata", {}).get("metadata_candidates", {})
            
            return {}
            
        except Exception as e:
            logger.error(f"提取元数据候选字段失败: {e}")
            return {}
    
    def _calculate_json_depth(self, data: Any, current_depth: int = 0) -> int:
        """计算JSON嵌套深度"""
        try:
            if isinstance(data, dict):
                if not data:
                    return current_depth
                return max(
                    self._calculate_json_depth(value, current_depth + 1) 
                    for value in data.values()
                )
            elif isinstance(data, list):
                if not data:
                    return current_depth
                return max(
                    self._calculate_json_depth(item, current_depth + 1) 
                    for item in data
                )
            else:
                return current_depth
                
        except Exception as e:
            logger.error(f"计算JSON深度失败: {e}")
            return current_depth
    
    def update_validation_rules(
        self, 
        template_type: str, 
        rules: Dict[str, Any]
    ) -> bool:
        """
        更新验证规则
        
        Args:
            template_type: 模板类型
            rules: 新的验证规则
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if template_type in self.validation_rules:
                self.validation_rules[template_type].update(rules)
                logger.info(f"验证规则更新成功: template_type={template_type}")
                return True
            else:
                logger.warning(f"不支持的模板类型: {template_type}")
                return False
                
        except Exception as e:
            logger.error(f"更新验证规则失败: template_type={template_type}, error={e}")
            return False

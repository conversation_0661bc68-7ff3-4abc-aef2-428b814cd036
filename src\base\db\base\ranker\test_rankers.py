#!/usr/bin/env python3
"""
Ranker模块测试

测试RRF和加权平均排序器的功能和正确性。

作者: HSBC Knowledge Team
日期: 2025-01-14
"""

import sys
import os
import unittest
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

from base.db.base.ranker import BaseRanker, RRFRanker, WeightedRanker


class TestRankers(unittest.TestCase):
    """Ranker模块测试类"""
    
    def setUp(self):
        """设置测试数据"""
        # 模拟两个搜索结果列表
        self.search_results_1 = [
            {"id": 1, "distance": 0.1, "entity": {"content": "文档1"}},
            {"id": 2, "distance": 0.3, "entity": {"content": "文档2"}},
            {"id": 3, "distance": 0.5, "entity": {"content": "文档3"}},
        ]
        
        self.search_results_2 = [
            {"id": 2, "distance": 0.2, "entity": {"content": "文档2"}},
            {"id": 3, "distance": 0.4, "entity": {"content": "文档3"}},
            {"id": 4, "distance": 0.6, "entity": {"content": "文档4"}},
        ]
        
        self.search_results_3 = [
            {"id": 1, "distance": 0.15, "entity": {"content": "文档1"}},
            {"id": 4, "distance": 0.25, "entity": {"content": "文档4"}},
            {"id": 5, "distance": 0.35, "entity": {"content": "文档5"}},
        ]
        
        self.all_results = [
            self.search_results_1,
            self.search_results_2,
            self.search_results_3
        ]
    
    def test_rrf_ranker_basic(self):
        """测试RRF排序器基本功能"""
        print("\n🔍 测试RRF排序器基本功能")
        
        ranker = RRFRanker(k=60)
        results = ranker.rank(self.all_results, top_k=5)
        
        print(f"RRF排序结果 (k=60):")
        for i, result in enumerate(results, 1):
            rrf_details = result['entity']['rrf_details']
            print(f"  {i}. ID={result['id']}, RRF分数={rrf_details['total_rrf_score']:.4f}")
            for detail in rrf_details['search_details']:
                print(f"     搜索{detail['search_index'] + 1}: 排名{detail['rank']}, "
                      f"贡献{detail['rrf_contribution']:.4f}")
        
        # 验证结果
        self.assertGreater(len(results), 0)
        self.assertLessEqual(len(results), 5)
        
        # 验证RRF分数递减
        for i in range(len(results) - 1):
            self.assertGreaterEqual(
                results[i]['entity']['rrf_details']['total_rrf_score'],
                results[i + 1]['entity']['rrf_details']['total_rrf_score']
            )
        
        print("✅ RRF排序器基本功能测试通过")
    
    def test_rrf_ranker_different_k(self):
        """测试不同k值的RRF排序器"""
        print("\n🔍 测试不同k值的RRF排序器")
        
        k_values = [10, 60, 100]
        
        for k in k_values:
            ranker = RRFRanker(k=k)
            results = ranker.rank([self.search_results_1, self.search_results_2], top_k=3)
            
            print(f"k={k}时的前3个结果:")
            for i, result in enumerate(results, 1):
                rrf_score = result['entity']['rrf_details']['total_rrf_score']
                print(f"  {i}. ID={result['id']}, RRF分数={rrf_score:.4f}")
            
            self.assertEqual(len(results), 3)
        
        print("✅ 不同k值的RRF排序器测试通过")
    
    def test_weighted_ranker_basic(self):
        """测试加权排序器基本功能"""
        print("\n🔍 测试加权排序器基本功能")
        
        # 测试列表权重
        ranker = WeightedRanker([0.5, 0.3, 0.2])
        results = ranker.rank(self.all_results, top_k=5)
        
        print(f"加权平均排序结果 (权重=[0.5, 0.3, 0.2]):")
        for i, result in enumerate(results, 1):
            weighted_details = result['entity']['weighted_details']
            print(f"  {i}. ID={result['id']}, 加权分数={weighted_details['total_weighted_score']:.4f}")
            for detail in weighted_details['score_details']:
                print(f"     搜索{detail['search_index'] + 1}: 归一化分数{detail['normalized_score']:.4f} × "
                      f"权重{detail['weight']:.4f} = {detail['contribution']:.4f}")
        
        # 验证结果
        self.assertGreater(len(results), 0)
        self.assertLessEqual(len(results), 5)
        
        # 验证加权分数递减
        for i in range(len(results) - 1):
            self.assertGreaterEqual(
                results[i]['entity']['weighted_details']['total_weighted_score'],
                results[i + 1]['entity']['weighted_details']['total_weighted_score']
            )
        
        print("✅ 加权排序器基本功能测试通过")
    
    def test_weighted_ranker_dict_weights(self):
        """测试字典权重的加权排序器"""
        print("\n🔍 测试字典权重的加权排序器")
        
        # 测试字典权重
        ranker = WeightedRanker({0: 0.6, 1: 0.4})
        results = ranker.rank([self.search_results_1, self.search_results_2], top_k=3)
        
        print(f"加权平均排序结果 (权重={{0: 0.6, 1: 0.4}}):")
        for i, result in enumerate(results, 1):
            weighted_details = result['entity']['weighted_details']
            print(f"  {i}. ID={result['id']}, 加权分数={weighted_details['total_weighted_score']:.4f}")
        
        self.assertEqual(len(results), 3)
        print("✅ 字典权重的加权排序器测试通过")
    
    def test_ranker_comparison(self):
        """比较RRF和加权排序器的结果"""
        print("\n🔍 比较RRF和加权排序器的结果")
        
        # 使用相同的输入数据
        test_data = [self.search_results_1, self.search_results_2]
        
        # RRF排序
        rrf_ranker = RRFRanker(k=60)
        rrf_results = rrf_ranker.rank(test_data, top_k=4)
        
        # 加权排序
        weighted_ranker = WeightedRanker([0.6, 0.4])
        weighted_results = weighted_ranker.rank(test_data, top_k=4)
        
        print("RRF排序结果:")
        for i, result in enumerate(rrf_results, 1):
            rrf_score = result['entity']['rrf_details']['total_rrf_score']
            print(f"  {i}. ID={result['id']}, RRF分数={rrf_score:.4f}")
        
        print("\n加权平均排序结果:")
        for i, result in enumerate(weighted_results, 1):
            weighted_score = result['entity']['weighted_details']['total_weighted_score']
            print(f"  {i}. ID={result['id']}, 加权分数={weighted_score:.4f}")
        
        # 验证两种方法都返回了结果
        self.assertGreater(len(rrf_results), 0)
        self.assertGreater(len(weighted_results), 0)
        
        print("✅ Ranker比较测试通过")
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n🔍 测试错误处理")
        
        # 测试无效的k值
        with self.assertRaises(ValueError):
            RRFRanker(k=-1)
        
        # 测试空权重
        with self.assertRaises(ValueError):
            WeightedRanker([])
        
        # 测试负权重
        with self.assertRaises(ValueError):
            WeightedRanker([0.5, -0.3])
        
        # 测试空结果列表
        ranker = RRFRanker()
        with self.assertRaises(ValueError):
            ranker.rank([])
        
        print("✅ 错误处理测试通过")
    
    def test_single_search_result(self):
        """测试单个搜索结果的情况"""
        print("\n🔍 测试单个搜索结果的情况")
        
        # RRF排序
        rrf_ranker = RRFRanker(k=60)
        rrf_results = rrf_ranker.rank([self.search_results_1], top_k=2)
        
        print("单个搜索结果的RRF排序:")
        for i, result in enumerate(rrf_results, 1):
            rrf_score = result['entity']['rrf_details']['total_rrf_score']
            print(f"  {i}. ID={result['id']}, RRF分数={rrf_score:.4f}")
        
        # 加权排序
        weighted_ranker = WeightedRanker([1.0])
        weighted_results = weighted_ranker.rank([self.search_results_1], top_k=2)
        
        print("\n单个搜索结果的加权排序:")
        for i, result in enumerate(weighted_results, 1):
            weighted_score = result['entity']['weighted_details']['total_weighted_score']
            print(f"  {i}. ID={result['id']}, 加权分数={weighted_score:.4f}")
        
        self.assertEqual(len(rrf_results), 2)
        self.assertEqual(len(weighted_results), 2)
        
        print("✅ 单个搜索结果测试通过")


def main():
    """运行所有测试"""
    print("🚀 开始Ranker模块测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestRankers)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 所有Ranker测试通过！")
    else:
        print("❌ 部分Ranker测试失败")
        print(f"失败: {len(result.failures)}, 错误: {len(result.errors)}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

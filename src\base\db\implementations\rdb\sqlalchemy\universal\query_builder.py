

from typing import Dict, Any, List, Optional, Union, Sequence, Tuple
from dataclasses import dataclass, field
from enum import Enum

from .dialects.base import DatabaseDialect
from .exceptions import QueryError, UnsupportedOperationError


class JoinType(Enum):
    """SQL JOIN types"""
    INNER = "INNER JOIN"
    LEFT = "LEFT JOIN"
    RIGHT = "RIGHT JOIN"
    FULL = "FULL OUTER JOIN"
    CROSS = "CROSS JOIN"


class OrderDirection(Enum):
    """ORDER BY directions"""
    ASC = "ASC"
    DESC = "DESC"


@dataclass
class QueryCondition:
    """Represents a query condition"""
    column: str
    operator: str
    value: Any
    table_alias: Optional[str] = None
    
    def to_sql(self, dialect: DatabaseDialect, param_name: str = None) -> str:
        """Convert condition to SQL"""
        # Special handling for EXISTS and NOT EXISTS
        if self.operator.upper() in ["EXISTS", "NOT EXISTS"]:
            # For EXISTS, the value is the subquery
            return f"{self.operator} {self.value}"

        column_ref = f"{self.table_alias}.{dialect.quote_identifier(self.column)}" if self.table_alias else dialect.quote_identifier(self.column)

        if self.operator.upper() == "IN":
            if isinstance(self.value, (list, tuple)):
                # For SQLAlchemy, we use named parameters
                placeholders = ", ".join([f":{param_name}_{i}" for i in range(len(self.value))])
                return f"{column_ref} IN ({placeholders})"
            else:
                # Handle subquery case
                if isinstance(self.value, str) and self.value.startswith("(") and self.value.endswith(")"):
                    return f"{column_ref} IN {self.value}"
                else:
                    return f"{column_ref} IN (:{param_name})"
        elif self.operator.upper() == "NOT IN":
            if isinstance(self.value, str) and self.value.startswith("(") and self.value.endswith(")"):
                return f"{column_ref} NOT IN {self.value}"
            else:
                return f"{column_ref} NOT IN (:{param_name})"
        elif self.operator.upper() == "BETWEEN":
            return f"{column_ref} BETWEEN :{param_name}_start AND :{param_name}_end"
        elif self.operator.upper() == "IS NULL":
            return f"{column_ref} IS NULL"
        elif self.operator.upper() == "IS NOT NULL":
            return f"{column_ref} IS NOT NULL"
        elif self.operator.upper() == "LIKE":
            return f"{column_ref} LIKE :{param_name}"
        else:
            return f"{column_ref} {self.operator} :{param_name}"


@dataclass
class JoinClause:
    """Represents a JOIN clause"""
    join_type: JoinType
    table: str
    on_condition: str
    table_alias: Optional[str] = None
    
    def to_sql(self, dialect: DatabaseDialect) -> str:
        """Convert JOIN to SQL"""
        table_ref = f"{dialect.quote_identifier(self.table)}"
        if self.table_alias:
            table_ref += f" AS {dialect.quote_identifier(self.table_alias)}"
        
        return f"{self.join_type.value} {table_ref} ON {self.on_condition}"


class UniversalQueryBuilder:
    """Universal query builder that adapts to different SQL dialects"""
    
    def __init__(self, dialect: DatabaseDialect):
        self.dialect = dialect
        self.reset()
    
    def reset(self):
        """Reset the query builder to initial state"""
        self._select_columns: List[str] = []
        self._from_table: Optional[str] = None
        self._table_alias: Optional[str] = None
        self._joins: List[JoinClause] = []
        self._where_conditions: List[QueryCondition] = []
        self._group_by_columns: List[str] = []
        self._having_conditions: List[QueryCondition] = []
        self._order_by_columns: List[tuple] = []  # (column, direction)
        self._limit_value: Optional[int] = None
        self._offset_value: Optional[int] = None
        self._distinct: bool = False
        self._parameters: Dict[str, Any] = {}
        self._parameter_counter: int = 0
    
    # ==================== SELECT Clause ====================
    
    def select(self, *columns: str) -> 'UniversalQueryBuilder':
        """
        Add columns to SELECT clause
        
        Args:
            *columns: Column names or expressions
        
        Returns:
            Self for method chaining
        """
        self._select_columns.extend(columns)
        return self
    
    def select_distinct(self, *columns: str) -> 'UniversalQueryBuilder':
        """
        Add DISTINCT columns to SELECT clause
        
        Args:
            *columns: Column names or expressions
        
        Returns:
            Self for method chaining
        """
        self._distinct = True
        self._select_columns.extend(columns)
        return self
    
    def select_all(self) -> 'UniversalQueryBuilder':
        """
        Select all columns (*)

        Returns:
            Self for method chaining
        """
        self._select_columns = ["*"]
        return self

    def distinct(self) -> 'UniversalQueryBuilder':
        """
        Add DISTINCT to the query

        Returns:
            Self for method chaining
        """
        self._distinct = True
        return self
    
    # ==================== FROM Clause ====================
    
    def from_table(self, table: str, alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """
        Set FROM table
        
        Args:
            table: Table name
            alias: Table alias
        
        Returns:
            Self for method chaining
        """
        self._from_table = table
        self._table_alias = alias
        return self
    
    # ==================== JOIN Clauses ====================
    
    def join(self, table: str, on_condition: str, alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """Add INNER JOIN"""
        return self._add_join(JoinType.INNER, table, on_condition, alias)
    
    def left_join(self, table: str, on_condition: str, alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """Add LEFT JOIN"""
        return self._add_join(JoinType.LEFT, table, on_condition, alias)
    
    def right_join(self, table: str, on_condition: str, alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """Add RIGHT JOIN"""
        return self._add_join(JoinType.RIGHT, table, on_condition, alias)
    
    def full_join(self, table: str, on_condition: str, alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """Add FULL OUTER JOIN"""
        return self._add_join(JoinType.FULL, table, on_condition, alias)
    
    def cross_join(self, table: str, alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """Add CROSS JOIN"""
        return self._add_join(JoinType.CROSS, table, "1=1", alias)  # CROSS JOIN doesn't need ON condition
    
    def _add_join(self, join_type: JoinType, table: str, on_condition: str, alias: Optional[str]) -> 'UniversalQueryBuilder':
        """Internal method to add JOIN clause"""
        join_clause = JoinClause(join_type, table, on_condition, alias)
        self._joins.append(join_clause)
        return self
    
    # ==================== WHERE Clause ====================
    
    def where(self, column: str, operator: str, value: Any, table_alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """
        Add WHERE condition
        
        Args:
            column: Column name
            operator: Comparison operator (=, !=, >, <, >=, <=, LIKE, IN, etc.)
            value: Value to compare
            table_alias: Table alias for the column
        
        Returns:
            Self for method chaining
        """
        condition = QueryCondition(column, operator, value, table_alias)
        self._where_conditions.append(condition)
        return self
    
    def where_in(self, column: str, values: Sequence[Any], table_alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """Add WHERE column IN (values) condition"""
        return self.where(column, "IN", list(values), table_alias)
    
    def where_between(self, column: str, start: Any, end: Any, table_alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """Add WHERE column BETWEEN start AND end condition"""
        return self.where(column, "BETWEEN", [start, end], table_alias)
    
    def where_like(self, column: str, pattern: str, table_alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """Add WHERE column LIKE pattern condition"""
        return self.where(column, "LIKE", pattern, table_alias)
    
    def where_null(self, column: str, table_alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """Add WHERE column IS NULL condition"""
        return self.where(column, "IS NULL", None, table_alias)
    
    def where_not_null(self, column: str, table_alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """Add WHERE column IS NOT NULL condition"""
        return self.where(column, "IS NOT NULL", None, table_alias)
    
    # ==================== GROUP BY Clause ====================
    
    def group_by(self, *columns: str) -> 'UniversalQueryBuilder':
        """
        Add GROUP BY columns
        
        Args:
            *columns: Column names
        
        Returns:
            Self for method chaining
        """
        self._group_by_columns.extend(columns)
        return self
    
    # ==================== HAVING Clause ====================
    
    def having(self, column: str, operator: str, value: Any, table_alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """
        Add HAVING condition
        
        Args:
            column: Column name (usually an aggregate function)
            operator: Comparison operator
            value: Value to compare
            table_alias: Table alias for the column
        
        Returns:
            Self for method chaining
        """
        condition = QueryCondition(column, operator, value, table_alias)
        self._having_conditions.append(condition)
        return self
    
    # ==================== ORDER BY Clause ====================
    
    def order_by(self, column: str, direction: Union[OrderDirection, str] = OrderDirection.ASC) -> 'UniversalQueryBuilder':
        """
        Add ORDER BY column
        
        Args:
            column: Column name
            direction: Sort direction (ASC or DESC)
        
        Returns:
            Self for method chaining
        """
        if isinstance(direction, str):
            direction = OrderDirection(direction.upper())
        
        self._order_by_columns.append((column, direction))
        return self
    
    def order_by_desc(self, column: str) -> 'UniversalQueryBuilder':
        """Add ORDER BY column DESC"""
        return self.order_by(column, OrderDirection.DESC)
    
    # ==================== LIMIT and OFFSET ====================
    
    def limit(self, count: int) -> 'UniversalQueryBuilder':
        """
        Set LIMIT
        
        Args:
            count: Maximum number of rows
        
        Returns:
            Self for method chaining
        """
        self._limit_value = count
        return self
    
    def offset(self, count: int) -> 'UniversalQueryBuilder':
        """
        Set OFFSET
        
        Args:
            count: Number of rows to skip
        
        Returns:
            Self for method chaining
        """
        self._offset_value = count
        return self
    
    def paginate(self, page: int, per_page: int) -> 'UniversalQueryBuilder':
        """
        Add pagination (LIMIT and OFFSET)
        
        Args:
            page: Page number (1-based)
            per_page: Items per page
        
        Returns:
            Self for method chaining
        """
        if page < 1:
            raise QueryError("Page number must be >= 1")
        
        offset = (page - 1) * per_page
        return self.limit(per_page).offset(offset)
    
    # ==================== SQL Generation ====================
    
    def build_sql(self) -> Tuple[str, Dict[str, Any]]:
        """
        Build the final SQL query and parameters
        
        Returns:
            Tuple of (SQL string, parameters dict)
        """
        if not self._from_table:
            raise QueryError("FROM table is required")
        
        sql_parts = []
        
        # SELECT clause
        select_clause = "SELECT "
        if self._distinct:
            select_clause += "DISTINCT "
        
        if self._select_columns:
            columns = ", ".join([self.dialect.quote_identifier(col) if col != "*" else col for col in self._select_columns])
        else:
            columns = "*"
        
        select_clause += columns
        sql_parts.append(select_clause)
        
        # FROM clause
        from_clause = f"FROM {self.dialect.quote_identifier(self._from_table)}"
        if self._table_alias:
            from_clause += f" AS {self.dialect.quote_identifier(self._table_alias)}"
        sql_parts.append(from_clause)
        
        # JOIN clauses
        for join in self._joins:
            sql_parts.append(join.to_sql(self.dialect))
        
        # WHERE clause
        if self._where_conditions:
            where_parts = []
            for i, condition in enumerate(self._where_conditions):
                param_name = f"where_{i}"
                where_parts.append(condition.to_sql(self.dialect, param_name))
            sql_parts.append(f"WHERE {' AND '.join(where_parts)}")
        
        # GROUP BY clause
        if self._group_by_columns:
            group_columns = ", ".join([self.dialect.quote_identifier(col) for col in self._group_by_columns])
            sql_parts.append(f"GROUP BY {group_columns}")
        
        # HAVING clause
        if self._having_conditions:
            having_parts = []
            for i, condition in enumerate(self._having_conditions):
                param_name = f"having_{i}"
                having_parts.append(condition.to_sql(self.dialect, param_name))
            sql_parts.append(f"HAVING {' AND '.join(having_parts)}")
        
        # ORDER BY clause
        if self._order_by_columns:
            order_parts = []
            for column, direction in self._order_by_columns:
                quoted_column = self.dialect.quote_identifier(column)
                order_parts.append(f"{quoted_column} {direction.value}")
            sql_parts.append(f"ORDER BY {', '.join(order_parts)}")
        
        # LIMIT and OFFSET
        if self._limit_value is not None:
            limit_clause = self.dialect.build_limit_clause(self._limit_value, self._offset_value)
            sql_parts.append(limit_clause)
        
        sql = " ".join(sql_parts)
        
        # Collect parameters
        parameters = {}

        # WHERE parameters
        for i, condition in enumerate(self._where_conditions):
            if condition.value is not None and condition.operator.upper() not in ["IS NULL", "IS NOT NULL"]:
                param_base = f"where_{i}"
                if condition.operator.upper() == "IN":
                    if isinstance(condition.value, (list, tuple)):
                        for j, val in enumerate(condition.value):
                            parameters[f"{param_base}_{j}"] = val
                    else:
                        parameters[param_base] = condition.value
                elif condition.operator.upper() == "BETWEEN":
                    if isinstance(condition.value, (list, tuple)) and len(condition.value) == 2:
                        parameters[f"{param_base}_start"] = condition.value[0]
                        parameters[f"{param_base}_end"] = condition.value[1]
                else:
                    parameters[param_base] = condition.value

        # HAVING parameters
        for i, condition in enumerate(self._having_conditions):
            if condition.value is not None and condition.operator.upper() not in ["IS NULL", "IS NOT NULL"]:
                param_base = f"having_{i}"
                if condition.operator.upper() == "IN":
                    if isinstance(condition.value, (list, tuple)):
                        for j, val in enumerate(condition.value):
                            parameters[f"{param_base}_{j}"] = val
                    else:
                        parameters[param_base] = condition.value
                elif condition.operator.upper() == "BETWEEN":
                    if isinstance(condition.value, (list, tuple)) and len(condition.value) == 2:
                        parameters[f"{param_base}_start"] = condition.value[0]
                        parameters[f"{param_base}_end"] = condition.value[1]
                else:
                    parameters[param_base] = condition.value
        
        return sql, parameters

    def build(self) -> 'QueryRequest':
        """
        Build QueryRequest object from current query builder state
        
        Returns:
            QueryRequest object that can be used with RDB interface
        """
        from .....base.rdb import (
            QueryRequest, QueryFilter, QueryFilterGroup, QuerySort,
            ComparisonOperator, LogicalOperator, SortOrder
        )
        
        # Convert WHERE conditions to QueryFilters
        filters = []
        for condition in self._where_conditions:
            # Map operator strings to ComparisonOperator enums
            operator_map = {
                "=": ComparisonOperator.EQ,
                "!=": ComparisonOperator.NE,
                ">": ComparisonOperator.GT,
                ">=": ComparisonOperator.GTE,
                "<": ComparisonOperator.LT,
                "<=": ComparisonOperator.LTE,
                "IN": ComparisonOperator.IN,
                "NOT IN": ComparisonOperator.NOT_IN,
                "LIKE": ComparisonOperator.LIKE,
                "BETWEEN": ComparisonOperator.BETWEEN,
                "IS NULL": ComparisonOperator.IS_NULL,
                "IS NOT NULL": ComparisonOperator.IS_NOT_NULL,
            }
            
            operator = operator_map.get(condition.operator.upper(), ComparisonOperator.EQ)
            filter_obj = QueryFilter(
                field=condition.column,
                operator=operator,
                value=condition.value
            )
            filters.append(filter_obj)
        
        # Create filter group if we have filters
        filter_group = None
        if filters:
            filter_group = QueryFilterGroup(
                operator=LogicalOperator.AND,
                filters=filters
            )
        
        # Convert ORDER BY to QuerySort objects
        sorts = []
        for column, direction in self._order_by_columns:
            sort_order = SortOrder.DESC if direction.value.upper() == "DESC" else SortOrder.ASC
            sort_obj = QuerySort(field=column, order=sort_order)
            sorts.append(sort_obj)
        
        # Determine columns
        columns = None
        if self._select_columns and "*" not in self._select_columns:
            columns = self._select_columns
        
        return QueryRequest(
            table=self._from_table,
            columns=columns,
            filters=filter_group,
            sorts=sorts if sorts else None,
            limit=self._limit_value,
            offset=self._offset_value
        )
    
    def __str__(self) -> str:
        """String representation of the query"""
        try:
            sql, params = self.build_sql()
            return f"SQL: {sql}\nParameters: {params}"
        except Exception as e:
            return f"Invalid query: {e}"

    # ==================== CASE WHEN Support ====================

    def case_when(self, conditions: List[tuple], else_value: Any = None, alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """
        Add CASE WHEN expression to SELECT

        Args:
            conditions: List of (condition, value) tuples
            else_value: ELSE value (optional)
            alias: Column alias for the CASE expression

        Returns:
            Self for method chaining

        Example:
            builder.case_when([
                ("status = 'active'", 1),
                ("status = 'inactive'", 0)
            ], else_value=-1, alias="status_code")
        """
        case_parts = ["CASE"]

        for condition, value in conditions:
            if isinstance(value, str):
                case_parts.append(f"WHEN {condition} THEN '{value}'")
            else:
                case_parts.append(f"WHEN {condition} THEN {value}")

        if else_value is not None:
            if isinstance(else_value, str):
                case_parts.append(f"ELSE '{else_value}'")
            else:
                case_parts.append(f"ELSE {else_value}")

        case_parts.append("END")
        case_expression = " ".join(case_parts)

        if alias:
            case_expression += f" AS {self.dialect.quote_identifier(alias)}"

        self._select_columns.append(case_expression)
        return self

    # ==================== Subquery Support ====================

    def where_exists(self, subquery: str) -> 'UniversalQueryBuilder':
        """
        Add EXISTS subquery condition

        Args:
            subquery: Subquery SQL string

        Returns:
            Self for method chaining
        """
        condition = QueryCondition("", "EXISTS", f"({subquery})", None)
        self._where_conditions.append(condition)
        return self

    def where_not_exists(self, subquery: str) -> 'UniversalQueryBuilder':
        """
        Add NOT EXISTS subquery condition

        Args:
            subquery: Subquery SQL string

        Returns:
            Self for method chaining
        """
        condition = QueryCondition("", "NOT EXISTS", f"({subquery})", None)
        self._where_conditions.append(condition)
        return self

    def where_in_subquery(self, column: str, subquery: str, table_alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """
        Add IN subquery condition

        Args:
            column: Column name
            subquery: Subquery SQL string
            table_alias: Optional table alias

        Returns:
            Self for method chaining
        """
        condition = QueryCondition(column, "IN", f"({subquery})", table_alias)
        self._where_conditions.append(condition)
        return self

    def where_not_in_subquery(self, column: str, subquery: str, table_alias: Optional[str] = None) -> 'UniversalQueryBuilder':
        """
        Add NOT IN subquery condition

        Args:
            column: Column name
            subquery: Subquery SQL string
            table_alias: Optional table alias

        Returns:
            Self for method chaining
        """
        condition = QueryCondition(column, "NOT IN", f"({subquery})", table_alias)
        self._where_conditions.append(condition)
        return self

"""
Metadata系统CRUD操作模块

提供模块化的CRUD操作功能：
- 基础模块：crud_base.py
- 核心数据库/表/字段模块：crud_meta.py
- 数据主题和关联模块：crud_subjects.py
- 码值和关联模块：crud_codes.py
- 关联关系模块：crud_relations.py
"""

from .crud_base import MetadataCrudBase
from .crud_meta import MetadataCrudMeta
from .crud_subjects import MetadataCrudSubjects
from .crud_codes import MetadataCrudCodes
from .crud_relations import MetadataCrudRelations

__all__ = [
    'MetadataCrudBase',
    'MetadataCrudMeta',
    'MetadataCrudSubjects',
    'MetadataCrudCodes',
    'MetadataCrudRelations'
]

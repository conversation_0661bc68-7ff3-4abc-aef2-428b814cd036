#!/usr/bin/env python3
"""
数据库操作改进简化测试

直接测试database_operations.py中的改进，避免复杂的模块依赖
"""

import asyncio
import time
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入相关模块
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

from service import get_client
from modules.knowledge.dd.crud import DDCrud


async def test_dd_crud_methods():
    """测试DDCrud方法是否可用"""
    print("🔧 测试DDCrud方法可用性")
    print("-" * 50)
    
    try:
        # 获取客户端
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = None
        embedding_client = None
        
        try:
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except:
            pass
        
        # 创建DDCrud实例
        dd_crud = DDCrud(rdb_client, vdb_client, embedding_client)
        logger.info("✅ 创建DDCrud实例成功")
        
        # 测试分发后数据查询
        logger.info("测试list_post_distributions方法...")
        try:
            records = await dd_crud.list_post_distributions(limit=1)
            logger.info(f"✅ list_post_distributions成功: 返回{len(records)}条记录")
        except Exception as e:
            logger.error(f"❌ list_post_distributions失败: {e}")
        
        # 测试分发前数据查询
        logger.info("测试list_pre_distributions方法...")
        try:
            records = await dd_crud.list_pre_distributions(limit=1)
            logger.info(f"✅ list_pre_distributions成功: 返回{len(records)}条记录")
        except Exception as e:
            logger.error(f"❌ list_pre_distributions失败: {e}")
        
        # 测试创建分发后数据
        logger.info("测试create_post_distribution方法...")
        try:
            timestamp = int(time.time())
            test_data = {
                'pre_distribution_id': 14091,  # 使用真实存在的ID
                'submission_id': f'DD_CRUD_TEST_{timestamp}',
                'submission_type': 'SUBMISSION',
                'report_type': 'detail',
                'set': 'standard',
                'version': 'v1.0',
                'dept_id': '114',  # 使用真实存在的部门ID
                'dr01': 'ADS',
                'dr07': f'dd_crud_test_{timestamp}',
                'dr22': '114',  # 使用真实存在的部门ID
                'bdr01': '114',  # 使用真实存在的部门ID
                'bdr03': 'DDCrud方法测试'
            }
            
            result = await dd_crud.create_post_distribution(test_data)
            logger.info(f"✅ create_post_distribution成功: ID={result}")
            
            # 测试更新
            logger.info("测试update_post_distributions方法...")
            update_data = {'bdr03': f'更新测试_{timestamp}'}
            update_result = await dd_crud.update_post_distributions(
                update_data,
                conditions={'submission_id': test_data['submission_id']}
            )
            logger.info(f"✅ update_post_distributions成功: {update_result}")
            
        except Exception as e:
            logger.error(f"❌ create_post_distribution失败: {e}")
        
        print("\n✅ DDCrud方法测试完成")
        return True
        
    except Exception as e:
        logger.error(f"DDCrud方法测试失败: {e}")
        return False


async def test_database_operations_import():
    """测试database_operations模块导入"""
    print("\n🔧 测试database_operations模块导入")
    print("-" * 50)
    
    try:
        # 直接导入database_operations模块
        from modules.dd_submission.department_assignment.core.database_operations import PostDistributionDBOperations, DatabaseOperationError
        logger.info("✅ 成功导入PostDistributionDBOperations")
        
        # 获取客户端
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = None
        
        try:
            vdb_client = await get_client("database.vdbs.pgvector")
        except:
            pass
        
        # 创建实例
        db_ops = PostDistributionDBOperations(rdb_client, vdb_client)
        logger.info("✅ 成功创建PostDistributionDBOperations实例")
        
        # 测试基本方法
        logger.info("测试get_pre_distribution_data方法...")
        try:
            result = await db_ops.get_pre_distribution_data('test_report_code')
            logger.info(f"✅ get_pre_distribution_data成功: 返回{len(result)}条记录")
        except Exception as e:
            logger.error(f"❌ get_pre_distribution_data失败: {e}")
        
        # 测试统计方法
        logger.info("测试get_batch_processing_stats方法...")
        try:
            stats = await db_ops.get_batch_processing_stats('test_dr07', 'v1.0')
            logger.info(f"✅ get_batch_processing_stats成功: {stats}")
        except Exception as e:
            logger.error(f"❌ get_batch_processing_stats失败: {e}")
        
        print("\n✅ database_operations模块测试完成")
        return True
        
    except Exception as e:
        logger.error(f"database_operations模块测试失败: {e}")
        return False


async def test_crud_consistency():
    """测试CRUD操作一致性"""
    print("\n🔧 测试CRUD操作一致性")
    print("-" * 50)
    
    try:
        from modules.dd_submission.department_assignment.core.database_operations import PostDistributionDBOperations
        
        # 获取客户端
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = None
        
        try:
            vdb_client = await get_client("database.vdbs.pgvector")
        except:
            pass
        
        # 创建实例
        db_ops = PostDistributionDBOperations(rdb_client, vdb_client)
        
        timestamp = int(time.time())
        
        # 测试插入操作
        logger.info("测试插入操作...")
        test_record = {
            'pre_distribution_id': 14091,  # 使用真实存在的ID
            'submission_id': f'CONSISTENCY_TEST_{timestamp}',
            'submission_type': 'SUBMISSION',
            'report_type': 'detail',
            'set': 'standard',
            'version': 'v1.0',
            'dept_id': '114',  # 使用真实存在的部门ID
            'dr01': 'ADS',
            'dr07': f'consistency_test_{timestamp}',
            'dr22': '114',  # 使用真实存在的部门ID
            'bdr01': '114',  # 使用真实存在的部门ID
            'bdr03': '一致性测试数据'
        }
        
        try:
            await db_ops._insert_new_record(test_record)
            logger.info(f"✅ 插入操作成功: {test_record['submission_id']}")
        except Exception as e:
            logger.error(f"❌ 插入操作失败: {e}")
        
        # 测试查询操作
        logger.info("测试查询操作...")
        try:
            existing_record = await db_ops._check_existing_record(
                test_record['submission_id'],
                test_record['dr07'],
                test_record['version']
            )
            
            if existing_record:
                logger.info(f"✅ 查询操作成功: 找到记录")
            else:
                logger.warning("⚠️  查询操作未找到记录")
        except Exception as e:
            logger.error(f"❌ 查询操作失败: {e}")
        
        # 测试更新操作
        logger.info("测试更新操作...")
        try:
            update_record = test_record.copy()
            update_record['bdr03'] = f'更新后的一致性测试数据_{timestamp}'
            
            await db_ops._update_existing_record(update_record)
            logger.info(f"✅ 更新操作成功")
        except Exception as e:
            logger.error(f"❌ 更新操作失败: {e}")
        
        print("\n✅ CRUD操作一致性测试完成")
        return True
        
    except Exception as e:
        logger.error(f"CRUD操作一致性测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 数据库操作改进简化测试")
    print("=" * 80)
    
    test_functions = [
        ("DDCrud方法可用性", test_dd_crud_methods),
        ("database_operations模块导入", test_database_operations_import),
        ("CRUD操作一致性", test_crud_consistency)
    ]
    
    all_passed = True
    results = {}
    
    for test_name, test_func in test_functions:
        try:
            result = await test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name}测试通过")
            else:
                logger.error(f"❌ {test_name}测试失败")
                all_passed = False
                
        except Exception as e:
            logger.error(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
            all_passed = False
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("📊 数据库操作改进简化测试结果汇总")
    print("=" * 80)
    
    passed_count = 0
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n🎯 总体结果: {passed_count}/{total_count} 测试通过")
    
    if all_passed:
        print("\n🎉 所有数据库操作改进简化测试通过！")
        return True
    else:
        print("\n⚠️  部分数据库操作改进简化测试失败，需要检查")
        return False


if __name__ == "__main__":
    asyncio.run(main())

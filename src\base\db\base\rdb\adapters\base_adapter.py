"""
基础适配器

定义适配器的基础功能和通用逻辑

设计原则：
1. 类型安全 - 确保转换后的数据类型正确
2. 错误处理 - 优雅处理转换失败的情况
3. 扩展性 - 支持不同数据库的特殊类型
4. 性能优化 - 避免不必要的转换操作
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, date, time
import decimal
import json

from ..core.types import DatabaseValue, DatabaseRecord, DatabaseType
from ..core.exceptions import RDBError, DataError


class BaseAdapter(ABC):
    """适配器基类
    
    提供数据库特定数据到统一格式的转换功能
    """
    
    def __init__(self, database_type: DatabaseType):
        self.database_type = database_type
    
    @abstractmethod
    def get_supported_types(self) -> List[type]:
        """获取支持的数据类型"""
        pass
    
    def adapt_value(self, value: Any) -> DatabaseValue:
        """适配单个值
        
        将数据库特定的值转换为标准的DatabaseValue类型
        """
        if value is None:
            return None
        
        # 基础类型直接返回
        if isinstance(value, (bool, int, float, str, bytes)):
            return value
        
        # 日期时间类型
        if isinstance(value, (datetime, date, time)):
            return value
        
        # 数值类型
        if isinstance(value, decimal.Decimal):
            return value
        
        # 列表和字典（JSON类型）
        if isinstance(value, (list, dict)):
            return value
        
        # 尝试转换为字符串
        try:
            return str(value)
        except Exception as e:
            raise DataError(
                f"Cannot adapt value of type {type(value)}: {value}",
                original_error=e,
                database_type=self.database_type
            )
    
    def adapt_record(self, record: Any) -> DatabaseRecord:
        """适配单条记录
        
        将数据库特定的记录格式转换为标准的字典格式
        """
        if record is None:
            return {}
        
        # 如果已经是字典，适配每个值
        if isinstance(record, dict):
            return {
                key: self.adapt_value(value)
                for key, value in record.items()
            }
        
        # 如果是命名元组或类似对象
        if hasattr(record, '_asdict'):
            return {
                key: self.adapt_value(value)
                for key, value in record._asdict().items()
            }
        
        # 如果有__dict__属性
        if hasattr(record, '__dict__'):
            return {
                key: self.adapt_value(value)
                for key, value in record.__dict__.items()
                if not key.startswith('_')
            }
        
        # 如果是序列类型，尝试转换为字典
        if hasattr(record, '__iter__') and not isinstance(record, (str, bytes)):
            try:
                items = list(record)
                if len(items) == 2 and isinstance(items[0], str):
                    # 假设是(key, value)对
                    return {items[0]: self.adapt_value(items[1])}
                else:
                    # 假设是值的序列，使用索引作为键
                    return {
                        str(i): self.adapt_value(value)
                        for i, value in enumerate(items)
                    }
            except Exception:
                pass
        
        # 最后尝试转换为字符串
        try:
            return {"value": self.adapt_value(record)}
        except Exception as e:
            raise DataError(
                f"Cannot adapt record of type {type(record)}: {record}",
                original_error=e,
                database_type=self.database_type
            )
    
    def adapt_records(self, records: Any) -> List[DatabaseRecord]:
        """适配多条记录"""
        if records is None:
            return []
        
        if not hasattr(records, '__iter__'):
            return [self.adapt_record(records)]
        
        return [self.adapt_record(record) for record in records]
    
    def extract_column_names(self, cursor_description: Any) -> List[str]:
        """从游标描述中提取列名
        
        不同数据库驱动的cursor.description格式可能不同
        """
        if not cursor_description:
            return []
        
        column_names = []
        for column_info in cursor_description:
            if isinstance(column_info, (tuple, list)) and len(column_info) > 0:
                # 大多数驱动：(name, type, display_size, internal_size, precision, scale, null_ok)
                column_names.append(str(column_info[0]))
            elif hasattr(column_info, 'name'):
                # 某些驱动有name属性
                column_names.append(str(column_info.name))
            else:
                # fallback
                column_names.append(str(column_info))
        
        return column_names
    
    def create_record_from_row(self, row: Any, column_names: List[str]) -> DatabaseRecord:
        """从行数据和列名创建记录"""
        if not column_names:
            return self.adapt_record(row)
        
        if hasattr(row, '__iter__') and not isinstance(row, (str, bytes, dict)):
            # 行是序列类型
            row_values = list(row)
            if len(row_values) != len(column_names):
                # 长度不匹配，使用适配记录的默认逻辑
                return self.adapt_record(row)
            
            return {
                column_names[i]: self.adapt_value(value)
                for i, value in enumerate(row_values)
            }
        else:
            # 行不是序列，使用默认适配
            return self.adapt_record(row)
    
    def normalize_sql_identifier(self, identifier: str) -> str:
        """标准化SQL标识符
        
        移除引号，转换为小写等
        """
        if not identifier:
            return identifier
        
        # 移除常见的引号
        identifier = identifier.strip()
        for quote in ['"', "'", '`', '[', ']']:
            identifier = identifier.strip(quote)
        
        return identifier.lower()
    
    def format_error_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """格式化错误上下文信息"""
        formatted_context = {}
        
        for key, value in context.items():
            try:
                # 尝试序列化值以确保可以安全存储
                if isinstance(value, (str, int, float, bool, type(None))):
                    formatted_context[key] = value
                elif isinstance(value, (datetime, date, time)):
                    formatted_context[key] = value.isoformat()
                elif isinstance(value, (list, dict)):
                    formatted_context[key] = json.dumps(value, default=str)
                else:
                    formatted_context[key] = str(value)
            except Exception:
                formatted_context[key] = f"<{type(value).__name__}>"
        
        return formatted_context
    
    def validate_adapted_value(self, value: DatabaseValue) -> bool:
        """验证适配后的值是否有效"""
        # 检查值是否是支持的类型
        if value is None:
            return True
        
        if isinstance(value, (bool, int, float, str, bytes)):
            return True
        
        if isinstance(value, (datetime, date, time, decimal.Decimal)):
            return True
        
        if isinstance(value, (list, dict)):
            return True
        
        return False
    
    def get_type_mapping(self) -> Dict[str, type]:
        """获取数据库类型到Python类型的映射
        
        子类可以重写此方法提供特定的类型映射
        """
        return {
            'varchar': str,
            'char': str,
            'text': str,
            'int': int,
            'integer': int,
            'bigint': int,
            'smallint': int,
            'float': float,
            'double': float,
            'decimal': decimal.Decimal,
            'numeric': decimal.Decimal,
            'boolean': bool,
            'bool': bool,
            'date': date,
            'time': time,
            'datetime': datetime,
            'timestamp': datetime,
            'json': dict,
            'jsonb': dict,
            'blob': bytes,
            'binary': bytes,
        }
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(database_type={self.database_type})"

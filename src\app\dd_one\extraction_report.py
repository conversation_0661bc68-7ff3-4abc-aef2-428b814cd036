import json

from doc_parse.document_parse_interface import DocumentParserInterface
from doc_parse.parses.simple_parse import SimpleExcelParser

# 创建接口实例
interface = DocumentParserInterface()


def get_report_data(file_path: str, csv_path: str, type: str):
    # 根据不同类型，选择不同的解析器
    if type == '1104':
        interface.register_parser("simple", SimpleExcelParser)
        result = interface.extraction_report("simple", csv_path, file_path, '')
    elif type == 'djz':
        # todo 大集中的解析方式
        result = {}
    elif type == 'survey':
        # todo  survey的解析方式
        result = {}
    else:
        raise ValueError("Invalid type")
    return result


def get_report_data_list(data_dict: dict) -> str:
    # Use enumerate for efficient ID assignment and list comprehension for merging
    merged_list = [
        {**item, "entry_id": idx + 1}
        for key in data_dict
        for idx, item in enumerate(data_dict[key])
    ]

    # Serialize to JSON with proper encoding
    return json.dumps(merged_list, ensure_ascii=False)


if __name__ == '__main__':
    file_path = '/data/ideal/code/hr_code/hsbc_1_data/test_file/G1402/2、G14_II填报说明-mk-1119.doc'
    csv_path = '/data/ideal/code/hr_code/hsbc_1_data/test_file/G1402/G14_II_template.xls'
    print(get_report_data_list(get_report_data(file_path, csv_path, '1104')))

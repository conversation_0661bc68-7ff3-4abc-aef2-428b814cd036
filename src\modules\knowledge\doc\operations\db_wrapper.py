"""
数据库操作包装器 - 仅适配UniversalSQLAlchemyClient

提供统一的数据库操作接口，仅支持UniversalSQLAlchemyClient
支持同步和异步操作
"""

import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class DatabaseOperationWrapper:
    """数据库操作包装器 - 仅适配UniversalSQLAlchemyClient，支持同步和异步操作"""
    
    def __init__(self, client):
        self.client = client
    
    # ==========================================
    # 同步方法
    # ==========================================
    
    def insert(self, table: str, data: List[Dict[str, Any]]) -> bool:
        """插入接口（同步，仅UniversalSQLAlchemyClient）"""
        try:
            from base.db.base.rdb import InsertRequest
            request = InsertRequest(table=table, data=data)
            response = self.client.insert(request)
            return response.success if hasattr(response, 'success') else True
        except Exception as e:
            logger.error(f"数据库插入失败: {e}")
            return False
    
    def select(self, table: str, condition=None, limit=None, offset=None, order_by=None, columns=None) -> List[Dict[str, Any]]:
        """查询接口（同步，仅UniversalSQLAlchemyClient）"""
        try:
            from base.db.base.rdb import QueryRequest, QueryFilter, ComparisonOperator, QueryFilterGroup, LogicalOperator, QuerySort, SortOrder
            # 构建过滤器
            filters = None
            if condition:
                filter_list = []
                for key, value in condition.items():
                    filter_obj = QueryFilter(field=key, operator=ComparisonOperator.EQ, value=value)
                    filter_list.append(filter_obj)
                if len(filter_list) == 1:
                    filters = filter_list[0]
                else:
                    filters = QueryFilterGroup(operator=LogicalOperator.AND, filters=filter_list)
            # 构建排序
            sorts = None
            if order_by:
                sort_list = []
                if isinstance(order_by, str):
                    order_by = [order_by]
                for sort_str in order_by:
                    if ' DESC' in sort_str.upper():
                        field = sort_str.replace(' DESC', '').replace(' desc', '').strip()
                        sort_list.append(QuerySort(field=field, order=SortOrder.DESC))
                    else:
                        field = sort_str.replace(' ASC', '').replace(' asc', '').strip()
                        sort_list.append(QuerySort(field=field, order=SortOrder.ASC))
                sorts = sort_list
            request = QueryRequest(
                table=table,
                columns=columns,
                filters=filters,
                sorts=sorts,
                limit=limit,
                offset=offset
            )
            response = self.client.query(request)
            return response.data if hasattr(response, 'data') else []
        except Exception as e:
            logger.error(f"数据库查询失败: {e}")
            return []
    
    def update(self, table: str, filter: Dict[str, Any], data: Dict[str, Any]) -> bool:
        """更新接口（同步，仅UniversalSQLAlchemyClient）"""
        try:
            from base.db.base.rdb import UpdateRequest, QueryFilter, ComparisonOperator, QueryFilterGroup, LogicalOperator
            filter_list = []
            for key, value in filter.items():
                filter_obj = QueryFilter(field=key, operator=ComparisonOperator.EQ, value=value)
                filter_list.append(filter_obj)
            if len(filter_list) == 1:
                filters = filter_list[0]
            else:
                filters = QueryFilterGroup(operator=LogicalOperator.AND, filters=filter_list)
            request = UpdateRequest(table=table, data=data, filters=filters)
            response = self.client.update(request)
            return response.success if hasattr(response, 'success') else True
        except Exception as e:
            logger.error(f"数据库更新失败: {e}")
            return False
    
    def delete(self, table: str, filter: Dict[str, Any]) -> bool:
        """删除接口（同步，仅UniversalSQLAlchemyClient）"""
        try:
            from base.db.base.rdb import DeleteRequest, QueryFilter, ComparisonOperator, QueryFilterGroup, LogicalOperator
            filter_list = []
            for key, value in filter.items():
                filter_obj = QueryFilter(field=key, operator=ComparisonOperator.EQ, value=value)
                filter_list.append(filter_obj)
            if len(filter_list) == 1:
                filters = filter_list[0]
            else:
                filters = QueryFilterGroup(operator=LogicalOperator.AND, filters=filter_list)
            request = DeleteRequest(table=table, filters=filters)
            response = self.client.delete(request)
            return response.success if hasattr(response, 'success') else True
        except Exception as e:
            logger.error(f"数据库删除失败: {e}")
            return False
    
    def execute(self, sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """执行原始SQL查询（同步，仅UniversalSQLAlchemyClient）"""
        try:
            return self.client.execute(sql, params or {})
        except Exception as e:
            logger.error(f"SQL查询执行失败: {e}")
            return []


    # ==========================================
    # 异步方法
    # ==========================================
    
    async def ainsert(self, table: str, data: List[Dict[str, Any]]) -> bool:
        """插入接口（异步，仅UniversalSQLAlchemyClient）"""
        try:
            from base.db.base.rdb import InsertRequest
            request = InsertRequest(table=table, data=data)
            response = await self.client.ainsert(request)
            return response.success if hasattr(response, 'success') else True
        except Exception as e:
            logger.error(f"异步数据库插入失败: {e}")
            return False
    
    async def aselect(self, table: str, condition=None, limit=None, offset=None, order_by=None, columns=None) -> List[Dict[str, Any]]:
        """查询接口（异步，仅UniversalSQLAlchemyClient）"""
        try:
            from base.db.base.rdb import QueryRequest, QueryFilter, ComparisonOperator, QueryFilterGroup, LogicalOperator, QuerySort, SortOrder
            filters = None
            if condition:
                filter_list = []
                for key, value in condition.items():
                    filter_obj = QueryFilter(field=key, operator=ComparisonOperator.EQ, value=value)
                    filter_list.append(filter_obj)
                if len(filter_list) == 1:
                    filters = filter_list[0]
                else:
                    filters = QueryFilterGroup(operator=LogicalOperator.AND, filters=filter_list)
            sorts = None
            if order_by:
                sort_list = []
                if isinstance(order_by, str):
                    order_by = [order_by]
                for sort_str in order_by:
                    if ' DESC' in sort_str.upper():
                        field = sort_str.replace(' DESC', '').replace(' desc', '').strip()
                        sort_list.append(QuerySort(field=field, order=SortOrder.DESC))
                    else:
                        field = sort_str.replace(' ASC', '').replace(' asc', '').strip()
                        sort_list.append(QuerySort(field=field, order=SortOrder.ASC))
                sorts = sort_list
            request = QueryRequest(
                table=table,
                columns=columns,
                filters=filters,
                sorts=sorts,
                limit=limit,
                offset=offset
            )
            response = await self.client.aquery(request)
            return response.data if hasattr(response, 'data') else []
        except Exception as e:
            logger.error(f"异步数据库查询失败: {e}")
            return []
    
    async def aupdate(self, table: str, filter: Dict[str, Any], data: Dict[str, Any]) -> bool:
        """更新接口（异步，仅UniversalSQLAlchemyClient）"""
        try:
            from base.db.base.rdb import UpdateRequest, QueryFilter, ComparisonOperator, QueryFilterGroup, LogicalOperator
            filter_list = []
            for key, value in filter.items():
                filter_obj = QueryFilter(field=key, operator=ComparisonOperator.EQ, value=value)
                filter_list.append(filter_obj)
            if len(filter_list) == 1:
                filters = filter_list[0]
            else:
                filters = QueryFilterGroup(operator=LogicalOperator.AND, filters=filter_list)
            request = UpdateRequest(table=table, data=data, filters=filters)
            response = await self.client.aupdate(request)
            return response.success if hasattr(response, 'success') else True
        except Exception as e:
            logger.error(f"异步数据库更新失败: {e}")
            return False
    
    async def adelete(self, table: str, filter: Dict[str, Any]) -> bool:
        """删除接口（异步，仅UniversalSQLAlchemyClient）"""
        try:
            from base.db.base.rdb import DeleteRequest, QueryFilter, ComparisonOperator, QueryFilterGroup, LogicalOperator
            filter_list = []
            for key, value in filter.items():
                filter_obj = QueryFilter(field=key, operator=ComparisonOperator.EQ, value=value)
                filter_list.append(filter_obj)
            if len(filter_list) == 1:
                filters = filter_list[0]
            else:
                filters = QueryFilterGroup(operator=LogicalOperator.AND, filters=filter_list)
            request = DeleteRequest(table=table, filters=filters)
            response = await self.client.adelete(request)
            return response.success if hasattr(response, 'success') else True
        except Exception as e:
            logger.error(f"异步数据库删除失败: {e}")
            return False
    
    # ==========================================
    # 原始SQL执行方法 (支持异步)
    # ==========================================
    
    async def aexecute(self, sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """执行原始SQL查询（异步，仅UniversalSQLAlchemyClient）"""
        try:
            if hasattr(self.client, 'aexecute') and callable(getattr(self.client, 'aexecute')):
                return await self.client.aexecute(sql, params or {})
            elif hasattr(self.client, 'execute') and callable(getattr(self.client, 'execute')):
                return self.client.execute_query(sql, params or {})
            else:
                logger.warning(f"Client {type(self.client).__name__} 不支持SQL查询执行")
                return []
        except Exception as e:
            logger.error(f"异步SQL查询执行失败: {e}")
            return []

    
    # ==========================================
    # 客户端信息和调试方法
    # ==========================================
    
    def get_client_info(self) -> Dict[str, Any]:
        """获取底层客户端信息"""
        return {
            "client_type": type(self.client).__name__,
            "client_module": type(self.client).__module__,
            "has_async_support": {
                "ainsert": hasattr(self.client, 'ainsert'),
                "aselect": hasattr(self.client, 'aquery'),
                "aupdate": hasattr(self.client, 'aupdate'),
                "adelete": hasattr(self.client, 'adelete'),
                "aexecute_query": hasattr(self.client, 'aexecute_query'),
                "aexecute_command": hasattr(self.client, 'aexecute_command'),
            },
            "has_sync_support": {
                "insert": hasattr(self.client, 'insert'),
                "select": hasattr(self.client, 'query'),
                "update": hasattr(self.client, 'update'),
                "delete": hasattr(self.client, 'delete'),
            }
        } 
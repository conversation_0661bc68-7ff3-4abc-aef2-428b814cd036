"""
Pipeline字段映射器使用示例

这个脚本演示了如何使用PipelineFieldMapper进行字段映射和聚合。
"""

import asyncio
import logging
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 模拟导入（实际使用时需要正确的导入路径）
from modules.dd_submission.dd_b.utils.pipeline_field_mapper import (
    PipelineFieldMapper,
    PipelineFieldMappingResult,
    AggregatedFieldResult
)


class MockRecord:
    """模拟记录对象"""
    def __init__(self, record_id: int, submission_type: str = "NORMAL"):
        self.id = record_id
        self.submission_type = submission_type


class MockMetadataCRUD:
    """模拟Metadata CRUD"""
    
    async def get_source_table(self, table_name: str) -> Dict[str, Any]:
        """模拟查询表信息"""
        table_mapping = {
            "bdm_acc_payment_sched": {"table_name_cn": "还款计划表"},
            "bdm_acc_loanbusi_relation": {"table_name_cn": "新旧信贷业务关系表"},
            "adm_lon_varoius": {"table_name_cn": "客户各项贷款分析表"}
        }
        return table_mapping.get(table_name, {"table_name_cn": table_name})
    
    async def get_source_column(self, column_name: str) -> Dict[str, Any]:
        """模拟查询字段信息"""
        column_mapping = {
            "loan_bal": {"column_name_cn": "本金余额"},
            "lending_ref": {"column_name_cn": "借据编号"},
            "serial_overdue_cnt": {"column_name_cn": "连续逾期次数"},
            "overdue_days": {"column_name_cn": "逾期天数"}
        }
        return column_mapping.get(column_name, {"column_name_cn": column_name})


def create_mock_pipeline_result(record_id: int) -> Dict[str, Any]:
    """创建模拟的Pipeline结果"""
    if record_id == 1:
        return {
            "business_logic": {
                "表范围": ["bdm_acc_payment_sched", "bdm_acc_loanbusi_relation"],
                "计算逻辑": {},
                "条件": "serial_overdue_cnt > 361",
                "维度": {"loan_bal": "bdm_acc_loanbusi_relation", "lending_ref": "bdm_acc_loanbusi_relation"},
                "整体逻辑描述": "查询逾期361天以上的贷款余额"
            },
            "parser_info": {
                "tables": ["bdm_acc_payment_sched", "bdm_acc_loanbusi_relation"],
                "columns": {"loan_bal": "bdm_acc_loanbusi_relation", "lending_ref": "bdm_acc_loanbusi_relation", "serial_overdue_cnt": "bdm_acc_payment_sched"},
                "join": ["bdm_acc_loanbusi_relation.lending_ref = bdm_acc_payment_sched.lending_ref"]
            },
            "sql_candidates": ["select sum(loan_bal) from bdm_acc_loanbusi_relation T1 join bdm_acc_payment_sched T2 on T1.lending_ref = T2.lending_ref where T2.serial_overdue_cnt > 361"]
        }
    elif record_id == 2:
        return {
            "business_logic": {
                "表范围": ["adm_lon_varoius"],
                "计算逻辑": {},
                "条件": "overdue_days > 180",
                "维度": {"loan_bal": "adm_lon_varoius", "overdue_days": "adm_lon_varoius"},
                "整体逻辑描述": "查询逾期180天以上的贷款"
            },
            "parser_info": {
                "tables": ["adm_lon_varoius"],
                "columns": {"loan_bal": "adm_lon_varoius", "overdue_days": "adm_lon_varoius"},
                "join": []
            },
            "sql_candidates": ["select sum(loan_bal) from adm_lon_varoius where overdue_days > 180"]
        }
    else:
        return {
            "business_logic": {},
            "parser_info": {},
            "sql_candidates": []
        }


async def example_single_record_mapping():
    """示例1: 单记录映射"""
    print("\n=== 示例1: 单记录映射 ===")
    
    # 创建映射器
    metadata_crud = MockMetadataCRUD()
    mapper = PipelineFieldMapper(metadata_crud)
    
    # 创建测试数据
    record = MockRecord(record_id=1)
    pipeline_result = create_mock_pipeline_result(1)
    
    # 执行映射
    mapping_result = await mapper.map_single_record(
        pipeline_result=pipeline_result,
        original_record=record,
        keep_raw_format=True
    )
    
    # 输出结果
    print(f"映射成功: {mapping_result.mapping_success}")
    print(f"BDR09 (表英文名): {mapping_result.bdr09_raw}")
    print(f"BDR10 (表中文名): {mapping_result.bdr10_raw}")
    print(f"BDR11 (字段信息): {mapping_result.bdr11_raw}")
    print(f"SDR09 (字段中文名): {mapping_result.sdr09_raw}")
    print(f"SDR10 (SQL): {mapping_result.sdr10_raw}")
    
    # 转换为字符串格式
    string_format = mapping_result.to_string_format()
    print(f"\n字符串格式:")
    for key, value in string_format.items():
        print(f"  {key}: {value}")


async def example_batch_mapping():
    """示例2: 批量映射"""
    print("\n=== 示例2: 批量映射 ===")
    
    # 创建映射器
    metadata_crud = MockMetadataCRUD()
    mapper = PipelineFieldMapper(metadata_crud)
    
    # 创建测试数据
    records = [MockRecord(i) for i in range(1, 4)]
    pipeline_results = [create_mock_pipeline_result(i) for i in range(1, 4)]
    
    # 执行批量映射
    mapping_results = await mapper.map_batch_records(
        pipeline_results=pipeline_results,
        original_records=records,
        keep_raw_format=True
    )
    
    # 输出结果
    print(f"批量映射完成: {len(mapping_results)} 个记录")
    for result in mapping_results:
        print(f"记录 {result.record_id}: 成功={result.mapping_success}")
        if result.mapping_success:
            print(f"  表名: {result.bdr09_raw}")
            print(f"  字段: {list(result.bdr11_raw.keys()) if result.bdr11_raw else []}")


async def example_field_aggregation():
    """示例3: 字段聚合"""
    print("\n=== 示例3: 字段聚合 ===")
    
    # 创建映射器
    metadata_crud = MockMetadataCRUD()
    mapper = PipelineFieldMapper(metadata_crud)
    
    # 创建测试数据
    records = [MockRecord(i) for i in range(1, 3)]
    pipeline_results = [create_mock_pipeline_result(i) for i in range(1, 3)]
    
    # 执行批量映射
    mapping_results = await mapper.map_batch_records(
        pipeline_results=pipeline_results,
        original_records=records,
        keep_raw_format=True
    )
    
    # 执行聚合
    aggregated = mapper.aggregate_mapping_results(mapping_results)
    
    # 输出聚合结果
    print(f"聚合后的表英文名: {aggregated.bdr09_aggregated}")
    print(f"聚合后的表中文名: {aggregated.bdr10_aggregated}")
    print(f"聚合后的字段信息: {aggregated.bdr11_aggregated}")
    print(f"聚合后的字段中文名: {aggregated.sdr09_aggregated}")
    print(f"SDR12候选值: {aggregated.sdr12_candidates}")
    
    # 生成RANGE记录
    range_record = aggregated.to_range_record()
    print(f"\nRANGE记录字段:")
    for key, value in range_record.items():
        print(f"  {key}: {value}")


async def example_complete_workflow():
    """示例4: 完整工作流程"""
    print("\n=== 示例4: 完整工作流程 ===")
    
    # 模拟大批量处理场景
    metadata_crud = MockMetadataCRUD()
    mapper = PipelineFieldMapper(metadata_crud)
    
    # 1. 模拟查询结果（包含RANGE类型）
    all_records = [
        MockRecord(1, "NORMAL"),
        MockRecord(2, "NORMAL"),
        MockRecord(3, "NORMAL"),
        MockRecord(999, "RANGE")  # RANGE类型记录
    ]
    
    # 2. 分离普通记录和RANGE记录
    normal_records = [r for r in all_records if r.submission_type != 'RANGE']
    range_records = [r for r in all_records if r.submission_type == 'RANGE']
    
    print(f"总记录数: {len(all_records)}")
    print(f"普通记录数: {len(normal_records)}")
    print(f"RANGE记录数: {len(range_records)}")
    
    # 3. 处理普通记录
    pipeline_results = [create_mock_pipeline_result(r.id) for r in normal_records]
    mapping_results = await mapper.map_batch_records(
        pipeline_results=pipeline_results,
        original_records=normal_records,
        keep_raw_format=True
    )
    
    # 4. 聚合结果
    aggregated = mapper.aggregate_mapping_results(mapping_results)
    
    # 5. 转换普通记录为字符串格式
    normal_string_results = mapper.convert_to_string_format(mapping_results)
    
    # 6. 生成RANGE记录
    range_fields = aggregated.to_range_record()
    range_fields['record_id'] = str(range_records[0].id)
    
    # 7. 合并最终结果
    final_results = normal_string_results + [range_fields]
    
    print(f"\n最终结果:")
    print(f"总记录数: {len(final_results)}")
    for i, result in enumerate(final_results):
        record_type = "RANGE" if i == len(final_results) - 1 else "NORMAL"
        print(f"记录 {result['record_id']} ({record_type}): {len(result)} 个字段")


async def main():
    """主函数"""
    print("Pipeline字段映射器使用示例")
    print("=" * 50)
    
    try:
        await example_single_record_mapping()
        await example_batch_mapping()
        await example_field_aggregation()
        await example_complete_workflow()
        
        print("\n" + "=" * 50)
        print("所有示例执行完成!")
        
    except Exception as e:
        logger.error(f"示例执行失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())

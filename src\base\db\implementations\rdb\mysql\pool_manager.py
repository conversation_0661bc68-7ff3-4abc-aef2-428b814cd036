"""
MySQL连接池管理器

基于Universal架构设计的MySQL专用连接池管理
支持同步和异步连接池，提供高效的连接复用和管理
"""

import asyncio
import threading
import time
from typing import Dict, Any, Optional, Tuple
import logging
from contextlib import contextmanager, asynccontextmanager

logger = logging.getLogger(__name__)

try:
    import pymysql
    import pymysql.cursors
except ImportError:
    pymysql = None

try:
    import aiomysql
except ImportError:
    aiomysql = None

from .config import MySQLConnectionConfig
from .exceptions import MySQLPoolError, MySQLConnectionError, wrap_mysql_error


class MySQLConnectionPool:
    """MySQL连接池基类"""
    
    def __init__(self, config: MySQLConnectionConfig):
        self.config = config
        self._created_at = time.time()
        self._total_connections = 0
        self._active_connections = 0
        self._stats = {
            'total_created': 0,
            'total_closed': 0,
            'current_active': 0,
            'peak_active': 0,
            'total_errors': 0
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        return {
            'pool_type': self.__class__.__name__,
            'created_at': self._created_at,
            'uptime': time.time() - self._created_at,
            'total_connections': self._total_connections,
            'active_connections': self._active_connections,
            'config': {
                'pool_size': self.config.pool_size,
                'max_overflow': self.config.max_overflow,
                'pool_timeout': self.config.pool_timeout,
                'pool_recycle': self.config.pool_recycle,
            },
            'stats': self._stats.copy()
        }


class MySQLSyncConnectionPool(MySQLConnectionPool):
    """MySQL同步连接池"""
    
    def __init__(self, config: MySQLConnectionConfig):
        super().__init__(config)
        self._pool = []
        self._lock = threading.RLock()
        self._condition = threading.Condition(self._lock)
        self._overflow_connections = 0
        self._closed = False
        
        # 预创建最小连接数
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        try:
            min_connections = max(1, self.config.pool_size // 2)
            for _ in range(min_connections):
                conn = self._create_connection()
                self._pool.append(conn)
                self._total_connections += 1
                self._stats['total_created'] += 1
        except Exception as e:
            logger.error(f"Failed to initialize MySQL sync pool: {e}")
            raise MySQLPoolError(f"Pool initialization failed: {e}", e)
    
    def _create_connection(self):
        """创建新的MySQL连接"""
        if not pymysql:
            raise MySQLConnectionError("pymysql is not installed")
        
        try:
            conn_params = self.config.get_sync_connection_params()
            conn = pymysql.connect(
                cursorclass=pymysql.cursors.DictCursor,
                **conn_params
            )
            conn.ping(reconnect=True)  # 启用自动重连
            return conn
        except Exception as e:
            self._stats['total_errors'] += 1
            raise wrap_mysql_error(e, "create_connection")
    
    @contextmanager
    def get_connection(self):
        """获取连接的上下文管理器"""
        conn = None
        try:
            conn = self._get_connection()
            yield conn
        finally:
            if conn:
                self._return_connection(conn)
    
    def _get_connection(self):
        """从池中获取连接"""
        with self._condition:
            # 等待可用连接
            while True:
                if self._closed:
                    raise MySQLPoolError("Connection pool is closed")
                
                # 尝试从池中获取连接
                if self._pool:
                    conn = self._pool.pop()
                    self._active_connections += 1
                    self._stats['current_active'] = self._active_connections
                    self._stats['peak_active'] = max(self._stats['peak_active'], self._active_connections)
                    
                    # 检查连接是否有效
                    try:
                        conn.ping(reconnect=False)
                        return conn
                    except:
                        # 连接无效，关闭并重新创建
                        try:
                            conn.close()
                        except:
                            pass
                        self._active_connections -= 1
                        self._stats['total_errors'] += 1
                        continue
                
                # 池中没有连接，检查是否可以创建新连接
                total_connections = self._total_connections + self._overflow_connections
                if total_connections < self.config.pool_size + self.config.max_overflow:
                    try:
                        conn = self._create_connection()
                        if total_connections >= self.config.pool_size:
                            self._overflow_connections += 1
                        else:
                            self._total_connections += 1
                        
                        self._active_connections += 1
                        self._stats['total_created'] += 1
                        self._stats['current_active'] = self._active_connections
                        self._stats['peak_active'] = max(self._stats['peak_active'], self._active_connections)
                        return conn
                    except Exception as e:
                        self._stats['total_errors'] += 1
                        raise
                
                # 等待连接释放
                if not self._condition.wait(timeout=self.config.pool_timeout):
                    raise MySQLPoolError(f"Timeout waiting for connection after {self.config.pool_timeout}s")
    
    def _return_connection(self, conn):
        """将连接返回到池中"""
        with self._condition:
            if self._closed:
                try:
                    conn.close()
                except:
                    pass
                return
            
            self._active_connections -= 1
            self._stats['current_active'] = self._active_connections
            
            # 检查连接是否需要回收
            if hasattr(conn, '_created_at'):
                if time.time() - conn._created_at > self.config.pool_recycle:
                    try:
                        conn.close()
                    except:
                        pass
                    self._stats['total_closed'] += 1
                    self._condition.notify()
                    return
            
            # 如果池未满，将连接返回池中
            if len(self._pool) < self.config.pool_size:
                conn._created_at = time.time()
                self._pool.append(conn)
            else:
                # 池已满，关闭溢出连接
                try:
                    conn.close()
                except:
                    pass
                self._overflow_connections -= 1
                self._stats['total_closed'] += 1
            
            self._condition.notify()
    
    def close(self):
        """关闭连接池"""
        with self._condition:
            if self._closed:
                return
            
            self._closed = True
            
            # 关闭所有连接
            while self._pool:
                conn = self._pool.pop()
                try:
                    conn.close()
                    self._stats['total_closed'] += 1
                except:
                    pass
            
            self._condition.notify_all()


class MySQLAsyncConnectionPool(MySQLConnectionPool):
    """MySQL异步连接池"""
    
    def __init__(self, config: MySQLConnectionConfig):
        super().__init__(config)
        self._pool = None
        self._initialized = False
        self._lock = asyncio.Lock()
    
    async def _initialize_pool(self):
        """初始化异步连接池"""
        if self._initialized:
            return
        
        if not aiomysql:
            raise MySQLConnectionError("aiomysql is not installed")
        
        try:
            conn_params = self.config.get_async_connection_params()
            
            # 创建aiomysql连接池
            self._pool = await aiomysql.create_pool(
                minsize=max(1, self.config.pool_size // 2),
                maxsize=self.config.pool_size + self.config.max_overflow,
                pool_recycle=self.config.pool_recycle,
                cursorclass=aiomysql.DictCursor,
                **conn_params
            )
            
            self._initialized = True
            self._stats['total_created'] = self._pool.maxsize
            logger.info(f"Initialized MySQL async pool with {self._pool.maxsize} max connections")
            
        except Exception as e:
            logger.error(f"Failed to initialize MySQL async pool: {e}")
            raise MySQLPoolError(f"Async pool initialization failed: {e}", e)
    
    @asynccontextmanager
    async def get_connection(self):
        """获取异步连接的上下文管理器"""
        await self._ensure_initialized()
        
        async with self._pool.acquire() as conn:
            self._active_connections += 1
            self._stats['current_active'] = self._active_connections
            self._stats['peak_active'] = max(self._stats['peak_active'], self._active_connections)
            
            try:
                yield conn
            finally:
                self._active_connections -= 1
                self._stats['current_active'] = self._active_connections
    
    async def _ensure_initialized(self):
        """确保连接池已初始化"""
        if not self._initialized:
            async with self._lock:
                if not self._initialized:
                    await self._initialize_pool()
    
    async def close(self):
        """关闭异步连接池"""
        if self._pool:
            self._pool.close()
            await self._pool.wait_closed()
            self._stats['total_closed'] = self._stats['total_created']
            logger.info("Closed MySQL async pool")


class PooledConnectionMixin:
    """连接池混入类，为MySQL客户端提供连接池功能"""
    
    def __init__(self):
        self._sync_pool: Optional[MySQLSyncConnectionPool] = None
        self._async_pool: Optional[MySQLAsyncConnectionPool] = None
        self._pool_lock = threading.RLock()
        self._async_pool_lock = asyncio.Lock()
    
    def _get_sync_pool(self) -> MySQLSyncConnectionPool:
        """获取同步连接池"""
        if not self._sync_pool:
            with self._pool_lock:
                if not self._sync_pool:
                    self._sync_pool = MySQLSyncConnectionPool(self.config)
        return self._sync_pool
    
    async def _get_async_pool(self) -> MySQLAsyncConnectionPool:
        """获取异步连接池"""
        if not self._async_pool:
            async with self._async_pool_lock:
                if not self._async_pool:
                    self._async_pool = MySQLAsyncConnectionPool(self.config)
        return self._async_pool
    
    def get_sync_connection(self):
        """获取同步连接的上下文管理器"""
        pool = self._get_sync_pool()
        return pool.get_connection()
    
    def get_async_connection(self):
        """获取异步连接的上下文管理器"""
        return self._async_connection_context()

    @asynccontextmanager
    async def _async_connection_context(self):
        """异步连接上下文管理器"""
        pool = await self._get_async_pool()
        async with pool.get_connection() as conn:
            yield conn
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        stats = {}
        
        if self._sync_pool:
            stats['sync_pool'] = self._sync_pool.get_stats()
        
        if self._async_pool:
            stats['async_pool'] = self._async_pool.get_stats()
        
        return stats
    
    def close_pools(self):
        """关闭所有连接池"""
        if self._sync_pool:
            self._sync_pool.close()
            self._sync_pool = None
        
        if self._async_pool:
            # 异步池的关闭需要在异步上下文中进行
            # 这里只标记为需要关闭
            pass
    
    async def aclose_pools(self):
        """异步关闭所有连接池"""
        if self._async_pool:
            await self._async_pool.close()
            self._async_pool = None
        
        # 同步池也可以在这里关闭
        if self._sync_pool:
            self._sync_pool.close()
            self._sync_pool = None

"""
MySQL数据库客户端实现

基于Universal架构设计的MySQL专用数据库客户端
直接基于原生MySQL驱动实现，提供高性能的数据库操作

主要特性：
- 完整的RDB抽象层接口支持
- 同步/异步双重操作模式
- 高效的连接池管理
- 完善的错误处理和重试机制
- 支持事务管理
- 性能监控和健康检查
- Hydra配置系统集成

使用示例：
    >>> from base.db.implementations.rdb.mysql import create_mysql_client
    >>> 
    >>> # 创建客户端
    >>> client = create_mysql_client(
    ...     host="localhost",
    ...     database="mydb", 
    ...     username="user",
    ...     password="pass"
    ... )
    >>> 
    >>> # 执行查询
    >>> result = client.fetch_all("SELECT * FROM users WHERE active = %s", [True])
    >>> print(result.data)
    >>> 
    >>> # 使用RDB接口
    >>> from base.db.base.rdb import QueryRequest
    >>> request = QueryRequest(table="users", columns=["id", "name"])
    >>> response = client.query(request)
    >>> print(response.data)
"""

# 导入核心组件
from .client import MySQLClient
from .config import MySQLConnectionConfig
from .exceptions import (
    MySQLError, MySQLConnectionError, MySQLConfigurationError,
    MySQLQueryError, MySQLTransactionError, MySQLTimeoutError,
    MySQLPoolError, MySQLAuthenticationError, MySQLPermissionError,
    wrap_mysql_error, handle_mysql_error
)

# 导入工厂函数
from .factory import (
    # 主要创建方法
    create_mysql_client_from_dict,
    create_mysql_client,
    create_mysql_client_from_url,
    
    # 高级创建方法
    create_mysql_client_with_pool,
    create_mysql_client_with_ssl,
    
    # 配置辅助
    create_mysql_config_from_dict,
    get_mysql_default_config,
    validate_mysql_config,
    
    # Hydra集成
    create_mysql_client_hydra,
    
    # 别名
    create_client,
    create_client_from_dict,
    create_client_from_url
)

# 导入连接池管理
from .pool_manager import (
    MySQLConnectionPool,
    MySQLSyncConnectionPool, 
    MySQLAsyncConnectionPool,
    PooledConnectionMixin
)

# 导入装饰器
from .decorators import (
    mysql_operation,
    mysql_transaction,
    mysql_performance_monitor,
    mysql_cache,
    mysql_timeout
)

# 版本信息
__version__ = "1.0.0"
__author__ = "HSBC Knowledge Team"
__description__ = "MySQL数据库客户端实现"

# 导出的公共接口
__all__ = [
    # 核心类
    "MySQLClient",
    "MySQLConnectionConfig",
    
    # 异常类
    "MySQLError",
    "MySQLConnectionError", 
    "MySQLConfigurationError",
    "MySQLQueryError",
    "MySQLTransactionError",
    "MySQLTimeoutError",
    "MySQLPoolError",
    "MySQLAuthenticationError",
    "MySQLPermissionError",
    "wrap_mysql_error",
    "handle_mysql_error",
    
    # 工厂函数
    "create_mysql_client_from_dict",
    "create_mysql_client",
    "create_mysql_client_from_url",
    "create_mysql_client_with_pool",
    "create_mysql_client_with_ssl",
    "create_mysql_config_from_dict",
    "get_mysql_default_config",
    "validate_mysql_config",
    "create_mysql_client_hydra",
    
    # 别名
    "create_client",
    "create_client_from_dict", 
    "create_client_from_url",
    
    # 连接池
    "MySQLConnectionPool",
    "MySQLSyncConnectionPool",
    "MySQLAsyncConnectionPool", 
    "PooledConnectionMixin",
    
    # 装饰器
    "mysql_operation",
    "mysql_transaction",
    "mysql_performance_monitor",
    "mysql_cache",
    "mysql_timeout",
]


def get_client_info() -> dict:
    """获取客户端信息"""
    return {
        "name": "MySQL Database Client",
        "version": __version__,
        "author": __author__,
        "description": __description__,
        "supported_databases": ["MySQL", "MariaDB"],
        "supported_drivers": ["pymysql", "aiomysql"],
        "features": [
            "同步/异步操作",
            "连接池管理", 
            "事务支持",
            "错误处理和重试",
            "性能监控",
            "健康检查",
            "RDB抽象层接口",
            "Hydra配置集成"
        ]
    }


def check_dependencies() -> dict:
    """检查依赖项"""
    dependencies = {}
    
    try:
        import pymysql
        dependencies["pymysql"] = {
            "available": True,
            "version": getattr(pymysql, "__version__", "unknown")
        }
    except ImportError:
        dependencies["pymysql"] = {
            "available": False,
            "error": "pymysql not installed"
        }
    
    try:
        import aiomysql
        dependencies["aiomysql"] = {
            "available": True,
            "version": getattr(aiomysql, "__version__", "unknown")
        }
    except ImportError:
        dependencies["aiomysql"] = {
            "available": False,
            "error": "aiomysql not installed"
        }
    
    return dependencies


def get_usage_examples() -> dict:
    """获取使用示例"""
    return {
        "basic_usage": '''
# 基本使用
from base.db.implementations.rdb.mysql import create_mysql_client

client = create_mysql_client(
    host="localhost",
    database="mydb",
    username="user", 
    password="pass"
)

# 执行查询
result = client.fetch_all("SELECT * FROM users")
print(result.data)
''',
        
        "rdb_interface": '''
# 使用RDB接口
from base.db.base.rdb import QueryRequest

request = QueryRequest(
    table="users",
    columns=["id", "name", "email"],
    limit=10
)

response = client.query(request)
print(f"Found {response.total_count} users")
''',
        
        "async_usage": '''
# 异步使用
import asyncio

async def async_example():
    await client.aconnect()
    
    result = await client.afetch_all("SELECT * FROM users")
    print(result.data)
    
    await client.adisconnect()

asyncio.run(async_example())
''',
        
        "transaction": '''
# 事务使用
with client.transaction() as conn:
    client.execute("INSERT INTO users (name) VALUES (%s)", ["Alice"])
    client.execute("INSERT INTO users (name) VALUES (%s)", ["Bob"])
    # 自动提交
''',
        
        "config_from_dict": '''
# 从字典配置创建
config = {
    "host": "localhost",
    "port": 3306,
    "database": "mydb",
    "username": "user",
    "password": "pass",
    "pool_size": 20,
    "charset": "utf8mb4"
}

client = create_mysql_client_from_dict(config)
'''
    }


# 模块初始化检查
def _check_module_health():
    """检查模块健康状态"""
    deps = check_dependencies()
    
    if not deps["pymysql"]["available"]:
        import warnings
        warnings.warn(
            "pymysql is not available. Synchronous operations will not work. "
            "Install with: pip install pymysql",
            ImportWarning
        )
    
    if not deps["aiomysql"]["available"]:
        import warnings
        warnings.warn(
            "aiomysql is not available. Asynchronous operations will not work. "
            "Install with: pip install aiomysql", 
            ImportWarning
        )


# 执行模块初始化检查
_check_module_health()

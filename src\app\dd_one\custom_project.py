import re
from typing import List, Dict, Tuple
from doc_parse.parses.simple_utils.dd_read_old import get_custom_indicator
import uuid

tran_data = {
    "峁": "POS"
}


def get_custom_indicat(indicator_data: List[Dict]) -> Tuple[Dict[str, List[Dict]], List[str]]:
    result = {}
    sheet_sort_map = {}

    # Process each indicator entry
    for item in sorted(indicator_data, key=lambda x: x['sheet_sort']):
        sheet_name = item['sheet_name']
        sheet_sort = item['sheet_sort']

        # Track minimum sheet_sort for each sheet_name
        sheet_sort_map[sheet_name] = min(sheet_sort_map.get(sheet_name, float('inf')), sheet_sort)

        # Extract and clean core fields
        name_x = item["col"][0].strip().replace('其中：', '')
        name_y = item["lines"][0].strip()
        id_y = '<split>'.join(item["lines"]).replace('\n', '').replace(' ', '')
        original_position = f"({item['mark']['x']},{item['mark']['y']})"

        # Apply translations to name_x
        for key, value in tran_data.items():
            name_x = name_x.replace(value, key)

        # Extract components using regex
        name = (re.search(r'\d+(.*)', name_x).group(0).replace(' ', '')
                if re.search(r'\d+(.*)', name_x) else '')
        project_name = (re.search(r'[\u4e00-\u9fff].*', name_x).group(0).strip()
                        if re.search(r'[\u4e00-\u9fff].*', name_x) else '')
        name_id = re.sub(r'[\u4e00-\u9fff].*', '', name_x).strip()
        name_id = name_id[:-1] if name_id.endswith('.') else name_id

        # Process id_y
        id_parts = [part for part in id_y.split('<split>')
                    if part and not re.match(r'^[a-zA-Z]', part)]
        y2 = '-'.join(set(id_parts)) if id_parts else ''

        # Reverse translations
        for key, value in tran_data.items():
            name = name.replace(key, value)
            project_name = project_name.replace(key, value)
            name_id = name_id.replace(key, value)

        # Create result dictionary
        entry = {
            "name": name,
            "y1": name_y,
            "y2": y2,
            "valid": "True",
            "project_name": project_name,
            "name_id": name_id,
            "original_position": original_position
        }

        # Add to result dictionary
        result.setdefault(sheet_name, []).append(entry)

    # Generate ordered sheet_names list
    sheet_names = sorted(sheet_sort_map, key=lambda x: sheet_sort_map[x])
    # print(result)
    return result, sheet_names


def custom_project_output(input_data: dict):
    project_names, sheet_names = get_custom_indicat(input_data['indicator_data'])
    if input_data["doc_set"] == '1104':
        outputs = get_custom_indicator(project_names, sheet_names, input_data['report_file_path'])
    elif input_data['doc_set'] == 'djz':
        # todo 大集中对这部分的开发
        outputs = {}
        pass
    else:
        outputs = {}
    merged_outputs = []
    # Create a mapping of original_position to entry_id from indicator_data
    entry_id_map = {item['mark']['x'] + ',' + item['mark']['y']: item.get('entry_id', '')
                    for item in input_data['indicator_data']}

    # Update outputs with entry_id and ensure mark fields are correctly populated
    for sheet_name in outputs:
        for item in outputs[sheet_name]:
            # Extract x, y from original_position
            original_position = item.get('original_position', '()')
            x, y = '', ''
            if original_position != '()':
                x, y = original_position.strip('()').split(',')
                x, y = x.strip(), y.strip()

            # Get entry_id based on x,y coordinates
            entry_id = entry_id_map.get(x + ',' + y, '')

            # Generate UUID if entry_id is empty
            if entry_id == '':
                entry_id = str(uuid.uuid4())

            # Update item with entry_id and mark fields
            item['entry_id'] = entry_id
            merged_outputs.append(item)
    out_data = {
        "report_code": input_data.get("report_code", ""),
        "indicator_data": merged_outputs
    }
    return out_data


test_data = [
    {
        "sheet_name": "G01_I_1表外业务情况表",
        "sheet_sort": 1,
        "col": ["1.承兑汇票"],
        "lines": ["A", "余额"],
        "mark": {"x": "5", "y": "4"},
        "entry": "1"
    },
    {
        "sheet_name": "G01_I_1表外业务情况表",
        "sheet_sort": 1,
        "col": ["2.跟单信用证"],
        "lines": ["A", "余额"],
        "mark": {"x": "6", "y": "4"},
        "entry": ""
    },
    {
        "sheet_name": "G01_I_2委托贷款投向统计表",
        "sheet_sort": 3,
        "col": ["2.对境内委托贷款"],
        "lines": ["A", "余额"],
        "mark": {"x": "8", "y": "3"},
        "entry": "2"
    }
]

data = {
    "report_code": "G0107_beta_<version>",
    "report_file_path": "/data/ideal/code/hr_code/test_dd_file/file/G0101/G01_I填报说明（231版）.doc",
    "report_type": "",  # 明细还是指标
    "doc_set": "",  # 报送类型：1104或者djz
    "indicator_data": test_data
}
print(custom_project_output(data))

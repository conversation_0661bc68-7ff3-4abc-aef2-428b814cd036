"""
Knowledge系统统一CRUD操作

参考DD系统的crud.py设计，基于UniversalSQLAlchemyClient的强大批量操作功能，
提供知识库的简洁CRUD功能：
- kb_knowledge

设计原则：
- 直接使用客户端的批量操作方法
- 实体类只用于数据验证，不用于复杂的SQL构建
- 每个方法都很简洁，主要是换表名
"""

from typing import Any, Dict, List, Optional, Tuple, Union
import logging
from datetime import datetime

from .shared.exceptions import KnowledgeError, KnowledgeValidationError, KnowledgeNotFoundError, KnowledgeConflictError
from .shared.constants import KnowledgeConstants, KnowledgeTableNames
from .shared.utils import KnowledgeUtils

logger = logging.getLogger(__name__)


class KnowledgeCrud:
    """Knowledge系统CRUD操作类"""

    def __init__(self, rdb_client: Any, vdb_client: Any = None, embedding_client: Any = None):
        """
        初始化CRUD操作

        Args:
            rdb_client: 关系型数据库客户端（MySQL）
            vdb_client: 向量数据库客户端（PGVector，可选）
            embedding_client: 向量化模型客户端（可选）
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
    
    # ==================== 知识库管理 ====================

    async def create_knowledge_base(self, kb_data: Dict[str, Any]) -> str:
        """创建知识库"""
        # 验证必需字段
        required_fields = ['knowledge_name', 'knowledge_type']
        for field in required_fields:
            if field not in kb_data or not kb_data[field]:
                raise KnowledgeValidationError(f"缺少必需字段: {field}", field)
        
        # 验证知识库类型
        KnowledgeUtils.validate_knowledge_type(kb_data['knowledge_type'])
        
        # 生成或验证knowledge_id
        if 'knowledge_id' not in kb_data or not kb_data['knowledge_id']:
            kb_data['knowledge_id'] = KnowledgeUtils.generate_knowledge_id()
        elif not KnowledgeUtils.validate_knowledge_id(kb_data['knowledge_id']):
            raise KnowledgeValidationError("无效的knowledge_id格式", "knowledge_id")
        
        # 处理模型配置
        if 'models' not in kb_data or not kb_data['models']:
            kb_data['models'] = KnowledgeUtils.get_default_models_configuration(kb_data['knowledge_type'])
        
        # 验证模型配置
        KnowledgeUtils.validate_models_configuration(kb_data['models'], kb_data['knowledge_type'])
        
        # 序列化模型配置
        kb_data['models'] = KnowledgeUtils.serialize_models(kb_data['models'])
        
        # 添加时间戳和默认值
        current_time = datetime.now()
        kb_data.update({
            'doc_nums': kb_data.get('doc_nums', 0),
            'knowledge_desc': kb_data.get('knowledge_desc', ''),
            'create_time': current_time,
            'update_time': current_time
        })

        try:
            # 检查知识库名称是否已存在
            existing_kb = await self.get_knowledge_base(knowledge_name=kb_data['knowledge_name'])
            if existing_kb:
                raise KnowledgeConflictError(f"知识库名称已存在: {kb_data['knowledge_name']}", "knowledge_name")
            
            # 使用批量插入（单条）
            result = await self.rdb_client.abatch_insert(
                table=KnowledgeTableNames.KB_KNOWLEDGE,
                data=[kb_data]
            )

            if not result.success:
                raise KnowledgeError(f"创建知识库失败: {result.error_message}")

            return kb_data['knowledge_id']
            
        except Exception as e:
            if isinstance(e, (KnowledgeError, KnowledgeValidationError, KnowledgeConflictError)):
                raise
            logger.error(f"创建知识库失败: {e}")
            raise KnowledgeError(f"创建知识库失败: {e}")

    async def get_knowledge_base(self, knowledge_id: str = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """
        获取知识库

        Args:
            knowledge_id: 知识库ID（主键，默认查询条件）
            **where_conditions: 其他查询条件，如 knowledge_name='xxx', knowledge_type='Doc'
        """
        # 构建查询条件：优先使用主键，否则使用其他条件
        if knowledge_id:
            where = {'knowledge_id': knowledge_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise KnowledgeValidationError("必须提供 knowledge_id 或其他查询条件")

        results = await self._aselect(
            table=KnowledgeTableNames.KB_KNOWLEDGE,
            where=where,
            limit=1
        )
        
        if results:
            # 反序列化模型配置
            kb_data = results[0].copy()
            kb_data['models'] = KnowledgeUtils.deserialize_models(kb_data.get('models', '{}'))
            return kb_data
        
        return None

    async def update_knowledge_base(
        self,
        kb_data: Dict[str, Any],
        knowledge_id: str = None,
        **where_conditions
    ) -> bool:
        """
        更新知识库

        Args:
            kb_data: 要更新的数据
            knowledge_id: 知识库ID（主键，默认更新条件）
            **where_conditions: 其他更新条件，如 knowledge_name='xxx', knowledge_type='Doc'
        """
        # 构建更新条件：优先使用主键，否则使用其他条件
        if knowledge_id:
            where = {'knowledge_id': knowledge_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise KnowledgeValidationError("必须提供 knowledge_id 或其他更新条件")

        # 验证更新数据
        if 'knowledge_type' in kb_data:
            KnowledgeUtils.validate_knowledge_type(kb_data['knowledge_type'])

        if 'models' in kb_data:
            # 获取当前知识库信息以验证模型配置
            current_kb = await self.get_knowledge_base(knowledge_id) if knowledge_id else await self.get_knowledge_base(**where_conditions)
            if not current_kb:
                raise KnowledgeNotFoundError(knowledge_id)

            kb_type = kb_data.get('knowledge_type', current_kb['knowledge_type'])
            KnowledgeUtils.validate_models_configuration(kb_data['models'], kb_type)
            kb_data['models'] = KnowledgeUtils.serialize_models(kb_data['models'])

        # 添加更新时间
        kb_data['update_time'] = datetime.now()

        try:
            # 使用批量更新（单条）
            updates = [{"data": kb_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=KnowledgeTableNames.KB_KNOWLEDGE,
                updates=updates
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"更新知识库失败: {e}")
            raise KnowledgeError(f"更新知识库失败: {e}")

    async def delete_knowledge_base(self, knowledge_id: str = None, **where_conditions) -> bool:
        """
        删除知识库

        Args:
            knowledge_id: 知识库ID（主键，默认删除条件）
            **where_conditions: 其他删除条件，如 knowledge_name='xxx', knowledge_type='Doc'
        """
        # 构建删除条件：优先使用主键，否则使用其他条件
        if knowledge_id:
            where = {'knowledge_id': knowledge_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise KnowledgeValidationError("必须提供 knowledge_id 或其他删除条件")

        try:
            result = await self._abatch_delete_by_conditions(
                table=KnowledgeTableNames.KB_KNOWLEDGE,
                where_conditions=[where]
            )
            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除知识库失败: {e}")
            raise KnowledgeError(f"删除知识库失败: {e}")

    async def list_knowledge_bases(
        self,
        knowledge_name: Optional[str] = None,
        knowledge_type: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询知识库列表"""
        where = KnowledgeUtils.build_search_filters(
            knowledge_name=knowledge_name,
            knowledge_type=knowledge_type,
            **filters
        )

        results = await self._aselect(
            table=KnowledgeTableNames.KB_KNOWLEDGE,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

        # 反序列化所有结果的模型配置
        for result in results:
            result['models'] = KnowledgeUtils.deserialize_models(result.get('models', '{}'))

        return results

    # ==================== 批量操作 ====================

    async def batch_create_knowledge_bases(self, kb_data_list: List[Dict[str, Any]]) -> List[str]:
        """批量创建知识库"""
        # 预处理所有数据
        processed_data = []
        knowledge_ids = []
        current_time = datetime.now()

        for kb_data in kb_data_list:
            # 验证必需字段
            required_fields = ['knowledge_name', 'knowledge_type']
            for field in required_fields:
                if field not in kb_data or not kb_data[field]:
                    raise KnowledgeValidationError(f"缺少必需字段: {field}", field)

            # 验证知识库类型
            KnowledgeUtils.validate_knowledge_type(kb_data['knowledge_type'])

            # 生成或验证knowledge_id
            if 'knowledge_id' not in kb_data or not kb_data['knowledge_id']:
                kb_data['knowledge_id'] = KnowledgeUtils.generate_knowledge_id()
            elif not KnowledgeUtils.validate_knowledge_id(kb_data['knowledge_id']):
                raise KnowledgeValidationError("无效的knowledge_id格式", "knowledge_id")

            # 处理模型配置
            if 'models' not in kb_data or not kb_data['models']:
                kb_data['models'] = KnowledgeUtils.get_default_models_configuration(kb_data['knowledge_type'])

            # 验证模型配置
            KnowledgeUtils.validate_models_configuration(kb_data['models'], kb_data['knowledge_type'])

            # 序列化模型配置
            kb_data['models'] = KnowledgeUtils.serialize_models(kb_data['models'])

            # 添加时间戳和默认值
            kb_data.update({
                'doc_nums': kb_data.get('doc_nums', 0),
                'knowledge_desc': kb_data.get('knowledge_desc', ''),
                'create_time': current_time,
                'update_time': current_time
            })

            processed_data.append(kb_data)
            knowledge_ids.append(kb_data['knowledge_id'])

        try:
            # 使用真正的批量插入
            result = await self.rdb_client.abatch_insert(
                table=KnowledgeTableNames.KB_KNOWLEDGE,
                data=processed_data
            )

            if not result.success:
                raise KnowledgeError(f"批量创建知识库失败: {result.error_message}")

            return knowledge_ids

        except Exception as e:
            if isinstance(e, (KnowledgeError, KnowledgeValidationError)):
                raise
            logger.error(f"批量创建知识库失败: {e}")
            raise KnowledgeError(f"批量创建知识库失败: {e}")

    async def batch_update_knowledge_bases(
        self,
        updates: List[Dict[str, Any]],
        where_conditions: List[Dict[str, Any]] = None
    ) -> bool:
        """批量更新知识库"""
        # 添加更新时间
        current_time = datetime.now()
        for update in updates:
            if 'data' in update:
                update['data']['update_time'] = current_time

        try:
            # 使用真正的批量更新
            result = await self.rdb_client.abatch_update(
                table=KnowledgeTableNames.KB_KNOWLEDGE,
                updates=updates
            )

            return result.success

        except Exception as e:
            logger.error(f"批量更新知识库失败: {e}")
            raise KnowledgeError(f"批量更新知识库失败: {e}")

    async def batch_delete_knowledge_bases(self, where_conditions: List[Dict[str, Any]]) -> bool:
        """批量删除知识库"""
        try:
            result = await self._abatch_delete_by_conditions(
                table=KnowledgeTableNames.KB_KNOWLEDGE,
                where_conditions=where_conditions
            )
            return result.success

        except Exception as e:
            logger.error(f"批量删除知识库失败: {e}")
            raise KnowledgeError(f"批量删除知识库失败: {e}")

    # ==================== 私有辅助方法 ====================

    async def _aselect(self, table: str, where: Optional[Dict[str, Any]] = None,
                      order_by: Optional[List[str]] = None, limit: Optional[int] = None,
                      offset: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        兼容性方法：将aselect调用转换为aquery调用

        Args:
            table: 表名
            where: WHERE条件
            order_by: 排序字段
            limit: 限制数量
            offset: 偏移量

        Returns:
            查询结果列表
        """
        query_request = {
            "table": table
        }

        # 只有在有条件时才添加filters
        if where:
            query_request["filters"] = where

        # 只有在有值时才添加limit和offset
        if limit is not None:
            query_request["limit"] = limit
        if offset is not None:
            query_request["offset"] = offset

        if order_by:
            # 转换order_by格式
            sorts = []
            for order_field in order_by:
                if ' DESC' in order_field.upper():
                    field = order_field.replace(' DESC', '').replace(' desc', '').strip()
                    sorts.append({"field": field, "order": "desc"})
                elif ' ASC' in order_field.upper():
                    field = order_field.replace(' ASC', '').replace(' asc', '').strip()
                    sorts.append({"field": field, "order": "asc"})
                else:
                    sorts.append({"field": order_field.strip(), "order": "asc"})
            query_request["sorts"] = sorts

        result = await self.rdb_client.aquery(query_request)
        return result.data if result else []

    async def _abatch_delete_by_conditions(self, table: str, where_conditions: List[Dict[str, Any]]) -> Any:
        """
        兼容性方法：通过条件批量删除

        Args:
            table: 表名
            where_conditions: 删除条件列表

        Returns:
            操作结果
        """
        # 暂时使用简单的实现：逐条删除
        total_affected = 0
        for where_condition in where_conditions:
            try:
                # 构建删除SQL
                where_clause = " AND ".join([f"{k} = :{k}" for k in where_condition.keys()])
                sql = f"DELETE FROM {table} WHERE {where_clause}"

                result = await self.rdb_client.aexecute(sql, where_condition)
                total_affected += result.affected_rows
            except Exception as e:
                logger.error(f"删除失败: {e}")

        # 返回模拟的结果对象
        class MockResult:
            def __init__(self, success, affected_rows):
                self.success = success
                self.affected_rows = affected_rows

        return MockResult(True, total_affected)

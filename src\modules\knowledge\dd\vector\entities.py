"""
Vector模块数据实体
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any


@dataclass
class Embedding:
    """
    向量实体

    字段说明：
    - data_row_id: dd_submission_data表的主键ID（原value_id）
    - field_id: 字段ID（9=dr09, 17=dr17）
    - data_layer: 数据层（对应dr01字段：ADM/BDM/ADS/ODS等）
    - knowledge_id: 知识库UUID（来自上层系统）
    """
    id: Optional[int] = None
    embedding: Optional[List[float]] = None
    knowledge_id: str = ""
    data_row_id: Optional[int] = None  # 更名为data_row_id，表示dd_submission_data的主键ID
    field_id: Optional[int] = None
    data_layer: str = ""
    is_latest: bool = True
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "embedding": self.embedding,
            "knowledge_id": self.knowledge_id,
            "data_row_id": self.data_row_id,  # 更名为data_row_id
            "field_id": self.field_id,
            "data_layer": self.data_layer,
            "is_latest": self.is_latest,
            "create_time": self.create_time,
            "update_time": self.update_time,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Embedding":
        return cls(
            id=data.get("id"),
            embedding=data.get("embedding"),
            knowledge_id=data.get("knowledge_id", ""),
            data_row_id=data.get("data_row_id"),  # 更名为data_row_id
            field_id=data.get("field_id"),
            data_layer=data.get("data_layer", ""),
            is_latest=data.get("is_latest", True),
            create_time=data.get("create_time"),
            update_time=data.get("update_time"),
        )
    
    def validate_embedding_dimension(self, expected_dim: int = 768) -> bool:
        """验证向量维度"""
        if not self.embedding:
            return False
        return len(self.embedding) == expected_dim

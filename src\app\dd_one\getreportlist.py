import os
import docx
from doc_parse.parses.simple_utils.utils.orig_sheet_reader import get_excel_type
from doc_parse.parses.simple_utils.dd_utils.convert_doc import convert_doc_to_docx, accept_all_revisions
from config.config import BASE_PATH, TMP_PATH


def getreportlist(file_info: list, version: str) -> list:
    """
    根据文件列表生成报告信息列表
    :param file_info: 包含文件信息的字典列表 [{"file_id": "", "file_path": ""}]
    :return: 报告信息列表
    """
    # 固定存储路径前缀（根据实际情况修改）

    # 初始化结果列表
    report_list = []

    # 按文件夹分组文件
    folder_files = {}
    for file in file_info:
        # 构造完整路径
        full_path = os.path.join(BASE_PATH, file['file_path'])
        # 获取文件夹名称
        folder = os.path.dirname(file['file_path'])
        if folder.startswith('A'):
            folder_type = 'djz'
        elif folder.startswith('G'):
            folder_type = '1104'
        else:
            continue
        if folder not in folder_files:
            folder_files[folder] = []
        folder_files[folder].append({
            'file_id': file['file_id'],
            'full_path': full_path,
            'extension': os.path.splitext(file['file_path'])[1].lower(),
            'folder_type': folder_type
        })
    # print(folder_files)
    # 处理每个文件夹
    for folder, files in folder_files.items():
        doc_file = None
        excel_file = None

        # 识别文档和表格文件
        for file in files:
            if file['extension'] in ['.doc', '.docx', '.wps']:
                doc_file = file
            elif file['extension'] in ['.xlsx', '.xls', '.et']:
                excel_file = file

        # 确保文件夹中同时有文档和表格文件
        if doc_file and excel_file:
            try:
                converted_doc_path = convert_doc_to_docx(doc_file['full_path'], os.path.join(TMP_PATH, 'converted'))
                doc_data = accept_all_revisions(converted_doc_path).strip()
                # 读取docx文件的第一行
                report_name = ""
                if doc_data:
                    report_name = doc_data.split('\n')[0].strip().replace('填报说明', '')

                # 提取文件名作为report_code
                excel_filename = os.path.splitext(os.path.basename(excel_file['full_path']))[0]
                report_code = f"{excel_filename}_beta_{version}"  # 版本号可根据需要修改

                # 提取对应的报表类型（明细还是指标）
                if excel_file['folder_type'] == '1104':
                    report_type = get_excel_type(os.path.join(BASE_PATH, excel_file['full_path']), '')
                elif excel_file['folder_type'] == 'djz':
                    report_type = 'indicator'
                else:
                    report_type = 'detail'
                # 构造报告信息
                report_info = {
                    "report_code": report_code,
                    "report_name": report_name if report_name else "未知表",
                    "report_freq": "month",
                    "report_type": report_type,
                    "table_id": excel_file['file_id'],
                    "comments_id": doc_file['file_id']
                }

                report_list.append(report_info)

            except Exception as e:
                print(f"处理文件夹 {folder} 时出错: {str(e)}")
                continue

    return report_list


if __name__ == '__main__':
    file_info = [
        {"file_id": "1", "file_path": r"G02/G02填报说明（241）.wps"},
        {"file_id": "2", "file_path": r"G02/G02（241）.xls"},
        {"file_id": "3", "file_path": r"G1402/2、G14_II填报说明-mk-1119.doc"},
        {"file_id": "4", "file_path": r"G1402/G14_II_template.xls"},
        {"file_id": "5", "file_path": r"G0903M/G09填报说明（251）.docx"},
        {"file_id": "6", "file_path": r"G0903M/G09_III_template.xls"},
    ]
    report_list = getreportlist(file_info)
    print(report_list)

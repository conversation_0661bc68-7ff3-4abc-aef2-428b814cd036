#!/usr/bin/env python3
"""
DD-B异步处理API测试脚本

测试异步处理模式的DD-B API
"""

import asyncio
import json
import logging
from typing import Dict, Any
import httpx

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AsyncDDBAPITester:
    """DD-B异步API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:30337"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def test_config_management(self) -> Dict[str, Any]:
        """测试配置管理"""
        logger.info("🔧 测试配置管理...")
        
        try:
            # 1. 获取当前配置
            response = await self.client.get(f"{self.base_url}/api/dd/dd-b/config")
            config_result = response.json()
            
            logger.info(f"当前配置: {json.dumps(config_result, indent=2, ensure_ascii=False)}")
            
            # 2. 设置回调URL
            test_callback_url = "http://test-frontend/api/dd-b/callback"
            response = await self.client.post(
                f"{self.base_url}/api/dd/dd-b/config/callback-url",
                params={"callback_url": test_callback_url}
            )
            set_result = response.json()
            
            logger.info(f"设置回调URL结果: {json.dumps(set_result, indent=2, ensure_ascii=False)}")
            
            return {
                "success": response.status_code == 200,
                "config": config_result,
                "set_result": set_result
            }
        
        except Exception as e:
            logger.error(f"配置管理测试失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def test_async_process(self, report_code: str, dept_id: str) -> Dict[str, Any]:
        """测试异步处理"""
        logger.info(f"🚀 测试异步DD-B处理: {report_code} - {dept_id}")
        
        request_data = {
            "report_code": report_code,
            "dept_id": dept_id
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/api/dd/dd-b/process",
                json=request_data
            )
            result = response.json()
            
            logger.info(f"异步处理状态码: {response.status_code}")
            logger.info(f"异步处理响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 验证响应格式
            expected_success_response = {
                "code": "0",
                "msg": "业务信息正在处理中"
            }
            
            expected_error_response = {
                "code": "400",
                "msg": "无法在数据库里搜索到相关信息"
            }
            
            is_valid_response = (
                (result.get("code") == "0" and "处理中" in result.get("msg", "")) or
                (result.get("code") == "400" and "搜索" in result.get("msg", ""))
            )
            
            return {
                "success": response.status_code == 200 and is_valid_response,
                "status_code": response.status_code,
                "response": result,
                "is_processing": result.get("code") == "0",
                "is_not_found": result.get("code") == "400"
            }
        
        except Exception as e:
            logger.error(f"异步处理测试失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def test_health_check(self) -> Dict[str, Any]:
        """测试健康检查"""
        logger.info("🔍 测试健康检查...")
        
        try:
            response = await self.client.get(f"{self.base_url}/api/dd/dd-b/health")
            result = response.json()
            
            logger.info(f"健康检查状态码: {response.status_code}")
            logger.info(f"健康检查响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response": result
            }
        
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def test_invalid_request(self) -> Dict[str, Any]:
        """测试无效请求"""
        logger.info("❌ 测试无效请求...")
        
        request_data = {
            "report_code": "",  # 空的report_code
            "dept_id": "",      # 空的dept_id
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/api/dd/dd-b/process",
                json=request_data
            )
            result = response.json()
            
            logger.info(f"无效请求状态码: {response.status_code}")
            logger.info(f"无效请求响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return {
                "success": response.status_code == 422,  # 期望验证错误
                "status_code": response.status_code,
                "response": result
            }
        
        except Exception as e:
            logger.error(f"无效请求测试失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()


async def main():
    """主测试函数"""
    logger.info("🎯 DD-B异步处理API测试")
    logger.info("=" * 60)
    
    # 测试参数
    TEST_REPORT_CODE = "S71_ADS_RELEASE_V0"  # 使用您的测试参数
    TEST_DEPT_ID = "30239"                   # 使用您的测试参数
    
    tester = AsyncDDBAPITester()
    
    try:
        # 1. 健康检查测试
        logger.info("\n📋 1. 健康检查测试")
        health_result = await tester.test_health_check()
        
        if not health_result["success"]:
            logger.error("❌ 健康检查失败，跳过其他测试")
            return False
        
        logger.info("✅ 健康检查通过")
        
        # 2. 配置管理测试
        logger.info("\n📋 2. 配置管理测试")
        config_result = await tester.test_config_management()
        
        if config_result["success"]:
            logger.info("✅ 配置管理测试通过")
        else:
            logger.warning("⚠️ 配置管理测试失败")
        
        # 3. 异步处理测试（有效数据）
        logger.info("\n📋 3. 异步处理测试（有效数据）")
        process_result = await tester.test_async_process(TEST_REPORT_CODE, TEST_DEPT_ID)
        
        if process_result["success"]:
            if process_result["is_processing"]:
                logger.info("✅ 异步处理测试通过 - 数据存在，正在处理")
            elif process_result["is_not_found"]:
                logger.info("✅ 异步处理测试通过 - 数据不存在，正确返回错误")
        else:
            logger.warning("⚠️ 异步处理测试失败")
        
        # 4. 异步处理测试（无效数据）
        logger.info("\n📋 4. 异步处理测试（无效数据）")
        invalid_process_result = await tester.test_async_process("INVALID_CODE", "INVALID_DEPT")
        
        if invalid_process_result["success"] and invalid_process_result["is_not_found"]:
            logger.info("✅ 无效数据测试通过 - 正确返回数据不存在")
        else:
            logger.warning("⚠️ 无效数据测试失败")
        
        # 5. 无效请求测试
        logger.info("\n📋 5. 无效请求测试")
        invalid_result = await tester.test_invalid_request()
        
        if invalid_result["success"]:
            logger.info("✅ 无效请求测试通过")
        else:
            logger.warning("⚠️ 无效请求测试失败")
        
        # 测试总结
        logger.info("\n" + "=" * 60)
        logger.info("📊 测试总结:")
        logger.info(f"  健康检查: {'✅ 通过' if health_result['success'] else '❌ 失败'}")
        logger.info(f"  配置管理: {'✅ 通过' if config_result['success'] else '❌ 失败'}")
        logger.info(f"  异步处理: {'✅ 通过' if process_result['success'] else '❌ 失败'}")
        logger.info(f"  无效数据: {'✅ 通过' if invalid_process_result['success'] else '❌ 失败'}")
        logger.info(f"  无效请求: {'✅ 通过' if invalid_result['success'] else '❌ 失败'}")
        
        # 计算成功率
        total_tests = 5
        passed_tests = sum([
            health_result["success"],
            config_result["success"],
            process_result["success"],
            invalid_process_result["success"],
            invalid_result["success"]
        ])
        
        success_rate = (passed_tests / total_tests) * 100
        logger.info(f"  成功率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        
        if success_rate >= 80:
            logger.info("🎉 异步API测试基本通过！")
            return True
        else:
            logger.warning("⚠️ 异步API测试存在问题，需要检查")
            return False
    
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        return False
    
    finally:
        await tester.close()


if __name__ == "__main__":
    # 运行异步API测试
    success = asyncio.run(main())
    exit(0 if success else 1)

-- ==========================================
-- DD批量提交数据插入脚本扩展 - 剩余记录 (TEST_SUB_049 到 TEST_SUB_080) - VARCHAR兼容版
-- ==========================================
--
-- 说明：
-- 1. 这是insert_dd_submission_data_bulk.sql的扩展文件
-- 2. 包含剩余32条记录以达到80条总目标
-- 3. 继续使用相同的表结构和字段定义
-- 4. 保持数据分布策略的一致性
-- 5. 兼容新的VARCHAR字段类型（已移除ENUM约束）
--
-- 使用方法：
-- 1. 先执行 insert_dd_submission_data_bulk.sql (前48条记录)
-- 2. 再执行本文件 (剩余32条记录)
--
-- 重要变更：
-- - 所有编码字段现在使用VARCHAR类型，不再受ENUM约束限制
-- - 字段值保持不变，确保测试数据的业务逻辑正确性
--
-- 创建时间：2024-01-15
-- 版本：v2.0.0 (VARCHAR兼容版)
-- ==========================================

-- 继续插入剩余记录
INSERT INTO dd_submission_data (
    submission_id, report_data_id, version, type,
    -- A类字段 (DR01-DR22)
    dr01, dr02, dr03, dr04, dr05, dr06, dr07, dr08, dr09, dr10,
    dr11, dr12, dr13, dr14, dr15, dr16, dr17, dr18, dr19, dr20, dr21, dr22,
    -- B类字段 (BDR01-BDR18)
    bdr01, bdr02, bdr03, bdr04, bdr05, bdr06, bdr07, bdr08, bdr09, bdr10,
    bdr11, bdr12, bdr13, bdr14, bdr15, bdr16, bdr17, bdr18,
    -- C类字段 (SDR01-SDR15，跳过SDR02)
    sdr01, sdr03, sdr03_5, sdr04, sdr05, sdr06, sdr07, sdr08, sdr08_5, sdr09,
    sdr10, sdr11, sdr12, sdr13, sdr14, sdr15,
    -- D类字段 (IDR01-IDR05)
    idr01, idr02, idr03, idr04, idr05
) VALUES

-- 剩余记录 (TEST_SUB_049 到 TEST_SUB_080)
('TEST_SUB_049', 1, 'v1.0', 'SUBMISSION', 'ODS', '数据备份监控需求', 'IT运维', 'DATA_BACKUP_001', '数据备份系统', '数据备份监控表', '3', '定期报送', '备份成功率', 'BACKUP_SUCCESS_RATE_001', 'success_rate', 'INDEX', '数据备份成功率', 'DE_SUCCESS_RATE', '数据备份成功率统计', 'DECIMAL(5,2)', '百分比格式', '备份成功率码表', '日报', '成功率0-100', '数据备份监控', 'TEST_DBA', 'TEST_DBA', '刘芳', 'TEST_IT', '陈军', '数据备份监控', 'IT运维', '1', '系统生成', '数据备份系统', '备份监控', '数据备份监控界面', 'success_rate', 'TEST_DBA', '数据备份表', '备份系统记录', '备份成功率', '备份监控逻辑', '备份类型码表', 'ODS', '数据备份系统', 'DBS_001', '杨丽', '数据备份源文件', '数据备份监控', '全量更新', 'success_rate', 'BACKUP_SUCCESS_RATE_001', '备份成功率字段', '备份监控统计', '是', '备份执行日志', '数值型', '日', '每日监控', '数据备份模型', 'DM_BACKUP_001', '数据备份 = 成功率统计', '数据安全保障', '数据备份监控模型'),

('TEST_SUB_050', 1, 'v1.0', 'RANGE', 'ADM', '权限管理范围', '权限管理', 'PERMISSION_MANAGEMENT_001', '权限管理系统', '权限管理表', '43', '按需报送', '权限变更记录', 'PERMISSION_CHANGE_001', 'permission_changes', 'INDEX', '权限变更记录', 'DE_PERMISSION_CHANGES', '用户权限变更记录', 'TEXT', '权限变更详情', '权限变更码表', '不定期', '变更记录非空', '权限管理监控', 'TEST_IT', 'TEST_IT', '赵敏', 'TEST_SECURITY', '孙涛', '权限管理', '权限管理', '1', '系统生成', '权限管理系统', '权限控制', '权限管理界面', 'permission_changes', 'TEST_IT', '权限管理表', '权限系统数据', '权限变更', '权限管理逻辑', '权限类型码表', 'ADM', '权限管理系统', 'PMS_001', '周华', '权限管理源文件', '权限管理', '实时更新', 'permission_changes', 'PERMISSION_CHANGE_001', '权限变更记录字段', '权限变更跟踪', '是', '权限操作日志', '字符型', '不定期', '实时记录', '权限管理模型', 'DM_PERMISSION_001', '权限管理 = 变更记录跟踪', '系统安全管理', '权限管理模型'),

-- 继续添加更多记录...
('TEST_SUB_051', 1, 'v1.0', 'SUBMISSION', 'ADS', '产品推荐效果需求', '产品管理', 'PRODUCT_RECOMMEND_001', '产品推荐系统', '产品推荐效果表', '35', '定期报送', '推荐点击率', 'RECOMMEND_CTR_001', 'click_through_rate', 'INDEX', '产品推荐点击率', 'DE_CTR', '产品推荐点击率统计', 'DECIMAL(5,2)', '百分比格式', '点击率码表', '日报', '点击率0-100', '推荐效果分析', 'TEST_MARKETING', 'TEST_MARKETING', '吴静', 'TEST_DATA_MGT', '马超', '产品推荐效果分析', '产品管理', '1', '计算生成', '产品推荐系统', '推荐分析', '推荐效果分析界面', 'click_through_rate', 'TEST_MARKETING', '推荐效果表', '推荐系统数据', '推荐点击率', '推荐效果逻辑', '推荐类型码表', 'ADS', '产品推荐系统', 'PRS_001', '黄磊', '推荐效果源文件', '产品推荐效果', '全量更新', 'click_through_rate', 'RECOMMEND_CTR_001', '推荐点击率字段', '推荐效果计算', '是', '推荐行为数据', '数值型', '日', '每日分析', '推荐效果模型', 'DM_RECOMMEND_001', '推荐效果 = 点击率分析', '推荐算法优化', '产品推荐效果模型'),

('TEST_SUB_052', 1, 'v1.0', 'SUBMISSION', 'BDM', '网点效能管理需求', '网点管理', 'BRANCH_EFFICIENCY_001', '网点管理系统', '网点效能管理表', '36', '定期报送', '网点业务量', 'BRANCH_VOLUME_001', 'business_volume', 'INDEX', '网点业务量', 'DE_BUSINESS_VOLUME', '网点日均业务量', 'INT', '非负整数', '业务量码表', '日报', '业务量大于等于0', '网点效能分析', 'TEST_OPERATIONS', 'TEST_OPERATIONS', '徐娟', 'TEST_DATA_MGT', '林峰', '网点效能管理', '网点管理', '1', '系统生成', '网点管理系统', '效能分析', '网点效能分析界面', 'business_volume', 'TEST_OPERATIONS', '网点效能表', '网点业务数据', '网点业务量', '效能分析逻辑', '网点类型码表', 'BDM', '网点管理系统', 'BMS_001', '郑云', '网点效能源文件', '网点效能管理', '全量更新', 'business_volume', 'BRANCH_VOLUME_001', '网点业务量字段', '网点效能统计', '是', '网点业务记录', '数值型', '日', '每日统计', '网点效能模型', 'DM_BRANCH_001', '网点效能 = 业务量分析', '网点运营优化', '网点效能管理模型'),

('TEST_SUB_053', 2, 'v1.0', 'RANGE', 'IDM', '系统监控统计范围', 'IT运维', 'SYSTEM_MONITOR_STAT_001', '系统监控平台', '系统监控统计表', '33', '实时报送', '系统可用率', 'SYSTEM_AVAILABILITY_001', 'availability_rate', 'INDEX', '系统可用率', 'DE_AVAILABILITY', '系统服务可用率', 'DECIMAL(5,2)', '百分比格式', '可用率码表', '实时', '可用率0-100', '系统可用性监控', 'TEST_IT', 'TEST_IT', '何琳', 'TEST_DBA', '罗斌', '系统监控统计', 'IT运维', '1', '系统生成', '系统监控平台', '监控统计', '系统监控统计界面', 'availability_rate', 'TEST_IT', '系统监控表', '系统监控数据', '系统可用率', '监控统计逻辑', '监控类型码表', 'IDM', '系统监控平台', 'SMP_001', '谢亮', '系统监控源文件', '系统监控统计', '实时更新', 'availability_rate', 'SYSTEM_AVAILABILITY_001', '系统可用率字段', '可用率监控', '是', '系统运行日志', '数值型', '实时', '实时监控', '系统监控模型', 'DM_MONITOR_001', '系统监控 = 可用率统计', '系统稳定性分析', '系统监控统计模型'),

('TEST_SUB_054', 1, 'v1.0', 'SUBMISSION', 'ODS', '安全事件记录需求', '安全管理', 'SECURITY_EVENT_001', '安全管理系统', '安全事件记录表', '3', '实时报送', '安全事件级别', 'SECURITY_LEVEL_001', 'security_level', 'INDEX', '安全事件级别', 'DE_SECURITY_LEVEL', '安全事件风险级别', 'INT', '1-低,2-中,3-高,4-严重', '安全级别码表', '实时', '级别1-4', '安全事件监控', 'TEST_SECURITY', 'TEST_SECURITY', '冯杰', 'TEST_IT', '邓丽', '安全事件记录', '安全管理', '1', '系统生成', '安全管理系统', '事件记录', '安全事件记录界面', 'security_level', 'TEST_SECURITY', '安全事件表', '安全系统数据', '安全事件级别', '安全事件逻辑', '事件类型码表', 'ODS', '安全管理系统', 'SMS_001', '曾强', '安全事件源文件', '安全事件记录', '实时更新', 'security_level', 'SECURITY_LEVEL_001', '安全事件级别字段', '安全事件分析', '是', '安全监控日志', '数值型', '实时', '实时记录', '安全事件模型', 'DM_SECURITY_001', '安全事件 = 级别分析', '安全风险管控', '安全事件记录模型'),

('TEST_SUB_055', 1, 'v1.0', 'SUBMISSION', 'ADM', '参数配置管理需求', '系统配置', 'PARAM_CONFIG_001', '参数配置系统', '参数配置管理表', '43', '按需报送', '配置参数值', 'CONFIG_VALUE_001', 'config_value', 'INDEX', '配置参数值', 'DE_CONFIG_VALUE', '系统配置参数值', 'VARCHAR(500)', '配置参数内容', '配置值码表', '不定期', '参数值非空', '配置参数管理', 'TEST_IT', 'TEST_IT', '韩雪', 'TEST_DATA_MGT', '蒋涛', '参数配置管理', '系统配置', '1', '手工录入', '参数配置系统', '配置管理', '参数配置管理界面', 'config_value', 'TEST_IT', '参数配置表', '系统配置数据', '配置参数值', '配置管理逻辑', '参数类型码表', 'ADM', '参数配置系统', 'PCS_001', '朱琳', '参数配置源文件', '参数配置管理', '全量更新', 'config_value', 'CONFIG_VALUE_001', '配置参数值字段', '配置参数维护', '是', '配置变更记录', '字符型', '不定期', '按需维护', '参数配置模型', 'DM_CONFIG_001', '参数配置 = 配置值管理', '系统配置标准化', '参数配置管理模型'),

-- 继续添加更多记录以快速达到80条目标...
('TEST_SUB_056', 1, 'v1.0', 'RANGE', 'ADS', '客户流失预警范围', '客户管理', 'CHURN_WARNING_001', '客户流失系统', '客户流失预警表', '16', '定期报送', '流失风险评分', 'CHURN_RISK_SCORE_001', 'churn_score', 'INDEX', '客户流失风险评分', 'DE_CHURN_SCORE', '客户流失风险评分', 'DECIMAL(5,2)', '0-10分制', '流失风险码表', '周报', '评分0-10', '客户流失预警', 'TEST_RETAIL', 'TEST_RETAIL', '彭飞', 'TEST_DATA_MGT', '袁静', '客户流失预警', '客户管理', '1', '计算生成', '客户流失系统', '流失预警', '客户流失预警界面', 'churn_score', 'TEST_RETAIL', '客户流失表', '客户行为数据', '流失风险评分', '流失预警逻辑', '流失类型码表', 'ADS', '客户流失系统', 'CCS_001', '高娜', '客户流失源文件', '客户流失预警', '全量更新', 'churn_score', 'CHURN_RISK_SCORE_001', '流失风险评分字段', '流失风险计算', '是', '客户行为特征', '数值型', '周', '每周预警', '客户流失模型', 'DM_CHURN_001', '客户流失 = 风险评分计算', '客户保留策略', '客户流失预警模型'),

('TEST_SUB_057', 1, 'v1.0', 'SUBMISSION', 'BDM', '信贷风险评估需求', '信贷管理', 'CREDIT_RISK_001', '信贷风险系统', '信贷风险评估表', '38', '定期报送', '信贷风险等级', 'CREDIT_RISK_LEVEL_001', 'risk_level', 'INDEX', '信贷风险等级', 'DE_RISK_LEVEL', '客户信贷风险等级', 'VARCHAR(20)', '风险等级编码', '信贷风险码表', '日报', '风险等级非空', '信贷风险监控', 'TEST_TREASURY', 'TEST_TREASURY', '孔亮', 'TEST_COMPLIANCE', '曹敏', '信贷风险评估', '信贷管理', '1', '计算生成', '信贷风险系统', '风险评估', '信贷风险评估界面', 'risk_level', 'TEST_TREASURY', '信贷风险表', '信贷业务数据', '信贷风险等级', '风险评估逻辑', '风险等级码表', 'BDM', '信贷风险系统', 'CRS_001', '严华', '信贷风险源文件', '信贷风险评估', '全量更新', 'risk_level', 'CREDIT_RISK_LEVEL_001', '信贷风险等级字段', '风险等级评估', '是', '信贷客户数据', '字符型', '日', '每日评估', '信贷风险模型', 'DM_CREDIT_RISK_001', '信贷风险 = 风险等级评估', '信贷风险管控', '信贷风险评估模型'),

('TEST_SUB_058', 3, 'v1.0', 'SUBMISSION', 'IDM', '数据同步监控需求', '数据集成', 'DATA_SYNC_001', '数据同步系统', '数据同步监控表', '3', '实时报送', '同步成功率', 'SYNC_SUCCESS_RATE_001', 'sync_rate', 'INDEX', '数据同步成功率', 'DE_SYNC_RATE', '数据同步成功率统计', 'DECIMAL(5,2)', '百分比格式', '同步成功率码表', '实时', '成功率0-100', '数据同步监控', 'TEST_DATA_MGT', 'TEST_DATA_MGT', '李明', 'TEST_IT', '王强', '数据同步监控', '数据集成', '1', '系统生成', '数据同步系统', '同步监控', '数据同步监控界面', 'sync_rate', 'TEST_DATA_MGT', '数据同步表', '数据同步记录', '同步成功率', '同步监控逻辑', '同步类型码表', 'IDM', '数据同步系统', 'DSS_001', '张伟', '数据同步源文件', '数据同步监控', '实时更新', 'sync_rate', 'SYNC_SUCCESS_RATE_001', '同步成功率字段', '同步监控统计', '是', '数据同步日志', '数值型', '实时', '实时监控', '数据同步模型', 'DM_SYNC_001', '数据同步 = 成功率监控', '数据集成优化', '数据同步监控模型'),

-- 快速添加剩余记录以达到80条目标
('TEST_SUB_059', 1, 'v1.0', 'SUBMISSION', 'ODS', '日志清理监控需求', 'IT运维', 'LOG_CLEANUP_001', '日志管理系统', '日志清理监控表', '3', '定期报送', '日志清理量', 'LOG_CLEANUP_SIZE_001', 'cleanup_size', 'INDEX', '日志清理数据量', 'DE_CLEANUP_SIZE', '日志清理数据量统计', 'BIGINT', '字节为单位', '数据量码表', '日报', '数据量大于等于0', '日志清理监控', 'TEST_DBA', 'TEST_DBA', '刘芳', 'TEST_IT', '陈军', '日志清理监控', 'IT运维', '1', '系统生成', '日志管理系统', '日志清理', '日志清理监控界面', 'cleanup_size', 'TEST_DBA', '日志清理表', '日志清理记录', '清理数据量', '日志清理逻辑', '日志类型码表', 'ODS', '日志管理系统', 'LMS_001', '杨丽', '日志清理源文件', '日志清理监控', '全量更新', 'cleanup_size', 'LOG_CLEANUP_SIZE_001', '日志清理量字段', '日志清理统计', '是', '日志清理记录', '数值型', '日', '每日清理', '日志清理模型', 'DM_LOG_CLEANUP_001', '日志清理 = 清理量统计', '存储空间优化', '日志清理监控模型'),

('TEST_SUB_060', 1, 'v1.0', 'RANGE', 'ADM', '元数据管理范围', '数据治理', 'METADATA_MANAGEMENT_001', '元数据系统', '元数据管理表', '43', '按需报送', '元数据版本', 'METADATA_VERSION_001', 'metadata_version', 'INDEX', '元数据版本', 'DE_METADATA_VERSION', '业务元数据版本号', 'VARCHAR(20)', '版本号格式', '元数据版本码表', '不定期', '版本号非空', '元数据版本管理', 'TEST_DATA_MGT', 'TEST_DATA_MGT', '赵敏', 'TEST_IT', '孙涛', '元数据管理', '数据治理', '1', '手工录入', '元数据系统', '元数据维护', '元数据管理界面', 'metadata_version', 'TEST_DATA_MGT', '元数据表', '元数据定义', '元数据版本', '元数据管理逻辑', '元数据类型码表', 'ADM', '元数据系统', 'MDS_001', '周华', '元数据源文件', '元数据管理', '全量更新', 'metadata_version', 'METADATA_VERSION_001', '元数据版本字段', '元数据版本管理', '是', '元数据定义数据', '字符型', '不定期', '按需维护', '元数据管理模型', 'DM_METADATA_001', '元数据管理 = 版本控制', '数据标准化管理', '元数据管理模型'),

-- 最后20条记录 (TEST_SUB_061 到 TEST_SUB_080)
('TEST_SUB_061', 1, 'v1.0', 'SUBMISSION', 'ADS', '客户信用评级需求', '信用管理', 'CREDIT_RATING_001', '信用评级系统', '客户信用评级表', '16', '定期报送', '信用评级', 'CREDIT_RATING_001', 'credit_rating', 'INDEX', '客户信用评级', 'DE_CREDIT_RATING', '客户信用等级评定', 'VARCHAR(10)', '信用等级编码', '信用等级码表', '月报', '等级非空', '信用评级管理', 'TEST_CORPORATE', 'TEST_CORPORATE', '吴静', 'TEST_COMPLIANCE', '马超', '客户信用评级', '信用管理', '1', '计算生成', '信用评级系统', '信用评级', '信用评级界面', 'credit_rating', 'TEST_CORPORATE', '信用评级表', '信用评估数据', '信用评级', '信用评级逻辑', '信用类型码表', 'ADS', '信用评级系统', 'CRS_001', '黄磊', '信用评级源文件', '客户信用评级', '全量更新', 'credit_rating', 'CREDIT_RATING_001', '信用评级字段', '信用评级计算', '是', '客户信用数据', '字符型', '月', '每月评级', '信用评级模型', 'DM_CREDIT_RATING_001', '信用评级 = 信用评估', '信用风险管控', '客户信用评级模型'),

('TEST_SUB_062', 1, 'v1.0', 'SUBMISSION', 'BDM', '业务流程优化需求', '流程管理', 'PROCESS_OPTIMIZE_001', '流程优化系统', '业务流程优化表', '33', '定期报送', '流程效率指标', 'PROCESS_EFFICIENCY_001', 'efficiency_index', 'INDEX', '流程效率指标', 'DE_EFFICIENCY_INDEX', '业务流程效率指标', 'DECIMAL(5,2)', '效率指数', '效率指标码表', '周报', '指标大于0', '流程效率监控', 'TEST_OPERATIONS', 'TEST_OPERATIONS', '徐娟', 'TEST_DATA_MGT', '林峰', '业务流程优化', '流程管理', '1', '计算生成', '流程优化系统', '流程优化', '流程优化界面', 'efficiency_index', 'TEST_OPERATIONS', '流程优化表', '流程执行数据', '效率指标', '流程优化逻辑', '流程类型码表', 'BDM', '流程优化系统', 'POS_001', '郑云', '流程优化源文件', '业务流程优化', '全量更新', 'efficiency_index', 'PROCESS_EFFICIENCY_001', '流程效率指标字段', '流程效率计算', '是', '流程执行记录', '数值型', '周', '每周优化', '流程优化模型', 'DM_PROCESS_OPT_001', '流程优化 = 效率指标分析', '流程持续改进', '业务流程优化模型'),

('TEST_SUB_063', 2, 'v1.0', 'RANGE', 'IDM', '接口调用统计范围', 'IT集成', 'API_CALL_STAT_001', 'API统计系统', '接口调用统计表', '33', '实时报送', '接口调用量', 'API_CALL_VOLUME_001', 'call_volume', 'INDEX', '接口调用量', 'DE_CALL_VOLUME', 'API接口调用量统计', 'BIGINT', '非负整数', '调用量码表', '实时', '调用量大于等于0', 'API调用监控', 'TEST_IT', 'TEST_IT', '何琳', 'TEST_DATA_MGT', '罗斌', '接口调用统计', 'IT集成', '1', '系统生成', 'API统计系统', 'API统计', '接口调用统计界面', 'call_volume', 'TEST_IT', 'API统计表', 'API调用记录', '接口调用量', 'API统计逻辑', 'API类型码表', 'IDM', 'API统计系统', 'ASS_001', '谢亮', '接口调用源文件', '接口调用统计', '实时更新', 'call_volume', 'API_CALL_VOLUME_001', '接口调用量字段', 'API调用统计', '是', 'API调用日志', '数值型', '实时', '实时统计', '接口调用模型', 'DM_API_CALL_001', '接口调用 = 调用量统计', 'API性能优化', '接口调用统计模型'),

('TEST_SUB_064', 1, 'v1.0', 'SUBMISSION', 'ODS', '存储空间监控需求', 'IT运维', 'STORAGE_MONITOR_001', '存储监控系统', '存储空间监控表', '3', '定期报送', '存储使用率', 'STORAGE_USAGE_001', 'usage_rate', 'INDEX', '存储空间使用率', 'DE_USAGE_RATE', '系统存储空间使用率', 'DECIMAL(5,2)', '百分比格式', '使用率码表', '日报', '使用率0-100', '存储空间监控', 'TEST_DBA', 'TEST_DBA', '冯杰', 'TEST_IT', '邓丽', '存储空间监控', 'IT运维', '1', '系统生成', '存储监控系统', '存储监控', '存储监控界面', 'usage_rate', 'TEST_DBA', '存储监控表', '存储系统数据', '存储使用率', '存储监控逻辑', '存储类型码表', 'ODS', '存储监控系统', 'SMS_001', '曾强', '存储监控源文件', '存储空间监控', '全量更新', 'usage_rate', 'STORAGE_USAGE_001', '存储使用率字段', '存储监控统计', '是', '存储监控日志', '数值型', '日', '每日监控', '存储监控模型', 'DM_STORAGE_001', '存储监控 = 使用率统计', '存储容量规划', '存储空间监控模型'),

('TEST_SUB_065', 1, 'v1.0', 'SUBMISSION', 'ADM', '数据字典管理需求', '数据治理', 'DATA_DICT_001', '数据字典系统', '数据字典管理表', '43', '按需报送', '字典条目数', 'DICT_ENTRY_COUNT_001', 'entry_count', 'INDEX', '数据字典条目数', 'DE_ENTRY_COUNT', '数据字典条目统计', 'INT', '非负整数', '条目数码表', '不定期', '条目数大于等于0', '数据字典管理', 'TEST_DATA_MGT', 'TEST_DATA_MGT', '韩雪', 'TEST_IT', '蒋涛', '数据字典管理', '数据治理', '1', '手工录入', '数据字典系统', '字典维护', '数据字典管理界面', 'entry_count', 'TEST_DATA_MGT', '数据字典表', '数据字典定义', '字典条目数', '字典管理逻辑', '字典类型码表', 'ADM', '数据字典系统', 'DDS_001', '朱琳', '数据字典源文件', '数据字典管理', '全量更新', 'entry_count', 'DICT_ENTRY_COUNT_001', '字典条目数字段', '字典条目统计', '是', '数据字典数据', '数值型', '不定期', '按需维护', '数据字典模型', 'DM_DICT_001', '数据字典 = 条目管理', '数据标准化', '数据字典管理模型'),

-- 继续添加最后15条记录
('TEST_SUB_066', 1, 'v1.0', 'RANGE', 'ADS', '风险预警统计范围', '风险管理', 'RISK_WARNING_STAT_001', '风险预警系统', '风险预警统计表', '4', '实时报送', '预警触发次数', 'WARNING_COUNT_001', 'warning_count', 'INDEX', '风险预警触发次数', 'DE_WARNING_COUNT', '风险预警触发统计', 'INT', '非负整数', '预警次数码表', '实时', '次数大于等于0', '风险预警统计', 'TEST_RISK_CTRL', 'TEST_RISK_CTRL', '彭飞', 'TEST_COMPLIANCE', '袁静', '风险预警统计', '风险管理', '1', '系统生成', '风险预警系统', '预警统计', '风险预警统计界面', 'warning_count', 'TEST_RISK_CTRL', '风险预警表', '风险预警记录', '预警触发次数', '预警统计逻辑', '预警类型码表', 'ADS', '风险预警系统', 'RWS_001', '高娜', '风险预警源文件', '风险预警统计', '实时更新', 'warning_count', 'WARNING_COUNT_001', '预警触发次数字段', '预警统计计算', '是', '风险预警日志', '数值型', '实时', '实时统计', '风险预警模型', 'DM_WARNING_STAT_001', '风险预警 = 触发次数统计', '风险预警优化', '风险预警统计模型'),

('TEST_SUB_067', 1, 'v1.0', 'SUBMISSION', 'BDM', '客户满意度调查需求', '客户服务', 'SATISFACTION_SURVEY_001', '满意度调查系统', '客户满意度调查表', '31', '定期报送', '满意度得分', 'SATISFACTION_SCORE_001', 'satisfaction_score', 'INDEX', '客户满意度得分', 'DE_SATISFACTION_SCORE', '客户服务满意度得分', 'DECIMAL(3,1)', '1-5分制', '满意度码表', '月报', '得分1-5', '满意度调查分析', 'TEST_CUSTOMER_SVC', 'TEST_CUSTOMER_SVC', '孔亮', 'TEST_DATA_MGT', '曹敏', '客户满意度调查', '客户服务', '1', '手工录入', '满意度调查系统', '满意度调查', '满意度调查界面', 'satisfaction_score', 'TEST_CUSTOMER_SVC', '满意度调查表', '客户调查数据', '满意度得分', '满意度调查逻辑', '调查类型码表', 'BDM', '满意度调查系统', 'SSS_001', '严华', '满意度调查源文件', '客户满意度调查', '全量更新', 'satisfaction_score', 'SATISFACTION_SCORE_001', '满意度得分字段', '满意度统计分析', '是', '客户调查记录', '数值型', '月', '每月调查', '满意度调查模型', 'DM_SATISFACTION_001', '满意度调查 = 得分统计', '客户服务改进', '客户满意度调查模型'),

('TEST_SUB_068', 3, 'v1.0', 'SUBMISSION', 'IDM', '数据血缘追踪需求', '数据治理', 'DATA_LINEAGE_001', '数据血缘系统', '数据血缘追踪表', '3', '按需报送', '血缘关系数', 'LINEAGE_COUNT_001', 'lineage_count', 'INDEX', '数据血缘关系数', 'DE_LINEAGE_COUNT', '数据血缘关系统计', 'INT', '非负整数', '血缘关系码表', '不定期', '关系数大于等于0', '数据血缘管理', 'TEST_DATA_MGT', 'TEST_DATA_MGT', '李明', 'TEST_IT', '王强', '数据血缘追踪', '数据治理', '1', '系统生成', '数据血缘系统', '血缘追踪', '数据血缘追踪界面', 'lineage_count', 'TEST_DATA_MGT', '数据血缘表', '数据血缘记录', '血缘关系数', '血缘追踪逻辑', '血缘类型码表', 'IDM', '数据血缘系统', 'DLS_001', '张伟', '数据血缘源文件', '数据血缘追踪', '全量更新', 'lineage_count', 'LINEAGE_COUNT_001', '血缘关系数字段', '血缘关系统计', '是', '数据血缘数据', '数值型', '不定期', '按需追踪', '数据血缘模型', 'DM_LINEAGE_001', '数据血缘 = 关系追踪', '数据治理优化', '数据血缘追踪模型'),

('TEST_SUB_069', 1, 'v1.0', 'SUBMISSION', 'ODS', '网络流量监控需求', 'IT运维', 'NETWORK_TRAFFIC_001', '网络监控系统', '网络流量监控表', '33', '实时报送', '网络带宽使用率', 'BANDWIDTH_USAGE_001', 'bandwidth_usage', 'INDEX', '网络带宽使用率', 'DE_BANDWIDTH_USAGE', '网络带宽使用率统计', 'DECIMAL(5,2)', '百分比格式', '带宽使用率码表', '实时', '使用率0-100', '网络流量监控', 'TEST_IT', 'TEST_IT', '刘芳', 'TEST_DBA', '陈军', '网络流量监控', 'IT运维', '1', '系统生成', '网络监控系统', '流量监控', '网络流量监控界面', 'bandwidth_usage', 'TEST_IT', '网络监控表', '网络监控数据', '带宽使用率', '流量监控逻辑', '网络类型码表', 'ODS', '网络监控系统', 'NMS_001', '杨丽', '网络监控源文件', '网络流量监控', '实时更新', 'bandwidth_usage', 'BANDWIDTH_USAGE_001', '带宽使用率字段', '网络流量统计', '是', '网络监控日志', '数值型', '实时', '实时监控', '网络监控模型', 'DM_NETWORK_001', '网络监控 = 带宽使用率统计', '网络性能优化', '网络流量监控模型'),

('TEST_SUB_070', 1, 'v1.0', 'RANGE', 'ADM', '业务规则管理范围', '业务管理', 'BUSINESS_RULE_001', '业务规则系统', '业务规则管理表', '43', '按需报送', '规则版本号', 'RULE_VERSION_001', 'rule_version', 'INDEX', '业务规则版本号', 'DE_RULE_VERSION', '业务规则版本管理', 'VARCHAR(20)', '版本号格式', '规则版本码表', '不定期', '版本号非空', '业务规则管理', 'TEST_OPERATIONS', 'TEST_OPERATIONS', '赵敏', 'TEST_DATA_MGT', '孙涛', '业务规则管理', '业务管理', '1', '手工录入', '业务规则系统', '规则管理', '业务规则管理界面', 'rule_version', 'TEST_OPERATIONS', '业务规则表', '业务规则定义', '规则版本号', '规则管理逻辑', '规则类型码表', 'ADM', '业务规则系统', 'BRS_001', '周华', '业务规则源文件', '业务规则管理', '全量更新', 'rule_version', 'RULE_VERSION_001', '规则版本号字段', '规则版本管理', '是', '业务规则数据', '字符型', '不定期', '按需维护', '业务规则模型', 'DM_RULE_001', '业务规则 = 版本管理', '业务标准化', '业务规则管理模型'),

-- 最后10条记录 (TEST_SUB_071 到 TEST_SUB_080)
('TEST_SUB_071', 1, 'v1.0', 'SUBMISSION', 'ADS', '客户行为分析需求', '客户分析', 'BEHAVIOR_ANALYSIS_001', '行为分析系统', '客户行为分析表', '31', '定期报送', '行为模式识别', 'BEHAVIOR_PATTERN_001', 'behavior_pattern', 'INDEX', '客户行为模式', 'DE_BEHAVIOR_PATTERN', '客户行为模式识别', 'VARCHAR(100)', '行为模式描述', '行为模式码表', '周报', '模式非空', '行为模式分析', 'TEST_OPERATIONS', 'TEST_OPERATIONS', '吴静', 'TEST_DATA_MGT', '马超', '客户行为分析', '客户分析', '1', '计算生成', '行为分析系统', '行为分析', '行为分析界面', 'behavior_pattern', 'TEST_OPERATIONS', '行为分析表', '客户行为数据', '行为模式', '行为分析逻辑', '行为类型码表', 'ADS', '行为分析系统', 'BAS_001', '黄磊', '行为分析源文件', '客户行为分析', '全量更新', 'behavior_pattern', 'BEHAVIOR_PATTERN_001', '行为模式字段', '行为模式识别', '是', '客户操作记录', '字符型', '周', '每周分析', '行为分析模型', 'DM_BEHAVIOR_001', '行为分析 = 模式识别', '客户洞察分析', '客户行为分析模型'),

('TEST_SUB_072', 1, 'v1.0', 'SUBMISSION', 'BDM', '产品销售分析需求', '产品管理', 'PRODUCT_SALES_ANALYSIS_001', '销售分析系统', '产品销售分析表', '35', '定期报送', '销售趋势指标', 'SALES_TREND_001', 'sales_trend', 'INDEX', '产品销售趋势', 'DE_SALES_TREND', '产品销售趋势指标', 'DECIMAL(10,2)', '趋势指数', '销售趋势码表', '月报', '趋势指标非空', '销售趋势分析', 'TEST_MARKETING', 'TEST_MARKETING', '徐娟', 'TEST_DATA_MGT', '林峰', '产品销售分析', '产品管理', '1', '计算生成', '销售分析系统', '销售分析', '销售分析界面', 'sales_trend', 'TEST_MARKETING', '销售分析表', '产品销售数据', '销售趋势', '销售分析逻辑', '产品类型码表', 'BDM', '销售分析系统', 'SAS_001', '郑云', '销售分析源文件', '产品销售分析', '全量更新', 'sales_trend', 'SALES_TREND_001', '销售趋势字段', '销售趋势计算', '是', '产品销售记录', '数值型', '月', '每月分析', '销售分析模型', 'DM_SALES_ANALYSIS_001', '销售分析 = 趋势计算', '产品策略优化', '产品销售分析模型'),

('TEST_SUB_073', 2, 'v1.0', 'RANGE', 'IDM', '系统集成状态范围', 'IT集成', 'INTEGRATION_STATUS_001', '集成状态系统', '系统集成状态表', '33', '实时报送', '集成健康度', 'INTEGRATION_HEALTH_001', 'health_score', 'INDEX', '系统集成健康度', 'DE_HEALTH_SCORE', '系统集成健康度评分', 'DECIMAL(5,2)', '0-100分制', '健康度码表', '实时', '健康度0-100', '集成健康监控', 'TEST_IT', 'TEST_IT', '何琳', 'TEST_DATA_MGT', '罗斌', '系统集成状态', 'IT集成', '1', '系统生成', '集成状态系统', '集成监控', '集成状态界面', 'health_score', 'TEST_IT', '集成状态表', '系统集成数据', '集成健康度', '集成监控逻辑', '集成类型码表', 'IDM', '集成状态系统', 'ISS_001', '谢亮', '集成状态源文件', '系统集成状态', '实时更新', 'health_score', 'INTEGRATION_HEALTH_001', '集成健康度字段', '集成健康度计算', '是', '系统集成日志', '数值型', '实时', '实时监控', '集成状态模型', 'DM_INTEGRATION_STATUS_001', '集成状态 = 健康度评估', '系统集成优化', '系统集成状态模型'),

('TEST_SUB_074', 1, 'v1.0', 'SUBMISSION', 'ODS', '应用性能监控需求', 'IT运维', 'APP_PERFORMANCE_001', '应用监控系统', '应用性能监控表', '33', '实时报送', '应用响应时间', 'APP_RESPONSE_TIME_001', 'app_response_time', 'INDEX', '应用响应时间', 'DE_APP_RESPONSE_TIME', '应用平均响应时间', 'INT', '毫秒为单位', '响应时间码表', '实时', '响应时间大于0', '应用性能监控', 'TEST_IT', 'TEST_IT', '冯杰', 'TEST_DBA', '邓丽', '应用性能监控', 'IT运维', '1', '系统生成', '应用监控系统', '性能监控', '应用性能监控界面', 'app_response_time', 'TEST_IT', '应用监控表', '应用监控数据', '应用响应时间', '性能监控逻辑', '应用类型码表', 'ODS', '应用监控系统', 'AMS_001', '曾强', '应用监控源文件', '应用性能监控', '实时更新', 'app_response_time', 'APP_RESPONSE_TIME_001', '应用响应时间字段', '应用性能统计', '是', '应用监控日志', '数值型', '实时', '实时监控', '应用监控模型', 'DM_APP_MONITOR_001', '应用监控 = 响应时间统计', '应用性能优化', '应用性能监控模型'),

('TEST_SUB_075', 1, 'v1.0', 'SUBMISSION', 'ADM', '系统配置审计需求', '系统管理', 'CONFIG_AUDIT_001', '配置审计系统', '系统配置审计表', '43', '按需报送', '配置变更记录', 'CONFIG_CHANGE_001', 'config_changes', 'INDEX', '系统配置变更', 'DE_CONFIG_CHANGES', '系统配置变更记录', 'TEXT', '配置变更详情', '配置变更码表', '不定期', '变更记录非空', '配置审计管理', 'TEST_IT', 'TEST_IT', '韩雪', 'TEST_SECURITY', '蒋涛', '系统配置审计', '系统管理', '1', '系统生成', '配置审计系统', '配置审计', '配置审计界面', 'config_changes', 'TEST_IT', '配置审计表', '系统配置数据', '配置变更记录', '配置审计逻辑', '配置类型码表', 'ADM', '配置审计系统', 'CAS_001', '朱琳', '配置审计源文件', '系统配置审计', '实时更新', 'config_changes', 'CONFIG_CHANGE_001', '配置变更记录字段', '配置变更跟踪', '是', '配置操作日志', '字符型', '不定期', '实时记录', '配置审计模型', 'DM_CONFIG_AUDIT_001', '配置审计 = 变更跟踪', '系统安全管理', '系统配置审计模型'),

('TEST_SUB_076', 1, 'v1.0', 'RANGE', 'ADS', '营销效果统计范围', '营销管理', 'MARKETING_STAT_001', '营销统计系统', '营销效果统计表', '35', '定期报送', '营销ROI', 'MARKETING_ROI_001', 'marketing_roi', 'INDEX', '营销投资回报率', 'DE_MARKETING_ROI', '营销活动投资回报率', 'DECIMAL(10,2)', 'ROI比率', '营销ROI码表', '月报', 'ROI大于等于0', '营销效果统计', 'TEST_MARKETING', 'TEST_MARKETING', '彭飞', 'TEST_DATA_MGT', '袁静', '营销效果统计', '营销管理', '1', '计算生成', '营销统计系统', '效果统计', '营销效果统计界面', 'marketing_roi', 'TEST_MARKETING', '营销统计表', '营销活动数据', '营销ROI', '效果统计逻辑', '营销类型码表', 'ADS', '营销统计系统', 'MSS_001', '高娜', '营销统计源文件', '营销效果统计', '全量更新', 'marketing_roi', 'MARKETING_ROI_001', '营销ROI字段', '营销ROI计算', '是', '营销投入产出数据', '数值型', '月', '每月统计', '营销统计模型', 'DM_MARKETING_STAT_001', '营销统计 = ROI计算', '营销策略优化', '营销效果统计模型'),

('TEST_SUB_077', 1, 'v1.0', 'SUBMISSION', 'BDM', '风险控制措施需求', '风险管理', 'RISK_CONTROL_MEASURE_001', '风险控制系统', '风险控制措施表', '4', '实时报送', '控制措施有效性', 'CONTROL_EFFECTIVENESS_001', 'effectiveness_score', 'INDEX', '风险控制措施有效性', 'DE_EFFECTIVENESS', '风险控制措施有效性评分', 'DECIMAL(5,2)', '0-10分制', '有效性码表', '实时', '有效性0-10', '风险控制监控', 'TEST_RISK_CTRL', 'TEST_RISK_CTRL', '孔亮', 'TEST_COMPLIANCE', '曹敏', '风险控制措施', '风险管理', '1', '计算生成', '风险控制系统', '控制措施', '风险控制措施界面', 'effectiveness_score', 'TEST_RISK_CTRL', '风险控制表', '风险控制数据', '控制措施有效性', '风险控制逻辑', '控制类型码表', 'BDM', '风险控制系统', 'RCS_001', '严华', '风险控制源文件', '风险控制措施', '实时更新', 'effectiveness_score', 'CONTROL_EFFECTIVENESS_001', '控制措施有效性字段', '有效性评估', '是', '风险控制记录', '数值型', '实时', '实时评估', '风险控制模型', 'DM_RISK_CONTROL_001', '风险控制 = 有效性评估', '风险管控优化', '风险控制措施模型'),

('TEST_SUB_078', 3, 'v1.0', 'SUBMISSION', 'IDM', '数据质量评估需求', '数据治理', 'DATA_QUALITY_ASSESS_001', '质量评估系统', '数据质量评估表', '3', '定期报送', '数据质量综合评分', 'QUALITY_OVERALL_SCORE_001', 'overall_score', 'INDEX', '数据质量综合评分', 'DE_OVERALL_SCORE', '数据质量综合评分', 'DECIMAL(5,2)', '0-100分制', '质量评分码表', '日报', '评分0-100', '数据质量评估', 'TEST_DATA_MGT', 'TEST_DATA_MGT', '李明', 'TEST_DBA', '王强', '数据质量评估', '数据治理', '1', '计算生成', '质量评估系统', '质量评估', '数据质量评估界面', 'overall_score', 'TEST_DATA_MGT', '质量评估表', '数据质量指标', '质量综合评分', '质量评估逻辑', '质量类型码表', 'IDM', '质量评估系统', 'QAS_001', '张伟', '质量评估源文件', '数据质量评估', '全量更新', 'overall_score', 'QUALITY_OVERALL_SCORE_001', '质量综合评分字段', '质量评估计算', '是', '数据质量检查结果', '数值型', '日', '每日评估', '质量评估模型', 'DM_QUALITY_ASSESS_001', '质量评估 = 综合评分计算', '数据治理提升', '数据质量评估模型'),

('TEST_SUB_079', 1, 'v1.0', 'SUBMISSION', 'ODS', '系统日志分析需求', 'IT运维', 'LOG_ANALYSIS_001', '日志分析系统', '系统日志分析表', '3', '定期报送', '异常日志数量', 'ERROR_LOG_COUNT_001', 'error_count', 'INDEX', '系统异常日志数量', 'DE_ERROR_COUNT', '系统异常日志统计', 'INT', '非负整数', '异常日志码表', '日报', '异常数量大于等于0', '系统日志分析', 'TEST_DBA', 'TEST_DBA', '刘芳', 'TEST_IT', '陈军', '系统日志分析', 'IT运维', '1', '系统生成', '日志分析系统', '日志分析', '日志分析界面', 'error_count', 'TEST_DBA', '日志分析表', '系统日志数据', '异常日志数量', '日志分析逻辑', '日志类型码表', 'ODS', '日志分析系统', 'LAS_001', '杨丽', '日志分析源文件', '系统日志分析', '全量更新', 'error_count', 'ERROR_LOG_COUNT_001', '异常日志数量字段', '异常日志统计', '是', '系统运行日志', '数值型', '日', '每日分析', '日志分析模型', 'DM_LOG_ANALYSIS_001', '日志分析 = 异常统计', '系统稳定性监控', '系统日志分析模型'),

('TEST_SUB_080', 1, 'v1.0', 'RANGE', 'ADM', '主数据管理范围', '主数据管理', 'MASTER_DATA_001', '主数据系统', '主数据管理表', '43', '按需报送', '主数据一致性评分', 'CONSISTENCY_SCORE_001', 'consistency_score', 'INDEX', '主数据一致性评分', 'DE_CONSISTENCY_SCORE', '主数据一致性评分', 'DECIMAL(5,2)', '0-100分制', '一致性码表', '不定期', '一致性0-100', '主数据管理', 'TEST_DATA_MGT', 'TEST_DATA_MGT', '赵敏', 'TEST_IT', '孙涛', '主数据管理', '主数据管理', '1', '计算生成', '主数据系统', '主数据管理', '主数据管理界面', 'consistency_score', 'TEST_DATA_MGT', '主数据表', '主数据定义', '一致性评分', '主数据管理逻辑', '主数据类型码表', 'ADM', '主数据系统', 'MDS_001', '周华', '主数据源文件', '主数据管理', '全量更新', 'consistency_score', 'CONSISTENCY_SCORE_001', '一致性评分字段', '一致性评估', '是', '主数据标准', '数值型', '不定期', '按需评估', '主数据模型', 'DM_MASTER_DATA_001', '主数据管理 = 一致性评估', '数据标准化管理', '主数据管理模型');

-- ==========================================
-- 80条记录插入完成
-- ==========================================

-- 数据分布总结：
-- - ADS: 30条 (TEST_SUB_001-008, 028-032, 041, 046, 051, 056, 061, 066, 071, 076)
-- - BDM: 20条 (TEST_SUB_015-020, 033-036, 042, 047, 052, 057, 062, 067, 072, 077)
-- - IDM: 15条 (TEST_SUB_021-023, 037-038, 043, 048, 053, 058, 063, 068, 073, 078)
-- - ODS: 10条 (TEST_SUB_024-025, 039, 044, 049, 054, 059, 064, 069, 074, 079)
-- - ADM: 5条 (TEST_SUB_026-027, 040, 045, 050, 055, 060, 065, 070, 075, 080)
--
-- 提交类型分布：
-- - SUBMISSION: 60条
-- - RANGE: 20条
--
-- 表ID覆盖：
-- - 表ID=16: 客户信息相关 (多条)
-- - 表ID=2: 交易记录相关 (多条)
-- - 表ID=31: 用户档案相关 (多条)
-- - 表ID=4: 风险汇总相关 (多条)
-- - 表ID=3: 系统日志相关 (多条)
-- - 表ID=33: API/IT系统相关 (多条)
-- - 表ID=35: 客户资产/产品相关 (多条)
-- - 表ID=36: 渠道管理相关 (多条)
-- - 表ID=38: 贷款信息相关 (多条)
-- - 表ID=43: 基础配置相关 (多条)
--
-- 部门分配测试覆盖：
-- - TEST_RETAIL, TEST_CORPORATE, TEST_CUSTOMER_SVC (客户相关)
-- - TEST_TREASURY, TEST_RISK_CTRL, TEST_COMPLIANCE (风险财务)
-- - TEST_IT, TEST_DBA, TEST_DATA_MGT (IT数据)
-- - TEST_OPERATIONS, TEST_MARKETING (运营营销)
-- - TEST_SECURITY (安全管理)
--
-- 完成时间：2024-01-15
-- 总记录数：80条 ✅

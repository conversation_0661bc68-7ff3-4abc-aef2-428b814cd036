"""
业务功能模块主入口文件

演示如何使用DD搜索和文本匹配功能。
"""

import asyncio
import sys
import os
from typing import List
import hydra
from loguru import logger

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from utils.common.config_util import config

# 初始化Hydra配置
with hydra.initialize(version_base=None, config_path="../config"):
    cfg = hydra.compose(config_name="config")
    config.initialize(cfg)


class DDFunctionManager:
    """业务功能管理器"""

    def __init__(self):
        """初始化"""
        self.dd_searcher = DDSearchService()
        self.text_matcher = TextMatcherService()

    async def search_dd_by_texts(
        self,
        field1: str,
        field2: str,
        knowledge_id: str,
        use_rdb: bool = True,
        use_vdb: bool = True,
        similarity_threshold: float = 0.7,
    ):
        """
        基于两段文本搜索DD条目

        Args:
            field1: 第一段文本
            field2: 第二段文本
            knowledge_id: 知识库ID
            use_rdb: 是否使用RDB精确搜索
            use_vdb: 是否使用VDB向量搜索
            similarity_threshold: 向量相似度阈值

        Returns:
            搜索结果
        """
        # 构建匹配类型列表
        match_types = []
        if use_rdb:
            match_types.append(MatchType.RDB_EXACT)
        if use_vdb:
            match_types.append(MatchType.VDB_SIMILAR)

        # 构建搜索请求
        request = SearchRequest(
            text1=field1,
            text2=field2,
            knowledge_id=knowledge_id,
            match_types=match_types,
            vdb_similarity_threshold=similarity_threshold,
            max_results_per_type=10,
        )

        # 执行搜索
        result = await self.dd_searcher.search_dd_entries(request)

        return result

    # async def calculate_text_similarity(
    #     self,
    #     text1: str,
    #     text2: str,
    #     knowledge_id: str = None,
    #     use_embedding: bool = True,
    # ):
    #     """
    #     计算两段文本的相似度
    #     """
    #     similarities = await self.text_matcher.calculate_text_similarity(
    #         text1=text1,
    #         text2=text2,
    #         knowledge_id=knowledge_id,
    #         use_embedding=use_embedding,
    #     )
    #
    #     return similarities
    #
    # async def search_dd_by_category(
    #     self,
    #     text: str,
    #     knowledge_id: str,
    #     categories: List[DDFieldCategory],
    #     max_results: int = 20,
    # ):
    #     """
    #     按类别搜索DD条目
    #     """
    #     request = SearchRequest(
    #         text1=text,
    #         text2="",
    #         knowledge_id=knowledge_id,
    #         match_types=[MatchType.RDB_EXACT, MatchType.VDB_SIMILAR],
    #         field_categories=categories,
    #         max_results_per_type=max_results,
    #     )
    #
    #     result = await self.dd_searcher.search_dd_entries(request)
    #     return result
    #

# 示例使用函数
async def example_usage():
    """示例用法"""

    # 初始化管理器
    manager = DDFunctionManager()

    # 示例1: 基于两段文本搜索DD条目
    print("=== 示例1: 基于两段文本搜索DD条目 ===")
    text1 = "客户账户余额"
    text2 = "查询客户的账户余额信息"
    knowledge_id = "test_kb_001"

    try:
        search_result = await manager.search_dd_by_texts(
            field1=text1,
            field2=text2,
            knowledge_id=knowledge_id,
            similarity_threshold=0.6,
        )

        print(f"搜索完成，总共找到 {search_result.total_count} 个结果")
        print(f"RDB精确匹配: {len(search_result.rdb_exact_matches)} 个")
        print(f"VDB向量匹配: {len(search_result.vdb_similar_matches)} 个")
        print(f"执行时间: {search_result.execution_time_ms:.2f}ms")

        # 显示部分结果
        for i, entry in enumerate(search_result.get_all_entries()[:3]):
            print(f"\n结果 {i + 1}:")
            print(f"  文件: {entry.file_name}")
            print(f"  字段: {entry.field_name} ({entry.field_code})")
            print(f"  值: {entry.field_value}")
            print(f"  类别: {entry.field_category.value}")
            print(f"  匹配类型: {entry.match_type.value}")
            if entry.similarity_score:
                print(f"  相似度: {entry.similarity_score:.3f}")

    except Exception as e:
        print(f"搜索失败: {e}")
    #
    # # 示例2: 计算文本相似度
    # print("\n=== 示例2: 计算文本相似度 ===")
    # try:
    #     similarities = await manager.calculate_text_similarity(
    #         text1="客户账户余额查询",
    #         text2="查询客户账户的余额信息",
    #         knowledge_id=knowledge_id
    #     )
    #
    #     for sim in similarities:
    #         print(f"{sim.method}: {sim.similarity_score:.3f}")
    #
    # except Exception as e:
    #     print(f"相似度计算失败: {e}")
    #
    # # 示例3: 按类别搜索
    # print("\n=== 示例3: 按类别搜索DD条目 ===")
    # try:
    #     category_result = await manager.search_dd_by_category(
    #         text="账户余额",
    #         knowledge_id=knowledge_id,
    #         categories=[DDFieldCategory.A, DDFieldCategory.B],
    #         max_results=5
    #     )
    #
    #     print(f"类别搜索完成，找到 {category_result.total_count} 个结果")
    #
    # except Exception as e:
    #     print(f"类别搜索失败: {e}")


def main():
    """主函数"""
    print("业务功能模块演示")
    print("================")

    # 运行异步示例
    asyncio.run(example_usage())


if __name__ == "__main__":
    main()

"""
请求适配器模块

企业级最佳实践：
1. 统一的请求转换逻辑
2. 类型安全的转换方法
3. 可扩展的适配器架构
4. 性能优化的缓存机制
"""

from typing import Dict, Any, Union, Optional, List
from functools import lru_cache
import logging

logger = logging.getLogger(__name__)

# 导入RDB抽象层实体
from .....base.rdb import (
    QueryRequest, InsertRequest, UpdateRequest, DeleteRequest,
    QueryFilter, QueryFilterGroup, QuerySort,
    ComparisonOperator, LogicalOperator, SortOrder
)


class RequestAdapter:
    """
    企业级请求适配器
    
    职责：
    1. 统一处理Dict到实体的转换
    2. 提供类型安全的转换方法
    3. 支持复杂的嵌套结构转换
    4. 缓存转换结果提升性能
    """
    
    def __init__(self):
        self._conversion_cache = {}
    
    def adapt_query(self, data: Union[QueryRequest, Dict[str, Any]]) -> QueryRequest:
        """适配查询请求"""
        if isinstance(data, QueryRequest):
            return data
        
        if not isinstance(data, dict):
            raise ValueError("Query input must be QueryRequest or dict")
        
        return QueryRequest(
            table=data['table'],
            columns=data.get('columns'),
            filters=self._convert_filters(data.get('filters')),
            sorts=self._convert_sorts(data.get('sorts')),
            limit=data.get('limit'),
            offset=data.get('offset')
        )
    
    def adapt_insert(self, data: Union[InsertRequest, Dict[str, Any]]) -> InsertRequest:
        """适配插入请求"""
        if isinstance(data, InsertRequest):
            return data
        
        if not isinstance(data, dict):
            raise ValueError("Insert input must be InsertRequest or dict")
        
        return InsertRequest(
            table=data['table'],
            data=data['data']
        )
    
    def adapt_update(self, data: Union[UpdateRequest, Dict[str, Any]]) -> UpdateRequest:
        """适配更新请求"""
        if isinstance(data, UpdateRequest):
            return data
        
        if not isinstance(data, dict):
            raise ValueError("Update input must be UpdateRequest or dict")
        
        return UpdateRequest(
            table=data['table'],
            data=data['data'],
            filters=self._convert_filters(data.get('filters'))
        )
    
    def adapt_delete(self, data: Union[DeleteRequest, Dict[str, Any]]) -> DeleteRequest:
        """适配删除请求"""
        if isinstance(data, DeleteRequest):
            return data
        
        if not isinstance(data, dict):
            raise ValueError("Delete input must be DeleteRequest or dict")
        
        return DeleteRequest(
            table=data['table'],
            filters=self._convert_filters(data.get('filters'))
        )
    
    def _convert_filters(self, filters_data: Any) -> Optional[Union[QueryFilter, QueryFilterGroup]]:
        """转换过滤器数据"""
        if filters_data is None:
            return None
        
        if isinstance(filters_data, (QueryFilter, QueryFilterGroup)):
            return filters_data
        
        if isinstance(filters_data, dict):
            # 简单字典格式: {"name": "John", "age": 25}
            if self._is_simple_filter_dict(filters_data):
                return self._create_simple_filter_group(filters_data)
            
            # 复杂格式: {"operator": "AND", "conditions": [...]}
            if 'operator' in filters_data and 'conditions' in filters_data:
                return self._create_complex_filter_group(filters_data)
        
        raise ValueError(f"Unsupported filters format: {type(filters_data)}")
    
    def _is_simple_filter_dict(self, data: dict) -> bool:
        """检查是否为简单过滤器字典 - 改进判断逻辑"""
        # 检查是否包含明显的复杂结构标识符
        for key, value in data.items():
            # 如果key以$开头，说明是操作符，不是简单字典
            if key.startswith('$'):
                return False
            
            # 如果value是字典且包含$操作符，说明是复杂条件
            if isinstance(value, dict) and any(k.startswith('$') for k in value.keys()):
                return False
            
            # 如果value是字典且包含特定的复杂结构关键字
            if isinstance(value, dict) and any(k in value for k in ['operator', 'conditions', 'field', 'op']):
                return False
        
        # 检查值类型是否为简单类型（保留原有逻辑作为补充）
        return all(
            isinstance(v, (str, int, float, bool, type(None), list, tuple)) 
            for v in data.values()
        )
    
    def _create_simple_filter_group(self, data: dict) -> QueryFilterGroup:
        """创建简单过滤器组"""
        filters = []
        for k, v in data.items():
            # 智能检测操作符
            operator = self._detect_operator_from_value(v)
            filters.append(QueryFilter(field=k, operator=operator, value=v))

        return QueryFilterGroup(
            operator=LogicalOperator.AND,
            filters=filters
        )

    def _detect_operator_from_value(self, value: Any) -> ComparisonOperator:
        """根据值智能检测操作符 - 更保守的LIKE检测逻辑"""
        if isinstance(value, str):
            # 只有明显的通配符模式才使用LIKE：
            # 1. 包含百分号 % (任意长度通配符)
            # 2. 以 % 开头或结尾的字符串
            # 3. % 和 _ 混合使用的明显模式
            if '%' in value:
                return ComparisonOperator.LIKE
            # 排除纯下划线作为业务标识符的情况，只有特定模式才认为是通配符
            elif '_' in value and (
                value.startswith('_') or value.endswith('_') or 
                # 如果下划线前后都有%，说明是通配符模式
                ('%' in value and '_' in value)
            ):
                return ComparisonOperator.LIKE
        elif value is None:
            return ComparisonOperator.IS_NULL
        elif isinstance(value, (list, tuple)):
            return ComparisonOperator.IN

        # 默认使用精确匹配
        return ComparisonOperator.EQ
    
    def _create_complex_filter_group(self, data: dict) -> QueryFilterGroup:
        """创建复杂过滤器组"""
        operator_map = {
            'AND': LogicalOperator.AND,
            'OR': LogicalOperator.OR
        }
        
        comparison_map = {
            'EQ': ComparisonOperator.EQ,
            'NE': ComparisonOperator.NE,
            'GT': ComparisonOperator.GT,
            'GTE': ComparisonOperator.GTE,
            'LT': ComparisonOperator.LT,
            'LTE': ComparisonOperator.LTE,
            'IN': ComparisonOperator.IN,
            'NOT_IN': ComparisonOperator.NOT_IN,
            'LIKE': ComparisonOperator.LIKE,
            'BETWEEN': ComparisonOperator.BETWEEN,
            'IS_NULL': ComparisonOperator.IS_NULL,
            'IS_NOT_NULL': ComparisonOperator.IS_NOT_NULL,
        }
        
        operator = operator_map.get(data['operator'].upper(), LogicalOperator.AND)
        conditions = []
        
        for cond in data['conditions']:
            if isinstance(cond, dict):
                if 'field' in cond:
                    # 这是一个单个过滤条件
                    op_str = cond.get('op', 'eq').upper()
                    operator_enum = comparison_map.get(op_str, ComparisonOperator.EQ)
                    conditions.append(QueryFilter(
                        field=cond['field'],
                        operator=operator_enum,
                        value=cond.get('value')
                    ))
                elif 'operator' in cond and 'conditions' in cond:
                    # 这是一个嵌套的过滤器组，递归处理
                    nested_group = self._create_complex_filter_group(cond)
                    conditions.append(nested_group)

        return QueryFilterGroup(operator=operator, filters=conditions)
    
    def _convert_sorts(self, sorts_data: Any) -> Optional[List[QuerySort]]:
        """转换排序数据"""
        if sorts_data is None:
            return None
        
        if isinstance(sorts_data, list) and all(isinstance(s, QuerySort) for s in sorts_data):
            return sorts_data
        
        if isinstance(sorts_data, list):
            result = []
            for sort_item in sorts_data:
                if isinstance(sort_item, dict) and 'field' in sort_item:
                    order_str = sort_item.get('order', 'asc').lower()
                    order = SortOrder.DESC if order_str == 'desc' else SortOrder.ASC
                    result.append(QuerySort(field=sort_item['field'], order=order))
                elif isinstance(sort_item, str):
                    # 简单字符串格式: "name" 或 "name:desc"
                    if ':' in sort_item:
                        field, order_str = sort_item.split(':', 1)
                        order = SortOrder.DESC if order_str.lower() == 'desc' else SortOrder.ASC
                    else:
                        field = sort_item
                        order = SortOrder.ASC
                    result.append(QuerySort(field=field, order=order))
            return result
        
        raise ValueError(f"Unsupported sorts format: {type(sorts_data)}")


# 全局适配器实例
_global_adapter = RequestAdapter()


def get_request_adapter() -> RequestAdapter:
    """获取全局请求适配器实例"""
    return _global_adapter

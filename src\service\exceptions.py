class ServiceError(Exception):
    """服务层基础异常"""
    pass


class ConfigError(ServiceError):
    """配置相关异常"""
    pass


class ClientError(ServiceError):
    """客户端相关异常"""
    pass


class ProviderError(ServiceError):
    """连接池提供者相关异常"""
    pass


class ConnectionError(ServiceError):
    """连接相关异常"""
    pass


class ValidationError(ServiceError):
    """验证相关异常"""
    pass


class LifecycleError(ServiceError):
    """生命周期管理异常"""
    pass

#!/usr/bin/env python3
"""
检查kb_knowledge表的实际结构
"""

import asyncio
import logging
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '../../..')
sys.path.insert(0, src_dir)

from service import get_client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def check_kb_knowledge_structure():
    """检查kb_knowledge表结构"""
    try:
        # 获取MySQL客户端
        mysql_client = await get_client('database.rdbs.mysql')
        
        # 检查kb_knowledge表结构
        logger.info("🔍 检查kb_knowledge表结构")
        desc_sql = "DESCRIBE kb_knowledge"
        desc_result = await mysql_client.afetch_all(desc_sql)
        
        if hasattr(desc_result, 'data'):
            columns = desc_result.data
        else:
            columns = desc_result
        
        logger.info(f"📊 kb_knowledge表字段 ({len(columns)}个):")
        for col in columns:
            if isinstance(col, dict):
                field_name = col.get('Field', 'Unknown')
                field_type = col.get('Type', 'Unknown')
                null_allowed = col.get('Null', 'Unknown')
                default_value = col.get('Default', 'Unknown')
                logger.info(f"  - {field_name}: {field_type} (Null: {null_allowed}, Default: {default_value})")
        
    except Exception as e:
        logger.error(f"❌ 检查失败: {e}")


if __name__ == "__main__":
    asyncio.run(check_kb_knowledge_structure())

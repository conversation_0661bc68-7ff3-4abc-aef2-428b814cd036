"""RDB客户端适配器 - 让现有代码兼容rdb_client"""

from sqlalchemy.engine import Engine
from typing import Optional
from loguru import logger
from utils.db.mschema.db_source import HITLSQLDatabase

class RDBAdapter:
    """RDB客户端适配器"""
    
    @staticmethod
    def get_engine_from_rdb_client(rdb_client=None) -> Engine:
        """从rdb_client获取SQLAlchemy Engine"""
        if rdb_client is None:
            from utils.db.common.clients import rdb_client as default_client
            rdb_client = default_client
        
        try:
            # 检查rdb_client是否有engine属性
            if hasattr(rdb_client, 'engine') and rdb_client.engine is not None:
                logger.info("从rdb_client获取SQLAlchemy engine")
                return rdb_client.engine
            
            # 检查是否有_engine属性
            elif hasattr(rdb_client, '_engine') and rdb_client._engine is not None:
                logger.info("从rdb_client获取_engine")  
                return rdb_client._engine
                
            else:
                raise RuntimeError("无法从rdb_client获取SQLAlchemy engine")
                
        except Exception as e:
            logger.error(f"从rdb_client获取engine失败: {e}")
            raise

    @staticmethod
    def create_hitl_database(db_name: str = 'default', rdb_client=None, **kwargs) -> 'HITLSQLDatabase':
        """从rdb_client创建HITLSQLDatabase实例"""
        
        
        engine = RDBAdapter.get_engine_from_rdb_client(rdb_client)
        return HITLSQLDatabase(engine=engine, db_name=db_name, **kwargs)


__all__ = ['RDBAdapter'] 
#!/usr/bin/env python3
"""
DD-B增强处理API简单使用示例

展示如何调用单个report_code的DD-B处理接口
"""

import asyncio
import json
import httpx


async def simple_dd_b_example():
    """简单的DD-B API调用示例"""
    
    # API配置
    BASE_URL = "http://localhost:30337"
    
    # 请求参数（您只需要修改这两个参数）
    request_data = {
        "report_code": "S71_ADS_RELEASE_V0",  # 您的报表代码
        "dept_id": "30239",                   # 您的部门ID
        "enable_auto_fill": True,             # 启用自动填充
        "return_original_data": False         # 不返回原始数据
    }
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            print("🚀 开始调用DD-B增强处理API...")
            print(f"📋 请求参数: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
            
            # 调用API
            response = await client.post(
                f"{BASE_URL}/api/dd/dd-b/process",
                json=request_data
            )
            
            # 处理响应
            if response.status_code == 200:
                result = response.json()
                print("✅ API调用成功!")
                print(f"📊 响应结果:")
                print(f"  状态码: {result.get('code')}")
                print(f"  消息: {result.get('msg')}")
                
                if result.get('data'):
                    data = result['data']
                    print(f"  找到记录: {data.get('total_records_found')} 条")
                    print(f"  处理记录: {data.get('records_processed')} 条")
                    print(f"  填充字段: {data.get('total_fields_filled')} 个")
                    print(f"  处理耗时: {data.get('processing_time_ms'):.2f}ms")
                    print(f"  处理状态: {data.get('status')}")
                    
                    # 显示填充详情（前5个）
                    if data.get('fill_details'):
                        print(f"  填充详情（前5个）:")
                        for i, detail in enumerate(data['fill_details'][:5]):
                            print(f"    {i+1}. {detail['field_name']}: '{detail['original_value']}' -> '{detail['filled_value']}'")
                
                return True
            
            else:
                print(f"❌ API调用失败: HTTP {response.status_code}")
                print(f"错误响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 调用异常: {e}")
            return False


async def health_check_example():
    """健康检查示例"""
    
    BASE_URL = "http://localhost:30337"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            print("🔍 检查DD-B模块健康状态...")
            
            response = await client.get(f"{BASE_URL}/api/dd/dd-b/health")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 健康检查通过!")
                print(f"📊 服务状态:")
                
                if result.get('data'):
                    data = result['data']
                    print(f"  整体状态: {data.get('status')}")
                    print(f"  MySQL客户端: {data.get('mysql_client')}")
                    print(f"  PGVector客户端: {data.get('pgvector_client')}")
                    print(f"  Embedding客户端: {data.get('embedding_client')}")
                    print(f"  DD-B模块: {data.get('dd_b_module')}")
                
                return True
            
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False


async def main():
    """主函数"""
    print("🎯 DD-B增强处理API使用示例")
    print("=" * 50)
    
    # 1. 健康检查
    print("\n📋 1. 健康检查")
    health_ok = await health_check_example()
    
    if not health_ok:
        print("⚠️ 健康检查失败，请检查服务状态")
        return False
    
    # 2. DD-B处理
    print("\n📋 2. DD-B增强处理")
    process_ok = await simple_dd_b_example()
    
    if process_ok:
        print("\n🎉 DD-B API调用示例完成!")
        return True
    else:
        print("\n❌ DD-B API调用失败")
        return False


if __name__ == "__main__":
    # 运行示例
    success = asyncio.run(main())
    exit(0 if success else 1)

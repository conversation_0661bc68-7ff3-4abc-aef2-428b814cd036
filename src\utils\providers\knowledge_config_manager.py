"""
知识库配置管理器 (Knowledge Configuration Manager)

负责管理知识库级别的动态配置，与Service层无缝集成：
- 创建知识库时自动生成默认配置
- Hash验证优化：相同配置使用默认路径，不同配置使用数据库路径
- 格式兼容：确保数据库配置与Service层格式一致
- 与Service层集成：支持配置的动态切换
"""

import json
from typing import Dict, Any, Optional, Union
from dataclasses import asdict
from omegaconf import DictConfig, OmegaConf
import logging

logger = logging.getLogger(__name__)


class KnowledgeConfigManager:
    """
    知识库配置管理器

    核心功能：
    1. 默认配置管理：从Service层自动生成知识库默认配置
    2. Hash验证优化：避免重复存储，优化查询性能
    3. 格式兼容性：确保数据库配置能构造相同的ServiceClient
    4. 动态切换支持：运行时根据配置差异选择最优路径
    """

    def __init__(self, rdb_client=None):
        """
        初始化配置管理器

        Args:
            rdb_client: 关系数据库客户端，如果不提供则使用Service层获取
        """
        self.rdb_client = rdb_client
        self._default_configs = {
            'MODEL': {
                'llm': 'opentrek',
                'embedding': 'moka-m3e-base',
                'model_parameters': {
                    'max_tokens': 8192,
                    'temperature': 0.9
                }
            },
            'SEARCH': {
                'similarity_threshold': 0.7,
                'top_k': 10,
                'search_method': 'semantic',
                'enable_rerank': True
            },
            'RAG': {
                'chunk_size': 800,
                'chunk_overlap': 100,
                'citation_limit': 5000
            }
        }

    async def _get_rdb_client(self):
        """获取RDB客户端"""
        if self.rdb_client is None:
            from service import get_client
            self.rdb_client = await get_client("database.rdbs.mysql", priority="standard")
        return self.rdb_client

    def _build_default_model_config(self) -> Dict[str, Any]:
        """
        构建默认模型配置 - 与Service层格式兼容

        这个方法构建的配置格式与Service层完全兼容，
        确保现有代码可以无缝使用
        """
        return self._default_configs['MODEL'].copy()

    def _build_default_search_config(self) -> Dict[str, Any]:
        """构建默认搜索配置"""
        return self._default_configs['SEARCH'].copy()

    def _build_default_rag_config(self) -> Dict[str, Any]:
        """构建默认RAG配置"""
        return self._default_configs['RAG'].copy()
    
    async def create_default_configs_for_knowledge(self, knowledge_id: str) -> bool:
        """
        为新创建的知识库生成默认配置（管理优化版）

        Args:
            knowledge_id: 知识库ID

        Returns:
            bool: 创建成功返回True
        """
        try:
            # 默认配置也存储具体数据，以方便管理
            configs = [
                {
                    'knowledge_id': knowledge_id,
                    'config_type': 'MODEL',
                    'is_default': 1,
                    'config_data': self._build_default_model_config(),
                    'description': '使用Service层默认模型配置'
                },
                {
                    'knowledge_id': knowledge_id,
                    'config_type': 'SEARCH',
                    'is_default': 1,
                    'config_data': self._build_default_search_config(),
                    'description': '使用系统默认搜索配置'
                },
                {
                    'knowledge_id': knowledge_id,
                    'config_type': 'RAG',
                    'is_default': 1,
                    'config_data': self._build_default_rag_config(),
                    'description': '使用系统默认RAG配置'
                }
            ]

            # 批量插入配置
            for cfg in configs:
                await self._insert_config(cfg)
            
            logger.info(f"成功为知识库 {knowledge_id} 创建默认配置快照")
            return True
            
        except Exception as e:
            logger.error(f"为知识库 {knowledge_id} 创建默认配置失败: {e}")
            return False
    
    async def _insert_config(self, config_data: Dict[str, Any]):
        """插入单个配置记录（管理优化版）"""
        sql = """
        INSERT INTO knowledge_configs (
            knowledge_id, config_type, is_default, config_data, description
        ) VALUES (
            %(knowledge_id)s, %(config_type)s, %(is_default)s, %(config_data)s, %(description)s
        ) ON DUPLICATE KEY UPDATE
            is_default = VALUES(is_default),
            config_data = VALUES(config_data),
            description = VALUES(description),
            update_time = CURRENT_TIMESTAMP
        """

        # config_data必须是JSON字符串
        params = config_data.copy()
        params['config_data'] = json.dumps(params['config_data'], ensure_ascii=False)

        rdb_client = await self._get_rdb_client()
        await rdb_client.aexecute(sql, params)
    
    async def get_effective_config(self, knowledge_id: str, config_type: str) -> Dict[str, Any]:
        """
        获取知识库的有效配置（管理优化版）

        核心逻辑：
        1. 总是从数据库获取配置，作为配置的唯一真实来源
        2. 如果数据库没有记录，则自动创建并存入默认配置

        Args:
            knowledge_id: 知识库ID
            config_type: 配置类型 ('MODEL', 'SEARCH', 'RAG')

        Returns:
            Dict: 有效的配置数据，与Service层格式兼容
        """
        try:
            sql = """
            SELECT config_data FROM knowledge_configs
            WHERE knowledge_id = %s AND config_type = %s
            """

            rdb_client = await self._get_rdb_client()
            result = await rdb_client.afetch_one(sql, (knowledge_id, config_type))

            if not result:
                logger.warning(f"知识库 {knowledge_id} 缺少 {config_type} 配置，自动创建默认配置快照")
                await self.create_default_configs_for_knowledge(knowledge_id)
                # 重新查询以获取新创建的配置
                result = await rdb_client.afetch_one(sql, (knowledge_id, config_type))

            config_data = result['config_data']

            if isinstance(config_data, str):
                config_data = json.loads(config_data)

            return config_data

        except Exception as e:
            logger.error(f"获取知识库 {knowledge_id} 的 {config_type} 配置失败: {e}")
            # 降级到内存中的默认配置
            return self._get_default_config_by_type(config_type)
    
    def _get_default_config_by_type(self, config_type: str) -> Dict[str, Any]:
        """根据配置类型获取默认配置"""
        if config_type == 'MODEL':
            return self._build_default_model_config()
        elif config_type == 'SEARCH':
            return self._build_default_search_config()
        elif config_type == 'RAG':
            return self._build_default_rag_config()
        else:
            raise ValueError(f"不支持的配置类型: {config_type}")
    
    async def update_config(self, knowledge_id: str, config_type: str,
                           new_config: Dict[str, Any], description: str = None) -> bool:
        """
        更新知识库配置（管理优化版）

        Args:
            knowledge_id: 知识库ID
            config_type: 配置类型
            new_config: 新的配置数据
            description: 配置描述

        Returns:
            bool: 更新成功返回True
        """
        try:
            # 检查是否与默认配置相同
            default_config = self._get_default_config_by_type(config_type)
            is_default = 1 if new_config == default_config else 0

            if description is None:
                description = "自定义配置" if not is_default else "恢复为默认配置"

            config_data = {
                'knowledge_id': knowledge_id,
                'config_type': config_type,
                'config_data': new_config,
                'is_default': is_default,
                'description': description
            }

            await self._insert_config(config_data)

            logger.info(f"成功更新知识库 {knowledge_id} 的 {config_type} 配置")
            return True

        except Exception as e:
            logger.error(f"更新知识库 {knowledge_id} 的 {config_type} 配置失败: {e}")
            return False
    
    async def get_knowledge_all_configs(self, knowledge_id: str) -> Dict[str, Dict[str, Any]]:
        """
        获取知识库的所有配置

        Args:
            knowledge_id: 知识库ID

        Returns:
            Dict: 包含所有配置类型的字典
        """
        result = {}
        for config_type in ['MODEL', 'SEARCH', 'RAG']:
            result[config_type.lower()] = await self.get_effective_config(knowledge_id, config_type)

        return result
    
    async def is_using_default_config(self, knowledge_id: str, config_type: str) -> bool:
        """
        检查知识库是否使用默认配置（简化版）

        Args:
            knowledge_id: 知识库ID
            config_type: 配置类型

        Returns:
            bool: 使用默认配置返回True
        """
        try:
            sql = """
            SELECT is_default FROM knowledge_configs
            WHERE knowledge_id = %s AND config_type = %s
            """

            rdb_client = await self._get_rdb_client()
            result = await rdb_client.afetch_one(sql, (knowledge_id, config_type))

            if not result:
                return True  # 没有配置记录，算作使用默认配置

            return bool(result['is_default'])

        except Exception as e:
            logger.error(f"检查默认配置状态失败: {e}")
            return True  # 出错时保守返回True


# 全局配置管理器实例
knowledge_config_manager = KnowledgeConfigManager()


# 便捷函数（异步版本）
async def create_default_configs_for_knowledge(knowledge_id: str) -> bool:
    """为知识库创建默认配置的便捷函数"""
    return await knowledge_config_manager.create_default_configs_for_knowledge(knowledge_id)


async def get_knowledge_config(knowledge_id: str, config_type: str) -> Dict[str, Any]:
    """获取知识库配置的便捷函数"""
    return await knowledge_config_manager.get_effective_config(knowledge_id, config_type)


async def update_knowledge_config(knowledge_id: str, config_type: str,
                                 new_config: Dict[str, Any], description: str = None) -> bool:
    """更新知识库配置的便捷函数"""
    return await knowledge_config_manager.update_config(knowledge_id, config_type, new_config, description)


__all__ = [
    'KnowledgeConfigManager',
    'knowledge_config_manager', 
    'create_default_configs_for_knowledge',
    'get_knowledge_config',
    'update_knowledge_config'
] 
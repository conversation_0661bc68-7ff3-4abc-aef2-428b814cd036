"""
Parser Info到Candidate Columns转换步骤
将parser_info中的columns和tables信息转换为candidate_columns格式
"""

import logging
from typing import Dict, Any, Set, List
from collections import defaultdict

logger = logging.getLogger(__name__)

from pipeline.core.base_step import BaseStep
from pipeline.core.context import PipelineContext


class ParserInfoToCandidateColumnsStep(BaseStep):
    """
    Parser Info到Candidate Columns转换步骤
    
    将parser_info中的结构化信息转换为Pipeline标准的candidate_columns格式
    
    输入格式：
    parser_info = {
        "columns": {"column_name": "table_name_or_empty", ...},
        "tables": {"table1", "table2", ...},  # Set of table names
        "where": [...],
        "join": [...],
        "value": {...}
    }
    
    输出格式：
    candidate_columns = {
        "table1": ["column1", "column2"],
        "table2": ["column3", "column4"],
        ...
    }
    """

    def __init__(self):
        super().__init__(
            name="parser_info_to_candidate_columns",
            description="将parser_info转换为candidate_columns格式"
        )

    async def preprocess(self, context: PipelineContext) -> Dict[str, Any]:
        """阶段1: 预处理 - 获取和验证parser_info"""
        parser_info = context.get("parser_info", {})
        if not parser_info:
            raise ValueError("parser_info不能为空")

        return {
            "parser_info": parser_info
        }

    async def process(self, preprocessed_data: Dict[str, Any], context: PipelineContext) -> Dict[str, Any]:
        """阶段2: 核心处理 - 执行转换"""
        parser_info = preprocessed_data["parser_info"]

        # 执行转换
        candidate_columns = self._transform_parser_info_to_candidate_columns(parser_info)

        logger.info(f"转换结果: {len(candidate_columns)}个表，"
                   f"共{sum(len(cols) for cols in candidate_columns.values())}个列")

        return {
            "candidate_columns": candidate_columns
        }

    async def postprocess(self, processed_result: Dict[str, Any], context: PipelineContext) -> Dict[str, Any]:
        """阶段4: 后处理 - 更新上下文"""
        candidate_columns = processed_result["candidate_columns"]

        # 更新上下文
        context.set("candidate_columns", candidate_columns)

        # 设置schema_generation_params以供SchemaGeneratorStep使用
        schema_generation_params = {
            "candidate_columns": candidate_columns,
            "source_type": "source",
            "is_final": True
        }
        context.set("schema_generation_params", schema_generation_params)

        logger.debug(f"已设置schema_generation_params: candidate_columns包含{len(candidate_columns)}个表")

        return candidate_columns

    def _transform_parser_info_to_candidate_columns(self, parser_info: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        将parser_info转换为candidate_columns格式
        
        Args:
            parser_info: 包含columns和tables信息的字典
            
        Returns:
            candidate_columns: 按表分组的列信息
        """
        columns_info = parser_info.get("columns", {})
        tables_info = parser_info.get("tables", set())
        
        if not columns_info:
            logger.warning("parser_info中没有columns信息")
            return {}
        
        # 确保tables_info是集合格式
        if isinstance(tables_info, list):
            tables_info = set(tables_info)
        elif not isinstance(tables_info, set):
            tables_info = set()
        
        logger.debug(f"输入columns: {columns_info}")
        logger.debug(f"输入tables: {tables_info}")
        
        # 按表分组列
        candidate_columns = defaultdict(list)
        unassigned_columns = []
        
        for column_name, table_name in columns_info.items():
            if table_name and table_name.strip():
                # 列已分配给特定表
                table_name = table_name.strip()
                candidate_columns[table_name].append(column_name)
                logger.debug(f"列 {column_name} 分配给表 {table_name}")
            else:
                # 列未分配给特定表
                unassigned_columns.append(column_name)
                logger.debug(f"列 {column_name} 未分配给特定表")
        
        # 处理未分配的列
        if unassigned_columns:
            logger.info(f"发现 {len(unassigned_columns)} 个未分配的列: {unassigned_columns}")
            
            if tables_info:
                # 如果有表信息，将未分配的列分配给第一个表
                first_table = next(iter(tables_info))
                candidate_columns[first_table].extend(unassigned_columns)
                logger.info(f"将未分配的列分配给表 {first_table}")
            else:
                # 如果没有表信息，创建一个默认表
                default_table = "default_table"
                candidate_columns[default_table].extend(unassigned_columns)
                logger.warning(f"没有表信息，创建默认表 {default_table}")
        
        # 确保所有在tables_info中的表都在结果中（即使没有列）
        for table_name in tables_info:
            if table_name not in candidate_columns:
                candidate_columns[table_name] = []
                logger.debug(f"表 {table_name} 没有分配的列，添加空列表")
        
        # 转换为普通字典并去重
        result = {}
        for table_name, columns in candidate_columns.items():
            # 去重并保持顺序
            unique_columns = []
            seen = set()
            for col in columns:
                if col not in seen:
                    unique_columns.append(col)
                    seen.add(col)
            result[table_name] = unique_columns
        
        logger.info(f"转换完成: {len(result)}个表")
        for table_name, columns in result.items():
            logger.debug(f"表 {table_name}: {len(columns)}个列 - {columns}")
        
        return result

    def format_display_result(self, final_result: Dict[str, List[str]]) -> str:
        """
        格式化结果用于显示
        
        Args:
            final_result: 转换后的candidate_columns
            
        Returns:
            格式化后的字符串
        """
        if not final_result or not isinstance(final_result, dict):
            return "❌ 未生成有效的candidate_columns或结果格式不正确。"
        
        display_parts = ["## Parser Info转换结果：\n"]
        
        total_tables = len(final_result)
        total_columns = sum(len(cols) for cols in final_result.values())
        
        display_parts.append(f"### 总览：")
        display_parts.append(f"- 表数量: {total_tables}")
        display_parts.append(f"- 列数量: {total_columns}")
        display_parts.append("")
        
        display_parts.append(f"### 详细信息：")
        for table_name, columns in final_result.items():
            display_parts.append(f"**{table_name}** ({len(columns)}列):")
            if columns:
                for col in columns:
                    display_parts.append(f"  - {col}")
            else:
                display_parts.append(f"  - (无列)")
            display_parts.append("")
        
        return "\n".join(display_parts)

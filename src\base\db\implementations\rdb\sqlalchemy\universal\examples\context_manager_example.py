#!/usr/bin/env python3
"""
上下文管理器使用示例

展示如何使用上下文管理器来自动管理数据库连接，
避免连接泄漏和资源管理问题。
"""

import sys
import os
import asyncio
import logging

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '../../../../../../..')
sys.path.insert(0, os.path.abspath(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def example_1_basic_context_manager():
    """示例1: 基本上下文管理器使用"""
    print("🧪 示例1: 基本上下文管理器使用")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.context_manager import mysql_client
    
    # 使用上下文管理器，自动管理连接
    with mysql_client(
        host="**************", port=37615, database="hsbc_data",
        username="root", password="idea@1008"
    ) as client:
        print(f"✅ 连接状态: {client.is_connected()}")
        
        # 执行简单查询
        result = client.execute("SELECT 1 as test_value")
        print(f"✅ 查询成功: 影响 {result.affected_rows} 行")

        # 执行fetch查询
        fetch_result = client.fetch_all("SELECT 'context_manager' as method, 123 as number")
        print(f"✅ Fetch查询成功: 获取 {len(fetch_result.data)} 条记录")
        if fetch_result.data:
            row = fetch_result.data[0]
            print(f"   结果: {row}")
    
    # 连接自动关闭
    print(f"✅ 上下文退出后连接状态: {client.is_connected()}")


async def example_2_async_context_manager():
    """示例2: 异步上下文管理器使用"""
    print("\n🧪 示例2: 异步上下文管理器使用")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.context_manager import mysql_client
    
    # 使用异步上下文管理器
    async with mysql_client(
        host="**************", port=37615, database="hsbc_data",
        username="root", password="idea@1008"
    ) as client:
        print(f"✅ 异步连接状态: {client.is_connected()}")
        
        # 执行异步查询
        result = await client.aexecute("SELECT 2 as async_value")
        print(f"✅ 异步查询成功: 影响 {result.affected_rows} 行")

        # 执行异步fetch查询
        fetch_result = await client.afetch_all("SELECT 'async_context' as method, 456 as number")
        print(f"✅ 异步Fetch查询成功: 获取 {len(fetch_result.data)} 条记录")
        if fetch_result.data:
            row = fetch_result.data[0]
            print(f"   结果: {row}")
    
    # 连接自动关闭
    print(f"✅ 异步上下文退出后连接状态: {client.is_connected()}")


def example_3_exception_safety():
    """示例3: 异常安全的连接管理"""
    print("\n🧪 示例3: 异常安全的连接管理")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.context_manager import mysql_client
    
    try:
        with mysql_client(
            host="**************", port=37615, database="hsbc_data",
            username="root", password="idea@1008"
        ) as client:
            print(f"✅ 连接建立: {client.is_connected()}")
            
            # 执行正常查询
            result = client.execute("SELECT 1 as test")
            print(f"✅ 正常查询成功: {result.affected_rows}")
            
            # 故意引发异常
            client.execute("INVALID SQL SYNTAX")
            
    except Exception as e:
        print(f"✅ 异常被正确捕获: {type(e).__name__}")
        print(f"✅ 连接在异常后自动清理: {client.is_connected()}")


def example_4_multiple_clients():
    """示例4: 多个客户端的隔离管理"""
    print("\n🧪 示例4: 多个客户端的隔离管理")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.context_manager import (
        mysql_client, get_connection_stats
    )
    
    print(f"📊 初始连接统计: {get_connection_stats()}")
    
    # 嵌套使用多个客户端
    with mysql_client(
        host="**************", port=37615, database="hsbc_data",
        username="root", password="idea@1008"
    ) as client1:
        print(f"✅ 客户端1连接: {client1.is_connected()}")
        print(f"📊 连接统计: {get_connection_stats()}")
        
        with mysql_client(
            host="**************", port=37615, database="hsbc_data",
            username="root", password="idea@1008"
        ) as client2:
            print(f"✅ 客户端2连接: {client2.is_connected()}")
            print(f"📊 连接统计: {get_connection_stats()}")
            
            # 两个客户端独立工作
            result1 = client1.execute("SELECT 'client1' as source")
            result2 = client2.execute("SELECT 'client2' as source")
            
            print(f"✅ 客户端1查询: {result1.affected_rows}")
            print(f"✅ 客户端2查询: {result2.affected_rows}")
        
        print(f"✅ 客户端2退出后，客户端1仍连接: {client1.is_connected()}")
        print(f"📊 连接统计: {get_connection_stats()}")
    
    print(f"✅ 所有客户端退出")
    print(f"📊 最终连接统计: {get_connection_stats()}")


def example_5_manual_vs_automatic():
    """示例5: 手动管理 vs 自动管理对比"""
    print("\n🧪 示例5: 手动管理 vs 自动管理对比")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client
    from base.db.implementations.rdb.sqlalchemy.universal.context_manager import mysql_client
    
    # 手动管理（不推荐）
    print("❌ 手动管理方式（容易出错）:")
    manual_client = create_mysql_client(
        host="**************", port=37615, database="hsbc_data",
        username="root", password="idea@1008"
    )
    try:
        manual_client.connect()
        result = manual_client.execute("SELECT 'manual' as method")
        print(f"   查询成功: {result.affected_rows}")
    finally:
        # 必须记住手动清理
        manual_client.disconnect()
        print(f"   手动断开连接: {manual_client.is_connected()}")
    
    # 自动管理（推荐）
    print("✅ 自动管理方式（推荐）:")
    with mysql_client(
        host="**************", port=37615, database="hsbc_data",
        username="root", password="idea@1008"
    ) as auto_client:
        result = auto_client.execute("SELECT 'automatic' as method")
        print(f"   查询成功: {result.affected_rows}")
        # 无需手动清理，自动管理
    print(f"   自动断开连接: {auto_client.is_connected()}")


def example_6_connection_pool_monitoring():
    """示例6: 连接池监控"""
    print("\n🧪 示例6: 连接池监控")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.context_manager import (
        mysql_client, get_connection_stats, cleanup_all_connections
    )
    
    def get_pool_info(client):
        """获取连接池详细信息"""
        if hasattr(client, 'sync_engine') and client.sync_engine:
            pool = client.sync_engine.pool
            return {
                'size': pool.size(),
                'checked_in': pool.checkedin(),
                'checked_out': pool.checkedout(),
                'overflow': pool.overflow(),
                'pool_type': type(pool).__name__
            }
        return {}
    
    with mysql_client(
        host="**************", port=37615, database="hsbc_data",
        username="root", password="idea@1008"
    ) as client:
        print(f"📊 连接池信息: {get_pool_info(client)}")
        print(f"📊 管理器统计: {get_connection_stats()}")
        
        # 执行一些操作
        for i in range(3):
            result = client.execute(f"SELECT {i+1} as iteration")
            print(f"   操作 {i+1}: {result.affected_rows}")
        
        print(f"📊 操作后连接池: {get_pool_info(client)}")
    
    # 清理所有连接
    cleanup_all_connections()
    print(f"📊 清理后统计: {get_connection_stats()}")


async def main():
    """主函数"""
    print("🚀 上下文管理器使用示例")
    print("=" * 80)
    
    # 运行所有示例
    example_1_basic_context_manager()
    await example_2_async_context_manager()
    example_3_exception_safety()
    example_4_multiple_clients()
    example_5_manual_vs_automatic()
    example_6_connection_pool_monitoring()
    
    print("\n🎉 所有示例运行完成！")
    print("\n💡 总结:")
    print("✅ 使用上下文管理器可以自动管理数据库连接")
    print("✅ 异常情况下连接也会被正确清理")
    print("✅ 多个客户端可以安全地并发使用")
    print("✅ 连接池状态可以被监控和管理")
    print("✅ 推荐使用自动管理而不是手动管理")


if __name__ == "__main__":
    asyncio.run(main())

"""
统一向量数据库基类

基于Milvus设计标准，定义统一的向量数据库抽象接口。
支持PGVector、Milvus、Chroma等向量数据库的统一访问。

设计原则：
1. API命名与Milvus保持一致
2. 参数格式标准化
3. 结果格式统一
4. 支持异步/同步双模式
5. 最大公约数设计，确保各数据库兼容

作者: HSBC Knowledge Team
日期: 2025-01-15
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
import logging

from .core.models import (
    CollectionSchema, Entity, SearchResult, SearchRequest as AnnSearchRequest,
    QueryRequest, GetRequest, InsertRequest, DeleteRequest,
    HybridSearchRequest
)
from .core.exceptions import VectorDBException
from .format_converter import UniversalConverter

logger = logging.getLogger(__name__)


class BaseVectorDB(ABC):
    """
    统一向量数据库基类

    基于Milvus API设计，提供标准化的向量数据库操作接口。
    所有具体实现都应该继承此类并实现相应的方法。
    """

    def __init__(self, db_type: str, **kwargs):
        """
        初始化向量数据库客户端

        Args:
            db_type: 数据库类型 ('milvus', 'pgvector', 'chroma')
            **kwargs: 数据库特定的配置参数
        """
        self.db_type = db_type.lower()
        self.converter = UniversalConverter(self.db_type)
        self._connected = False

    # ==================== 连接管理 ====================

    @abstractmethod
    def connect(self, **kwargs) -> None:
        """连接到向量数据库"""
        pass

    @abstractmethod
    async def aconnect(self, **kwargs) -> None:
        """异步连接到向量数据库"""
        pass

    @abstractmethod
    def disconnect(self) -> None:
        """断开数据库连接"""
        pass

    @abstractmethod
    async def adisconnect(self) -> None:
        """异步断开数据库连接"""
        pass

    @property
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected

    # ==================== 集合管理 ====================

    @abstractmethod
    def create_collection(self, collection_name: str, schema: CollectionSchema,
                         **kwargs) -> bool:
        """
        创建集合

        Args:
            collection_name: 集合名称
            schema: 集合模式定义
            **kwargs: 额外参数（如索引配置等）

        Returns:
            是否创建成功
        """
        pass

    @abstractmethod
    async def acreate_collection(self, collection_name: str, schema: CollectionSchema,
                                **kwargs) -> bool:
        """异步创建集合"""
        pass

    @abstractmethod
    def drop_collection(self, collection_name: str) -> bool:
        """删除集合"""
        pass

    @abstractmethod
    async def adrop_collection(self, collection_name: str) -> bool:
        """异步删除集合"""
        pass

    @abstractmethod
    def has_collection(self, collection_name: str) -> bool:
        """检查集合是否存在"""
        pass

    @abstractmethod
    async def ahas_collection(self, collection_name: str) -> bool:
        """异步检查集合是否存在"""
        pass

    @abstractmethod
    def list_collections(self) -> List[str]:
        """列出所有集合"""
        pass

    @abstractmethod
    async def alist_collections(self) -> List[str]:
        """异步列出所有集合"""
        pass

    # ==================== 数据操作 ====================

    @abstractmethod
    def insert(self, collection_name: str, data: List[Dict[str, Any]],
               partition_name: Optional[str] = None, **kwargs) -> bool:
        """
        插入数据

        Args:
            collection_name: 集合名称
            data: 要插入的数据列表
            partition_name: 分区名称
            **kwargs: 额外参数

        Returns:
            是否插入成功
        """
        pass

    @abstractmethod
    async def ainsert(self, collection_name: str, data: List[Dict[str, Any]],
                     partition_name: Optional[str] = None, **kwargs) -> bool:
        """异步插入数据"""
        pass

    @abstractmethod
    def delete(self, collection_name: str, expr: str,
               partition_name: Optional[str] = None, **kwargs) -> bool:
        """
        删除数据

        Args:
            collection_name: 集合名称
            expr: 删除条件表达式
            partition_name: 分区名称
            **kwargs: 额外参数

        Returns:
            是否删除成功
        """
        pass

    @abstractmethod
    async def adelete(self, collection_name: str, expr: str,
                     partition_name: Optional[str] = None, **kwargs) -> bool:
        """异步删除数据"""
        pass

    # ==================== 查询操作 ====================

    @abstractmethod
    def search(self, collection_name: str, data: List[List[float]],
               anns_field: str, limit: int = 10, expr: Optional[str] = None,
               output_fields: Optional[List[str]] = None,
               search_params: Optional[Dict[str, Any]] = None,
               **kwargs) -> List[List[SearchResult]]:
        """
        向量搜索（Milvus兼容接口）

        Args:
            collection_name: 集合名称
            data: 查询向量列表
            anns_field: 向量字段名
            limit: 返回结果数量
            expr: 过滤表达式
            output_fields: 输出字段列表
            search_params: 搜索参数
            **kwargs: 额外参数

        Returns:
            搜索结果列表，每个查询向量对应一个结果列表
        """
        pass

    @abstractmethod
    async def asearch(self, collection_name: str, data: List[List[float]],
                     anns_field: str, limit: int = 10, expr: Optional[str] = None,
                     output_fields: Optional[List[str]] = None,
                     search_params: Optional[Dict[str, Any]] = None,
                     **kwargs) -> List[List[SearchResult]]:
        """异步向量搜索"""
        pass

    @abstractmethod
    def query(self, collection_name: str, expr: str,
              output_fields: Optional[List[str]] = None,
              limit: Optional[int] = None, offset: Optional[int] = None,
              **kwargs) -> List[Dict[str, Any]]:
        """
        标量查询（Milvus兼容接口）

        Args:
            collection_name: 集合名称
            expr: 查询表达式
            output_fields: 输出字段列表
            limit: 结果数量限制
            offset: 偏移量
            **kwargs: 额外参数

        Returns:
            查询结果列表
        """
        pass

    @abstractmethod
    async def aquery(self, collection_name: str, expr: str,
                    output_fields: Optional[List[str]] = None,
                    limit: Optional[int] = None, offset: Optional[int] = None,
                    **kwargs) -> List[Dict[str, Any]]:
        """异步标量查询"""
        pass

    @abstractmethod
    def get(self, collection_name: str, ids: List[Any],
            output_fields: Optional[List[str]] = None,
            **kwargs) -> List[Dict[str, Any]]:
        """
        根据主键获取数据（Milvus兼容接口）

        Args:
            collection_name: 集合名称
            ids: 主键ID列表
            output_fields: 输出字段列表
            **kwargs: 额外参数

        Returns:
            查询结果列表
        """
        pass

    @abstractmethod
    async def aget(self, collection_name: str, ids: List[Any],
                  output_fields: Optional[List[str]] = None,
                  **kwargs) -> List[Dict[str, Any]]:
        """异步根据主键获取数据"""
        pass

    # ==================== 混合搜索 ====================

    @abstractmethod
    def hybrid_search(self, collection_name: str, reqs: List[AnnSearchRequest],
                     ranker: Any, limit: int = 10, **kwargs) -> List[SearchResult]:
        """
        混合搜索（Milvus兼容接口）

        Args:
            collection_name: 集合名称
            reqs: 搜索请求列表
            ranker: 排序器实例
            limit: 最终返回结果数量
            **kwargs: 额外参数

        Returns:
            排序后的搜索结果列表
        """
        pass

    @abstractmethod
    async def ahybrid_search(self, collection_name: str, reqs: List[AnnSearchRequest],
                            ranker: Any, limit: int = 10, **kwargs) -> List[SearchResult]:
        """异步混合搜索"""
        pass

    # ==================== 工具方法 ====================

    def count(self, collection_name: str, expr: Optional[str] = None,
              **kwargs) -> int:
        """
        统计数据数量

        Args:
            collection_name: 集合名称
            expr: 过滤表达式
            **kwargs: 额外参数

        Returns:
            数据数量
        """
        # 默认实现：通过query获取数量
        try:
            results = self.query(collection_name, expr or "", limit=1, **kwargs)
            return len(results)
        except Exception:
            return 0

    async def acount(self, collection_name: str, expr: Optional[str] = None,
                    **kwargs) -> int:
        """异步统计数据数量"""
        try:
            results = await self.aquery(collection_name, expr or "", limit=1, **kwargs)
            return len(results)
        except Exception:
            return 0

    def validate_connection(self) -> bool:
        """验证连接状态"""
        if not self.is_connected:
            raise VectorDBException("数据库未连接，请先调用connect()方法")
        return True

    def get_db_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        return {
            "db_type": self.db_type,
            "connected": self.is_connected,
            "converter": str(self.converter)
        }

    # ==================== 上下文管理器支持 ====================

    def __enter__(self):
        """同步上下文管理器入口"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """同步上下文管理器出口"""
        self.disconnect()

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.aconnect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.adisconnect()
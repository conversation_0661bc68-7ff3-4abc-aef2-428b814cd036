#!/usr/bin/env python3
"""
DD系统重构后架构测试示例

展示如何正确使用重构后的架构：
1. DDSearch类：提供基础搜索能力
2. ThreeLayerSearchService类：实现业务逻辑
3. 清晰的职责分离
"""

import asyncio
import logging
from typing import Dict, Any, List

# 导入路径修复
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

# 导入企业级日志系统
from utils.common.logger import setup_enterprise_plus_logger

# 导入DD系统组件
from service import get_client
from modules.knowledge.dd.search import DDSearch, SearchField, SearchMode
from modules.knowledge.dd.business.three_layer_search_service import ThreeLayerSearchService

# 设置企业级日志
logger = setup_enterprise_plus_logger(
    name="dd_refactored_architecture_test",
    level="DEBUG"
)


async def test_refactored_architecture():
    """测试重构后的架构"""
    print("🏗️ DD系统重构后架构测试")
    print("=" * 80)
    
    try:
        # 获取客户端
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        embedding_client = await get_client("model.embeddings.moka-m3e-base")
        
        # 1. 创建基础搜索实例（DDSearch专注于搜索功能）
        dd_search = DDSearch(rdb_client, vdb_client, embedding_client)
        print("✅ 创建DDSearch实例成功（专注基础搜索功能）")
        
        # 2. 创建业务逻辑服务实例
        three_layer_service = ThreeLayerSearchService(
            dd_search=dd_search,
            rdb_client=rdb_client,
            vdb_client=vdb_client,
            embedding_client=embedding_client
        )
        print("✅ 创建ThreeLayerSearchService实例成功（专注业务逻辑）")
        
        print(f"\n📋 架构职责分离验证:")
        await test_basic_search_capabilities(dd_search)
        await test_business_logic_capabilities(three_layer_service)
        await test_cross_database_search(dd_search)
        
        print(f"\n🎉 重构后架构测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"架构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_basic_search_capabilities(dd_search):
    """测试DDSearch的基础搜索能力"""
    print("\n1️⃣ 测试DDSearch基础搜索能力:")
    
    test_query = "客户"
    
    # 测试精确搜索
    try:
        exact_results = await dd_search.search(
            query=test_query,
            mode=SearchMode.EXACT,
            field=SearchField.DATA_ITEM_NAME,
            limit=3
        )
        print(f"   ✅ 精确搜索: {len(exact_results)}条结果")
    except Exception as e:
        print(f"   ❌ 精确搜索失败: {e}")
    
    # 测试模糊搜索
    try:
        fuzzy_results = await dd_search.search(
            query=test_query,
            mode=SearchMode.FUZZY,
            field=SearchField.DATA_ITEM_NAME,
            limit=3
        )
        print(f"   ✅ 模糊搜索: {len(fuzzy_results)}条结果")
    except Exception as e:
        print(f"   ❌ 模糊搜索失败: {e}")
    
    # 测试向量搜索
    try:
        vector_results = await dd_search.search(
            query=test_query,
            mode=SearchMode.VECTOR,
            field=SearchField.DATA_ITEM_NAME,
            limit=3,
            min_score=0.3
        )
        print(f"   ✅ 向量搜索: {len(vector_results)}条结果")
    except Exception as e:
        print(f"   ❌ 向量搜索失败: {e}")
    
    # 测试混合搜索
    try:
        hybrid_results = await dd_search.hybrid_search(
            query=test_query,
            limit=3,
            min_score=0.3
        )
        print(f"   ✅ 混合搜索: {len(hybrid_results)}条结果")
    except Exception as e:
        print(f"   ❌ 混合搜索失败: {e}")
    
    print("   📋 DDSearch职责：提供纯粹的搜索功能，不包含业务逻辑")


async def test_business_logic_capabilities(three_layer_service):
    """测试ThreeLayerSearchService的业务逻辑能力"""
    print("\n2️⃣ 测试ThreeLayerSearchService业务逻辑能力:")
    
    # 准备测试数据：模拟从biz_dd_pre表搜出的结果
    test_pre_distribution_data = [
        {
            "id": 1,
            "submission_id": "TEST_SUB_001",
            "submission_type": "SUBMISSION",
            "report_type": "detail",
            "set": "SET_A",
            "dr01": "ADS",
            "dr09": "客户姓名",
            "dr17": "客户的真实姓名信息"
        },
        {
            "id": 2,
            "submission_id": "TEST_SUB_002",
            "submission_type": "RANGE",
            "report_type": "index",
            "set": "survey",
            "dr01": "BDM",
            "dr09": "风险评级",
            "dr17": "客户风险等级评定"
        }
    ]
    
    try:
        # 执行三层搜索业务逻辑
        business_results = await three_layer_service.execute_three_layer_search(
            pre_distribution_data=test_pre_distribution_data,
            knowledge_id="58b452bc-24ca-46b2-89fb-cce1d68068c6",
            limit=5,
            min_score=0.3
        )
        
        print(f"   ✅ 三层搜索业务逻辑: {len(business_results)}条结果")
        
        # 分析业务逻辑结果
        if business_results:
            layer_stats = {}
            for result in business_results:
                layer = result.get("search_layer", "unknown")
                layer_stats[layer] = layer_stats.get(layer, 0) + 1
            
            print(f"   📊 业务逻辑层级统计:")
            for layer, count in layer_stats.items():
                print(f"     - {layer}: {count}条")
        
    except Exception as e:
        print(f"   ❌ 三层搜索业务逻辑失败: {e}")
    
    print("   📋 ThreeLayerSearchService职责：实现复杂业务逻辑，调用DDSearch基础能力")


async def test_cross_database_search(dd_search):
    """测试跨数据库搜索能力"""
    print("\n3️⃣ 测试跨数据库搜索能力:")
    
    try:
        # 测试text_search（跨MySQL和PG的混合搜索）
        cross_db_results = await dd_search.text_search(
            query="客户信息",
            limit=3,
            min_score=0.3,
            vector_weight=0.7,
            text_weight=0.3
        )
        
        print(f"   ✅ 跨数据库混合搜索: {len(cross_db_results)}条结果")
        
        # 分析搜索类型
        if cross_db_results:
            search_types = set()
            for result in cross_db_results:
                search_type = result.get("search_type", "unknown")
                search_types.add(search_type)
            
            print(f"   📊 搜索类型: {', '.join(search_types)}")
        
    except Exception as e:
        print(f"   ❌ 跨数据库搜索失败: {e}")
    
    print("   📋 跨数据库搜索：MySQL文本搜索 + PG向量搜索的结果融合")


async def demonstrate_architecture_benefits():
    """演示重构后架构的优势"""
    print("\n🏆 重构后架构优势演示:")
    
    print("   ✅ 职责分离清晰:")
    print("     - DDSearch: 专注搜索功能（精确、模糊、向量、混合）")
    print("     - ThreeLayerSearchService: 专注业务逻辑（三层搜索、四层筛选）")
    
    print("   ✅ 松耦合设计:")
    print("     - 可以独立使用DDSearch进行基础搜索")
    print("     - 可以独立使用ThreeLayerSearchService进行业务处理")
    print("     - 遵循'能用到就用，用不到就不用'的原则")
    
    print("   ✅ 易于维护和扩展:")
    print("     - 搜索功能优化只需修改DDSearch")
    print("     - 业务逻辑变更只需修改ThreeLayerSearchService")
    print("     - 新的业务需求可以创建新的业务服务类")
    
    print("   ✅ 便于测试:")
    print("     - 可以独立测试搜索功能")
    print("     - 可以独立测试业务逻辑")
    print("     - 可以使用Mock对象进行单元测试")


async def main():
    """主函数"""
    print("🚀 DD系统重构后架构测试")
    print("=" * 80)
    print("验证职责分离和松耦合设计：")
    print("1. DDSearch类：提供基础搜索能力")
    print("2. ThreeLayerSearchService类：实现业务逻辑")
    print("3. 清晰的职责分离，便于维护和扩展")
    print("=" * 80)
    
    success = await test_refactored_architecture()
    
    if success:
        await demonstrate_architecture_benefits()
        
        print("\n✅ 重构后架构测试通过！")
        print("\n📋 使用建议:")
        print("   1. 基础搜索需求：直接使用DDSearch类")
        print("   2. 复杂业务逻辑：使用ThreeLayerSearchService类")
        print("   3. 新的业务需求：创建新的业务服务类")
        print("   4. 保持DDSearch的纯净性，不要在其中添加业务逻辑")
    else:
        print("\n❌ 重构后架构测试失败，请检查错误信息")


if __name__ == "__main__":
    asyncio.run(main())

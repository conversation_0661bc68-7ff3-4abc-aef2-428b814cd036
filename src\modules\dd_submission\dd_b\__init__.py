"""
DD-B模块 - DD提交流程下一步处理

提供DD提交流程下一步处理功能：
- 数据查询和处理
- 字段自动填充
- 数据验证

主要接口：
- DDBProcessor: DD-B数据处理主接口
- DDBValidator: DD-B数据验证主接口

设计原则：
- 简洁的API设计，只暴露两个主要接口文件
- 清晰的目录结构，核心逻辑与基础设施分离
- 完整的错误处理和日志记录
- 遵循现有项目的代码规范和模式
"""

# 主要接口
from modules.dd_submission.dd_b.dd_b_processor import (
    DDBProcessor,
    process_dd_b_data,
    check_dd_b_completeness,
    get_dd_b_summary
)
from modules.dd_submission.dd_b.dd_b_validator import (
    DDBValidator,
    validate_dd_b_data,
    quick_validate_dd_b_data,
    validate_dd_b_record_format
)

# 数据模型（从infrastructure导入）
from modules.dd_submission.dd_b.infrastructure.models import (
    ProcessingStatusEnum,
    FieldStatusEnum,
    GenerationStrategyEnum,
    DDBProcessRequest,
    DDBRecord,
    FieldFillInfo,
    DDBProcessResult,
    DDBValidationRequest,
    DDBValidationResult,
    HistorySearchResult,
    GenerationDecision
)

# 常量和工具（从infrastructure导入）
from modules.dd_submission.dd_b.infrastructure.constants import (
    DDBConstants,
    DDBFieldTypes,
    DDBErrorCodes,
    DDBUtils
)

# 异常（从infrastructure导入）
from modules.dd_submission.dd_b.infrastructure.exceptions import (
    DDBError,
    DDBValidationError,
    DDBDatabaseError,
    DDBProcessingError,
    DDBTimeoutError,
    DDBDataNotFoundError,
    create_validation_error,
    create_database_error,
    create_processing_error,
    create_timeout_error,
    create_data_not_found_error
)

# 历史连接器（从core导入）
from modules.dd_submission.dd_b.core.history_connector import (
    HistoryInfoExtractor,
    OptimizedVectorSearchEngine,
    GenerationDecisionMaker,
    HistoryConnector,
    create_history_connector,
    extract_and_decide_single
)

# 大模型生成器（从core导入）
from modules.dd_submission.dd_b.core.llm_generator import (
    LLMGenerator,
    FieldGenerationIntegrator,
    ConcurrencyManager,
    create_llm_generator,
    create_field_generation_integrator,
    generate_fields_for_record
)

# 并发处理器（从core导入）
from modules.dd_submission.dd_b.core.concurrent_processor import (
    ConcurrentProcessor,
    create_concurrent_processor,
    create_and_start_processor
)

# 全局并发控制管理器（从core导入）
from modules.dd_submission.dd_b.core.global_concurrency_manager import (
    GlobalConcurrencyManager,
    ConcurrencyConfig,
    ConcurrencyScope,
    get_global_concurrency_manager,
    create_concurrency_config
)

# Pipeline集成器（从core导入）
from modules.dd_submission.dd_b.core.pipeline_integrator import (
    PipelineIntegrator,
    PipelineRequest,
    PipelineResult,
    FieldMappingResult,
    create_pipeline_integrator,
    execute_pipeline_for_record
)

# 增强数据处理器（从core导入）
from modules.dd_submission.dd_b.core.enhanced_data_processor import (
    EnhancedDataProcessor,
    create_enhanced_data_processor,
    process_dd_b_request_enhanced
)

# 批量增强处理器（从core导入）
from modules.dd_submission.dd_b.core.batch_enhanced_processor import (
    BatchEnhancedProcessor,
    BatchProcessRequest,
    BatchProcessResult,
    process_dd_b_batch_request
)

# 向后兼容性：保留原有的主要类名
from modules.dd_submission.dd_b.core.data_processor import DataProcessor as DDBDataProcessor
from modules.dd_submission.dd_b.core.field_filler import FieldFiller as DDBFieldFiller
from modules.dd_submission.dd_b.dd_b_processor import DDBProcessorLogic
from modules.dd_submission.dd_b.dd_b_validator import DDBValidatorLogic

__version__ = "1.0.0"

__all__ = [
    # 主要接口（新）
    "DDBProcessor",
    "DDBValidator",

    # 便捷函数（新）
    "process_dd_b_data",
    "check_dd_b_completeness", 
    "get_dd_b_summary",
    "validate_dd_b_data",
    "quick_validate_dd_b_data",
    "validate_dd_b_record_format",

    # 数据模型
    "ProcessingStatusEnum",
    "FieldStatusEnum",
    "GenerationStrategyEnum",
    "DDBProcessRequest",
    "DDBRecord",
    "FieldFillInfo",
    "DDBProcessResult",
    "DDBValidationRequest",
    "DDBValidationResult",
    "HistorySearchResult",
    "GenerationDecision",

    # 常量和工具
    "DDBConstants",
    "DDBFieldTypes",
    "DDBErrorCodes",
    "DDBUtils",

    # 异常
    "DDBError",
    "DDBValidationError",
    "DDBDatabaseError",
    "DDBProcessingError",
    "DDBTimeoutError",
    "DDBDataNotFoundError",
    "create_validation_error",
    "create_database_error",
    "create_processing_error",
    "create_timeout_error",
    "create_data_not_found_error",

    # 历史连接器
    "HistoryInfoExtractor",
    "OptimizedVectorSearchEngine",
    "GenerationDecisionMaker",
    "HistoryConnector",
    "create_history_connector",
    "extract_and_decide_single",

    # 大模型生成器
    "LLMGenerator",
    "FieldGenerationIntegrator",
    "ConcurrencyManager",
    "create_llm_generator",
    "create_field_generation_integrator",
    "generate_fields_for_record",

    # 并发处理器
    "ConcurrentProcessor",
    "create_concurrent_processor",
    "create_and_start_processor",

    # 全局并发控制管理器
    "GlobalConcurrencyManager",
    "ConcurrencyConfig",
    "ConcurrencyScope",
    "get_global_concurrency_manager",
    "create_concurrency_config",

    # Pipeline集成器
    "PipelineIntegrator",
    "PipelineRequest",
    "PipelineResult",
    "FieldMappingResult",
    "create_pipeline_integrator",
    "execute_pipeline_for_record",

    # 增强数据处理器
    "EnhancedDataProcessor",
    "create_enhanced_data_processor",
    "process_dd_b_request_enhanced",

    # 批量增强处理器
    "BatchEnhancedProcessor",
    "BatchProcessRequest",
    "BatchProcessResult",
    "process_dd_b_batch_request",

    # 向后兼容性
    "DDBDataProcessor",
    "DDBFieldFiller",
    "DDBProcessorLogic",
    "DDBValidatorLogic"
]

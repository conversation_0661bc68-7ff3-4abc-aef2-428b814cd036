"""
ORM SQLAlchemy客户端 - 隔离设计版本

基于SQLAlchemy ORM的数据库客户端，提供与universal客户端完全相同的接口，
但内部使用ORM模型进行数据操作，提供更好的类型安全和性能优化。

重构要点：
1. 每个客户端实例使用独立的Base和MetaData
2. 消除全局共享状态
3. 实现真正的实例隔离
4. 防止锁竞争和阻塞问题
"""

import time
import uuid
import threading
from contextlib import asynccontextmanager, contextmanager
from typing import Dict, Any, List, Optional, Union, Sequence, AsyncGenerator, Type
import logging

from sqlalchemy import create_engine, MetaData, Table, text, select, insert, update, delete, inspect
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session, declarative_base
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy.pool import QueuePool

# 导入新的RDB抽象层
from .....base.rdb import (
    # 核心接口
    DatabaseClient, DatabaseType, TransactionIsolation,

    # 请求和响应模型
    QueryRequest, InsertRequest, UpdateRequest, DeleteRequest,
    QueryResponse, OperationResponse, ConnectionConfig,

    # 异常
    ConnectionError as RDBConnectionError, TransactionError as RDBTransactionError,

    # 适配器
    DefaultResultAdapter, DefaultErrorAdapter
)

from .config import ORMConnectionConfig
from .models.base import ModelMixin
from .session import SessionManager, AsyncSessionManager
from .query import ORMQueryBuilder
from .exceptions import (
    ORMSQLAlchemyError, ConnectionError, QueryError,
    TransactionError, ModelError, wrap_orm_error
)
from .dialects import get_dialect, detect_dialect_from_url, DatabaseDialect

logger = logging.getLogger(__name__)


class ORMSQLAlchemyClient(DatabaseClient):
    """
    隔离设计的ORM SQLAlchemy客户端

    每个客户端实例拥有：
    1. 独立的Base类和MetaData
    2. 独立的模型注册表
    3. 独立的会话管理器
    4. 实例级别的锁
    """
    """
    ORM SQLAlchemy客户端

    提供与UniversalSQLAlchemyClient完全相同的接口，但内部使用SQLAlchemy ORM
    进行数据操作，提供更好的类型安全性和性能优化。

    Now implements the new RDB abstraction layer interface.
    """

    def __init__(self, config: Union[ORMConnectionConfig, ConnectionConfig]):
        """
        初始化ORM客户端 - 隔离设计版本

        Args:
            config: ORM连接配置（legacy）或新ConnectionConfig
        """
        # 实例唯一标识符
        self.instance_id = str(uuid.uuid4())[:8]

        # 实例级别的锁
        self._lock = threading.RLock()

        # 配置适配
        if isinstance(config, ConnectionConfig):
            self.rdb_config = config
            self.config = self._convert_to_legacy_config(config)
        else:
            self.config = config
            self.rdb_config = self._convert_from_legacy_config(config)

        # 检测数据库类型和方言
        self.database_type = self._detect_database_type()
        dialect_name = detect_dialect_from_url(self.config.database_url)
        self.dialect: DatabaseDialect = get_dialect(dialect_name)

        # 创建适配器
        self.result_adapter = DefaultResultAdapter(self.database_type)
        self.error_adapter = DefaultErrorAdapter(self.database_type)

        # SQLAlchemy引擎 - 延迟初始化
        self.sync_engine: Optional[Engine] = None
        self.async_engine: Optional[AsyncEngine] = None

        # 会话管理器
        self.session_manager: Optional[SessionManager] = None
        self.async_session_manager: Optional[AsyncSessionManager] = None

        # 实例级别的元数据
        self.metadata = MetaData()

        # 实例级别的Base类
        self.Base = declarative_base(metadata=self.metadata)

        # 实例级别的模型注册表
        self.models: Dict[str, Type] = {}

        # 连接状态
        self._sync_connected = False
        self._async_connected = False

        logger.info(f"Initialized isolated ORM SQLAlchemy Client (ID: {self.instance_id}) for {self.dialect.name}")

    # ==================== RDB Interface Methods ====================

    def get_database_type(self) -> DatabaseType:
        """获取数据库类型"""
        return self.database_type

    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._sync_connected or self._async_connected

    # ==================== Connection Management ====================
    
    def connect(self) -> None:
        """连接到数据库（同步）- 隔离设计版本"""
        with self._lock:
            try:
                if not self.sync_engine:
                    self._create_sync_engine()

                # 测试同步连接
                self._test_sync_connection()

                # 初始化会话管理器
                if not self.session_manager:
                    self.session_manager = SessionManager(self.sync_engine, self.config)

                # 初始化实例级别的模型
                self._initialize_model_registry()

                self._sync_connected = True
                logger.info(f"Successfully connected to {self.dialect.name} database (sync) - Instance {self.instance_id}")

            except Exception as e:
                error = wrap_orm_error(e, "sync connection")
                logger.error(f"Failed to connect to database (sync): {error}")
                raise error
    
    async def aconnect(self) -> None:
        """连接到数据库（异步）- 隔离设计版本"""
        # 使用异步锁保护
        async def _async_connect():
            if not self.async_engine:
                await self._create_async_engine()

            # 测试异步连接
            await self._test_async_connection()

            # 初始化异步会话管理器
            if not self.async_session_manager:
                self.async_session_manager = AsyncSessionManager(self.async_engine, self.config)

            # 确保同步引擎和模型已初始化
            with self._lock:
                if not self.sync_engine:
                    self._create_sync_engine()

                # 初始化实例级别的模型
                self._initialize_model_registry()

            self._async_connected = True
            logger.info(f"Successfully connected to {self.dialect.name} database (async) - Instance {self.instance_id}")

        try:
            # 执行异步连接
            await _async_connect()
            
        except Exception as e:
            error = wrap_orm_error(e, "async connection")
            logger.error(f"Failed to connect to database (async): {error}")
            raise error
    
    def disconnect(self) -> None:
        """断开数据库连接（同步）- 隔离设计版本"""
        with self._lock:
            try:
                # 清理模型字典
                self.models.clear()

                # 关闭会话管理器
                if self.session_manager:
                    self.session_manager.close_all()
                    self.session_manager = None

                # 关闭引擎
                if self.sync_engine:
                    self.sync_engine.dispose()
                    self.sync_engine = None

                self._sync_connected = False
                logger.info(f"Disconnected from database (sync) - Instance {self.instance_id}")

            except Exception as e:
                logger.error(f"Error during sync disconnect: {e}")
                raise wrap_orm_error(e, "sync disconnect")
    
    async def adisconnect(self) -> None:
        """断开数据库连接（异步）- 隔离设计版本"""
        # 使用异步锁保护
        async def _async_disconnect():
            # 关闭异步会话管理器
            if self.async_session_manager:
                await self.async_session_manager.close_all()
                self.async_session_manager = None

            # 关闭异步引擎
            if self.async_engine:
                await self.async_engine.dispose()
                self.async_engine = None

            self._async_connected = False
            logger.info(f"Disconnected from database (async) - Instance {self.instance_id}")

        try:
            # 执行异步断开连接
            await _async_disconnect()

        except Exception as e:
            logger.error(f"Error during async disconnect: {e}")
            raise wrap_orm_error(e, "async disconnect")
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._sync_connected or self._async_connected
    
    def is_sync_connected(self) -> bool:
        """检查同步连接状态"""
        return self._sync_connected
    
    def is_async_connected(self) -> bool:
        """检查异步连接状态"""
        return self._async_connected
    
    # ==================== RDB Interface CRUD Methods ====================

    def query(self, request: QueryRequest) -> QueryResponse:
        """执行查询（同步）- RDB接口方法"""
        if not self.sync_engine:
            raise RDBConnectionError("Not connected. Call connect() first.")

        start_time = time.time()

        try:
            # 简化实现：使用原生SQL
            sql, params = self._build_select_sql(request)

            with self.sync_engine.begin() as conn:
                result = conn.execute(text(sql), params)
                rows = [dict(row._mapping) for row in result]

            execution_time = time.time() - start_time
            return QueryResponse(
                data=rows,
                total_count=len(rows),
                execution_time=execution_time,
                query_sql=sql,
                query_parameters=params,
                database_type=self.database_type
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "query",
                "table": request.table,
                "request": str(request)
            })

    async def aquery(self, request: QueryRequest) -> QueryResponse:
        """执行查询（异步）- RDB接口方法"""
        if not self.async_engine:
            raise RDBConnectionError("Not connected. Call aconnect() first.")

        start_time = time.time()

        try:
            # 简化实现：使用原生SQL
            sql, params = self._build_select_sql(request)

            async with self.async_engine.begin() as conn:
                result = await conn.execute(text(sql), params)
                rows = [dict(row._mapping) for row in result]

            execution_time = time.time() - start_time
            return QueryResponse(
                data=rows,
                total_count=len(rows),
                execution_time=execution_time,
                query_sql=sql,
                query_parameters=params,
                database_type=self.database_type
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "aquery",
                "table": request.table,
                "request": str(request)
            })

    def execute(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> OperationResponse:
        """执行原生SQL（同步）- RDB接口方法"""
        if not self.sync_engine:
            raise RDBConnectionError("Not connected. Call connect() first.")

        start_time = time.time()

        try:
            with self.sync_engine.begin() as conn:
                result = conn.execute(text(sql), parameters or {})
                affected_rows = result.rowcount

            execution_time = time.time() - start_time
            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=parameters
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "execute",
                "sql": sql,
                "parameters": parameters
            })

    async def aexecute(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> OperationResponse:
        """执行原生SQL（异步）- RDB接口方法"""
        if not self.async_engine:
            raise RDBConnectionError("Not connected. Call aconnect() first.")

        start_time = time.time()

        try:
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text(sql), parameters or {})
                affected_rows = result.rowcount

            execution_time = time.time() - start_time
            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=parameters
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "aexecute",
                "sql": sql,
                "parameters": parameters
            })

    def fetch_all(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> QueryResponse:
        """获取所有结果（同步）- RDB接口方法"""
        if not self.sync_engine:
            raise RDBConnectionError("Not connected. Call connect() first.")

        start_time = time.time()

        try:
            with self.sync_engine.connect() as conn:
                result = conn.execute(text(sql), parameters or {})
                rows = [dict(row._mapping) for row in result]

            execution_time = time.time() - start_time
            return QueryResponse(
                data=rows,
                total_count=len(rows),
                execution_time=execution_time,
                query_sql=sql,
                query_parameters=parameters,
                database_type=self.database_type
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "fetch_all",
                "sql": sql,
                "parameters": parameters
            })

    async def afetch_all(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> QueryResponse:
        """获取所有结果（异步）- RDB接口方法"""
        if not self.async_engine:
            raise RDBConnectionError("Not connected. Call aconnect() first.")

        start_time = time.time()

        try:
            async with self.async_engine.connect() as conn:
                result = await conn.execute(text(sql), parameters or {})
                rows = [dict(row._mapping) for row in result]

            execution_time = time.time() - start_time
            return QueryResponse(
                data=rows,
                total_count=len(rows),
                execution_time=execution_time,
                query_sql=sql,
                query_parameters=parameters,
                database_type=self.database_type
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "afetch_all",
                "sql": sql,
                "parameters": parameters
            })

    def fetch_one(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """获取单个结果（同步）- RDB接口方法"""
        response = self.fetch_all(sql, parameters)
        return response.data[0] if response.data else None

    async def afetch_one(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """获取单个结果（异步）- RDB接口方法"""
        response = await self.afetch_all(sql, parameters)
        return response.data[0] if response.data else None

    @contextmanager
    def transaction(self, isolation_level: Optional[TransactionIsolation] = None):
        """事务上下文管理器（同步）- RDB接口方法"""
        if not self.sync_engine:
            raise RDBConnectionError("Not connected. Call connect() first.")

        conn = self.sync_engine.connect()
        trans = conn.begin()

        try:
            yield conn
            trans.commit()
        except Exception as e:
            trans.rollback()
            raise RDBTransactionError(
                f"Transaction failed: {e}",
                original_error=e,
                database_type=self.database_type
            )
        finally:
            conn.close()

    @asynccontextmanager
    async def atransaction(self, isolation_level: Optional[TransactionIsolation] = None):
        """事务上下文管理器（异步）- RDB接口方法"""
        if not self.async_engine:
            raise RDBConnectionError("Not connected. Call aconnect() first.")

        conn = await self.async_engine.connect()
        trans = await conn.begin()

        try:
            yield conn
            await trans.commit()
        except Exception as e:
            await trans.rollback()
            raise RDBTransactionError(
                f"Transaction failed: {e}",
                original_error=e,
                database_type=self.database_type
            )
        finally:
            await conn.close()

    def health_check(self) -> Dict[str, Any]:
        """健康检查 - RDB接口方法"""
        try:
            if not self.is_connected():
                return {
                    "status": "disconnected",
                    "database_type": self.database_type,
                    "error": "Not connected to database"
                }

            # 执行简单查询测试连接
            start_time = time.time()
            if self.sync_engine:
                with self.sync_engine.connect() as conn:
                    result = conn.execute(text("SELECT 1"))
                    result.fetchone()

            execution_time = time.time() - start_time

            return {
                "status": "healthy",
                "database_type": self.database_type,
                "response_time": execution_time,
                "connection_pool": {
                    "size": self.sync_engine.pool.size() if self.sync_engine else 0,
                    "checked_in": self.sync_engine.pool.checkedin() if self.sync_engine else 0,
                    "checked_out": self.sync_engine.pool.checkedout() if self.sync_engine else 0,
                },
                "model_registry": {
                    "registered_models": len(self.models),
                    "model_names": list(self.models.keys())
                }
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "database_type": self.database_type,
                "error": str(e)
            }

    # ==================== Legacy Raw SQL Execution ====================

    def execute_query(self, sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行原生SQL查询并返回结果（同步）
        
        Args:
            sql: SQL查询语句
            params: 查询参数
        
        Returns:
            查询结果列表
        """
        if params is None:
            params = {}
        
        if not self.sync_engine:
            raise ConnectionError("Sync engine not available. Call connect() first.")
        
        try:
            with self.sync_engine.begin() as conn:
                result = conn.execute(text(sql), params)
                return [dict(row._mapping) for row in result]
        except Exception as e:
            raise wrap_orm_error(e, "execute_query", {"sql": sql, "params": params})
    
    async def aexecute_query(self, sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行原生SQL查询并返回结果（异步）
        
        Args:
            sql: SQL查询语句
            params: 查询参数
        
        Returns:
            查询结果列表
        """
        if params is None:
            params = {}
        
        if not self.async_engine:
            raise ConnectionError("Async engine not available. Call aconnect() first.")
        
        try:
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text(sql), params)
                return [dict(row._mapping) for row in result]
        except Exception as e:
            raise wrap_orm_error(e, "aexecute_query", {"sql": sql, "params": params})
    
    def execute_sql(self, sql: str, params: Optional[Dict[str, Any]] = None) -> int:
        """
        执行原生SQL语句（INSERT, UPDATE, DELETE）并返回影响行数（同步）
        
        Args:
            sql: SQL语句
            params: 语句参数
        
        Returns:
            影响的行数
        """
        if params is None:
            params = {}
        
        if not self.sync_engine:
            raise ConnectionError("Sync engine not available. Call connect() first.")
        
        try:
            with self.sync_engine.begin() as conn:
                result = conn.execute(text(sql), params)
                return result.rowcount
        except Exception as e:
            raise wrap_orm_error(e, "execute_sql", {"sql": sql, "params": params})
    
    async def aexecute_sql(self, sql: str, params: Optional[Dict[str, Any]] = None) -> int:
        """
        执行原生SQL语句（INSERT, UPDATE, DELETE）并返回影响行数（异步）
        
        Args:
            sql: SQL语句
            params: 语句参数
        
        Returns:
            影响的行数
        """
        if params is None:
            params = {}
        
        if not self.async_engine:
            raise ConnectionError("Async engine not available. Call aconnect() first.")
        
        try:
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text(sql), params)
                return result.rowcount
        except Exception as e:
            raise wrap_orm_error(e, "aexecute_sql", {"sql": sql, "params": params})
    
    # ==================== Query Builder ====================
    
    def query(self, table: str) -> ORMQueryBuilder:
        """
        创建ORM查询构建器

        Args:
            table: 表名

        Returns:
            ORM查询构建器实例
        """
        if not self._sync_connected:
            raise ModelError("Not connected. Call connect() first.")

        try:
            model_class = self.get_model(table)
            return ORMQueryBuilder(model_class, self.dialect, self.session_manager)
        except Exception as e:
            raise wrap_orm_error(e, f"create query builder for {table}")
    
    # ==================== Internal Helper Methods ====================
    
    def _create_sync_engine(self) -> None:
        """创建同步引擎"""
        engine_options = {
            'pool_size': self.config.pool_size,
            'max_overflow': self.config.max_overflow,
            'pool_timeout': self.config.pool_timeout,
            'pool_recycle': self.config.pool_recycle,
            'pool_pre_ping': self.config.pool_pre_ping,
            'echo': self.config.echo,
            'echo_pool': self.config.echo_pool,
            'future': self.config.future,
        }
        
        # 添加方言特定选项
        if self.config.dialect_options:
            engine_options.update(self.config.dialect_options)
        
        self.sync_engine = create_engine(self.config.database_url, **engine_options)
        logger.debug("Created sync engine")
    
    async def _create_async_engine(self) -> None:
        """创建异步引擎"""
        # 转换为异步URL
        async_url = self.config.database_url
        if not async_url.startswith(('postgresql+asyncpg', 'mysql+aiomysql', 'sqlite+aiosqlite')):
            # 添加异步驱动
            if 'postgresql' in async_url:
                # 替换postgresql方言为asyncpg
                if async_url.startswith('postgresql+'):
                    async_url = async_url.replace('postgresql+', 'postgresql+asyncpg+', 1)
                else:
                    async_url = async_url.replace('postgresql://', 'postgresql+asyncpg://')
            elif 'mysql' in async_url:
                # 替换mysql方言为aiomysql
                if async_url.startswith('mysql+'):
                    # 替换现有驱动为aiomysql
                    parts = async_url.split('://', 1)
                    if len(parts) == 2:
                        async_url = f"mysql+aiomysql://{parts[1]}"
                else:
                    async_url = async_url.replace('mysql://', 'mysql+aiomysql://')
            elif 'sqlite' in async_url:
                # 替换sqlite方言为aiosqlite
                if async_url.startswith('sqlite+'):
                    async_url = async_url.replace('sqlite+', 'sqlite+aiosqlite+', 1)
                else:
                    async_url = async_url.replace('sqlite://', 'sqlite+aiosqlite://')
        
        engine_options = {
            'pool_size': self.config.pool_size,
            'max_overflow': self.config.max_overflow,
            'pool_timeout': self.config.pool_timeout,
            'pool_recycle': self.config.pool_recycle,
            'pool_pre_ping': self.config.pool_pre_ping,
            'echo': self.config.echo,
            'echo_pool': self.config.echo_pool,
        }
        
        # 添加方言特定选项
        if self.config.dialect_options:
            engine_options.update(self.config.dialect_options)
        
        self.async_engine = create_async_engine(async_url, **engine_options)
        logger.debug("Created async engine")
    
    def _test_sync_connection(self) -> None:
        """测试同步连接"""
        with self.sync_engine.begin() as conn:
            conn.execute(text("SELECT 1"))
    
    async def _test_async_connection(self) -> None:
        """测试异步连接"""
        async with self.async_engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
    
    def _initialize_model_registry(self) -> None:
        """
        初始化实例级别的模型注册表

        不再使用全局共享的ModelRegistry，而是为每个实例创建独立的模型
        """
        if not self.sync_engine:
            raise ConnectionError("Sync engine required for model initialization")

        with self._lock:
            # 清空现有模型
            self.models.clear()

            # 如果启用自动反射，预加载所有表
            if self.config.auto_reflect_tables:
                try:
                    # 获取所有表名
                    inspector = inspect(self.sync_engine)

                    # 应用表过滤器
                    all_tables = inspector.get_table_names()
                    if self.config.include_views:
                        all_tables.extend(inspector.get_view_names())

                    # 过滤表名
                    filtered_tables = self._filter_tables(all_tables)

                    # 为每个表创建模型
                    for table_name in filtered_tables:
                        self._create_model_for_table(table_name)

                    logger.info(f"Preloaded {len(self.models)} table models for instance {self.instance_id}")
                except Exception as e:
                    logger.warning(f"Failed to preload models: {e}")
                    # 不抛出异常，允许按需加载

    def _filter_tables(self, tables: List[str]) -> List[str]:
        """
        根据配置过滤表名

        Args:
            tables: 所有表名列表

        Returns:
            过滤后的表名列表
        """
        result = []

        for table_name in tables:
            # 排除表
            if self.config.excluded_tables and table_name in self.config.excluded_tables:
                continue

            # 前缀过滤
            if self.config.table_prefix_filter and not table_name.startswith(self.config.table_prefix_filter):
                continue

            # 后缀过滤
            if self.config.table_suffix_filter and not table_name.endswith(self.config.table_suffix_filter):
                continue

            result.append(table_name)

        return result

    def _create_model_for_table(self, table_name: str) -> Type:
        """
        为表创建模型类

        Args:
            table_name: 表名

        Returns:
            模型类
        """
        with self._lock:
            # 如果已经存在，直接返回
            if table_name in self.models:
                return self.models[table_name]

            # 反射表结构
            table = Table(
                table_name,
                self.metadata,
                autoload_with=self.sync_engine
            )

            # 生成类名（首字母大写，去除特殊字符）
            class_name = ''.join(word.capitalize() for word in table_name.replace('_', ' ').split())
            if not class_name.endswith('Model'):
                class_name += 'Model'

            # 创建模型类
            class_attrs = {
                '__tablename__': table_name,
                '__table__': table,
                '__instance_id__': self.instance_id,  # 添加实例ID标识
            }

            # 动态创建模型类
            model_class = type(
                class_name,
                (self.Base, ModelMixin),  # 使用实例级别的Base
                class_attrs
            )

            # 保存到模型字典
            self.models[table_name] = model_class

            logger.debug(f"Created model {class_name} for table {table_name} in instance {self.instance_id}")

            return model_class

    def get_model(self, table_name: str) -> Type:
        """
        获取表的模型类

        Args:
            table_name: 表名

        Returns:
            模型类
        """
        # 如果已经存在，直接返回
        if table_name in self.models:
            return self.models[table_name]

        # 否则创建新模型
        return self._create_model_for_table(table_name)

    # ==================== CRUD Operations ====================

    def select(
        self,
        table: str,
        columns: Optional[Sequence[str]] = None,
        where: Optional[Dict[str, Any]] = None,
        joins: Optional[List[str]] = None,
        group_by: Optional[Sequence[str]] = None,
        having: Optional[Dict[str, Any]] = None,
        order_by: Optional[Sequence[str]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        distinct: bool = False
    ) -> List[Dict[str, Any]]:
        """
        从表中选择记录（同步）

        Args:
            table: 表名
            columns: 要选择的列（None表示所有列）
            where: WHERE条件
            joins: JOIN子句列表
            group_by: GROUP BY列
            having: HAVING条件
            order_by: ORDER BY子句
            limit: LIMIT值
            offset: OFFSET值
            distinct: 是否使用DISTINCT

        Returns:
            表示选择记录的字典列表
        """
        try:
            if not self.session_manager:
                raise ConnectionError("Session manager not available. Call connect() first.")

            model_class = self.get_model(table)

            with self.session_manager.get_session() as session:
                query = session.query(model_class)

                # 应用列选择
                if columns:
                    # 获取指定列的属性
                    column_attrs = []
                    for col in columns:
                        if hasattr(model_class, col):
                            column_attrs.append(getattr(model_class, col))
                        else:
                            logger.warning(f"Column '{col}' not found in model {model_class.__name__}")
                    if column_attrs:
                        query = session.query(*column_attrs)

                # 应用DISTINCT
                if distinct:
                    query = query.distinct()

                # 应用WHERE条件
                if where:
                    for column, value in where.items():
                        if hasattr(model_class, column):
                            column_attr = getattr(model_class, column)
                            if isinstance(value, (list, tuple)):
                                query = query.filter(column_attr.in_(value))
                            elif value is None:
                                query = query.filter(column_attr.is_(None))
                            else:
                                query = query.filter(column_attr == value)

                # 应用JOIN条件
                if joins:
                    # 注意：这里简化实现，实际项目中需要更复杂的JOIN解析
                    logger.warning("JOIN功能在当前版本中简化实现，建议使用query builder进行复杂查询")

                # 应用GROUP BY
                if group_by:
                    group_attrs = []
                    for col in group_by:
                        if hasattr(model_class, col):
                            group_attrs.append(getattr(model_class, col))
                    if group_attrs:
                        query = query.group_by(*group_attrs)

                # 应用HAVING条件
                if having:
                    # HAVING需要在GROUP BY之后
                    for column, value in having.items():
                        if hasattr(model_class, column):
                            column_attr = getattr(model_class, column)
                            if isinstance(value, (list, tuple)):
                                query = query.having(column_attr.in_(value))
                            elif value is None:
                                query = query.having(column_attr.is_(None))
                            else:
                                query = query.having(column_attr == value)

                # 应用ORDER BY
                if order_by:
                    for col in order_by:
                        if col.endswith(" DESC"):
                            col_name = col[:-5].strip()
                            if hasattr(model_class, col_name):
                                query = query.order_by(getattr(model_class, col_name).desc())
                        else:
                            col_name = col.replace(" ASC", "").strip()
                            if hasattr(model_class, col_name):
                                query = query.order_by(getattr(model_class, col_name))

                # 应用LIMIT和OFFSET
                if limit is not None:
                    query = query.limit(limit)
                if offset is not None:
                    query = query.offset(offset)

                # 执行查询并转换为字典
                results = query.all()

                if columns and len(columns) == 1:
                    # 单列查询，返回值列表
                    formatted_results = []
                    for result in results:
                        # 处理SQLAlchemy查询结果
                        if hasattr(result, '__getitem__') and not isinstance(result, str):
                            # 如果是可索引的对象（如Row或tuple），取第一个值
                            try:
                                value = result[0]
                            except (IndexError, TypeError):
                                value = result
                        else:
                            value = result
                        formatted_results.append({columns[0]: value})
                    return formatted_results
                elif columns:
                    # 多列查询，构建字典
                    if results and isinstance(results[0], tuple):
                        return [dict(zip(columns, result)) for result in results]
                    else:
                        # 如果不是元组，可能是单个值
                        return [dict(zip(columns, [result] if not isinstance(result, (list, tuple)) else result)) for result in results]
                else:
                    # 完整模型查询
                    if results and hasattr(results[0], 'to_dict'):
                        return [result.to_dict() for result in results]
                    else:
                        # 如果不是模型对象，返回原始结果
                        return [{'result': result} for result in results]

        except Exception as e:
            raise wrap_orm_error(e, f"select from {table}")

    async def aselect(
        self,
        table: str,
        columns: Optional[Sequence[str]] = None,
        where: Optional[Dict[str, Any]] = None,
        joins: Optional[List[str]] = None,
        group_by: Optional[Sequence[str]] = None,
        having: Optional[Dict[str, Any]] = None,
        order_by: Optional[Sequence[str]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        distinct: bool = False
    ) -> List[Dict[str, Any]]:
        """
        从表中选择记录（异步）

        Args:
            table: 表名
            columns: 要选择的列（None表示所有列）
            where: WHERE条件
            joins: JOIN子句列表
            group_by: GROUP BY列
            having: HAVING条件
            order_by: ORDER BY子句
            limit: LIMIT值
            offset: OFFSET值
            distinct: 是否使用DISTINCT

        Returns:
            表示选择记录的字典列表
        """
        try:
            if not self.async_session_manager:
                raise ConnectionError("Async session manager not available. Call aconnect() first.")

            model_class = self.get_model(table)

            async with self.async_session_manager.get_session() as session:
                # 构建查询
                if columns:
                    # 获取指定列的属性
                    column_attrs = []
                    for col in columns:
                        if hasattr(model_class, col):
                            column_attrs.append(getattr(model_class, col))
                    if column_attrs:
                        query = select(*column_attrs)
                    else:
                        query = select(model_class)
                else:
                    query = select(model_class)

                # 应用DISTINCT
                if distinct:
                    query = query.distinct()

                # 应用WHERE条件
                if where:
                    for column, value in where.items():
                        if hasattr(model_class, column):
                            column_attr = getattr(model_class, column)
                            if isinstance(value, (list, tuple)):
                                query = query.where(column_attr.in_(value))
                            elif value is None:
                                query = query.where(column_attr.is_(None))
                            else:
                                query = query.where(column_attr == value)

                # 应用JOIN条件
                if joins:
                    # 注意：这里简化实现，实际项目中需要更复杂的JOIN解析
                    logger.warning("JOIN功能在当前版本中简化实现，建议使用query builder进行复杂查询")

                # 应用GROUP BY
                if group_by:
                    group_attrs = []
                    for col in group_by:
                        if hasattr(model_class, col):
                            group_attrs.append(getattr(model_class, col))
                    if group_attrs:
                        query = query.group_by(*group_attrs)

                # 应用HAVING条件
                if having:
                    # HAVING需要在GROUP BY之后
                    for column, value in having.items():
                        if hasattr(model_class, column):
                            column_attr = getattr(model_class, column)
                            if isinstance(value, (list, tuple)):
                                query = query.having(column_attr.in_(value))
                            elif value is None:
                                query = query.having(column_attr.is_(None))
                            else:
                                query = query.having(column_attr == value)

                # 应用ORDER BY
                if order_by:
                    for col in order_by:
                        if col.endswith(" DESC"):
                            col_name = col[:-5].strip()
                            if hasattr(model_class, col_name):
                                query = query.order_by(getattr(model_class, col_name).desc())
                        else:
                            col_name = col.replace(" ASC", "").strip()
                            if hasattr(model_class, col_name):
                                query = query.order_by(getattr(model_class, col_name))

                # 应用LIMIT和OFFSET
                if limit is not None:
                    query = query.limit(limit)
                if offset is not None:
                    query = query.offset(offset)

                # 执行查询
                result = await session.execute(query)

                if columns and len(columns) == 1:
                    # 单列查询
                    rows = result.scalars().all()
                    return [{columns[0]: row} for row in rows]
                elif columns:
                    # 多列查询
                    rows = result.all()
                    return [dict(zip(columns, row)) for row in rows]
                else:
                    # 完整模型查询
                    rows = result.scalars().all()
                    return [row.to_dict() for row in rows]

        except Exception as e:
            raise wrap_orm_error(e, f"aselect from {table}")

    def insert(
        self,
        table: str,
        data: Union[Dict[str, Any], List[Dict[str, Any]]]
    ) -> Union[Any, List[Any]]:
        """
        向表中插入数据（同步）

        Args:
            table: 表名
            data: 要插入的数据（单个字典或字典列表）

        Returns:
            插入的行ID（单个或列表）
        """
        try:
            if isinstance(data, dict):
                return self._insert_single(table, data)
            else:
                return self._insert_batch(table, data)
        except Exception as e:
            raise wrap_orm_error(e, f"insert into {table}")

    async def ainsert(
        self,
        table: str,
        data: Union[Dict[str, Any], List[Dict[str, Any]]]
    ) -> Union[Any, List[Any]]:
        """
        向表中插入数据（异步）

        Args:
            table: 表名
            data: 要插入的数据（单个字典或字典列表）

        Returns:
            插入的行ID（单个或列表）
        """
        try:
            if isinstance(data, dict):
                return await self._ainsert_single(table, data)
            else:
                return await self._ainsert_batch(table, data)
        except Exception as e:
            raise wrap_orm_error(e, f"ainsert into {table}")

    def update(
        self,
        table: str,
        values: Dict[str, Any],
        where: Dict[str, Any]
    ) -> int:
        """
        更新表中的数据（同步）

        Args:
            table: 表名
            values: 要更新的值
            where: WHERE条件

        Returns:
            影响的行数
        """
        try:
            if not self.session_manager:
                raise ConnectionError("Session manager not available. Call connect() first.")

            model_class = self.get_model(table)

            with self.session_manager.get_session() as session:
                query = session.query(model_class)

                # 应用WHERE条件
                for column, value in where.items():
                    if hasattr(model_class, column):
                        column_attr = getattr(model_class, column)
                        if isinstance(value, (list, tuple)):
                            query = query.filter(column_attr.in_(value))
                        elif value is None:
                            query = query.filter(column_attr.is_(None))
                        else:
                            query = query.filter(column_attr == value)

                # 执行更新
                affected_rows = query.update(values)
                session.commit()

                return affected_rows

        except Exception as e:
            raise wrap_orm_error(e, f"update {table}")

    async def aupdate(
        self,
        table: str,
        values: Dict[str, Any],
        where: Dict[str, Any]
    ) -> int:
        """
        更新表中的数据（异步）

        Args:
            table: 表名
            values: 要更新的值
            where: WHERE条件

        Returns:
            影响的行数
        """
        try:
            if not self.async_session_manager:
                raise ConnectionError("Async session manager not available. Call aconnect() first.")

            model_class = self.get_model(table)

            async with self.async_session_manager.get_session() as session:
                # 构建更新语句
                stmt = update(model_class)

                # 应用WHERE条件
                for column, value in where.items():
                    if hasattr(model_class, column):
                        column_attr = getattr(model_class, column)
                        if isinstance(value, (list, tuple)):
                            stmt = stmt.where(column_attr.in_(value))
                        elif value is None:
                            stmt = stmt.where(column_attr.is_(None))
                        else:
                            stmt = stmt.where(column_attr == value)

                # 设置更新值
                stmt = stmt.values(**values)

                # 执行更新
                result = await session.execute(stmt)
                await session.commit()

                return result.rowcount

        except Exception as e:
            raise wrap_orm_error(e, f"aupdate {table}")

    def delete(
        self,
        table: str,
        where: Dict[str, Any]
    ) -> int:
        """
        从表中删除数据（同步）

        Args:
            table: 表名
            where: WHERE条件

        Returns:
            影响的行数
        """
        try:
            if not self.session_manager:
                raise ConnectionError("Session manager not available. Call connect() first.")

            model_class = self.get_model(table)

            with self.session_manager.get_session() as session:
                query = session.query(model_class)

                # 应用WHERE条件
                for column, value in where.items():
                    if hasattr(model_class, column):
                        column_attr = getattr(model_class, column)
                        if isinstance(value, (list, tuple)):
                            query = query.filter(column_attr.in_(value))
                        elif value is None:
                            query = query.filter(column_attr.is_(None))
                        else:
                            query = query.filter(column_attr == value)

                # 执行删除
                affected_rows = query.delete()
                session.commit()

                return affected_rows

        except Exception as e:
            raise wrap_orm_error(e, f"delete from {table}")

    async def adelete(
        self,
        table: str,
        where: Dict[str, Any]
    ) -> int:
        """
        从表中删除数据（异步）

        Args:
            table: 表名
            where: WHERE条件

        Returns:
            影响的行数
        """
        try:
            if not self.async_session_manager:
                raise ConnectionError("Async session manager not available. Call aconnect() first.")

            model_class = self.get_model(table)

            async with self.async_session_manager.get_session() as session:
                # 构建删除语句
                stmt = delete(model_class)

                # 应用WHERE条件
                for column, value in where.items():
                    if hasattr(model_class, column):
                        column_attr = getattr(model_class, column)
                        if isinstance(value, (list, tuple)):
                            stmt = stmt.where(column_attr.in_(value))
                        elif value is None:
                            stmt = stmt.where(column_attr.is_(None))
                        else:
                            stmt = stmt.where(column_attr == value)

                # 执行删除
                result = await session.execute(stmt)
                await session.commit()

                return result.rowcount

        except Exception as e:
            raise wrap_orm_error(e, f"adelete from {table}")

    # ==================== Internal CRUD Helper Methods ====================

    def _insert_single(self, table: str, data: Dict[str, Any]) -> Any:
        """插入单行数据（同步）"""
        if not self.session_manager:
            raise ConnectionError("Session manager not available")

        model_class = self.get_model(table)

        with self.session_manager.get_session() as session:
            # 创建模型实例
            instance = model_class(**data)
            session.add(instance)

            # 刷新以获取数据库生成的值（如自增ID）
            session.flush()

            # 在提交前获取主键值，避免对象过期
            pk_columns = model_class.get_primary_key_columns()
            pk_values = {}
            for col in pk_columns:
                if hasattr(instance, col):
                    value = getattr(instance, col)
                    pk_values[col] = value

            session.commit()

            # 返回主键值
            if len(pk_values) == 1:
                return list(pk_values.values())[0]
            else:
                return pk_values

    async def _ainsert_single(self, table: str, data: Dict[str, Any]) -> Any:
        """插入单行数据（异步）"""
        if not self.async_session_manager:
            raise ConnectionError("Async session manager not available")

        model_class = self.get_model(table)

        async with self.async_session_manager.get_session() as session:
            # 创建模型实例
            instance = model_class(**data)
            session.add(instance)

            # 刷新以获取数据库生成的值（如自增ID）
            await session.flush()

            # 在提交前获取主键值，避免对象过期
            pk_columns = model_class.get_primary_key_columns()
            pk_values = {}
            for col in pk_columns:
                # 直接从实例的__dict__获取值，避免触发SQLAlchemy的属性访问
                if hasattr(instance, col):
                    value = getattr(instance, col)
                    pk_values[col] = value

            await session.commit()

            # 返回主键值
            if len(pk_values) == 1:
                return list(pk_values.values())[0]
            else:
                return pk_values

    def _insert_batch(self, table: str, data: List[Dict[str, Any]]) -> List[Any]:
        """插入多行数据（同步）"""
        if not data:
            return []

        if not self.session_manager:
            raise ConnectionError("Session manager not available")

        model_class = self.get_model(table)

        with self.session_manager.get_session() as session:
            instances = []
            for row_data in data:
                instance = model_class(**row_data)
                instances.append(instance)

            session.add_all(instances)

            # 刷新以获取数据库生成的值
            session.flush()

            # 在提交前获取主键值列表
            pk_columns = model_class.get_primary_key_columns()
            results = []
            for instance in instances:
                pk_values = {}
                for col in pk_columns:
                    if hasattr(instance, col):
                        value = getattr(instance, col)
                        pk_values[col] = value

                if len(pk_values) == 1:
                    results.append(list(pk_values.values())[0])
                else:
                    results.append(pk_values)

            session.commit()
            return results

    async def _ainsert_batch(self, table: str, data: List[Dict[str, Any]]) -> List[Any]:
        """插入多行数据（异步）"""
        if not data:
            return []

        if not self.async_session_manager:
            raise ConnectionError("Async session manager not available")

        model_class = self.get_model(table)

        async with self.async_session_manager.get_session() as session:
            instances = []
            for row_data in data:
                instance = model_class(**row_data)
                instances.append(instance)

            session.add_all(instances)

            # 刷新以获取数据库生成的值
            await session.flush()

            # 在提交前获取主键值列表
            pk_columns = model_class.get_primary_key_columns()
            results = []
            for instance in instances:
                pk_values = {}
                for col in pk_columns:
                    if hasattr(instance, col):
                        value = getattr(instance, col)
                        pk_values[col] = value

                if len(pk_values) == 1:
                    results.append(list(pk_values.values())[0])
                else:
                    results.append(pk_values)

            await session.commit()
            return results

    # ==================== Legacy Health Check and Utility Methods ====================

    async def ahealth_check(self) -> Dict[str, Any]:
        """
        执行数据库连接健康检查（异步）

        Returns:
            健康检查结果
        """
        try:
            # 测试异步连接
            if not self.async_engine:
                raise ConnectionError("Async engine not available")

            async with self.async_engine.begin() as conn:
                result = await conn.execute(text("SELECT 1 AS health_check"))
                health_value = result.scalar()

            return {
                'status': 'healthy' if health_value == 1 else 'unhealthy',
                'dialect': self.dialect.name,
                'sync_connected': self._sync_connected,
                'async_connected': self._async_connected,
                'async_available': self.async_engine is not None,
                'sync_available': self.sync_engine is not None,
                'model_registry_initialized': True,
                'cached_models': len(self.models)
            }

        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'dialect': self.dialect.name,
                'sync_connected': self._sync_connected,
                'async_connected': False,
                'model_registry_initialized': True
            }

    def get_dialect_info(self) -> Dict[str, Any]:
        """获取当前方言信息"""
        return {
            'name': self.dialect.name,
            'description': self.dialect.description,
            'default_port': self.dialect.default_port,
            'default_driver': self.dialect.default_driver,
            'async_driver': self.dialect.async_driver,
            'features': {
                'json_support': self.dialect.features.json_support.value,
                'array_support': self.dialect.features.array_support.value,
                'window_functions': self.dialect.features.window_functions.value,
                'cte_support': self.dialect.features.cte_support.value,
                'full_text_search': self.dialect.features.full_text_search.value,
                'partitioning': self.dialect.features.partitioning.value,
                'upsert_support': self.dialect.features.upsert_support.value,
                'returning_clause': self.dialect.features.returning_clause.value,
                'bulk_insert': self.dialect.features.bulk_insert.value,
                'async_support': self.dialect.features.async_support.value,
            }
        }

    def get_model_info(self, table: str) -> Dict[str, Any]:
        """
        获取模型信息

        Args:
            table: 表名

        Returns:
            模型信息字典
        """
        if not self._sync_connected:
            raise ModelError("Not connected. Call connect() first.")

        model_class = self.get_model(table)

        # 获取模型信息
        from sqlalchemy import inspect as sqlalchemy_inspect
        mapper = sqlalchemy_inspect(model_class)

        return {
            'table_name': table,
            'class_name': model_class.__name__,
            'columns': [col.name for col in mapper.columns],
            'primary_keys': [col.name for col in mapper.primary_key],
            'relationships': [rel.key for rel in mapper.relationships],
            'instance_id': self.instance_id
        }

    def get_available_tables(self) -> List[str]:
        """
        获取所有可用的表名

        Returns:
            表名列表
        """
        if not self._sync_connected:
            raise ModelError("Not connected. Call connect() first.")

        # 返回已加载的模型表名
        return list(self.models.keys())

    def refresh_model(self, table: str) -> None:
        """
        刷新指定表的模型

        Args:
            table: 表名
        """
        if not self._sync_connected:
            raise ModelError("Not connected. Call connect() first.")

        with self._lock:
            # 从模型字典中移除
            if table in self.models:
                del self.models[table]

            # 重新创建模型
            self._create_model_for_table(table)

        logger.info(f"Refreshed model for table: {table} in instance {self.instance_id}")

    def refresh_all_models(self) -> None:
        """刷新所有模型"""
        if not self._sync_connected:
            raise ModelError("Not connected. Call connect() first.")

        with self._lock:
            # 清空所有模型
            table_names = list(self.models.keys())
            self.models.clear()

            # 重新创建所有模型
            for table_name in table_names:
                self._create_model_for_table(table_name)

        logger.info(f"Refreshed all models in instance {self.instance_id}")

    # ==================== Transaction Management ====================

    @asynccontextmanager
    async def async_transaction(self) -> AsyncGenerator[AsyncSession, None]:
        """
        异步事务上下文管理器

        Yields:
            AsyncSession用于事务操作
        """
        if not self.async_session_manager:
            raise TransactionError("Async session manager not available for transactions")

        async with self.async_session_manager.get_session() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise

    @contextmanager
    def sync_transaction(self):
        """
        同步事务上下文管理器

        Yields:
            Session用于事务操作
        """
        if not self.session_manager:
            raise TransactionError("Session manager not available for transactions")

        with self.session_manager.get_session() as session:
            try:
                yield session
                session.commit()
            except Exception:
                session.rollback()
                raise

    # ==================== Configuration and Helper Methods ====================

    def _detect_database_type(self) -> DatabaseType:
        """检测数据库类型"""
        url = self.config.database_url.lower()
        if 'mysql' in url:
            return DatabaseType.MYSQL
        elif 'postgresql' in url or 'postgres' in url:
            return DatabaseType.POSTGRESQL
        elif 'sqlite' in url:
            return DatabaseType.SQLITE
        elif 'oracle' in url:
            return DatabaseType.ORACLE
        elif 'mssql' in url or 'sqlserver' in url:
            return DatabaseType.SQLSERVER
        else:
            return DatabaseType.MYSQL  # 默认

    def _convert_to_legacy_config(self, config: ConnectionConfig) -> ORMConnectionConfig:
        """将新配置转换为旧配置格式"""
        # 构建数据库URL
        if config.database and (config.database.endswith('.db') or not config.host):
            # SQLite数据库
            url = f"sqlite:///{config.database}"
        elif config.host and config.username and config.password:
            # 网络数据库 - 对用户名和密码进行URL编码
            from urllib.parse import quote_plus
            encoded_username = quote_plus(config.username)
            encoded_password = quote_plus(config.password)

            if config.port == 3306:
                url = f"mysql+pymysql://{encoded_username}:{encoded_password}@{config.host}:{config.port}/{config.database}"
            elif config.port == 5432:
                url = f"postgresql+asyncpg://{encoded_username}:{encoded_password}@{config.host}:{config.port}/{config.database}"
            elif config.port == 1521:
                url = f"oracle://{encoded_username}:{encoded_password}@{config.host}:{config.port}/{config.database}"
            elif config.port == 1433:
                url = f"mssql://{encoded_username}:{encoded_password}@{config.host}:{config.port}/{config.database}"
            else:
                # 默认使用MySQL
                url = f"mysql+pymysql://{encoded_username}:{encoded_password}@{config.host}:{config.port}/{config.database}"
        else:
            # 如果没有完整的连接信息，使用database字段作为URL
            url = config.database or f"sqlite:///test.db"

        return ORMConnectionConfig(
            database_url=url,
            pool_size=config.pool_size,
            max_overflow=config.max_overflow,
            pool_timeout=config.pool_timeout,
            pool_recycle=config.pool_recycle,
            echo=config.echo
        )

    def _convert_from_legacy_config(self, config: ORMConnectionConfig) -> ConnectionConfig:
        """将旧配置转换为新配置格式"""
        from .....base.rdb.utils import parse_connection_string

        try:
            parsed = parse_connection_string(config.database_url)
            return ConnectionConfig(
                host=parsed.get('host', 'localhost'),
                port=parsed.get('port', 3306),
                database=parsed.get('database', ''),
                username=parsed.get('username', ''),
                password=parsed.get('password', ''),
                pool_size=config.pool_size,
                max_overflow=config.max_overflow,
                pool_timeout=config.pool_timeout,
                pool_recycle=config.pool_recycle,
                echo=config.echo
            )
        except Exception:
            # 如果解析失败，使用默认值
            return ConnectionConfig(
                host='localhost',
                port=3306,
                database='',
                username='',
                password='',
                pool_size=config.pool_size,
                max_overflow=config.max_overflow,
                pool_timeout=config.pool_timeout,
                pool_recycle=config.pool_recycle,
                echo=config.echo
            )

    # ==================== SQL Building Methods ====================

    def _build_select_sql(self, request: QueryRequest) -> tuple[str, Dict[str, Any]]:
        """构建SELECT SQL语句"""
        # 重用Universal客户端的SQL构建逻辑
        # 构建列列表
        if request.columns:
            columns = ", ".join([self.dialect.quote_identifier(col) for col in request.columns])
        else:
            columns = "*"

        # 构建基础查询
        sql = f"SELECT {columns} FROM {self.dialect.quote_identifier(request.table)}"
        params = {}

        # 构建WHERE子句
        if request.filters:
            # 处理单个过滤器或过滤器组
            if hasattr(request.filters, 'filters'):
                # 这是一个QueryFilterGroup
                where_clause, where_params = self._build_where_clause(request.filters)
            else:
                # 这是一个单个QueryFilter，包装成QueryFilterGroup
                from .....base.rdb import QueryFilterGroup, LogicalOperator
                filter_group = QueryFilterGroup(
                    operator=LogicalOperator.AND,
                    filters=[request.filters]
                )
                where_clause, where_params = self._build_where_clause(filter_group)

            sql += f" WHERE {where_clause}"
            params.update(where_params)

        # 构建ORDER BY子句
        if request.sorts:
            order_clauses = []
            for sort in request.sorts:
                direction = "DESC" if sort.order.value == "desc" else "ASC"
                order_clauses.append(f"{self.dialect.quote_identifier(sort.field)} {direction}")
            sql += f" ORDER BY {', '.join(order_clauses)}"

        # 构建LIMIT和OFFSET
        if request.limit:
            sql += f" LIMIT {request.limit}"

        if request.offset:
            sql += f" OFFSET {request.offset}"

        return sql, params

    def _build_insert_sql(self, request: InsertRequest) -> tuple[str, Dict[str, Any]]:
        """构建INSERT SQL语句"""
        if isinstance(request.data, list):
            # 批量插入
            if not request.data:
                raise ValueError("Insert data cannot be empty")

            first_record = request.data[0]
            columns = list(first_record.keys())
            quoted_columns = [self.dialect.quote_identifier(col) for col in columns]

            # 构建VALUES子句
            values_clauses = []
            params = {}
            for i, record in enumerate(request.data):
                value_placeholders = []
                for col in columns:
                    param_name = f"{col}_{i}"
                    value_placeholders.append(f":{param_name}")
                    params[param_name] = record.get(col)
                values_clauses.append(f"({', '.join(value_placeholders)})")

            sql = f"INSERT INTO {self.dialect.quote_identifier(request.table)} ({', '.join(quoted_columns)}) VALUES {', '.join(values_clauses)}"
        else:
            # 单条插入
            columns = list(request.data.keys())
            quoted_columns = [self.dialect.quote_identifier(col) for col in columns]
            placeholders = [f":{col}" for col in columns]

            sql = f"INSERT INTO {self.dialect.quote_identifier(request.table)} ({', '.join(quoted_columns)}) VALUES ({', '.join(placeholders)})"
            params = request.data

        return sql, params

    def _build_update_sql(self, request: UpdateRequest) -> tuple[str, Dict[str, Any]]:
        """构建UPDATE SQL语句"""
        # 构建SET子句
        set_clauses = []
        params = {}

        for col, value in request.data.items():
            set_clauses.append(f"{self.dialect.quote_identifier(col)} = :{col}")
            params[col] = value

        sql = f"UPDATE {self.dialect.quote_identifier(request.table)} SET {', '.join(set_clauses)}"

        # 构建WHERE子句
        if request.filters:
            # 处理单个过滤器或过滤器组
            if hasattr(request.filters, 'filters'):
                # 这是一个QueryFilterGroup
                where_clause, where_params = self._build_where_clause(request.filters)
            else:
                # 这是一个单个QueryFilter，包装成QueryFilterGroup
                from .....base.rdb import QueryFilterGroup, LogicalOperator
                filter_group = QueryFilterGroup(
                    operator=LogicalOperator.AND,
                    filters=[request.filters]
                )
                where_clause, where_params = self._build_where_clause(filter_group)

            sql += f" WHERE {where_clause}"
            # 避免参数名冲突
            for key, value in where_params.items():
                new_key = f"where_{key}"
                params[new_key] = value
                sql = sql.replace(f":{key}", f":{new_key}")

        return sql, params

    def _build_delete_sql(self, request: DeleteRequest) -> tuple[str, Dict[str, Any]]:
        """构建DELETE SQL语句"""
        sql = f"DELETE FROM {self.dialect.quote_identifier(request.table)}"
        params = {}

        # 构建WHERE子句
        if request.filters:
            # 处理单个过滤器或过滤器组
            if hasattr(request.filters, 'filters'):
                # 这是一个QueryFilterGroup
                where_clause, where_params = self._build_where_clause(request.filters)
            else:
                # 这是一个单个QueryFilter，包装成QueryFilterGroup
                from .....base.rdb import QueryFilterGroup, LogicalOperator
                filter_group = QueryFilterGroup(
                    operator=LogicalOperator.AND,
                    filters=[request.filters]
                )
                where_clause, where_params = self._build_where_clause(filter_group)

            sql += f" WHERE {where_clause}"
            params.update(where_params)

        return sql, params

    def _build_where_clause(self, filter_group) -> tuple[str, Dict[str, Any]]:
        """构建WHERE子句"""
        from .....base.rdb import QueryFilter, QueryFilterGroup, ComparisonOperator, LogicalOperator

        conditions = []
        params = {}
        param_counter = 0

        for filter_item in filter_group.filters:
            if isinstance(filter_item, QueryFilter):
                condition, filter_params = self._build_filter_condition(filter_item, param_counter)
                conditions.append(condition)
                params.update(filter_params)
                param_counter += len(filter_params)
            elif isinstance(filter_item, QueryFilterGroup):
                nested_condition, nested_params = self._build_where_clause(filter_item)
                conditions.append(f"({nested_condition})")
                params.update(nested_params)

        if filter_group.operator == LogicalOperator.AND:
            return " AND ".join(conditions), params
        else:
            return " OR ".join(conditions), params

    def _build_filter_condition(self, filter_obj, param_counter: int) -> tuple[str, Dict[str, Any]]:
        """构建单个过滤条件"""
        from .....base.rdb import ComparisonOperator

        column = self.dialect.quote_identifier(filter_obj.field)
        params = {}

        if filter_obj.operator == ComparisonOperator.EQ:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} = :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.NE:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} != :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.GT:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} > :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.GTE:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} >= :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.LT:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} < :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.LTE:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} <= :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.IN:
            if not isinstance(filter_obj.value, (list, tuple)):
                raise ValueError("IN operator requires a list or tuple value")

            placeholders = []
            for i, val in enumerate(filter_obj.value):
                param_name = f"param_{param_counter}_{i}"
                params[param_name] = val
                placeholders.append(f":{param_name}")

            return f"{column} IN ({', '.join(placeholders)})", params
        elif filter_obj.operator == ComparisonOperator.NOT_IN:
            if not isinstance(filter_obj.value, (list, tuple)):
                raise ValueError("NOT IN operator requires a list or tuple value")

            placeholders = []
            for i, val in enumerate(filter_obj.value):
                param_name = f"param_{param_counter}_{i}"
                params[param_name] = val
                placeholders.append(f":{param_name}")

            return f"{column} NOT IN ({', '.join(placeholders)})", params
        elif filter_obj.operator == ComparisonOperator.LIKE:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} LIKE :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.BETWEEN:
            if not isinstance(filter_obj.value, (list, tuple)) or len(filter_obj.value) != 2:
                raise ValueError("BETWEEN operator requires exactly 2 values")

            param_name1 = f"param_{param_counter}_start"
            param_name2 = f"param_{param_counter}_end"
            params[param_name1] = filter_obj.value[0]
            params[param_name2] = filter_obj.value[1]
            return f"{column} BETWEEN :{param_name1} AND :{param_name2}", params
        elif filter_obj.operator == ComparisonOperator.IS_NULL:
            return f"{column} IS NULL", params
        elif filter_obj.operator == ComparisonOperator.IS_NOT_NULL:
            return f"{column} IS NOT NULL", params
        else:
            raise ValueError(f"Unsupported operator: {filter_obj.operator}")

from typing import Dict, Any, Optional
import json # 仅用于打印下面的示例数据
from loguru import logger
from modules.pg_database.llm.llm_chat import get_embeddings
from modules.pg_database.pgvector.rag import hybrid_search
from utils.db.get_partitionkey import get_or_create_partition_key

def find_field_from_merged_info(
    table_identifier: str,
    column_identifier: str,
    merged_info: Dict[str, Dict[str, Any]],
    output_field: str # 新增参数，指定需要返回的字段名
) -> Optional[Any]: # 返回类型变为 Any，因为返回的字段值类型不确定
    """
    在 merged_column_info 中查找匹配的列并返回其指定的字段值。

    Args:
        table_identifier: 表的标识符，可以是 table_name 或 table_code。
        column_identifier: 列的标识符，可以是 col_name 或 col_real_name。
        merged_info: 包含所有列详细信息的大字典。
        output_field: 指定需要从匹配的条目中返回的字段名称 (例如 'col_code', 'col_id')。

    Returns:
        匹配条目中指定 output_field 的值，如果未找到或指定字段不存在则返回 None。
    """
    for column_details in merged_info.values():
        # 检查表标识符是否匹配（不区分大小写）
        table_match = (column_details.get('table_name', '').lower() == table_identifier.lower() or
                       column_details.get('table_code', '').lower() == table_identifier.lower() or 
                       column_details.get('table_real_name', '').lower() == table_identifier.lower() or
                       'table_'+column_details.get('table_name', '').lower() == table_identifier.lower() or
                       column_details.get('table_name', '').lower() == 'table_'+table_identifier.lower())

        # 获取 col_name 和 col_real_name，确保它们是字符串
        col_name = column_details.get('col_name', '')
        col_real_name = column_details.get('col_real_name', '')
        
        # 确保 col_name 和 col_real_name 是字符串
        if not isinstance(col_name, str):
            col_name = str(col_name)
        if not isinstance(col_real_name, str):
            col_real_name = str(col_real_name)

        # 检查列标识符是否匹配（不区分大小写）
        column_match = (col_name.lower() == column_identifier.lower() or
                        col_real_name.lower() == column_identifier.lower())

        # 如果表和列都匹配，则返回指定 output_field 的值
        if table_match and column_match:
            # 使用 .get() 以防指定的 output_field 不存在于字典中
            return column_details.get(output_field,None)

    # 如果遍历完所有条目仍未找到匹配项，则返回 None
    return None

def get_col_id_from_mysql(
    mysql_client: Any,
    column_identifier: str,
    mysql_table_name: str = 'model_info' # The name of the MySQL table holding column metadata
) -> Optional[Dict[str, Any]]: # Changed return type to Dict
    """
    Queries MySQL (model_info table) based only on col_name 
    and returns a dictionary of column details if found.

    Args:
        mysql_client: The MySQL client instance.
        column_identifier: Column identifier (expected to match col_name).
        mysql_table_name: The name of the MySQL table holding column metadata.

    Returns:
        A dictionary containing column details (col_id, col_code, etc.) or None.
    """
    if not mysql_client or not column_identifier:
        logger.warning("MySQL client or column_identifier missing for get_col_id_from_mysql")
        return None

    # Define the columns we want to retrieve to build the dictionary
    # Adjust these based on the actual columns in your mysql_table_name
    query_cols = [
        "col_id", "col_code", "table_code", "table_name", 
        "col_name", "col_real_name", "col_name_cn", 
        "col_desc", "col_type"
    ]
    
    query_op = {
        "op_type": "SELECT",
        "table_name": mysql_table_name,
        "query_cols": query_cols,
        "data_dict": {
            "logic": "AND", 
            "conditions": [
                {"type": "=", "col_name": "col_name", "col_val": column_identifier}
            ]
        },
        "limit": 1 
    }

    try:
        response = mysql_client.batch_operation(query_op)
        result_list = None
        success = False
        if isinstance(response, (list, tuple)) and len(response) > 0 and isinstance(response[0], list):
            result_list = response[0] 
            success = True 
        elif isinstance(response, list):
            result_list = response
            success = True 
        else:
            logger.warning(f"MySQL query for col_name '{column_identifier}' returned unexpected type: {type(response)}. Value: {response}")
            success = False

        if success:
            if result_list: # Found at least one record
                col_details = result_list[0]
                
                # Explicitly check if col_id exists and is not None from the direct query
                has_direct_col_id = 'col_id' in col_details and col_details['col_id'] is not None
                
                if has_direct_col_id:
                    col_details['col_id'] = str(col_details['col_id'])
                # ONLY if col_id wasn't found directly, try extracting from col_code
                elif 'col_code' in col_details and isinstance(col_details['col_code'], str) and '#' in col_details['col_code']:
                    try:
                        extracted_id = col_details['col_code'].split('#')[-1]
                        col_details['col_id'] = str(extracted_id) # Add the extracted col_id
                        logger.info(f"Extracted col_id '{extracted_id}' from col_code {col_details['col_code']}")
                    except IndexError:
                        logger.warning(f"Could not extract col_id from col_code {col_details['col_code']}")
                        # Ensure col_id key doesn't exist if extraction fails
                        col_details.pop('col_id', None) 
                # If neither direct col_id nor extraction worked, ensure no col_id key
                elif 'col_id' not in col_details: 
                    logger.warning(f"Neither direct col_id nor col_code available/suitable for extraction for {column_identifier}")
                    col_details.pop('col_id', None) # Ensure consistency

                logger.info(f"Final col_details before return from MySQL lookup: {col_details}") # Log before returning
                return col_details 
            else:
                logger.info(f"No matching record found in MySQL for col_name '{column_identifier}'.")
                return None
        else:
            logger.warning(f"MySQL query failed or returned unexpected data for col_name '{column_identifier}'. Response: {response}")
            return None

    except Exception as e:
        logger.error(f"Error during MySQL query for col details (col_name: '{column_identifier}'): {e}", exc_info=True)
        return None

# Implementation for vector search on column metadata
def vector_search_column_metadata(
    pg_client: Any,
    mysql_client: Any, # Needed for partition key
    table_identifier: str,
    column_identifier: str,
    model_id: str, # Added model_id
    distance_threshold: float = 0.3 # Added distance threshold
) -> Optional[Dict[str, Any]]: # Changed return type to Dict
    """
    Performs vector search on column metadata embeddings to find the best match 
    and returns a dictionary of its details.

    Args:
        pg_client: The Pgvector client instance.
        mysql_client: The MySQL client instance (for partition key).
        table_identifier: Table name or code to scope the search.
        column_identifier: The column name/identifier to search for semantically.
        model_id: The model ID for partitioning.
        distance_threshold: Maximum allowed distance for a match.

    Returns:
        A dictionary containing column details of the best match, or None.
    """
    logger.info(f"Attempting vector search fallback for column '{column_identifier}' in table '{table_identifier}' (model: {model_id}).")
    
    if not pg_client or not mysql_client or not model_id:
        logger.warning("Missing pg_client, mysql_client, or model_id for vector search fallback.")
        return None

    try:
        # 1. Get Partition Key (Assuming partition based on model_id)
        # Adjust partition name strategy if different (e.g., model_id + table_identifier?)
        partition_key = get_or_create_partition_key(model_id, mysql_client)
        if not partition_key:
            logger.error(f"Failed to get partition key for model_id: {model_id}")
            return None
        
        # 2. Get embedding for the column identifier we are searching for
        embedding_response = get_embeddings(column_identifier)
        if not embedding_response or "data" not in embedding_response or not embedding_response["data"] or 'embedding' not in embedding_response["data"][0]:
            logger.error(f"Failed to get embedding for column identifier: {column_identifier}")
            return None
        search_embedding = embedding_response["data"][0]['embedding']

        # 3. Prepare for hybrid_search
        vec_dict = {
            "embedding": search_embedding # Assuming embedding column name is 'embedding'
        }
        # Using simple vector ranking for fallback search
        rank_dict = {"type": "vector", "rank_rule": [{"embedding": 1.0}]}
        out_fields = ["col_id", "distance"] # Assuming 'col_id' is stored with the embedding
        # Assuming a field 'table_key' stores table_name or table_code with the column embedding
        expr = f"table_key = '{table_identifier}'" 
        embedding_table = 'hsbc_embedding_data' # Assuming table name
        pg_client.set_table(embedding_table)

        # 4. Execute search
        results = hybrid_search(
            pgvector_client=pg_client,
            table_name=embedding_table,
            vec_dict=vec_dict,
            rank_dict=rank_dict,
            out_filed=out_fields,
            topk=1,
            expr=expr,
            partition_name=partition_key,
            metric_type="cosine"
        )
        logger.info(f"Vector search fallback results for column '{column_identifier}': {results}")

        # 5. Process result
        if results and isinstance(results, list) and len(results) > 0:
            top_result = results[0]
            distance = top_result.get('distance')
            
            if distance is not None and distance < distance_threshold:
                logger.info(f"Vector search found matching column details with distance: {distance} (Threshold: {distance_threshold})")
                # Ensure col_id is string
                if 'col_id' in top_result and top_result['col_id'] is not None:
                    top_result['col_id'] = str(top_result['col_id'])
                # Remove distance field before returning
                top_result.pop('distance', None)
                return top_result # Return the whole dictionary
            else:
                logger.warning(f"Vector search top result for '{column_identifier}' did not meet threshold. Distance: {distance}")
                return None
        else:
            logger.info(f"Vector search fallback found no results for column '{column_identifier}' in table '{table_identifier}'.")
            return None

    except Exception as e:
        logger.error(f"Error during vector search fallback for column '{column_identifier}': {e}", exc_info=True)
        return None

def get_col_id_robust(
    mysql_client: Any,
    pg_client: Any,
    table_identifier: str,
    column_identifier: str,
    model_id: str # Added model_id
) -> Optional[Dict[str, Any]]: # Changed return type to Dict
    """
    Robustly retrieves column details dict by first trying direct MySQL lookup,
    then falling back to vector search on column metadata.

    Args:
        mysql_client: MySQL client instance.
        pg_client: Pgvector client instance.
        table_identifier: Table name or table code.
        column_identifier: Column name or column real name.
        model_id: The model ID for partitioning vector search.

    Returns:
        A dictionary containing column details if found, otherwise None.
    """
    # 1. Try direct MySQL lookup - call with correct arguments
    col_details_mysql = get_col_id_from_mysql(mysql_client, column_identifier)
    
    if col_details_mysql:
        return col_details_mysql
    
    # 2. If direct lookup fails, try vector search fallback
    logger.info(f"Direct MySQL lookup failed for col '{column_identifier}' (table context: '{table_identifier}'). Trying vector search fallback.")
    col_details_vector = vector_search_column_metadata(pg_client, mysql_client, table_identifier, column_identifier, model_id)
    if col_details_vector:
        logger.info(f"Vector search found column details for '{column_identifier}' in table '{table_identifier}'.")
        return col_details_vector # Return the dict found via vector search
    else:
        logger.warning(f"Vector search fallback also failed to find details for column '{column_identifier}' in table '{table_identifier}'.")
        return None # Both methods failed


if __name__ == "__main__":
    # 假设这是你的包含所有列信息的 merged_column_info 字典
    # (使用你提供的示例数据)
    merged_column_info = {
    "adm_lon_varoius_loan_bal": {
        "col_id": "1445",
        "col_code": "meta#13.adm_lon_varoius#1445",
        "table_code": "13.adm_lon_varoius",
        "table_name": "1001",
        "table_real_name": "adm_lon_varoius",
        "col_name": "adm_lon_varoius_loan_bal",
        "col_real_name": "loan_bal",
        "col_name_cn": "本金余额",
        "col_desc": "本金余额",
        "col_type": "NUMBER",
        "col_data_example": None
    },
    "adm_lon_varoius_corp_hold_type": {
        "col_id": "1419",
        "col_code": "meta#13.adm_lon_varoius#1419",
        "table_code": "13.adm_lon_varoius",
        "table_name": "1001",
        "table_real_name": "adm_lon_varoius",
        "col_name": "adm_lon_varoius_corp_hold_type",
        "col_real_name": "corp_hold_type",
        "col_name_cn": "企业控股类型",
        "col_desc": "企业控股类型",
        "col_type": "STRING",
        "col_data_example": None
    },
    "adm_lon_varoius_loan_bal_cny": {
        "col_id": "1446",
        "col_code": "meta#13.adm_lon_varoius#1446",
        "table_code": "13.adm_lon_varoius",
        "table_name": "1001",
        "table_real_name": "adm_lon_varoius",
        "col_name": "adm_lon_varoius_loan_bal_cny",
        "col_real_name": "loan_bal_cny",
        "col_name_cn": "本金余额_折人民币",
        "col_desc": "本金余额_折人民币",
        "col_type": "NUMBER",
        "col_data_example": None
    },
    "adm_pub_branch_level_info_bank_code": {
        "col_id": "1524",
        "col_code": "meta#13.adm_pub_branch_level_info#1524",
        "table_code": "13.adm_pub_branch_level_info",
        "table_name": "1001",
        "table_real_name": "adm_pub_branch_level_info",
        "col_name": "adm_pub_branch_level_info_bank_code",
        "col_real_name": "bank_code",
        "col_name_cn": "no_comment",
        "col_desc": "no_comment",
        "col_type": "STRING",
        "col_data_example": None
    }
    }
    # --- 使用示例 ---

    # 示例 1：获取 col_code
    col_code_result = find_field_from_merged_info(
        "1001",
        "loan_bal",
        merged_column_info,
        "col_code" # 指定输出 col_code
    )
    print(f"示例 1 (获取 col_code): {col_code_result}")

    # 示例 2：获取 col_id
    col_id_result = find_field_from_merged_info(
        "1001",
        "adm_lon_varoius_corp_hold_type",
        merged_column_info,
        "col_id" # 指定输出 col_id
    )
    print(f"示例 2 (获取 col_id): {col_id_result}")

    # 示例 3：获取 col_name_cn
    col_cn_result = find_field_from_merged_info(
        "1001",
        "bank_code",
        merged_column_info,
        "col_name_cn" # 指定输出 col_name_cn
    )
    print(f"示例 3 (获取 col_name_cn): {col_cn_result}")


    # 示例 4：获取不存在的字段
    non_existent_field_result = find_field_from_merged_info(
        "1001",
        "loan_bal",
        merged_column_info,
        "non_existent_field" # 指定一个不存在的字段
    )
    print(f"示例 4 (获取不存在的字段): {non_existent_field_result}")


    # 示例 5：未找到匹配项
    not_found_result = find_field_from_merged_info(
        "non_existent_table",
        "non_existent_col",
        merged_column_info,
        "col_code" # 即使指定了字段，也找不到匹配项
    )
    print(f"示例 5 (未找到): {not_found_result}")

    # Example usage for the new robust function (requires running clients)
    # from utils.db.client_util import mysql_client, pgvector_client # Assuming you have these
    # if mysql_client and pgvector_client:
    #     robust_col_id = get_col_id_robust(mysql_client, pgvector_client, "adm_lon_varoius", "loan_bal")
    #     print(f"Example Robust Lookup: {robust_col_id}")
    #     robust_col_id_typo = get_col_id_robust(mysql_client, pgvector_client, "adm_lon_varoius", "loan balance") # Example typo
    #     print(f"Example Robust Lookup (Typo): {robust_col_id_typo}") 
    # else:
    #     print("MySQL or Pgvector client not available for testing get_col_id_robust")
    pass
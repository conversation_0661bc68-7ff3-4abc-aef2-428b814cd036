from __future__ import annotations
from typing import Dict, List, Set, Iterable, Any
import sqlglot
from sqlglot import expressions as exp
import logging

logger = logging.getLogger(__name__)

# =====================================================
# 通用小工具
# =====================================================
def build_column_to_tables(table_column_dict: Dict[str, Set[str]]) -> Dict[str, Set[str]]:
    """
    根据 {表: {字段集合}} 生成反向索引 {字段: {可能的真实表}}
    """
    column_to_tables: Dict[str, Set[str]] = {}
    for tbl, cols in table_column_dict.items():
        for col in cols:
            column_to_tables.setdefault(col, set()).add(tbl)
    return column_to_tables


def _build_alias_map(select_node: exp.Select) -> Dict[str, str]:
    """根据某个 SELECT 节点构建 别名->真实表名 映射"""
    alias_to_real: Dict[str, str] = {}
    for tbl_node in select_node.find_all(exp.Table):
        real_name = tbl_node.this.this if tbl_node.this else ""
        alias = tbl_node.alias if tbl_node.alias else real_name
        alias_to_real[alias] = real_name
    return alias_to_real


def _replace_alias_in_expr(node: exp.Expression,
                           alias_to_real: Dict[str, str]) -> exp.Expression:
    """把表达式里的列别名统一换成真实表名"""
    for col in node.find_all(exp.Column):
        if col.table and col.table in alias_to_real:
            col.set("table", alias_to_real[col.table])
    return node


def _split_to_atomic_conditions(expr: exp.Expression) -> List[str]:
    """把 AND/OR 连接拆到最小单元"""
    if isinstance(expr, exp.And):
        return _split_to_atomic_conditions(expr.left) + _split_to_atomic_conditions(expr.right)
    if isinstance(expr, exp.Or):
        return [expr.sql()]
    return [expr.sql()]

from sqlglot import tokenize, TokenType
def _quote_digit_ident_safe(sql: str, dialect: str = "mysql", quote_char: str = "`") -> str:
    """
    仅对数字开头的标识符加引号，保留空格、注释、大小写等原始结构
    """
    # 1. 按字符位置生成可变列表，方便原地替换
    buf = list(sql)
    logger.debug(buf)   
    col_list=[]

    # 2. 逐个 token 处理
    for tok in tokenize(sql, dialect=dialect):
        if tok.token_type == TokenType.VAR and tok.text[0].isdigit():
            logger.debug(tok.text)
            for i in col_list:
                if tok.text != i:
                    col_list.append(tok.text)
                    sql = sql.replace(tok.text, quote_char + tok.text + quote_char)


    # 3. 拼回字符串
    return sql

def _safe_parse(sql: str, dialect: str = "mysql") -> sqlglot.Expression:
    quoted_sql = _quote_digit_ident_safe(sql, dialect=dialect)
    try:
        return sqlglot.parse_one(quoted_sql, dialect=dialect)
    except Exception as e:
        raise ValueError(f"SQL 解析失败: {e}")
# =====================================================
# 三大核心功能
# =====================================================
# # 默认的表-字段字典（用于演示和测试）
# table_column_dict: Dict[str, Set[str]] = {
#     "orders": {"id", "amount", "price", "cust_id", "order_date"},
#     "customer": {"id", "name", "phone"},
#     "items": {"id", "price", "order_id", "qty"}
# }

# # 预计算反向索引，全局只需一次
# column_to_tables = build_column_to_tables(table_column_dict)


def extract_columns_with_real_table(sql: str) -> Dict[str, str]:
    """
    提取 SQL 中所有字段与其真实表名的映射；
    若 AST 里未给出表前缀，则用 table_column_dict 反查补全。
    返回：{字段名: 真实表名}
    """
    try:
        tree = _safe_parse(sql)
    except Exception as e:
        raise ValueError(f"SQL 解析失败: {e}")

    mapping: Dict[str, str] = {}
    for select in tree.find_all(exp.Select):
        alias_to_real = _build_alias_map(select)
        for col in select.find_all(exp.Column):
            col_name = col.this.this if col.this else ""
            alias = col.table if col.table else ""
            real_table = alias_to_real.get(alias, "")

            if not real_table:                       # 反查补全
                candidates = column_to_tables.get(col_name, set())
                if len(candidates) == 1:
                    real_table = candidates.pop()
                # 其余情况保持空串
            mapping.setdefault(col_name, real_table)
    return mapping


# =====================================================
# 按“块”返回原始 WHERE 条件
# =====================================================
def extract_where_conditions(sql: str) -> List[str]:
    """
    返回每个 SELECT 块的完整 WHERE 条件（不拆分 AND/OR）。
    多个 SELECT / UNION 时，以列表形式给出。
    """
    try:
        tree = _safe_parse(sql)
    except Exception as e:
        raise ValueError(f"SQL 解析失败: {e}")

    result: List[str] = []
    for select in tree.find_all(exp.Select):
        alias_to_real = _build_alias_map(select)
        where_node = select.find(exp.Where)
        if not where_node:
            result.append("")          # 占位，保持索引一致性
            continue

        # 保留原始逻辑，不做拆分
        expr = _replace_alias_in_expr(where_node.this, alias_to_real)
        result.append(expr.sql())
    return result


# =====================================================
# 按“块”返回原始 ON 条件（一个 JOIN 对应一个元素）
# =====================================================
def extract_join_conditions(sql: str) -> List[str]:
    """
    返回每个 JOIN 的完整 ON 条件（不拆分 AND/OR）。
    按出现顺序组成列表。
    """
    try:
        tree = _safe_parse(sql)
    except Exception as e:
        raise ValueError(f"SQL 解析失败: {e}")

    result: List[str] = []
    for select in tree.find_all(exp.Select):
        alias_to_real = _build_alias_map(select)
        for join in select.find_all(exp.Join):
            on_expr = join.args.get("on")
            if not on_expr:
                result.append("")
                continue
            on_expr = _replace_alias_in_expr(on_expr, alias_to_real)
            result.append(on_expr.sql())
    return result

def extract_real_tables(sql: str) -> Set[str]:
    """
    返回 SQL 中所有真实物理表名（不含临时表 / 子查询 / CTE）
    """
    try:
        tree = _safe_parse(sql)
    except Exception as e:
        raise ValueError(f"SQL 解析失败: {e}")

    real_tables: Set[str] = set()
    # 遍历所有 Table 节点
    for tbl_node in tree.find_all(exp.Table):
        real_name = tbl_node.this.this if tbl_node.this else ""
        if real_name:                       # 过滤空字符串
            real_tables.add(real_name)
    return real_tables

# -----------------------------------------------------
# 新增：提取 SELECT 中带 “value” 字样的字段及其计算逻辑
# -----------------------------------------------------
def extract_value_columns_logic(sql: str) -> Dict[str, str]:
    """
    返回所有在 SELECT 列表中，别名或表达式本身包含 'value' 字样的字段
    及其原始计算表达式（未替换表别名）。
    返回：{ alias -> 原始表达式字符串 }
    """
    try:
        tree = _safe_parse(sql)
    except Exception as e:
        raise ValueError(f"SQL 解析失败: {e}")

    value_exprs: Dict[str, str] = {}

    for select in tree.find_all(exp.Select):
        for proj in select.expressions:
            # 1. 真正的最终别名（优先 AS，没有再取表达式本身）
            alias = proj.alias or proj.sql()
            # 2. 只匹配别名或表达式本身
            if "value" not in str(alias).lower():
                continue

            # 3. 仅取表达式部分（不包含 FROM/WHERE）
            expr_sql = proj.sql()
            # 4. 如果表达式是子查询，只保留 SELECT 子句
            if isinstance(proj, exp.Subquery):
                # 子查询时，只取子查询的 SELECT 表达式
                sub_select = proj.unalias()
                if isinstance(sub_select, exp.Subqueryable):
                    expr_sql = sub_select.expressions[0].sql() if sub_select.expressions else expr_sql

            value_exprs[alias] = expr_sql

    return value_exprs

def run_sql_analysis(
        sql: str,
        *,
        table_dict: Dict[str, Set[str]] | None = None,
        actions: Iterable[str] | str = "all"
) -> Dict[str, Any]:
    # 1. 选择表-字段字典
    tc_dict = table_column_dict if table_dict is None else table_dict

    # 2. 更新全局反向索引（如果调用方换了字典）
    global column_to_tables
    column_to_tables = build_column_to_tables(tc_dict)

    # 3. 标准化 actions
    if isinstance(actions, str):
        actions = [actions.lower()]
    else:
        actions = [str(a).lower() for a in actions]

    if "all" in actions:
        actions = {"columns", "where", "join", "tables", "value"}

    # 4. 按需执行
    output: Dict[str, Any] = {}
    if "columns" in actions:
        output["columns"] = extract_columns_with_real_table(sql)
    if "where" in actions:
        output["where"] = extract_where_conditions(sql)
    if "join" in actions:
        output["join"] = extract_join_conditions(sql)
    if "tables" in actions:
        output["tables"] = extract_real_tables(sql)
    if "value" in actions:
        output["value"] = extract_value_columns_logic(sql)

    return output

def find_all_tables(sql:str):
    table_s = _safe_parse(sql).find_all(exp.Table)
    table_list=[table_.dump()['args']['this']['args']['this']  for table_ in table_s]
    return table_list


# =====================================================
# 快速自测
# =====================================================
if __name__ == "__main__":
    test_sql = """
    SELECT 
        b.org_no,
        CASE 
            WHEN b.ccy_code = 'USD'              THEN '1'  -- 1.美元USD
            WHEN b.ccy_code = 'EUR'              THEN '2'  -- 2.欧元EUR
            WHEN b.ccy_code = 'JPY'              THEN '3'  -- 3.日圆JPY
            WHEN b.ccy_code = 'GBP'              THEN '4'  -- 4.英镑GBP
            WHEN b.ccy_code = 'HKD'              THEN '5'  -- 5.港元HKD
            WHEN b.ccy_code = 'CHF'              THEN '6'  -- 6.瑞士法郎CHF
            WHEN b.ccy_code = 'AUD'              THEN '7'  -- 7.澳大利亚元AUD
            WHEN b.ccy_code = 'CAD'              THEN '8'  -- 8.加拿大元CAD
            WHEN b.ccy_code IN ('YYY','XYZ','AAA','AAB','AAC','XAU','AUX','AUY','AAM')      THEN '9'  -- 9.黄金
        END AS row_id,
        CAST(SUM((b.debit_bal_cny +  b.credit_bal_cny) * a.optor) AS DECIMAL(20, 4)) AS value_a
    FROM idm_pb_index_item_mapping a
    INNER JOIN bdm_fin_gl_lrrkey b on a.item_code = b.lrr_code and b.accounting_cycle = '2' and b.data_dt = LAST_DAY(ADD_MONTHS('${load_date}',-1))
    WHERE a.index_code = 'OA-25-09'     --其他资产-其他资产C
    and b.lrr_code not in ('A23990Z00Z00002','L23990Z00Z00002')
    GROUP BY b.org_no,row_id
    UNION ALL
    SELECT 
        b.org_no,
        CASE 
            WHEN b.ccy_code = 'USD'              THEN '1'  -- 1.美元USD
            WHEN b.ccy_code = 'EUR'              THEN '2'  -- 2.欧元EUR
            WHEN b.ccy_code = 'JPY'              THEN '3'  -- 3.日圆JPY
            WHEN b.ccy_code = 'GBP'              THEN '4'  -- 4.英镑GBP
            WHEN b.ccy_code = 'HKD'              THEN '5'  -- 5.港元HKD
            WHEN b.ccy_code = 'CHF'              THEN '6'  -- 6.瑞士法郎CHF
            WHEN b.ccy_code = 'AUD'              THEN '7'  -- 7.澳大利亚元AUD
            WHEN b.ccy_code = 'CAD'              THEN '8'  -- 8.加拿大元CAD
            WHEN b.ccy_code IN ('YYY','XYZ','AAA','AAB','AAC','XAU','AUX','AUY','AAM')      THEN '9'  -- 9.黄金
        END AS row_id,
        CAST(SUM((b.debit_bal_cny +  b.credit_bal_cny) * a.optor) AS DECIMAL(20, 4)) AS value_a
    FROM idm_pb_index_item_mapping a
    INNER JOIN bdm_fin_gl_lrrkey b on a.item_code = b.lrr_code and b.accounting_cycle = '2' and b.data_dt = LAST_DAY(ADD_MONTHS('${load_date}',-1))
    WHERE a.index_code = 'OA-25-05'     --其他资产-其他资产C
    AND nvl(b.lrr_code,'') <> 'L21060Z00Z00003'
    GROUP BY b.org_no,row_id
    """

    test_sql1 = """
WITH tmp_00 AS ( 
SELECT data_dt, amount * 0.025 AS value_a, amount AS value_b 
FROM bdm_gdc_index_fin 
WHERE data_dt between  '${date_start}' and  '${date_end}' AND 1104_report = 'G40' AND index_code = '2_A' ), ccp_margin AS ( 
SELECT 1104_report, reference_content_1, vlookup_key_value_1, vlookup_key_value_2, vlookup_key_value_3, value_1, acct_type, p_dt 
FROM ( 
SELECT a.1104_report, a.reference_content_1, a.vlookup_key_value_1, a.vlookup_key_value_2, a.vlookup_key_value_3, a.value_1, a.value_2, a.p_dt 
FROM ods_cdp_gdc_table_1104_reference_table_full AS a 
WHERE a.p_dt between  '${date_start}' and  '${date_end}' AND a.1104_report = 'G14' AND a.reference_content_1 = 'CCP Margin' ) AS ui LATERAL VIEW EXPLODE(SPLIT(ui.value_2, "","")) adtable AS acct_type ), t_all AS ( 
SELECT '1_4_1' AS row_id, SUM(a.non_exempt_exposure) AS value_a1, SUM(CASE WHEN a.exposure_type = '1' THEN a.total_risk_exposure_miti END) AS value_b, SUM(CASE WHEN a.exposure_type = '2' THEN a.total_risk_exposure_miti END) AS value_c, SUM(CASE WHEN a.exposure_type = '3' THEN a.total_risk_exposure_miti END) AS value_d, SUM(CASE WHEN a.exposure_type = '4' THEN a.total_risk_exposure_miti END) AS value_e, SUM(CASE WHEN a.exposure_type = '5' THEN a.total_risk_exposure_miti END) AS value_f, SUM(CASE WHEN a.exposure_type = '6' THEN a.total_risk_exposure_miti END) AS value_g 
,a.data_dt as data_dt FROM adm_rsk_exposure AS a 
WHERE a.data_dt between  '${date_start}' and  '${date_end}' AND group_cust_no IN ( 
SELECT group_cust_no 
FROM ( 
SELECT group_cust_no 
FROM adm_rsk_exposure AS p1 
LEFT JOIN tmp_00 AS p2 
ON p1.data_dt = p2.data_dt 
WHERE p1.data_dt between  '${date_start}' and  '${date_end}' AND group_cust_type = '01' AND group_cust_flag = 'Y' 
GROUP BY group_cust_no HAVING SUM(total_risk_exposure_miti) >= MAX(p2.value_a) 
ORDER BY SUM(non_exempt_exposure) DESC LIMIT 1 ) AS a ) group by a.data_dt) 
SELECT data_dt, value_a as indicator 
FROM ( 
SELECT 'CNHSBC900Z' AS org_no, row_id AS rowid, CAST(( COALESCE(value_b, 0) + COALESCE(value_c, 0) + COALESCE(value_d, 0) + COALESCE(value_e, 0) + COALESCE(value_f, 0) + COALESCE(value_g, 0) ) AS DECIMAL(20, 4)) AS value_a, GETDATE() AS rec_creat_dt_tm 
,data_dt FROM t_all ) sub 
WHERE org_no='${org_num}' and rowid = '1_4_1'


    """

    # ----------------- 表-字段字典 -----------------
    table_column_dict: Dict[str, Set[str]] = {
        "orders": {"id", "amount", "price", "cust_id", "order_date"},
        "customer": {"id", "name", "phone"},
        "items": {"id", "price", "order_id", "qty"}
    }



    # 1. 全量跑
    print("=== run all ===")
    result = run_sql_analysis(test_sql1,table_dict=table_column_dict, actions="all")
    print(result)


    # 2. 只跑部分
    print("=== run partial ===")
    print(run_sql_analysis(test_sql,table_dict=table_column_dict, actions=["where", "join"]))

    # 3. 只跑部分
    print("=== run select ===")
    print(run_sql_analysis(test_sql1, table_dict=table_column_dict, actions=["tables"]))
    print("=== run find tables ===")
    print(find_all_tables(test_sql1))


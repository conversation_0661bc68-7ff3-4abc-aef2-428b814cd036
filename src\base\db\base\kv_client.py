from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union


class KVClient(ABC):
    """
    键值数据库客户端的抽象基类。
    定义了与键值数据库（如Redis）交互的基本操作接口。
    """

    @abstractmethod
    def connect(self) -> None:
        """
        连接到键值数据库。
        """
        pass

    @abstractmethod
    def disconnect(self) -> None:
        """
        断开与键值数据库的连接。
        """
        pass

    @abstractmethod
    def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """
        设置键值对。

        Args:
            key: 键名
            value: 值
            expire: 过期时间（秒），默认为None（永不过期）

        Returns:
            设置是否成功
        """
        pass

    @abstractmethod
    def get(self, key: str) -> Any:
        """
        获取指定键的值。

        Args:
            key: 键名

        Returns:
            键对应的值，如果键不存在则返回None
        """
        pass

    @abstractmethod
    def delete(self, key: str) -> bool:
        """
        删除指定键。

        Args:
            key: 键名

        Returns:
            删除是否成功
        """
        pass

    @abstractmethod
    def exists(self, key: str) -> bool:
        """
        检查键是否存在。

        Args:
            key: 键名

        Returns:
            键是否存在
        """
        pass


    @abstractmethod
    def keys(self, pattern: str) -> List[str]:
        """
        查找所有符合给定模式的键。

        Args:
            pattern: 键名模式，支持通配符

        Returns:
            符合模式的键名列表
        """
        pass

    @abstractmethod
    def expire(self, key: str, seconds: int) -> bool:
        """
        设置键的过期时间。

        Args:
            key: 键名
            seconds: 过期时间（秒）

        Returns:
            设置是否成功
        """
        pass
    
    # 哈希表操作
    @abstractmethod
    def hset(self, key: str, field: str, value: Any) -> bool:
        """
        设置哈希表中的字段值。

        Args:
            key: 哈希表键名
            field: 字段名
            value: 字段值

        Returns:
            设置是否成功
        """
        pass

    @abstractmethod
    def hget(self, key: str, field: str) -> Any:
        """
        获取哈希表中的字段值。

        Args:
            key: 哈希表键名
            field: 字段名

        Returns:
            字段值，如果字段不存在则返回None
        """
        pass

    @abstractmethod
    def hmset(self, key: str, mapping: Dict[str, Any]) -> bool:
        """
        批量设置哈希表中的字段值。

        Args:
            key: 哈希表键名
            mapping: 字段名和字段值的映射

        Returns:
            设置是否成功
        """
        pass

    @abstractmethod
    def hmget(self, key: str, fields: List[str]) -> Dict[str, Any]:
        """
        批量获取哈希表中的字段值。

        Args:
            key: 哈希表键名
            fields: 字段名列表

        Returns:
            字段名和字段值的映射
        """
        pass

    @abstractmethod
    def hdel(self, key: str, field: str) -> bool:
        """
        删除哈希表中的字段。

        Args:
            key: 哈希表键名
            field: 字段名

        Returns:
            删除是否成功
        """
        pass

    @abstractmethod
    def hgetall(self, key: str) -> Dict[str, Any]:
        """
        获取哈希表中的所有字段和值。

        Args:
            key: 哈希表键名

        Returns:
            字段名和字段值的映射
        """
        pass

    # 列表操作
    @abstractmethod
    def lpush(self, key: str, *values: Any) -> int:
        """
        将一个或多个值插入到列表头部。

        Args:
            key: 列表键名
            *values: 要插入的值

        Returns:
            操作后列表的长度
        """
        pass

    @abstractmethod
    def rpush(self, key: str, *values: Any) -> int:
        """
        将一个或多个值插入到列表尾部。

        Args:
            key: 列表键名
            *values: 要插入的值

        Returns:
            操作后列表的长度
        """
        pass

    @abstractmethod
    def lpop(self, key: str) -> Any:
        """
        移除并返回列表的第一个元素。

        Args:
            key: 列表键名

        Returns:
            列表的第一个元素，如果列表为空则返回None
        """
        pass

    @abstractmethod
    def rpop(self, key: str) -> Any:
        """
        移除并返回列表的最后一个元素。

        Args:
            key: 列表键名

        Returns:
            列表的最后一个元素，如果列表为空则返回None
        """
        pass

    @abstractmethod
    def lrange(self, key: str, start: int, stop: int) -> List[Any]:
        """
        获取列表指定范围内的元素。

        Args:
            key: 列表键名
            start: 开始位置
            stop: 结束位置

        Returns:
            指定范围内的元素列表
        """
        pass

    # 管道操作
    @abstractmethod
    def pipeline(self) -> 'Pipeline':
        """
        创建一个管道对象，用于批量执行命令。

        Returns:
            管道对象
        """
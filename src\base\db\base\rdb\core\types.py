"""
RDB核心类型定义

定义数据库抽象层的核心类型，确保类型安全和一致性
"""

from typing import (
    Any, Dict, List, Optional, Union, Tuple, 
    Protocol, TypeVar, Generic, Callable, 
    AsyncContextManager, ContextManager
)
from enum import Enum
from datetime import datetime, date, time
import decimal

# ==================== 基础类型 ====================

# 数据库值类型
DatabaseValue = Union[
    None, bool, int, float, str, bytes,
    datetime, date, time, decimal.Decimal,
    List[Any], Dict[str, Any]
]

# 记录类型
DatabaseRecord = Dict[str, DatabaseValue]
DatabaseRecords = List[DatabaseRecord]

# 参数类型
QueryParameters = Dict[str, DatabaseValue]

# 连接字符串类型
ConnectionString = str

# ==================== 枚举类型 ====================

class DatabaseType(str, Enum):
    """数据库类型枚举"""
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    SQLITE = "sqlite"
    ORACLE = "oracle"
    SQLSERVER = "sqlserver"


class SortOrder(str, Enum):
    """排序方向"""
    ASC = "asc"
    DESC = "desc"


class ComparisonOperator(str, Enum):
    """比较操作符"""
    EQ = "eq"           # =
    NE = "ne"           # !=
    GT = "gt"           # >
    GTE = "gte"         # >=
    LT = "lt"           # <
    LTE = "lte"         # <=
    IN = "in"           # IN
    NOT_IN = "not_in"   # NOT IN
    LIKE = "like"       # LIKE
    ILIKE = "ilike"     # ILIKE (case insensitive)
    IS_NULL = "is_null" # IS NULL
    IS_NOT_NULL = "is_not_null" # IS NOT NULL
    BETWEEN = "between" # BETWEEN


class LogicalOperator(str, Enum):
    """逻辑操作符"""
    AND = "and"
    OR = "or"
    NOT = "not"


class JoinType(str, Enum):
    """JOIN类型"""
    INNER = "inner"
    LEFT = "left"
    RIGHT = "right"
    FULL = "full"
    CROSS = "cross"


class TransactionIsolation(str, Enum):
    """事务隔离级别"""
    READ_UNCOMMITTED = "read_uncommitted"
    READ_COMMITTED = "read_committed"
    REPEATABLE_READ = "repeatable_read"
    SERIALIZABLE = "serializable"


class ConnectionPoolStatus(str, Enum):
    """连接池状态"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    CLOSED = "closed"


# ==================== 协议类型 ====================

class DatabaseConnection(Protocol):
    """数据库连接协议"""
    
    async def execute(self, query: str, parameters: Optional[QueryParameters] = None) -> Any:
        """执行查询"""
        ...
    
    async def fetch_all(self, query: str, parameters: Optional[QueryParameters] = None) -> DatabaseRecords:
        """获取所有结果"""
        ...
    
    async def fetch_one(self, query: str, parameters: Optional[QueryParameters] = None) -> Optional[DatabaseRecord]:
        """获取单个结果"""
        ...
    
    async def close(self) -> None:
        """关闭连接"""
        ...


class DatabaseTransaction(Protocol):
    """数据库事务协议"""
    
    async def commit(self) -> None:
        """提交事务"""
        ...
    
    async def rollback(self) -> None:
        """回滚事务"""
        ...
    
    async def __aenter__(self) -> 'DatabaseTransaction':
        """进入事务上下文"""
        ...
    
    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """退出事务上下文"""
        ...


class DatabasePool(Protocol):
    """数据库连接池协议"""
    
    async def acquire(self) -> DatabaseConnection:
        """获取连接"""
        ...
    
    async def release(self, connection: DatabaseConnection) -> None:
        """释放连接"""
        ...
    
    async def close(self) -> None:
        """关闭连接池"""
        ...
    
    def get_status(self) -> Dict[str, Any]:
        """获取连接池状态"""
        ...


# ==================== 泛型类型 ====================

T = TypeVar('T')
R = TypeVar('R')

class QueryResult(Generic[T]):
    """查询结果泛型"""
    
    def __init__(
        self,
        data: T,
        total_count: Optional[int] = None,
        execution_time: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.data = data
        self.total_count = total_count
        self.execution_time = execution_time
        self.metadata = metadata or {}


# ==================== 回调类型 ====================

# 连接回调
ConnectionCallback = Callable[[DatabaseConnection], None]
AsyncConnectionCallback = Callable[[DatabaseConnection], Any]

# 查询回调
QueryCallback = Callable[[str, QueryParameters], None]
AsyncQueryCallback = Callable[[str, QueryParameters], Any]

# 错误回调
ErrorCallback = Callable[[Exception], None]
AsyncErrorCallback = Callable[[Exception], Any]

# ==================== 配置类型 ====================

class PoolConfig(Protocol):
    """连接池配置协议"""
    min_size: int
    max_size: int
    max_queries: int
    max_inactive_connection_lifetime: float
    timeout: float


class SSLConfig(Protocol):
    """SSL配置协议"""
    enabled: bool
    cert_file: Optional[str]
    key_file: Optional[str]
    ca_file: Optional[str]
    verify_mode: Optional[str]


# ==================== 工厂类型 ====================

DatabaseClientFactory = Callable[..., Any]
AsyncDatabaseClientFactory = Callable[..., Any]

# ==================== 上下文管理器类型 ====================

DatabaseContextManager = Union[
    ContextManager[DatabaseConnection],
    AsyncContextManager[DatabaseConnection]
]

TransactionContextManager = Union[
    ContextManager[DatabaseTransaction],
    AsyncContextManager[DatabaseTransaction]
]

# ==================== 导出类型 ====================

__all__ = [
    # 基础类型
    "DatabaseValue",
    "DatabaseRecord", 
    "DatabaseRecords",
    "QueryParameters",
    "ConnectionString",
    
    # 枚举类型
    "DatabaseType",
    "SortOrder",
    "ComparisonOperator",
    "LogicalOperator", 
    "JoinType",
    "TransactionIsolation",
    "ConnectionPoolStatus",
    
    # 协议类型
    "DatabaseConnection",
    "DatabaseTransaction",
    "DatabasePool",
    
    # 泛型类型
    "QueryResult",
    
    # 回调类型
    "ConnectionCallback",
    "AsyncConnectionCallback",
    "QueryCallback",
    "AsyncQueryCallback",
    "ErrorCallback",
    "AsyncErrorCallback",
    
    # 配置类型
    "PoolConfig",
    "SSLConfig",
    
    # 工厂类型
    "DatabaseClientFactory",
    "AsyncDatabaseClientFactory",
    
    # 上下文管理器类型
    "DatabaseContextManager",
    "TransactionContextManager",
]

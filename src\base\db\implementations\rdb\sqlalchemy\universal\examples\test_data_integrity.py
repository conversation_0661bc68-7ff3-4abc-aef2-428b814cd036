#!/usr/bin/env python3
"""
数据完整性测试脚本

用于验证批量插入的数据完整性和日志功能，特别针对1141条记录丢失1条的问题。
"""

import asyncio
import logging
import time
from typing import List, Dict, Any

# 配置详细的日志输出
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('batch_insert_test.log')
    ]
)

logger = logging.getLogger(__name__)


def create_test_data(count: int) -> List[Dict[str, Any]]:
    """创建测试数据"""
    test_data = []
    for i in range(count):
        test_data.append({
            "id": i + 1,
            "name": f"用户_{i+1}",
            "email": f"user_{i+1}@example.com",
            "phone": f"138{i:08d}",
            "address": f"地址_{i+1}",
            "status": "active",
            "created_at": "2024-01-01 00:00:00"
        })
    return test_data


def test_batch_splitting_logic():
    """测试批次分割逻辑"""
    logger.info("=== 测试批次分割逻辑 ===")
    
    test_cases = [
        {"total": 1141, "batch_size": 100},
        {"total": 1141, "batch_size": 50},
        {"total": 1141, "batch_size": 200},
        {"total": 1000, "batch_size": 100},
        {"total": 999, "batch_size": 100},
        {"total": 1001, "batch_size": 100},
    ]
    
    for case in test_cases:
        total = case["total"]
        batch_size = case["batch_size"]
        
        logger.info(f"测试: {total}条记录, batch_size={batch_size}")
        
        # 模拟批次分割
        batches = []
        total_in_batches = 0
        
        for i in range(0, total, batch_size):
            batch_data_size = min(batch_size, total - i)
            batch_num = i // batch_size + 1
            batches.append((batch_num, batch_data_size, i, i + batch_data_size - 1))
            total_in_batches += batch_data_size
            
            logger.debug(f"  批次 {batch_num}: {batch_data_size}条记录 (索引 {i} 到 {i + batch_data_size - 1})")
        
        expected_batches = (total + batch_size - 1) // batch_size
        actual_batches = len(batches)
        
        logger.info(f"  预期批次数: {expected_batches}, 实际批次数: {actual_batches}")
        logger.info(f"  总记录数: {total}, 批次中总记录数: {total_in_batches}")
        
        if total_in_batches != total:
            logger.error(f"  ❌ 数据完整性检查失败: 预期{total}, 实际{total_in_batches}")
        elif expected_batches != actual_batches:
            logger.error(f"  ❌ 批次数不匹配: 预期{expected_batches}, 实际{actual_batches}")
        else:
            logger.info(f"  ✅ 批次分割正确")


async def test_batch_insert_with_logging():
    """测试带日志的批量插入"""
    logger.info("=== 测试批量插入数据完整性 ===")
    
    # 这里需要实际的数据库连接，暂时模拟
    # 在实际使用时，请替换为真实的配置
    try:
        from base.db.implementations.rdb.sqlalchemy.universal.client import UniversalSQLAlchemyClient
        from base.db.implementations.rdb.sqlalchemy.universal.config import UniversalConnectionConfig
        
        config = UniversalConnectionConfig(
            dialect="mysql",
            host="localhost",
            port=3306,
            database="test_db",
            username="test",
            password="test"
        )
        
        client = UniversalSQLAlchemyClient(config)
        
        # 测试不同的数据量
        test_cases = [
            {"count": 1141, "batch_size": 100, "name": "您的实际场景"},
            {"count": 1000, "batch_size": 100, "name": "标准场景"},
            {"count": 999, "batch_size": 100, "name": "边界场景-1"},
            {"count": 1001, "batch_size": 100, "name": "边界场景+1"},
        ]
        
        for case in test_cases:
            logger.info(f"\n--- {case['name']}: {case['count']}条记录, batch_size={case['batch_size']} ---")
            
            # 创建测试数据
            test_data = create_test_data(case['count'])
            logger.info(f"创建了 {len(test_data)} 条测试数据")
            
            # 同步版本测试
            logger.info("测试同步版本...")
            try:
                client.connect()
                
                start_time = time.time()
                result = client.batch_insert(
                    table="test_users_sync",
                    data=test_data,
                    batch_size=case['batch_size']
                )
                elapsed_time = time.time() - start_time
                
                logger.info(f"同步版本结果:")
                logger.info(f"  成功: {result.success}")
                logger.info(f"  影响行数: {result.affected_rows}")
                logger.info(f"  执行时间: {elapsed_time:.2f}秒")
                logger.info(f"  输入记录数: {result.operation_parameters.get('input_record_count', 'N/A')}")
                logger.info(f"  处理记录数: {result.operation_parameters.get('processed_record_count', 'N/A')}")
                logger.info(f"  数据完整性: {result.operation_parameters.get('data_integrity_check', 'N/A')}")
                logger.info(f"  成功率: {result.operation_parameters.get('success_rate', 'N/A'):.2%}")
                
                if not result.success:
                    logger.error("同步版本失败:")
                    for error in result.operation_parameters.get('errors', []):
                        logger.error(f"  - {error}")
                
                client.disconnect()
                
            except Exception as e:
                logger.error(f"同步版本测试失败: {e}", exc_info=True)
            
            # 异步版本测试
            logger.info("测试异步版本...")
            try:
                await client.aconnect()
                
                start_time = time.time()
                result = await client.abatch_insert(
                    table="test_users_async",
                    data=test_data,
                    batch_size=case['batch_size'],
                    max_concurrency=3
                )
                elapsed_time = time.time() - start_time
                
                logger.info(f"异步版本结果:")
                logger.info(f"  成功: {result.success}")
                logger.info(f"  影响行数: {result.affected_rows}")
                logger.info(f"  执行时间: {elapsed_time:.2f}秒")
                logger.info(f"  输入记录数: {result.operation_parameters.get('input_record_count', 'N/A')}")
                logger.info(f"  预期记录数: {result.operation_parameters.get('total_expected_records', 'N/A')}")
                logger.info(f"  数据完整性: {result.operation_parameters.get('data_integrity_check', 'N/A')}")
                logger.info(f"  成功率: {result.operation_parameters.get('success_rate', 'N/A'):.2%}")
                
                if not result.success:
                    logger.error("异步版本失败:")
                    for error in result.operation_parameters.get('errors', []):
                        logger.error(f"  - {error}")
                    
                    integrity_issues = result.operation_parameters.get('data_integrity_issues', [])
                    if integrity_issues:
                        logger.error("数据完整性问题:")
                        for issue in integrity_issues:
                            logger.error(f"  - {issue}")
                
                await client.adisconnect()
                
            except Exception as e:
                logger.error(f"异步版本测试失败: {e}", exc_info=True)
    
    except ImportError:
        logger.warning("无法导入UniversalSQLAlchemyClient，跳过实际数据库测试")


def test_edge_cases():
    """测试边界情况"""
    logger.info("=== 测试边界情况 ===")
    
    edge_cases = [
        {"name": "正好整除", "total": 1000, "batch_size": 100},
        {"name": "余数为1", "total": 1001, "batch_size": 100},
        {"name": "余数为99", "total": 1099, "batch_size": 100},
        {"name": "小于批次大小", "total": 50, "batch_size": 100},
        {"name": "等于批次大小", "total": 100, "batch_size": 100},
        {"name": "您的场景", "total": 1141, "batch_size": 100},
    ]
    
    for case in edge_cases:
        total = case["total"]
        batch_size = case["batch_size"]
        
        logger.info(f"\n{case['name']}: {total}条记录, batch_size={batch_size}")
        
        # 计算预期值
        expected_batches = (total + batch_size - 1) // batch_size
        
        # 模拟分批
        actual_batches = 0
        total_processed = 0
        
        for i in range(0, total, batch_size):
            batch_size_actual = min(batch_size, total - i)
            actual_batches += 1
            total_processed += batch_size_actual
            
            if actual_batches <= 3 or actual_batches == expected_batches:
                logger.debug(f"  批次 {actual_batches}: {batch_size_actual}条记录")
            elif actual_batches == 4 and expected_batches > 5:
                logger.debug(f"  ... (省略中间批次)")
        
        logger.info(f"  预期批次: {expected_batches}, 实际批次: {actual_batches}")
        logger.info(f"  预期记录: {total}, 处理记录: {total_processed}")
        
        if expected_batches != actual_batches:
            logger.error(f"  ❌ 批次数不匹配")
        elif total != total_processed:
            logger.error(f"  ❌ 记录数不匹配")
        else:
            logger.info(f"  ✅ 边界情况正确")


def analyze_1141_case():
    """专门分析1141条记录的情况"""
    logger.info("=== 专门分析1141条记录场景 ===")
    
    total = 1141
    batch_size = 100
    
    logger.info(f"分析: {total}条记录, batch_size={batch_size}")
    
    # 详细分析每个批次
    batches = []
    for i in range(0, total, batch_size):
        batch_data_size = min(batch_size, total - i)
        batch_num = i // batch_size + 1
        start_idx = i
        end_idx = i + batch_data_size - 1
        
        batches.append({
            "batch_num": batch_num,
            "size": batch_data_size,
            "start_idx": start_idx,
            "end_idx": end_idx,
            "range": f"{start_idx+1}-{end_idx+1}"  # 1-based for user display
        })
        
        logger.info(f"批次 {batch_num}: {batch_data_size}条记录, 索引{start_idx}-{end_idx}, 记录{start_idx+1}-{end_idx+1}")
    
    total_in_batches = sum(batch["size"] for batch in batches)
    
    logger.info(f"\n汇总:")
    logger.info(f"  总批次数: {len(batches)}")
    logger.info(f"  输入记录数: {total}")
    logger.info(f"  批次记录总数: {total_in_batches}")
    logger.info(f"  最后一个批次: {batches[-1]['size']}条记录")
    
    if total_in_batches != total:
        logger.error(f"❌ 发现数据丢失: 丢失了 {total - total_in_batches} 条记录")
        
        # 分析可能的原因
        logger.error("可能的原因:")
        logger.error("1. 批次分割逻辑错误")
        logger.error("2. 数组索引计算错误")
        logger.error("3. 批次处理中的静默失败")
        logger.error("4. 事务回滚未正确报告")
    else:
        logger.info("✅ 批次分割逻辑正确，没有数据丢失")
        
        # 提供调试建议
        logger.info("\n如果仍然出现数据丢失，请检查:")
        logger.info("1. 数据库约束冲突（唯一键、外键等）")
        logger.info("2. 事务超时或连接中断")
        logger.info("3. 批次处理中的异常被静默捕获")
        logger.info("4. SQL执行结果的affected_rows统计")


if __name__ == "__main__":
    logger.info("数据完整性测试开始")
    logger.info("=" * 60)
    
    # 运行所有测试
    test_batch_splitting_logic()
    test_edge_cases()
    analyze_1141_case()
    
    # 运行实际数据库测试（如果可用）
    asyncio.run(test_batch_insert_with_logging())
    
    logger.info("=" * 60)
    logger.info("数据完整性测试完成")
    logger.info("\n检查要点:")
    logger.info("1. 查看日志文件 batch_insert_test.log 获取详细信息")
    logger.info("2. 确认批次分割逻辑没有off-by-one错误")
    logger.info("3. 验证每个批次的affected_rows与预期一致")
    logger.info("4. 检查是否有静默失败的批次")
    logger.info("5. 确认事务提交状态")

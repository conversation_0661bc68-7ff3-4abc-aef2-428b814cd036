import fastapi
from api.dd_sql_recommend.routers import router as dd_sql_recommend_router
from api.dd_submission.routers import duty_distribution_router, data_backfill_router
from utils.common.logger import setup_enterprise_logger

logger = setup_enterprise_logger()

app = fastapi.FastAPI()
app.include_router(dd_sql_recommend_router)
app.include_router(duty_distribution_router)
app.include_router(data_backfill_router)



if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=30338)
# @package usage.column_search
# 列级向量搜索配置

table_name: "md_column_embeddings"

search_schema:
  vector_field: "embedding"
  topk: 20
  metric_type: "cosine"
  output_fields:
    - "id"
    - "knowledge_id"
    - "source_type"
    - "rdb_table_id"
    - "rdb_column_id"
    - "content_type"
    - "create_time"
  expr: ""
  partition_name: ""

query_schema:
  topk: 20
  expr: ""
  partition_name: ""
  output_fields:
    - "id"
    - "knowledge_id"
    - "source_type"
    - "rdb_table_id"
    - "rdb_column_id"
    - "content_type"
    - "create_time"

# 列级搜索特定配置
column_search_config:
  # 支持的内容类型
  supported_content_types:
    - "column_name"
    - "column_name_cn"
    - "column_desc"
  
  # 支持的数据源类型
  supported_source_types:
    - "SOURCE"
    - "INDEX"
  
  # 默认搜索过滤条件
  default_filters:
    # 默认搜索所有内容类型
    content_types: ["column_name", "column_name_cn", "column_desc"]
    # 可以按表ID过滤
    table_scope_enabled: true
  
  # 搜索结果排序
  result_ordering:
    # 主要排序：相似度得分（降序）
    primary: "similarity_score DESC"
    # 次要排序：表ID（升序）
    secondary: "rdb_table_id ASC"
    # 第三排序：创建时间（降序）
    tertiary: "create_time DESC"
  
  # 表级范围搜索优化
  table_scope_optimization:
    # 启用表级范围搜索
    enabled: true
    # 当指定表ID时，优先在该表范围内搜索
    prefer_table_scope: true

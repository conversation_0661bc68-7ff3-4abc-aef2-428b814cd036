from dataclasses import dataclass, field
from typing import Dict, Any, Optional, Union
from urllib.parse import urlparse
import re


@dataclass
class PGVectorConnectionConfig:
    """PGVector连接配置"""
    
    # 基础连接参数
    host: str
    port: int = 5432
    database: str = "postgres"
    username: str = "postgres"
    password: str = ""
    
    # 连接池配置
    min_connections: int = 1
    max_connections: int = 10
    pool_timeout: float = 30.0
    pool_recycle: int = 3600
    pool_pre_ping: bool = True
    
    # 向量配置
    vector_dimension: int = 1536
    distance_metric: str = "cosine"  # cosine, l2, inner_product
    
    # 性能配置
    enable_cache: bool = True
    cache_size: int = 1000
    cache_ttl: int = 3600
    
    # 重试配置
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # 调试配置
    echo: bool = False
    echo_pool: bool = False
    
    # 扩展配置
    extra_options: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def from_url(cls, database_url: str, **kwargs) -> 'PGVectorConnectionConfig':
        """从数据库URL创建配置"""
        parsed = urlparse(database_url)
        
        if parsed.scheme not in ['postgresql', 'postgres']:
            raise ValueError(f"Unsupported scheme: {parsed.scheme}")
        
        config = cls(
            host=parsed.hostname or "localhost",
            port=parsed.port or 5432,
            database=parsed.path.lstrip('/') or "postgres",
            username=parsed.username or "postgres",
            password=parsed.password or "",
            **kwargs
        )
        
        return config
    
    @classmethod
    def from_components(cls, host: str, database: str, username: str, password: str,
                       port: int = 5432, **kwargs) -> 'PGVectorConnectionConfig':
        """从组件创建配置"""
        return cls(
            host=host,
            port=port,
            database=database,
            username=username,
            password=password,
            **kwargs
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'username': self.username,
            'password': self.password,
            'min_connections': self.min_connections,
            'max_connections': self.max_connections,
            'pool_timeout': self.pool_timeout,
            'pool_recycle': self.pool_recycle,
            'pool_pre_ping': self.pool_pre_ping,
            'vector_dimension': self.vector_dimension,
            'distance_metric': self.distance_metric,
            'enable_cache': self.enable_cache,
            'cache_size': self.cache_size,
            'cache_ttl': self.cache_ttl,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay,
            'echo': self.echo,
            'echo_pool': self.echo_pool,
            'extra_options': self.extra_options
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'PGVectorConnectionConfig':
        """从字典创建配置 - 智能过滤冗余参数"""

        # 定义所有支持的参数
        supported_params = {
            # 基础连接参数
            'host', 'port', 'database', 'username', 'password',

            # 连接池配置
            'min_connections', 'max_connections', 'pool_timeout', 'pool_recycle', 'pool_pre_ping',

            # 向量配置
            'vector_dimension', 'distance_metric',

            # 性能配置
            'enable_cache', 'cache_size', 'cache_ttl',

            # 重试配置
            'max_retries', 'retry_delay',

            # 调试配置
            'echo', 'echo_pool',

            # 扩展配置
            'extra_options'
        }

        # 过滤配置，只保留支持的参数
        filtered_config = {k: v for k, v in config_dict.items() if k in supported_params}

        # 记录被过滤的参数（用于调试）
        filtered_out = set(config_dict.keys()) - supported_params
        if filtered_out:
            import logging
            logger = logging.getLogger(__name__)
            logger.debug(f"PGVectorConnectionConfig 过滤了冗余参数: {filtered_out}")

        # 验证必需参数
        required_params = {'host', 'database', 'username'}
        missing_params = required_params - set(filtered_config.keys())
        if missing_params:
            raise ValueError(f"缺少必需的连接参数: {missing_params}")

        # 如果缺少可选字段，使用默认值
        defaults = {
            'port': 5432,
            'password': '',
            'min_connections': 1,
            'max_connections': 10,
            'pool_timeout': 30.0,
            'pool_recycle': 3600,
            'pool_pre_ping': True,
            'vector_dimension': 1536,
            'distance_metric': 'cosine',
            'enable_cache': True,
            'cache_size': 1000,
            'cache_ttl': 3600,
            'max_retries': 3,
            'retry_delay': 1.0,
            'echo': False,
            'echo_pool': False,
            'extra_options': {}
        }

        # 合并默认值（只对缺失的键）
        for key, default_value in defaults.items():
            if key not in filtered_config:
                filtered_config[key] = default_value

        return cls(**filtered_config)
    
    def get_connection_string(self) -> str:
        """获取连接字符串"""
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    def get_sync_connection_params(self) -> Dict[str, Any]:
        """获取同步连接参数"""
        return {
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'user': self.username,
            'password': self.password,
            **self.extra_options
        }
    
    def get_async_connection_params(self) -> Dict[str, Any]:
        """获取异步连接参数 - aiopg格式（已废弃）"""
        return {
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'user': self.username,
            'password': self.password,
            'minsize': self.min_connections,
            'maxsize': self.max_connections,
            **self.extra_options
        }

    def get_asyncpg_connection_params(self) -> Dict[str, Any]:
        """获取asyncpg连接参数"""
        return {
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'user': self.username,
            'password': self.password,
            'min_size': self.min_connections,
            'max_size': self.max_connections,
            'command_timeout': 60,
            'server_settings': {
                'application_name': 'pgvector_client',
                **self.extra_options
            }
        }
    
    def validate(self) -> None:
        """验证配置"""
        if not self.host:
            raise ValueError("Host is required")
        
        if not self.database:
            raise ValueError("Database is required")
        
        if not self.username:
            raise ValueError("Username is required")
        
        if self.port <= 0 or self.port > 65535:
            raise ValueError("Port must be between 1 and 65535")
        
        if self.min_connections < 1:
            raise ValueError("min_connections must be at least 1")
        
        if self.max_connections < self.min_connections:
            raise ValueError("max_connections must be >= min_connections")
        
        if self.vector_dimension <= 0:
            raise ValueError("vector_dimension must be positive")
        
        if self.distance_metric not in ['cosine', 'l2', 'inner_product']:
            raise ValueError("distance_metric must be one of: cosine, l2, inner_product")
        
        if self.cache_size < 0:
            raise ValueError("cache_size must be non-negative")
        
        if self.max_retries < 0:
            raise ValueError("max_retries must be non-negative")


@dataclass
class VectorSearchConfig:
    """向量搜索配置"""
    
    # 搜索参数
    top_k: int = 10
    threshold: float = 0.0
    include_metadata: bool = True
    include_vectors: bool = False
    
    # 过滤参数
    filters: Optional[Dict[str, Any]] = None
    
    # 性能参数
    use_index: bool = True
    parallel_search: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'top_k': self.top_k,
            'threshold': self.threshold,
            'include_metadata': self.include_metadata,
            'include_vectors': self.include_vectors,
            'filters': self.filters,
            'use_index': self.use_index,
            'parallel_search': self.parallel_search
        }


@dataclass
class CollectionConfig:
    """集合配置"""
    
    # 基础配置
    name: str
    dimension: int
    distance_metric: str = "cosine"
    
    # 索引配置
    index_type: str = "ivfflat"  # ivfflat, hnsw
    index_params: Dict[str, Any] = field(default_factory=dict)
    
    # 分区配置
    enable_partitioning: bool = False
    partition_key: Optional[str] = None
    default_partition: str = "default"
    
    # 元数据配置
    metadata_schema: Optional[Dict[str, str]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'dimension': self.dimension,
            'distance_metric': self.distance_metric,
            'index_type': self.index_type,
            'index_params': self.index_params,
            'enable_partitioning': self.enable_partitioning,
            'partition_key': self.partition_key,
            'default_partition': self.default_partition,
            'metadata_schema': self.metadata_schema
        }

#!/usr/bin/env python3
"""
数据回填适配器

提供统一的接口，智能选择使用优化引擎或原始引擎
"""

import os
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

from modules.dd_submission.department_assignment.core.optimized_backfill_engine import (
    OptimizedBackfillEngine, BackfillConfig, BackfillResult
)
from modules.dd_submission.department_assignment.monitoring.performance_monitor import monitor_batch_operation_enhanced


class BackfillAdapter:
    """
    数据回填适配器
    
    智能选择使用优化引擎或原始引擎，确保向后兼容性和最佳性能
    """
    
    def __init__(self, rdb_client, vdb_client=None):
        """
        初始化数据回填适配器
        
        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端（可选）
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client

        # 配置优化引擎
        self.optimization_config = self._load_optimization_config()
        self.optimized_engine = OptimizedBackfillEngine(
            rdb_client, vdb_client, self.optimization_config
        ) if self.optimization_config.enabled else None
        
        # 性能统计
        self.performance_stats = {
            'total_requests': 0,
            'optimized_requests': 0,
            'original_requests': 0,
            'optimization_enabled': self.optimization_config.enabled
        }
    
    def _load_optimization_config(self) -> 'OptimizationConfig':
        """加载优化配置"""
        return OptimizationConfig(
            enabled=os.getenv('BACKFILL_OPTIMIZATION_ENABLED', 'true').lower() == 'true',
            min_data_size_for_optimization=int(os.getenv('BACKFILL_MIN_SIZE_FOR_OPT', '5')),
            max_data_size_for_optimization=int(os.getenv('BACKFILL_MAX_SIZE_FOR_OPT', '10000')),
            batch_size=int(os.getenv('BACKFILL_BATCH_SIZE', '500')),
            max_concurrency=int(os.getenv('BACKFILL_MAX_CONCURRENCY', '3')),
            timeout_per_batch=float(os.getenv('BACKFILL_TIMEOUT_PER_BATCH', '120.0')),
            enable_fallback=os.getenv('BACKFILL_ENABLE_FALLBACK', 'true').lower() == 'true'
        )
    
    @monitor_batch_operation_enhanced("adaptive_data_backfill")
    async def process_data_backfill(
        self,
        report_code: str,
        step: str,
        data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        自适应数据回填处理
        
        Args:
            report_code: 报表代码
            step: 处理步骤
            data: 待处理数据列表
            
        Returns:
            Dict[str, Any]: 处理结果（兼容原始接口格式）
        """
        self.performance_stats['total_requests'] += 1
        
        try:
            # 决策：使用优化引擎还是原始引擎
            use_optimization = self._should_use_optimization(data)
            
            if use_optimization and self.optimized_engine:
                logger.info(f"使用优化引擎处理数据回填: {len(data)}条数据")
                result = await self._process_with_optimized_engine(report_code, step, data)
                self.performance_stats['optimized_requests'] += 1
            else:
                logger.info(f"使用原始引擎处理数据回填: {len(data)}条数据")
                raise NotImplementedError('原始引擎已废弃')
            
            return result
            
        except Exception as e:
            logger.error(f"数据回填处理失败: {e}")
            
            # 返回失败结果
            return {
                "success": False,
                "message": f"数据回填处理失败: {str(e)}",
                "statistics": {
                    "total_count": len(data),
                    "updated_count": 0,
                    "error_count": len(data)
                }
            }
    
    def _should_use_optimization(self, data: List[Dict[str, Any]]) -> bool:
        """判断是否应该使用优化引擎"""
        if not self.optimization_config.enabled:
            return False
        
        if not self.optimized_engine:
            return False
        
        data_size = len(data)
        
        # 数据量太小，优化效果不明显
        if data_size < self.optimization_config.min_data_size_for_optimization:
            return False
        
        # 数据量太大，可能超出处理能力
        if data_size > self.optimization_config.max_data_size_for_optimization:
            return False
        
        # 检查数据质量
        if not self._validate_data_for_optimization(data):
            return False
        
        return True
    
    def _validate_data_for_optimization(self, data: List[Dict[str, Any]]) -> bool:
        """验证数据是否适合优化处理"""
        if not data:
            return False
        
        # 检查必需字段
        required_fields = ['entry_id']
        for item in data[:5]:  # 检查前5条数据
            for field in required_fields:
                if field not in item or not item[field]:
                    return False
        
        return True
    
    async def _process_with_optimized_engine(
        self,
        report_code: str,
        step: str,
        data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """使用优化引擎处理"""
        result = await self.optimized_engine.process_data_backfill(report_code, step, data)
        
        # 转换为兼容的返回格式
        return {
            "success": result.success,
            "message": f"优化引擎处理完成，成功更新{result.updated_count}条记录" if result.success else result.error_message,
            "statistics": {
                "total_count": result.total_count,
                "updated_count": result.updated_count,
                "error_count": result.error_count,
                "processing_time_ms": result.processing_time_ms,
                "engine_type": "optimized"
            },
            "processing_details": result.details
        }
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = self.performance_stats.copy()
        
        if stats['total_requests'] > 0:
            stats['optimization_usage_rate'] = stats['optimized_requests'] / stats['total_requests']
        else:
            stats['optimization_usage_rate'] = 0.0
        
        # 获取优化引擎的详细统计
        if self.optimized_engine:
            optimized_stats = self.optimized_engine.get_performance_stats()
            stats['optimized_engine_stats'] = optimized_stats
        
        return stats
    
    def enable_optimization(self):
        """启用优化"""
        self.optimization_config.enabled = True
        if not self.optimized_engine:
            self.optimized_engine = OptimizedBackfillEngine(
                self.rdb_client, self.vdb_client, 
                BackfillConfig(
                    batch_size=self.optimization_config.batch_size,
                    max_concurrency=self.optimization_config.max_concurrency,
                    timeout_per_batch=self.optimization_config.timeout_per_batch,
                    fallback_enabled=self.optimization_config.enable_fallback
                )
            )
        logger.info("数据回填优化已启用")
    
    def disable_optimization(self):
        """禁用优化"""
        self.optimization_config.enabled = False
        logger.info("数据回填优化已禁用")
    
    def update_optimization_config(self, **kwargs):
        """更新优化配置"""
        for key, value in kwargs.items():
            if hasattr(self.optimization_config, key):
                setattr(self.optimization_config, key, value)
                logger.info(f"更新优化配置: {key} = {value}")
        
        # 重新创建优化引擎
        if self.optimization_config.enabled:
            self.optimized_engine = OptimizedBackfillEngine(
                self.rdb_client, self.vdb_client,
                BackfillConfig(
                    batch_size=self.optimization_config.batch_size,
                    max_concurrency=self.optimization_config.max_concurrency,
                    timeout_per_batch=self.optimization_config.timeout_per_batch,
                    fallback_enabled=self.optimization_config.enable_fallback
                )
            )


class OptimizationConfig:
    """优化配置类"""
    
    def __init__(
        self,
        enabled: bool = True,
        min_data_size_for_optimization: int = 5,
        max_data_size_for_optimization: int = 10000,
        batch_size: int = 500,
        max_concurrency: int = 3,
        timeout_per_batch: float = 120.0,
        enable_fallback: bool = True
    ):
        self.enabled = enabled
        self.min_data_size_for_optimization = min_data_size_for_optimization
        self.max_data_size_for_optimization = max_data_size_for_optimization
        self.batch_size = batch_size
        self.max_concurrency = max_concurrency
        self.timeout_per_batch = timeout_per_batch
        self.enable_fallback = enable_fallback


# 全局适配器实例（可选）
_global_adapter = None

def get_backfill_adapter(rdb_client, vdb_client=None) -> BackfillAdapter:
    """获取数据回填适配器实例"""
    global _global_adapter
    
    if _global_adapter is None:
        _global_adapter = BackfillAdapter(rdb_client, vdb_client)
    
    return _global_adapter


# 向后兼容的函数接口
async def process_data_backfill_adaptive(
    rdb_client,
    vdb_client,
    report_code: str,
    step: str,
    data: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    自适应数据回填处理函数（向后兼容接口）
    
    Args:
        rdb_client: 关系数据库客户端
        vdb_client: 向量数据库客户端
        report_code: 报表代码
        step: 处理步骤
        data: 待处理数据列表
        
    Returns:
        Dict[str, Any]: 处理结果
    """
    adapter = get_backfill_adapter(rdb_client, vdb_client)
    return await adapter.process_data_backfill(report_code, step, data)

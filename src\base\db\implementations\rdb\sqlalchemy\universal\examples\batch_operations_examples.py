"""
批量操作统一接口使用示例

本文件展示了修改后的批量删除和批量更新方法的使用方式，
包括向后兼容性和新功能的使用示例。
"""

import asyncio
import logging
from typing import List, Dict, Any

from base.db.base.rdb import (
    QueryFilterGroup, QueryFilter, ComparisonOperator, LogicalOperator
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BatchOperationsExamples:
    """批量操作示例类"""
    
    def __init__(self, client):
        self.client = client

    # ==================== 批量删除示例 ====================
    
    async def example_batch_delete_simple_ids(self):
        """示例1：简单ID删除（最常用）"""
        logger.info("=== 示例1：简单ID删除 ===")
        
        # 通过ID列表删除
        conditions = [
            {"id": 1},
            {"id": 2}, 
            {"id": 3}
        ]
        
        result = await self.client.abatch_delete(
            table="users",
            conditions=conditions,
            batch_size=1000
        )
        
        logger.info(f"删除结果: 成功={result.success}, 影响行数={result.affected_rows}")
        return result

    async def example_batch_delete_simple_conditions(self):
        """示例2：简单条件删除"""
        logger.info("=== 示例2：简单条件删除 ===")
        
        conditions = [
            {"status": "inactive"},
            {"age": 18},
            {"department": "HR", "active": False}  # 多个等值条件
        ]
        
        result = await self.client.abatch_delete(
            table="users",
            conditions=conditions
        )
        
        logger.info(f"删除结果: 成功={result.success}, 影响行数={result.affected_rows}")
        return result

    async def example_batch_delete_complex_conditions(self):
        """示例3：复杂条件删除（使用MongoDB风格操作符）"""
        logger.info("=== 示例3：复杂条件删除 ===")
        
        conditions = [
            # 年龄小于18的用户
            {"age": {"$lt": 18}},
            
            # 最后登录时间早于2023年的用户
            {"last_login": {"$lt": "2023-01-01"}},
            
            # 状态为inactive且部门为HR的用户
            {"status": "inactive", "department": "HR"},
            
            # 年龄在18-65之间的用户
            {"age": {"$gte": 18, "$lte": 65}},
            
            # 分类在指定列表中的记录
            {"category": {"$in": ["A", "B", "C"]}},
            
            # 名称包含test的记录
            {"name": {"$like": "%test%"}},
            
            # 删除时间为空的记录
            {"deleted_at": {"$null": True}}
        ]
        
        result = await self.client.abatch_delete(
            table="users",
            conditions=conditions,
            batch_size=500,
            max_concurrency=3
        )
        
        logger.info(f"删除结果: 成功={result.success}, 影响行数={result.affected_rows}")
        return result

    async def example_batch_delete_query_filter_group(self):
        """示例4：使用QueryFilterGroup的复杂条件删除"""
        logger.info("=== 示例4：QueryFilterGroup复杂条件删除 ===")
        
        # 构建复杂的OR条件：删除状态为inactive或年龄小于18的用户
        complex_condition = QueryFilterGroup(
            operator=LogicalOperator.OR,
            filters=[
                QueryFilter("status", ComparisonOperator.EQ, "inactive"),
                QueryFilter("age", ComparisonOperator.LT, 18),
                QueryFilterGroup(
                    operator=LogicalOperator.AND,
                    filters=[
                        QueryFilter("department", ComparisonOperator.EQ, "HR"),
                        QueryFilter("last_login", ComparisonOperator.LT, "2023-01-01")
                    ]
                )
            ]
        )
        
        conditions = [complex_condition]
        
        result = await self.client.abatch_delete(
            table="users",
            conditions=conditions
        )
        
        logger.info(f"删除结果: 成功={result.success}, 影响行数={result.affected_rows}")
        return result

    # ==================== 批量更新示例 ====================
    
    async def example_batch_update_traditional(self):
        """示例5：传统格式批量更新（向后兼容）"""
        logger.info("=== 示例5：传统格式批量更新 ===")
        
        updates = [
            {"data": {"status": "active", "updated_at": "2024-01-01"}, "filters": {"id": 1}},
            {"data": {"status": "inactive", "updated_at": "2024-01-01"}, "filters": {"id": 2}},
            {"data": {"department": "IT"}, "filters": {"name": "张三"}}
        ]
        
        result = await self.client.abatch_update(
            table="users",
            updates=updates,
            batch_size=100
        )
        
        logger.info(f"更新结果: 成功={result.success}, 影响行数={result.affected_rows}")
        return result

    async def example_batch_update_new_format(self):
        """示例6：新格式批量更新（使用where）"""
        logger.info("=== 示例6：新格式批量更新 ===")
        
        updates = [
            {
                "data": {"status": "inactive", "updated_at": "2024-01-01"},
                "where": {"age": {"$lt": 18}}
            },
            {
                "data": {"status": "archived", "archive_date": "2024-01-01"},
                "where": {"last_login": {"$lt": "2023-01-01"}, "status": "inactive"}
            },
            {
                "data": {"department": "ARCHIVED"},
                "where": {"department": {"$in": ["HR", "FINANCE"]}, "active": False}
            }
        ]
        
        result = await self.client.abatch_update(
            table="users",
            updates=updates,
            batch_size=50,
            max_concurrency=2
        )
        
        logger.info(f"更新结果: 成功={result.success}, 影响行数={result.affected_rows}")
        return result

    async def example_batch_update_complex_conditions(self):
        """示例7：复杂条件批量更新"""
        logger.info("=== 示例7：复杂条件批量更新 ===")
        
        # 使用QueryFilterGroup的复杂更新
        complex_filter = QueryFilterGroup(
            operator=LogicalOperator.AND,
            filters=[
                QueryFilter("status", ComparisonOperator.EQ, "active"),
                QueryFilterGroup(
                    operator=LogicalOperator.OR,
                    filters=[
                        QueryFilter("age", ComparisonOperator.GT, 65),
                        QueryFilter("last_login", ComparisonOperator.LT, "2022-01-01")
                    ]
                )
            ]
        )
        
        updates = [
            {
                "data": {"status": "retired", "retirement_date": "2024-01-01"},
                "where": complex_filter
            },
            {
                "data": {"priority": "high"},
                "where": {"role": {"$in": ["admin", "manager"]}, "active": True}
            }
        ]
        
        result = await self.client.abatch_update(
            table="users",
            updates=updates
        )
        
        logger.info(f"更新结果: 成功={result.success}, 影响行数={result.affected_rows}")
        return result

    # ==================== 复杂度边界示例 ====================
    
    def example_complexity_boundaries(self):
        """示例8：复杂度边界说明"""
        logger.info("=== 示例8：复杂度边界说明 ===")
        
        # ✅ 适合批量方法的场景
        suitable_examples = {
            "单表等值查询": {"status": "active"},
            "范围查询": {"age": {"$between": [18, 65]}},
            "IN查询": {"category": {"$in": ["A", "B", "C"]}},
            "逻辑组合": {"$or": [{"status": "active"}, {"priority": "high"}]},
            "模糊匹配": {"name": {"$like": "%张%"}},
            "空值检查": {"deleted_at": {"$null": True}}
        }
        
        # ❌ 不适合批量方法的场景（应使用原生SQL）
        unsuitable_examples = {
            "JOIN查询": "SELECT u.*, p.name FROM users u JOIN profiles p ON u.id = p.user_id",
            "子查询": "DELETE FROM users WHERE id IN (SELECT user_id FROM inactive_users)",
            "函数调用": "UPDATE users SET last_login = NOW() WHERE active = 1",
            "大批量操作": "处理超过10万条记录的操作",
            "复杂业务逻辑": "涉及多表关联和复杂计算的操作"
        }
        
        logger.info("✅ 适合批量方法的场景:")
        for scenario, example in suitable_examples.items():
            logger.info(f"  - {scenario}: {example}")
        
        logger.info("❌ 不适合批量方法的场景（建议使用原生SQL）:")
        for scenario, example in unsuitable_examples.items():
            logger.info(f"  - {scenario}: {example}")

    # ==================== 完整示例运行 ====================
    
    async def run_all_examples(self):
        """运行所有示例"""
        logger.info("开始运行所有批量操作示例...")
        
        try:
            # 批量删除示例
            await self.example_batch_delete_simple_ids()
            await self.example_batch_delete_simple_conditions()
            await self.example_batch_delete_complex_conditions()
            await self.example_batch_delete_query_filter_group()
            
            # 批量更新示例
            await self.example_batch_update_traditional()
            await self.example_batch_update_new_format()
            await self.example_batch_update_complex_conditions()
            
            # 复杂度边界说明
            self.example_complexity_boundaries()
            
            logger.info("所有示例运行完成！")
            
        except Exception as e:
            logger.error(f"示例运行失败: {str(e)}")
            raise


# ==================== 使用说明 ====================

async def main():
    """主函数示例"""
    # 假设已经有了数据库客户端实例
    # client = UniversalSQLAlchemyClient(connection_config)
    # await client.connect()
    
    # examples = BatchOperationsExamples(client)
    # await examples.run_all_examples()
    
    pass


if __name__ == "__main__":
    asyncio.run(main())

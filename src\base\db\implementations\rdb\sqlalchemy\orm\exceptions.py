"""
ORM客户端异常定义

基于universal异常，添加ORM特定的异常类型
"""

from typing import Optional, Any, Dict

# 复用universal的异常基础
from ..universal.exceptions import (
    UniversalSQLAlchemyError,
    ConnectionError,
    ConfigurationError,
    QueryError,
    TransactionError,
    wrap_database_error
)


class ORMSQLAlchemyError(UniversalSQLAlchemyError):
    """ORM SQLAlchemy客户端基础异常"""
    pass


class ModelError(ORMSQLAlchemyError):
    """ORM模型相关错误"""
    pass


class ModelGenerationError(ModelError):
    """动态模型生成错误"""
    pass


class ModelCacheError(ModelError):
    """模型缓存错误"""
    pass


class SessionError(ORMSQLAlchemyError):
    """会话管理错误"""
    pass


class SessionContextError(SessionError):
    """会话上下文错误"""
    pass


class QueryBuilderError(ORMSQLAlchemyError):
    """ORM查询构建器错误"""
    pass


class RelationshipError(ORMSQLAlchemyError):
    """关系映射错误"""
    pass


class BulkOperationError(ORMSQLAlchemyError):
    """批量操作错误"""
    pass


def wrap_orm_error(
    error: Exception, 
    operation: str, 
    context: Optional[Dict[str, Any]] = None
) -> ORMSQLAlchemyError:
    """
    包装ORM操作异常
    
    Args:
        error: 原始异常
        operation: 操作描述
        context: 额外上下文信息
    
    Returns:
        包装后的ORM异常
    """
    if isinstance(error, ORMSQLAlchemyError):
        return error
    
    # 根据异常类型选择合适的包装类
    error_type = type(error).__name__
    
    if 'model' in operation.lower() or 'Model' in error_type:
        return ModelError(f"Model operation failed: {operation}", error, context)
    elif 'session' in operation.lower() or 'Session' in error_type:
        return SessionError(f"Session operation failed: {operation}", error, context)
    elif 'query' in operation.lower() or 'Query' in error_type:
        return QueryBuilderError(f"Query operation failed: {operation}", error, context)
    elif 'bulk' in operation.lower():
        return BulkOperationError(f"Bulk operation failed: {operation}", error, context)
    else:
        return ORMSQLAlchemyError(f"ORM operation failed: {operation}", error, context)


def wrap_model_generation_error(
    error: Exception,
    table_name: str,
    context: Optional[Dict[str, Any]] = None
) -> ModelGenerationError:
    """
    包装模型生成异常
    
    Args:
        error: 原始异常
        table_name: 表名
        context: 额外上下文信息
    
    Returns:
        模型生成异常
    """
    context = context or {}
    context['table_name'] = table_name
    
    return ModelGenerationError(
        f"Failed to generate model for table '{table_name}': {error}",
        error,
        context
    )


def wrap_session_error(
    error: Exception,
    operation: str,
    session_id: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
) -> SessionError:
    """
    包装会话异常
    
    Args:
        error: 原始异常
        operation: 操作描述
        session_id: 会话ID
        context: 额外上下文信息
    
    Returns:
        会话异常
    """
    context = context or {}
    if session_id:
        context['session_id'] = session_id
    
    return SessionError(
        f"Session {operation} failed: {error}",
        error,
        context
    )


# 重新导出universal异常，保持接口兼容性
__all__ = [
    # ORM特定异常
    'ORMSQLAlchemyError',
    'ModelError',
    'ModelGenerationError',
    'ModelCacheError',
    'SessionError',
    'SessionContextError',
    'QueryBuilderError',
    'RelationshipError',
    'BulkOperationError',
    
    # 异常包装函数
    'wrap_orm_error',
    'wrap_model_generation_error',
    'wrap_session_error',
    
    # 复用的universal异常
    'UniversalSQLAlchemyError',
    'ConnectionError',
    'ConfigurationError',
    'QueryError',
    'TransactionError',
    'wrap_database_error',
]

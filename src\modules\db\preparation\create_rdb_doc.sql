-- ==========================================
-- 文档领域（Doc） - 数据库表结构
-- ==========================================
-- 
-- 说明：
-- 1. 本脚本专门用于创建文档（Doc）相关的数据库表。
-- 2. 包含知识库主表和所有以 `doc_` 为前缀的表。
-- 
-- 创建时间：2025-07-04
-- ==========================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ==========================================
-- 1. 全局知识库表 (Global Knowledge Base)
-- ==========================================
DROP TABLE IF EXISTS `kb_knowledge`;
CREATE TABLE `kb_knowledge` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `knowledge_name` varchar(255) NOT NULL COMMENT '知识库名称',
  `doc_nums` int NOT NULL DEFAULT '0' COMMENT '知识库中的文档数量',
  `knowledge_type` varchar(255) NOT NULL COMMENT '知识库类别：DD/Doc/MetaData',
  `knowledge_desc` varchar(255) NOT NULL COMMENT '知识库的描述信息',
  `models` json NOT NULL COMMENT '绑定的模型配置(JSON格式): {"embedding": "model_name", "llm": "model_name", ...}',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '知识库创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '知识库最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_id` (`knowledge_id`),
  KEY `idx_knowledge_name` (`knowledge_name`),
  KEY `idx_knowledge_type` (`knowledge_type`),
  KEY `idx_models` ((CAST(`models`->>'$.embedding' AS CHAR(255)))),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='全局知识库表';

-- ==========================================
-- 知识库文档领域表 (Knowledge Base Domain Tables)
-- 表名前缀: doc_ (Document)
-- ==========================================

-- ==========================================
-- 2. 文档表 (Documents)
-- ==========================================
DROP TABLE IF EXISTS `doc_documents`;
CREATE TABLE `doc_documents` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `doc_id` varchar(64) NOT NULL COMMENT '文档ID，UUID格式',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `doc_name` varchar(200) NOT NULL COMMENT '文档名称',
  `doc_type` varchar(50) DEFAULT NULL COMMENT '文档类型：outside-外部，inside-内部',
  `author` varchar(100) DEFAULT NULL COMMENT '文档作者',
  `vector_similarity_weight` float DEFAULT NULL COMMENT '向量相似度权重',
  `similarity_threshold` float DEFAULT NULL COMMENT '相似度阈值',
  `chunk_nums` int DEFAULT '0' COMMENT '分块总数',
  `parse_type` varchar(50) NOT NULL COMMENT '解析方式',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '解析状态：pending-待处理，processing-处理中，completed-已完成，failed-失败',
  `location` varchar(500) NOT NULL COMMENT '文件存储位置',
  `doc_format` varchar(20) NOT NULL COMMENT '文件格式：pdf,docx,txt等',
  `metadata` text DEFAULT NULL COMMENT '文档源数据',
  `percentage` float DEFAULT '0' COMMENT '分块进度',
  `parse_end_time` datetime DEFAULT NULL COMMENT '解析结束时间',
  `parse_message` text DEFAULT NULL COMMENT '解析状态描述',
  `task_id` varchar(100) DEFAULT NULL COMMENT '任务队列ID',
  `doc_ocr_result_path` varchar(500) DEFAULT NULL COMMENT 'OCR识别结果路径',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否生效：1-生效，0-失效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_doc_id` (`doc_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_doc_name` (`doc_name`),
  KEY `idx_status` (`status`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_doc_documents_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档表';

-- ==========================================
-- 3. 分块表 (Chunks)
-- ==========================================
DROP TABLE IF EXISTS `doc_chunks`;
CREATE TABLE `doc_chunks` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `chunk_id` varchar(64) NOT NULL COMMENT '分块ID',
  `doc_id` varchar(64) NOT NULL COMMENT '文档ID',
  `chapter_layer` varchar(100) DEFAULT NULL COMMENT '文档章节层级信息',
  `parent_id` varchar(64) DEFAULT NULL COMMENT '父分块ID，用于树形结构',
  `sub_chunk_ids` json DEFAULT NULL COMMENT '子分块ID列表，JSON数组',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否生效：1-生效，0-失效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_chunk_id` (`chunk_id`),
  KEY `idx_doc_id` (`doc_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_doc_chunks_document` FOREIGN KEY (`doc_id`) REFERENCES `doc_documents` (`doc_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_doc_chunks_parent` FOREIGN KEY (`parent_id`) REFERENCES `doc_chunks` (`chunk_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分块表';

-- ==========================================
-- 4. 分块信息表 (Chunks Info)
-- ==========================================
DROP TABLE IF EXISTS `doc_chunks_info`;
CREATE TABLE `doc_chunks_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `chunk_info_id` varchar(64) NOT NULL COMMENT '分块信息ID，UUID格式',
  `chunk_id` varchar(64) NOT NULL COMMENT '关联的分块ID',
  `info_type` varchar(50) NOT NULL COMMENT '信息类型：content-内容，keyword-关键词等',
  `info_value` text NOT NULL COMMENT '信息值',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否生效：1-生效，0-失效',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_chunk_info_id` (`chunk_info_id`),
  KEY `idx_chunk_id` (`chunk_id`),
  KEY `idx_info_type` (`info_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_time` (`created_time`),
  CONSTRAINT `fk_doc_chunks_info_chunk` FOREIGN KEY (`chunk_id`) REFERENCES `doc_chunks` (`chunk_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分块信息表';

-- ==========================================
-- 5. 文档别名映射表 (Document Alias)
-- ==========================================
DROP TABLE IF EXISTS `doc_document_alias`;
CREATE TABLE `doc_document_alias` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `doc_id` varchar(64) NOT NULL COMMENT '文档ID',
  `doc_name` varchar(200) NOT NULL COMMENT '原始文件名',
  `cleaned_name` varchar(200) NOT NULL COMMENT '清理后的文档名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_doc_id` (`doc_id`),
  KEY `idx_doc_name` (`doc_name`),
  KEY `idx_cleaned_name` (`cleaned_name`),
  CONSTRAINT `fk_doc_document_alias_doc` FOREIGN KEY (`doc_id`) REFERENCES `doc_documents` (`doc_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档别名映射表';

-- ==========================================
-- 6. 文档搜索历史表 (Document Search History)
-- 描述: 记录知识库的搜索测试历史，简化版本，只保留界面必需字段
-- ==========================================
DROP TABLE IF EXISTS `doc_search_history`;
CREATE TABLE `doc_search_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '搜索历史ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `search_query` text NOT NULL COMMENT '搜索查询内容',
  `search_method` varchar(50) NOT NULL COMMENT '搜索方式：semantic-语义检索，fulltext-全文检索，hybrid-混合检索',
  `citation_limit` int DEFAULT '5000' COMMENT '引用上限',
  `min_similarity` float DEFAULT '0.0' COMMENT '最低相关度',
  `enable_rerank` tinyint(1) DEFAULT '1' COMMENT '是否启用结果重排：1-启用，0-禁用',
  `enable_optimization` tinyint(1) DEFAULT '0' COMMENT '是否启用问题优化：1-启用，0-禁用',
  `search_time_ms` int DEFAULT NULL COMMENT '搜索耗时（毫秒）',
  `total_results` int DEFAULT '0' COMMENT '返回结果总数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '搜索时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_search_method` (`search_method`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_doc_search_history_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档搜索历史表（简化版）';

-- ==========================================
-- 7. 搜索结果详情表 (Search Result Details)
-- 描述: 存储每次搜索的详细结果信息，支持分片排名展示
-- ==========================================
DROP TABLE IF EXISTS `doc_search_results`;
CREATE TABLE `doc_search_results` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `search_history_id` bigint NOT NULL COMMENT '搜索历史ID',
  `chunk_id` varchar(64) NOT NULL COMMENT '分块ID',
  `doc_id` varchar(64) NOT NULL COMMENT '文档ID',
  `doc_name` varchar(200) NOT NULL COMMENT '文档名称',
  `page_number` int DEFAULT NULL COMMENT '页码',
  `content_snippet` text DEFAULT NULL COMMENT '内容片段',
  `overall_rank` int NOT NULL COMMENT '综合排名（#1, #2, #3...）',
  `semantic_score` float DEFAULT NULL COMMENT '语义检索分数',
  `fulltext_score` float DEFAULT NULL COMMENT '全文检索分数',
  `rerank_score` float DEFAULT NULL COMMENT '重排分数',
  `final_score` float NOT NULL COMMENT '最终分数（用于排序）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_search_history_id` (`search_history_id`),
  KEY `idx_chunk_id` (`chunk_id`),
  KEY `idx_doc_id` (`doc_id`),
  KEY `idx_overall_rank` (`overall_rank`),
  CONSTRAINT `fk_doc_search_results_history` FOREIGN KEY (`search_history_id`) REFERENCES `doc_search_history` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_doc_search_results_chunk` FOREIGN KEY (`chunk_id`) REFERENCES `doc_chunks` (`chunk_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_doc_search_results_doc` FOREIGN KEY (`doc_id`) REFERENCES `doc_documents` (`doc_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索结果详情表';

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1; 
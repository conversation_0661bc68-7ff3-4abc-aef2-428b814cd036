"""
按依赖顺序测试：源数据库 → 源表 → 源字段

严格按照 create_rdb_metadata.sql 中的实际表结构进行测试
"""

import asyncio
import logging
import time
from typing import Dict, Any, List

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局测试数据存储
test_data_store = {
    'knowledge_id': None,
    'source_db_id': None,
    'source_table_id': None,
    'source_column_id': None
}


async def setup_test_environment():
    """设置测试环境，创建测试知识库"""
    print("🔧 设置测试环境")
    print("-" * 40)

    try:
        # 获取客户端
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
        from service import get_client
        from modules.knowledge.knowledge.crud import KnowledgeCrud

        rdb_client = await get_client("database.rdbs.mysql")
        knowledge_crud = KnowledgeCrud(rdb_client)

        # 创建测试知识库
        timestamp = int(time.time())
        test_kb_data = {
            'knowledge_name': f'源数据库表字段测试知识库_{timestamp}',
            'knowledge_type': 'MetaData',
            'knowledge_desc': '源数据库、源表、源字段CRUD测试知识库',
            'models': {
                'embedding': 'moka-m3e-base'
            }
        }

        knowledge_id = await knowledge_crud.create_knowledge_base(test_kb_data)
        if not knowledge_id:
            raise Exception("创建测试知识库失败")

        test_data_store['knowledge_id'] = knowledge_id
        print(f"   ✅ 创建测试知识库: {knowledge_id}")

        return rdb_client, knowledge_id

    except Exception as e:
        logger.error(f"设置测试环境失败: {e}")
        raise


async def cleanup_test_environment():
    """清理测试环境"""
    print("\n🧹 清理测试环境")
    print("-" * 40)

    try:
        if test_data_store['knowledge_id']:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            from modules.knowledge.knowledge.crud import KnowledgeCrud

            rdb_client = await get_client("database.rdbs.mysql")
            knowledge_crud = KnowledgeCrud(rdb_client)

            # 删除测试知识库（会级联删除所有相关数据）
            await knowledge_crud.delete_knowledge_base(test_data_store['knowledge_id'])
            print(f"   ✅ 删除测试知识库: {test_data_store['knowledge_id']}")

    except Exception as e:
        logger.error(f"清理测试环境失败: {e}")


async def test_source_database_crud(rdb_client, knowledge_id: str):
    """测试源数据库的完整CRUD操作"""
    print("\n1️⃣ 测试源数据库CRUD操作:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的

        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)

        # 创建源数据库
        timestamp = int(time.time())
        test_db_data = {
            'knowledge_id': knowledge_id,
            'db_name': f'test_source_db_{timestamp}',
            'db_name_cn': f'测试源数据库_{timestamp}',
            'data_layer': 'ods',
            'db_desc': '测试源数据库描述',
            'is_active': True
        }

        db_id, vector_results = await meta_crud.create_source_database(test_db_data)
        if not db_id or db_id <= 0:
            raise Exception("创建源数据库失败：返回的ID无效")
        
        test_data_store['source_db_id'] = db_id
        print(f"   ✅ 创建源数据库: {db_id}")

        return True

    except Exception as e:
        print(f"   ❌ 源数据库CRUD测试失败: {e}")
        return False


async def test_source_table_crud(rdb_client, knowledge_id: str, db_id: int):
    """测试源表的完整CRUD操作"""
    print("\n2️⃣ 测试源表CRUD操作:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的

        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)

        # 创建源表
        timestamp = int(time.time())
        test_table_data = {
            'knowledge_id': knowledge_id,
            'db_id': db_id,
            'table_name': f'test_source_table_{timestamp}',
            'table_name_cn': f'测试源表_{timestamp}',
            'table_desc': '测试源表描述',
            'is_active': True
        }

        table_id, vector_results = await meta_crud.create_source_table(test_table_data)
        if not table_id or table_id <= 0:
            raise Exception("创建源表失败：返回的ID无效")
        
        test_data_store['source_table_id'] = table_id
        print(f"   ✅ 创建源表: {table_id}")

        return True

    except Exception as e:
        print(f"   ❌ 源表CRUD测试失败: {e}")
        return False


async def test_source_column_crud(rdb_client, knowledge_id: str, table_id: int):
    """测试源字段的完整CRUD操作"""
    print("\n3️⃣ 测试源字段CRUD操作:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的

        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)

        # 1. 创建源字段（严格按照实际表结构）
        timestamp = int(time.time())
        test_column_data = {
            'knowledge_id': knowledge_id,
            'table_id': table_id,  # 外键关联到源表
            'column_name': f'test_column_{timestamp}',
            'column_name_cn': f'测试字段_{timestamp}',
            'column_desc': '测试源字段描述',
            'data_type': 'STRING',
            'data_example': '示例数据',
            'is_primary_key': False,
            'is_sensitive': False
        }

        column_id, vector_results = await meta_crud.create_source_column(test_column_data)
        if not column_id or column_id <= 0:
            raise Exception("创建源字段失败：返回的ID无效")
        
        test_data_store['source_column_id'] = column_id
        print(f"   ✅ 创建源字段: {column_id}")
        print(f"   📊 向量化结果: {len(vector_results)} 个向量")

        # 2. 获取源字段（主键查询）
        column_info = await meta_crud.get_source_column(column_id)
        if not column_info or not column_info.get('column_name'):
            raise Exception("主键查询源字段失败：未返回有效数据")
        print(f"   ✅ 主键获取源字段: {column_info['column_name']}")

        # 3. 获取源字段（条件查询）
        column_by_name = await meta_crud.get_source_column(
            knowledge_id=knowledge_id,
            table_id=table_id,
            column_name=test_column_data['column_name']
        )
        if not column_by_name or not column_by_name.get('column_id'):
            raise Exception("条件查询源字段失败：未返回有效数据")
        print(f"   ✅ 条件查询源字段: {column_by_name['column_id']}")

        # 4. 更新源字段
        update_success = await meta_crud.update_source_column(
            {'column_desc': '更新后的字段描述'},
            column_id=column_id
        )
        if not update_success:
            raise Exception("更新源字段失败：返回False")
        print(f"   ✅ 更新源字段: {update_success}")

        # 5. 验证更新
        updated_column = await meta_crud.get_source_column(column_id)
        if not updated_column or '更新后的字段描述' not in updated_column.get('column_desc', ''):
            raise Exception("验证更新失败：描述未正确更新")
        print(f"   ✅ 验证更新: {updated_column['column_desc']}")

        # 6. 列出源字段
        column_list = await meta_crud.list_source_columns(knowledge_id=knowledge_id, table_id=table_id)
        if not column_list or len(column_list) == 0:
            raise Exception("列出源字段失败：未返回数据")
        print(f"   ✅ 列出源字段: {len(column_list)} 个")

        return True

    except Exception as e:
        print(f"   ❌ 源字段CRUD测试失败: {e}")
        return False


async def test_cascade_delete(rdb_client, knowledge_id: str, db_id: int, table_id: int, column_id: int):
    """测试级联删除功能"""
    print("\n4️⃣ 测试级联删除功能:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_meta import MetadataCrudMeta

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的

        meta_crud = MetadataCrudMeta(rdb_client, vdb_client, embedding_client)

        # 1. 验证数据存在
        db_exists = await meta_crud.get_source_database(db_id)
        table_exists = await meta_crud.get_source_table(table_id)
        column_exists = await meta_crud.get_source_column(column_id)
        
        if not db_exists:
            raise Exception("测试数据库不存在")
        if not table_exists:
            raise Exception("测试表不存在")
        if not column_exists:
            raise Exception("测试字段不存在")
        
        print(f"   ✅ 验证测试数据存在: 数据库{db_id}, 表{table_id}, 字段{column_id}")

        # 2. 删除数据库（应该级联删除表和字段）
        delete_success = await meta_crud.delete_source_database(db_id=db_id)
        if not delete_success:
            raise Exception("删除源数据库失败：返回False")
        print(f"   ✅ 删除源数据库: {delete_success}")

        # 3. 验证级联删除
        db_after_delete = await meta_crud.get_source_database(db_id)
        table_after_delete = await meta_crud.get_source_table(table_id)
        column_after_delete = await meta_crud.get_source_column(column_id)
        
        if db_after_delete is not None:
            raise Exception("数据库删除失败：仍然存在")
        if table_after_delete is not None:
            raise Exception("级联删除失败：表仍然存在")
        if column_after_delete is not None:
            raise Exception("级联删除失败：字段仍然存在")
        
        print(f"   ✅ 验证级联删除: 数据库、表、字段都已删除")

        # 清空测试数据存储（因为已删除）
        test_data_store['source_db_id'] = None
        test_data_store['source_table_id'] = None
        test_data_store['source_column_id'] = None

        return True

    except Exception as e:
        print(f"   ❌ 级联删除测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 源数据库 → 源表 → 源字段 依赖顺序CRUD测试")
    print("=" * 80)

    try:
        # 1. 设置测试环境
        rdb_client, knowledge_id = await setup_test_environment()

        # 2. 执行所有测试
        test_results = []
        
        # 源数据库CRUD操作测试
        result1 = await test_source_database_crud(rdb_client, knowledge_id)
        test_results.append(("源数据库CRUD", result1))

        if result1 and test_data_store['source_db_id']:
            # 源表CRUD操作测试（依赖源数据库）
            result2 = await test_source_table_crud(rdb_client, knowledge_id, test_data_store['source_db_id'])
            test_results.append(("源表CRUD", result2))

            if result2 and test_data_store['source_table_id']:
                # 源字段CRUD操作测试（依赖源表）
                result3 = await test_source_column_crud(rdb_client, knowledge_id, test_data_store['source_table_id'])
                test_results.append(("源字段CRUD", result3))

                if result3 and test_data_store['source_column_id']:
                    # 级联删除测试
                    result4 = await test_cascade_delete(
                        rdb_client, knowledge_id, 
                        test_data_store['source_db_id'], 
                        test_data_store['source_table_id'], 
                        test_data_store['source_column_id']
                    )
                    test_results.append(("级联删除", result4))

        # 3. 输出测试结果
        print("\n" + "=" * 80)
        print("📊 测试结果汇总")
        print("=" * 80)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False

        if all_passed:
            print("\n🎉 所有测试通过！源数据库、源表、源字段CRUD功能正常")
        else:
            print("\n⚠️  部分测试失败，请检查日志")

        return all_passed

    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        print(f"\n❌ 测试执行失败: {e}")
        return False

    finally:
        # 4. 清理测试环境
        await cleanup_test_environment()


if __name__ == "__main__":
    asyncio.run(main())

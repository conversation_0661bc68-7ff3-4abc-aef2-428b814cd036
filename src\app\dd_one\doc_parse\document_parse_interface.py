from typing import Type
from doc_parse.base_parse.base_document_parse import BaseDocumentParser


class DocumentParserInterface:
    def __init__(self):
        """
        初始化文档解析接口，包含一个空的解析器注册表。
        """
        self._parsers = {}

    def register_parser(self, parser_name: str, parser_class: Type[BaseDocumentParser]) -> None:
        """
        注册一个解析器类，使用指定的名称。

        参数:
            parser_name (str): 解析器的标识名称
            parser_class (Type[BaseDocumentParser]): 要注册的解析器类

        异常:
            ValueError: 如果parser_class不是BaseDocumentParser的子类
        """
        if not issubclass(parser_class, BaseDocumentParser):
            raise ValueError(f"{parser_class.__name__} 必须是 BaseDocumentParser 的子类")
        self._parsers[parser_name] = parser_class

    def parse_document(self, parser_name: str, metrics_file: str, description_file: str,
                       sheet_names: str = None) -> str:
        """
        使用指定的解析器解析文档。

        参数:
            parser_name (str): 已注册的解析器名称
            metrics_file (str): 指标Excel文件路径
            description_file (str): 描述文件路径

        返回:
            str: 生成的Excel文件路径

        异常:
            ValueError: 如果parser_name未注册
        """
        if parser_name not in self._parsers:
            raise ValueError(f"未注册的解析器名称: {parser_name}")

        parser = self._parsers[parser_name](metrics_file, description_file, sheet_names)
        return parser.document_unparsing(metrics_file, description_file, sheet_names)

    def extraction_report(self, parser_name: str, metrics_file: str, description_file: str,
                          sheet_names: str = None) -> str:
        """
        使用指定的解析器解析文档读取出内容。

        参数:
            parser_name (str): 已注册的解析器名称
            metrics_file (str): 指标Excel文件路径
            description_file (str): 描述文件路径

        返回:
            str: 生成的Excel文件路径

        异常:
            ValueError: 如果parser_name未注册
        """
        if parser_name not in self._parsers:
            raise ValueError(f"未注册的解析器名称: {parser_name}")

        parser = self._parsers[parser_name](metrics_file, description_file, sheet_names)
        return parser.extraction_report(metrics_file, description_file, sheet_names)

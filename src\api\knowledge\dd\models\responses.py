"""
DD系统响应模型

定义所有API端点的响应数据模型，用于输出格式化和文档生成。
"""

from typing import Any, Dict, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


# ==================== 部门管理响应模型 ====================

class DepartmentResponse(BaseModel):
    """部门响应模型"""
    dept_id: str = Field(..., description="部门ID")
    dept_name: str = Field(..., description="部门名称")
    dept_desc: Optional[str] = Field(None, description="部门描述")
    dept_type: str = Field(..., description="部门类型")
    is_active: bool = Field(..., description="是否激活")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        json_schema_extra = {
            "example": {
                "dept_id": "TECH_DEPT_001",
                "dept_name": "技术部",
                "dept_desc": "负责技术开发和维护",
                "dept_type": "normal",
                "is_active": True,
                "create_time": "2024-01-01T10:00:00",
                "update_time": "2024-01-01T10:00:00"
            }
        }


# ==================== 填报数据响应模型 ====================

class SubmissionDataResponse(BaseModel):
    """填报数据响应模型"""
    id: int = Field(..., description="主键ID")
    submission_id: str = Field(..., description="填报ID")
    version: str = Field(..., description="版本")
    submission_type: str = Field(..., description="填报类型")
    report_type: str = Field(..., description="报表类型")
    set: Optional[str] = Field(None, description="套系信息")
    dr01: str = Field(..., description="数据层")
    dr06: str = Field(..., description="表名")
    dr07: str = Field(..., description="表名ID")
    dr09: str = Field(..., description="数据项名称")
    dr17: str = Field(..., description="需求口径")
    dr19: Optional[str] = Field(None, description="报送频率")
    dr22: Optional[str] = Field(None, description="责任部门")
    bdr01: Optional[str] = Field(None, description="业务部门")
    bdr02: Optional[str] = Field(None, description="责任人")
    report_data_id: Optional[int] = Field(None, description="关联报表数据ID")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "submission_id": "SUB_001",
                "version": "2024-01-01",
                "submission_type": "SUBMISSION",
                "report_type": "detail",
                "set": "SET_001",
                "dr01": "ADS",
                "dr06": "客户信息表",
                "dr07": "customer_info",
                "dr09": "客户姓名",
                "dr17": "客户的真实姓名，不能为空，最大长度50字符",
                "dr19": "日报",
                "dr22": "客户管理部",
                "bdr01": "客户部",
                "bdr02": "张三",
                "report_data_id": 1,
                "create_time": "2024-01-01T10:00:00",
                "update_time": "2024-01-01T10:00:00"
            }
        }


class SubmissionDataCreateResponse(BaseModel):
    """填报数据创建响应模型"""
    submission_id: int = Field(..., description="填报数据ID")
    vector_ids: List[Dict[str, Any]] = Field(..., description="向量化结果")

    class Config:
        json_schema_extra = {
            "example": {
                "submission_id": 1,
                "vector_ids": [
                    {
                        "vector_id": "vec_001",
                        "data_row_id": 2053676751,
                        "field_id": 9,
                        "field_code": "dr09",
                        "content": "客户姓名"
                    },
                    {
                        "vector_id": "vec_002", 
                        "data_row_id": 1849985493,
                        "field_id": 17,
                        "field_code": "dr17",
                        "content": "客户的真实姓名，不能为空，最大长度50字符"
                    }
                ]
            }
        }


# ==================== 搜索功能响应模型 ====================

class SearchResultItem(BaseModel):
    """搜索结果项模型"""
    score: float = Field(..., description="相似度分数")
    submission_data: SubmissionDataResponse = Field(..., description="填报数据")
    vector_info: Optional[Dict[str, Any]] = Field(None, description="向量信息")

    class Config:
        json_schema_extra = {
            "example": {
                "score": 0.85,
                "submission_data": {
                    "id": 1,
                    "submission_id": "SUB_001",
                    "dr09": "客户姓名",
                    "dr17": "客户的真实姓名，不能为空"
                },
                "vector_info": {
                    "data_row_id": 2053676751,
                    "field_id": 9,
                    "data_layer": "ADS"
                }
            }
        }


class SearchResponse(BaseModel):
    """搜索响应模型"""
    results: List[SearchResultItem] = Field(..., description="搜索结果列表")
    total: int = Field(..., description="总结果数")
    query: str = Field(..., description="搜索查询")
    search_time: float = Field(..., description="搜索耗时(秒)")

    class Config:
        json_schema_extra = {
            "example": {
                "results": [
                    {
                        "score": 0.85,
                        "submission_data": {
                            "id": 1,
                            "submission_id": "SUB_001",
                            "dr09": "客户姓名"
                        }
                    }
                ],
                "total": 1,
                "query": "客户姓名",
                "search_time": 0.123
            }
        }


# ==================== 分发数据响应模型 ====================

class DistributionResponse(BaseModel):
    """分发数据响应模型"""
    id: int = Field(..., description="主键ID")
    submission_id: str = Field(..., description="填报ID")
    version: str = Field(..., description="版本")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "submission_id": "SUB_001",
                "version": "2024-01-01",
                "create_time": "2024-01-01T10:00:00",
                "update_time": "2024-01-01T10:00:00"
            }
        }


# ==================== 报表数据响应模型 ====================

class ReportDataResponse(BaseModel):
    """报表数据响应模型"""
    id: int = Field(..., description="主键ID")
    knowledge_id: str = Field(..., description="知识库ID")
    version: str = Field(..., description="版本")
    report_name: str = Field(..., description="报表名称")
    report_code: str = Field(..., description="报表代码")
    report_layer: str = Field(..., description="报表层级")
    report_type: str = Field(..., description="报表类型")
    set: Optional[str] = Field(None, description="套系信息")
    report_desc: Optional[str] = Field(None, description="报表描述")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "knowledge_id": "KB_001",
                "version": "2024-01-01",
                "report_name": "客户信息报表",
                "report_code": "RPT_CUSTOMER",
                "report_layer": "BDM",
                "report_type": "detail",
                "set": "SET_001",
                "report_desc": "客户基础信息统计报表",
                "create_time": "2024-01-01T10:00:00",
                "update_time": "2024-01-01T10:00:00"
            }
        }


# ==================== 系统响应模型 ====================

class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="系统状态")
    service: str = Field(..., description="服务名称")
    version: str = Field(..., description="版本号")
    modules: List[str] = Field(..., description="功能模块列表")

    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "service": "DD数据需求管理系统",
                "version": "1.0.0",
                "modules": [
                    "部门管理",
                    "填报数据管理",
                    "搜索功能",
                    "分发数据管理",
                    "报表数据管理"
                ]
            }
        }


class ApiOverviewResponse(BaseModel):
    """API概览响应模型"""
    service: str = Field(..., description="服务名称")
    description: str = Field(..., description="服务描述")
    api_groups: Dict[str, Any] = Field(..., description="API分组信息")
    features: List[str] = Field(..., description="功能特性列表")

    class Config:
        json_schema_extra = {
            "example": {
                "service": "DD数据需求管理系统",
                "description": "提供DD系统的完整API接口",
                "api_groups": {
                    "部门管理": {
                        "prefix": "/api/knowledge/dd/departments",
                        "endpoints": ["POST /", "GET /{dept_id}", "PUT /{dept_id}"]
                    }
                },
                "features": [
                    "自动向量化处理",
                    "智能语义搜索",
                    "RESTful API设计"
                ]
            }
        }

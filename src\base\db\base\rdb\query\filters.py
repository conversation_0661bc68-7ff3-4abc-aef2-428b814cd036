"""
过滤器构建和验证

提供复杂过滤条件的构建和验证功能
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime, date

from ..core.types import DatabaseValue, ComparisonOperator, LogicalOperator
from ..core.models import QueryFilter, QueryFilterGroup
from ..core.exceptions import ValidationError


class FilterBuilder:
    """过滤器构建器
    
    提供便捷的方法构建复杂的过滤条件
    """
    
    def __init__(self):
        self._filters: List[Union[QueryFilter, QueryFilterGroup]] = []
        self._current_operator = LogicalOperator.AND
    
    def reset(self) -> 'FilterBuilder':
        """重置构建器"""
        self._filters = []
        self._current_operator = LogicalOperator.AND
        return self
    
    def use_and(self) -> 'FilterBuilder':
        """使用AND逻辑"""
        self._current_operator = LogicalOperator.AND
        return self
    
    def use_or(self) -> 'FilterBuilder':
        """使用OR逻辑"""
        self._current_operator = LogicalOperator.OR
        return self
    
    def eq(self, field: str, value: DatabaseValue) -> 'FilterBuilder':
        """等于条件"""
        return self._add_filter(field, ComparisonOperator.EQ, value)
    
    def ne(self, field: str, value: DatabaseValue) -> 'FilterBuilder':
        """不等于条件"""
        return self._add_filter(field, ComparisonOperator.NE, value)
    
    def gt(self, field: str, value: DatabaseValue) -> 'FilterBuilder':
        """大于条件"""
        return self._add_filter(field, ComparisonOperator.GT, value)
    
    def gte(self, field: str, value: DatabaseValue) -> 'FilterBuilder':
        """大于等于条件"""
        return self._add_filter(field, ComparisonOperator.GTE, value)
    
    def lt(self, field: str, value: DatabaseValue) -> 'FilterBuilder':
        """小于条件"""
        return self._add_filter(field, ComparisonOperator.LT, value)
    
    def lte(self, field: str, value: DatabaseValue) -> 'FilterBuilder':
        """小于等于条件"""
        return self._add_filter(field, ComparisonOperator.LTE, value)
    
    def in_(self, field: str, values: List[DatabaseValue]) -> 'FilterBuilder':
        """IN条件"""
        return self._add_filter(field, ComparisonOperator.IN, values)
    
    def not_in(self, field: str, values: List[DatabaseValue]) -> 'FilterBuilder':
        """NOT IN条件"""
        return self._add_filter(field, ComparisonOperator.NOT_IN, values)
    
    def like(self, field: str, pattern: str) -> 'FilterBuilder':
        """LIKE条件"""
        return self._add_filter(field, ComparisonOperator.LIKE, pattern)
    
    def ilike(self, field: str, pattern: str) -> 'FilterBuilder':
        """ILIKE条件（不区分大小写）"""
        return self._add_filter(field, ComparisonOperator.ILIKE, pattern)
    
    def between(self, field: str, start: DatabaseValue, end: DatabaseValue) -> 'FilterBuilder':
        """BETWEEN条件"""
        return self._add_filter(field, ComparisonOperator.BETWEEN, [start, end])
    
    def is_null(self, field: str) -> 'FilterBuilder':
        """IS NULL条件"""
        return self._add_filter(field, ComparisonOperator.IS_NULL, None)
    
    def is_not_null(self, field: str) -> 'FilterBuilder':
        """IS NOT NULL条件"""
        return self._add_filter(field, ComparisonOperator.IS_NOT_NULL, None)
    
    def contains(self, field: str, value: str) -> 'FilterBuilder':
        """包含条件（LIKE %value%）"""
        return self.like(field, f"%{value}%")
    
    def starts_with(self, field: str, value: str) -> 'FilterBuilder':
        """开始于条件（LIKE value%）"""
        return self.like(field, f"{value}%")
    
    def ends_with(self, field: str, value: str) -> 'FilterBuilder':
        """结束于条件（LIKE %value）"""
        return self.like(field, f"%{value}")
    
    def date_range(self, field: str, start_date: date, end_date: date) -> 'FilterBuilder':
        """日期范围条件"""
        return self.between(field, start_date, end_date)
    
    def today(self, field: str) -> 'FilterBuilder':
        """今天条件"""
        today = date.today()
        return self.date_range(field, today, today)
    
    def this_year(self, field: str) -> 'FilterBuilder':
        """今年条件"""
        today = date.today()
        start_of_year = date(today.year, 1, 1)
        end_of_year = date(today.year, 12, 31)
        return self.date_range(field, start_of_year, end_of_year)
    
    def group(self, builder_func) -> 'FilterBuilder':
        """创建过滤器组
        
        Args:
            builder_func: 接受FilterBuilder参数的函数
        
        Example:
            builder.group(lambda b: b.eq('status', 'active').or_().eq('status', 'pending'))
        """
        group_builder = FilterBuilder()
        builder_func(group_builder)
        group = group_builder.build()
        
        if group:
            self._filters.append(group)
        
        return self
    
    def and_group(self, builder_func) -> 'FilterBuilder':
        """创建AND过滤器组"""
        group_builder = FilterBuilder().use_and()
        builder_func(group_builder)
        group = group_builder.build()
        
        if group:
            self._filters.append(group)
        
        return self
    
    def or_group(self, builder_func) -> 'FilterBuilder':
        """创建OR过滤器组"""
        group_builder = FilterBuilder().use_or()
        builder_func(group_builder)
        group = group_builder.build()
        
        if group:
            self._filters.append(group)
        
        return self
    
    def add_filter(self, filter_obj: Union[QueryFilter, QueryFilterGroup]) -> 'FilterBuilder':
        """添加自定义过滤器"""
        self._filters.append(filter_obj)
        return self
    
    def build(self) -> Optional[QueryFilterGroup]:
        """构建过滤器组"""
        if not self._filters:
            return None
        
        if len(self._filters) == 1 and isinstance(self._filters[0], QueryFilterGroup):
            return self._filters[0]
        
        return QueryFilterGroup(
            operator=self._current_operator,
            filters=self._filters.copy()
        )
    
    def _add_filter(self, field: str, operator: ComparisonOperator, value: DatabaseValue) -> 'FilterBuilder':
        """添加过滤器"""
        filter_obj = QueryFilter(field=field, operator=operator, value=value)
        self._filters.append(filter_obj)
        return self
    
    def __len__(self) -> int:
        return len(self._filters)
    
    def __bool__(self) -> bool:
        return len(self._filters) > 0


class FilterValidator:
    """过滤器验证器
    
    验证过滤器的有效性和安全性
    """
    
    def __init__(self, allowed_fields: Optional[List[str]] = None):
        self.allowed_fields = set(allowed_fields) if allowed_fields else None
        self.max_filter_depth = 10  # 最大嵌套深度
        self.max_in_values = 1000   # IN操作最大值数量
    
    def validate_filter(self, filter_obj: QueryFilter) -> None:
        """验证单个过滤器"""
        # 验证字段名
        if not filter_obj.field:
            raise ValidationError("Filter field cannot be empty")
        
        if self.allowed_fields and filter_obj.field not in self.allowed_fields:
            raise ValidationError(f"Field '{filter_obj.field}' is not allowed")
        
        # 验证操作符和值的匹配
        self._validate_operator_value(filter_obj.operator, filter_obj.value)
        
        # 验证字段名安全性
        self._validate_field_name(filter_obj.field)
    
    def validate_filter_group(self, filter_group: QueryFilterGroup, depth: int = 0) -> None:
        """验证过滤器组"""
        if depth > self.max_filter_depth:
            raise ValidationError(f"Filter nesting depth exceeds maximum ({self.max_filter_depth})")
        
        if not filter_group.filters:
            raise ValidationError("Filter group cannot be empty")
        
        for filter_item in filter_group.filters:
            if isinstance(filter_item, QueryFilter):
                self.validate_filter(filter_item)
            elif isinstance(filter_item, QueryFilterGroup):
                self.validate_filter_group(filter_item, depth + 1)
            else:
                raise ValidationError(f"Invalid filter type: {type(filter_item)}")
    
    def _validate_operator_value(self, operator: ComparisonOperator, value: DatabaseValue) -> None:
        """验证操作符和值的匹配"""
        if operator in (ComparisonOperator.IS_NULL, ComparisonOperator.IS_NOT_NULL):
            if value is not None:
                raise ValidationError(f"Operator {operator} should not have a value")
        
        elif operator in (ComparisonOperator.IN, ComparisonOperator.NOT_IN):
            if not isinstance(value, (list, tuple, set)):
                raise ValidationError(f"Operator {operator} requires a list/tuple/set value")
            
            if len(value) > self.max_in_values:
                raise ValidationError(f"IN operation exceeds maximum values ({self.max_in_values})")
            
            if len(value) == 0:
                raise ValidationError(f"Operator {operator} requires at least one value")
        
        elif operator == ComparisonOperator.BETWEEN:
            if not isinstance(value, (list, tuple)) or len(value) != 2:
                raise ValidationError(f"Operator {operator} requires exactly 2 values")
        
        elif operator in (ComparisonOperator.LIKE, ComparisonOperator.ILIKE):
            if not isinstance(value, str):
                raise ValidationError(f"Operator {operator} requires a string value")
        
        else:
            # 其他操作符需要非空值
            if value is None:
                raise ValidationError(f"Operator {operator} requires a non-null value")
    
    def _validate_field_name(self, field_name: str) -> None:
        """验证字段名安全性"""
        # 检查SQL注入风险
        dangerous_chars = [';', '--', '/*', '*/', 'xp_', 'sp_']
        field_lower = field_name.lower()
        
        for dangerous in dangerous_chars:
            if dangerous in field_lower:
                raise ValidationError(f"Field name contains dangerous characters: {field_name}")
        
        # 检查字段名格式
        if not field_name.replace('_', '').replace('.', '').isalnum():
            raise ValidationError(f"Field name contains invalid characters: {field_name}")


def create_filter() -> FilterBuilder:
    """创建过滤器构建器的便捷函数"""
    return FilterBuilder()


def and_filter(*filters: Union[QueryFilter, QueryFilterGroup]) -> QueryFilterGroup:
    """创建AND过滤器组的便捷函数"""
    return QueryFilterGroup(
        operator=LogicalOperator.AND,
        filters=list(filters)
    )


def or_filter(*filters: Union[QueryFilter, QueryFilterGroup]) -> QueryFilterGroup:
    """创建OR过滤器组的便捷函数"""
    return QueryFilterGroup(
        operator=LogicalOperator.OR,
        filters=list(filters)
    )

"""
DD系统管理API路由

提供DD系统的管理功能，包括：
- 健康检查
- API概览
- 系统状态
"""

from fastapi import APIRouter
from ..models.responses import HealthCheckResponse, ApiOverviewResponse
from ..utils.helpers import format_response

# 创建路由器
router = APIRouter(tags=["DD系统管理"])


@router.get("/health", response_model=HealthCheckResponse, summary="DD系统健康检查")
async def health_check():
    """
    DD系统健康检查端点
    
    返回系统状态信息，包括：
    - 系统运行状态
    - 服务名称和版本
    - 功能模块列表
    """
    return HealthCheckResponse(
        status="healthy",
        service="DD数据需求管理系统",
        version="1.0.0",
        modules=[
            "部门管理",
            "填报数据管理",
            "搜索功能",
            "分发数据管理",
            "报表数据管理"
        ]
    )


@router.get("/overview", response_model=ApiOverviewResponse, summary="DD系统API概览")
async def api_overview():
    """
    DD系统API概览
    
    返回所有可用的API端点信息，包括：
    - API分组信息
    - 端点列表
    - 功能特性
    """
    return ApiOverviewResponse(
        service="DD数据需求管理系统",
        description="提供DD系统的完整API接口，包括CRUD操作和智能搜索功能",
        api_groups={
            "部门管理": {
                "prefix": "/api/knowledge/dd/departments",
                "endpoints": [
                    "POST / - 创建部门",
                    "GET /{dept_id} - 获取部门详情",
                    "PUT /{dept_id} - 更新部门信息",
                    "DELETE /{dept_id} - 删除部门",
                    "GET / - 查询部门列表"
                ],
                "description": "完整的部门CRUD操作，支持部门类型和状态管理"
            },
            "填报数据管理": {
                "prefix": "/api/knowledge/dd/submissions",
                "endpoints": [
                    "POST / - 创建填报数据（含向量化）",
                    "GET /{submission_id} - 获取填报数据详情",
                    "PUT /{submission_id} - 更新填报数据",
                    "DELETE /{submission_id} - 删除填报数据",
                    "GET / - 查询填报数据列表"
                ],
                "description": "填报数据的完整生命周期管理，自动向量化dr09和dr17字段"
            },
            "搜索功能": {
                "prefix": "/api/knowledge/dd/search",
                "endpoints": [
                    "POST /vector - 向量搜索",
                    "POST /hybrid - 混合搜索",
                    "GET /by-data-item - 按数据项名称搜索",
                    "GET /by-requirement - 按需求口径搜索"
                ],
                "description": "智能搜索功能，支持语义搜索和精确搜索"
            },
            "分发数据管理": {
                "prefix": "/api/knowledge/dd/distribution",
                "endpoints": [
                    "POST /pre - 创建分发前数据",
                    "GET /pre/{pre_id} - 获取分发前数据详情",
                    "GET /pre - 查询分发前数据列表",
                    "POST /post - 创建分发后数据",
                    "GET /post/{post_id} - 获取分发后数据详情",
                    "GET /post - 查询分发后数据列表"
                ],
                "description": "数据分发流程管理，支持分发前后数据的完整追踪"
            },
            "报表数据管理": {
                "prefix": "/api/knowledge/dd/reports",
                "endpoints": [
                    "POST / - 创建报表数据",
                    "GET /{report_id} - 获取报表数据详情",
                    "PUT /{report_id} - 更新报表数据",
                    "DELETE /{report_id} - 删除报表数据",
                    "GET / - 查询报表数据列表",
                    "GET /by-knowledge/{knowledge_id} - 根据知识库ID查询报表"
                ],
                "description": "报表数据的完整管理，支持多层级报表组织"
            }
        },
        features=[
            "自动向量化处理（dr09、dr17字段）",
            "智能语义搜索",
            "混合搜索（向量+文本）",
            "分页查询支持",
            "多维度过滤",
            "RESTful API设计",
            "完整的OpenAPI文档",
            "统一错误处理",
            "参数验证",
            "依赖注入架构"
        ]
    )


@router.get("/status", summary="系统状态详情")
async def system_status():
    """
    获取系统详细状态信息
    
    返回更详细的系统运行状态，包括：
    - 各模块状态
    - 数据库连接状态
    - 性能指标
    """
    return {
        "system": {
            "name": "DD数据需求管理系统",
            "version": "1.0.0",
            "status": "running",
            "uptime": "系统运行中"
        },
        "modules": {
            "departments": {"status": "active", "description": "部门管理模块"},
            "submissions": {"status": "active", "description": "填报数据管理模块"},
            "search": {"status": "active", "description": "搜索功能模块"},
            "distribution": {"status": "active", "description": "分发数据管理模块"},
            "reports": {"status": "active", "description": "报表数据管理模块"}
        },
        "database": {
            "mysql": {"status": "connected", "description": "关系数据库"},
            "pgvector": {"status": "connected", "description": "向量数据库"}
        },
        "api": {
            "total_endpoints": 28,
            "documentation": "/docs",
            "base_url": "/api/knowledge/dd"
        }
    }


@router.get("/metrics", summary="系统指标")
async def system_metrics():
    """
    获取系统性能指标
    
    返回系统的性能和使用统计信息
    """
    return {
        "api_metrics": {
            "total_requests": "统计中",
            "average_response_time": "统计中",
            "error_rate": "统计中"
        },
        "database_metrics": {
            "connection_pool_size": "统计中",
            "active_connections": "统计中",
            "query_performance": "统计中"
        },
        "search_metrics": {
            "vector_searches": "统计中",
            "hybrid_searches": "统计中",
            "average_search_time": "统计中"
        },
        "note": "详细指标统计功能待实现"
    }

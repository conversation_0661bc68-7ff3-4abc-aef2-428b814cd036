"""
ORM数据库方言模块

提供各种数据库的方言实现，用于ORM客户端
"""

from .base import DatabaseDialect, DatabaseFeatures, FeatureSupport
from .mysql import MySQLDialect
from .sqlite import SQLiteDialect

# 方言注册表
DIALECT_REGISTRY = {
    'mysql': MySQLDialect,
    'sqlite': SQLiteDialect,
}

def get_dialect(dialect_name: str) -> DatabaseDialect:
    """
    获取指定的数据库方言实例

    Args:
        dialect_name: 方言名称

    Returns:
        数据库方言实例

    Raises:
        ValueError: 如果方言不支持
    """
    if dialect_name not in DIALECT_REGISTRY:
        raise ValueError(f"Unsupported dialect: {dialect_name}")

    dialect_class = DIALECT_REGISTRY[dialect_name]
    return dialect_class()

def detect_dialect_from_url(database_url: str) -> str:
    """
    从数据库URL检测方言

    Args:
        database_url: 数据库连接URL

    Returns:
        方言名称
    """
    url_lower = database_url.lower()

    if 'mysql' in url_lower:
        return 'mysql'
    elif 'postgresql' in url_lower or 'postgres' in url_lower:
        return 'postgresql'
    elif 'sqlite' in url_lower:
        return 'sqlite'
    else:
        raise ValueError(f"Cannot detect dialect from URL: {database_url}")

def list_supported_dialects() -> list:
    """列出所有支持的方言"""
    return list(DIALECT_REGISTRY.keys())

def is_dialect_supported(dialect_name: str) -> bool:
    """检查方言是否支持"""
    return dialect_name in DIALECT_REGISTRY

# 为了保持接口兼容性，提供别名
get_supported_dialects = list_supported_dialects

__all__ = [
    'DatabaseDialect',
    'DatabaseFeatures',
    'FeatureSupport',
    'MySQLDialect',
    'SQLiteDialect',
    'get_dialect',
    'detect_dialect_from_url',
    'list_supported_dialects',
    'get_supported_dialects',
    'is_dialect_supported',
    'DIALECT_REGISTRY'
]

import logging
import asyncio
import weakref
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import OrderedDict

from ..exceptions import ClientError

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    client: Any
    created_at: datetime = field(default_factory=datetime.now)
    last_accessed: datetime = field(default_factory=datetime.now)
    access_count: int = 0
    ttl: Optional[int] = None  # 生存时间（秒）
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        
        return datetime.now() - self.created_at > timedelta(seconds=self.ttl)
    
    def touch(self):
        """更新访问时间"""
        self.last_accessed = datetime.now()
        self.access_count += 1


class ClientCache:
    """
    客户端缓存
    
    特性：
    - LRU淘汰策略
    - TTL过期机制
    - 弱引用支持
    - 自动清理
    """
    
    def __init__(self, 
                 max_size: int = 100,
                 default_ttl: Optional[int] = None,
                 cleanup_interval: int = 300):
        """
        初始化缓存
        
        Args:
            max_size: 最大缓存大小
            default_ttl: 默认TTL（秒）
            cleanup_interval: 清理间隔（秒）
        """
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._max_size = max_size
        self._default_ttl = default_ttl
        self._cleanup_interval = cleanup_interval
        self._cleanup_task: Optional[asyncio.Task] = None
        self._initialized = False
        self._lock = asyncio.Lock()
        
        # 统计信息
        self._stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "expirations": 0
        }
    
    async def initialize(self):
        """初始化缓存"""
        if self._initialized:
            return
        
        # 启动清理任务
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        self._initialized = True
        logger.info(f"客户端缓存初始化完成，最大大小: {self._max_size}")
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存的客户端
        
        Args:
            key: 缓存键
            
        Returns:
            客户端实例或None
        """
        async with self._lock:
            if key not in self._cache:
                self._stats["misses"] += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if entry.is_expired():
                del self._cache[key]
                self._stats["expirations"] += 1
                self._stats["misses"] += 1
                logger.debug(f"缓存条目已过期: {key}")
                return None
            
            # 更新访问信息
            entry.touch()
            
            # 移动到末尾（LRU）
            self._cache.move_to_end(key)
            
            self._stats["hits"] += 1
            logger.debug(f"缓存命中: {key}")
            return entry.client
    
    async def set(self, 
                  key: str, 
                  client: Any, 
                  ttl: Optional[int] = None) -> bool:
        """
        设置缓存
        
        Args:
            key: 缓存键
            client: 客户端实例
            ttl: 生存时间（秒）
            
        Returns:
            是否成功设置
        """
        try:
            async with self._lock:
                # 使用默认TTL
                if ttl is None:
                    ttl = self._default_ttl
                
                # 创建缓存条目
                entry = CacheEntry(
                    key=key,
                    client=client,
                    ttl=ttl
                )
                
                # 如果键已存在，更新
                if key in self._cache:
                    self._cache[key] = entry
                    self._cache.move_to_end(key)
                else:
                    # 检查是否需要淘汰
                    if len(self._cache) >= self._max_size:
                        await self._evict_lru()
                    
                    self._cache[key] = entry
                
                logger.debug(f"缓存设置成功: {key}")
                return True
                
        except Exception as e:
            logger.error(f"设置缓存失败: {e}")
            return False
    
    async def remove(self, key: str) -> Optional[Any]:
        """
        移除缓存条目
        
        Args:
            key: 缓存键
            
        Returns:
            被移除的客户端实例
        """
        async with self._lock:
            if key in self._cache:
                entry = self._cache.pop(key)
                logger.debug(f"缓存条目已移除: {key}")
                return entry.client
            return None
    
    async def clear(self):
        """清空缓存"""
        async with self._lock:
            self._cache.clear()
            logger.info("缓存已清空")
    
    async def list_all(self) -> Dict[str, Any]:
        """列出所有缓存的客户端"""
        async with self._lock:
            return {key: entry.client for key, entry in self._cache.items()}
    
    async def get_info(self, key: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存条目信息
        
        Args:
            key: 缓存键
            
        Returns:
            缓存条目信息
        """
        async with self._lock:
            if key not in self._cache:
                return None
            
            entry = self._cache[key]
            return {
                "key": entry.key,
                "created_at": entry.created_at,
                "last_accessed": entry.last_accessed,
                "access_count": entry.access_count,
                "ttl": entry.ttl,
                "is_expired": entry.is_expired(),
                "client_type": type(entry.client).__name__
            }
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        async with self._lock:
            total_requests = self._stats["hits"] + self._stats["misses"]
            hit_rate = self._stats["hits"] / total_requests if total_requests > 0 else 0
            
            return {
                "size": len(self._cache),
                "max_size": self._max_size,
                "hit_rate": hit_rate,
                "hits": self._stats["hits"],
                "misses": self._stats["misses"],
                "evictions": self._stats["evictions"],
                "expirations": self._stats["expirations"]
            }
    
    async def _evict_lru(self):
        """淘汰最近最少使用的条目"""
        if self._cache:
            # OrderedDict的第一个元素是最旧的
            key, entry = self._cache.popitem(last=False)
            self._stats["evictions"] += 1
            logger.debug(f"LRU淘汰缓存条目: {key}")
    
    async def _cleanup_expired(self):
        """清理过期条目"""
        expired_keys = []
        
        for key, entry in self._cache.items():
            if entry.is_expired():
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
            self._stats["expirations"] += 1
        
        if expired_keys:
            logger.debug(f"清理过期缓存条目: {len(expired_keys)}个")
    
    async def _cleanup_loop(self):
        """清理循环"""
        while self._initialized:
            try:
                async with self._lock:
                    await self._cleanup_expired()
                
                await asyncio.sleep(self._cleanup_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"缓存清理循环错误: {e}")
                await asyncio.sleep(self._cleanup_interval)
    
    async def cleanup(self):
        """清理所有资源"""
        logger.info("开始清理客户端缓存...")
        
        self._initialized = False
        
        # 取消清理任务
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 清空缓存
        async with self._lock:
            self._cache.clear()
        
        logger.info("客户端缓存清理完成")
    
    def __len__(self) -> int:
        """返回缓存大小"""
        return len(self._cache)
    
    def __contains__(self, key: str) -> bool:
        """检查键是否在缓存中"""
        return key in self._cache

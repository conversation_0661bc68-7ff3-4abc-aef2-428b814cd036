"""
Metadata模块异常定义

参考DD系统的exceptions.py设计，定义元数据相关的异常类。
"""


class MetadataError(Exception):
    """元数据操作异常基类"""
    
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "METADATA_ERROR"


class MetadataValidationError(MetadataError):
    """元数据验证异常"""
    
    def __init__(self, message: str, field_name: str = None):
        super().__init__(message, "METADATA_VALIDATION_ERROR")
        self.field_name = field_name


class MetadataNotFoundError(MetadataError):
    """元数据未找到异常"""
    
    def __init__(self, entity_type: str = None, entity_id: str = None, message: str = None):
        if message is None:
            if entity_type and entity_id:
                message = f"{entity_type}不存在: {entity_id}"
            elif entity_type:
                message = f"{entity_type}不存在"
            else:
                message = "元数据不存在"
        super().__init__(message, "METADATA_NOT_FOUND")
        self.entity_type = entity_type
        self.entity_id = entity_id


class MetadataConflictError(MetadataError):
    """元数据冲突异常（如名称重复）"""
    
    def __init__(self, message: str, conflict_field: str = None):
        super().__init__(message, "METADATA_CONFLICT_ERROR")
        self.conflict_field = conflict_field


class MetadataIntegrityError(MetadataError):
    """元数据完整性异常（如外键约束）"""
    
    def __init__(self, message: str, constraint_name: str = None):
        super().__init__(message, "METADATA_INTEGRITY_ERROR")
        self.constraint_name = constraint_name

"""
迭代式Schema生成Pipeline使用示例
展示新的9步骤Pipeline流程
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../"))

from pipeline.factory import PipelineFactory
from pipeline.core.context import PipelineContext


async def demonstrate_iterative_pipeline():
    """演示迭代式Schema生成Pipeline"""
    print("🚀 迭代式Schema生成Pipeline演示")
    print("=" * 60)
    
    # 创建Pipeline
    pipeline = PipelineFactory.create_iterative_schema_pipeline(include_business_logic=True)
    print(f"✅ 创建Pipeline成功，包含 {len(pipeline.steps)} 个步骤")
    
    # 显示Pipeline流程
    print("\n📋 Pipeline处理流程:")
    for i, step in enumerate(pipeline.steps, 1):
        step_desc = {
            "schema_generator_1": "第1次Schema生成 - 基于输入table_names",
            "table_selector": "表选择 - 筛选最相关的表",
            "schema_generator_2": "第2次Schema生成 - 基于筛选后的表",
            "column_selector": "列选择 - 筛选最相关的列",
            "schema_generator_3": "第3次Schema生成 - 基于筛选后的列",
            "key_associator": "二次列筛选 - 关联键信息整合",
            "schema_generator_final": "第4次Schema生成 - 最终Schema (is_final=True)",
            "sql_generator": "SQL生成 - 生成查询语句",
            "business_logic_generator": "业务逻辑生成 - 生成结构化描述"
        }
        desc = step_desc.get(step.name, step.description)
        print(f"  {i}. {step.name}: {desc}")
    
    # 创建上下文
    context = PipelineContext(
        user_question="查询2024年制造业企业的贷款余额统计",
        hint="银保监会口径，IS_CBIRC_LOAN='Y'"
    )
    
    # 设置初始参数
    context.set("schema_generation_params", {
        "source_type": "source",
        "table_names": ["loan_info", "customer_info", "industry_info", "account_info"],
        "column_limit": 6,
        "token_size": 7500,
        "is_final": False  # 初始阶段设为False
    })
    
    print(f"\n🎯 用户问题: {context.user_question}")
    print(f"💡 提示信息: {context.hint}")
    print(f"📊 初始表名: {context.get('schema_generation_params')['table_names']}")
    
    print("\n" + "=" * 60)
    print("开始Pipeline执行...")
    print("=" * 60)
    
    # 注意：这里只是演示Pipeline结构，实际执行需要真实的LLM和数据库连接
    print("\n⚠️  注意：以下是Pipeline结构演示，实际执行需要:")
    print("   - 配置LLM服务连接")
    print("   - 配置数据库连接") 
    print("   - 配置相应的prompt模板和解析器")
    
    # 模拟各步骤的预期输出
    print("\n📝 各步骤预期输出格式:")
    
    print("\n1️⃣  第1次Schema生成输出:")
    print("   context.db_schema = [")
    print("     {")
    print('       "prompt": "CREATE TABLE loan_info (...);",')
    print('       "db_to_tables": {"bank_db": ["loan_info", "customer_info"]},')
    print('       "table_to_columns": {"loan_info": ["id", "amount", "..."], ...}')
    print("     },")
    print("     ...")
    print("   ]")
    
    print("\n2️⃣  表选择输出:")
    print("   context.candidate_tables = {")
    print('     "bank_db": ["loan_info", "customer_info"],')
    print('     "ref_db": ["industry_info"]')
    print("   }")
    
    print("\n3️⃣  列选择输出:")
    print("   context.candidate_columns = {")
    print('     "loan_info": ["id", "amount", "customer_id", "industry_id"],')
    print('     "customer_info": ["id", "name", "industry_id"],')
    print('     "industry_info": ["id", "name", "type"]')
    print("   }")
    
    print("\n4️⃣  最终Schema生成输出 (is_final=True):")
    print('   context.db_schema = "prompt"  # 字符串格式')
    print("   context.candidate_tables = {...}  # 分离的映射")
    print("   context.candidate_columns = {...}  # 分离的映射")
    
    print("\n5️⃣  SQL生成输出:")
    print("   context.sql_candidates = [")
    print('     "SELECT SUM(l.amount) FROM loan_info l JOIN industry_info i ON l.industry_id = i.id WHERE i.type = \'制造业\' AND YEAR(l.create_date) = 2024 AND l.IS_CBIRC_LOAN = \'Y\'"')
    print("   ]")
    
    print("\n6️⃣  业务逻辑生成输出:")
    print("   context.business_logic = {")
    print('     "表范围": "涉及表及字段: loan_info(amount, customer_id, industry_id); industry_info(id, name, type)",')
    print('     "计算逻辑": "对制造业企业的贷款金额进行求和统计",')
    print('     "条件": "限定2024年数据，银保监会贷款口径(IS_CBIRC_LOAN=\'Y\')",')
    print('     "维度": "按行业类型(制造业)进行数据筛选和聚合",')
    print('     "整体逻辑描述": "查询2024年制造业企业在银保监会口径下的贷款余额总计"')
    print("   }")


async def demonstrate_pipeline_flexibility():
    """演示Pipeline的灵活性"""
    print("\n" + "=" * 60)
    print("🔧 Pipeline灵活性演示")
    print("=" * 60)
    
    # 创建不同配置的Pipeline
    pipelines = {
        "标准Pipeline": PipelineFactory.create_nl2sql_pipeline(include_business_logic=False),
        "增强Pipeline": PipelineFactory.create_enhanced_nl2sql_pipeline(include_business_logic=True),
        "迭代Pipeline": PipelineFactory.create_iterative_schema_pipeline(include_business_logic=True),
        "自定义Pipeline": PipelineFactory.create_custom_pipeline([
            "schema_generator", "column_selector", "sql_generator"
        ])
    }
    
    for name, pipeline in pipelines.items():
        step_names = [step.name for step in pipeline.steps]
        print(f"\n📋 {name}:")
        print(f"   步骤数: {len(step_names)}")
        print(f"   步骤: {' → '.join(step_names)}")


async def main():
    """主函数"""
    await demonstrate_iterative_pipeline()
    await demonstrate_pipeline_flexibility()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("=" * 60)
    
    print("\n📚 使用说明:")
    print("1. 迭代式Pipeline通过4次Schema生成逐步精化结果")
    print("2. 每次Schema生成都基于前一步的筛选结果")
    print("3. 最终的is_final=True确保输出格式适配后续步骤")
    print("4. BusinessLogicGenerator使用get_colkv.py提取精确的表范围信息")
    print("5. 整个流程支持多Schema片段的并发处理和智能合并")
    
    print("\n🔧 实际部署时需要:")
    print("- 配置LLM服务 (OpenAI, Azure, 本地模型等)")
    print("- 配置数据库连接 (MySQL, PostgreSQL等)")
    print("- 准备prompt模板文件")
    print("- 配置解析器规则")
    print("- 设置日志和监控")


if __name__ == "__main__":
    asyncio.run(main())

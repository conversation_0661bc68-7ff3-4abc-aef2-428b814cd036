"""
数据回填功能测试

测试数据回填的完整流程，包括：
1. 数据验证测试
2. 字段变化检查测试
3. 部门关联验证测试
4. 数据更新操作测试
"""

import asyncio
import logging
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from ..data_backfill import DataBackfill
from ..infrastructure.models import DepartmentAssignmentRequest


class DataBackfillTester:
    """数据回填测试类"""
    
    def __init__(self, rdb_client, vdb_client=None):
        """
        初始化测试类
        
        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端（可选）
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.data_backfill = DataBackfill(rdb_client, vdb_client)
        
    async def prepare_test_data(self):
        """准备测试数据"""
        logger.info("开始准备数据回填测试数据...")
        
        # 准备biz_dd_post_distribution测试数据
        await self._prepare_post_distribution_data()
        
        logger.info("数据回填测试数据准备完成")
    
    async def _prepare_post_distribution_data(self):
        """准备分发后数据"""
        logger.info("准备biz_dd_post_distribution测试数据...")
        
        # 清理现有测试数据
        await self.rdb_client.aexecute(
            "DELETE FROM biz_dd_post_distribution WHERE submission_id LIKE 'BACKFILL_%'"
        )
        
        # 插入测试数据
        test_records = [
            {
                "submission_id": "BACKFILL_001",
                "dr07": "g0107",
                "version": "v1.0",
                "dr22": "DEPT_FINANCE",
                "bdr01": "DEPT_FINANCE",
                "bdr03": "",
                "bdr09": "[1,2,3]"  # JSON格式的table_id列表
            },
            {
                "submission_id": "BACKFILL_002",
                "dr07": "g0107",
                "version": "v1.0", 
                "dr22": "DEPT_CREDIT",
                "bdr01": "DEPT_CREDIT",
                "bdr03": "备注信息",
                "bdr09": "[4,5,6]"
            }
        ]
        
        for record in test_records:
            await self.rdb_client.aexecute(
                """
                INSERT INTO biz_dd_post_distribution 
                (submission_id, dr07, version, dr22, bdr01, bdr03, bdr09)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """,
                (
                    record["submission_id"], record["dr07"], record["version"],
                    record["dr22"], record["bdr01"], record["bdr03"], record["bdr09"]
                )
            )
        
        logger.info(f"插入了{len(test_records)}条biz_dd_post_distribution测试数据")
    
    async def test_data_validation(self):
        """测试数据验证功能"""
        logger.info("=" * 50)
        logger.info("测试场景1: 数据验证")
        logger.info("=" * 50)
        
        # 测试有效数据
        valid_data = [
            {
                "entry_id": "BACKFILL_001",
                "entry_type": "填报项",
                "DR22": ["DEPT_RISK"],
                "BDR01": ["DEPT_RISK"],
                "BDR03": ["新备注"]
            }
        ]
        
        result = await self.data_backfill.validate_backfill_data("g0107_beta_v1.0", valid_data)
        
        logger.info(f"有效数据验证结果:")
        logger.info(f"  成功: {result['success']}")
        logger.info(f"  有效数量: {result['valid_count']}/{result['total_count']}")
        
        assert result['success'], "有效数据验证应该成功"
        
        # 测试无效数据
        invalid_data = [
            {
                "entry_type": "填报项",  # 缺少entry_id
                "DR22": ["DEPT_RISK"],
                "BDR01": ["DEPT_RISK"],
                "BDR03": ["新备注"]
            },
            {
                "entry_id": "BACKFILL_002",
                "entry_type": "填报项",
                "DR22": "DEPT_RISK",  # 应该是列表格式
                "BDR01": ["DEPT_RISK"],
                "BDR03": ["新备注"]
            }
        ]
        
        result = await self.data_backfill.validate_backfill_data("g0107_beta_v1.0", invalid_data)
        
        logger.info(f"无效数据验证结果:")
        logger.info(f"  成功: {result['success']}")
        logger.info(f"  错误数量: {result['error_count']}")
        logger.info(f"  错误详情: {len(result['validation_errors'])}个错误")
        
        assert not result['success'], "无效数据验证应该失败"
        assert result['error_count'] == 2, "应该检测到2个错误"
        
        return result
    
    async def test_backfill_processing(self):
        """测试回填处理功能"""
        logger.info("=" * 50)
        logger.info("测试场景2: 回填处理")
        logger.info("=" * 50)
        
        # 创建回填数据
        backfill_data = [
            {
                "entry_id": "BACKFILL_001",
                "entry_type": "填报项",
                "DR22": ["DEPT_RISK"],
                "BDR01": ["DEPT_RISK"],
                "BDR03": ["更新后的备注"]
            }
        ]
        
        # 执行回填处理
        result = await self.data_backfill.process_backfill(
            "g0107_beta_v1.0", "义务解读", backfill_data
        )
        
        # 验证结果
        logger.info(f"回填处理结果:")
        logger.info(f"  成功: {result['success']}")
        logger.info(f"  消息: {result['message']}")
        
        if result.get('statistics'):
            stats = result['statistics']
            logger.info(f"  统计信息:")
            logger.info(f"    总数: {stats['total_count']}")
            logger.info(f"    更新数: {stats['updated_count']}")
            logger.info(f"    错误数: {stats['error_count']}")
            logger.info(f"    成功率: {stats['success_rate']:.2%}")
        
        assert result['success'], "回填处理应该成功"
        
        return result
    
    async def test_backfill_status_query(self):
        """测试回填状态查询"""
        logger.info("=" * 50)
        logger.info("测试场景3: 状态查询")
        logger.info("=" * 50)
        
        # 查询所有记录的状态
        result = await self.data_backfill.get_backfill_status("g0107_beta_v1.0")
        
        logger.info(f"状态查询结果:")
        logger.info(f"  成功: {result['success']}")
        logger.info(f"  记录数: {result['total_count']}")
        
        # 查询特定记录的状态
        result = await self.data_backfill.get_backfill_status(
            "g0107_beta_v1.0", ["BACKFILL_001"]
        )
        
        logger.info(f"特定记录查询结果:")
        logger.info(f"  成功: {result['success']}")
        logger.info(f"  记录数: {result['total_count']}")
        
        assert result['success'], "状态查询应该成功"
        
        return result
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行数据回填功能测试")
        
        try:
            # 1. 准备测试数据
            await self.prepare_test_data()
            
            # 2. 运行各种测试场景
            validation_result = await self.test_data_validation()
            processing_result = await self.test_backfill_processing()
            status_result = await self.test_backfill_status_query()
            
            # 3. 生成测试报告
            logger.info("=" * 50)
            logger.info("数据回填测试报告汇总")
            logger.info("=" * 50)
            logger.info(f"数据验证测试: {'成功' if validation_result else '失败'}")
            logger.info(f"回填处理测试: {'成功' if processing_result.get('success') else '失败'}")
            logger.info(f"状态查询测试: {'成功' if status_result.get('success') else '失败'}")
            
            logger.info("所有数据回填测试完成！")
            
        except Exception as e:
            logger.error(f"测试失败: {e}")
            raise


# 测试运行函数
async def run_data_backfill_tests(rdb_client, vdb_client=None):
    """运行数据回填测试"""
    tester = DataBackfillTester(rdb_client, vdb_client)
    await tester.run_all_tests()


if __name__ == "__main__":
    # 这里需要根据实际情况初始化数据库客户端
    # rdb_client = YourRDBClient()
    # vdb_client = YourVDBClient()
    # asyncio.run(run_data_backfill_tests(rdb_client, vdb_client))
    print("请在实际环境中运行测试，需要提供数据库客户端实例")

"""
动态模型生成器

从数据库表结构自动生成SQLAlchemy模型类
"""

import threading
from typing import Dict, Any, Optional, Type, List, Set
from sqlalchemy import MetaData, Table, inspect
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy.orm import relationship
import logging

from .base import BaseModel, create_model_from_table
from ..exceptions import ModelGenerationError, ModelCacheError, wrap_model_generation_error

logger = logging.getLogger(__name__)


class ModelCache:
    """模型缓存管理器"""
    
    def __init__(self, max_size: int = 100):
        """
        初始化模型缓存
        
        Args:
            max_size: 最大缓存大小
        """
        self.max_size = max_size
        self._cache: Dict[str, Type[BaseModel]] = {}
        self._access_order: List[str] = []
        self._lock = threading.RLock()
    
    def get(self, table_name: str) -> Optional[Type[BaseModel]]:
        """
        获取缓存的模型类
        
        Args:
            table_name: 表名
        
        Returns:
            模型类或None
        """
        with self._lock:
            if table_name in self._cache:
                # 更新访问顺序
                self._access_order.remove(table_name)
                self._access_order.append(table_name)
                return self._cache[table_name]
            return None
    
    def put(self, table_name: str, model_class: Type[BaseModel]) -> None:
        """
        缓存模型类
        
        Args:
            table_name: 表名
            model_class: 模型类
        """
        with self._lock:
            # 如果已存在，更新访问顺序
            if table_name in self._cache:
                self._access_order.remove(table_name)
                self._access_order.append(table_name)
                self._cache[table_name] = model_class
                return
            
            # 检查缓存大小
            if len(self._cache) >= self.max_size:
                # 移除最久未访问的项
                oldest = self._access_order.pop(0)
                del self._cache[oldest]
                logger.debug(f"Evicted model from cache: {oldest}")
            
            # 添加新项
            self._cache[table_name] = model_class
            self._access_order.append(table_name)
            logger.debug(f"Cached model: {table_name}")
    
    def remove(self, table_name: str) -> bool:
        """
        移除缓存的模型
        
        Args:
            table_name: 表名
        
        Returns:
            是否成功移除
        """
        with self._lock:
            if table_name in self._cache:
                del self._cache[table_name]
                self._access_order.remove(table_name)
                logger.debug(f"Removed model from cache: {table_name}")
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_order.clear()
            logger.debug("Cleared model cache")
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)
    
    def get_cached_tables(self) -> Set[str]:
        """获取所有缓存的表名"""
        with self._lock:
            return set(self._cache.keys())


class DynamicModelGenerator:
    """动态模型生成器"""
    
    def __init__(
        self,
        engine: Engine,
        cache_size: int = 100,
        include_views: bool = False,
        table_prefix_filter: Optional[str] = None,
        table_suffix_filter: Optional[str] = None,
        excluded_tables: Optional[List[str]] = None,
        use_independent_base: bool = True
    ):
        """
        初始化动态模型生成器

        Args:
            engine: SQLAlchemy引擎
            cache_size: 模型缓存大小
            include_views: 是否包含视图
            table_prefix_filter: 表名前缀过滤器
            table_suffix_filter: 表名后缀过滤器
            excluded_tables: 排除的表名列表
            use_independent_base: 是否使用独立的declarative base
        """
        self.engine = engine
        self.metadata = MetaData()
        self.cache = ModelCache(cache_size)
        self.include_views = include_views
        self.table_prefix_filter = table_prefix_filter
        self.table_suffix_filter = table_suffix_filter
        self.excluded_tables = set(excluded_tables or [])

        # 创建实例级别的declarative base
        self._use_independent_base = use_independent_base
        if use_independent_base:
            from .base import InstanceBaseModel
            self._instance_base, self._base_model_class = InstanceBaseModel.create_instance_base()
            logger.debug("Created independent declarative base for this instance")
        else:
            from .base import Base, BaseModel
            self._instance_base = Base
            self._base_model_class = BaseModel
            logger.debug("Using global declarative base")

        # 反射锁
        self._reflection_lock = threading.RLock()
        self._reflected_tables: Set[str] = set()
    
    def _should_include_table(self, table_name: str) -> bool:
        """
        检查是否应该包含指定表
        
        Args:
            table_name: 表名
        
        Returns:
            是否包含
        """
        # 检查排除列表
        if table_name in self.excluded_tables:
            return False
        
        # 检查前缀过滤器
        if self.table_prefix_filter and not table_name.startswith(self.table_prefix_filter):
            return False
        
        # 检查后缀过滤器
        if self.table_suffix_filter and not table_name.endswith(self.table_suffix_filter):
            return False
        
        return True
    
    def _reflect_table(self, table_name: str) -> Table:
        """
        反射单个表
        
        Args:
            table_name: 表名
        
        Returns:
            SQLAlchemy Table对象
        """
        with self._reflection_lock:
            if table_name not in self._reflected_tables:
                try:
                    # 反射表结构
                    table = Table(
                        table_name,
                        self.metadata,
                        autoload_with=self.engine,
                        extend_existing=True
                    )
                    self._reflected_tables.add(table_name)
                    logger.debug(f"Reflected table: {table_name}")
                    return table
                except Exception as e:
                    raise wrap_model_generation_error(
                        e, table_name, {'operation': 'table_reflection'}
                    )
            else:
                return self.metadata.tables[table_name]
    
    def generate_model(self, table_name: str, force_refresh: bool = False) -> Type[BaseModel]:
        """
        为指定表生成模型类
        
        Args:
            table_name: 表名
            force_refresh: 是否强制刷新缓存
        
        Returns:
            动态生成的模型类
        """
        # 检查是否应该包含此表
        if not self._should_include_table(table_name):
            raise ModelGenerationError(f"Table '{table_name}' is excluded by filters")
        
        # 检查缓存
        if not force_refresh:
            cached_model = self.cache.get(table_name)
            if cached_model:
                logger.debug(f"Using cached model for table: {table_name}")
                return cached_model
        
        try:
            # 反射表结构
            table = self._reflect_table(table_name)
            
            # 生成模型类（使用实例级别的BaseModel）
            if self._use_independent_base:
                model_class = self._base_model_class.create_model_from_table(table_name, table)
            else:
                model_class = create_model_from_table(table_name, table)
            
            # 缓存模型类
            self.cache.put(table_name, model_class)
            
            logger.info(f"Generated dynamic model for table: {table_name}")
            return model_class
            
        except Exception as e:
            raise wrap_model_generation_error(e, table_name)
    
    def generate_all_models(self, force_refresh: bool = False) -> Dict[str, Type[BaseModel]]:
        """
        为所有表生成模型类
        
        Args:
            force_refresh: 是否强制刷新缓存
        
        Returns:
            表名到模型类的映射字典
        """
        try:
            # 获取所有表名
            inspector = inspect(self.engine)
            table_names = inspector.get_table_names()
            
            # 如果包含视图，也获取视图名
            if self.include_views:
                view_names = inspector.get_view_names()
                table_names.extend(view_names)
            
            models = {}
            for table_name in table_names:
                if self._should_include_table(table_name):
                    try:
                        model_class = self.generate_model(table_name, force_refresh)
                        models[table_name] = model_class
                    except Exception as e:
                        logger.error(f"Failed to generate model for table {table_name}: {e}")
                        # 继续处理其他表
                        continue
            
            logger.info(f"Generated {len(models)} dynamic models")
            return models
            
        except Exception as e:
            raise ModelGenerationError(f"Failed to generate all models: {e}", e)
    
    def refresh_model(self, table_name: str) -> Type[BaseModel]:
        """
        刷新指定表的模型

        Args:
            table_name: 表名

        Returns:
            刷新后的模型类
        """
        # 清除缓存
        self.cache.remove(table_name)

        # 清除反射缓存
        with self._reflection_lock:
            if table_name in self._reflected_tables:
                self._reflected_tables.remove(table_name)
                if table_name in self.metadata.tables:
                    self.metadata.remove(self.metadata.tables[table_name])

        # 清除declarative base中的类注册（避免SAWarning）
        self._clear_declarative_class(table_name)

        # 重新生成
        return self.generate_model(table_name, force_refresh=True)

    def _clear_declarative_class(self, table_name: str) -> None:
        """
        清除declarative base中的类注册，避免SAWarning

        Args:
            table_name: 表名
        """
        try:
            # 生成类名（与create_model_from_table中的逻辑一致）
            class_name = ''.join(word.capitalize() for word in table_name.replace('_', ' ').split())
            if not class_name.endswith('Model'):
                class_name += 'Model'

            # 清理实例级别的注册表
            if self._use_independent_base and hasattr(self._base_model_class, 'registry'):
                registry = self._base_model_class.registry
            else:
                from .base import BaseModel
                registry = BaseModel.registry if hasattr(BaseModel, 'registry') else None

            if registry is None:
                return

            # 只清理_class_registry，避免操作复杂的内部结构
            if hasattr(registry, '_class_registry') and hasattr(registry._class_registry, 'get'):
                class_registry = registry._class_registry

                # 尝试各种可能的类名格式
                possible_names = [
                    class_name,
                    f"base.db.implementations.rdb.orm.models.base.{class_name}",
                ]

                for name in possible_names:
                    if name in class_registry:
                        try:
                            del class_registry[name]
                            logger.debug(f"Cleared declarative class: {name}")
                            break
                        except (TypeError, AttributeError):
                            # 如果是frozenset或其他不可变类型，跳过
                            continue

        except Exception as e:
            # 如果清理失败，记录调试信息但不影响主流程
            logger.debug(f"Failed to clear declarative class for {table_name}: {e}")

    def get_available_tables(self) -> List[str]:
        """
        获取所有可用的表名
        
        Returns:
            表名列表
        """
        try:
            inspector = inspect(self.engine)
            table_names = inspector.get_table_names()
            
            if self.include_views:
                view_names = inspector.get_view_names()
                table_names.extend(view_names)
            
            return [name for name in table_names if self._should_include_table(name)]
            
        except Exception as e:
            raise ModelGenerationError(f"Failed to get available tables: {e}", e)

    def clear_cache(self) -> None:
        """清空所有缓存"""
        # 获取所有缓存的表名，用于清理declarative base
        cached_tables = self.cache.get_cached_tables()

        self.cache.clear()
        with self._reflection_lock:
            self._reflected_tables.clear()
            self.metadata.clear()

        # 清理declarative base中的所有类注册
        for table_name in cached_tables:
            self._clear_declarative_class(table_name)

        logger.info("Cleared all model generator caches")

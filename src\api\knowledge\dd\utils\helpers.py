"""
DD系统工具函数

提供DD系统API的辅助工具函数，包括：
- 响应格式化
- 数据验证
- 分页处理
- 通用工具
"""

import re
import uuid
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime


def format_response(success: bool, message: str, data: Any = None) -> Dict[str, Any]:
    """
    格式化API响应
    
    Args:
        success: 操作是否成功
        message: 响应消息
        data: 响应数据
        
    Returns:
        Dict[str, Any]: 格式化的响应数据
    """
    response = {
        "success": success,
        "message": message,
        "timestamp": datetime.now().isoformat()
    }
    
    if data is not None:
        response["data"] = data
        
    return response


def validate_uuid(value: str) -> bool:
    """
    验证UUID格式
    
    Args:
        value: 待验证的字符串
        
    Returns:
        bool: 是否为有效的UUID
    """
    try:
        uuid.UUID(value)
        return True
    except ValueError:
        return False


def validate_submission_id(submission_id: str) -> bool:
    """
    验证填报ID格式
    
    Args:
        submission_id: 填报ID
        
    Returns:
        bool: 是否为有效的填报ID格式
    """
    # 填报ID格式：字母开头，可包含字母、数字、下划线，长度3-50
    pattern = r'^[A-Za-z][A-Za-z0-9_]{2,49}$'
    return bool(re.match(pattern, submission_id))


def validate_dept_id(dept_id: str) -> bool:
    """
    验证部门ID格式
    
    Args:
        dept_id: 部门ID
        
    Returns:
        bool: 是否为有效的部门ID格式
    """
    # 部门ID格式：字母开头，可包含字母、数字、下划线，长度3-30
    pattern = r'^[A-Za-z][A-Za-z0-9_]{2,29}$'
    return bool(re.match(pattern, dept_id))


def paginate_results(
    items: List[Any], 
    page: int, 
    page_size: int
) -> Tuple[List[Any], Dict[str, Any]]:
    """
    对结果进行分页处理
    
    Args:
        items: 原始数据列表
        page: 页码（从1开始）
        page_size: 每页数量
        
    Returns:
        Tuple[List[Any], Dict[str, Any]]: (分页后的数据, 分页信息)
    """
    total = len(items)
    total_pages = (total + page_size - 1) // page_size
    
    start_index = (page - 1) * page_size
    end_index = start_index + page_size
    
    paginated_items = items[start_index:end_index]
    
    pagination_info = {
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": total_pages,
        "has_next": page < total_pages,
        "has_prev": page > 1
    }
    
    return paginated_items, pagination_info


def clean_text(text: str) -> str:
    """
    清理文本内容
    
    Args:
        text: 原始文本
        
    Returns:
        str: 清理后的文本
    """
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 移除特殊字符（保留中文、英文、数字、常用标点）
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】""''，。；：！？]', '', text)
    
    return text


def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """
    从文本中提取关键词
    
    Args:
        text: 输入文本
        max_keywords: 最大关键词数量
        
    Returns:
        List[str]: 关键词列表
    """
    if not text:
        return []
    
    # 简单的关键词提取（基于长度和频率）
    words = re.findall(r'[\u4e00-\u9fa5a-zA-Z]{2,}', text)
    
    # 统计词频
    word_count = {}
    for word in words:
        word_count[word] = word_count.get(word, 0) + 1
    
    # 按频率排序并返回前N个
    sorted_words = sorted(word_count.items(), key=lambda x: x[1], reverse=True)
    keywords = [word for word, count in sorted_words[:max_keywords]]
    
    return keywords


def calculate_similarity_score(text1: str, text2: str) -> float:
    """
    计算两个文本的相似度分数
    
    Args:
        text1: 文本1
        text2: 文本2
        
    Returns:
        float: 相似度分数（0-1）
    """
    if not text1 or not text2:
        return 0.0
    
    # 简单的基于关键词重叠的相似度计算
    keywords1 = set(extract_keywords(text1))
    keywords2 = set(extract_keywords(text2))
    
    if not keywords1 or not keywords2:
        return 0.0
    
    intersection = keywords1.intersection(keywords2)
    union = keywords1.union(keywords2)
    
    return len(intersection) / len(union) if union else 0.0


def format_datetime(dt: Optional[datetime]) -> Optional[str]:
    """
    格式化日期时间
    
    Args:
        dt: 日期时间对象
        
    Returns:
        Optional[str]: 格式化的日期时间字符串
    """
    if dt is None:
        return None
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def parse_version(version: str) -> Tuple[int, int, int]:
    """
    解析版本号
    
    Args:
        version: 版本字符串（如：2024-01-01）
        
    Returns:
        Tuple[int, int, int]: (年, 月, 日)
    """
    try:
        parts = version.split('-')
        if len(parts) == 3:
            return int(parts[0]), int(parts[1]), int(parts[2])
    except ValueError:
        pass
    
    # 默认返回当前日期
    now = datetime.now()
    return now.year, now.month, now.day


def generate_submission_id(prefix: str = "SUB") -> str:
    """
    生成填报ID
    
    Args:
        prefix: ID前缀
        
    Returns:
        str: 生成的填报ID
    """
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    random_suffix = str(uuid.uuid4())[:8].upper()
    return f"{prefix}_{timestamp}_{random_suffix}"


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除不安全字符
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 清理后的文件名
    """
    # 移除或替换不安全字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    filename = re.sub(r'\s+', '_', filename)
    
    # 限制长度
    if len(filename) > 100:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        filename = name[:95] + ('.' + ext if ext else '')
    
    return filename


def build_search_filters(
    data_layer: Optional[str] = None,
    version: Optional[str] = None,
    dept_id: Optional[str] = None,
    knowledge_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    构建搜索过滤条件
    
    Args:
        data_layer: 数据层
        version: 版本
        dept_id: 部门ID
        knowledge_id: 知识库ID
        
    Returns:
        Dict[str, Any]: 过滤条件字典
    """
    filters = {}
    
    if data_layer:
        filters["data_layer"] = data_layer
    if version:
        filters["version"] = version
    if dept_id:
        filters["dept_id"] = dept_id
    if knowledge_id:
        filters["knowledge_id"] = knowledge_id
    
    return filters

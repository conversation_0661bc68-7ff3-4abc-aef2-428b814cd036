"""
查询执行器

提供查询执行的统一接口，支持同步和异步操作
"""

from typing import Dict, Any, List, Optional, Type, Union
import logging

from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, insert, update, delete, func

from ..models.base import BaseModel
from ..session.manager import SessionManager, AsyncSessionManager
from ..exceptions import QueryError, wrap_orm_error

logger = logging.getLogger(__name__)


class QueryExecutor:
    """同步查询执行器"""
    
    def __init__(self, session_manager: SessionManager):
        """
        初始化查询执行器
        
        Args:
            session_manager: 会话管理器
        """
        self.session_manager = session_manager
    
    def execute_select(
        self,
        model_class: Type[BaseModel],
        columns: Optional[List[str]] = None,
        where_conditions: Optional[List[Any]] = None,
        order_by: Optional[List[Any]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        distinct: bool = False
    ) -> List[Dict[str, Any]]:
        """
        执行SELECT查询
        
        Args:
            model_class: 模型类
            columns: 选择的列
            where_conditions: WHERE条件列表
            order_by: ORDER BY子句列表
            limit: LIMIT值
            offset: OFFSET值
            distinct: 是否DISTINCT
        
        Returns:
            查询结果列表
        """
        try:
            with self.session_manager.get_session() as session:
                # 构建查询
                if columns and columns != ["*"]:
                    column_attrs = []
                    for col in columns:
                        if hasattr(model_class, col):
                            column_attrs.append(getattr(model_class, col))
                    
                    if column_attrs:
                        query = session.query(*column_attrs)
                    else:
                        query = session.query(model_class)
                else:
                    query = session.query(model_class)
                
                # 应用DISTINCT
                if distinct:
                    query = query.distinct()
                
                # 应用WHERE条件
                if where_conditions:
                    from sqlalchemy import and_
                    query = query.filter(and_(*where_conditions))
                
                # 应用ORDER BY
                if order_by:
                    query = query.order_by(*order_by)
                
                # 应用LIMIT和OFFSET
                if limit is not None:
                    query = query.limit(limit)
                if offset is not None:
                    query = query.offset(offset)
                
                # 执行查询
                results = query.all()
                
                # 转换结果
                if columns and columns != ["*"] and len(columns) == 1:
                    return [{columns[0]: result} for result in results]
                elif columns and columns != ["*"]:
                    return [dict(zip(columns, result)) for result in results]
                else:
                    return [result.to_dict() for result in results]
                    
        except Exception as e:
            raise wrap_orm_error(e, "execute select query")
    
    def execute_insert(
        self,
        model_class: Type[BaseModel],
        data: Union[Dict[str, Any], List[Dict[str, Any]]]
    ) -> Union[Any, List[Any]]:
        """
        执行INSERT操作
        
        Args:
            model_class: 模型类
            data: 插入数据
        
        Returns:
            插入的主键值
        """
        try:
            with self.session_manager.get_session() as session:
                if isinstance(data, dict):
                    # 单行插入
                    instance = model_class(**data)
                    session.add(instance)
                    session.commit()
                    
                    pk_values = instance.get_primary_key_values()
                    return list(pk_values.values())[0] if len(pk_values) == 1 else pk_values
                else:
                    # 批量插入
                    instances = [model_class(**row) for row in data]
                    session.add_all(instances)
                    session.commit()
                    
                    results = []
                    for instance in instances:
                        pk_values = instance.get_primary_key_values()
                        results.append(list(pk_values.values())[0] if len(pk_values) == 1 else pk_values)
                    
                    return results
                    
        except Exception as e:
            raise wrap_orm_error(e, "execute insert")
    
    def execute_update(
        self,
        model_class: Type[BaseModel],
        values: Dict[str, Any],
        where_conditions: List[Any]
    ) -> int:
        """
        执行UPDATE操作
        
        Args:
            model_class: 模型类
            values: 更新值
            where_conditions: WHERE条件列表
        
        Returns:
            影响的行数
        """
        try:
            with self.session_manager.get_session() as session:
                query = session.query(model_class)
                
                if where_conditions:
                    from sqlalchemy import and_
                    query = query.filter(and_(*where_conditions))
                
                affected_rows = query.update(values)
                session.commit()
                
                return affected_rows
                
        except Exception as e:
            raise wrap_orm_error(e, "execute update")
    
    def execute_delete(
        self,
        model_class: Type[BaseModel],
        where_conditions: List[Any]
    ) -> int:
        """
        执行DELETE操作
        
        Args:
            model_class: 模型类
            where_conditions: WHERE条件列表
        
        Returns:
            影响的行数
        """
        try:
            with self.session_manager.get_session() as session:
                query = session.query(model_class)
                
                if where_conditions:
                    from sqlalchemy import and_
                    query = query.filter(and_(*where_conditions))
                
                affected_rows = query.delete()
                session.commit()
                
                return affected_rows
                
        except Exception as e:
            raise wrap_orm_error(e, "execute delete")
    
    def execute_count(
        self,
        model_class: Type[BaseModel],
        where_conditions: Optional[List[Any]] = None
    ) -> int:
        """
        执行COUNT查询
        
        Args:
            model_class: 模型类
            where_conditions: WHERE条件列表
        
        Returns:
            记录数量
        """
        try:
            with self.session_manager.get_session() as session:
                query = session.query(model_class)
                
                if where_conditions:
                    from sqlalchemy import and_
                    query = query.filter(and_(*where_conditions))
                
                return query.count()
                
        except Exception as e:
            raise wrap_orm_error(e, "execute count")


class AsyncQueryExecutor:
    """异步查询执行器"""
    
    def __init__(self, session_manager: AsyncSessionManager):
        """
        初始化异步查询执行器
        
        Args:
            session_manager: 异步会话管理器
        """
        self.session_manager = session_manager
    
    async def execute_select(
        self,
        model_class: Type[BaseModel],
        columns: Optional[List[str]] = None,
        where_conditions: Optional[List[Any]] = None,
        order_by: Optional[List[Any]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        distinct: bool = False
    ) -> List[Dict[str, Any]]:
        """
        执行异步SELECT查询
        
        Args:
            model_class: 模型类
            columns: 选择的列
            where_conditions: WHERE条件列表
            order_by: ORDER BY子句列表
            limit: LIMIT值
            offset: OFFSET值
            distinct: 是否DISTINCT
        
        Returns:
            查询结果列表
        """
        try:
            async with self.session_manager.get_session() as session:
                # 构建查询
                if columns and columns != ["*"]:
                    column_attrs = []
                    for col in columns:
                        if hasattr(model_class, col):
                            column_attrs.append(getattr(model_class, col))
                    
                    if column_attrs:
                        stmt = select(*column_attrs)
                    else:
                        stmt = select(model_class)
                else:
                    stmt = select(model_class)
                
                # 应用DISTINCT
                if distinct:
                    stmt = stmt.distinct()
                
                # 应用WHERE条件
                if where_conditions:
                    from sqlalchemy import and_
                    stmt = stmt.where(and_(*where_conditions))
                
                # 应用ORDER BY
                if order_by:
                    stmt = stmt.order_by(*order_by)
                
                # 应用LIMIT和OFFSET
                if limit is not None:
                    stmt = stmt.limit(limit)
                if offset is not None:
                    stmt = stmt.offset(offset)
                
                # 执行查询
                result = await session.execute(stmt)
                
                # 转换结果
                if columns and columns != ["*"] and len(columns) == 1:
                    rows = result.scalars().all()
                    return [{columns[0]: row} for row in rows]
                elif columns and columns != ["*"]:
                    rows = result.all()
                    return [dict(zip(columns, row)) for row in rows]
                else:
                    rows = result.scalars().all()
                    return [row.to_dict() for row in rows]
                    
        except Exception as e:
            raise wrap_orm_error(e, "execute async select query")
    
    async def execute_insert(
        self,
        model_class: Type[BaseModel],
        data: Union[Dict[str, Any], List[Dict[str, Any]]]
    ) -> Union[Any, List[Any]]:
        """
        执行异步INSERT操作
        
        Args:
            model_class: 模型类
            data: 插入数据
        
        Returns:
            插入的主键值
        """
        try:
            async with self.session_manager.get_session() as session:
                if isinstance(data, dict):
                    # 单行插入
                    instance = model_class(**data)
                    session.add(instance)
                    await session.commit()
                    
                    pk_values = instance.get_primary_key_values()
                    return list(pk_values.values())[0] if len(pk_values) == 1 else pk_values
                else:
                    # 批量插入
                    instances = [model_class(**row) for row in data]
                    session.add_all(instances)
                    await session.commit()
                    
                    results = []
                    for instance in instances:
                        pk_values = instance.get_primary_key_values()
                        results.append(list(pk_values.values())[0] if len(pk_values) == 1 else pk_values)
                    
                    return results
                    
        except Exception as e:
            raise wrap_orm_error(e, "execute async insert")

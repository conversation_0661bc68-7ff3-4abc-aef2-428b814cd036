# DD-B模块

DD提交流程下一步处理模块，负责基于历史数据的智能生成逻辑，通过向量搜索历史沉淀信息来决定数据生成策略。

## 功能特性

### 核心功能
- **数据查询**: 从 `biz_dd_post` 表查询指定 `report_code` 和 `dept_id` 的记录
- **字段检查**: 检查 BDR09、BDR10、BDR11、BDR16 主要字段的完整性
- **历史信息提取**: 通过 `pre_distribution_id` 关联查询历史信息
- **向量搜索**: 在 `dd_submission_data` 表中进行向量化搜索
- **智能决策**: 根据搜索结果置信度自动选择处理策略
- **自动填充**: 根据业务规则自动填充空字段
- **数据验证**: 提供完整的数据格式和业务规则验证

### 🆕 历史连接器功能
- **历史信息提取**: 从 `biz_dd_pre_distribution` 表提取 `dr09` 和 `dr17` 字段
- **向量搜索**: 使用提取的信息在历史数据中搜索相似记录
- **置信度判断**: 根据搜索相似度（60%阈值）决定处理策略
- **三种策略**: 直接返回、高置信度填充、低置信度生成

### 填充规则
- **主要字段检查**: BDR09、BDR10、BDR11、BDR16
- **第一组填充字段**: brd06、brd07、brd08
  - brd06: "不适用"
  - brd07: "1"
  - brd08: "CRD"
- **第二组填充字段**: BDR12、BDR13、BDR14、BDR15、brd17
  - 统一填充为: "不适用"

### 🔄 处理逻辑（新增历史连接器）

#### 策略A: 直接返回策略
- **触发条件**: 主要字段（BDR09、BDR10、BDR11、BDR16）全部不为空
- **处理方式**: 直接返回所有BDR字段数据
- **返回字段**: BDR05, BDR06, BDR07, BDR08, BDR09, BDR10, BDR11, BDR12, BDR13, BDR14, BDR15, BDR16, BDR17

#### 策略B: 高置信度策略
- **触发条件**: 主要字段不完整 + 向量搜索相似度 ≥ 60%
- **处理方式**: 完全使用历史数据填充所有BDR字段
- **数据来源**: 最佳匹配的历史记录

#### 策略C: 低置信度策略
- **触发条件**: 主要字段不完整 + 向量搜索相似度 < 60%
- **处理方式**:
  1. 标记需要重新生成的字段: BDR09, BDR10, BDR11, BDR16
  2. 其他字段按默认规则填充

#### 传统填充策略
- **触发条件**: 历史连接器未启用或历史信息提取失败
- **处理方式**: 按原有DD-B模块的填充规则处理

## 快速开始

### 基本使用

```python
import asyncio
from service import get_client
from modules.dd_submission.dd_b import DDBProcessor

async def basic_example():
    # 1. 获取数据库客户端
    rdb_client = await get_client("database.rdbs.mysql")
    
    # 2. 创建DD-B处理器
    processor = DDBProcessor(rdb_client)
    
    # 3. 处理数据
    result = await processor.process_simple(
        report_code="v1.0",  # 现在直接是版本号
        dept_id="DEPT_FINANCE"
    )
    
    # 4. 获取处理结果
    print(f"处理状态: {result.status}")
    print(f"找到记录: {result.total_records_found} 条")
    print(f"填充字段: {result.total_fields_filled} 个")
    
    # 5. 获取处理后的数据
    processed_records = result.processed_records
    for record in processed_records:
        print(f"记录ID: {record.id}")
        print(f"BDR09: {record.BDR09}")
        print(f"brd06: {record.brd06}")

asyncio.run(basic_example())
```

### 数据验证

```python
from modules.dd_submission.dd_b import DDBValidator, DDBRecord

async def validation_example():
    # 1. 创建验证器
    validator = DDBValidator()
    
    # 2. 创建测试记录
    records = [
        DDBRecord(
            report_code="g0107_beta_v1.0",
            dept_id="DEPT_FINANCE",
            BDR09="客户姓名",
            BDR10="客户信息"
        )
    ]
    
    # 3. 执行验证
    result = await validator.validate_simple(
        "g0107_beta_v1.0",
        "DEPT_FINANCE",
        records
    )
    
    # 4. 检查验证结果
    if result.is_valid:
        print("数据验证通过")
    else:
        print(f"验证失败: {result.validation_errors}")

asyncio.run(validation_example())
```

## API参考

### DDBProcessor

主要的数据处理接口：

```python
class DDBProcessor:
    async def process_simple(self, report_code: str, dept_id: str, **kwargs) -> DDBProcessResult:
        """简化的处理接口"""
        
    async def get_records_summary(self, report_code: str, dept_id: str) -> Dict[str, Any]:
        """获取记录摘要信息"""
        
    async def check_data_completeness(self, report_code: str, dept_id: str) -> Dict[str, Any]:
        """检查数据完整性"""
        
    async def get_health_status(self) -> Dict[str, Any]:
        """获取处理器健康状态"""
```

### DDBValidator

数据验证接口：

```python
class DDBValidator:
    async def validate_simple(self, report_code: str, dept_id: str, records: List[DDBRecord], **kwargs) -> DDBValidationResult:
        """简化的验证接口"""
        
    async def quick_validate(self, records: List[DDBRecord]) -> Dict[str, Any]:
        """快速验证"""
```

### 数据模型

#### DDBProcessRequest
```python
@dataclass
class DDBProcessRequest:
    report_code: str                    # 报表代码（现在直接是版本）
    dept_id: str                       # 部门ID
    enable_auto_fill: bool = True      # 启用自动填充
    validate_before_fill: bool = True  # 填充前验证
    return_original_data: bool = False # 是否返回原始数据
```

#### DDBRecord
```python
@dataclass
class DDBRecord:
    # 主要检查字段
    BDR09: Optional[str] = None
    BDR10: Optional[str] = None
    BDR11: Optional[str] = None
    BDR16: Optional[str] = None
    
    # 需要填充的字段
    brd06: Optional[str] = None
    brd07: Optional[str] = None
    brd08: Optional[str] = None
    BDR12: Optional[str] = None
    BDR13: Optional[str] = None
    BDR14: Optional[str] = None
    BDR15: Optional[str] = None
    brd17: Optional[str] = None
```

## 运行测试

### 使用模拟客户端测试
```bash
cd src/modules/dd_submission/dd_b
python tests/run_tests.py --use-mock
```

### 使用真实数据库测试
```bash
cd src/modules/dd_submission/dd_b
python tests/run_tests.py --db-host localhost --db-port 3306 --db-name hsbc_knowledge --db-user root --db-password password
```

### 运行单元测试
```bash
cd src/modules/dd_submission/dd_b
python -m pytest tests/test_dd_b_processor.py -v
```

## 技术实现

### 模块结构
```
dd_b/
├── dd_b_processor.py          # 🎯 主接口：数据处理
├── dd_b_validator.py          # 🎯 主接口：数据验证
├── __init__.py               # 模块初始化
├── core/                     # 🔧 核心业务逻辑
│   ├── data_processor.py     # 数据处理引擎
│   └── field_filler.py       # 字段填充引擎
├── infrastructure/           # 🏗️ 基础设施组件
│   ├── models.py            # 数据模型
│   ├── constants.py         # 常量定义
│   └── exceptions.py        # 异常处理
└── tests/                   # 🧪 测试文件
    ├── test_dd_b_processor.py
    └── run_tests.py
```

### 设计原则
- **简洁的API**: 只暴露两个主要接口文件
- **清晰的架构**: 核心逻辑与基础设施分离
- **完整的错误处理**: 统一的异常处理机制
- **全面的测试**: 单元测试和集成测试覆盖

### 性能特性
- **批量处理**: 支持批量记录处理
- **异步操作**: 全异步API设计
- **错误恢复**: 智能的错误恢复策略
- **日志记录**: 完整的操作日志

## 配置说明

### 常量配置
```python
class DDBConstants:
    TABLE_NAME = "biz_dd_post"
    MAIN_CHECK_FIELDS = ["BDR09", "BDR10", "BDR11", "BDR16"]
    FILL_GROUP_1_FIELDS = ["brd06", "brd07", "brd08"]
    FILL_GROUP_2_FIELDS = ["BDR12", "BDR13", "BDR14", "BDR15", "brd17"]
    
    DEFAULT_FILL_VALUES = {
        "brd06": "不适用",
        "brd07": "1",
        "brd08": "CRD",
        "BDR12": "不适用",
        "BDR13": "不适用",
        "BDR14": "不适用",
        "BDR15": "不适用",
        "brd17": "不适用"
    }
```

## 注意事项

1. **数据库依赖**: 需要MySQL数据库服务
2. **表结构**: 依赖 `biz_dd_post` 表的存在
3. **字段映射**: 确保数据库字段与模型字段一致
4. **版本处理**: report_code现在直接是版本号，会自动添加前缀
5. **错误处理**: 建议在生产环境中添加适当的错误监控

## 版本信息

- **版本**: 1.0.0
- **依赖**: MySQL数据库客户端
- **Python**: 3.8+
- **测试状态**: ✅ 基本功能测试完成
- **部署状态**: ✅ 可用于测试环境

"""
DD系统报表数据管理API路由

提供报表数据的完整CRUD操作接口，包括：
- 创建报表数据
- 获取报表数据详情
- 更新报表数据
- 删除报表数据
- 查询报表数据列表
"""

from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from loguru import logger

from api.knowledge.models.response_models import (
    ListResponse, CreateResponse, UpdateResponse, DeleteResponse, DetailResponse
)
from ..models.requests import ReportDataCreateRequest, ReportDataUpdateRequest
from ..models.responses import ReportDataResponse
from ..models.enums import DataLayerEnum
from ..dependencies.common import get_dd_crud, validate_pagination
from ..utils.helpers import format_response

# 创建路由器
router = APIRouter(tags=["DD报表数据管理"], prefix="/reports")


@router.post("/", response_model=CreateResponse, summary="创建报表数据")
async def create_report_data(
    request: ReportDataCreateRequest,
    dd_crud = Depends(get_dd_crud)
):
    """
    创建新的报表数据
    
    - **knowledge_id**: 知识库ID，必须唯一
    - **version**: 版本号
    - **report_name**: 报表名称
    - **report_code**: 报表代码
    - **report_layer**: 报表层级（ADM/BDM/ADS/ODS）
    - **report_desc**: 报表描述（可选）
    """
    try:
        report_data = request.model_dump()
        report_id = await dd_crud.create_report_data(report_data)
        
        return CreateResponse(
            success=True,
            message="报表数据创建成功",
            data={"report_id": report_id}
        )
    except Exception as e:
        logger.error(f"创建报表数据失败: {e}")
        raise HTTPException(status_code=400, detail=f"创建报表数据失败: {str(e)}")


@router.get("/{report_id}", response_model=DetailResponse, summary="获取报表数据详情")
async def get_report_data(
    report_id: int,
    dd_crud = Depends(get_dd_crud)
):
    """
    根据ID获取报表数据详情
    
    - **report_id**: 报表数据ID
    """
    try:
        report = await dd_crud.get_report_data(report_id)
        
        if not report:
            raise HTTPException(status_code=404, detail=f"报表数据不存在: {report_id}")
        
        return DetailResponse(
            success=True,
            message="获取报表数据详情成功",
            data=report
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取报表数据详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取报表数据详情失败: {str(e)}")


@router.put("/{report_id}", response_model=UpdateResponse, summary="更新报表数据")
async def update_report_data(
    report_id: int,
    request: ReportDataUpdateRequest,
    dd_crud = Depends(get_dd_crud)
):
    """
    更新报表数据
    
    - **report_id**: 报表数据ID
    - **report_name**: 报表名称（可选）
    - **report_code**: 报表代码（可选）
    - **report_layer**: 报表层级（可选）
    - **report_desc**: 报表描述（可选）
    """
    try:
        # 检查报表数据是否存在
        existing_report = await dd_crud.get_report_data(report_id)
        if not existing_report:
            raise HTTPException(status_code=404, detail=f"报表数据不存在: {report_id}")
        
        # 只更新非空字段
        update_data = {k: v for k, v in request.model_dump().items() if v is not None}
        
        if not update_data:
            raise HTTPException(status_code=400, detail="没有提供要更新的字段")
        
        success = await dd_crud.update_report_data(report_id, update_data)
        
        if not success:
            raise HTTPException(status_code=400, detail="更新报表数据失败")
        
        return UpdateResponse(
            success=True,
            message="报表数据更新成功",
            data={"report_id": report_id}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新报表数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新报表数据失败: {str(e)}")


@router.delete("/{report_id}", response_model=DeleteResponse, summary="删除报表数据")
async def delete_report_data(
    report_id: int,
    dd_crud = Depends(get_dd_crud)
):
    """
    删除报表数据
    
    - **report_id**: 报表数据ID
    """
    try:
        # 检查报表数据是否存在
        existing_report = await dd_crud.get_report_data(report_id)
        if not existing_report:
            raise HTTPException(status_code=404, detail=f"报表数据不存在: {report_id}")
        
        success = await dd_crud.delete_report_data(report_id)
        
        if not success:
            raise HTTPException(status_code=400, detail="删除报表数据失败")
        
        return DeleteResponse(
            success=True,
            message="报表数据删除成功",
            data={"report_id": report_id}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除报表数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除报表数据失败: {str(e)}")


@router.get("/", response_model=ListResponse, summary="查询报表数据列表")
async def list_report_data(
    report_layer: Optional[DataLayerEnum] = Query(None, description="报表层级过滤"),
    version: Optional[str] = Query(None, description="版本过滤"),
    knowledge_id: Optional[str] = Query(None, description="知识库ID过滤"),
    pagination = Depends(validate_pagination),
    dd_crud = Depends(get_dd_crud)
):
    """
    查询报表数据列表，支持分页和过滤
    
    - **report_layer**: 报表层级过滤（可选）
    - **version**: 版本过滤（可选）
    - **knowledge_id**: 知识库ID过滤（可选）
    - **page**: 页码（默认1）
    - **page_size**: 每页数量（默认20，最大100）
    """
    try:
        page, page_size, offset = pagination
        
        # 构建查询参数
        filters = {}
        if report_layer is not None:
            filters["report_layer"] = report_layer.value
        if version is not None:
            filters["version"] = version
        if knowledge_id is not None:
            filters["knowledge_id"] = knowledge_id
        
        # 查询报表数据列表
        reports = await dd_crud.list_report_data(
            **filters,
            limit=page_size,
            offset=offset
        )
        
        # 查询总数
        total_reports = await dd_crud.list_report_data(**filters)
        total = len(total_reports)
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        return ListResponse(
            success=True,
            message="查询报表数据列表成功",
            data={
                "items": reports,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }
        )
    except Exception as e:
        logger.error(f"查询报表数据列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询报表数据列表失败: {str(e)}")


@router.get("/by-knowledge/{knowledge_id}", response_model=ListResponse, summary="根据知识库ID查询报表")
async def get_reports_by_knowledge_id(
    knowledge_id: str,
    version: Optional[str] = Query(None, description="版本过滤"),
    dd_crud = Depends(get_dd_crud)
):
    """
    根据知识库ID查询相关的报表数据
    
    - **knowledge_id**: 知识库ID
    - **version**: 版本过滤（可选）
    """
    try:
        # 构建查询参数
        filters = {"knowledge_id": knowledge_id}
        if version is not None:
            filters["version"] = version
        
        # 查询报表数据
        reports = await dd_crud.list_report_data(**filters)
        
        return ListResponse(
            success=True,
            message=f"查询知识库 {knowledge_id} 的报表数据成功",
            data={
                "items": reports,
                "total": len(reports),
                "knowledge_id": knowledge_id,
                "version": version
            }
        )
    except Exception as e:
        logger.error(f"根据知识库ID查询报表失败: {e}")
        raise HTTPException(status_code=500, detail=f"根据知识库ID查询报表失败: {str(e)}")

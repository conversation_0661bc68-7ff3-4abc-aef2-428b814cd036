"""
向量数据库数据预填充模块

从dd_submission_data表读取数据，进行向量化处理，并存储到PgVector数据库
"""

import asyncio
import logging
import time
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import numpy as np

# 添加项目根目录到路径
current_dir = Path(__file__).resolve().parent
src_dir = current_dir.parent.parent.parent.parent
sys.path.insert(0, str(src_dir))

from service import get_client, cleanup
from modules.knowledge.dd.crud import DDCrud

logger = logging.getLogger(__name__)


class VectorDataPreparation:
    """向量数据预填充类"""
    
    def __init__(self):
        self.mysql_client = None
        self.pgvector_client = None
        self.embedding_client = None
        self.dd_crud = None
        self.stats = {
            'total_records': 0,
            'processed_records': 0,
            'successful_vectors': 0,
            'failed_vectors': 0,
            'start_time': None,
            'end_time': None
        }
    
    async def setup_clients(self):
        """建立客户端连接"""
        logger.info("🔧 建立客户端连接...")
        
        try:
            # 建立数据库连接
            self.mysql_client = await get_client("database.rdbs.mysql")
            self.pgvector_client = await get_client("database.vdbs.pgvector")
            self.embedding_client = await get_client("model.embeddings.moka-m3e-base")
            
            # 初始化DD CRUD
            self.dd_crud = DDCrud(self.mysql_client, self.pgvector_client, self.embedding_client)
            
            logger.info("✅ 所有客户端连接成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 客户端连接失败: {e}")
            return False
    
    async def get_submission_data_count(self) -> int:
        """获取dd_submission_data表的总记录数"""
        try:
            query = """
            SELECT COUNT(*) as total_count
            FROM dd_submission_data 
            WHERE dr09 IS NOT NULL 
            AND dr17 IS NOT NULL 
            AND LENGTH(dr09) > 3 
            AND LENGTH(dr17) > 10
            """
            
            result = await self.mysql_client.afetch_all(query)
            total_count = result.data[0]['total_count'] if result.data else 0
            
            logger.info(f"📊 dd_submission_data表符合条件的记录总数: {total_count}")
            return total_count
            
        except Exception as e:
            logger.error(f"❌ 获取记录总数失败: {e}")
            return 0
    
    async def get_submission_data_batch(self, offset: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """批量获取dd_submission_data表数据"""
        try:
            query = """
            SELECT
                id as data_row_id,
                submission_id,
                dr09,
                dr17,
                dr01,
                type,
                report_data_id,
                create_time,
                update_time
            FROM dd_submission_data
            WHERE dr09 IS NOT NULL
            AND dr17 IS NOT NULL
            AND LENGTH(dr09) > 3
            AND LENGTH(dr17) > 10
            ORDER BY id
            LIMIT :limit OFFSET :offset
            """

            result = await self.mysql_client.afetch_all(query, {"limit": limit, "offset": offset})
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"❌ 获取批量数据失败: {e}")
            return []
    
    async def get_knowledge_id_from_report_data(self, report_data_id: int) -> Optional[str]:
        """从dd_report_data表获取knowledge_id"""
        try:
            if not report_data_id:
                return None
                
            query = """
            SELECT knowledge_id
            FROM dd_report_data
            WHERE id = :report_data_id
            """

            result = await self.mysql_client.afetch_all(query, {"report_data_id": report_data_id})
            if result.data:
                return result.data[0]['knowledge_id']
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ 获取knowledge_id失败: report_data_id={report_data_id}, error={e}")
            return None
    

    
    async def create_vectors_using_dd_crud(
        self,
        dr09_content: str,
        dr17_content: str,
        knowledge_id: str,
        data_row_id: int,
        data_layer: str
    ) -> Tuple[bool, bool]:
        """使用DDCrud创建向量，复用现有的向量化逻辑和版本控制机制"""
        try:
            # 构造符合DDCrud期望格式的submission_data
            submission_data = {
                "knowledge_id": knowledge_id,
                "dr01": data_layer  # data_layer对应dr01字段
            }

            # 构造向量化内容
            vectorized_content = {}
            if dr09_content and dr09_content.strip():
                vectorized_content["dr09"] = dr09_content.strip()
            if dr17_content and dr17_content.strip():
                vectorized_content["dr17"] = dr17_content.strip()

            if not vectorized_content:
                return False, False

            # 调用DDCrud的私有方法来创建向量
            vector_ids = await self.dd_crud._create_vectors_for_submission(
                submission_data, data_row_id, vectorized_content
            )

            # 检查结果
            dr09_success = False
            dr17_success = False

            for vector_data in vector_ids:
                if isinstance(vector_data, dict):
                    field_code = vector_data.get("field_code", "")
                    if field_code == "dr09":
                        dr09_success = True
                    elif field_code == "dr17":
                        dr17_success = True

            return dr09_success, dr17_success

        except Exception as e:
            logger.warning(f"⚠️ 使用DDCrud创建向量失败: {e}")
            return False, False
    
    async def process_single_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """处理单条记录"""
        data_row_id = record['data_row_id']
        dr09 = record.get('dr09', '')
        dr17 = record.get('dr17', '')
        dr01 = record.get('dr01', 'ADS')
        report_data_id = record.get('report_data_id')
        
        result = {
            'data_row_id': data_row_id,
            'dr09_success': False,
            'dr17_success': False,
            'knowledge_id': None,
            'errors': []
        }
        
        try:
            # 获取knowledge_id
            knowledge_id = await self.get_knowledge_id_from_report_data(report_data_id)
            if not knowledge_id:
                knowledge_id = f"dd_submission_{data_row_id}"  # 使用默认值
            
            result['knowledge_id'] = knowledge_id
            
            # 使用DDCrud创建向量（同时处理dr09和dr17）
            dr09_success, dr17_success = await self.create_vectors_using_dd_crud(
                dr09, dr17, knowledge_id, data_row_id, dr01
            )

            result['dr09_success'] = dr09_success
            result['dr17_success'] = dr17_success

            # 更新统计信息
            if dr09_success:
                self.stats['successful_vectors'] += 1
            elif dr09:
                self.stats['failed_vectors'] += 1
                result['errors'].append('dr09向量化失败')

            if dr17_success:
                self.stats['successful_vectors'] += 1
            elif dr17:
                self.stats['failed_vectors'] += 1
                result['errors'].append('dr17向量化失败')
            
            self.stats['processed_records'] += 1
            
        except Exception as e:
            logger.error(f"❌ 处理记录失败: data_row_id={data_row_id}, error={e}")
            result['errors'].append(f'处理失败: {str(e)}')
            self.stats['failed_vectors'] += 1
        
        return result
    
    async def process_batch(self, batch_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量处理数据"""
        logger.info(f"🔄 开始处理批次: {len(batch_data)}条记录")
        
        results = []
        for i, record in enumerate(batch_data):
            try:
                result = await self.process_single_record(record)
                results.append(result)
                
                # 每10条记录显示一次进度
                if (i + 1) % 10 == 0:
                    logger.info(f"   已处理: {i + 1}/{len(batch_data)}")
                    
            except Exception as e:
                logger.error(f"❌ 批量处理中断: {e}")
                results.append({
                    'data_row_id': record.get('data_row_id', 'unknown'),
                    'dr09_success': False,
                    'dr17_success': False,
                    'errors': [f'批量处理失败: {str(e)}']
                })
        
        successful_count = len([r for r in results if r['dr09_success'] or r['dr17_success']])
        logger.info(f"✅ 批次处理完成: {successful_count}/{len(batch_data)}条记录成功")
        
        return results
    
    async def run_preparation(self, batch_size: int = 50, max_records: Optional[int] = None):
        """运行向量数据预填充"""
        logger.info("🚀 开始向量数据库数据预填充")
        logger.info("=" * 80)
        
        self.stats['start_time'] = datetime.now()
        
        try:
            # 建立连接
            if not await self.setup_clients():
                logger.error("❌ 客户端连接失败，预填充终止")
                return
            
            # 获取总记录数
            total_count = await self.get_submission_data_count()
            if total_count == 0:
                logger.warning("⚠️ 没有符合条件的记录，预填充结束")
                return
            
            self.stats['total_records'] = min(total_count, max_records) if max_records else total_count
            
            logger.info(f"📊 预填充统计:")
            logger.info(f"   总记录数: {total_count}")
            logger.info(f"   计划处理: {self.stats['total_records']}")
            logger.info(f"   批次大小: {batch_size}")
            
            # 批量处理
            offset = 0
            batch_results = []
            
            while offset < self.stats['total_records']:
                current_batch_size = min(batch_size, self.stats['total_records'] - offset)
                
                logger.info(f"📦 获取批次数据: offset={offset}, limit={current_batch_size}")
                
                # 获取批次数据
                batch_data = await self.get_submission_data_batch(offset, current_batch_size)
                
                if not batch_data:
                    logger.warning(f"⚠️ 批次数据为空，跳过: offset={offset}")
                    offset += current_batch_size
                    continue
                
                # 处理批次
                batch_result = await self.process_batch(batch_data)
                batch_results.extend(batch_result)
                
                offset += len(batch_data)
                
                # 显示总体进度
                progress = (offset / self.stats['total_records']) * 100
                logger.info(f"📈 总体进度: {offset}/{self.stats['total_records']} ({progress:.1f}%)")
                
                # 短暂休息，避免过载
                await asyncio.sleep(0.1)
            
            self.stats['end_time'] = datetime.now()
            
            # 生成最终报告
            self._generate_final_report(batch_results)
            
        except Exception as e:
            logger.error(f"❌ 向量数据预填充失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        try:
            logger.info("🧹 清理测试资源...")
            await cleanup()
            logger.info("✅ 资源清理完成")
        except Exception as e:
            logger.warning(f"⚠️ 资源清理警告: {e}")
    
    def _generate_final_report(self, batch_results: List[Dict[str, Any]]):
        """生成最终报告"""
        logger.info("=" * 80)
        logger.info("📊 向量数据库数据预填充最终报告")
        logger.info("=" * 80)
        
        # 计算统计信息
        duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
        
        successful_records = len([r for r in batch_results if r['dr09_success'] or r['dr17_success']])
        failed_records = len(batch_results) - successful_records
        
        dr09_success_count = len([r for r in batch_results if r['dr09_success']])
        dr17_success_count = len([r for r in batch_results if r['dr17_success']])
        
        logger.info(f"⏱️ 执行时间: {duration:.2f}秒")
        logger.info(f"📊 处理统计:")
        logger.info(f"   总记录数: {self.stats['total_records']}")
        logger.info(f"   已处理: {self.stats['processed_records']}")
        logger.info(f"   成功记录: {successful_records}")
        logger.info(f"   失败记录: {failed_records}")
        logger.info(f"   成功率: {(successful_records/len(batch_results)*100):.1f}%" if batch_results else "0%")
        
        logger.info(f"🔤 向量化统计:")
        logger.info(f"   dr09成功: {dr09_success_count}")
        logger.info(f"   dr17成功: {dr17_success_count}")
        logger.info(f"   总向量数: {self.stats['successful_vectors']}")
        logger.info(f"   失败向量: {self.stats['failed_vectors']}")
        
        logger.info(f"⚡ 性能指标:")
        logger.info(f"   处理速度: {self.stats['processed_records']/duration:.2f}记录/秒" if duration > 0 else "N/A")
        logger.info(f"   向量化速度: {self.stats['successful_vectors']/duration:.2f}向量/秒" if duration > 0 else "N/A")
        
        # 保存详细报告
        report_file = f"vector_data_preparation_report_{time.strftime('%Y%m%d_%H%M%S')}.json"
        report_data = {
            'summary': {
                'total_records': self.stats['total_records'],
                'processed_records': self.stats['processed_records'],
                'successful_records': successful_records,
                'failed_records': failed_records,
                'successful_vectors': self.stats['successful_vectors'],
                'failed_vectors': self.stats['failed_vectors'],
                'duration_seconds': duration,
                'processing_rate': self.stats['processed_records']/duration if duration > 0 else 0,
                'vectorization_rate': self.stats['successful_vectors']/duration if duration > 0 else 0
            },
            'detailed_results': batch_results[:100],  # 保存前100条详细结果
            'timestamp': self.stats['start_time'].isoformat(),
            'end_timestamp': self.stats['end_time'].isoformat()
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📝 详细报告已保存到: {report_file}")
        
        # 最终评估
        if successful_records / len(batch_results) >= 0.8 if batch_results else False:
            logger.info("🎉 向量数据预填充成功完成！")
        elif successful_records / len(batch_results) >= 0.5 if batch_results else False:
            logger.info("⚠️ 向量数据预填充部分成功，建议检查失败原因")
        else:
            logger.info("❌ 向量数据预填充失败较多，需要排查问题")
        
        logger.info("=" * 80)


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='DD义务分发系统向量数据预填充')
    parser.add_argument('--batch-size', type=int, default=50, help='批次大小 (默认: 50)')
    parser.add_argument('--max-records', type=int, help='最大处理记录数 (默认: 全部)')
    
    args = parser.parse_args()
    
    preparation = VectorDataPreparation()
    await preparation.run_preparation(
        batch_size=args.batch_size,
        max_records=args.max_records
    )


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    asyncio.run(main())

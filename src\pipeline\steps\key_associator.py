"""
关联键处理步骤 - 简化版本
根据已选择的列信息，查找相关的关联键信息，生成关联键描述和结构化数据
"""

from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

from pipeline.core.base_step import BaseStep
from pipeline.core.context import PipelineContext
from modules.knowledge.metadata.crud import MetadataCrud

class KeyAssociatorStep(BaseStep):
    """
    关联键处理步骤 - 简化版本
    根据已选择的列信息，查找相关的关联键信息，生成关联键描述和结构化数据
    """

    def __init__(self, data_type: str = "source"):
        """
        初始化关联键处理步骤
        
        Args:
            data_type: 数据类型，'source' 或 'index'
        """
        super().__init__(
            name=f"key_associator_{data_type}",
            description=f"根据已选择的{data_type}列查找关联键信息，生成关联键描述"
        )
        self.data_type = data_type
        self.metadata_crud = None
    
    async def preprocess(self, context: PipelineContext) -> Dict[str, Any]:
        """阶段1: 预处理 - 准备候选列ID数据"""
        try:
            # 初始化MetadataCrud
            if not self.metadata_crud:
                from service import get_client
                rdb_client = await get_client("database.rdbs.mysql")
                self.metadata_crud = MetadataCrud(rdb_client)

            # 从context中获取候选列ID
            candidate_column_ids_dict = context.get("candidate_column_ids", {})
            candidate_column_ids = []

            if isinstance(candidate_column_ids_dict, dict):
                for table_id, column_ids in candidate_column_ids_dict.items():
                    if isinstance(column_ids, list):
                        candidate_column_ids.extend(column_ids)

            if not candidate_column_ids:
                logger.warning("没有找到候选列ID信息，跳过关联键查找")
                return {"candidate_column_ids": []}

            # 去重
            candidate_column_ids = list(set(candidate_column_ids))
            logger.info(f"找到 {len(candidate_column_ids)} 个列ID需要查找关联键: {candidate_column_ids}")

            return {"candidate_column_ids": candidate_column_ids}

        except Exception as e:
            logger.error(f"关联键预处理失败: {e}")
            return {"candidate_column_ids": []}

    async def process(self, preprocessed_data: Dict[str, Any], context: PipelineContext) -> Dict[str, Any]:
        """核心处理 - 查找关联键信息并生成描述"""
        try:
            candidate_column_ids = preprocessed_data.get("candidate_column_ids", [])

            if not candidate_column_ids:
                logger.warning("没有候选列ID，跳过关联键查找")
                return {"associate_keys": {}, "associate_info": "", "relation_mappings": {}}

            # 构建关联关系映射 {source_column_id: [target_column_id1, target_column_id2, ...]}
            relation_mappings = {}
            
            for column_id in candidate_column_ids:
                # 查找这个列作为源列的关联关系
                source_relations = await self._get_source_relations(column_id)
                # 查找这个列作为目标列的关联关系  
                target_relations = await self._get_target_relations(column_id)
                
                # 收集这个列的所有关联列
                related_columns = []
                related_columns.extend([rel['target_column_id'] for rel in source_relations])
                related_columns.extend([rel['source_column_id'] for rel in target_relations])
                
                if related_columns:
                    relation_mappings[column_id] = list(set(related_columns))

            # 构建结构化的关联键信息：{table.column: [related_table.column1, ...]}
            associate_keys = await self._build_relation_structure(relation_mappings)

            # 生成关联键描述文本
            associate_info = self._generate_relation_description(associate_keys)

            logger.info(f"找到 {len(relation_mappings)} 个列的关联关系，涉及 {len(associate_keys)} 个关联关系")

            return {
                "associate_keys": associate_keys,
                "associate_info": associate_info,
                "relation_mappings": relation_mappings  # 保留原始映射信息
            }

        except Exception as e:
            logger.error(f"关联键查找失败: {e}")
            return {"associate_keys": {}, "associate_info": "", "relation_mappings": {}}
    
    async def update_context(self, result: Dict[str, Any], context: PipelineContext) -> None:
        """更新上下文 - 设置关联键信息"""
        try:
            associate_keys = result.get("associate_keys", {})
            associate_info = result.get("associate_info", "")
            
            # 更新context的三个操作
            context.set("associate_keys", associate_keys)
            context.set("associate_info", associate_info)
            
            # 将associate_info追加到db_schema末尾
            if associate_info:
                current_schema = context.get("db_schema", "")
                updated_schema = f"{current_schema}\n\n{associate_info}"
                context.set("db_schema", updated_schema)
                
            logger.info(f"已更新上下文：associate_keys包含{len(associate_keys)}个表，associate_info已追加到db_schema")

        except Exception as e:
            logger.error(f"更新上下文失败: {e}")

    # ==================== 核心关联键查找方法 ====================

    async def _get_source_relations(self, column_id: int) -> List[Dict[str, Any]]:
        """查找这个列作为源列的关联关系"""
        try:
            if self.data_type == "source":
                relations = await self.metadata_crud.list_source_key_relations(source_column_id=column_id)
            else:  # index
                relations = await self.metadata_crud.list_index_key_relations(source_column_id=column_id)
            return relations
        except Exception as e:
            logger.error(f"查找源关联关系失败: {e}")
            return []

    async def _get_target_relations(self, column_id: int) -> List[Dict[str, Any]]:
        """查找这个列作为目标列的关联关系"""
        try:
            if self.data_type == "source":
                relations = await self.metadata_crud.list_source_key_relations(target_column_id=column_id)
            else:  # index
                relations = await self.metadata_crud.list_index_key_relations(target_column_id=column_id)
            return relations
        except Exception as e:
            logger.error(f"查找目标关联关系失败: {e}")
            return []

    # ==================== 结构化数据构建方法 ====================

    async def _build_relation_structure(self, relation_mappings: Dict[int, List[int]]) -> Dict[str, List[str]]:
        """将关联映射转换为table.column的结构化数据"""
        try:
            associate_keys = {}
            
            for column_id, related_column_ids in relation_mappings.items():
                # 获取源列信息
                if self.data_type == "source":
                    column_info = await self.metadata_crud.get_source_column(column_id=column_id)
                else:
                    column_info = await self.metadata_crud.get_index_column(column_id=column_id)
                
                if not column_info:
                    continue
                
                table_id = column_info['table_id']
                column_name = column_info['column_name']
                
                # 获取源表信息
                if self.data_type == "source":
                    table_info = await self.metadata_crud.get_source_table(table_id=table_id)
                else:
                    table_info = await self.metadata_crud.get_index_table(table_id=table_id)
                
                if not table_info:
                    continue
                
                table_name = table_info['table_name']
                source_key = f"{table_name}.{column_name}"
                
                # 收集相关的列信息
                related_keys = []
                for related_column_id in related_column_ids:
                    # 获取相关列信息（注意：相关列可能是source或index类型）
                    related_column_info = None
                    related_table_info = None
                    
                    # 先尝试当前类型
                    if self.data_type == "source":
                        related_column_info = await self.metadata_crud.get_source_column(column_id=related_column_id)
                        if related_column_info:
                            related_table_info = await self.metadata_crud.get_source_table(table_id=related_column_info['table_id'])
                    else:
                        related_column_info = await self.metadata_crud.get_index_column(column_id=related_column_id)
                        if related_column_info:
                            related_table_info = await self.metadata_crud.get_index_table(table_id=related_column_info['table_id'])
                    
                    # 如果当前类型找不到，尝试另一种类型
                    if not related_column_info:
                        if self.data_type == "source":
                            related_column_info = await self.metadata_crud.get_index_column(column_id=related_column_id)
                            if related_column_info:
                                related_table_info = await self.metadata_crud.get_index_table(table_id=related_column_info['table_id'])
                        else:
                            related_column_info = await self.metadata_crud.get_source_column(column_id=related_column_id)
                            if related_column_info:
                                related_table_info = await self.metadata_crud.get_source_table(table_id=related_column_info['table_id'])
                    
                    if related_column_info and related_table_info:
                        related_table_name = related_table_info['table_name']
                        related_column_name = related_column_info['column_name']
                        related_keys.append(f"{related_table_name}.{related_column_name}")
                
                if related_keys:
                    associate_keys[source_key] = related_keys
            
            return associate_keys
            
        except Exception as e:
            logger.error(f"构建关联键结构失败: {e}")
            return {}

    def _generate_relation_description(self, associate_keys: Dict[str, List[str]]) -> str:
        """生成关联键描述文本"""
        try:
            if not associate_keys:
                return ""

            # 方案1: 简洁美观，节省token
            relation_descriptions = []
            for key, related_keys in associate_keys.items():
                if related_keys:
                    related_keys_str = ", ".join(related_keys)
                    relation_descriptions.append(f"{key}: 【{related_keys_str}】")

            if not relation_descriptions:
                return ""

            return f"【Business Association Keys】\n{' | '.join(relation_descriptions)}"
            
        except Exception as e:
            logger.error(f"生成关联键描述失败: {e}")
            return ""

    def format_display_result(self, final_result: Dict[str, Any]) -> str:
        """格式化显示结果"""
        associate_keys = final_result.get("associate_keys", {})
        associate_info = final_result.get("associate_info", "")
        
        if not associate_keys:
            return "## 🔗 关联键查找结果：\n- 未找到相关的关联键。"
        
        display_parts = ["## 🔗 关联键查找结果：\n"]
        display_parts.append(f"📊 **统计**: 找到 {len(associate_keys)} 个关联关系\n")
        
        if associate_info:
            display_parts.append(f"📝 **描述**: {associate_info}\n")
        
        display_parts.append("📋 **详细关联关系**:")
        for source_key, related_keys in associate_keys.items():
            related_str = ", ".join(related_keys)
            display_parts.append(f"- **{source_key}**: → 【{related_str}】")
        
        return "\n".join(display_parts)

"""
ORM客户端工厂函数

提供与universal工厂函数完全相同的接口，用于创建ORM客户端实例
"""

from typing import Dict, Any, Optional
import logging

from .client import ORMSQLAlchemyClient
from .config import ORMConnectionConfig, get_default_orm_config

logger = logging.getLogger(__name__)


def create_orm_client(
    database_url: str,
    **options
) -> ORMSQLAlchemyClient:
    """
    从数据库URL创建ORM SQLAlchemy客户端
    
    Args:
        database_url: 数据库连接URL
        **options: 额外配置选项
    
    Returns:
        ORMSQLAlchemyClient实例
    
    Example:
        >>> client = create_orm_client("postgresql://user:pass@localhost/db")
        >>> await client.aconnect()
    """
    config = ORMConnectionConfig(database_url=database_url, **options)
    client = ORMSQLAlchemyClient(config)
    
    logger.info(f"Created ORM SQLAlchemy client for {config.database_url}")
    
    return client


def create_orm_client_from_components(
    database_type: str,
    host: str,
    database: str,
    username: str,
    password: str,
    port: Optional[int] = None,
    driver: Optional[str] = None,
    **options
) -> ORMSQLAlchemyClient:
    """
    从组件创建ORM SQLAlchemy客户端
    
    Args:
        database_type: 数据库类型 (mysql, postgresql, sqlite, etc.)
        host: 数据库主机
        database: 数据库名
        username: 用户名
        password: 密码
        port: 端口号（可选，使用方言默认值）
        driver: 数据库驱动（可选，使用方言默认值）
        **options: 额外配置选项
    
    Returns:
        ORMSQLAlchemyClient实例
    
    Example:
        >>> client = create_orm_client_from_components(
        ...     "mysql", "localhost", "mydb", "user", "pass"
        ... )
        >>> await client.aconnect()
    """
    # 验证必需参数
    if database_type != 'sqlite':
        if not username or not password:
            from .exceptions import ConfigurationError
            raise ConfigurationError("username and password are required for non-SQLite databases")

    # 合并数据库类型的默认配置（默认配置不覆盖用户选项）
    default_config = get_default_orm_config(database_type)
    for key, value in default_config.items():
        if key not in options:
            options[key] = value
    
    config = ORMConnectionConfig.from_components(
        dialect=database_type,
        host=host,
        database=database,
        username=username,
        password=password,
        port=port,
        driver=driver,
        **options
    )
    client = ORMSQLAlchemyClient(config)
    
    logger.info(f"Created ORM SQLAlchemy client for {database_type}://{host}:{port}/{database}")
    
    return client


def create_orm_sqlite_client(
    database_path: str,
    **options
) -> ORMSQLAlchemyClient:
    """
    创建ORM SQLAlchemy SQLite客户端
    
    Args:
        database_path: SQLite数据库文件路径
        **options: 额外配置选项
    
    Returns:
        ORMSQLAlchemyClient实例
    
    Example:
        >>> client = create_orm_sqlite_client("/path/to/database.db")
        >>> await client.aconnect()
    """
    # 合并SQLite默认配置（默认配置不覆盖用户选项）
    default_config = get_default_orm_config('sqlite')
    for key, value in default_config.items():
        if key not in options:
            options[key] = value
    
    database_url = f"sqlite:///{database_path}"
    return create_orm_client(database_url, **options)


def create_orm_mysql_client(
    host: str,
    database: str,
    username: str,
    password: str,
    port: int = 3306,
    charset: str = "utf8mb4",
    use_independent_base: bool = True,
    **options
) -> ORMSQLAlchemyClient:
    """
    创建ORM SQLAlchemy MySQL客户端

    Args:
        host: MySQL主机
        database: 数据库名
        username: 用户名
        password: 密码
        port: 端口号（默认：3306）
        charset: 字符集（默认：utf8mb4）
        use_independent_base: 是否使用独立的declarative base（默认：True，避免SAWarning）
        **options: 额外配置选项
    
    Returns:
        ORMSQLAlchemyClient实例
    
    Example:
        >>> client = create_orm_mysql_client(
        ...     "localhost", "mydb", "user", "pass"
        ... )
        >>> await client.aconnect()
    """
    # 添加MySQL特定选项
    dialect_options = options.get('dialect_options', {})
    dialect_options.update({
        'connect_args': {
            'charset': charset,
            'use_unicode': True
        }
    })
    options['dialect_options'] = dialect_options
    
    # 合并MySQL默认配置（默认配置不覆盖用户选项）
    default_config = get_default_orm_config('mysql')
    for key, value in default_config.items():
        if key not in options:
            options[key] = value

    # 传递独立base选项
    options['use_independent_base'] = use_independent_base

    return create_orm_client_from_components(
        "mysql", host, database, username, password, port, **options
    )


def create_orm_postgresql_client(
    host: str,
    database: str,
    username: str,
    password: str,
    port: int = 5432,
    **options
) -> ORMSQLAlchemyClient:
    """
    创建ORM SQLAlchemy PostgreSQL客户端
    
    Args:
        host: PostgreSQL主机
        database: 数据库名
        username: 用户名
        password: 密码
        port: 端口号（默认：5432）
        **options: 额外配置选项
    
    Returns:
        ORMSQLAlchemyClient实例
    
    Example:
        >>> client = create_orm_postgresql_client(
        ...     "localhost", "mydb", "user", "pass"
        ... )
        >>> await client.aconnect()
    """
    # 合并PostgreSQL默认配置（默认配置不覆盖用户选项）
    default_config = get_default_orm_config('postgresql')
    for key, value in default_config.items():
        if key not in options:
            options[key] = value
    
    return create_orm_client_from_components(
        "postgresql", host, database, username, password, port, **options
    )


async def create_and_connect_orm_client(
    database_url: str,
    **options
) -> ORMSQLAlchemyClient:
    """
    创建并连接ORM SQLAlchemy客户端
    
    Args:
        database_url: 数据库连接URL
        **options: 额外配置选项
    
    Returns:
        已连接的ORMSQLAlchemyClient实例
    
    Example:
        >>> client = await create_and_connect_orm_client(
        ...     "postgresql://user:pass@localhost/db"
        ... )
    """
    client = create_orm_client(database_url, **options)
    await client.aconnect()
    return client


def get_supported_databases() -> list:
    """
    获取支持的数据库列表
    
    Returns:
        支持的数据库类型列表
    """
    return ["mysql", "postgresql", "sqlite"]


def create_orm_client_from_config_dict(config_dict: Dict[str, Any]) -> ORMSQLAlchemyClient:
    """
    从配置字典创建ORM SQLAlchemy客户端
    
    Args:
        config_dict: 配置字典
    
    Returns:
        ORMSQLAlchemyClient实例
    
    Example:
        >>> config = {
        ...     "database_url": "postgresql://user:pass@localhost/db",
        ...     "pool_size": 10,
        ...     "echo": True,
        ...     "enable_dynamic_models": True
        ... }
        >>> client = create_orm_client_from_config_dict(config)
        >>> await client.aconnect()
    """
    config = ORMConnectionConfig.from_dict(config_dict)
    client = ORMSQLAlchemyClient(config)
    
    logger.info(f"Created ORM SQLAlchemy client from config dict")
    
    return client


def create_orm_client_for_database(
    database_type: str,
    connection_params: Dict[str, Any],
    **options
) -> ORMSQLAlchemyClient:
    """
    为特定数据库类型创建ORM客户端
    
    Args:
        database_type: 数据库类型
        connection_params: 连接参数
        **options: 额外配置选项
    
    Returns:
        ORMSQLAlchemyClient实例
    """
    # 合并数据库类型的默认配置（默认配置不覆盖用户选项）
    default_config = get_default_orm_config(database_type)
    for key, value in default_config.items():
        if key not in options:
            options[key] = value
    
    from .config import create_orm_config_for_database
    config = create_orm_config_for_database(database_type, connection_params, **options)
    client = ORMSQLAlchemyClient(config)
    
    logger.info(f"Created ORM SQLAlchemy client for {database_type}")
    
    return client


# 便捷别名，保持与universal接口一致
create_client = create_orm_client
create_sqlite_client = create_orm_sqlite_client
create_mysql_client = create_orm_mysql_client
create_postgresql_client = create_orm_postgresql_client
create_and_connect_client = create_and_connect_orm_client

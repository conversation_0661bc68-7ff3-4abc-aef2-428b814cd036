-- ==========================================
-- RDB 全局数据库表结构创建脚本
-- ==========================================
--
-- 说明：
-- 1. 本脚本为自动生成文件，整合了所有RDB（关系型数据库）相关的表结构。
-- 2. 包含了三个主要领域：
--    - 元数据 (Metadata)
--    - 数据需求 (DD)
--    - 文档 (Doc)
-- 3. 脚本执行顺序：元数据 (md_*) -> 数据需求 (dd_*) -> 文档 (doc_*)。
-- 4. `kb_knowledge` 表在元数据部分创建，其他领域通过外键关联。
--
-- 创建时间：2025-07-04
-- ==========================================

-- 设置全局参数
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ==========================================
-- I. 元数据领域 (Metadata Domain)
-- Creates: kb_knowledge, md_* tables
-- Source: create_rdb_metadata.sql
-- ==========================================
-- ==========================================
-- 1. 全局知识库表 (Global Knowledge Base)
-- ==========================================
DROP TABLE IF EXISTS `kb_knowledge`;
CREATE TABLE `kb_knowledge` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `knowledge_name` varchar(255) NOT NULL COMMENT '知识库名称',
  `doc_nums` int NOT NULL DEFAULT '0' COMMENT '知识库中的文档数量',
  `knowledge_type` varchar(255) NOT NULL COMMENT '知识库类别：DD/Doc/MetaData',
  `knowledge_desc` varchar(255) NOT NULL COMMENT '知识库的描述信息',
  `models` json NOT NULL COMMENT '绑定的模型配置(JSON格式): {"embedding": "model_name", "llm": "model_name", ...}',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '知识库创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '知识库最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_id` (`knowledge_id`),
  KEY `idx_knowledge_name` (`knowledge_name`),
  KEY `idx_knowledge_type` (`knowledge_type`),
  KEY `idx_models` ((CAST(`models`->>'$.embedding' AS CHAR(255)))),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='全局知识库表';

-- ==========================================
-- 元数据领域表 (Metadata Domain Tables)
-- 表名前缀: md_ (Metadata)
-- ==========================================

-- ==========================================
-- 2. 源数据库管理表 (Source Database)
-- ==========================================
DROP TABLE IF EXISTS `md_source_database`;
CREATE TABLE `md_source_database` (
  `db_id` bigint NOT NULL AUTO_INCREMENT COMMENT '数据库ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '关联的知识库ID',
  `db_name` varchar(100) NOT NULL COMMENT '数据库名称',
  `db_name_cn` varchar(100) DEFAULT NULL COMMENT '数据库中文名称',
  `data_layer` varchar(50) NOT NULL COMMENT '数据层标识：adm-管理层，bdm-基础数据层，ods-操作数据层，ads-应用数据层',
  `db_desc` text COMMENT '数据库描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`db_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_data_layer` (`data_layer`),
  KEY `idx_db_name` (`db_name`),
  KEY `idx_is_active` (`is_active`),
  UNIQUE KEY `uk_source_database_business` (`knowledge_id`, `db_name`),
  CONSTRAINT `fk_md_source_database_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='源数据库管理表';

-- ==========================================
-- 3. 指标数据库管理表 (Index Database)
-- ==========================================
DROP TABLE IF EXISTS `md_index_database`;
CREATE TABLE `md_index_database` (
  `db_id` bigint NOT NULL AUTO_INCREMENT COMMENT '数据库ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '关联的知识库ID',
  `db_name` varchar(100) NOT NULL COMMENT '数据库名称',
  `db_name_cn` varchar(100) DEFAULT NULL COMMENT '数据库中文名称',
  `data_layer` varchar(50) NOT NULL COMMENT '数据层标识：adm-管理层，bdm-基础数据层，ods-操作数据层，ads-应用数据层',
  `db_desc` text COMMENT '数据库描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`db_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_data_layer` (`data_layer`),
  KEY `idx_db_name` (`db_name`),
  KEY `idx_is_active` (`is_active`),
  UNIQUE KEY `uk_index_database_business` (`knowledge_id`, `db_name`),
  CONSTRAINT `fk_md_index_database_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标数据库管理表';

-- ==========================================
-- 4. 源表管理表 (Source Tables)
-- ==========================================
DROP TABLE IF EXISTS `md_source_tables`;
CREATE TABLE `md_source_tables` (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '表ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `db_id` bigint NOT NULL COMMENT '数据源ID',
  `table_name` varchar(100) NOT NULL COMMENT '表名',
  `table_name_cn` varchar(100) DEFAULT NULL COMMENT '表中文名',
  `table_desc` text COMMENT '表描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`table_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_db_id` (`db_id`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_is_active` (`is_active`),
  UNIQUE KEY `uk_source_tables_business` (`knowledge_id`, `db_id`, `table_name`),
  CONSTRAINT `fk_md_source_tables_db_id` FOREIGN KEY (`db_id`) REFERENCES `md_source_database` (`db_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_source_tables_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='源表管理表';

-- ==========================================
-- 5. 指标表管理表 (Index Tables)
-- ==========================================
DROP TABLE IF EXISTS `md_index_tables`;
CREATE TABLE `md_index_tables` (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '表ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `db_id` bigint NOT NULL COMMENT '数据源ID',
  `table_name` varchar(100) NOT NULL COMMENT '表名',
  `table_name_cn` varchar(100) DEFAULT NULL COMMENT '表中文名',
  `table_desc` text COMMENT '表描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`table_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_db_id` (`db_id`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_is_active` (`is_active`),
  UNIQUE KEY `uk_index_tables_business` (`knowledge_id`, `db_id`, `table_name`),
  CONSTRAINT `fk_md_index_tables_db_id` FOREIGN KEY (`db_id`) REFERENCES `md_index_database` (`db_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_index_tables_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标表管理表';

-- ==========================================
-- 6. 源字段管理表 (Source Columns)
-- ==========================================
DROP TABLE IF EXISTS `md_source_columns`;
CREATE TABLE `md_source_columns` (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字段ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `table_id` bigint NOT NULL COMMENT '表ID',
  `column_name` varchar(100) NOT NULL COMMENT '字段名',
  `column_name_cn` varchar(100) DEFAULT NULL COMMENT '字段中文名',
  `column_desc` text COMMENT '字段描述',
  `data_type` varchar(50) DEFAULT NULL COMMENT '数据类型：NUMBER,STRING,DATE等',
  `data_example` text COMMENT '数据样例',
  `is_vectorized` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已向量化：1-是，0-否',
  `is_primary_key` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否主键：1-是，0-否',
  `is_sensitive` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否敏感数据：1-是，0-否',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`column_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_table_id` (`table_id`),
  KEY `idx_column_name` (`column_name`),
  KEY `idx_is_primary_key` (`is_primary_key`),
  KEY `idx_is_vectorized` (`is_vectorized`),
  UNIQUE KEY `uk_source_columns_business` (`knowledge_id`, `table_id`, `column_name`),
  CONSTRAINT `fk_md_source_columns_table_id` FOREIGN KEY (`table_id`) REFERENCES `md_source_tables` (`table_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_source_columns_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='源字段管理表';

-- ==========================================
-- 7. 指标字段管理表 (Index Columns)
-- ==========================================
DROP TABLE IF EXISTS `md_index_columns`;
CREATE TABLE `md_index_columns` (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字段ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `table_id` bigint NOT NULL COMMENT '表ID',
  `column_name` varchar(100) NOT NULL COMMENT '字段名',
  `column_name_cn` varchar(100) DEFAULT NULL COMMENT '字段中文名',
  `index_type` varchar(50) DEFAULT NULL COMMENT '指标类型：atom-原子指标，compute-计算指标',
  `column_desc` text COMMENT '字段描述',
  `data_type` varchar(50) DEFAULT NULL COMMENT '数据类型：NUMBER,STRING,DATE,INDEX_DATE等',
  `data_example` text COMMENT '数据样例',
  `comment` text COMMENT '备注说明',
  `is_vectorized` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已向量化：1-是，0-否',
  `is_primary_key` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否主键：1-是，0-否',
  `is_sensitive` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否敏感数据：1-是，0-否',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`column_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_table_id` (`table_id`),
  KEY `idx_column_name` (`column_name`),
  KEY `idx_index_type` (`index_type`),
  KEY `idx_is_primary_key` (`is_primary_key`),
  KEY `idx_is_vectorized` (`is_vectorized`),
  UNIQUE KEY `uk_index_columns_business` (`knowledge_id`, `table_id`, `column_name`),
  CONSTRAINT `fk_md_index_columns_table_id` FOREIGN KEY (`table_id`) REFERENCES `md_index_tables` (`table_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_index_columns_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标字段管理表';

-- ==========================================
-- 8. 源关联键信息表 (Source Key Relation Info)
-- ==========================================
DROP TABLE IF EXISTS `md_source_key_relation_info`;
CREATE TABLE `md_source_key_relation_info` (
  `relation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `source_column_id` bigint NOT NULL COMMENT '源字段ID',
  `target_column_id` bigint NOT NULL COMMENT '目标字段ID',
  `relation_type` varchar(20) NOT NULL DEFAULT 'FK' COMMENT '关联类型：FK-外键，REF-参考关联',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`relation_id`),
  KEY `idx_source_column_id` (`source_column_id`),
  KEY `idx_target_column_id` (`target_column_id`),
  KEY `idx_relation_type` (`relation_type`),
  UNIQUE KEY `uk_source_key_relation_business` (`source_column_id`, `target_column_id`, `relation_type`),
  CONSTRAINT `fk_md_source_key_relation_source` FOREIGN KEY (`source_column_id`) REFERENCES `md_source_columns` (`column_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_source_key_relation_target` FOREIGN KEY (`target_column_id`) REFERENCES `md_source_columns` (`column_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='源关联键信息表';

-- ==========================================
-- 9. 指标关联键信息表 (Index Key Relation Info)
-- ==========================================
DROP TABLE IF EXISTS `md_index_key_relation_info`;
CREATE TABLE `md_index_key_relation_info` (
  `relation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `source_column_id` bigint NOT NULL COMMENT '源字段ID',
  `target_column_id` bigint NOT NULL COMMENT '目标字段ID',
  `relation_type` varchar(20) NOT NULL DEFAULT 'FK' COMMENT '关联类型：FK-外键，REF-参考关联',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`relation_id`),
  KEY `idx_source_column_id` (`source_column_id`),
  KEY `idx_target_column_id` (`target_column_id`),
  KEY `idx_relation_type` (`relation_type`),
  UNIQUE KEY `uk_index_key_relation_business` (`source_column_id`, `target_column_id`, `relation_type`),
  CONSTRAINT `fk_md_index_key_relation_source` FOREIGN KEY (`source_column_id`) REFERENCES `md_index_columns` (`column_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_index_key_relation_target` FOREIGN KEY (`target_column_id`) REFERENCES `md_index_columns` (`column_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标关联键信息表';

-- ==========================================
-- 10. 码值集关联表 (Reference Code Relation) - 旧版本，将被新版本替代
-- ==========================================
-- 注意：此表定义已被后面的新版本替代，这里保留是为了兼容性

-- ==========================================
-- 11. 参考码值集表 (Reference Code Set) - 先创建，因为被 doc 表引用
-- ==========================================
DROP TABLE IF EXISTS `md_reference_code_set`;
CREATE TABLE `md_reference_code_set` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '码值集ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `code_set_name` varchar(100) NOT NULL COMMENT '码值集名称',
  `code_set_desc` varchar(500) DEFAULT NULL COMMENT '码值集描述',
  `code_set_type` varchar(50) DEFAULT 'ENUM' COMMENT '码值集类型',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_code_set_name` (`knowledge_id`, `code_set_name`),
  UNIQUE KEY `uk_reference_code_set_business` (`knowledge_id`, `code_set_name`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_md_reference_code_set_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参考码值集表';

-- ==========================================
-- 12. 码值集文档表已删除 - 文件名直接存储在码值集表中
-- ==========================================

-- ==========================================
-- 13. 参考码值表 (Reference Code Value)
-- ==========================================
DROP TABLE IF EXISTS `md_reference_code_value`;
CREATE TABLE `md_reference_code_value` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '码值ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `code_set_id` bigint NOT NULL COMMENT '码值集ID',
  `code_value` varchar(100) NOT NULL COMMENT '码值',
  `code_desc` varchar(200) NOT NULL COMMENT '码值描述',
  `code_value_cn` varchar(200) DEFAULT NULL COMMENT '码值中文描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_code_set_id` (`code_set_id`),
  KEY `idx_code_value` (`code_value`),
  KEY `idx_is_active` (`is_active`),
  UNIQUE KEY `uk_code_set_code_value` (`code_set_id`, `code_value`),
  UNIQUE KEY `uk_reference_code_value_business` (`knowledge_id`, `code_set_id`, `code_value`),
  CONSTRAINT `fk_md_code_value_set` FOREIGN KEY (`code_set_id`) REFERENCES `md_reference_code_set` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_code_value_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参考码值表';

-- ==========================================
-- 14. 码值关联表 (Code Set Relation)
-- ==========================================
DROP TABLE IF EXISTS `md_reference_code_relation`;
CREATE TABLE `md_reference_code_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `column_id` bigint NOT NULL COMMENT '字段ID（源字段或指标字段）',
  `code_set_id` bigint NOT NULL COMMENT '码值集ID',
  `column_type` enum('source','index') NOT NULL DEFAULT 'source' COMMENT '字段类型：source-源字段，index-指标字段',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_column_code_set` (`column_id`, `code_set_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_code_set_id` (`code_set_id`),
  KEY `idx_column_type` (`column_type`),
  CONSTRAINT `fk_md_code_relation_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_code_relation_code_set` FOREIGN KEY (`code_set_id`) REFERENCES `md_reference_code_set` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='码值关联表';

-- ==========================================
-- 15. 数据主题表 (Data Subject)
-- ==========================================
DROP TABLE IF EXISTS `md_data_subject`;
CREATE TABLE `md_data_subject` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '数据主题ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `subject_code` varchar(50) NOT NULL COMMENT '数据主题编码',
  `subject_name` varchar(100) NOT NULL COMMENT '数据主题名称',
  `subject_desc` text COMMENT '数据主题描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_subject_code` (`knowledge_id`, `subject_code`),
  UNIQUE KEY `uk_data_subject_business` (`knowledge_id`, `subject_code`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_subject_name` (`subject_name`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_md_data_subject_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据主题表';

-- ==========================================
-- 16. 数据主题关联表 (Data Subject Relation)
-- ==========================================
DROP TABLE IF EXISTS `md_data_subject_relation`;
CREATE TABLE `md_data_subject_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `subject_id` bigint NOT NULL COMMENT '数据主题ID',
  `source_type` enum('SOURCE','INDEX') NOT NULL COMMENT '目标类型：SOURCE-源数据，INDEX-指标数据',
  `relation_path` varchar(500) NOT NULL COMMENT '关联路径：db 或 db.table 或 db.table.col',
  `relation_level` enum('DATABASE','TABLE','COLUMN') NOT NULL COMMENT '关联层级：DATABASE-数据库级别，TABLE-表级别，COLUMN-字段级别',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_subject_relation` (`knowledge_id`, `subject_id`, `relation_path`),
  UNIQUE KEY `uk_data_subject_relation_business` (`knowledge_id`, `subject_id`, `relation_path`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_subject_id` (`subject_id`),
  KEY `idx_source_type` (`source_type`),
  KEY `idx_relation_level` (`relation_level`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_md_data_subject_relation_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_data_subject_relation_subject` FOREIGN KEY (`subject_id`) REFERENCES `md_data_subject` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据主题关联表';

-- ==========================================
-- II. 数据需求领域 (DD Domain)
-- Creates: dd_* tables and initializes dd_fields_metadata
-- Source: create_rdb_dd.sql
-- ==========================================
-- ==========================================
-- 1. DD文件表 (DD Files)
-- 描述: 存储DD文件的元数据，作为文件/文档的容器
-- ==========================================
DROP TABLE IF EXISTS `dd_files`;
CREATE TABLE `dd_files` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `file_id` varchar(64) NOT NULL COMMENT '文件ID，UUID格式',
  `knowledge_id` varchar(255) NOT NULL COMMENT '关联的知识库ID',
  `file_name` varchar(200) NOT NULL COMMENT '文件名称',
  `version` varchar(20) NOT NULL DEFAULT '1.0' COMMENT '文件版本号',
  `data_source` varchar(200) DEFAULT NULL COMMENT '数据来源，描述数据的来源系统或文件',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '处理状态：pending-待处理，processing-处理中，completed-已完成，failed-失败',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否生效：1-生效，0-失效',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_file_id` (`file_id`),
  KEY `idx_knowledge_version` (`knowledge_id`, `version`),
  KEY `idx_file_name` (`file_name`),
  KEY `idx_status` (`status`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_dd_files_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DD文件表 (文件/文档元数据)';

-- ==========================================
-- 2. DD字段元数据表 (DD Fields Metadata)
-- 描述: 定义DD表格的所有字段元信息，内容来自dd.md，支持字段级配置
-- ==========================================
DROP TABLE IF EXISTS `dd_fields_metadata`;
CREATE TABLE `dd_fields_metadata` (
  `field_id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `field_code` varchar(50) NOT NULL COMMENT '字段编码，如DR01、BDR01等',
  `field_name` varchar(255) NOT NULL COMMENT '字段名称（英文）',
  `field_name_cn` varchar(255) NOT NULL COMMENT '字段名称（中文）',
  `field_desc` text COMMENT '字段详细描述',
  `category_level` char(1) NOT NULL COMMENT '分类层级：A-结果数据需求, B-业务解读, C-IT解读, D-指标解读',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `sub_category` varchar(100) DEFAULT NULL COMMENT '子分类，如A1管理信息、A2表字段等',
  `data_type` varchar(50) NOT NULL DEFAULT 'TEXT' COMMENT '数据类型：TEXT, NUMBER, DATE, ENUM等',
  `max_length` int DEFAULT NULL COMMENT '最大长度限制',
  `default_value` varchar(255) DEFAULT NULL COMMENT '默认值',
  `validation_rules` json DEFAULT NULL COMMENT '校验规则，JSON格式',
  `enum_values` json DEFAULT NULL COMMENT '枚举值列表，JSON格式（当data_type为ENUM时使用）',
  `field_order` int NOT NULL DEFAULT '0' COMMENT '字段排序，用于模板生成时的列顺序',
  `is_vectorized` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否需要向量化：1-是，0-否',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`field_id`),
  UNIQUE KEY `uk_field_code` (`field_code`),
  KEY `idx_category_level` (`category_level`),
  KEY `idx_sub_category` (`sub_category`),
  KEY `idx_field_order` (`field_order`),
  KEY `idx_is_vectorized` (`is_vectorized`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DD字段元数据表 (字段配置和向量化控制)';

-- ==========================================
-- 3. DD数据行表 (DD Data Rows) - 灵活键值对存储
-- 描述: 存储DD表格的每一行数据，使用field_code + field_value的灵活设计
-- ==========================================
DROP TABLE IF EXISTS `dd_data_rows`;
CREATE TABLE `dd_data_rows` (
  `row_id` bigint NOT NULL AUTO_INCREMENT COMMENT '数据行ID',
  `file_id` varchar(64) NOT NULL COMMENT '关联的文件ID',
  `version` varchar(20) NOT NULL DEFAULT '1.0' COMMENT '数据版本号，与file版本关联',
  `row_number` int NOT NULL COMMENT '行号，用于排序和定位',

  -- 元数据和状态字段
  `completion_status` varchar(20) DEFAULT 'incomplete' COMMENT '完成状态：incomplete-未完成，complete-已完成，validated-已校验',
  `completion_rate` decimal(5,2) DEFAULT '0.00' COMMENT '完成率百分比：0.00-100.00',
  `last_modified_field` varchar(50) DEFAULT NULL COMMENT '最后修改的字段编码',
  `modified_by` varchar(255) DEFAULT NULL COMMENT '最后修改人',
  `validation_errors` json DEFAULT NULL COMMENT '数据校验错误信息，JSON格式',
  `vectorization_status` json DEFAULT NULL COMMENT '字段向量化状态，JSON格式，记录哪些字段需要重新向量化',

  -- 时间戳
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`row_id`),
  KEY `idx_file_id_version` (`file_id`, `version`),
  UNIQUE KEY `uk_file_version_row` (`file_id`, `version`, `row_number`),
  KEY `idx_completion_status` (`completion_status`),
  KEY `idx_last_modified_field` (`last_modified_field`),
  KEY `idx_modified_by` (`modified_by`),

  CONSTRAINT `fk_dd_data_rows_file` FOREIGN KEY (`file_id`) REFERENCES `dd_files` (`file_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DD数据行表 (基础行信息，配合字段值表使用)';

-- ==========================================
-- 4. DD字段值表 (DD Field Values) - 键值对存储
-- 描述: 存储DD表格中每个字段的具体值，支持灵活的字段扩展
-- ==========================================
DROP TABLE IF EXISTS `dd_field_values`;
CREATE TABLE `dd_field_values` (
  `value_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字段值ID',
  `row_id` bigint NOT NULL COMMENT '关联的数据行ID',
  `field_id` int NOT NULL COMMENT '字段ID，关联dd_fields_metadata表',
  `field_value` text DEFAULT NULL COMMENT '字段值内容',
  `is_vectorized` tinyint(1) NOT NULL DEFAULT '0' COMMENT '该实例是否已向量化：1-是，0-否',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`value_id`),
  UNIQUE KEY `uk_row_field` (`row_id`, `field_id`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_is_vectorized` (`is_vectorized`),

  CONSTRAINT `fk_dd_field_values_row` FOREIGN KEY (`row_id`) REFERENCES `dd_data_rows` (`row_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_dd_field_values_field` FOREIGN KEY (`field_id`) REFERENCES `dd_fields_metadata` (`field_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DD字段值表 (键值对存储，支持灵活字段扩展)';

-- ==========================================
-- 第二部分：DD字段元数据初始化
-- ==========================================

-- 清空现有数据（如果需要重新初始化）
-- DELETE FROM dd_fields_metadata;

-- ==========================================
-- A类字段：结果数据需求 (DR01-DR22)
-- ==========================================

INSERT INTO dd_fields_metadata (
    field_code, field_name, field_name_cn, field_desc,
    category_level, category_name, sub_category,
    data_type, is_vectorized, field_order
) VALUES
-- A1.管理信息
('DR01', 'Target Data Layer', '产出结果数据所在的数据层', 'ADS/BDM/IDM/ADM等', 'A', '结果数据需求', 'A1.管理信息', 'ENUM', 0, 1),

-- A2.表-字段
('DR02', 'Requirement Document Name', '需求文件名称', '需求文档的名称', 'A', '结果数据需求', 'A2.表-字段', 'TEXT', 0, 2),
('DR03', 'Requirement Domain', '需求主题域', '需求所属的主题域', 'A', '结果数据需求', 'A2.表-字段', 'TEXT', 0, 3),
('DR04', 'Requirement Domain ID', '需求主题域ID', '需求主题域的唯一标识', 'A', '结果数据需求', 'A2.表-字段', 'TEXT', 0, 4),
('DR05', 'System Table Name', '系统文件表', '系统中的表名', 'A', '结果数据需求', 'A2.表-字段', 'TEXT', 0, 5),
('DR06', 'Requirement Table Name', '表名', '需求中的表名', 'A', '结果数据需求', 'A2.表-字段', 'TEXT', 0, 6),
('DR07', 'Requirement Table Name ID', '表名ID', '表名的唯一标识', 'A', '结果数据需求', 'A2.表-字段', 'TEXT', 0, 7),
('DR08', 'Reporting Model', '报送模式', '全量/增量/变量', 'A', '结果数据需求', 'A2.表-字段', 'ENUM', 0, 8),
('DR09', 'Requirement Data Field in Chinese', '数据项名称', '数据字段的中文名称', 'A', '结果数据需求', 'A2.表-字段', 'TEXT', 1, 9),
('DR10', 'Requirement Data Field ID', '数据项名称ID', '数据项的唯一标识', 'A', '结果数据需求', 'A2.表-字段', 'TEXT', 0, 10),
('DR11', 'System Table Field Name', '系统文件表字段', '系统表中的字段名', 'A', '结果数据需求', 'A2.表-字段', 'TEXT', 0, 11),
('DR12', 'System Table Field is Primary Key or Foreign Key Indicator', '系统文件表字段主键/外键标识位', '标识字段是否为主键或外键', 'A', '结果数据需求', 'A2.表-字段', 'ENUM', 0, 12),

-- A3.领域数据标准
('DR13', 'Data Element', '数据元', '数据元定义', 'A', '结果数据需求', 'A3.领域数据标准', 'TEXT', 0, 13),
('DR14', 'Data Element Serial No.', '数据元代码', '数据元的编码', 'A', '结果数据需求', 'A3.领域数据标准', 'TEXT', 0, 14),
('DR15', 'Data Element Description', '数据元说明', '数据元的详细说明', 'A', '结果数据需求', 'A3.领域数据标准', 'TEXT', 0, 15),

-- A4.表-字段
('DR16', 'Data Format', '格式要求', '需求要求的数据格式', 'A', '结果数据需求', 'A4.表-字段', 'TEXT', 0, 16),
('DR17', 'Requirement caliber/filling rules', '需求口径/填写规则', '数据填写的规则和口径', 'A', '结果数据需求', 'A4.表-字段', 'TEXT', 1, 17),
('DR18', 'Reference Code', '码表', '含版本号的码表信息', 'A', '结果数据需求', 'A4.表-字段', 'TEXT', 0, 18),
('DR19', 'Reporting Frequency', '报送频率', '日报/周报/月报/季报/半年报/年报/Adhoc', 'A', '结果数据需求', 'A4.表-字段', 'ENUM', 0, 19),
('DR20', 'Data Quality Rules', '数据质量检核规则', '需求要求的数据质量规则', 'A', '结果数据需求', 'A4.表-字段', 'TEXT', 0, 20),
('DR21', 'Data desensitization requirements', '数据脱敏要求', '数据脱敏的具体要求', 'A', '结果数据需求', 'A4.表-字段', 'TEXT', 0, 21),
('DR22', 'IRR TOM/RRMS Data Producer', 'RRMS数据提供部门', '数据提供的部门信息', 'A', '结果数据需求', 'A4.表-字段', 'TEXT', 0, 22);

-- ==========================================
-- B类字段：业务解读数据需求 (BDR01-BDR18)
-- ==========================================

INSERT INTO dd_fields_metadata (
    field_code, field_name, field_name_cn, field_desc,
    category_level, category_name, sub_category,
    data_type, is_vectorized, field_order
) VALUES
-- B1.数据需求业务初步解读
('BDR01', 'Data Producing/Processing Department', '数据生产/处理部门', '负责数据生产和处理的部门', 'B', '业务解读', 'B1.数据需求业务初步解读', 'TEXT', 0, 23),
('BDR02', 'Data Producing/Processing Department Representative', '数据生成/处理部门代表', '部门代表人员信息', 'B', '业务解读', 'B1.数据需求业务初步解读', 'TEXT', 0, 24),
('BDR03', 'Data Management Department', '数据管理部门', '负责数据管理的部门', 'B', '业务解读', 'B1.数据需求业务初步解读', 'TEXT', 0, 25),
('BDR04', 'Data Management Department Representative', '数据管理部门代表', '数据管理部门代表人员', 'B', '业务解读', 'B1.数据需求业务初步解读', 'TEXT', 0, 26),
('BDR05', 'Business Process', '业务流程', '相关的业务流程描述', 'B', '业务解读', 'B1.数据需求业务初步解读', 'TEXT', 0, 27),
('BDR06', 'data subject', '数据主题', '数据所属的主题分类', 'B', '业务解读', 'B1.数据需求业务初步解读', 'TEXT', 0, 28),
('BDR07', 'Product/Service', '产品/服务', '相关的产品或服务', 'B', '业务解读', 'B1.数据需求业务初步解读', 'TEXT', 0, 29),

-- B2.数据需求业务深度解读
('BDR08', 'Data Source Type', '数据来源类型', '1-System; 2-Manual; 3-External', 'B', '业务解读', 'B2.数据需求业务深度解读', 'ENUM', 0, 30),
('BDR09', 'Source System Name', '源系统名称', 'for system data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'TEXT', 0, 31),
('BDR10', 'Function/Screen of Source System', '源系统对应功能界面', 'for system data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'TEXT', 0, 32),
('BDR11', 'Function/Screen Description of Source System', '源系统对应功能界面描述', 'for system data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'TEXT', 0, 33),
('BDR12', 'Data Field of Source System', '源系统对应字段', 'for system data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'TEXT', 0, 34),
('BDR13', 'Manual Data Uploading Department', '手工上传数据维护部门', 'for manual data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'TEXT', 0, 35),
('BDR14', 'Manual Uploading Table Name', '手工上传表名称', 'for manual data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'TEXT', 0, 36),
('BDR15', 'External Source Name', '外部数据来源名称', 'for external data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'TEXT', 0, 37),
('BDR16', 'External data element(s)', '外部数据项', 'for external data only', 'B', '业务解读', 'B2.数据需求业务深度解读', 'TEXT', 0, 38),
('BDR17', 'Business Data Mapping Logic', '业务数据匹配逻辑', '业务层面的数据匹配逻辑', 'B', '业务解读', 'B2.数据需求业务深度解读', 'TEXT', 0, 39),
('BDR18', 'Reference Code and Mapping Logic', '码表映射信息', '含版本号的码表映射信息', 'B', '业务解读', 'B2.数据需求业务深度解读', 'TEXT', 0, 40);

-- ==========================================
-- C类字段：IT解读业务数据需求 (SDR01-SDR15)
-- ==========================================

INSERT INTO dd_fields_metadata (
    field_code, field_name, field_name_cn, field_desc,
    category_level, category_name, sub_category,
    data_type, is_vectorized, field_order
) VALUES
-- C1.业务数据需求ITBA初步解读
('SDR01', 'Source Data Layer', '源头数据所在的数据层', 'ODS/BDM/IDM/ADM等', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'ENUM', 0, 41),
('SDR02', 'EIM ID', '源系统唯一标识码', 'for system data only', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'TEXT', 0, 42),
('SDR03', 'Source System Name', '源系统名称', 'for system data only', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'TEXT', 0, 43),
('SDR03.5', 'Source System Name ID', '源系统名称ID', 'for system data only', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'TEXT', 0, 44),
('SDR04', 'Source System ITBA Contact', '源系统数据需求分析代表', '源系统的ITBA联系人', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'TEXT', 0, 45),
('SDR05', 'Source System File Name', '源系统文件名', 'for system data only', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'TEXT', 0, 46),
('SDR06', 'Source System File Description', '源系统文件名描述', 'for system data only', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'TEXT', 0, 47),
('SDR07', 'Table Update Mode', '表更新模式', '全量/增量', 'C', 'IT解读', 'C1.业务数据需求ITBA初步解读', 'ENUM', 0, 48),

-- C2.业务数据需求ITBA深度解读
('SDR08', 'Data Field of Source System', '源系统对应字段', 'for system data only', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'TEXT', 0, 49),
('SDR08.5', 'Data Field ID of Source System', '源系统对应字段ID', 'for system data only', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'TEXT', 0, 50),
('SDR09', 'Data Field Description of Source System', '源系统对应字段描述', 'for system data only, 多个字段情况用分号"；"拼接', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'TEXT', 0, 51),
('SDR10', 'Data Processing Logic', '数据加工逻辑', '数据处理的具体逻辑', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'TEXT', 0, 52),
('SDR11', 'Trusted Source Indicator', '可信数据源标识', '可信/授权/其他/NA', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'ENUM', 0, 53),
('SDR12', 'Related data element used for data producing', '用于生成此数据的其他关联数据项', '关联数据项信息', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'TEXT', 0, 54),
('SDR13', 'Data Type', '数据类型', 'alphabetic, numeric, date等', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'ENUM', 0, 55),
('SDR14', 'Data Element Refresh Frequency', '数据接入频率', '数据刷新的频率', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'TEXT', 0, 56),
('SDR15', 'Remark', '备注', 'IT解读相关备注', 'C', 'IT解读', 'C2.业务数据需求ITBA深度解读', 'TEXT', 0, 57);

-- ==========================================
-- D类字段：指标数据需求 (IDR01-IDR05)
-- ==========================================

INSERT INTO dd_fields_metadata (
    field_code, field_name, field_name_cn, field_desc,
    category_level, category_name, sub_category,
    data_type, is_vectorized, field_order
) VALUES
('IDR01', 'Data Model', '数据模型', '相关的数据模型', 'D', '指标解读', 'D1.技术口径转化指标逻辑', 'TEXT', 0, 58),
('IDR02', 'Data Model ID', '数据模型ID', '数据模型的唯一标识', 'D', '指标解读', 'D1.技术口径转化指标逻辑', 'TEXT', 0, 59),
('IDR03', 'Atomic Indicator Logic', '原子指标逻辑', '原子指标的计算逻辑', 'D', '指标解读', 'D1.技术口径转化指标逻辑', 'TEXT', 0, 60),
('IDR04', 'Calculated Indicator Logic', '计算指标逻辑', '计算指标的逻辑', 'D', '指标解读', 'D1.技术口径转化指标逻辑', 'TEXT', 0, 61),
('IDR05', 'Remark', '备注', '指标解读相关备注', 'D', '指标解读', 'D1.技术口径转化指标逻辑', 'TEXT', 0, 62);


-- ==========================================
-- III. 文档领域 (Doc Domain)
-- Creates: doc_* tables
-- Source: create_rdb_doc.sql
-- ==========================================
-- ==========================================
-- 2. 文档表 (Documents)
-- ==========================================
DROP TABLE IF EXISTS `doc_documents`;
CREATE TABLE `doc_documents` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `doc_id` varchar(64) NOT NULL COMMENT '文档ID，UUID格式',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `doc_name` varchar(200) NOT NULL COMMENT '文档名称',
  `doc_type` varchar(50) DEFAULT NULL COMMENT '文档类型：outside-外部，inside-内部',
  `author` varchar(100) DEFAULT NULL COMMENT '文档作者',
  `vector_similarity_weight` float DEFAULT NULL COMMENT '向量相似度权重',
  `similarity_threshold` float DEFAULT NULL COMMENT '相似度阈值',
  `chunk_nums` int DEFAULT '0' COMMENT '分块总数',
  `parse_type` varchar(50) NOT NULL COMMENT '解析方式',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '解析状态：pending-待处理，processing-处理中，completed-已完成，failed-失败',
  `location` varchar(500) NOT NULL COMMENT '文件存储位置',
  `doc_format` varchar(20) NOT NULL COMMENT '文件格式：pdf,docx,txt等',
  `metadata` text DEFAULT NULL COMMENT '文档源数据',
  `percentage` float DEFAULT '0' COMMENT '分块进度',
  `parse_end_time` datetime DEFAULT NULL COMMENT '解析结束时间',
  `parse_message` text DEFAULT NULL COMMENT '解析状态描述',
  `task_id` varchar(100) DEFAULT NULL COMMENT '任务队列ID',
  `doc_ocr_result_path` varchar(500) DEFAULT NULL COMMENT 'OCR识别结果路径',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否生效：1-生效，0-失效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_doc_id` (`doc_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_doc_name` (`doc_name`),
  KEY `idx_status` (`status`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_doc_documents_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档表';

-- ==========================================
-- 3. 分块表 (Chunks)
-- ==========================================
DROP TABLE IF EXISTS `doc_chunks`;
CREATE TABLE `doc_chunks` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `chunk_id` varchar(64) NOT NULL COMMENT '分块ID',
  `doc_id` varchar(64) NOT NULL COMMENT '文档ID',
  `chapter_layer` varchar(100) DEFAULT NULL COMMENT '文档章节层级信息',
  `parent_id` varchar(64) DEFAULT NULL COMMENT '父分块ID，用于树形结构',
  `sub_chunk_ids` json DEFAULT NULL COMMENT '子分块ID列表，JSON数组',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否生效：1-生效，0-失效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_chunk_id` (`chunk_id`),
  KEY `idx_doc_id` (`doc_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_doc_chunks_document` FOREIGN KEY (`doc_id`) REFERENCES `doc_documents` (`doc_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_doc_chunks_parent` FOREIGN KEY (`parent_id`) REFERENCES `doc_chunks` (`chunk_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分块表';

-- ==========================================
-- 4. 分块信息表 (Chunks Info)
-- ==========================================
DROP TABLE IF EXISTS `doc_chunks_info`;
CREATE TABLE `doc_chunks_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `chunk_info_id` varchar(64) NOT NULL COMMENT '分块信息ID，UUID格式',
  `chunk_id` varchar(64) NOT NULL COMMENT '关联的分块ID',
  `info_type` varchar(50) NOT NULL COMMENT '信息类型：content-内容，keyword-关键词等',
  `info_value` text NOT NULL COMMENT '信息值',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否生效：1-生效，0-失效',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_chunk_info_id` (`chunk_info_id`),
  KEY `idx_chunk_id` (`chunk_id`),
  KEY `idx_info_type` (`info_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_time` (`created_time`),
  CONSTRAINT `fk_doc_chunks_info_chunk` FOREIGN KEY (`chunk_id`) REFERENCES `doc_chunks` (`chunk_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分块信息表';

-- ==========================================
-- 5. 文档别名映射表 (Document Alias)
-- ==========================================
DROP TABLE IF EXISTS `doc_document_alias`;
CREATE TABLE `doc_document_alias` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `doc_id` varchar(64) NOT NULL COMMENT '文档ID',
  `doc_name` varchar(200) NOT NULL COMMENT '原始文件名',
  `cleaned_name` varchar(200) NOT NULL COMMENT '清理后的文档名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_doc_id` (`doc_id`),
  KEY `idx_doc_name` (`doc_name`),
  KEY `idx_cleaned_name` (`cleaned_name`),
  CONSTRAINT `fk_doc_document_alias_doc` FOREIGN KEY (`doc_id`) REFERENCES `doc_documents` (`doc_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档别名映射表';

-- ==========================================
-- 6. 文档搜索历史表 (Document Search History)
-- 描述: 记录知识库的搜索测试历史，简化版本，只保留界面必需字段
-- ==========================================
DROP TABLE IF EXISTS `doc_search_history`;
CREATE TABLE `doc_search_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '搜索历史ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `search_query` text NOT NULL COMMENT '搜索查询内容',
  `search_method` varchar(50) NOT NULL COMMENT '搜索方式：semantic-语义检索，fulltext-全文检索，hybrid-混合检索',
  `citation_limit` int DEFAULT '5000' COMMENT '引用上限',
  `min_similarity` float DEFAULT '0.0' COMMENT '最低相关度',
  `enable_rerank` tinyint(1) DEFAULT '1' COMMENT '是否启用结果重排：1-启用，0-禁用',
  `enable_optimization` tinyint(1) DEFAULT '0' COMMENT '是否启用问题优化：1-启用，0-禁用',
  `search_time_ms` int DEFAULT NULL COMMENT '搜索耗时（毫秒）',
  `total_results` int DEFAULT '0' COMMENT '返回结果总数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '搜索时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_search_method` (`search_method`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_doc_search_history_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档搜索历史表（简化版）';

-- ==========================================
-- 7. 搜索结果详情表 (Search Result Details)
-- 描述: 存储每次搜索的详细结果信息，支持分片排名展示
-- ==========================================
DROP TABLE IF EXISTS `doc_search_results`;
CREATE TABLE `doc_search_results` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `search_history_id` bigint NOT NULL COMMENT '搜索历史ID',
  `chunk_id` varchar(64) NOT NULL COMMENT '分块ID',
  `doc_id` varchar(64) NOT NULL COMMENT '文档ID',
  `doc_name` varchar(200) NOT NULL COMMENT '文档名称',
  `page_number` int DEFAULT NULL COMMENT '页码',
  `content_snippet` text DEFAULT NULL COMMENT '内容片段',
  `overall_rank` int NOT NULL COMMENT '综合排名（#1, #2, #3...）',
  `semantic_score` float DEFAULT NULL COMMENT '语义检索分数',
  `fulltext_score` float DEFAULT NULL COMMENT '全文检索分数',
  `rerank_score` float DEFAULT NULL COMMENT '重排分数',
  `final_score` float NOT NULL COMMENT '最终分数（用于排序）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_search_history_id` (`search_history_id`),
  KEY `idx_chunk_id` (`chunk_id`),
  KEY `idx_doc_id` (`doc_id`),
  KEY `idx_overall_rank` (`overall_rank`),
  CONSTRAINT `fk_doc_search_results_history` FOREIGN KEY (`search_history_id`) REFERENCES `doc_search_history` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_doc_search_results_chunk` FOREIGN KEY (`chunk_id`) REFERENCES `doc_chunks` (`chunk_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_doc_search_results_doc` FOREIGN KEY (`doc_id`) REFERENCES `doc_documents` (`doc_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索结果详情表';

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
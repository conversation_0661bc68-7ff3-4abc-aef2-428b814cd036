"""
RDB统一异常体系

定义数据库抽象层的统一异常，确保不同数据库实现的错误处理一致性
参考SQLAlchemy、Django ORM等框架的异常设计
"""

from typing import Optional, Dict, Any, List
from .types import DatabaseType


# ==================== 基础异常类 ====================

class RDBError(Exception):
    """RDB基础异常类
    
    所有数据库相关异常的基类
    """
    
    def __init__(
        self,
        message: str,
        original_error: Optional[Exception] = None,
        error_code: Optional[str] = None,
        database_type: Optional[DatabaseType] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.original_error = original_error
        self.error_code = error_code
        self.database_type = database_type
        self.context = context or {}
    
    def __str__(self) -> str:
        parts = [self.message]
        
        if self.database_type:
            parts.append(f"Database: {self.database_type}")
        
        if self.error_code:
            parts.append(f"Code: {self.error_code}")
        
        if self.original_error:
            parts.append(f"Original: {self.original_error}")
        
        return " | ".join(parts)


# ==================== 连接相关异常 ====================

class ConnectionError(RDBError):
    """数据库连接异常"""
    pass


class ConnectionTimeoutError(ConnectionError):
    """连接超时异常"""
    pass


class ConnectionPoolError(ConnectionError):
    """连接池异常"""
    
    def __init__(
        self,
        message: str,
        pool_size: Optional[int] = None,
        active_connections: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.pool_size = pool_size
        self.active_connections = active_connections


class AuthenticationError(ConnectionError):
    """认证失败异常"""
    pass


class DatabaseNotFoundError(ConnectionError):
    """数据库不存在异常"""
    pass


# ==================== 查询相关异常 ====================

class QueryError(RDBError):
    """查询异常基类"""
    
    def __init__(
        self,
        message: str,
        query: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.query = query
        self.parameters = parameters


class SQLSyntaxError(QueryError):
    """SQL语法错误"""
    pass


class QueryTimeoutError(QueryError):
    """查询超时异常"""
    pass


class TableNotFoundError(QueryError):
    """表不存在异常"""
    
    def __init__(
        self,
        message: str,
        table_name: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.table_name = table_name


class ColumnNotFoundError(QueryError):
    """列不存在异常"""
    
    def __init__(
        self,
        message: str,
        column_name: Optional[str] = None,
        table_name: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.column_name = column_name
        self.table_name = table_name


# ==================== 数据相关异常 ====================

class DataError(RDBError):
    """数据异常基类"""
    pass


class ValidationError(DataError):
    """数据验证异常"""
    
    def __init__(
        self,
        message: str,
        field_errors: Optional[Dict[str, List[str]]] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.field_errors = field_errors or {}


class IntegrityError(DataError):
    """数据完整性异常"""
    pass


class UniqueConstraintError(IntegrityError):
    """唯一约束异常"""
    
    def __init__(
        self,
        message: str,
        constraint_name: Optional[str] = None,
        columns: Optional[List[str]] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.constraint_name = constraint_name
        self.columns = columns or []


class ForeignKeyConstraintError(IntegrityError):
    """外键约束异常"""
    
    def __init__(
        self,
        message: str,
        constraint_name: Optional[str] = None,
        parent_table: Optional[str] = None,
        child_table: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.constraint_name = constraint_name
        self.parent_table = parent_table
        self.child_table = child_table


class CheckConstraintError(IntegrityError):
    """检查约束异常"""
    
    def __init__(
        self,
        message: str,
        constraint_name: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.constraint_name = constraint_name


class NotNullConstraintError(IntegrityError):
    """非空约束异常"""
    
    def __init__(
        self,
        message: str,
        column_name: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.column_name = column_name


# ==================== 事务相关异常 ====================

class TransactionError(RDBError):
    """事务异常基类"""
    pass


class TransactionRollbackError(TransactionError):
    """事务回滚异常"""
    pass


class DeadlockError(TransactionError):
    """死锁异常"""
    pass


class SerializationError(TransactionError):
    """序列化异常"""
    pass


# ==================== 配置相关异常 ====================

class ConfigurationError(RDBError):
    """配置异常"""
    pass


class UnsupportedDatabaseError(ConfigurationError):
    """不支持的数据库异常"""
    
    def __init__(
        self,
        message: str,
        database_type: Optional[str] = None,
        supported_types: Optional[List[str]] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.unsupported_database_type = database_type
        self.supported_types = supported_types or []


class UnsupportedFeatureError(ConfigurationError):
    """不支持的功能异常"""
    
    def __init__(
        self,
        message: str,
        feature_name: Optional[str] = None,
        database_type: Optional[DatabaseType] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.feature_name = feature_name


# ==================== 操作相关异常 ====================

class OperationError(RDBError):
    """操作异常基类"""
    pass


class BulkOperationError(OperationError):
    """批量操作异常"""
    
    def __init__(
        self,
        message: str,
        failed_records: Optional[List[Dict[str, Any]]] = None,
        success_count: int = 0,
        failure_count: int = 0,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.failed_records = failed_records or []
        self.success_count = success_count
        self.failure_count = failure_count


class MigrationError(OperationError):
    """迁移异常"""
    pass


# ==================== 异常工具函数 ====================

def wrap_database_error(
    original_error: Exception,
    database_type: DatabaseType,
    context: Optional[Dict[str, Any]] = None
) -> RDBError:
    """
    包装数据库特定异常为统一异常
    
    Args:
        original_error: 原始异常
        database_type: 数据库类型
        context: 上下文信息
    
    Returns:
        统一的RDB异常
    """
    error_message = str(original_error)
    error_type = type(original_error).__name__
    
    # 根据异常类型和消息内容判断异常类别
    if "connection" in error_message.lower():
        if "timeout" in error_message.lower():
            return ConnectionTimeoutError(
                error_message,
                original_error=original_error,
                database_type=database_type,
                context=context
            )
        elif "auth" in error_message.lower() or "password" in error_message.lower():
            return AuthenticationError(
                error_message,
                original_error=original_error,
                database_type=database_type,
                context=context
            )
        else:
            return ConnectionError(
                error_message,
                original_error=original_error,
                database_type=database_type,
                context=context
            )
    
    elif "syntax" in error_message.lower() or "sql" in error_message.lower():
        return SQLSyntaxError(
            error_message,
            original_error=original_error,
            database_type=database_type,
            context=context
        )
    
    elif "timeout" in error_message.lower():
        return QueryTimeoutError(
            error_message,
            original_error=original_error,
            database_type=database_type,
            context=context
        )
    
    elif "unique" in error_message.lower() or "duplicate" in error_message.lower():
        return UniqueConstraintError(
            error_message,
            original_error=original_error,
            database_type=database_type,
            context=context
        )
    
    elif "foreign key" in error_message.lower():
        return ForeignKeyConstraintError(
            error_message,
            original_error=original_error,
            database_type=database_type,
            context=context
        )
    
    elif "not null" in error_message.lower():
        return NotNullConstraintError(
            error_message,
            original_error=original_error,
            database_type=database_type,
            context=context
        )
    
    elif "deadlock" in error_message.lower():
        return DeadlockError(
            error_message,
            original_error=original_error,
            database_type=database_type,
            context=context
        )
    
    else:
        # 默认返回通用查询异常
        return QueryError(
            error_message,
            original_error=original_error,
            database_type=database_type,
            context=context
        )


# ==================== 导出异常 ====================

__all__ = [
    # 基础异常
    "RDBError",
    
    # 连接异常
    "ConnectionError",
    "ConnectionTimeoutError", 
    "ConnectionPoolError",
    "AuthenticationError",
    "DatabaseNotFoundError",
    
    # 查询异常
    "QueryError",
    "SQLSyntaxError",
    "QueryTimeoutError",
    "TableNotFoundError",
    "ColumnNotFoundError",
    
    # 数据异常
    "DataError",
    "ValidationError",
    "IntegrityError",
    "UniqueConstraintError",
    "ForeignKeyConstraintError",
    "CheckConstraintError",
    "NotNullConstraintError",
    
    # 事务异常
    "TransactionError",
    "TransactionRollbackError",
    "DeadlockError",
    "SerializationError",
    
    # 配置异常
    "ConfigurationError",
    "UnsupportedDatabaseError",
    "UnsupportedFeatureError",
    
    # 操作异常
    "OperationError",
    "BulkOperationError",
    "MigrationError",
    
    # 工具函数
    "wrap_database_error",
]

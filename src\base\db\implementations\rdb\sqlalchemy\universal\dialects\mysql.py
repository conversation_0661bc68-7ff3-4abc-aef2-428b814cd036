

from typing import Dict, Any, List, Optional, Tuple
from .base import DatabaseDialect, DatabaseFeatures, FeatureSupport


class MySQLDialect(DatabaseDialect):
    """MySQL database dialect implementation"""
    
    @property
    def name(self) -> str:
        return "mysql"
    
    @property
    def description(self) -> str:
        return "MySQL Database (5.6+)"
    
    @property
    def default_port(self) -> Optional[int]:
        return 3306
    
    @property
    def default_driver(self) -> str:
        return "pymysql"
    
    @property
    def async_driver(self) -> Optional[str]:
        return "aiomysql"
    
    @property
    def features(self) -> DatabaseFeatures:
        return DatabaseFeatures(
            json_support=FeatureSupport.FULL,      # MySQL 5.7+
            array_support=FeatureSupport.NONE,     # No native array support
            window_functions=FeatureSupport.FULL,  # MySQL 8.0+
            cte_support=FeatureSupport.FULL,       # MySQL 8.0+
            full_text_search=FeatureSupport.FULL,  # Native FULLTEXT
            partitioning=FeatureSupport.FULL,      # Native partitioning
            upsert_support=FeatureSupport.FULL,    # ON DUPLICATE KEY UPDATE
            returning_clause=FeatureSupport.NONE,  # No RETURNING clause
            bulk_insert=FeatureSupport.FULL,       # INSERT IGNORE, bulk inserts
            async_support=FeatureSupport.FULL      # aiomysql
        )
    
    # ==================== Query Building ====================
    
    def build_limit_clause(self, limit: int, offset: Optional[int] = None) -> str:
        """Build MySQL LIMIT clause"""
        if offset is not None:
            return f"LIMIT {offset}, {limit}"
        return f"LIMIT {limit}"
    
    def build_upsert_statement(
        self,
        table_name: str,
        data: Dict[str, Any],
        conflict_columns: List[str],
        update_columns: Optional[List[str]] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """Build MySQL ON DUPLICATE KEY UPDATE statement"""
        
        # Determine columns to update
        if update_columns is None:
            # Update all columns except conflict columns
            update_columns = [col for col in data.keys() if col not in conflict_columns]
        
        # Build INSERT part
        columns = list(data.keys())
        placeholders = ", ".join([f":{col}" for col in columns])
        columns_clause = ", ".join([self.quote_identifier(col) for col in columns])
        
        insert_sql = f"INSERT INTO {self.quote_identifier(table_name)} ({columns_clause}) VALUES ({placeholders})"
        
        # Build ON DUPLICATE KEY UPDATE part
        if update_columns:
            update_clauses = []
            for col in update_columns:
                quoted_col = self.quote_identifier(col)
                update_clauses.append(f"{quoted_col} = VALUES({quoted_col})")
            
            update_sql = " ON DUPLICATE KEY UPDATE " + ", ".join(update_clauses)
            full_sql = insert_sql + update_sql
        else:
            full_sql = insert_sql
        
        return full_sql, data
    
    def build_bulk_insert_statement(
        self,
        table_name: str,
        columns: List[str],
        batch_size: int,
        ignore_conflicts: bool = False
    ) -> str:
        """Build MySQL bulk insert statement"""
        placeholders = ", ".join(["%s"] * len(columns))
        values_clause = ", ".join([f"({placeholders})"] * batch_size)
        columns_clause = ", ".join([self.quote_identifier(col) for col in columns])
        
        ignore_clause = "IGNORE " if ignore_conflicts else ""
        
        return f"INSERT {ignore_clause}INTO {self.quote_identifier(table_name)} ({columns_clause}) VALUES {values_clause}"
    
    # ==================== Identifier Handling ====================
    
    def get_identifier_quote_char(self) -> str:
        """MySQL uses backticks for identifier quoting"""
        return "`"
    
    def get_parameter_placeholder(self) -> str:
        """MySQL uses %s for parameter placeholders"""
        return "%s"
    
    # ==================== Data Type Mapping ====================
    
    def map_python_type_to_sql(self, python_type: type) -> str:
        """Map Python types to MySQL SQL types"""
        type_mapping = {
            int: "INT",
            float: "DOUBLE",
            str: "VARCHAR(255)",
            bool: "TINYINT(1)",
            bytes: "BLOB",
            dict: "JSON",  # MySQL 5.7+
            list: "JSON",  # MySQL 5.7+
        }
        
        return type_mapping.get(python_type, "TEXT")
    
    def get_boolean_literal(self, value: bool) -> str:
        """MySQL boolean literals"""
        return "1" if value else "0"
    
    # ==================== MySQL-Specific Features ====================
    
    def get_ignore_conflicts_clause(self) -> str:
        """MySQL IGNORE clause for INSERT"""
        return "IGNORE "
    
    def build_fulltext_search_query(
        self,
        table_name: str,
        search_columns: List[str],
        search_term: str,
        mode: str = "NATURAL"
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Build MySQL FULLTEXT search query
        
        Args:
            table_name: Target table
            search_columns: Columns to search in
            search_term: Search term
            mode: Search mode (NATURAL, BOOLEAN, QUERY_EXPANSION)
        
        Returns:
            Tuple of (SQL, parameters)
        """
        columns_clause = ", ".join([self.quote_identifier(col) for col in search_columns])
        
        if mode.upper() == "BOOLEAN":
            match_clause = f"MATCH({columns_clause}) AGAINST (:search_term IN BOOLEAN MODE)"
        elif mode.upper() == "QUERY_EXPANSION":
            match_clause = f"MATCH({columns_clause}) AGAINST (:search_term WITH QUERY EXPANSION)"
        else:  # NATURAL
            match_clause = f"MATCH({columns_clause}) AGAINST (:search_term IN NATURAL LANGUAGE MODE)"
        
        sql = f"SELECT *, {match_clause} AS relevance FROM {self.quote_identifier(table_name)} WHERE {match_clause}"
        
        return sql, {"search_term": search_term}
    
    def build_json_extract_query(
        self,
        table_name: str,
        json_column: str,
        json_path: str,
        where_clause: Optional[str] = None
    ) -> str:
        """
        Build MySQL JSON extraction query
        
        Args:
            table_name: Target table
            json_column: JSON column name
            json_path: JSON path expression
            where_clause: Optional WHERE clause
        
        Returns:
            SQL query
        """
        json_col = self.quote_identifier(json_column)
        table = self.quote_identifier(table_name)
        
        sql = f"SELECT JSON_EXTRACT({json_col}, '{json_path}') AS extracted_value FROM {table}"
        
        if where_clause:
            sql += f" WHERE {where_clause}"
        
        return sql
    
    def get_auto_increment_info(self, table_name: str) -> str:
        """Get AUTO_INCREMENT information for a table"""
        return f"""
        SELECT 
            AUTO_INCREMENT 
        FROM 
            INFORMATION_SCHEMA.TABLES 
        WHERE 
            TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = '{table_name}'
        """
    
    def get_table_engine_info(self, table_name: str) -> str:
        """Get storage engine information for a table"""
        return f"""
        SELECT 
            ENGINE,
            TABLE_COLLATION,
            TABLE_COMMENT
        FROM 
            INFORMATION_SCHEMA.TABLES 
        WHERE 
            TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = '{table_name}'
        """
    
    # ==================== Connection Handling ====================
    
    def build_connection_url(
        self,
        host: str,
        database: str,
        username: str,
        password: str,
        port: Optional[int] = None,
        use_async: bool = True,
        charset: str = "utf8mb4",
        **kwargs
    ) -> str:
        """Build MySQL connection URL with charset support"""
        if port is None:
            port = self.default_port
        
        driver = self.async_driver if (use_async and self.async_driver) else self.default_driver
        scheme = f"{self.name}+{driver}"
        
        # Add charset to kwargs
        kwargs['charset'] = charset
        
        url = f"{scheme}://{username}:{password}@{host}:{port}/{database}"
        
        # Add query parameters
        if kwargs:
            params = "&".join([f"{k}={v}" for k, v in kwargs.items()])
            url += f"?{params}"
        
        return url
    
    def get_recommended_engine_options(self) -> Dict[str, Any]:
        """Get recommended SQLAlchemy engine options for MySQL"""
        return {
            'pool_recycle': 3600,  # Recycle connections every hour
            'pool_pre_ping': True,  # Validate connections before use
            'isolation_level': 'READ_COMMITTED',
            'connect_args': {
                'charset': 'utf8mb4',
                'use_unicode': True,
                'autocommit': False
            }
        }

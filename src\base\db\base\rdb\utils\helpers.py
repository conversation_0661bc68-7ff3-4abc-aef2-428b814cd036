"""
RDB辅助函数

提供各种实用的辅助函数
"""

import re
import urllib.parse
from typing import Any, Dict, List, Optional, Tuple, Union
from urllib.parse import urlparse, parse_qs

from ..core.types import DatabaseType
from ..core.models import ConnectionConfig
from ..core.exceptions import ValidationError, ConfigurationError


def format_sql(sql: str, parameters: Optional[Dict[str, Any]] = None) -> str:
    """格式化SQL语句
    
    Args:
        sql: SQL语句
        parameters: 参数字典
    
    Returns:
        格式化后的SQL语句
    """
    if not sql:
        return sql
    
    # 移除多余的空白字符
    formatted_sql = re.sub(r'\s+', ' ', sql.strip())
    
    # 如果有参数，进行简单的替换（仅用于日志显示）
    if parameters:
        for key, value in parameters.items():
            placeholder = f":{key}"
            if placeholder in formatted_sql:
                if isinstance(value, str):
                    formatted_sql = formatted_sql.replace(placeholder, f"'{value}'")
                elif value is None:
                    formatted_sql = formatted_sql.replace(placeholder, "NULL")
                else:
                    formatted_sql = formatted_sql.replace(placeholder, str(value))
    
    return formatted_sql


def validate_connection_string(connection_string: str) -> bool:
    """验证连接字符串格式
    
    Args:
        connection_string: 连接字符串
    
    Returns:
        是否有效
    """
    if not connection_string:
        return False
    
    try:
        parsed = urlparse(connection_string)
        
        # 检查必需的组件
        if not parsed.scheme:
            return False
        
        if not parsed.hostname:
            return False
        
        # 检查支持的数据库类型
        supported_schemes = ['mysql', 'postgresql', 'postgres', 'sqlite', 'oracle', 'mssql']
        if parsed.scheme.lower() not in supported_schemes:
            return False
        
        return True
        
    except Exception:
        return False


def parse_connection_string(connection_string: str) -> Dict[str, Any]:
    """解析连接字符串
    
    Args:
        connection_string: 连接字符串
        
    Returns:
        解析后的连接参数
        
    Raises:
        ValidationError: 连接字符串格式无效
    """
    if not validate_connection_string(connection_string):
        raise ValidationError(f"Invalid connection string: {connection_string}")
    
    try:
        parsed = urlparse(connection_string)
        
        # 确定数据库类型
        scheme_mapping = {
            'mysql': DatabaseType.MYSQL,
            'postgresql': DatabaseType.POSTGRESQL,
            'postgres': DatabaseType.POSTGRESQL,
            'sqlite': DatabaseType.SQLITE,
            'oracle': DatabaseType.ORACLE,
            'mssql': DatabaseType.SQLSERVER,
        }
        
        database_type = scheme_mapping.get(parsed.scheme.lower())
        if not database_type:
            raise ValidationError(f"Unsupported database type: {parsed.scheme}")
        
        # 解析基本参数
        result = {
            'database_type': database_type,
            'host': parsed.hostname,
            'port': parsed.port,
            'database': parsed.path.lstrip('/') if parsed.path else None,
            'username': parsed.username,
            'password': parsed.password,
        }
        
        # 解析查询参数
        if parsed.query:
            query_params = parse_qs(parsed.query)
            for key, values in query_params.items():
                if values:
                    # 取第一个值
                    value = values[0]
                    
                    # 尝试转换类型
                    if value.lower() in ('true', 'false'):
                        result[key] = value.lower() == 'true'
                    elif value.isdigit():
                        result[key] = int(value)
                    elif '.' in value and value.replace('.', '').isdigit():
                        result[key] = float(value)
                    else:
                        result[key] = value
        
        return result
        
    except Exception as e:
        raise ValidationError(f"Failed to parse connection string: {e}")


def create_connection_config(
    database_type: Union[str, DatabaseType],
    host: str,
    database: str,
    username: str,
    password: str,
    port: Optional[int] = None,
    **kwargs
) -> ConnectionConfig:
    """创建连接配置
    
    Args:
        database_type: 数据库类型
        host: 主机名
        database: 数据库名
        username: 用户名
        password: 密码
        port: 端口号
        **kwargs: 其他配置参数
    
    Returns:
        连接配置对象
    """
    # 转换数据库类型
    if isinstance(database_type, str):
        try:
            database_type = DatabaseType(database_type.lower())
        except ValueError:
            raise ValidationError(f"Unsupported database type: {database_type}")
    
    # 设置默认端口
    if port is None:
        default_ports = {
            DatabaseType.MYSQL: 3306,
            DatabaseType.POSTGRESQL: 5432,
            DatabaseType.SQLITE: None,
            DatabaseType.ORACLE: 1521,
            DatabaseType.SQLSERVER: 1433,
        }
        port = default_ports.get(database_type)
    
    # 创建配置
    config = ConnectionConfig(
        host=host,
        port=port or 0,  # SQLite不需要端口
        database=database,
        username=username,
        password=password,
        **kwargs
    )
    
    return config


def merge_configs(base_config: ConnectionConfig, override_config: Dict[str, Any]) -> ConnectionConfig:
    """合并配置
    
    Args:
        base_config: 基础配置
        override_config: 覆盖配置
    
    Returns:
        合并后的配置
    """
    # 转换为字典
    base_dict = {
        'host': base_config.host,
        'port': base_config.port,
        'database': base_config.database,
        'username': base_config.username,
        'password': base_config.password,
        'charset': base_config.charset,
        'timezone': base_config.timezone,
        'connect_timeout': base_config.connect_timeout,
        'read_timeout': base_config.read_timeout,
        'write_timeout': base_config.write_timeout,
        'ssl_enabled': base_config.ssl_enabled,
        'ssl_cert': base_config.ssl_cert,
        'ssl_key': base_config.ssl_key,
        'ssl_ca': base_config.ssl_ca,
        'pool_size': base_config.pool_size,
        'max_overflow': base_config.max_overflow,
        'pool_timeout': base_config.pool_timeout,
        'pool_recycle': base_config.pool_recycle,
        'echo': base_config.echo,
        'autocommit': base_config.autocommit,
        'isolation_level': base_config.isolation_level,
    }
    
    # 应用覆盖
    base_dict.update(override_config)
    
    # 创建新配置
    return ConnectionConfig(**base_dict)


def build_connection_string(config: ConnectionConfig, database_type: DatabaseType) -> str:
    """构建连接字符串
    
    Args:
        config: 连接配置
        database_type: 数据库类型
    
    Returns:
        连接字符串
    """
    # 确定scheme
    scheme_mapping = {
        DatabaseType.MYSQL: 'mysql',
        DatabaseType.POSTGRESQL: 'postgresql',
        DatabaseType.SQLITE: 'sqlite',
        DatabaseType.ORACLE: 'oracle',
        DatabaseType.SQLSERVER: 'mssql',
    }
    
    scheme = scheme_mapping.get(database_type)
    if not scheme:
        raise ConfigurationError(f"Unsupported database type: {database_type}")
    
    # SQLite特殊处理
    if database_type == DatabaseType.SQLITE:
        return f"sqlite:///{config.database}"
    
    # 构建URL
    url_parts = [scheme, '://']
    
    # 用户名和密码
    if config.username:
        url_parts.append(urllib.parse.quote(config.username))
        if config.password:
            url_parts.append(':')
            url_parts.append(urllib.parse.quote(config.password))
        url_parts.append('@')
    
    # 主机和端口
    url_parts.append(config.host)
    if config.port:
        url_parts.append(':')
        url_parts.append(str(config.port))
    
    # 数据库名
    if config.database:
        url_parts.append('/')
        url_parts.append(urllib.parse.quote(config.database))
    
    # 查询参数
    query_params = []
    
    if config.charset and config.charset != 'utf8mb4':
        query_params.append(f"charset={config.charset}")
    
    if config.timezone:
        query_params.append(f"timezone={urllib.parse.quote(config.timezone)}")
    
    if config.ssl_enabled:
        query_params.append("ssl=true")
    
    if query_params:
        url_parts.append('?')
        url_parts.append('&'.join(query_params))
    
    return ''.join(url_parts)


def sanitize_table_name(table_name: str) -> str:
    """清理表名
    
    Args:
        table_name: 原始表名
    
    Returns:
        清理后的表名
    """
    if not table_name:
        raise ValidationError("Table name cannot be empty")
    
    # 移除危险字符
    sanitized = re.sub(r'[^\w\.]', '', table_name)
    
    # 检查长度
    if len(sanitized) > 64:
        raise ValidationError(f"Table name too long: {table_name}")
    
    # 检查格式
    if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$', sanitized):
        raise ValidationError(f"Invalid table name format: {table_name}")
    
    return sanitized


def sanitize_column_name(column_name: str) -> str:
    """清理列名
    
    Args:
        column_name: 原始列名
    
    Returns:
        清理后的列名
    """
    if not column_name:
        raise ValidationError("Column name cannot be empty")
    
    # 移除危险字符
    sanitized = re.sub(r'[^\w\.]', '', column_name)
    
    # 检查长度
    if len(sanitized) > 64:
        raise ValidationError(f"Column name too long: {column_name}")
    
    # 检查格式
    if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$', sanitized):
        raise ValidationError(f"Invalid column name format: {column_name}")
    
    return sanitized


def escape_like_pattern(pattern: str) -> str:
    """转义LIKE模式中的特殊字符
    
    Args:
        pattern: 原始模式
    
    Returns:
        转义后的模式
    """
    if not pattern:
        return pattern
    
    # 转义特殊字符
    escaped = pattern.replace('\\', '\\\\')  # 先转义反斜杠
    escaped = escaped.replace('%', '\\%')    # 转义百分号
    escaped = escaped.replace('_', '\\_')    # 转义下划线
    
    return escaped


def generate_placeholder(index: int, database_type: DatabaseType) -> str:
    """生成参数占位符
    
    Args:
        index: 参数索引
        database_type: 数据库类型
    
    Returns:
        占位符字符串
    """
    if database_type in (DatabaseType.MYSQL, DatabaseType.SQLITE):
        return "?"
    elif database_type == DatabaseType.POSTGRESQL:
        return f"${index + 1}"
    elif database_type in (DatabaseType.ORACLE, DatabaseType.SQLSERVER):
        return "?"
    else:
        return "?"


def quote_identifier(identifier: str, database_type: DatabaseType) -> str:
    """引用SQL标识符
    
    Args:
        identifier: 标识符
        database_type: 数据库类型
    
    Returns:
        引用后的标识符
    """
    if not identifier:
        return identifier
    
    # 不同数据库的引用字符
    quote_chars = {
        DatabaseType.MYSQL: '`',
        DatabaseType.POSTGRESQL: '"',
        DatabaseType.SQLITE: '"',
        DatabaseType.ORACLE: '"',
        DatabaseType.SQLSERVER: '[',
    }
    
    quote_char = quote_chars.get(database_type, '"')
    
    if database_type == DatabaseType.SQLSERVER:
        return f"[{identifier}]"
    else:
        return f"{quote_char}{identifier}{quote_char}"

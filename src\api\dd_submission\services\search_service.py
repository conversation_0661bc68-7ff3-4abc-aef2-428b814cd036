#!/usr/bin/env python3
"""
搜索服务

实现三层搜索机制和四级筛选逻辑：
1. 精确匹配搜索
2. 混合搜索（向量+模糊）
3. TFIDF部门推荐
4. 四级筛选：套系→报告类型→提交类型→DR01→评分
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from collections import Counter
import math

logger = logging.getLogger(__name__)


class SearchService:
    """搜索服务"""
    
    def __init__(self, rdb_client, vdb_client=None):
        """初始化搜索服务"""
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        
        # 搜索配置
        self.hybrid_search_threshold = 0.7
        self.set_type_mapping = {
            'survey': 'non-standard',  # 非标准套系
            '1104': 'standard',        # 标准套系
            'djz': 'standard',         # 标准套系
        }
    
    async def execute_three_layer_search(self, 
                                       dr09: str, 
                                       dr17: str, 
                                       search_item: Dict[str, Any]) -> Dict[str, Any]:
        """执行三层搜索"""
        try:
            # 第一层：精确匹配搜索
            exact_result = await self._exact_match_search(dr09, dr17)
            if exact_result['found']:
                if len(exact_result['matches']) == 1:
                    return {
                        'search_type': 'exact',
                        'result': exact_result['matches'][0],
                        'multiple_results': False
                    }
                else:
                    return {
                        'search_type': 'exact',
                        'results': exact_result['matches'],
                        'multiple_results': True
                    }
            
            # 第二层：混合搜索
            hybrid_result = await self._hybrid_search(dr09, dr17)
            if hybrid_result['found'] and hybrid_result['max_score'] > self.hybrid_search_threshold:
                # 需要四级筛选
                filtered_result = await self._apply_four_level_filtering(
                    hybrid_result['matches'], search_item
                )
                return {
                    'search_type': 'hybrid',
                    'result': filtered_result
                }
            
            # 第三层：TFIDF推荐
            tfidf_result = await self._tfidf_recommendation(dr09, dr17)
            return {
                'search_type': 'tfidf',
                'result': tfidf_result
            }
            
        except Exception as e:
            logger.error(f"三层搜索执行失败: {e}")
            # 降级到TFIDF推荐
            tfidf_result = await self._tfidf_recommendation(dr09, dr17)
            return {
                'search_type': 'tfidf',
                'result': tfidf_result
            }
    
    async def _exact_match_search(self, dr09: str, dr17: str) -> Dict[str, Any]:
        """精确匹配搜索"""
        try:
            sql = """
            SELECT submission_id, dr09, dr17, report_data_id, dr22 as dept_id, type
            FROM dd_submission_data
            WHERE dr09 = %s AND dr17 = %s
            """

            # 处理不同客户端的参数格式
            try:
                result = await self.rdb_client.afetch_all(sql, [dr09, dr17])
            except Exception as e:
                if "List argument must consist only of tuples or dictionaries" in str(e):
                    # 尝试使用字典参数
                    sql = """
                    SELECT submission_id, dr09, dr17, report_data_id, dr22 as dept_id, type
                    FROM dd_submission_data
                    WHERE dr09 = :dr09 AND dr17 = :dr17
                    """
                    result = await self.rdb_client.afetch_all(sql, {"dr09": dr09, "dr17": dr17})
                else:
                    raise e

            # 处理不同的返回格式
            if isinstance(result, list):
                if result:
                    return {
                        'found': True,
                        'matches': result
                    }
                else:
                    return {
                        'found': False,
                        'matches': []
                    }
            elif hasattr(result, 'success'):
                if result.success and result.data:
                    return {
                        'found': True,
                        'matches': result.data
                    }
                else:
                    return {
                        'found': False,
                        'matches': []
                    }
            else:
                return {
                    'found': False,
                    'matches': []
                }
                
        except Exception as e:
            logger.error(f"精确匹配搜索失败: {e}")
            return {
                'found': False,
                'matches': []
            }
    
    async def _hybrid_search(self, dr09: str, dr17: str) -> Dict[str, Any]:
        """混合搜索（向量+模糊）"""
        try:
            # 1. NLP分词处理
            dr17_tokens = self._nlp_tokenize(dr17)
            
            # 2. 模糊搜索（使用LIKE查询模拟）
            fuzzy_results = await self._fuzzy_search(dr09, dr17, dr17_tokens)
            
            # 3. 计算相似度分数
            scored_results = []
            for result in fuzzy_results:
                score = self._calculate_similarity_score(
                    dr09, dr17, result['dr09'], result['dr17']
                )
                result['score'] = score
                scored_results.append(result)
            
            # 4. 按分数排序
            scored_results.sort(key=lambda x: x['score'], reverse=True)
            
            max_score = scored_results[0]['score'] if scored_results else 0.0
            
            return {
                'found': len(scored_results) > 0,
                'matches': scored_results,
                'max_score': max_score
            }
            
        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            return {
                'found': False,
                'matches': [],
                'max_score': 0.0
            }
    
    def _nlp_tokenize(self, text: str) -> List[str]:
        """NLP分词处理（简化版）"""
        # 简化的中文分词：按字符分割，然后组合成词
        # 首先按标点符号分割
        segments = re.split(r'[，。、；：！？\s]+', text)

        tokens = []
        for segment in segments:
            if not segment:
                continue

            # 对每个段落进行进一步分词
            # 提取连续的中文字符作为词
            chinese_words = re.findall(r'[\u4e00-\u9fff]{2,}', segment)
            tokens.extend(chinese_words)

            # 提取单个中文字符
            single_chars = re.findall(r'[\u4e00-\u9fff]', segment)
            tokens.extend(single_chars)

            # 提取英文单词和数字
            english_words = re.findall(r'[a-zA-Z0-9]+', segment)
            tokens.extend(english_words)

        # 过滤停用词和短词
        stop_words = {'的', '和', '与', '或', '等', '及', '以及', '包括', '含有', '了', '在', '是', '有', '为'}
        filtered_tokens = [token for token in tokens if len(token) >= 1 and token not in stop_words]

        # 去重并保持顺序
        seen = set()
        unique_tokens = []
        for token in filtered_tokens:
            if token not in seen:
                seen.add(token)
                unique_tokens.append(token)

        return unique_tokens
    
    async def _fuzzy_search(self, dr09: str, dr17: str, dr17_tokens: List[str]) -> List[Dict[str, Any]]:
        """模糊搜索"""
        try:
            # 构建模糊查询条件
            like_conditions = []
            params = {}
            
            # dr09的模糊匹配
            like_conditions.append("dr09 LIKE :dr09")
            params["dr09"] = f"%{dr09}%"
            
            # dr17的关键词匹配
            for idx, token in enumerate(dr17_tokens[:5]):  # 限制关键词数量
                key = f"dr17_{idx}"
                like_conditions.append(f"dr17 LIKE :{key}")
                params[key] = f"%{token}%"
            
            if not like_conditions:
                return []
            
            sql = f"""
            SELECT submission_id, dr09, dr17, report_data_id, dr22 as dept_id, type
            FROM dd_submission_data
            WHERE {' OR '.join(like_conditions)}
            LIMIT 20
            """
            
            result = await self.rdb_client.afetch_all(sql, params)
            
            if result.data:
                return result.data
            else:
                return []
                
        except Exception as e:
            logger.error(f"模糊搜索失败: {e}")
            return []
    
    def _calculate_similarity_score(self, dr09_1: str, dr17_1: str, dr09_2: str, dr17_2: str) -> float:
        """计算相似度分数"""
        try:
            # 简化的相似度计算
            dr09_score = self._string_similarity(dr09_1, dr09_2)
            dr17_score = self._string_similarity(dr17_1, dr17_2)
            
            # 加权平均
            total_score = (dr09_score * 0.3 + dr17_score * 0.7)
            return min(total_score, 1.0)
            
        except Exception as e:
            logger.error(f"计算相似度失败: {e}")
            return 0.0
    
    def _string_similarity(self, str1: str, str2: str) -> float:
        """字符串相似度计算（简化版Jaccard相似度）"""
        if not str1 or not str2:
            return 0.0
        
        # 转换为字符集合
        set1 = set(str1)
        set2 = set(str2)
        
        # Jaccard相似度
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    async def _apply_four_level_filtering(self, 
                                        candidates: List[Dict], 
                                        search_item: Dict[str, Any]) -> Dict[str, Any]:
        """应用四级筛选"""
        try:
            if not candidates:
                return {}
            
            if len(candidates) == 1:
                return candidates[0]
            
            # 第一级：套系匹配筛选
            step1_result = await self._filter_by_set_type(candidates, search_item)
            if len(step1_result) == 1:
                return step1_result[0]
            elif len(step1_result) == 0:
                return self._select_by_score(candidates)
            
            # 第二级：报告类型筛选
            step2_result = await self._filter_by_report_type(step1_result, search_item)
            if len(step2_result) == 1:
                return step2_result[0]
            elif len(step2_result) == 0:
                return self._select_by_score(step1_result)
            
            # 第三级：提交类型筛选
            step3_result = await self._filter_by_submission_type(step2_result, search_item)
            if len(step3_result) == 1:
                return step3_result[0]
            elif len(step3_result) == 0:
                return self._select_by_score(step2_result)
            
            # 第四级：DR01字段筛选
            step4_result = await self._filter_by_dr01_field(step3_result, search_item)
            if len(step4_result) == 1:
                return step4_result[0]
            elif len(step4_result) == 0:
                return self._select_by_score(step3_result)
            
            # 最终评分筛选
            return self._select_by_score(step4_result)
            
        except Exception as e:
            logger.error(f"四级筛选失败: {e}")
            return self._select_by_score(candidates)
    
    async def _filter_by_set_type(self, candidates: List[Dict], search_item: Dict) -> List[Dict]:
        """第一级：套系匹配筛选"""
        try:
            # 获取候选项的套系信息
            enriched_candidates = []
            for candidate in candidates:
                report_data_id = candidate.get('report_data_id')
                if report_data_id:
                    set_info = await self._get_set_info_by_report_data_id(report_data_id)
                    candidate['set_type'] = self._classify_set_type(set_info.get('set', ''))
                    enriched_candidates.append(candidate)
            
            # 确定目标套系类型（简化逻辑：优先选择标准套系）
            target_set_type = 'standard'
            
            # 筛选同类套系
            filtered = [c for c in enriched_candidates if c.get('set_type') == target_set_type]
            
            return filtered if filtered else enriched_candidates
            
        except Exception as e:
            logger.error(f"套系筛选失败: {e}")
            return candidates
    
    async def _get_set_info_by_report_data_id(self, report_data_id: str) -> Dict[str, Any]:
        """根据报告数据ID获取套系信息（使用命名参数）"""
        try:
            sql = "SELECT `set`, report_type FROM dd_report_data WHERE id = :report_data_id"
            result = await self.rdb_client.afetch_one(sql, {"report_data_id": report_data_id})
            
            if result:
                return result
            else:
                return {}
                
        except Exception as e:
            logger.error(f"获取套系信息失败: {e}")
            return {}
    
    def _classify_set_type(self, set_value: str) -> str:
        """分类套系类型"""
        return self.set_type_mapping.get(set_value, 'standard')
    
    async def _filter_by_report_type(self, candidates: List[Dict], search_item: Dict) -> List[Dict]:
        """第二级：报告类型筛选"""
        try:
            # 简化逻辑：优先选择detail类型
            target_report_type = 'detail'
            
            # 获取报告类型信息
            enriched_candidates = []
            for candidate in candidates:
                report_data_id = candidate.get('report_data_id')
                if report_data_id:
                    set_info = await self._get_set_info_by_report_data_id(report_data_id)
                    candidate['report_type'] = set_info.get('report_type', '')
                    enriched_candidates.append(candidate)
            
            # 筛选匹配的报告类型
            filtered = [c for c in enriched_candidates if c.get('report_type') == target_report_type]
            
            return filtered if filtered else enriched_candidates
            
        except Exception as e:
            logger.error(f"报告类型筛选失败: {e}")
            return candidates
    
    async def _filter_by_submission_type(self, candidates: List[Dict], search_item: Dict) -> List[Dict]:
        """第三级：提交类型筛选"""
        try:
            target_submission_type = search_item.get('submission_type', '')
            
            # 筛选匹配的提交类型
            filtered = [c for c in candidates if c.get('type') == target_submission_type]
            
            return filtered if filtered else candidates
            
        except Exception as e:
            logger.error(f"提交类型筛选失败: {e}")
            return candidates
    
    async def _filter_by_dr01_field(self, candidates: List[Dict], search_item: Dict) -> List[Dict]:
        """第四级：DR01字段筛选"""
        try:
            target_dr01 = search_item.get('dr01', '')
            
            # 筛选匹配的DR01字段
            filtered = [c for c in candidates if c.get('dr01') == target_dr01]
            
            return filtered if filtered else candidates
            
        except Exception as e:
            logger.error(f"DR01字段筛选失败: {e}")
            return candidates
    
    def _select_by_score(self, candidates: List[Dict]) -> Dict[str, Any]:
        """评分筛选"""
        if not candidates:
            return {}
        
        # 选择最高分的候选项
        best_candidate = max(candidates, key=lambda x: x.get('score', 0.0))
        return best_candidate
    
    async def _tfidf_recommendation(self, dr09: str, dr17: str) -> Dict[str, Any]:
        """TFIDF部门推荐"""
        try:
            # 1. 获取部门数据
            departments = await self._get_departments_for_tfidf()
            
            # 2. 分词处理
            dr17_tokens = self._nlp_tokenize(dr17)
            search_terms = [dr09] + dr17_tokens
            
            # 3. TFIDF计算
            tfidf_scores = []
            for dept in departments:
                dept_tokens = self._nlp_tokenize(dept.get('dept_desc', ''))
                dept_terms = [dept.get('dept_name', '')] + dept_tokens
                
                score = self._calculate_tfidf_score(search_terms, dept_terms)
                tfidf_scores.append((dept['dept_id'], score))
            
            # 4. 选择最高分部门
            if tfidf_scores:
                best_dept_id, best_score = max(tfidf_scores, key=lambda x: x[1])
                
                # 5. 获取部门关联的table_ids
                table_ids = await self._get_department_table_relations(best_dept_id)
                
                return {
                    'dept_id': best_dept_id,
                    'tfidf_score': best_score,
                    'table_ids': table_ids
                }
            else:
                return {
                    'dept_id': 'DEFAULT_DEPT',
                    'tfidf_score': 0.0,
                    'table_ids': []
                }
                
        except Exception as e:
            logger.error(f"TFIDF推荐失败: {e}")
            return {
                'dept_id': 'DEFAULT_DEPT',
                'tfidf_score': 0.0,
                'table_ids': []
            }
    
    async def _get_departments_for_tfidf(self) -> List[Dict[str, Any]]:
        """获取部门数据用于TFIDF"""
        try:
            sql = "SELECT dept_id, dept_name, dept_desc FROM dd_departments"
            result = await self.rdb_client.afetch_all(sql)
            
            if result.data:
                return result.data
            else:
                return []
                
        except Exception as e:
            logger.error(f"获取部门数据失败: {e}")
            return []
    
    def _calculate_tfidf_score(self, search_terms: List[str], dept_terms: List[str]) -> float:
        """计算TFIDF分数（简化版）"""
        try:
            if not search_terms or not dept_terms:
                return 0.0
            
            # 简化的TF-IDF：计算词频重叠度
            search_counter = Counter(search_terms)
            dept_counter = Counter(dept_terms)
            
            # 计算交集词频
            intersection_score = 0.0
            for term, count in search_counter.items():
                if term in dept_counter:
                    intersection_score += min(count, dept_counter[term])
            
            # 归一化
            total_search_terms = sum(search_counter.values())
            total_dept_terms = sum(dept_counter.values())
            
            if total_search_terms > 0 and total_dept_terms > 0:
                return intersection_score / math.sqrt(total_search_terms * total_dept_terms)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"计算TFIDF分数失败: {e}")
            return 0.0
    
    async def _get_department_table_relations(self, dept_id: str) -> List[str]:
        """获取部门表关联"""
        try:
            sql = "SELECT table_id FROM dd_departments_relation WHERE dept_id = :dept_id"
            result = await self.rdb_client.afetch_all(sql, {"dept_id": dept_id})
            
            if result.data:
                return [row['table_id'] for row in result.data]
            else:
                return []
                
        except Exception as e:
            logger.error(f"获取部门表关联失败: {e}")
            return []

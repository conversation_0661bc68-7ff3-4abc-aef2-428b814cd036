"""
SQLAlchemy ORM-based Universal Database Client

这个模块提供了一个完全基于SQLAlchemy ORM的数据库客户端实现，
与universal客户端保持完全相同的接口，但内部使用ORM模型进行数据操作。

主要特性：
- 动态ORM模型生成
- 完全兼容universal客户端接口
- 优化的会话管理
- 支持多种数据库方言
- 高性能查询优化
"""

from .client import ORMSQLAlchemyClient
from .config import ORMConnectionConfig
from .factory import (
    create_orm_client,
    create_orm_sqlite_client,
    create_orm_mysql_client,
    create_orm_postgresql_client,
    create_and_connect_orm_client,
    get_supported_databases,
    create_orm_client_from_components,
    create_orm_client_from_config_dict,
    create_orm_client_for_database
)
from .exceptions import ORMSQLAlchemyError
from .service_integration import (
    ORMClientManager,
    create_managed_orm_mysql_client,
    create_managed_orm_postgresql_client,
    create_managed_orm_sqlite_client,
    cleanup_orm_clients,
    cleanup_all_orm_clients
)

__version__ = "1.0.0"

__all__ = [
    # 核心类
    "ORMSQLAlchemyClient",
    "ORMConnectionConfig",

    # 工厂函数
    "create_orm_client",
    "create_orm_sqlite_client",
    "create_orm_mysql_client",
    "create_orm_postgresql_client",
    "create_and_connect_orm_client",
    "create_orm_client_from_components",
    "create_orm_client_from_config_dict",
    "create_orm_client_for_database",
    "get_supported_databases",

    # 服务集成
    "ORMClientManager",
    "create_managed_orm_mysql_client",
    "create_managed_orm_postgresql_client",
    "create_managed_orm_sqlite_client",
    "cleanup_orm_clients",
    "cleanup_all_orm_clients",

    # 异常
    "ORMSQLAlchemyError"
]

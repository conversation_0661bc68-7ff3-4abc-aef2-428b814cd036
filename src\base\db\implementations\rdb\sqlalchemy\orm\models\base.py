"""
ORM基础模型类

提供所有动态生成模型的基础功能和通用方法
"""

from typing import Dict, Any, Optional, List, Type
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import Session
from sqlalchemy import inspect
import logging

logger = logging.getLogger(__name__)

# 全局默认base（向后兼容）
Base = declarative_base()

class InstanceBaseModel:
    """
    实例级别的BaseModel工厂

    每个ORM客户端实例创建自己的declarative base和BaseModel
    """

    @staticmethod
    def create_instance_base():
        """
        为每个实例创建独立的declarative base和BaseModel

        Returns:
            tuple: (declarative_base, BaseModel_class)
        """
        # 创建独立的declarative base
        instance_base = declarative_base()

        # 创建继承自该base的BaseModel
        class InstanceBaseModel(instance_base, ModelMixin):
            __abstract__ = True

            @classmethod
            def create_model_from_table(cls, table_name: str, table_obj):
                """实例级别的模型创建方法"""
                # 生成类名
                class_name = ''.join(word.capitalize() for word in table_name.replace('_', ' ').split())
                if not class_name.endswith('Model'):
                    class_name += 'Model'

                # 创建类属性
                attrs = {
                    '__tablename__': table_name,
                    '__table__': table_obj,
                }

                # 动态创建模型类
                model_class = type(class_name, (cls,), attrs)

                logger.debug(f"Created instance model class: {class_name} for table: {table_name}")

                return model_class

        return instance_base, InstanceBaseModel


class ModelMixin:
    """模型混入类，提供通用的模型方法"""
    
    def to_dict(self, include_relationships: bool = False) -> Dict[str, Any]:
        """
        将模型实例转换为字典
        
        Args:
            include_relationships: 是否包含关系字段
        
        Returns:
            模型数据字典
        """
        result = {}
        
        # 获取所有列
        mapper = inspect(self.__class__)
        for column in mapper.columns:
            value = getattr(self, column.name)
            # 处理特殊类型
            if hasattr(value, 'isoformat'):  # datetime类型
                result[column.name] = value.isoformat()
            elif isinstance(value, bytes):  # bytes类型
                result[column.name] = value.decode('utf-8', errors='ignore')
            else:
                result[column.name] = value
        
        # 包含关系字段
        if include_relationships:
            for relationship in mapper.relationships:
                rel_value = getattr(self, relationship.key)
                if rel_value is not None:
                    if hasattr(rel_value, '__iter__') and not isinstance(rel_value, str):
                        # 一对多关系
                        result[relationship.key] = [
                            item.to_dict() if hasattr(item, 'to_dict') else str(item)
                            for item in rel_value
                        ]
                    else:
                        # 一对一关系
                        result[relationship.key] = (
                            rel_value.to_dict() if hasattr(rel_value, 'to_dict') else str(rel_value)
                        )
        
        return result
    
    def update_from_dict(self, data: Dict[str, Any], exclude_keys: Optional[List[str]] = None) -> None:
        """
        从字典更新模型实例
        
        Args:
            data: 更新数据字典
            exclude_keys: 排除的键列表
        """
        exclude_keys = exclude_keys or []
        mapper = inspect(self.__class__)
        
        for key, value in data.items():
            if key in exclude_keys:
                continue
                
            # 检查是否为有效的列
            if hasattr(mapper.columns, key):
                setattr(self, key, value)
            elif hasattr(mapper.relationships, key):
                # 处理关系字段（简单处理，不深入更新关系对象）
                logger.warning(f"Skipping relationship field update: {key}")
    
    @classmethod
    def get_table_name(cls) -> str:
        """获取表名"""
        return cls.__tablename__
    
    @classmethod
    def get_primary_key_columns(cls) -> List[str]:
        """获取主键列名列表"""
        mapper = inspect(cls)
        return [column.name for column in mapper.primary_key]
    
    @classmethod
    def get_column_names(cls) -> List[str]:
        """获取所有列名"""
        mapper = inspect(cls)
        return [column.name for column in mapper.columns]
    
    @classmethod
    def get_relationship_names(cls) -> List[str]:
        """获取所有关系名"""
        mapper = inspect(cls)
        return [relationship.key for relationship in mapper.relationships]
    
    def get_primary_key_values(self) -> Dict[str, Any]:
        """获取主键值字典"""
        pk_columns = self.get_primary_key_columns()
        return {col: getattr(self, col) for col in pk_columns}
    
    def __repr__(self) -> str:
        """字符串表示"""
        pk_values = self.get_primary_key_values()
        pk_str = ', '.join(f"{k}={v}" for k, v in pk_values.items())
        return f"<{self.__class__.__name__}({pk_str})>"


class BaseModel(Base, ModelMixin):
    """
    基础模型类，所有动态生成的模型都继承自此类
    
    提供通用的模型功能和方法
    """
    __abstract__ = True
    
    @classmethod
    def create_from_table(cls, table_name: str, table_obj, **kwargs) -> Type['BaseModel']:
        """
        从SQLAlchemy Table对象创建模型类
        
        Args:
            table_name: 表名
            table_obj: SQLAlchemy Table对象
            **kwargs: 额外的类属性
        
        Returns:
            动态生成的模型类
        """
        # 创建类属性字典
        attrs = {
            '__tablename__': table_name,
            '__table__': table_obj,
            **kwargs
        }
        
        # 动态创建类
        model_class = type(
            f"{table_name.title()}Model",
            (BaseModel,),
            attrs
        )
        
        return model_class


def create_model_from_table(table_name: str, table_obj, base_class: Type = None) -> Type:
    """
    从表对象创建模型类的便捷函数
    
    Args:
        table_name: 表名
        table_obj: SQLAlchemy Table对象
        base_class: 基础类
    
    Returns:
        动态生成的模型类
    """
    # 生成类名（首字母大写，去除特殊字符）
    class_name = ''.join(word.capitalize() for word in table_name.replace('_', ' ').split())
    if not class_name.endswith('Model'):
        class_name += 'Model'
    
    # 始终使用BaseModel（包含所有必要方法）
    actual_base = BaseModel

    # 创建类属性
    attrs = {
        '__tablename__': table_name,
        '__table__': table_obj,
    }

    # 动态创建模型类
    model_class = type(class_name, (actual_base,), attrs)
    
    logger.debug(f"Created dynamic model class: {class_name} for table: {table_name}")
    
    return model_class

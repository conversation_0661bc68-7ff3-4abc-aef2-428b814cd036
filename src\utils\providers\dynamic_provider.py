"""
动态配置提供者 (Dynamic Provider)

统一的配置管理和连接池策略，支持：
- 同步和异步API
- 基于pool_id的智能分配（支持knowledge_id、user_id等）
- 智能连接池管理
- 企业级扩展性

设计原则：
- 统一API：同步和异步使用相同的接口
- 内置连接池：不需要外部依赖
- 灵活分配模型：支持多种分配策略
- 资源管理：自动清理和优化
"""

import asyncio
import threading
import time
import hashlib
import weakref
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from collections import defaultdict
import aiohttp
from loguru import logger

from utils.common.config_util import config
from utils.common.service_client import ServiceClient
from utils.adapters.service_client_factory import EnhancedServiceClient, create_enhanced_service_client
from utils.providers.knowledge_config_manager import knowledge_config_manager


@dataclass
class PoolConfig:
    """连接池配置"""
    max_connections: int = 100
    max_pools_per_tenant: int = 10  # 改名：每个租户的最大连接池数
    max_total_pools: int = 500
    connection_timeout: int = 10
    read_timeout: int = 300
    idle_timeout: int = 1800
    cleanup_interval: int = 600

    # === 智能共享阈值配置 ===
    # LLM模型：按knowledge_id共享，无状态安全
    llm_shared_threshold: int = 1  # 降低阈值，同一知识库即可共享

    # Embedding模型：按knowledge_id共享，无状态安全
    embedding_shared_threshold: int = 1  # 降低阈值，同一知识库即可共享

    # 数据库连接：按knowledge_id隔离，保证数据安全
    rdb_shared_threshold: int = 999  # 实际永不共享，每个知识库独立
    vdb_shared_threshold: int = 999  # 实际永不共享，每个知识库独立


class ConnectionPoolManager:
    """
    内置连接池管理器

    核心职责：
    1. 管理所有的aiohttp.ClientSession（连接池）
    2. 智能决策：共享池 vs 租户隔离池
    3. 自动清理：释放不用的连接池
    4. 资源监控：统计连接池使用情况
    
    分配策略：
    - LLM/Embedding: 按knowledge_id共享（无状态，安全）
    - RDB/VDB: 按knowledge_id隔离（有状态，安全）
    """

    def __init__(self, config: PoolConfig):
        self.config = config

        # === 连接池存储 ===
        # 共享连接池：同一knowledge_id下的AI模型共享
        # 格式: {"shared_llm_kb001": ClientSession}
        self._shared_sessions: Dict[str, aiohttp.ClientSession] = {}

        # 租户隔离连接池：每个knowledge_id独立的数据库连接池
        # 格式: {"kb001": {"rdb_554301cd": ClientSession, "vdb_7bb9ab75": ClientSession}}
        self._tenant_sessions: Dict[str, Dict[str, aiohttp.ClientSession]] = defaultdict(dict)

        # === 使用统计 ===
        # 配置使用统计：记录哪些pool_id在使用哪些配置
        # 格式: {"80ce4ddd...": {"kb001", "kb002", "user123"}}
        self._config_usage: Dict[str, set] = defaultdict(set)

        # === 并发控制 ===
        # 异步锁：保证连接池操作的线程安全
        self._session_lock = asyncio.Lock()

        # 清理任务：定期清理空闲连接池的后台任务
        self._cleanup_task: Optional[asyncio.Task] = None

        # === 资源追踪 ===
        # 弱引用列表：用于自动清理已经被垃圾回收的session
        # 当session被垃圾回收时，会自动从这个列表中移除
        self._session_refs: List[weakref.ref] = []
    
    def should_use_shared_pool(self, service_type: str, config_hash: str, pool_id: str) -> bool:
        """
        智能路由决策：判断是否使用共享连接池

        新的决策逻辑：
        1. LLM/Embedding: 按knowledge_id共享（pool_id = knowledge_id）
        2. RDB/VDB: 按knowledge_id隔离（pool_id = knowledge_id）
        3. 特殊情况: pool_id = "system" 时使用全局共享

        Args:
            service_type: 服务类型 ('llm', 'embedding', 'rdb', 'vdb')
            config_hash: 配置哈希值，用于标识相同配置
            pool_id: 连接池ID（通常是knowledge_id）

        Returns:
            bool: True=使用共享池, False=使用租户隔离池
        """
        # 特殊情况：系统级调用使用全局共享
        if pool_id == "system":
            return service_type in ['llm', 'embedding']
        
        # 获取当前配置的使用pool_id数
        pools_using_this_config = self._config_usage.get(config_hash, set())
        pools_count = len(pools_using_this_config)

        # === 根据服务类型使用不同的共享阈值 ===
        if service_type == 'llm':
            threshold = self.config.llm_shared_threshold
        elif service_type == 'embedding':
            threshold = self.config.embedding_shared_threshold
        elif service_type == 'rdb':
            threshold = self.config.rdb_shared_threshold
        elif service_type == 'vdb':
            threshold = self.config.vdb_shared_threshold
        else:
            logger.warning(f"Unknown service type: {service_type}, using conservative isolation")
            return False

        should_share = pools_count >= threshold

        logger.debug(f"Pool decision for {service_type} (pool_id: {pool_id}): "
                    f"{'SHARED' if should_share else 'ISOLATED'} "
                    f"({pools_count} pools >= {threshold})")

        return should_share
    
    async def get_session(self,
                         service_type: str,
                         config_hash: str,
                         pool_id: str) -> aiohttp.ClientSession:
        """
        获取连接会话 - 连接池管理的核心方法

        工作流程：
        1. 记录配置使用情况（用于智能决策）
        2. 智能路由决策（共享池 vs 用户池）
        3. 获取或创建连接池
        4. 返回可用的连接会话

        Args:
            service_type: 服务类型
            config_hash: 配置哈希值
            pool_id: 连接池ID（通常是knowledge_id，也可以是user_id等）

        Returns:
            aiohttp.ClientSession: 可用的连接会话
        """
        # === 步骤1: 记录配置使用情况 ===
        self._config_usage[config_hash].add(pool_id)

        # === 步骤2: 智能路由决策 ===
        use_shared = self.should_use_shared_pool(service_type, config_hash, pool_id)

        # === 步骤3: 线程安全的连接池操作 ===
        async with self._session_lock:
            if use_shared:
                # === 共享池路径 ===
                # 同一知识库的AI模型共享连接池
                session_key = f"shared_{service_type}_{pool_id}_{config_hash[:8]}"

                if session_key not in self._shared_sessions or self._shared_sessions[session_key].closed:
                    self._shared_sessions[session_key] = await self._create_session(service_type)
                    logger.debug(f"Created shared session: {session_key}")

                return self._shared_sessions[session_key]

            else:
                # === 租户隔离池路径 ===
                # 每个知识库有独立的数据库连接池
                session_key = f"{service_type}_{config_hash[:8]}"

                if session_key not in self._tenant_sessions[pool_id] or \
                   self._tenant_sessions[pool_id][session_key].closed:

                    # === 连接池数量限制检查 ===
                    if len(self._tenant_sessions[pool_id]) >= self.config.max_pools_per_tenant:
                        old_key = list(self._tenant_sessions[pool_id].keys())[0]
                        session = self._tenant_sessions[pool_id][old_key]
                        logger.debug(f"Reusing session for pool {pool_id}: {old_key}")
                        return session

                    self._tenant_sessions[pool_id][session_key] = await self._create_session(service_type)
                    logger.debug(f"Created tenant session: {pool_id}/{session_key}")

                return self._tenant_sessions[pool_id][session_key]
    
    async def _create_session(self, service_type: str) -> aiohttp.ClientSession:
        """
        创建新的连接会话

        根据服务类型优化连接池参数：
        - LLM/Embedding: 大连接池，长超时（适合AI模型推理）
        - Database: 小连接池，短超时（适合数据库查询）

        Args:
            service_type: 服务类型

        Returns:
            aiohttp.ClientSession: 新创建的连接会话
        """
        if service_type in ['llm', 'embedding']:
            # === AI模型服务连接池配置 ===
            # 特点：推理时间长，需要大连接池和长超时
            connector = aiohttp.TCPConnector(
                limit=self.config.max_connections,    # 总连接数限制（默认100）
                limit_per_host=50,                     # 每个主机连接数限制
                keepalive_timeout=120,                 # 连接保活时间（2分钟）
                enable_cleanup_closed=True             # 自动清理关闭的连接
            )
            timeout = aiohttp.ClientTimeout(
                total=self.config.read_timeout,        # 总超时（默认5分钟）
                sock_connect=self.config.connection_timeout  # 连接超时（默认10秒）
            )
        else:
            # === 数据库服务连接池配置 ===
            # 特点：查询时间短，需要小连接池和短超时
            connector = aiohttp.TCPConnector(
                limit=50,                              # 较小的总连接数
                limit_per_host=20,                     # 较小的每主机连接数
                keepalive_timeout=60,                  # 较短的保活时间（1分钟）
                enable_cleanup_closed=True             # 自动清理关闭的连接
            )
            timeout = aiohttp.ClientTimeout(
                total=180,                             # 较短的总超时（3分钟）
                sock_connect=self.config.connection_timeout  # 连接超时保持一致
            )

        # === 创建连接会话 ===
        session = aiohttp.ClientSession(connector=connector, timeout=timeout)

        # === 资源追踪机制 ===
        # 使用弱引用追踪session，当session被垃圾回收时自动清理
        def cleanup_callback(ref):
            """当session被垃圾回收时的回调函数"""
            try:
                self._session_refs.remove(ref)
            except ValueError:
                pass  # 引用可能已经被移除

        ref = weakref.ref(session, cleanup_callback)
        self._session_refs.append(ref)

        return session
    
    async def cleanup_sessions(self) -> int:
        """
        清理空闲会话 - 自动资源回收机制

        清理策略：
        1. 清理已关闭的共享会话
        2. 清理已关闭的用户会话
        3. 清理空的用户会话字典

        这个方法可以定期调用，确保不会积累无用的连接池引用

        Returns:
            int: 清理的会话数量
        """
        cleaned = 0

        # === 线程安全的清理操作 ===
        async with self._session_lock:
            # 清理共享会话
            for key in list(self._shared_sessions.keys()):
                session = self._shared_sessions[key]
                if session.closed:
                    # 移除已关闭的共享连接池
                    del self._shared_sessions[key]
                    cleaned += 1
                    logger.debug(f"Cleaned closed shared session: {key}")

            # 清理租户会话
            for pool_id in list(self._tenant_sessions.keys()):
                for key in list(self._tenant_sessions[pool_id].keys()):
                    session = self._tenant_sessions[pool_id][key]
                    if session.closed:
                        del self._tenant_sessions[pool_id][key]
                        cleaned += 1
                        logger.debug(f"Cleaned closed tenant session: {pool_id}/{key}")

                # 清理空的租户字典
                if not self._tenant_sessions[pool_id]:
                    del self._tenant_sessions[pool_id]
                    logger.debug(f"Removed empty tenant session dict: {pool_id}")

        if cleaned > 0:
            logger.info(f"Cleaned {cleaned} closed sessions")

        return cleaned
    
    async def close_all_sessions(self):
        """
        关闭所有会话 - 完全清理机制

        用途：
        1. 测试结束时的完全清理
        2. 应用关闭时的资源释放
        3. 重置连接池状态

        这个方法确保：
        - 所有连接池都被正确关闭
        - 所有引用都被清理
        - 不会有session泄露警告
        """
        closed_count = 0

        async with self._session_lock:
            # 关闭所有共享会话
            for session_key, session in self._shared_sessions.items():
                if not session.closed:
                    await session.close()
                    closed_count += 1
                    logger.debug(f"Closed shared session: {session_key}")

            self._shared_sessions.clear()

            # 关闭所有租户会话
            for pool_id, pool_sessions in self._tenant_sessions.items():
                for session_key, session in pool_sessions.items():
                    if not session.closed:
                        await session.close()
                        closed_count += 1
                        logger.debug(f"Closed tenant session: {pool_id}/{session_key}")

            self._tenant_sessions.clear()
            self._config_usage.clear()
            self._session_refs.clear()

        logger.info(f"All sessions closed: {closed_count} sessions")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取连接池统计信息 - 监控和调试工具

        提供详细的连接池使用情况，包括：
        - 共享连接池数量
        - 每个用户的连接池数量
        - 总连接池数量
        - 配置使用情况

        Returns:
            Dict: 详细的统计信息
        """
        # === 计算各种统计指标 ===

        # 共享连接池数量
        shared_count = len(self._shared_sessions)
        tenant_session_counts = {pool_id: len(sessions)
                               for pool_id, sessions in self._tenant_sessions.items()}
        total_sessions = shared_count + sum(len(sessions) for sessions in self._tenant_sessions.values())
        config_usage_stats = {config_hash: len(pools)
                             for config_hash, pools in self._config_usage.items()}

        return {
            # 连接池数量统计
            'shared_sessions': shared_count,
            'tenant_sessions': tenant_session_counts,  # 改名：tenant_sessions
            'total_sessions': total_sessions,
            # 使用情况统计
            'config_usage': config_usage_stats,
            'total_tenants': len(self._tenant_sessions),  # 改名：total_tenants
            'active_configs': len(self._config_usage),
            # 资源利用率
            'avg_sessions_per_tenant': (
                total_sessions / max(len(self._tenant_sessions), 1)
            ),
            # 共享效率
            'sharing_efficiency': (
                shared_count / max(total_sessions, 1)
            ) if total_sessions > 0 else 0
        }


class DynamicProvider:
    """
    统一的动态配置提供者
    
    提供同步和异步API，内置连接池管理
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self.pool_config = PoolConfig()
            self.pool_manager = ConnectionPoolManager(self.pool_config)
            self._client_cache: Dict[str, ServiceClient] = {}
            self._cache_lock = asyncio.Lock()
            self._initialized = True
            logger.info("DynamicProvider initialized")
    
    def _build_cache_key(self, 
                        service_type: str, 
                        alias: str, 
                        pool_id: Optional[str] = None,
                        knowledge_id: Optional[str] = None,
                        user_id: Optional[str] = None,
                        config_hash: Optional[str] = None) -> str:
        """
        构建缓存键 - 支持多种分配策略和配置差异
        
        优先级: pool_id > knowledge_id > user_id > global
        配置差异: 如果提供config_hash，则在缓存键中包含它以区分不同配置
        """
        base_key = ""
        if pool_id:
            base_key = f"pool_{pool_id}_{service_type}_{alias}"
        elif knowledge_id:
            base_key = f"kb_{knowledge_id}_{service_type}_{alias}"
        elif user_id:
            base_key = f"user_{user_id}_{service_type}_{alias}"
        else:
            base_key = f"global_{service_type}_{alias}"
        
        # 如果有配置hash，添加到缓存键中以区分不同配置
        if config_hash:
            base_key += f"_cfg_{config_hash[:8]}"
        
        return base_key
    
    # === 知识库配置支持方法 ===
    def _create_service_from_knowledge_config(self, 
                                            service_type: str,
                                            knowledge_id: str,
                                            config_type: str) -> ServiceClient:
        """
        从知识库配置创建服务客户端
        
        Args:
            service_type: 服务类型 ('llm', 'embedding', 'rdb', 'vdb')
            knowledge_id: 知识库ID
            config_type: 配置类型 ('MODEL', 'SEARCH', 'RAG')
            
        Returns:
            ServiceClient: 配置好的服务客户端
        """
        try:
            # 获取知识库的有效配置
            kb_config = knowledge_config_manager.get_effective_config(knowledge_id, config_type)
            
            if config_type == 'MODEL':
                if service_type == 'llm':
                    llm_alias = kb_config.get('llm')
                    if not llm_alias:
                        raise ValueError(f"知识库 {knowledge_id} 的模型配置中缺少llm字段")
                    
                    # 获取LLM配置并应用自定义参数
                    all_llms = config.get_raw_config().model.llms
                    if llm_alias not in all_llms:
                        raise ValueError(f"LLM模型 '{llm_alias}' 不存在")
                    
                    llm_config = all_llms[llm_alias]
                    
                    # 应用知识库自定义的模型参数
                    if 'model_parameters' in kb_config:
                        # 创建配置副本并更新参数
                        from omegaconf import OmegaConf
                        custom_config = OmegaConf.create(OmegaConf.to_container(llm_config, resolve=True))
                        if hasattr(custom_config, 'model_parameters'):
                            custom_config.model_parameters.update(kb_config['model_parameters'])
                        else:
                            custom_config.model_parameters = kb_config['model_parameters']
                        
                        return ServiceClient(custom_config, singleton_id=f"kb_{knowledge_id}_llm_{llm_alias}")
                    else:
                        return ServiceClient(llm_config, singleton_id=f"kb_{knowledge_id}_llm_{llm_alias}")
                
                elif service_type == 'embedding':
                    embedding_alias = kb_config.get('embedding')
                    if not embedding_alias:
                        raise ValueError(f"知识库 {knowledge_id} 的模型配置中缺少embedding字段")
                    
                    all_embeddings = config.get_raw_config().model.embeddings
                    if embedding_alias not in all_embeddings:
                        raise ValueError(f"Embedding模型 '{embedding_alias}' 不存在")
                    
                    embedding_config = all_embeddings[embedding_alias]
                    return ServiceClient(embedding_config, singleton_id=f"kb_{knowledge_id}_embedding_{embedding_alias}")
            
            # 对于SEARCH和RAG配置，目前暂时返回默认服务
            # TODO: 后续可以扩展支持搜索和RAG参数的动态配置
            logger.warning(f"暂不支持 {config_type} 类型的动态服务创建，使用默认配置")
            return None
            
        except Exception as e:
            logger.error(f"从知识库配置创建 {service_type} 服务失败: {e}")
            raise

    def _is_knowledge_using_default_config(self, knowledge_id: str, config_type: str) -> bool:
        """检查知识库是否使用默认配置"""
        try:
            return knowledge_config_manager.is_using_default_config(knowledge_id, config_type)
        except Exception as e:
            logger.error(f"检查知识库 {knowledge_id} 默认配置状态失败: {e}")
            return True  # 出错时保守返回True

    # === 同步API ===
    def get_llm(self, 
               model_alias: Optional[str] = None,
               pool_id: Optional[str] = None,
               knowledge_id: Optional[str] = None,
               user_id: Optional[str] = None) -> ServiceClient:
        """
        同步获取LLM客户端
        
        支持两种模式：
        1. 指定model_alias: 使用指定的模型配置 
        2. 指定knowledge_id: 从知识库配置中获取模型配置
        """
        # 模式1: 没有指定任何参数，使用默认配置
        if model_alias is None and knowledge_id is None:
            return ServiceClient(config.llm)
        
        # 模式2: 指定knowledge_id但没有model_alias，从知识库配置获取
        if model_alias is None and knowledge_id is not None:
            try:
                # 检查知识库是否使用默认配置
                if self._is_knowledge_using_default_config(knowledge_id, 'MODEL'):
                    # 使用默认配置，直接返回默认LLM客户端
                    logger.debug(f"知识库 {knowledge_id} 使用默认LLM配置")
                    return self.get_llm(config.get_raw_config().active_llm, pool_id, knowledge_id, user_id)
                else:
                    # 使用自定义配置，从知识库配置创建客户端
                    cache_key = self._build_cache_key("llm", "knowledge_custom", pool_id, knowledge_id, user_id)
                    
                    if cache_key not in self._client_cache:
                        client = self._create_service_from_knowledge_config('llm', knowledge_id, 'MODEL')
                        if client:
                            self._client_cache[cache_key] = client
                            logger.info(f"创建知识库自定义LLM客户端: {knowledge_id}")
                        else:
                            # 降级到默认配置
                            return self.get_llm(config.get_raw_config().active_llm, pool_id, knowledge_id, user_id)
                    
                    return self._client_cache[cache_key]
            except Exception as e:
                logger.error(f"从知识库 {knowledge_id} 获取LLM配置失败，降级到默认配置: {e}")
                return self.get_llm(config.get_raw_config().active_llm, pool_id, knowledge_id, user_id)
        
        # 模式3: 指定model_alias，使用指定的模型配置（原有逻辑）
        cache_key = self._build_cache_key("llm", model_alias, pool_id, knowledge_id, user_id)
        
        if cache_key not in self._client_cache:
            all_llms = config.get_raw_config().model.llms
            if model_alias not in all_llms:
                available = list(all_llms.keys())
                raise ValueError(f"LLM模型 '{model_alias}' 不存在。可用模型: {available}")
            
            llm_config = all_llms[model_alias]
            self._client_cache[cache_key] = ServiceClient(llm_config, singleton_id=cache_key)
            
            # 确定实际的pool_id
            actual_pool_id = pool_id or knowledge_id or user_id or "system"
            logger.info(f"创建LLM客户端: {model_alias} (pool_id: {actual_pool_id})")
        
        return self._client_cache[cache_key]
    
    def get_embedding(self, 
                     model_alias: Optional[str] = None,
                     pool_id: Optional[str] = None,
                     knowledge_id: Optional[str] = None,
                     user_id: Optional[str] = None) -> ServiceClient:
        """
        同步获取Embedding客户端
        
        支持两种模式：
        1. 指定model_alias: 使用指定的模型配置
        2. 指定knowledge_id: 从知识库配置中获取模型配置
        """
        # 模式1: 没有指定任何参数，使用默认配置
        if model_alias is None and knowledge_id is None:
            return ServiceClient(config.embedding)
        
        # 模式2: 指定knowledge_id但没有model_alias，从知识库配置获取
        if model_alias is None and knowledge_id is not None:
            try:
                # 检查知识库是否使用默认配置
                if self._is_knowledge_using_default_config(knowledge_id, 'MODEL'):
                    # 使用默认配置，直接返回默认Embedding客户端
                    logger.debug(f"知识库 {knowledge_id} 使用默认Embedding配置")
                    return self.get_embedding(config.get_raw_config().active_embedding, pool_id, knowledge_id, user_id)
                else:
                    # 使用自定义配置，从知识库配置创建客户端
                    cache_key = self._build_cache_key("embedding", "knowledge_custom", pool_id, knowledge_id, user_id)
                    
                    if cache_key not in self._client_cache:
                        client = self._create_service_from_knowledge_config('embedding', knowledge_id, 'MODEL')
                        if client:
                            self._client_cache[cache_key] = client
                            logger.info(f"创建知识库自定义Embedding客户端: {knowledge_id}")
                        else:
                            # 降级到默认配置
                            return self.get_embedding(config.get_raw_config().active_embedding, pool_id, knowledge_id, user_id)
                    
                    return self._client_cache[cache_key]
            except Exception as e:
                logger.error(f"从知识库 {knowledge_id} 获取Embedding配置失败，降级到默认配置: {e}")
                return self.get_embedding(config.get_raw_config().active_embedding, pool_id, knowledge_id, user_id)
        
        # 模式3: 指定model_alias，使用指定的模型配置（原有逻辑）
        cache_key = self._build_cache_key("embedding", model_alias, pool_id, knowledge_id, user_id)
        
        if cache_key not in self._client_cache:
            all_embeddings = config.get_raw_config().model.embeddings
            if model_alias not in all_embeddings:
                available = list(all_embeddings.keys())
                raise ValueError(f"Embedding模型 '{model_alias}' 不存在。可用模型: {available}")
            
            embedding_config = all_embeddings[model_alias]
            self._client_cache[cache_key] = ServiceClient(embedding_config, singleton_id=cache_key)
            
            actual_pool_id = pool_id or knowledge_id or user_id or "system"
            logger.info(f"创建Embedding客户端: {model_alias} (pool_id: {actual_pool_id})")
        
        return self._client_cache[cache_key]
    
    def get_rdb(self, 
               db_alias: Optional[str] = None,
               pool_id: Optional[str] = None,
               knowledge_id: Optional[str] = None,
               user_id: Optional[str] = None) -> ServiceClient:
        """同步获取RDB客户端"""
        if db_alias is None:
            return ServiceClient(config.rdb)
        
        cache_key = self._build_cache_key("rdb", db_alias, pool_id, knowledge_id, user_id)
        
        if cache_key not in self._client_cache:
            all_rdbs = config.get_raw_config().database.rdbs
            if db_alias not in all_rdbs:
                available = list(all_rdbs.keys())
                raise ValueError(f"RDB数据库 '{db_alias}' 不存在。可用数据库: {available}")
            
            rdb_config = all_rdbs[db_alias]
            self._client_cache[cache_key] = ServiceClient(rdb_config, singleton_id=cache_key)
            
            actual_pool_id = pool_id or knowledge_id or user_id or "system"
            logger.info(f"创建RDB客户端: {db_alias} (pool_id: {actual_pool_id})")
        
        return self._client_cache[cache_key]
    
    def get_vdb(self, 
               db_alias: Optional[str] = None,
               pool_id: Optional[str] = None,
               knowledge_id: Optional[str] = None,
               user_id: Optional[str] = None) -> ServiceClient:
        """同步获取VDB客户端"""
        if db_alias is None:
            return ServiceClient(config.vdb)
        
        cache_key = self._build_cache_key("vdb", db_alias, pool_id, knowledge_id, user_id)
        
        if cache_key not in self._client_cache:
            all_vdbs = config.get_raw_config().database.vdbs
            if db_alias not in all_vdbs:
                available = list(all_vdbs.keys())
                raise ValueError(f"VDB数据库 '{db_alias}' 不存在。可用数据库: {available}")
            
            vdb_config = all_vdbs[db_alias]
            self._client_cache[cache_key] = ServiceClient(vdb_config, singleton_id=cache_key)
            
            actual_pool_id = pool_id or knowledge_id or user_id or "system"
            logger.info(f"创建VDB客户端: {db_alias} (pool_id: {actual_pool_id})")
        
        return self._client_cache[cache_key]

    # 异步API
    async def get_llm_async(self,
                           model_alias: Optional[str] = None,
                           pool_id: Optional[str] = None,
                           knowledge_id: Optional[str] = None,
                           user_id: Optional[str] = None) -> ServiceClient:
        """异步获取LLM客户端"""
        if model_alias is None:
            return ServiceClient(config.llm)

        cache_key = self._build_cache_key("llm", model_alias, pool_id, knowledge_id, user_id)

        async with self._cache_lock:
            if cache_key not in self._client_cache:
                all_llms = config.get_raw_config().model.llms
                if model_alias not in all_llms:
                    available = list(all_llms.keys())
                    raise ValueError(f"LLM模型 '{model_alias}' 不存在。可用模型: {available}")

                llm_config = all_llms[model_alias]
                client = ServiceClient(llm_config, singleton_id=cache_key)

                # 确定实际的pool_id
                actual_pool_id = pool_id or knowledge_id or user_id or "system"
                
                # 获取优化的连接会话
                config_hash = hashlib.md5(str(llm_config).encode()).hexdigest()
                session = await self.pool_manager.get_session('llm', config_hash, actual_pool_id)

                # 附加session到客户端
                if hasattr(client, '_session'):
                    client._session = session
                elif hasattr(client, 'session'):
                    client.session = session

                self._client_cache[cache_key] = client
                logger.info(f"创建异步LLM客户端: {model_alias} (pool_id: {actual_pool_id})")

            return self._client_cache[cache_key]

    async def get_embedding_async(self,
                                 model_alias: Optional[str] = None,
                                 pool_id: Optional[str] = None,
                                 knowledge_id: Optional[str] = None,
                                 user_id: Optional[str] = None) -> ServiceClient:
        """异步获取Embedding客户端"""
        if model_alias is None:
            return ServiceClient(config.embedding)

        cache_key = self._build_cache_key("embedding", model_alias, pool_id, knowledge_id, user_id)

        async with self._cache_lock:
            if cache_key not in self._client_cache:
                all_embeddings = config.get_raw_config().model.embeddings
                if model_alias not in all_embeddings:
                    available = list(all_embeddings.keys())
                    raise ValueError(f"Embedding模型 '{model_alias}' 不存在。可用模型: {available}")

                embedding_config = all_embeddings[model_alias]
                client = ServiceClient(embedding_config, singleton_id=cache_key)

                actual_pool_id = pool_id or knowledge_id or user_id or "system"
                
                config_hash = hashlib.md5(str(embedding_config).encode()).hexdigest()
                session = await self.pool_manager.get_session('embedding', config_hash, actual_pool_id)

                if hasattr(client, '_session'):
                    client._session = session
                elif hasattr(client, 'session'):
                    client.session = session

                self._client_cache[cache_key] = client
                logger.info(f"创建异步Embedding客户端: {model_alias} (pool_id: {actual_pool_id})")

            return self._client_cache[cache_key]

    async def get_rdb_async(self,
                           db_alias: Optional[str] = None,
                           pool_id: Optional[str] = None,
                           knowledge_id: Optional[str] = None,
                           user_id: Optional[str] = None) -> ServiceClient:
        """异步获取RDB客户端"""
        if db_alias is None:
            return ServiceClient(config.rdb)

        cache_key = self._build_cache_key("rdb", db_alias, pool_id, knowledge_id, user_id)

        async with self._cache_lock:
            if cache_key not in self._client_cache:
                all_rdbs = config.get_raw_config().database.rdbs
                if db_alias not in all_rdbs:
                    available = list(all_rdbs.keys())
                    raise ValueError(f"RDB数据库 '{db_alias}' 不存在。可用数据库: {available}")

                rdb_config = all_rdbs[db_alias]
                # 使用增强版ServiceClient，自动应用适配器
                client = create_enhanced_service_client(
                    config=rdb_config,
                    singleton_id=cache_key,
                    enable_adapter=True
                )

                actual_pool_id = pool_id or knowledge_id or user_id or "system"
                
                config_hash = hashlib.md5(str(rdb_config).encode()).hexdigest()
                session = await self.pool_manager.get_session('rdb', config_hash, actual_pool_id)

                if hasattr(client, '_session'):
                    client._session = session
                elif hasattr(client, 'session'):
                    client.session = session

                self._client_cache[cache_key] = client
                logger.info(f"创建异步RDB客户端: {db_alias} (pool_id: {actual_pool_id})")

            return self._client_cache[cache_key]

    async def get_vdb_async(self,
                           db_alias: Optional[str] = None,
                           pool_id: Optional[str] = None,
                           knowledge_id: Optional[str] = None,
                           user_id: Optional[str] = None) -> ServiceClient:
        """异步获取VDB客户端"""
        if db_alias is None:
            return ServiceClient(config.vdb)

        cache_key = self._build_cache_key("vdb", db_alias, pool_id, knowledge_id, user_id)

        async with self._cache_lock:
            if cache_key not in self._client_cache:
                all_vdbs = config.get_raw_config().database.vdbs
                if db_alias not in all_vdbs:
                    available = list(all_vdbs.keys())
                    raise ValueError(f"VDB数据库 '{db_alias}' 不存在。可用数据库: {available}")

                vdb_config = all_vdbs[db_alias]
                client = ServiceClient(vdb_config, singleton_id=cache_key)

                actual_pool_id = pool_id or knowledge_id or user_id or "system"
                
                config_hash = hashlib.md5(str(vdb_config).encode()).hexdigest()
                session = await self.pool_manager.get_session('vdb', config_hash, actual_pool_id)

                if hasattr(client, '_session'):
                    client._session = session
                elif hasattr(client, 'session'):
                    client.session = session

                self._client_cache[cache_key] = client
                logger.info(f"创建异步VDB客户端: {db_alias} (pool_id: {actual_pool_id})")

            return self._client_cache[cache_key]

    # 管理方法
    def list_available_llms(self) -> List[str]:
        """列出可用的LLM模型"""
        return list(config.get_raw_config().model.llms.keys())

    def list_available_embeddings(self) -> List[str]:
        """列出可用的Embedding模型"""
        return list(config.get_raw_config().model.embeddings.keys())

    def list_available_databases(self) -> Dict[str, List[str]]:
        """列出可用的数据库"""
        return {
            'rdb': list(config.get_raw_config().database.rdbs.keys()),
            'vdb': list(config.get_raw_config().database.vdbs.keys())
        }

    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计"""
        return self.pool_manager.get_stats()

    async def cleanup_resources(self) -> int:
        """清理资源"""
        return await self.pool_manager.cleanup_sessions()

    async def close_all_sessions(self):
        """关闭所有会话（测试结束时调用）"""
        await self.pool_manager.close_all_sessions()

    def clear_cache(self, pattern: Optional[str] = None, user_id: Optional[str] = None) -> int:
        """清理客户端缓存"""
        if pattern is None and user_id is None:
            count = len(self._client_cache)
            self._client_cache.clear()
            return count

        keys_to_remove = []
        for key in self._client_cache.keys():
            should_remove = True

            if user_id is not None:
                if not key.startswith(f"user_{user_id}_"):
                    should_remove = False

            if pattern is not None and should_remove:
                if not any(p in key for p in pattern.split('*')):
                    should_remove = False

            if should_remove:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del self._client_cache[key]

        return len(keys_to_remove)


# 全局实例
provider = DynamicProvider()

# 便捷函数 - 同步
def get_llm(model_alias: Optional[str] = None,
           pool_id: Optional[str] = None,
           knowledge_id: Optional[str] = None,
           user_id: Optional[str] = None) -> ServiceClient:
    """获取LLM客户端"""
    return provider.get_llm(model_alias, pool_id, knowledge_id, user_id)

def get_embedding(model_alias: Optional[str] = None,
                 pool_id: Optional[str] = None,
                 knowledge_id: Optional[str] = None,
                 user_id: Optional[str] = None) -> ServiceClient:
    """获取Embedding客户端"""
    return provider.get_embedding(model_alias, pool_id, knowledge_id, user_id)

def get_rdb(db_alias: Optional[str] = None,
           pool_id: Optional[str] = None,
           knowledge_id: Optional[str] = None,
           user_id: Optional[str] = None) -> ServiceClient:
    """获取RDB客户端"""
    return provider.get_rdb(db_alias, pool_id, knowledge_id, user_id)

def get_vdb(db_alias: Optional[str] = None,
           pool_id: Optional[str] = None,
           knowledge_id: Optional[str] = None,
           user_id: Optional[str] = None) -> ServiceClient:
    """获取VDB客户端"""
    return provider.get_vdb(db_alias, pool_id, knowledge_id, user_id)

# 便捷函数 - 异步
async def get_llm_async(model_alias: Optional[str] = None,
                       pool_id: Optional[str] = None,
                       knowledge_id: Optional[str] = None,
                       user_id: Optional[str] = None) -> ServiceClient:
    """异步获取LLM客户端"""
    return await provider.get_llm_async(model_alias, pool_id, knowledge_id, user_id)

async def get_embedding_async(model_alias: Optional[str] = None,
                             pool_id: Optional[str] = None,
                             knowledge_id: Optional[str] = None,
                             user_id: Optional[str] = None) -> ServiceClient:
    """异步获取Embedding客户端"""
    return await provider.get_embedding_async(model_alias, pool_id, knowledge_id, user_id)

async def get_rdb_async(db_alias: Optional[str] = None,
                       pool_id: Optional[str] = None,
                       knowledge_id: Optional[str] = None,
                       user_id: Optional[str] = None) -> ServiceClient:
    """异步获取RDB客户端"""
    return await provider.get_rdb_async(db_alias, pool_id, knowledge_id, user_id)

async def get_vdb_async(db_alias: Optional[str] = None,
                       pool_id: Optional[str] = None,
                       knowledge_id: Optional[str] = None,
                       user_id: Optional[str] = None) -> ServiceClient:
    """异步获取VDB客户端"""
    return await provider.get_vdb_async(db_alias, pool_id, knowledge_id, user_id)

# === 知识库专用便捷函数 ===
def get_knowledge_llm(knowledge_id: str,
                     pool_id: Optional[str] = None) -> ServiceClient:
    """获取知识库专用的LLM客户端（基于知识库配置）"""
    return provider.get_llm(None, pool_id, knowledge_id, None)

def get_knowledge_embedding(knowledge_id: str,
                           pool_id: Optional[str] = None) -> ServiceClient:
    """获取知识库专用的Embedding客户端（基于知识库配置）"""
    return provider.get_embedding(None, pool_id, knowledge_id, None)

async def get_knowledge_llm_async(knowledge_id: str,
                                 pool_id: Optional[str] = None) -> ServiceClient:
    """异步获取知识库专用的LLM客户端（基于知识库配置）"""
    return await provider.get_llm_async(None, pool_id, knowledge_id, None)

async def get_knowledge_embedding_async(knowledge_id: str,
                                       pool_id: Optional[str] = None) -> ServiceClient:
    """异步获取知识库专用的Embedding客户端（基于知识库配置）"""
    return await provider.get_embedding_async(None, pool_id, knowledge_id, None)

def get_knowledge_config(knowledge_id: str, config_type: str) -> Dict[str, Any]:
    """获取知识库配置（便捷函数）"""
    from utils.providers.knowledge_config_manager import get_knowledge_config
    return get_knowledge_config(knowledge_id, config_type)

def update_knowledge_config(knowledge_id: str, config_type: str, 
                           new_config: Dict[str, Any], description: str = None) -> bool:
    """更新知识库配置（便捷函数）"""
    from utils.providers.knowledge_config_manager import update_knowledge_config
    return update_knowledge_config(knowledge_id, config_type, new_config, description)


__all__ = [
    'DynamicProvider', 'provider',
    'get_llm', 'get_embedding', 'get_rdb', 'get_vdb',
    'get_llm_async', 'get_embedding_async', 'get_rdb_async', 'get_vdb_async',
    # 知识库专用函数
    'get_knowledge_llm', 'get_knowledge_embedding',
    'get_knowledge_llm_async', 'get_knowledge_embedding_async',
    'get_knowledge_config', 'update_knowledge_config'
]

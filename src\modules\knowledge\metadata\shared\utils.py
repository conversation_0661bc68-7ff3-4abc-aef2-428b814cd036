"""
Metadata模块工具函数

参考DD系统的utils.py设计，提供元数据相关的工具函数。
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from .constants import MetadataConstants
from .exceptions import MetadataValidationError


class MetadataUtils:
    """元数据工具类"""
    
    @staticmethod
    def validate_data_layer(data_layer: str) -> None:
        """验证数据层类型"""
        if data_layer not in MetadataConstants.SUPPORTED_DATA_LAYERS:
            raise MetadataValidationError(
                f"不支持的数据层类型: {data_layer}，支持的类型: {MetadataConstants.SUPPORTED_DATA_LAYERS}",
                "data_layer"
            )
    
    @staticmethod
    def validate_data_type(data_type: str) -> None:
        """验证数据类型"""
        if data_type not in MetadataConstants.SUPPORTED_DATA_TYPES:
            raise MetadataValidationError(
                f"不支持的数据类型: {data_type}，支持的类型: {MetadataConstants.SUPPORTED_DATA_TYPES}",
                "data_type"
            )
    
    @staticmethod
    def validate_index_type(index_type: str) -> None:
        """验证指标类型"""
        if index_type not in MetadataConstants.SUPPORTED_INDEX_TYPES:
            raise MetadataValidationError(
                f"不支持的指标类型: {index_type}，支持的类型: {MetadataConstants.SUPPORTED_INDEX_TYPES}",
                "index_type"
            )
    
    @staticmethod
    def validate_relation_type(relation_type: str) -> None:
        """验证关联类型"""
        if relation_type not in MetadataConstants.SUPPORTED_RELATION_TYPES:
            raise MetadataValidationError(
                f"不支持的关联类型: {relation_type}，支持的类型: {MetadataConstants.SUPPORTED_RELATION_TYPES}",
                "relation_type"
            )
    
    @staticmethod
    def validate_column_type(column_type: str) -> None:
        """验证字段类型"""
        if column_type not in MetadataConstants.SUPPORTED_COLUMN_TYPES:
            raise MetadataValidationError(
                f"不支持的字段类型: {column_type}，支持的类型: {MetadataConstants.SUPPORTED_COLUMN_TYPES}",
                "column_type"
            )
    
    @staticmethod
    def validate_source_type(source_type: str) -> None:
        """验证数据源类型"""
        if source_type not in MetadataConstants.SUPPORTED_SOURCE_TYPES:
            raise MetadataValidationError(
                f"不支持的数据源类型: {source_type}，支持的类型: {MetadataConstants.SUPPORTED_SOURCE_TYPES}",
                "source_type"
            )
    
    @staticmethod
    def validate_relation_level(relation_level: str) -> None:
        """验证关联层级"""
        if relation_level not in MetadataConstants.SUPPORTED_RELATION_LEVELS:
            raise MetadataValidationError(
                f"不支持的关联层级: {relation_level}，支持的类型: {MetadataConstants.SUPPORTED_RELATION_LEVELS}",
                "relation_level"
            )
    
    @staticmethod
    def validate_code_set_type(code_set_type: str) -> None:
        """验证码值集类型"""
        if code_set_type not in MetadataConstants.SUPPORTED_CODE_SET_TYPES:
            raise MetadataValidationError(
                f"不支持的码值集类型: {code_set_type}，支持的类型: {MetadataConstants.SUPPORTED_CODE_SET_TYPES}",
                "code_set_type"
            )
    
    @staticmethod
    def extract_vectorized_content(metadata_data: Dict[str, Any]) -> Dict[str, str]:
        """提取需要向量化的内容"""
        vectorized_content = {}
        for field in MetadataConstants.VECTORIZED_FIELDS:
            if field in metadata_data and metadata_data[field]:
                vectorized_content[field] = str(metadata_data[field])
        return vectorized_content
    
    @staticmethod
    def build_search_filters(
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        is_active: Optional[bool] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """构建搜索过滤条件"""
        filters = {}
        
        if knowledge_id:
            filters['knowledge_id'] = knowledge_id
        
        if data_layer:
            filters['data_layer'] = data_layer
            
        if is_active is not None:
            filters['is_active'] = is_active
            
        # 添加其他过滤条件
        for key, value in kwargs.items():
            if value is not None:
                filters[key] = value
                
        return filters
    
    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> None:
        """验证必需字段"""
        for field in required_fields:
            if field not in data or data[field] is None or (isinstance(data[field], str) and not data[field].strip()):
                raise MetadataValidationError(f"缺少必需字段: {field}", field)
    
    @staticmethod
    def add_timestamps(data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """添加时间戳"""
        current_time = datetime.now()
        
        if not is_update:
            data['create_time'] = current_time
        
        data['update_time'] = current_time
        return data
    
    @staticmethod
    def build_relation_path(db_name: str = None, table_name: str = None, column_name: str = None) -> str:
        """构建关联路径"""
        path_parts = []
        
        if db_name:
            path_parts.append(db_name)
        
        if table_name:
            path_parts.append(table_name)
            
        if column_name:
            path_parts.append(column_name)
        
        return ".".join(path_parts)
    
    @staticmethod
    def parse_relation_path(relation_path: str) -> Dict[str, str]:
        """解析关联路径"""
        parts = relation_path.split(".")
        result = {}

        if len(parts) >= 1:
            result['db_name'] = parts[0]

        if len(parts) >= 2:
            result['table_name'] = parts[1]

        if len(parts) >= 3:
            result['column_name'] = parts[2]

        return result

    @staticmethod
    def extract_vectorized_content(data: Dict[str, Any]) -> Dict[str, str]:
        """
        提取需要向量化的内容

        Args:
            data: 实体数据

        Returns:
            字段代码到内容的映射
        """
        vectorized_content = {}

        # 根据数据中的字段提取向量化内容
        for field_code, field_value in data.items():
            if field_value and isinstance(field_value, str) and field_value.strip():
                # 只对特定字段进行向量化
                if field_code in ['db_name', 'db_desc', 'table_name', 'table_desc',
                                'column_name', 'column_desc', 'code_value', 'code_desc',
                                'subject_name', 'subject_desc']:
                    vectorized_content[field_code] = field_value.strip()

        return vectorized_content

    # ==================== 实体验证方法 ====================

    @staticmethod
    def validate_source_database_data(data: Dict[str, Any]) -> None:
        """验证源数据库数据"""
        required_fields = ['knowledge_id', 'db_name']
        MetadataUtils.validate_required_fields(data, required_fields)

        if 'data_layer' in data:
            MetadataUtils.validate_data_layer(data['data_layer'])

    @staticmethod
    def validate_index_database_data(data: Dict[str, Any]) -> None:
        """验证指标数据库数据"""
        required_fields = ['knowledge_id', 'db_name']
        MetadataUtils.validate_required_fields(data, required_fields)

        if 'data_layer' in data:
            MetadataUtils.validate_data_layer(data['data_layer'])

    @staticmethod
    def validate_source_table_data(data: Dict[str, Any]) -> None:
        """验证源表数据"""
        required_fields = ['knowledge_id', 'db_id', 'table_name']
        MetadataUtils.validate_required_fields(data, required_fields)

    @staticmethod
    def validate_index_table_data(data: Dict[str, Any]) -> None:
        """验证指标表数据"""
        required_fields = ['knowledge_id', 'db_id', 'table_name']
        MetadataUtils.validate_required_fields(data, required_fields)

    @staticmethod
    def validate_source_column_data(data: Dict[str, Any]) -> None:
        """验证源字段数据"""
        required_fields = ['knowledge_id', 'table_id', 'column_name']
        MetadataUtils.validate_required_fields(data, required_fields)

        if 'column_type' in data:
            MetadataUtils.validate_column_type(data['column_type'])

    @staticmethod
    def validate_index_column_data(data: Dict[str, Any]) -> None:
        """
        验证指标字段数据

        根据实际表结构验证必需字段：
        - knowledge_id: 知识库ID
        - table_id: 表ID
        - column_name: 字段名
        """
        required_fields = ['knowledge_id', 'table_id', 'column_name']
        MetadataUtils.validate_required_fields(data, required_fields)

        # 验证指标类型（如果提供）
        if 'index_type' in data:
            valid_types = ['atom', 'compute']
            if data['index_type'] not in valid_types:
                raise ValueError(f"无效的指标类型: {data['index_type']}，必须是 {valid_types} 之一")

    @staticmethod
    def validate_code_set_data(data: Dict[str, Any]) -> None:
        """验证码值集数据"""
        required_fields = ['knowledge_id', 'code_set_name']
        MetadataUtils.validate_required_fields(data, required_fields)

        if 'code_set_type' in data:
            MetadataUtils.validate_code_set_type(data['code_set_type'])

    @staticmethod
    def validate_code_value_data(data: Dict[str, Any]) -> None:
        """验证码值数据"""
        required_fields = ['knowledge_id', 'code_set_id', 'code_value']
        MetadataUtils.validate_required_fields(data, required_fields)

    @staticmethod
    def validate_data_subject_data(data: Dict[str, Any]) -> None:
        """验证数据主题数据"""
        required_fields = ['knowledge_id', 'subject_name']
        MetadataUtils.validate_required_fields(data, required_fields)

    @staticmethod
    def validate_subject_relation_data(data: Dict[str, Any]) -> None:
        """
        验证数据主题关联数据

        根据实际表结构验证必需字段：
        - knowledge_id: 知识库ID
        - subject_id: 数据主题ID
        - source_type: 目标类型 (SOURCE/INDEX)
        - relation_path: 关联路径
        - relation_level: 关联层级 (DATABASE/TABLE/COLUMN)
        """
        required_fields = ['knowledge_id', 'subject_id', 'source_type', 'relation_path', 'relation_level']
        MetadataUtils.validate_required_fields(data, required_fields)

        # 验证枚举值
        if 'source_type' in data:
            valid_source_types = ['SOURCE', 'INDEX']
            if data['source_type'] not in valid_source_types:
                raise ValueError(f"无效的源类型: {data['source_type']}，必须是 {valid_source_types} 之一")

        if 'relation_level' in data:
            valid_levels = ['DATABASE', 'TABLE', 'COLUMN']
            if data['relation_level'] not in valid_levels:
                raise ValueError(f"无效的关联层级: {data['relation_level']}，必须是 {valid_levels} 之一")

    @staticmethod
    def validate_source_key_relation_data(data: Dict[str, Any]) -> None:
        """
        验证源关联键信息数据

        根据实际表结构验证必需字段：
        - knowledge_id: 知识库ID
        - source_column_id: 源字段ID
        - target_column_id: 目标字段ID
        """
        required_fields = ['knowledge_id', 'source_column_id', 'target_column_id']
        MetadataUtils.validate_required_fields(data, required_fields)

        # 验证关联类型（如果提供）
        if 'relation_type' in data:
            valid_types = ['FK', 'REF']
            if data['relation_type'] not in valid_types:
                raise ValueError(f"无效的关联类型: {data['relation_type']}，必须是 {valid_types} 之一")

    @staticmethod
    def validate_index_key_relation_data(data: Dict[str, Any]) -> None:
        """
        验证指标关联键信息数据

        根据实际表结构验证必需字段：
        - source_column_id: 源字段ID
        - target_column_id: 目标字段ID
        """
        required_fields = ['source_column_id', 'target_column_id']
        MetadataUtils.validate_required_fields(data, required_fields)

        # 验证关联类型（如果提供）
        if 'relation_type' in data:
            valid_types = ['FK', 'REF']
            if data['relation_type'] not in valid_types:
                raise ValueError(f"无效的关联类型: {data['relation_type']}，必须是 {valid_types} 之一")

    @staticmethod
    def validate_code_relation_data(data: Dict[str, Any]) -> None:
        """
        验证码值关联数据

        根据实际表结构验证必需字段：
        - knowledge_id: 知识库ID
        - column_id: 字段ID
        - code_set_id: 码值集ID
        - column_type: 字段类型
        """
        required_fields = ['knowledge_id', 'column_id', 'code_set_id', 'column_type']
        MetadataUtils.validate_required_fields(data, required_fields)

        # 验证字段类型（如果提供）
        if 'column_type' in data:
            valid_types = ['source', 'index']
            if data['column_type'] not in valid_types:
                raise ValueError(f"无效的字段类型: {data['column_type']}，必须是 {valid_types} 之一")

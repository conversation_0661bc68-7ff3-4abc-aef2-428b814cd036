-- ==========================================
-- RDB 参考码值 (Reference Code) 相关表结构创建脚本
-- ==========================================
--
-- 说明：
-- 1. 本脚本从 create_rdb_all.sql 中提取，专注于参考码值相关的表。
-- 2. 包含了以下三个核心表：
--    - md_reference_code_set
--    - md_reference_code_value
--    - md_reference_code_relation
--    注意：md_reference_code_doc 表已删除，code_set_name 本身就可以作为文档名
-- 3. 依赖于 'kb_knowledge' 表的存在，请在执行此脚本前确保该表已创建。
--
-- 创建时间：2025-07-04
-- ==========================================

-- 设置全局参数
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ==========================================
-- 1. 参考码值集表 (Reference Code Set)
-- ==========================================
DROP TABLE IF EXISTS `md_reference_code_set`;
CREATE TABLE `md_reference_code_set` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '码值集ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `code_set_name` varchar(100) NOT NULL COMMENT '码值集名称',
  `code_set_desc` varchar(500) DEFAULT NULL COMMENT '码值集描述',
  `code_set_type` varchar(50) DEFAULT 'ENUM' COMMENT '码值集类型',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_code_set_name` (`knowledge_id`, `code_set_name`),
  UNIQUE KEY `uk_reference_code_set_business` (`knowledge_id`, `code_set_name`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_md_reference_code_set_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参考码值集表';

-- ==========================================
-- 2. 码值集文档表已删除 - code_set_name 本身就可以作为文档名
-- ==========================================

-- ==========================================
-- 3. 参考码值表 (Reference Code Value)
-- ==========================================
DROP TABLE IF EXISTS `md_reference_code_value`;
CREATE TABLE `md_reference_code_value` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '码值ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `code_set_id` bigint NOT NULL COMMENT '码值集ID',
  `code_value` varchar(100) NOT NULL COMMENT '码值',
  `code_desc` varchar(200) NOT NULL COMMENT '码值描述',
  `code_value_cn` varchar(200) DEFAULT NULL COMMENT '码值中文描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活：1-激活，0-停用',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_code_set_id` (`code_set_id`),
  KEY `idx_code_value` (`code_value`),
  KEY `idx_is_active` (`is_active`),
  UNIQUE KEY `uk_code_set_code_value` (`code_set_id`, `code_value`),
  UNIQUE KEY `uk_reference_code_value_business` (`knowledge_id`, `code_set_id`, `code_value`),
  CONSTRAINT `fk_md_code_value_set` FOREIGN KEY (`code_set_id`) REFERENCES `md_reference_code_set` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_code_value_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参考码值表';

-- ==========================================
-- 4. 码值关联表 (Code Set Relation)
-- ==========================================
DROP TABLE IF EXISTS `md_reference_code_relation`;
CREATE TABLE `md_reference_code_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `knowledge_id` varchar(255) NOT NULL COMMENT '知识库ID',
  `column_id` bigint NOT NULL COMMENT '字段ID（源字段或指标字段）',
  `code_set_id` bigint NOT NULL COMMENT '码值集ID',
  `column_type` enum('source','index') NOT NULL DEFAULT 'source' COMMENT '字段类型：source-源字段，index-指标字段',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_column_code_set` (`column_id`, `code_set_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_code_set_id` (`code_set_id`),
  KEY `idx_column_type` (`column_type`),
  CONSTRAINT `fk_md_code_relation_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `kb_knowledge` (`knowledge_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_md_code_relation_code_set` FOREIGN KEY (`code_set_id`) REFERENCES `md_reference_code_set` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='码值关联表';

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1; 
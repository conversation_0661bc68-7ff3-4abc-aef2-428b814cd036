"""
企业级连接池监控演示

展示优化后的连接池配置和监控功能：
1. 验证优化后的连接池配置
2. 展示非侵入式监控指标
3. 提供性能分析和优化建议

Created: 2025-07-16
"""

import asyncio
import sys
import os
import time
from typing import Dict, Any

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from service import get_client, cleanup


async def test_rdb_pool_optimization():
    """测试RDB连接池优化效果"""
    print("\n=== SQLAlchemy Universal RDB 连接池优化验证 ===")
    
    try:
        # 获取MySQL客户端
        mysql_client = await get_client("database.rdbs.mysql")
        print(f"✅ MySQL客户端获取成功: {type(mysql_client).__name__}")
        
        # 获取连接池管理器（如果可访问）
        if hasattr(mysql_client, '_session_manager') and hasattr(mysql_client._session_manager, 'pool_manager'):
            pool_manager = mysql_client._session_manager.pool_manager
            
            # 获取连接池指标
            if hasattr(pool_manager, 'get_pool_metrics'):
                metrics = pool_manager.get_pool_metrics()
                print(f"📊 连接池指标:")
                print(f"   - 活跃同步池: {metrics.get('active_sync_pools', 0)}")
                print(f"   - 活跃异步池: {metrics.get('active_async_pools', 0)}")
                print(f"   - 池利用率: {metrics.get('pool_utilization', 0):.2%}")
                print(f"   - 创建的池数量: {metrics.get('pool_creation_count', 0)}")
            
            # 获取健康摘要
            if hasattr(pool_manager, 'get_pool_health_summary'):
                health = pool_manager.get_pool_health_summary()
                print(f"🏥 健康状态: {health.get('health_status', 'unknown')}")
                print(f"   - 利用率: {health.get('utilization', 0):.2%}")
                
                recommendations = health.get('recommendations', [])
                if recommendations:
                    print(f"💡 优化建议:")
                    for rec in recommendations:
                        print(f"   - {rec}")
        
        # 测试连接功能
        result = await mysql_client.afetch_all("SELECT 'Pool optimization test' as message")
        print(f"✅ 连接测试成功: {result.data}")
        
    except Exception as e:
        print(f"❌ RDB连接池测试失败: {e}")


async def test_pgvector_pool_optimization():
    """测试PGVector连接池优化效果"""
    print("\n=== PGVector 连接池优化验证 ===")
    
    try:
        # 获取PGVector客户端
        pgvector_client = await get_client("database.vdbs.pgvector")
        print(f"✅ PGVector客户端获取成功: {type(pgvector_client).__name__}")
        
        # 获取会话管理器（如果可访问）
        if hasattr(pgvector_client, 'session_manager'):
            session_manager = pgvector_client.session_manager
            
            # 获取基础连接池状态
            if hasattr(session_manager, 'get_pool_status'):
                status = session_manager.get_pool_status()
                print(f"📊 连接池状态:")
                print(f"   - 同步连接池存在: {status.get('sync_pool_exists', False)}")
                print(f"   - 异步连接池存在: {status.get('async_pool_exists', False)}")
                print(f"   - 同步最大连接: {status.get('sync_max_connections', 'N/A')}")
                print(f"   - 异步最大连接: {status.get('async_max_connections', 'N/A')}")
            
            # 获取增强指标（如果可用）
            if hasattr(session_manager, 'get_enhanced_pool_metrics'):
                enhanced_metrics = session_manager.get_enhanced_pool_metrics()
                print(f"📈 增强指标:")
                print(f"   - 同步利用率: {enhanced_metrics.get('sync_utilization', 0):.2%}")
                print(f"   - 异步利用率: {enhanced_metrics.get('async_utilization', 0):.2%}")
                print(f"   - 健康状态: {enhanced_metrics.get('health_status', 'unknown')}")
                print(f"   - 连接效率: {enhanced_metrics.get('connection_efficiency', 0):.2%}")
                
                recommendations = enhanced_metrics.get('recommendations', [])
                if recommendations:
                    print(f"💡 优化建议:")
                    for rec in recommendations:
                        print(f"   - {rec}")
        
        # 测试连接功能
        collections = pgvector_client.list_collections()
        print(f"✅ 连接测试成功: 发现 {len(collections)} 个集合")
        
    except Exception as e:
        print(f"❌ PGVector连接池测试失败: {e}")


async def test_embedding_session_optimization():
    """测试嵌入模型会话优化效果"""
    print("\n=== Generic Embedding 会话优化验证 ===")
    
    try:
        # 获取嵌入客户端
        embed_client = await get_client("model.embeddings.moka-m3e-base")
        print(f"✅ 嵌入客户端获取成功: {type(embed_client).__name__}")
        
        # 获取会话指标（通过类方法）
        from base.model_serve.model_runtime.model_providers.embedding_model.generic_embedding import EmbeddingSessionManager
        
        if hasattr(EmbeddingSessionManager, 'get_session_metrics'):
            metrics = EmbeddingSessionManager.get_session_metrics()
            print(f"📊 会话指标:")
            print(f"   - 状态: {metrics.get('status', 'unknown')}")
            print(f"   - 健康状态: {metrics.get('health_status', 'unknown')}")
            print(f"   - 利用率: {metrics.get('utilization', 0):.2%}")
            print(f"   - 清理间隔: {metrics.get('cleanup_interval', 0)}秒")
            
            connector_metrics = metrics.get('connector_metrics', {})
            if connector_metrics:
                print(f"🔗 连接器配置:")
                print(f"   - 全局限制: {connector_metrics.get('limit', 'N/A')}")
                print(f"   - 单主机限制: {connector_metrics.get('limit_per_host', 'N/A')}")
                print(f"   - 保活超时: {connector_metrics.get('keepalive_timeout', 'N/A')}秒")
                print(f"   - DNS缓存: {connector_metrics.get('use_dns_cache', False)}")
            
            recommendations = metrics.get('recommendations', [])
            if recommendations:
                print(f"💡 优化建议:")
                for rec in recommendations:
                    print(f"   - {rec}")
        
        # 测试嵌入功能
        result = await embed_client.ainvoke(texts=["连接池优化测试"])
        if hasattr(result, 'embeddings') and result.embeddings:
            print(f"✅ 嵌入测试成功: 向量维度 {len(result.embeddings[0])}")
        else:
            print(f"✅ 嵌入测试成功: {type(result).__name__}")
        
    except Exception as e:
        print(f"❌ 嵌入会话测试失败: {e}")


async def test_llm_session_optimization():
    """测试LLM会话优化效果"""
    print("\n=== OpenTrek LLM 会话优化验证 ===")
    
    try:
        # 获取LLM客户端
        llm_client = await get_client("model.llms.opentrek")
        print(f"✅ LLM客户端获取成功: {type(llm_client).__name__}")
        
        # 获取会话指标（通过类方法）
        from base.model_serve.model_runtime.model_providers.llm_model.opentrek_llm import SessionManager
        
        if hasattr(SessionManager, 'get_session_metrics'):
            metrics = SessionManager.get_session_metrics()
            print(f"📊 会话指标:")
            print(f"   - 状态: {metrics.get('status', 'unknown')}")
            print(f"   - 健康状态: {metrics.get('health_status', 'unknown')}")
            print(f"   - 利用率: {metrics.get('utilization', 0):.2%}")
            print(f"   - 清理间隔: {metrics.get('cleanup_interval', 0)}秒")
            
            connector_metrics = metrics.get('connector_metrics', {})
            if connector_metrics:
                print(f"🔗 连接器配置:")
                print(f"   - 全局限制: {connector_metrics.get('limit', 'N/A')}")
                print(f"   - 单主机限制: {connector_metrics.get('limit_per_host', 'N/A')}")
                print(f"   - 保活超时: {connector_metrics.get('keepalive_timeout', 'N/A')}秒")
            
            llm_metrics = metrics.get('llm_specific_metrics', {})
            if llm_metrics:
                print(f"🤖 LLM特定指标:")
                print(f"   - 对象池大小: {llm_metrics.get('chunk_pool_size', 'N/A')}")
                print(f"   - 流式超时: {llm_metrics.get('stream_timeout', 'N/A')}秒")
        
        # 获取性能摘要
        if hasattr(SessionManager, 'get_performance_summary'):
            summary = SessionManager.get_performance_summary()
            print(f"🎯 性能摘要:")
            print(f"   - 性能分数: {summary.get('performance_score', 0)}/100")
            print(f"   - 状态摘要: {summary.get('status_summary', 'unknown')}")
            print(f"   - 优化优先级: {summary.get('optimization_priority', 'unknown')}")
        
        # 测试LLM功能
        from base.model_serve.model_runtime.entities import PromptMessage
        messages = [PromptMessage(role='user', content='连接池优化测试')]
        
        result = await llm_client.ainvoke(prompt_messages=messages, stream=False)
        print(f"✅ LLM测试成功: {result.message.content[:50]}...")
        
    except Exception as e:
        print(f"❌ LLM会话测试失败: {e}")


async def generate_optimization_report():
    """生成优化报告"""
    print("\n=== 企业级连接池优化报告 ===")
    
    print("📈 配置优化摘要:")
    print("   🔹 SQLAlchemy RDB:")
    print("     - pool_size: 10 → 20 (+100%)")
    print("     - max_overflow: 20 → 40 (+100%)")
    print("     - pool_timeout: 30 → 60秒 (+100%)")
    print("     - pool_recycle: 3600 → 1800秒 (-50%)")
    
    print("   🔹 PGVector:")
    print("     - min_connections: 1 → 5 (+400%)")
    print("     - max_connections: 10 → 25 (+150%)")
    print("     - 新增: command_timeout=60秒")
    print("     - 新增: max_inactive_connection_lifetime=300秒")
    
    print("   🔹 Generic Embedding:")
    print("     - connector_limit: 100 → 200 (+100%)")
    print("     - limit_per_host: 30 → 50 (+67%)")
    print("     - keepalive_timeout: 30 → 60秒 (+100%)")
    print("     - cleanup_interval: 300 → 120秒 (-60%)")
    print("     - 新增: DNS缓存, 默认头部")
    
    print("   🔹 OpenTrek LLM:")
    print("     - connector_limit: 100 → 150 (+50%)")
    print("     - limit_per_host: 30 → 40 (+33%)")
    print("     - total_timeout: 300 → 240秒 (-20%)")
    print("     - cleanup_interval: 300 → 180秒 (-40%)")
    print("     - stream_timeout: 30 → 45秒 (+50%)")
    
    print("\n🎯 预期效果:")
    print("   ✅ 提升并发处理能力 50-150%")
    print("   ✅ 减少连接建立延迟")
    print("   ✅ 改善资源利用率")
    print("   ✅ 增强系统稳定性")
    print("   ✅ 提供实时监控能力")


async def main():
    """主函数"""
    try:
        print("🚀 企业级连接池优化验证")
        print("=" * 60)
        
        # 执行各组件的优化验证
        await test_rdb_pool_optimization()
        await test_pgvector_pool_optimization()
        await test_embedding_session_optimization()
        await test_llm_session_optimization()
        
        # 生成优化报告
        await generate_optimization_report()
        
        print("\n🎉 企业级连接池优化验证完成！")
        print("\n📋 关键改进:")
        print("✅ 连接池容量增加 50-400%")
        print("✅ 超时配置更加合理")
        print("✅ 添加非侵入式监控")
        print("✅ 保持向后兼容性")
        print("✅ 提供优化建议")
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        print("\n--- 清理 Service 层资源 ---")
        await cleanup()
        print("✅ 资源清理完成")


if __name__ == "__main__":
    asyncio.run(main())

from typing import Optional, Union, Generator, AsyncGenerator

from ..__base.large_language_model import LargeLanguageModel
from ...entities import PromptMessage, PromptMessageTool, LLMResult, LLMResultChunk, AssistantPromptMessage


class Zhipu(LargeLanguageModel):

    def invoke_llm(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            model_parameters: dict,
            prompt_messages: list[PromptMessage],
            tools: Optional[list[PromptMessageTool]] = None,
            stop: Optional[list[str]] = None,
            stream: bool = True,
    ) -> Union[LLMResult, Generator[LLMResultChunk, None, None]]:
        print('invoke in Zhipu1')
        return LLMResult(
            model=model,
            prompt_messages=[],
            message=AssistantPromptMessage(
                content='Hello, how can I help you?',
                tool_calls=[],
            ),
            usage=self._calc_response_usage(
                model=model,
                credentials=credentials,
                prompt_tokens=0,
                completion_tokens=0,
            ))

    async def ainvoke_llm(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            model_parameters: dict,
            prompt_messages: list[PromptMessage],
            tools: Optional[list[PromptMessageTool]] = None,
            stop: Optional[list[str]] = None,
            stream: bool = True,
    ) -> Union[LLMResult, AsyncGenerator[LLMResultChunk, None]]:
        print('ainvoke in Zhipu')

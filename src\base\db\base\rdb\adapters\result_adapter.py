"""
结果适配器

将数据库特定的查询结果转换为统一的响应格式
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
import time

from ..core.types import DatabaseValue, DatabaseRecord, DatabaseRecords, DatabaseType
from ..core.models import QueryResponse, OperationResponse
from ..core.interfaces import ResultAdapter
from .base_adapter import BaseAdapter


class DefaultResultAdapter(BaseAdapter, ResultAdapter):
    """默认结果适配器
    
    提供通用的结果转换逻辑，适用于大多数数据库
    """
    
    def __init__(self, database_type: DatabaseType):
        super().__init__(database_type)
    
    def get_supported_types(self) -> List[type]:
        """获取支持的数据类型"""
        return [
            type(None), bool, int, float, str, bytes,
            dict, list, tuple,
        ]
    
    def adapt_query_result(
        self, 
        raw_result: Any, 
        metadata: Optional[Dict[str, Any]] = None
    ) -> QueryResponse:
        """适配查询结果
        
        Args:
            raw_result: 原始查询结果
            metadata: 元数据信息
                - execution_time: 执行时间
                - query_sql: 执行的SQL
                - query_parameters: 查询参数
                - cursor_description: 游标描述
                - total_count: 总记录数（用于分页）
        
        Returns:
            标准化的查询响应
        """
        metadata = metadata or {}
        
        # 适配数据
        if raw_result is None:
            data = []
        elif isinstance(raw_result, list):
            data = self.adapt_records(raw_result)
        else:
            # 单个结果，转换为列表
            data = [self.adapt_record(raw_result)]
        
        # 如果有游标描述，使用它来创建更准确的记录
        cursor_description = metadata.get('cursor_description')
        if cursor_description and data:
            column_names = self.extract_column_names(cursor_description)
            if column_names:
                # 重新创建记录以确保列名正确
                adapted_data = []
                for i, record in enumerate(data):
                    if isinstance(raw_result, list) and i < len(raw_result):
                        adapted_record = self.create_record_from_row(raw_result[i], column_names)
                        adapted_data.append(adapted_record)
                    else:
                        adapted_data.append(record)
                data = adapted_data
        
        return QueryResponse(
            data=data,
            total_count=metadata.get('total_count'),
            execution_time=metadata.get('execution_time'),
            query_sql=metadata.get('query_sql'),
            query_parameters=metadata.get('query_parameters'),
            database_type=self.database_type,
            database_version=metadata.get('database_version')
        )
    
    def adapt_operation_result(
        self,
        raw_result: Any,
        metadata: Optional[Dict[str, Any]] = None
    ) -> OperationResponse:
        """适配操作结果

        Args:
            raw_result: 原始操作结果
            metadata: 元数据信息
                - affected_rows: 影响的行数
                - execution_time: 执行时间
                - operation_sql: 执行的SQL
                - operation_parameters: 操作参数
                - data: 返回的数据（如果有RETURNING子句）
                - success: 操作是否成功

        Returns:
            标准化的操作响应
        """
        metadata = metadata or {}

        # 确定操作是否成功
        success = metadata.get('success', True)
        if raw_result is not None:
            # 如果有原始结果，尝试从中提取成功状态
            if hasattr(raw_result, 'rowcount'):
                affected_rows = getattr(raw_result, 'rowcount', 0)
                success = affected_rows >= 0
            elif isinstance(raw_result, (int, float)):
                affected_rows = int(raw_result)
                success = affected_rows >= 0
            else:
                affected_rows = metadata.get('affected_rows', 0)
        else:
            affected_rows = metadata.get('affected_rows', 0)

        # 处理返回数据
        returning_data = metadata.get('returning_data')
        if returning_data is not None:
            returning_data = self.adapt_records(returning_data)

        return OperationResponse(
            success=success,
            affected_rows=affected_rows,
            data=returning_data,
            execution_time=metadata.get('execution_time'),
            operation_sql=metadata.get('operation_sql'),
            operation_parameters=metadata.get('operation_parameters'),
            error_message=metadata.get('error_message'),
            error_code=metadata.get('error_code')
        )
    
    def adapt_cursor_result(
        self,
        cursor: Any,
        fetch_method: str = 'fetchall'
    ) -> tuple[DatabaseRecords, List[str]]:
        """适配游标结果
        
        Args:
            cursor: 数据库游标
            fetch_method: 获取方法 ('fetchall', 'fetchone', 'fetchmany')
        
        Returns:
            (记录列表, 列名列表)
        """
        # 提取列名
        column_names = []
        if hasattr(cursor, 'description') and cursor.description:
            column_names = self.extract_column_names(cursor.description)
        
        # 获取数据
        if fetch_method == 'fetchall':
            raw_rows = cursor.fetchall() if hasattr(cursor, 'fetchall') else []
        elif fetch_method == 'fetchone':
            raw_row = cursor.fetchone() if hasattr(cursor, 'fetchone') else None
            raw_rows = [raw_row] if raw_row is not None else []
        elif fetch_method == 'fetchmany':
            raw_rows = cursor.fetchmany() if hasattr(cursor, 'fetchmany') else []
        else:
            raise ValueError(f"Unsupported fetch method: {fetch_method}")
        
        # 适配记录
        records = []
        for raw_row in raw_rows:
            if column_names:
                record = self.create_record_from_row(raw_row, column_names)
            else:
                record = self.adapt_record(raw_row)
            records.append(record)
        
        return records, column_names
    
    def create_execution_metadata(
        self,
        start_time: float,
        sql: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        cursor: Optional[Any] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建执行元数据
        
        Args:
            start_time: 开始时间（time.time()）
            sql: 执行的SQL
            parameters: 查询参数
            cursor: 数据库游标
            **kwargs: 其他元数据
        
        Returns:
            元数据字典
        """
        execution_time = time.time() - start_time
        
        metadata = {
            'execution_time': execution_time,
            'query_sql': sql,
            'operation_sql': sql,
            'query_parameters': parameters,
            'operation_parameters': parameters,
            'database_type': self.database_type,
        }
        
        # 从游标提取信息
        if cursor:
            if hasattr(cursor, 'description'):
                metadata['cursor_description'] = cursor.description
            
            if hasattr(cursor, 'rowcount'):
                metadata['affected_rows'] = cursor.rowcount
        
        # 添加其他元数据
        metadata.update(kwargs)
        
        return metadata
    
    def extract_error_info(self, error: Exception) -> Dict[str, Any]:
        """从异常中提取错误信息
        
        Args:
            error: 异常对象
        
        Returns:
            错误信息字典
        """
        error_info = {
            'error_message': str(error),
            'error_type': type(error).__name__,
            'success': False,
        }
        
        # 尝试提取错误码
        if hasattr(error, 'errno'):
            error_info['error_code'] = str(error.errno)
        elif hasattr(error, 'pgcode'):
            error_info['error_code'] = str(error.pgcode)
        elif hasattr(error, 'code'):
            error_info['error_code'] = str(error.code)
        
        # 尝试提取SQL状态
        if hasattr(error, 'sqlstate'):
            error_info['sql_state'] = str(error.sqlstate)
        
        return error_info


class StreamingResultAdapter(DefaultResultAdapter):
    """流式结果适配器
    
    用于处理大结果集的流式读取
    """
    
    def __init__(self, database_type: DatabaseType, batch_size: int = 1000):
        super().__init__(database_type)
        self.batch_size = batch_size
    
    def adapt_streaming_result(
        self,
        cursor: Any,
        metadata: Optional[Dict[str, Any]] = None
    ) -> QueryResponse:
        """适配流式结果
        
        Args:
            cursor: 数据库游标
            metadata: 元数据
        
        Returns:
            查询响应（数据为生成器）
        """
        metadata = metadata or {}
        
        # 提取列名
        column_names = []
        if hasattr(cursor, 'description') and cursor.description:
            column_names = self.extract_column_names(cursor.description)
        
        def record_generator():
            """记录生成器"""
            while True:
                if hasattr(cursor, 'fetchmany'):
                    batch = cursor.fetchmany(self.batch_size)
                else:
                    # fallback to fetchone
                    row = cursor.fetchone() if hasattr(cursor, 'fetchone') else None
                    batch = [row] if row is not None else []
                
                if not batch:
                    break
                
                for raw_row in batch:
                    if column_names:
                        yield self.create_record_from_row(raw_row, column_names)
                    else:
                        yield self.adapt_record(raw_row)
        
        # 注意：这里返回生成器而不是列表
        return QueryResponse(
            data=list(record_generator()),  # 为了兼容性，这里还是转换为列表
            execution_time=metadata.get('execution_time'),
            query_sql=metadata.get('query_sql'),
            query_parameters=metadata.get('query_parameters'),
            database_type=self.database_type
        )

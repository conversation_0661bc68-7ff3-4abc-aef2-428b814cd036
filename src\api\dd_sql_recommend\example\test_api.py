"""
DD SQL推荐API测试脚本
"""

import asyncio
import logging
import httpx
from app.dd_sql_recommend.models import (
    SQLRecommendRequest,
    QuestionRecommendRequest,
    SQLGenerateRequest,
    SingleSQLGenerateRequest,
    SQLIntegrationRequest
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API基础URL（根据实际部署情况修改）
BASE_URL = "http://localhost:30338"


async def test_dd_sql_recommend_api():
    """测试DD SQL推荐API"""
    async with httpx.AsyncClient() as client:
        # 测试数据
        report_code = "G0107_beta_v1.0"
        dept_id = "DEPT_IT"
        entry_id = "SUBMIT_001"
        
        try:
            # 测试推荐SQL接口
            logger.info("测试推荐SQL接口...")
            sql_request = SQLRecommendRequest(
                report_code=report_code,
                dept_id=dept_id,
                entry_id=entry_id
            )
            response = await client.post(
                f"{BASE_URL}/api/dd/sql-recommend/recommend-sql",
                json=sql_request.dict()
            )
            if response.status_code == 200:
                result = response.json()
                logger.info(f"推荐SQL结果: {result}")
            else:
                logger.error(f"推荐SQL接口失败: {response.status_code}, {response.text}")
            
            # 测试推荐问题接口
            logger.info("测试推荐问题接口...")
            question_request = QuestionRecommendRequest(
                report_code=report_code,
                dept_id=dept_id,
                entry_id=entry_id
            )
            response = await client.post(
                f"{BASE_URL}/api/dd/sql-recommend/recommend-questions",
                json=question_request.dict()
            )
            if response.status_code == 200:
                result = response.json()
                logger.info(f"推荐问题列表: {result}")
            else:
                logger.error(f"推荐问题接口失败: {response.status_code}, {response.text}")
            
            # 测试单个SQL生成接口
            logger.info("测试单个SQL生成接口...")
            single_sql_request = SingleSQLGenerateRequest(
                report_code=report_code,
                dept_id=dept_id,
                entry_id=entry_id,
                question="需要从客户表获取基本信息"
            )
            response = await client.post(
                f"{BASE_URL}/api/dd/sql-recommend/generate-single-sql",
                json=single_sql_request.dict()
            )
            if response.status_code == 200:
                result = response.json()
                logger.info(f"单个SQL生成结果: {result}")
            else:
                logger.error(f"单个SQL生成接口失败: {response.status_code}, {response.text}")
            
            # 测试批量SQL生成接口
            logger.info("测试批量SQL生成接口...")
            sql_generate_request = SQLGenerateRequest(
                report_code=report_code,
                dept_id=dept_id,
                entry_id=entry_id,
                question_list=[
                    "需要从客户表获取基本信息",
                    "需要从交易表获取交易记录",
                    "以union的形式，筛选最近三个月的数据"
                ]
            )
            response = await client.post(
                f"{BASE_URL}/api/dd/sql-recommend/generate-sql-list",
                json=sql_generate_request.dict()
            )
            if response.status_code == 200:
                result = response.json()
                logger.info(f"批量SQL生成结果: {result}")
            else:
                logger.error(f"批量SQL生成接口失败: {response.status_code}, {response.text}")
            
            # 测试SQL整合接口
            logger.info("测试SQL整合接口...")
            integration_request = SQLIntegrationRequest(
                report_code=report_code,
                dept_id=dept_id,
                entry_id=entry_id,
                question_list=[
                    "需要从客户表获取基本信息",
                    "需要从交易表获取交易记录"
                ],
                sql_list=[
                    "SELECT * FROM customer",
                    "SELECT * FROM transaction"
                ]
            )
            response = await client.post(
                f"{BASE_URL}/api/dd/sql-recommend/integrate-sql",
                json=integration_request.dict()
            )
            if response.status_code == 200:
                result = response.json()
                logger.info(f"SQL整合结果: {result}")
            else:
                logger.error(f"SQL整合接口失败: {response.status_code}, {response.text}")
            
            logger.info("所有API测试完成")
            
        except Exception as e:
            logger.error(f"测试过程中出现错误: {e}")
            raise


async def test_api_endpoints():
    """测试所有API端点的基本功能"""
    async with httpx.AsyncClient() as client:
        try:
            # 测试API根路径
            response = await client.get(f"{BASE_URL}/api/dd/sql-recommend")
            logger.info(f"API根路径状态: {response.status_code}")
            
            # 测试OpenAPI文档
            response = await client.get(f"{BASE_URL}/api/dd/sql-recommend/docs")
            logger.info(f"OpenAPI文档状态: {response.status_code}")
            
        except Exception as e:
            logger.error(f"API端点测试失败: {e}")

"""
以下为DD SQL推荐模块五个接口的curl命令示例，可直接迁移到apifox进行测试。
请根据实际后端服务host:port进行替换。

1. 推荐SQL接口
用于推荐历史上可能的SQL
参数：report_code（版本号）、dept_id（部门ID）、entry_id（业务表submission_id）

curl -X POST \
  http://localhost:30338/api/dd/sql-recommend/recommend-sql \
  -H "Content-Type: application/json" \
  -d '{
    "report_code": "G0107_release_v1.0",
    "dept_id": "114",
    "entry_id": "SUBMIT_001"
  }'

2. 推荐指引问题接口
用于推荐数个指引类型的问题

curl -X POST \
  http://localhost:30338/api/dd/sql-recommend/recommend-questions \
  -H "Content-Type: application/json" \
  -d '{
    "report_code": "G0107_release_v1.0",
    "dept_id": "114",
    "entry_id": "SUBMIT_001"
  }'

3. 批量生成SQL接口
传入问题列表，批量生成SQL

curl -X POST \
  http://localhost:30338/api/dd/sql-recommend/generate-sql-list \
  -H "Content-Type: application/json" \
  -d '{
    "report_code": "G0107_release_v1.0",
    "dept_id": "114",
    "entry_id": "SUBMIT_001",
    "question_list": [
        "从客户基本信息表获取姓名、身份证号、联系电话和地址字段",
        "通过客户ID关联客户基本信息表和交易表，获取完整的客户交易信息",
        "从交易表中筛选出ctime字段，将以上信息拼接起来"
    ]
  }'

4. 单个生成SQL接口
传入单个问题，生成SQL

curl -X POST \
  http://localhost:30338/api/dd/sql-recommend/generate-single-sql \
  -H "Content-Type: application/json" \
  -d '{
    "report_code": "G0107_release_v1.0",
    "dept_id": "114",
    "entry_id": "SUBMIT_001",
    "question": "需要从b表拿到kk字段，c表拿到ww字段"
  }'

5. SQL整合接口
传入问题列表和SQL列表，整合为一个完整SQL

curl -X POST \
  http://localhost:30338/api/dd/sql-recommend/integrate-sql \
  -H "Content-Type: application/json" \
  -d '{
    "report_code": "G0107_release_v1.0",
    "dept_id": "114",
    "entry_id": "SUBMIT_001",
    "question_list": [
        "从客户基本信息表获取姓名、身份证号、联系电话和地址字段",
        "通过客户ID关联客户基本信息表和交易表，获取完整的客户交易信息",
        "从交易表中筛选出ctime字段，将以上信息拼接起来"
    ],
    "sql_list": [
        "select user_name, user_id, user_phone, user_address \nfrom ADM_TOP_G07 \nwhere IS_CBIRC_LOAN='Y'\n",
        "select columns_a, user_name, user_mail, user_gender, user_credit\nfrom ADM_LON_REALESTATE_LOAN\njoin ADM_TOP_G07 on ADM_LON_REALESTATE_LOAN.user_credit = ADM_TOP_G07.user_credit\njoin ADM_PUB_BRANCH_LEVEL_INFO on ADM_LON_REALESTATE_LOAN.user_credit = ADM_PUB_BRANCH_LEVEL_INFO.user_credit\nwhere ADM_LON_REALESTATE_LOAN.IS_CBIRC_LOAN='Y'\n",
        "select ctime \nfrom ADM_LON_REALESTATE_LOAN \nwhere IS_CBIRC_LOAN='Y'\n"
    ]
  }'
"""


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_dd_sql_recommend_api())
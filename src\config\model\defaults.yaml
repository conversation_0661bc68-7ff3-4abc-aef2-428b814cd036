# This file serves as a blueprint for the complete 'model' configuration.
# It explicitly lists all available model components from the sub-directories
# and places them into the correct structure using package overrides.

defaults:
  # Load LLMs into the 'llms' dictionary
  - llms/<EMAIL>
  # To add a new LLM like 'llama3', create 'llms/llama3.yaml' and add this line:
  # - llms/llama3@llms.llama3

  # Load Embedding models into the 'embeddings' dictionary
  - embeddings/<EMAIL>-m3e-base 
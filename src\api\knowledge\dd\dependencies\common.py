"""
DD系统通用依赖注入

提供DD系统API的通用依赖注入功能，包括：
- 数据库客户端获取
- 业务逻辑实例创建
- 参数验证
- 分页处理
"""

import sys
import logging
from pathlib import Path
from typing import Optional, Tuple
from fastapi import HTTPException, Query

logger = logging.getLogger(__name__)

# 添加项目根目录到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from service import get_client
from modules.knowledge.dd import DDCrud, DDSearch
from utils.common.uuid_utils import is_valid_uuid4


# ==================== 业务逻辑依赖 ====================

async def get_dd_crud() -> DDCrud:
    """
    获取DD CRUD实例
    
    Returns:
        DDCrud: DD系统的CRUD操作实例
        
    Raises:
        HTTPException: 当服务初始化失败时
    """
    try:
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        return DDCrud(rdb_client, vdb_client)
    except Exception as e:
        logger.error(f"获取DD CRUD实例失败: {e}")
        raise HTTPException(status_code=500, detail="DD CRUD服务初始化失败")


async def get_dd_search() -> DDSearch:
    """
    获取DD Search实例
    
    Returns:
        DDSearch: DD系统的搜索功能实例
        
    Raises:
        HTTPException: 当搜索服务初始化失败时
    """
    try:
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        # 尝试获取embedding客户端，如果失败则使用None
        try:
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except:
            embedding_client = None
            logger.warning("无法获取embedding客户端，搜索功能将受限")

        return DDSearch(rdb_client, vdb_client, embedding_client)
    except Exception as e:
        logger.error(f"获取DD Search实例失败: {e}")
        raise HTTPException(status_code=500, detail="DD搜索服务初始化失败")


# ==================== 参数验证依赖 ====================

def validate_knowledge_id(knowledge_id: str) -> str:
    """
    验证知识库ID格式
    
    Args:
        knowledge_id: 知识库ID
        
    Returns:
        str: 验证通过的知识库ID
        
    Raises:
        HTTPException: 当ID格式无效时
    """
    if not is_valid_uuid4(knowledge_id):
        raise HTTPException(
            status_code=400,
            detail=f"无效的knowledge_id格式: {knowledge_id}，必须是标准UUID4格式"
        )
    return knowledge_id


def validate_pagination(
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(20, description="每页数量", ge=1, le=100)
) -> Tuple[int, int, int]:
    """
    验证分页参数
    
    Args:
        page: 页码（从1开始）
        page_size: 每页数量（1-100）
        
    Returns:
        Tuple[int, int, int]: (page, page_size, offset)
    """
    offset = (page - 1) * page_size
    return page, page_size, offset


# ==================== 查询参数依赖 ====================

def get_optional_filters(
    data_layer: Optional[str] = Query(None, description="数据层过滤"),
    version: Optional[str] = Query(None, description="版本过滤"),
    is_active: Optional[bool] = Query(None, description="激活状态过滤")
) -> dict:
    """
    获取可选的查询过滤条件
    
    Args:
        data_layer: 数据层过滤
        version: 版本过滤
        is_active: 激活状态过滤
        
    Returns:
        dict: 过滤条件字典
    """
    filters = {}
    if data_layer is not None:
        filters["data_layer"] = data_layer
    if version is not None:
        filters["version"] = version
    if is_active is not None:
        filters["is_active"] = is_active
    return filters


def get_search_params(
    limit: int = Query(10, description="返回结果数量限制", ge=1, le=100),
    min_score: float = Query(0.5, description="最小相似度分数", ge=0.0, le=1.0),
    knowledge_id: Optional[str] = Query(None, description="知识库ID过滤")
) -> dict:
    """
    获取搜索参数
    
    Args:
        limit: 结果数量限制
        min_score: 最小相似度分数
        knowledge_id: 知识库ID过滤
        
    Returns:
        dict: 搜索参数字典
    """
    params = {
        "limit": limit,
        "min_score": min_score
    }
    if knowledge_id is not None:
        params["knowledge_id"] = knowledge_id
    return params


# ==================== 错误处理依赖 ====================

def handle_not_found(item_name: str, item_id: str):
    """
    处理资源不存在的情况
    
    Args:
        item_name: 资源名称
        item_id: 资源ID
        
    Raises:
        HTTPException: 404错误
    """
    raise HTTPException(
        status_code=404, 
        detail=f"{item_name}不存在: {item_id}"
    )


def handle_validation_error(field_name: str, error_msg: str):
    """
    处理验证错误
    
    Args:
        field_name: 字段名称
        error_msg: 错误信息
        
    Raises:
        HTTPException: 400错误
    """
    raise HTTPException(
        status_code=400,
        detail=f"{field_name}验证失败: {error_msg}"
    )


def handle_service_error(service_name: str, error: Exception):
    """
    处理服务错误
    
    Args:
        service_name: 服务名称
        error: 异常对象
        
    Raises:
        HTTPException: 500错误
    """
    logger.error(f"{service_name}服务错误: {error}")
    raise HTTPException(
        status_code=500,
        detail=f"{service_name}服务暂时不可用，请稍后重试"
    )

"""
元数据管理系统 API模块 v2.0

重构后的模块化架构，参照DD系统的设计模式：

📁 目录结构：
├── routers/          # API路由模块（按功能分组）
├── models/           # 数据模型（请求/响应/枚举）
├── dependencies/     # 依赖注入和通用功能
├── utils/           # 工具函数和辅助功能
├── docs/            # 文档和示例
└── tests/           # 测试文件

🚀 功能模块：
- 数据库管理API（源数据库、指标数据库）
- 表管理API（源表、指标表）
- 字段管理API（源字段、指标字段）
- 码值集管理API
- 数据主题管理API
- 关联关系管理API
- 搜索功能API（向量/混合/精确搜索）
- 系统管理API（健康检查/概览/状态）
"""

from .main import router

__all__ = [
    "router",
]

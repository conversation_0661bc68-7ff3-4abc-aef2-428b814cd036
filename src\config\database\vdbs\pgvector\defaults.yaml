# @package database.vdbs.pgvector
# PGVector默认配置

defaults:
  - _self_
  - connection
  # Load priority configurations
  - priority/standard@priority  # 默认使用标准优先级


# 客户端目标类
_target_: base.db.implementations.vs.pgvector.factory.create_pgvector_client

# 数据库类型标识
db_type: "pgvector"

# 基础连接池配置
min_connections: 5
max_connections: 25
pool_timeout: 30.0
pool_recycle: 3600
pool_pre_ping: true

# 基础缓存配置
enable_cache: true
cache_size: 1000
cache_ttl: 3600

# 向量配置
vector_dimension: 1536
distance_metric: "cosine"

# 搜索配置
search_params:
  default_limit: 10
  max_limit: 100
  similarity_threshold: 0.7

# PostgreSQL特定配置
postgres_specific:
  isolation_level: "READ_COMMITTED"
  application_name: "pgvector_client"

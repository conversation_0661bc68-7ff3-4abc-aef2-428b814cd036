"""
NLP分词处理器

替换TF-IDF分词，使用基础NLP分词算法进行关键词提取
"""

import re
import jieba
import logging
from typing import List, Set, Dict, Any

logger = logging.getLogger(__name__)


class NLPProcessor:
    """NLP分词处理器"""
    
    def __init__(self):
        """初始化NLP处理器"""
        # 停用词列表
        self.stop_words = self._load_stop_words()

        # 扩展的金融业务词汇（基于jieba分析结果优化）
        self.financial_vocabulary = {
            # 风险相关
            '风险评级': {'freq': 1000, 'pos': 'n'},
            '信用风险': {'freq': 800, 'pos': 'n'},
            '操作风险': {'freq': 600, 'pos': 'n'},
            '市场风险': {'freq': 500, 'pos': 'n'},
            '风险管理': {'freq': 900, 'pos': 'n'},
            '风险控制': {'freq': 700, 'pos': 'n'},

            # 客户相关
            '客户评级': {'freq': 900, 'pos': 'n'},
            '客户画像': {'freq': 700, 'pos': 'n'},
            '客户分层': {'freq': 400, 'pos': 'n'},
            '客户信息': {'freq': 800, 'pos': 'n'},

            # 系统相关
            '核心系统': {'freq': 800, 'pos': 'n'},
            '风控系统': {'freq': 600, 'pos': 'n'},
            '报送系统': {'freq': 500, 'pos': 'n'},
            '管理系统': {'freq': 700, 'pos': 'n'},
            '银行系统': {'freq': 600, 'pos': 'n'},

            # 业务相关
            '零售业务': {'freq': 900, 'pos': 'n'},
            '对公业务': {'freq': 800, 'pos': 'n'},
            '同业业务': {'freq': 400, 'pos': 'n'},
            '银行业务': {'freq': 800, 'pos': 'n'},
            '业务流程': {'freq': 600, 'pos': 'n'},

            # 合规相关
            '合规管理': {'freq': 700, 'pos': 'n'},
            '监管报送': {'freq': 600, 'pos': 'n'},
            '合规监管': {'freq': 500, 'pos': 'n'},
            '反洗钱': {'freq': 500, 'pos': 'n'},

            # 数据相关
            '数据项': {'freq': 800, 'pos': 'n'},
            '数据治理': {'freq': 600, 'pos': 'n'},
            '数据管理': {'freq': 700, 'pos': 'n'},
            '信息管理': {'freq': 600, 'pos': 'n'},

            # 基础词汇
            '客户': {'freq': 1000, 'pos': 'n'},
            '账户': {'freq': 900, 'pos': 'n'},
            '交易': {'freq': 800, 'pos': 'n'},
            '余额': {'freq': 700, 'pos': 'n'},
            '金额': {'freq': 600, 'pos': 'n'},
            '银行': {'freq': 1000, 'pos': 'n'},
            '金融': {'freq': 900, 'pos': 'n'},
            '风险': {'freq': 1000, 'pos': 'n'},
            '合规': {'freq': 800, 'pos': 'n'},
            '监管': {'freq': 700, 'pos': 'n'},
            '报送': {'freq': 600, 'pos': 'n'}
        }

        # 添加金融词汇到jieba词典（使用频率和词性信息）
        self._load_financial_vocabulary()

    def _load_financial_vocabulary(self):
        """
        加载金融业务词汇到jieba词典

        利用jieba的add_word功能，添加带有频率和词性的专业词汇
        """
        logger.info(f"开始加载{len(self.financial_vocabulary)}个金融业务词汇")

        for word, info in self.financial_vocabulary.items():
            # 使用jieba.add_word添加词汇，包含频率和词性信息
            jieba.add_word(word, freq=info['freq'], tag=info['pos'])

        logger.info("✅ 金融业务词汇加载完成")

    def add_custom_word(self, word: str, freq: int = 100, pos: str = 'n'):
        """
        动态添加自定义词汇

        Args:
            word: 词汇
            freq: 词频（影响分词优先级）
            pos: 词性
        """
        jieba.add_word(word, freq=freq, tag=pos)
        self.financial_vocabulary[word] = {'freq': freq, 'pos': pos}
        logger.info(f"添加自定义词汇: {word} (频率: {freq}, 词性: {pos})")

    def get_vocabulary_stats(self) -> Dict[str, Any]:
        """
        获取词汇统计信息

        Returns:
            词汇统计信息
        """
        return {
            'total_words': len(self.financial_vocabulary),
            'pos_distribution': self._get_pos_distribution(),
            'freq_distribution': self._get_freq_distribution()
        }

    def _get_pos_distribution(self) -> Dict[str, int]:
        """获取词性分布"""
        pos_count = {}
        for word_info in self.financial_vocabulary.values():
            pos = word_info['pos']
            pos_count[pos] = pos_count.get(pos, 0) + 1
        return pos_count

    def _get_freq_distribution(self) -> Dict[str, int]:
        """获取频率分布"""
        freq_ranges = {'高频(800+)': 0, '中频(400-799)': 0, '低频(<400)': 0}
        for word_info in self.financial_vocabulary.values():
            freq = word_info['freq']
            if freq >= 800:
                freq_ranges['高频(800+)'] += 1
            elif freq >= 400:
                freq_ranges['中频(400-799)'] += 1
            else:
                freq_ranges['低频(<400)'] += 1
        return freq_ranges
    
    def _load_stop_words(self) -> Set[str]:
        """加载停用词列表"""
        # 基础停用词
        stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '它', '他', '她', '们', '个', '中', '来', '为', '能', '对',
            '与', '及', '或', '但', '而', '因', '所以', '如果', '虽然', '然而', '因此',
            '包括', '包含', '具有', '进行', '实现', '完成', '处理', '管理', '操作', '使用',
            '相关', '关于', '属于', '用于', '基于', '通过', '根据', '按照', '依据', '符合'
        }
        return stop_words
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """
        从文本中提取关键词
        
        Args:
            text: 输入文本
            max_keywords: 最大关键词数量
            
        Returns:
            关键词列表
        """
        if not text or not text.strip():
            return []
        
        try:
            # 1. 预处理文本
            cleaned_text = self._preprocess_text(text)
            
            # 2. jieba分词
            words = jieba.lcut(cleaned_text)
            
            # 3. 过滤和清理
            keywords = self._filter_words(words)
            
            # 4. 去重并限制数量
            unique_keywords = list(dict.fromkeys(keywords))  # 保持顺序的去重
            
            return unique_keywords[:max_keywords]
            
        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return []
    
    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 转换为小写
        text = text.lower()
        
        # 移除特殊字符，保留中文、英文、数字
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _filter_words(self, words: List[str]) -> List[str]:
        """过滤词汇"""
        filtered_words = []
        
        for word in words:
            # 跳过空词和单字符词（除非是重要的单字符）
            if not word or len(word.strip()) == 0:
                continue
            
            word = word.strip()
            
            # 跳过停用词
            if word in self.stop_words:
                continue
            
            # 跳过纯数字（除非是重要的业务编号）
            if word.isdigit() and len(word) < 3:
                continue
            
            # 跳过单个字符（除非是重要字符）
            if len(word) == 1 and word not in {'ID', 'id', 'Id'}:
                continue
            
            # 保留有意义的词汇
            if len(word) >= 2 or word in self.business_keywords:
                filtered_words.append(word)
        
        return filtered_words
    
    def extract_keywords_from_fields(self, dr09: str, dr17: str) -> Dict[str, List[str]]:
        """
        从dr09和dr17字段提取关键词

        Args:
            dr09: 数据项名称
            dr17: 数据项定义

        Returns:
            包含各字段关键词的字典
        """
        # 提取分词关键词
        dr09_keywords = self.extract_keywords(dr09, max_keywords=5)
        dr17_keywords = self.extract_keywords(dr17, max_keywords=8)

        # 合并关键词并去重（dr09权重更高）
        combined_keywords = dr09_keywords + dr17_keywords
        unique_combined = list(dict.fromkeys(combined_keywords))[:10]

        # 构建完整的关键词列表，包含原始字段和分词结果
        all_keywords = []

        # 1. 添加原始dr09和dr17（如果不为空）
        if dr09 and dr09.strip():
            all_keywords.append(dr09.strip())
        if dr17 and dr17.strip():
            all_keywords.append(dr17.strip())

        # 2. 添加分词后的关键词（去重）
        for keyword in unique_combined:
            if keyword not in all_keywords:
                all_keywords.append(keyword)

        result = {
            'dr09_keywords': dr09_keywords,
            'dr17_keywords': dr17_keywords,
            'combined_keywords': unique_combined,
            'all_keywords': all_keywords,
            'original_dr09': dr09.strip() if dr09 else '',
            'original_dr17': dr17.strip() if dr17 else ''
        }

        return result
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的相似度（基于关键词重叠）
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            相似度分数 (0-1)
        """
        try:
            keywords1 = set(self.extract_keywords(text1))
            keywords2 = set(self.extract_keywords(text2))
            
            if not keywords1 or not keywords2:
                return 0.0
            
            # 计算Jaccard相似度
            intersection = len(keywords1 & keywords2)
            union = len(keywords1 | keywords2)
            
            if union == 0:
                return 0.0
            
            return intersection / union
            
        except Exception as e:
            logger.error(f"文本相似度计算失败: {e}")
            return 0.0
    
    def find_matching_keywords(self, source_keywords: List[str], target_text: str) -> Dict[str, Any]:
        """
        在目标文本中查找匹配的关键词
        
        Args:
            source_keywords: 源关键词列表
            target_text: 目标文本
            
        Returns:
            匹配结果
        """
        target_keywords = set(self.extract_keywords(target_text))
        matched_keywords = []
        
        for keyword in source_keywords:
            if keyword in target_keywords:
                matched_keywords.append(keyword)
        
        match_ratio = len(matched_keywords) / len(source_keywords) if source_keywords else 0.0
        
        return {
            'matched_keywords': matched_keywords,
            'match_count': len(matched_keywords),
            'total_source_keywords': len(source_keywords),
            'match_ratio': match_ratio,
            'target_keywords': list(target_keywords)
        }
    
    def enhance_search_query(self, dr09: str, dr17: str) -> Dict[str, Any]:
        """
        增强搜索查询，生成多种搜索策略的关键词
        
        Args:
            dr09: 数据项名称
            dr17: 数据项定义
            
        Returns:
            增强的搜索查询信息
        """
        keywords_info = self.extract_keywords_from_fields(dr09, dr17)
        
        # 生成不同的搜索策略
        search_strategies = {
            'exact_match': {
                'dr09': dr09.strip(),
                'dr17': dr17.strip()
            },
            'keyword_match': {
                'primary_keywords': keywords_info['dr09_keywords'][:3],  # 主要关键词
                'secondary_keywords': keywords_info['dr17_keywords'][:5],  # 次要关键词
                'all_keywords': keywords_info['combined_keywords']
            },
            'fuzzy_match': {
                'query_text': f"{dr09} {dr17}",
                'keywords': keywords_info['combined_keywords']
            }
        }
        
        return {
            'keywords_info': keywords_info,
            'search_strategies': search_strategies,
            'search_text': f"{dr09} {dr17}".strip()
        }


# 全局实例（单例模式）
_nlp_processor_instance = None

def get_nlp_processor() -> NLPProcessor:
    """获取NLP处理器实例（单例）"""
    global _nlp_processor_instance
    if _nlp_processor_instance is None:
        _nlp_processor_instance = NLPProcessor()
    return _nlp_processor_instance

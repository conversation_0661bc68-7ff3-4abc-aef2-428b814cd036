#!/usr/bin/env python3
"""
DD-B批量增强处理器测试

测试集成到现有框架的批量处理功能：
1. 使用正确的客户端获取方式
2. 使用正确的参数格式
3. 集成现有的DDB框架
4. 测试批量处理流程
"""

import asyncio
import os
import sys
import time
from typing import Dict, Any, List

# 设置项目根目录
project_root = os.getcwd()
sys.path.insert(0, project_root)

# 设置日志
from utils.common.logger import setup_enterprise_plus_logger
logger = setup_enterprise_plus_logger(
    name="batch_enhanced_processor_test",
    level="INFO"
)


async def get_database_clients():
    """获取数据库客户端（使用正确的方式）"""
    try:
        from service import get_client

        # 获取必需的MySQL客户端
        rdb_client = await get_client('database.rdbs.mysql')
        logger.info("✅ RDB客户端获取成功")

        # 尝试获取可选的向量数据库和embedding客户端
        vdb_client = None
        embedding_client = None

        try:
            vdb_client = await get_client('database.vdbs.pgvector')
            logger.info("✅ VDB客户端获取成功")
        except Exception as e:
            logger.warning(f"⚠️ VDB客户端获取失败: {e}")

        try:
            embedding_client = await get_client('model.embeddings.moka-m3e-base')
            logger.info("✅ Embedding客户端获取成功")
        except Exception as e:
            logger.warning(f"⚠️ Embedding客户端获取失败: {e}")

        return {
            'rdb_client': rdb_client,
            'vdb_client': vdb_client,
            'embedding_client': embedding_client
        }

    except Exception as e:
        logger.error(f"❌ 数据库客户端获取失败: {e}")
        raise


async def test_batch_enhanced_processor():
    """测试批量增强处理器"""
    print("\n🚀 开始DD-B批量增强处理器测试")
    print("=" * 60)
    
    try:
        # 1. 获取数据库客户端
        logger.info("1️⃣ 获取数据库客户端...")
        clients = await get_database_clients()
        rdb_client = clients['rdb_client']
        vdb_client = clients['vdb_client']
        embedding_client = clients['embedding_client']
        
        # 2. 导入批量处理模块
        logger.info("2️⃣ 导入批量处理模块...")
        from modules.dd_submission.dd_b import (
            process_dd_b_batch_request,
            BatchProcessRequest,
            BatchProcessResult
        )
        
        # 3. 准备测试参数（使用正确的格式）
        logger.info("3️⃣ 准备测试参数...")
        dept_id = '["30239"]'  # 正确的格式
        version = "S71_ADS_RELEASE_V0"
        where_conditions = [
            {"table_id": "1"},
            {"table_id": "2"},
            {"table_id": "3"},
        ]
        
        print(f"📋 测试参数:")
        print(f"  dept_id: {dept_id}")
        print(f"  version: {version}")
        print(f"  where_conditions: {len(where_conditions)}个条件")
        for i, condition in enumerate(where_conditions, 1):
            print(f"    {i}. {condition}")
        
        # 4. 执行批量处理
        logger.info("4️⃣ 执行批量处理...")
        start_time = time.time()
        
        result = await process_dd_b_batch_request(
            rdb_client=rdb_client,
            dept_id=dept_id,
            version=version,
            where_conditions=where_conditions,
            vdb_client=vdb_client,
            embedding_client=embedding_client,
            max_workers=15
        )
        
        processing_time = time.time() - start_time
        
        # 5. 验证处理结果
        logger.info("5️⃣ 验证处理结果...")
        print(f"\n📊 处理结果:")
        print(f"  状态: {result.status}")
        print(f"  总记录数: {result.total_records}")
        print(f"  成功处理: {result.processed_records}")
        print(f"  失败记录: {result.failed_records}")
        print(f"  处理耗时: {result.processing_time:.2f}s")
        print(f"  Pipeline耗时: {result.pipeline_time:.2f}s")
        print(f"  聚合耗时: {result.aggregation_time:.2f}s")
        
        if result.total_records > 0:
            success_rate = result.processed_records / result.total_records * 100
            avg_time_ms = result.processing_time * 1000 / result.total_records
            print(f"  成功率: {success_rate:.1f}%")
            print(f"  平均每条: {avg_time_ms:.1f}ms")
        
        # 6. 验证结果结构
        logger.info("6️⃣ 验证结果结构...")
        if result.results:
            sample_record = result.results[0]
            print(f"\n📋 结果结构验证:")
            print(f"  结果记录数: {len(result.results)}")
            print(f"  示例记录类型: {type(sample_record)}")
            
            # 检查DDBRecord字段
            if hasattr(sample_record, 'bdr09'):
                print(f"  ✅ 包含BDR字段")
            if hasattr(sample_record, 'sdr09'):
                print(f"  ✅ 包含SDR字段")
        else:
            print(f"  ❌ 未获取到处理结果")
        
        # 7. 错误分析
        if result.errors:
            logger.info("7️⃣ 错误分析...")
            print(f"\n⚠️ 错误信息:")
            for i, error in enumerate(result.errors[:3], 1):
                print(f"  {i}. {error}")
            if len(result.errors) > 3:
                print(f"  ... 还有{len(result.errors) - 3}个错误")
        
        # 8. 测试总结
        logger.info("8️⃣ 测试总结...")
        print(f"\n🎯 测试总结:")
        if result.status.name == "SUCCESS":
            print(f"  🎉 测试完全成功!")
        elif result.status.name == "PARTIAL_SUCCESS":
            print(f"  ⚠️ 测试部分成功")
        else:
            print(f"  ❌ 测试失败")
        
        print(f"  📊 最终统计:")
        print(f"    处理记录: {result.processed_records}/{result.total_records}")
        if result.total_records > 0:
            print(f"    成功率: {result.processed_records/result.total_records*100:.1f}%")
        print(f"    总耗时: {result.processing_time:.2f}s")
        
        return result
        
    except Exception as e:
        logger.error(f"批量增强处理器测试失败: {e}")
        print(f"\n❌ 测试失败: {e}")
        raise


async def test_client_integration():
    """测试客户端集成"""
    print("\n🔧 客户端集成测试")
    print("=" * 40)
    
    try:
        # 测试客户端获取
        clients = await get_database_clients()
        
        print(f"📋 客户端状态:")
        print(f"  RDB客户端: {'✅' if clients['rdb_client'] else '❌'}")
        print(f"  VDB客户端: {'✅' if clients['vdb_client'] else '❌'}")
        print(f"  Embedding客户端: {'✅' if clients['embedding_client'] else '❌'}")
        
        # 测试模块导入
        from modules.dd_submission.dd_b import (
            BatchEnhancedProcessor,
            BatchProcessRequest,
            process_dd_b_batch_request
        )
        
        print(f"📦 模块导入:")
        print(f"  BatchEnhancedProcessor: ✅")
        print(f"  BatchProcessRequest: ✅")
        print(f"  process_dd_b_batch_request: ✅")
        
        # 测试参数格式
        dept_id = '["30239"]'
        version = "S71_ADS_RELEASE_V0"
        where_conditions = [{"table_id": "1"}]
        
        batch_request = BatchProcessRequest(
            dept_id=dept_id,
            version=version,
            where_conditions=where_conditions
        )
        
        print(f"📝 参数格式:")
        print(f"  dept_id格式: {'✅' if dept_id.startswith('[') else '❌'}")
        print(f"  BatchProcessRequest创建: ✅")
        
        ddb_requests = batch_request.to_ddb_requests()
        print(f"  转换为DDBProcessRequest: ✅ ({len(ddb_requests)}个)")
        
    except Exception as e:
        print(f"❌ 客户端集成测试失败: {e}")


async def test_parameter_formats():
    """测试参数格式"""
    print("\n📝 参数格式测试")
    print("=" * 40)
    
    # 测试不同的参数格式
    test_cases = [
        {
            "name": "标准格式",
            "dept_id": '["30239"]',
            "version": "S71_ADS_RELEASE_V0",
            "where_conditions": [{"table_id": "1"}]
        },
        {
            "name": "多条件格式",
            "dept_id": '["30239"]',
            "version": "S71_ADS_RELEASE_V0", 
            "where_conditions": [
                {"table_id": "1"},
                {"table_id": "2", "category": "A"},
                {"table_id": "3", "status": "active"}
            ]
        },
        {
            "name": "复杂条件格式",
            "dept_id": '["30239", "30240"]',
            "version": "S71_ADS_RELEASE_V0",
            "where_conditions": [
                {"table_id": str(i), "type": f"type_{i%3}"} 
                for i in range(1, 6)
            ]
        }
    ]
    
    for case in test_cases:
        print(f"\n📋 {case['name']}:")
        print(f"  dept_id: {case['dept_id']}")
        print(f"  version: {case['version']}")
        print(f"  条件数: {len(case['where_conditions'])}")
        print(f"  示例条件: {case['where_conditions'][0]}")
        
        try:
            from modules.dd_submission.dd_b import BatchProcessRequest
            request = BatchProcessRequest(
                dept_id=case['dept_id'],
                version=case['version'],
                where_conditions=case['where_conditions']
            )
            ddb_requests = request.to_ddb_requests()
            print(f"  ✅ 格式验证通过，生成{len(ddb_requests)}个DDB请求")
        except Exception as e:
            print(f"  ❌ 格式验证失败: {e}")


async def main():
    """主函数"""
    print("DD-B批量增强处理器完整测试")
    print("=" * 80)
    
    try:
        # 客户端集成测试
        await test_client_integration()
        
        # 参数格式测试
        await test_parameter_formats()
        
        # 主要功能测试
        result = await test_batch_enhanced_processor()
        
        print("\n" + "=" * 80)
        print("🎉 所有测试完成!")
        
        if result and result.status.name in ["SUCCESS", "PARTIAL_SUCCESS"]:
            print("✅ 批量增强处理器工作正常")
        else:
            print("❌ 批量增强处理器存在问题")
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        print(f"\n❌ 测试执行失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())

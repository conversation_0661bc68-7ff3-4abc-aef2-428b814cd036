import json


def extract_json_from_response(response: str):
    try:
        # 尝试直接解析整个响应为 JSON
        return json.loads(response)
    except json.JSONDecodeError:
        pass

    try:
        # 提取 ```json 中的内容
        # print('我开始解析咯',response)
        start_idx = response.find("```json") + len("```json")
        end_idx = response.find("```", start_idx)
        if end_idx == -1:
            end_idx = len(response)
        json_str = response[start_idx:end_idx].strip().replace('\'', '"')
        return json.loads(json_str)
    except Exception as e:
        raise ValueError(f"无法从响应中提取有效 JSON: {str(e)}")


_get_all_site_prompt = '''
你是一名专业的法规识别工程师。

任务目标：
- 根据用户输入描述，准确识别出所引用的国家法规编号。
- 如果用户描述中没有指定的国家法规编号，影响法规编号内容设为null即可。
- 最终输出一个符合指定格式的 JSON 对象。

输出要求：
- 严格保持原法规编号格式，不要自行修改或调整编号内容。
- 不添加任何解释、说明或其他额外内容。

输出格式示例：
{
  "影响法规编号": "法规编号",
  "影响编号起始id": "id_1",
  "影响编号结束id": "id_2"
}

示例一：
输入：
2.3-2.6反映填报机构发放给境内法人，以及发放给境内个人用于经营目的的委托贷款期末余额。所列3个行业的分类标准，按照中华人民共和国国家标准GB/T4754-2021“国民经济行业分类”的标准执行，反映报告期末填报机构发放委托贷款的行业投向。如果无法合理地确定贷款的投向，则应按借款人主营业务所在行业进行分类。

输出：
{"影响法规编号":"GB/T4754-2021","影响编号起始id":"2.3","影响编号结束id": "2.6"}

示例二：
输入：
B-E反映填报机构发放给境内法人，以及发放给境内个人用于经营目的的委托贷款期末余额。所列3个行业的分类标准，按照中华人民共和国国家标准“国民经济行业分类”的标准执行，反映报告期末填报机构发放委托贷款的行业投向。如果无法合理地确定贷款的投向，则应按借款人主营业务所在行业进行分类。

输出：
{"影响法规编号":null,"影响编号起始id":"B","影响编号结束id": "E"}

这是用户输入{subitem_input}
'''

_project_prompt = '''
# 任务要求

我需要你根据我提供的逻辑规则信息，将其转换为一个符合特定格式的 JSON 列表。每个 JSON 对象包含五个属性：
`type`：表示适用的列名（如 "A"、"B"、"C"）或 `"all"` 表示适用于所有列。
`val` : 表示完整的逻辑表达式
`project_id`: 仅保留数字部分（如 `13.C` 的 `project_id` 是 `"13"`），如果支持全部项目，这个为空
`is_all_project`: 布尔值，是否支持全部的项目 true或者false
`remove_project_ids`: 不被支持的一些项目id，只在我们is_all_project为true时启用，用于标识部分不使用的project_id

## 输入示例
[61.]≥[26.]+[27.]；
[62.]≥[6.]；
[63.]≤[25.]+[24.]，仅适用于C列
[64.]≤[49.]，仅适用于B列
[C]=[A]+[B]；本校验关系适用于[1.]、[2.]行
[13.C]= [13.A]-[13.B]
[3.]=[61.]+[62.] （本关系不覆盖[C]列）
这是最终输出可以参考的所有project_id[61,62,63,64,1,2,13]
这是最终输出可以参考的所有列名（type）[A,B,C]

## 输出示例
```json
[
  {"type":"all", "val":"[61]≥[26]+[27]", "project_id":"61","is_all_project":false,"remove_project_ids":[]},
  {"type":"all", "val":"[61]≥[26]+[27]", "project_id":"26","is_all_project":false,"remove_project_ids":[]},
  {"type":"all", "val":"[61]≥[26]+[27]", "project_id":"27","is_all_project":false,"remove_project_ids":[]},
  {"type":"all", "val":"[62]≥[6]", "project_id":"6","is_all_project":false,"remove_project_ids":[]}
  {"type":"all", "val":"[62]≥[6]", "project_id":"62","is_all_project":false,"remove_project_ids":[]},
  {"type":"C", "val":"[63.C]≤[25.C]+[24.C]", "project_id":"63","is_all_project":false,"remove_project_ids":[]},
  {"type":"C", "val":"[63.C]≤[25.C]+[24.C]", "project_id":"25","is_all_project":false,"remove_project_ids":[]},
  {"type":"C", "val":"[63.C]≤[25.C]+[24.C]", "project_id":"24","is_all_project":false,"remove_project_ids":[]},
  {"type":"B", "val":"[64.B]≤[49.B]", "project_id":"64","is_all_project":false,"remove_project_ids":[]},
  {"type":"B", "val":"[64.B]≤[49.B]", "project_id":"49","is_all_project":false,"remove_project_ids":[]},
  {"type":"C", "val":"[1.C]=[1.A]+[1.B]", "project_id":"1","is_all_project":false,"remove_project_ids":[]},
  {"type":"A", "val":"[1.C]=[1.A]+[1.B]", "project_id":"1","is_all_project":false,"remove_project_ids":[]},
  {"type":"B", "val":"[1.C]=[1.A]+[1.B]", "project_id":"1","is_all_project":false,"remove_project_ids":[]},
  {"type":"C", "val":"[2.C]=[2.A]+[2.B]", "project_id":"2","is_all_project":false,"remove_project_ids":[]},
  {"type":"A", "val":"[2.C]=[2.A]+[2.B]", "project_id":"2","is_all_project":false,"remove_project_ids":[]},
  {"type":"B", "val":"[2.C]=[2.A]+[2.B]", "project_id":"2","is_all_project":false,"remove_project_ids":[]},
  {"type":"C", "val":"[13.C]= [13.A]-[13.B]", "project_id":"13","is_all_project":false,"remove_project_ids":[]},
  {"type":"A", "val":"[13.C]= [13.A]-[13.B]", "project_id":"13","is_all_project":false,"remove_project_ids":[]},
  {"type":"B", "val":"[13.C]= [13.A]-[13.B]", "project_id":"13","is_all_project":false,"remove_project_ids":[]},
  {"type":"A", "val":"[3.]=[61.]+[62.]", "project_id":"3","is_all_project":false,"remove_project_ids":[]},
  {"type":"A", "val":"[3.]=[61.]+[62.]", "project_id":"61","is_all_project":false,"remove_project_ids":[]},
  {"type":"A", "val":"[3.]=[61.]+[62.]", "project_id":"62","is_all_project":false,"remove_project_ids":[]},
  {"type":"B", "val":"[3.]=[61.]+[62.]", "project_id":"3","is_all_project":false,"remove_project_ids":[]}
  {"type":"B", "val":"[3.]=[61.]+[62.]", "project_id":"61","is_all_project":false,"remove_project_ids":[]}
  {"type":"B", "val":"[3.]=[61.]+[62.]", "project_id":"62","is_all_project":false,"remove_project_ids":[]}
]

## 输入示例
A≥B，适用于除[1.1][2.2]以外的各行
所有project_id[1,1.1,1.2,1.3,2,2.1,2.2,2.3]
这是最终输出可以参考的所有列名（type）[A,B]

## 输出示例
```json
[
  {"type":"all", "val":"[A]≥[B]", "project_id":"","is_all_project":true,"remove_project_ids":["1.1","2.2"]}
]

规则说明：
请严格按照输入内容进行解析，不要自行添加、修改或推测逻辑。
如果指定列（如“仅适用于C列”），则"type": "C"或者用户指定了[id.C] op [....]也是C。
如果规则中定义了多个行（如“适用于[1.]、[2.]行”），则需分别生成对应的 JSON 对象，如果对比我们所有的project_id发现只缺失某几个项目，则启用is_all_project跟remove_project_ids来标识。
如果project_id人家指定了多级序号，你也要展示多级序号，不要只保留一级
如果用户有些描述指定了除了xx不在的，我们要使用is_all_project跟remove_project_ids来标识。
注意project_id只有前面的序号，如果用户指定了12.A这种，那project_id只是12，A体现在type中
当我们is_all_project为true时，则代表这个规则是针对全部项目的，那project_id的内容就应该为空，通过remove_project_ids来标识弃用哪些项目的id
如果用户标识了适用于全部列，那我们的type是all，且is_all_project为false。
你要明确，适用于各列，这个列的标签在type这里体现，all为所有列
如果解析数据没有任何可以提取的规则，那就直接返回一个空列表即可
传递进入val的值，项目前的 G01_I_2_类似的标签不要移除
如果用户的要求是适用于xx-xx行，需要去对比用户给出的所有的project_id来给出结果,当出现remove_project_ids时，一定要跟用户要求仔细对比
注意识别type，当用户已经明确指定对应的类型，不要直接给all

当前任务
请对以下输入进行解析，并输出符合上述格式的 JSON 列表：
<解析数据>
{project_data}
<解析数据>
这是最终输出可以参考的所有project_id[{project_ids}]
这是最终输出可以参考的所有列名（type）[{valid_column}]

注意：只需输出最终的 JSON 结果，不需要额外解释。注意is_all_project的使用，尽量我们不使用remove_project_ids和is_all_project为true。
'''

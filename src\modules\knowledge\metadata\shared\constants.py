"""
Metadata模块常量定义

参考DD系统的constants.py设计，定义元数据相关的常量。
"""


class MetadataConstants:
    """元数据常量"""
    
    # 数据层类型
    DATA_LAYER_ADM = "adm"
    DATA_LAYER_BDM = "bdm"
    DATA_LAYER_ODS = "ods"
    DATA_LAYER_ADS = "ads"
    
    # 支持的数据层类型列表
    SUPPORTED_DATA_LAYERS = [DATA_LAYER_ADM, DATA_LAYER_BDM, DATA_LAYER_ODS, DATA_LAYER_ADS]
    
    # 数据类型
    DATA_TYPE_NUMBER = "NUMBER"
    DATA_TYPE_STRING = "STRING"
    DATA_TYPE_DATE = "DATE"
    DATA_TYPE_TEXT = "TEXT"
    DATA_TYPE_INDEX_DATE = "INDEX_DATE"
    
    # 支持的数据类型列表
    SUPPORTED_DATA_TYPES = [DATA_TYPE_NUMBER, DATA_TYPE_STRING, DATA_TYPE_DATE, DATA_TYPE_TEXT, DATA_TYPE_INDEX_DATE]
    
    # 指标类型
    INDEX_TYPE_ATOM = "atom"
    INDEX_TYPE_COMPUTE = "compute"
    
    # 支持的指标类型列表
    SUPPORTED_INDEX_TYPES = [INDEX_TYPE_ATOM, INDEX_TYPE_COMPUTE]
    
    # 关联类型
    RELATION_TYPE_FK = "FK"
    RELATION_TYPE_REF = "REF"
    
    # 支持的关联类型列表
    SUPPORTED_RELATION_TYPES = [RELATION_TYPE_FK, RELATION_TYPE_REF]
    
    # 字段类型
    COLUMN_TYPE_SOURCE = "source"
    COLUMN_TYPE_INDEX = "index"
    
    # 支持的字段类型列表
    SUPPORTED_COLUMN_TYPES = [COLUMN_TYPE_SOURCE, COLUMN_TYPE_INDEX]
    
    # 数据源类型
    SOURCE_TYPE_SOURCE = "SOURCE"
    SOURCE_TYPE_INDEX = "INDEX"
    
    # 支持的数据源类型列表
    SUPPORTED_SOURCE_TYPES = [SOURCE_TYPE_SOURCE, SOURCE_TYPE_INDEX]
    
    # 关联层级
    RELATION_LEVEL_DATABASE = "DATABASE"
    RELATION_LEVEL_TABLE = "TABLE"
    RELATION_LEVEL_COLUMN = "COLUMN"
    
    # 支持的关联层级列表
    SUPPORTED_RELATION_LEVELS = [RELATION_LEVEL_DATABASE, RELATION_LEVEL_TABLE, RELATION_LEVEL_COLUMN]
    
    # 码值集类型
    CODE_SET_TYPE_ENUM = "ENUM"
    
    # 支持的码值集类型列表
    SUPPORTED_CODE_SET_TYPES = [CODE_SET_TYPE_ENUM]
    
    # 向量化字段
    VECTORIZED_FIELDS = [
        "db_desc",           # 数据库描述
        "db_name",           # 数据库名称
        "db_name_cn",        # 数据库中文名称
        "table_desc",        # 表描述
        "table_name",        # 表名称
        "table_name_cn",     # 表中文名称
        "column_desc",       # 字段描述
        "column_name",       # 字段名称
        "column_name_cn",    # 字段中文名称
        "code_set_desc",     # 码值集描述（包含中文名称）
        "code_set_name",     # 码值集名称
        "code_value_cn",     # 码值中文描述
        "subject_desc",      # 数据主题描述
        "comment"            # 备注说明
    ]
    
    # 分页默认值
    DEFAULT_PAGE_SIZE = 20
    DEFAULT_MAX_PAGE_SIZE = 100


class MetadataTableNames:
    """元数据表名常量"""
    
    # 源数据库相关表
    MD_SOURCE_DATABASE = "md_source_database"
    MD_SOURCE_TABLES = "md_source_tables"
    MD_SOURCE_COLUMNS = "md_source_columns"
    MD_SOURCE_KEY_RELATION_INFO = "md_source_key_relation_info"
    
    # 指标数据库相关表
    MD_INDEX_DATABASE = "md_index_database"
    MD_INDEX_TABLES = "md_index_tables"
    MD_INDEX_COLUMNS = "md_index_columns"
    MD_INDEX_KEY_RELATION_INFO = "md_index_key_relation_info"
    
    # 码值相关表
    MD_REFERENCE_CODE_SET = "md_reference_code_set"
    MD_REFERENCE_CODE_RELATION = "md_reference_code_relation"
    MD_REFERENCE_CODE_VALUE = "md_reference_code_value"
    
    # 数据主题相关表
    MD_DATA_SUBJECT = "md_data_subject"
    MD_DATA_SUBJECT_RELATION = "md_data_subject_relation"
    
    # 知识库主表
    KB_KNOWLEDGE = "kb_knowledge"


class MetadataCascadeRelations:
    """元数据级联关系映射"""

    # 级联删除关系映射：父表 -> 子表列表
    CASCADE_DELETE_RELATIONS = {
        # 源数据库级联关系
        MetadataTableNames.MD_SOURCE_DATABASE: [
            MetadataTableNames.MD_SOURCE_TABLES,
            MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO
        ],
        MetadataTableNames.MD_SOURCE_TABLES: [
            MetadataTableNames.MD_SOURCE_COLUMNS
        ],

        # 指标数据库级联关系
        MetadataTableNames.MD_INDEX_DATABASE: [
            MetadataTableNames.MD_INDEX_TABLES,
            MetadataTableNames.MD_INDEX_KEY_RELATION_INFO
        ],
        MetadataTableNames.MD_INDEX_TABLES: [
            MetadataTableNames.MD_INDEX_COLUMNS
        ],

        # 码值级联关系
        MetadataTableNames.MD_REFERENCE_CODE_SET: [
            MetadataTableNames.MD_REFERENCE_CODE_VALUE,
            MetadataTableNames.MD_REFERENCE_CODE_RELATION
        ],

        # 数据主题级联关系
        MetadataTableNames.MD_DATA_SUBJECT: [
            MetadataTableNames.MD_DATA_SUBJECT_RELATION
        ]
    }

    # 实体类型到表名的映射
    ENTITY_TYPE_TO_TABLE = {
        'source_database': MetadataTableNames.MD_SOURCE_DATABASE,
        'index_database': MetadataTableNames.MD_INDEX_DATABASE,
        'source_table': MetadataTableNames.MD_SOURCE_TABLES,
        'index_table': MetadataTableNames.MD_INDEX_TABLES,
        'source_column': MetadataTableNames.MD_SOURCE_COLUMNS,
        'index_column': MetadataTableNames.MD_INDEX_COLUMNS,
        'code_set': MetadataTableNames.MD_REFERENCE_CODE_SET,
        'code_value': MetadataTableNames.MD_REFERENCE_CODE_VALUE,
        'code_relation': MetadataTableNames.MD_REFERENCE_CODE_RELATION,
        'data_subject': MetadataTableNames.MD_DATA_SUBJECT,
        'subject_relation': MetadataTableNames.MD_DATA_SUBJECT_RELATION,
        'source_key_relation': MetadataTableNames.MD_SOURCE_KEY_RELATION_INFO,
        'index_key_relation': MetadataTableNames.MD_INDEX_KEY_RELATION_INFO
    }

    # 实体类型到主键字段的映射
    ENTITY_TYPE_TO_ID_FIELD = {
        'source_database': 'db_id',
        'index_database': 'db_id',
        'source_table': 'table_id',
        'index_table': 'table_id',
        'source_column': 'column_id',
        'index_column': 'column_id',
        'code_set': 'id',
        'code_value': 'id',
        'code_relation': 'id',
        'data_subject': 'id',
        'subject_relation': 'id',
        'source_key_relation': 'relation_id',
        'index_key_relation': 'relation_id'
    }


class MetadataVectorCollections:
    """元数据向量集合映射"""

    # 向量集合名称
    MD_TABLE_EMBEDDINGS = 'md_table_embeddings'
    MD_COLUMN_EMBEDDINGS = 'md_column_embeddings'
    MD_CODE_EMBEDDINGS = 'md_code_embeddings'
    CODE_RELATION = 'md_code_relation_embeddings'

    # 实体类型到向量集合的映射
    ENTITY_TYPE_TO_COLLECTION = {
        'source_database': MD_TABLE_EMBEDDINGS,  # 数据库级别使用表级集合
        'index_database': MD_TABLE_EMBEDDINGS,
        'source_table': MD_TABLE_EMBEDDINGS,
        'index_table': MD_TABLE_EMBEDDINGS,
        'source_column': MD_COLUMN_EMBEDDINGS,
        'index_column': MD_COLUMN_EMBEDDINGS,
        'code_value': MD_CODE_EMBEDDINGS,  # 只有码值创建向量，码值集不创建
        'code_relation': CODE_RELATION
    }

    # 需要向量化的实体类型
    VECTORIZED_ENTITY_TYPES = [
        'source_database',
        'index_database',
        'source_table',
        'index_table',
        'source_column',
        'index_column',
        'code_value',
        'code_relation'
    ]

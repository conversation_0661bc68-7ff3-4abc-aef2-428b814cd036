"""
文档模板解析器

支持多种格式的文档模板解析，包括Excel、CSV、JSON等。
"""

import json
import pandas as pd
from pathlib import Path
from typing import Any, Dict, List, Optional
import logging

# 标准化日志导入
logger = logging.getLogger(__name__)


class TemplateParser:
    """
    文档模板解析器
    
    支持多种格式的文档模板解析：
    - Excel文件（.xlsx, .xls）
    - CSV文件（.csv）
    - JSON文件（.json）
    """
    
    def __init__(self):
        """初始化模板解析器"""
        self.supported_formats = {
            "excel": [".xlsx", ".xls"],
            "csv": [".csv"],
            "json": [".json"]
        }
        logger.info("文档模板解析器初始化完成")
    
    async def parse_template(
        self, 
        file_path: Path, 
        template_type: str
    ) -> Dict[str, Any]:
        """
        解析文档模板
        
        Args:
            file_path: 文件路径
            template_type: 模板类型（excel/csv/json）
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            if template_type == "excel":
                return await self._parse_excel(file_path)
            elif template_type == "csv":
                return await self._parse_csv(file_path)
            elif template_type == "json":
                return await self._parse_json(file_path)
            else:
                raise ValueError(f"不支持的模板类型: {template_type}")
                
        except Exception as e:
            logger.error(f"模板解析失败: file_path={file_path}, template_type={template_type}, error={e}")
            raise
    
    async def _parse_excel(self, file_path: Path) -> Dict[str, Any]:
        """
        解析Excel文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            # 读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            
            parsed_data = {
                "file_type": "excel",
                "file_path": str(file_path),
                "sheets": {},
                "metadata": {
                    "sheet_count": len(excel_file.sheet_names),
                    "sheet_names": excel_file.sheet_names
                }
            }
            
            # 解析每个工作表
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                sheet_data = {
                    "name": sheet_name,
                    "rows": len(df),
                    "columns": len(df.columns),
                    "column_names": df.columns.tolist(),
                    "data_types": df.dtypes.astype(str).to_dict(),
                    "sample_data": df.head(5).to_dict("records"),
                    "has_header": True,  # 假设有表头
                    "metadata_candidates": self._identify_metadata_candidates(df)
                }
                
                parsed_data["sheets"][sheet_name] = sheet_data
            
            logger.info(f"Excel解析成功: file_path={file_path}, sheets={len(parsed_data['sheets'])}")
            return parsed_data
            
        except Exception as e:
            logger.error(f"Excel解析失败: file_path={file_path}, error={e}")
            raise
    
    async def _parse_csv(self, file_path: Path) -> Dict[str, Any]:
        """
        解析CSV文件
        
        Args:
            file_path: CSV文件路径
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(file_path)
            
            parsed_data = {
                "file_type": "csv",
                "file_path": str(file_path),
                "data": {
                    "rows": len(df),
                    "columns": len(df.columns),
                    "column_names": df.columns.tolist(),
                    "data_types": df.dtypes.astype(str).to_dict(),
                    "sample_data": df.head(5).to_dict("records"),
                    "has_header": True,  # 假设有表头
                    "metadata_candidates": self._identify_metadata_candidates(df)
                },
                "metadata": {
                    "encoding": "utf-8",  # 简化处理
                    "delimiter": ","
                }
            }
            
            logger.info(f"CSV解析成功: file_path={file_path}, rows={len(df)}")
            return parsed_data
            
        except Exception as e:
            logger.error(f"CSV解析失败: file_path={file_path}, error={e}")
            raise
    
    async def _parse_json(self, file_path: Path) -> Dict[str, Any]:
        """
        解析JSON文件
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            # 读取JSON文件
            with open(file_path, "r", encoding="utf-8") as f:
                json_data = json.load(f)
            
            parsed_data = {
                "file_type": "json",
                "file_path": str(file_path),
                "data": json_data,
                "metadata": {
                    "structure": self._analyze_json_structure(json_data),
                    "metadata_candidates": self._identify_json_metadata_candidates(json_data)
                }
            }
            
            logger.info(f"JSON解析成功: file_path={file_path}")
            return parsed_data
            
        except Exception as e:
            logger.error(f"JSON解析失败: file_path={file_path}, error={e}")
            raise
    
    def _identify_metadata_candidates(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """
        识别可能的元数据字段
        
        Args:
            df: DataFrame数据
            
        Returns:
            Dict[str, List[str]]: 元数据候选字段
        """
        try:
            candidates = {
                "database_names": [],
                "table_names": [],
                "column_names": [],
                "descriptions": [],
                "data_types": []
            }
            
            # 基于列名识别可能的元数据字段
            for col in df.columns:
                col_lower = col.lower()
                
                if any(keyword in col_lower for keyword in ["database", "db", "数据库"]):
                    candidates["database_names"].append(col)
                elif any(keyword in col_lower for keyword in ["table", "表", "表名"]):
                    candidates["table_names"].append(col)
                elif any(keyword in col_lower for keyword in ["column", "field", "字段", "列"]):
                    candidates["column_names"].append(col)
                elif any(keyword in col_lower for keyword in ["desc", "description", "说明", "描述"]):
                    candidates["descriptions"].append(col)
                elif any(keyword in col_lower for keyword in ["type", "类型", "数据类型"]):
                    candidates["data_types"].append(col)
            
            return candidates
            
        except Exception as e:
            logger.error(f"识别元数据候选字段失败: {e}")
            return {}
    
    def _analyze_json_structure(self, json_data: Any) -> Dict[str, Any]:
        """
        分析JSON结构
        
        Args:
            json_data: JSON数据
            
        Returns:
            Dict[str, Any]: 结构分析结果
        """
        try:
            if isinstance(json_data, dict):
                return {
                    "type": "object",
                    "keys": list(json_data.keys()),
                    "key_count": len(json_data.keys())
                }
            elif isinstance(json_data, list):
                return {
                    "type": "array",
                    "length": len(json_data),
                    "item_type": type(json_data[0]).__name__ if json_data else "unknown"
                }
            else:
                return {
                    "type": type(json_data).__name__,
                    "value": str(json_data)
                }
                
        except Exception as e:
            logger.error(f"分析JSON结构失败: {e}")
            return {"type": "unknown"}
    
    def _identify_json_metadata_candidates(self, json_data: Any) -> Dict[str, List[str]]:
        """
        识别JSON中的元数据候选字段
        
        Args:
            json_data: JSON数据
            
        Returns:
            Dict[str, List[str]]: 元数据候选字段
        """
        try:
            candidates = {
                "database_names": [],
                "table_names": [],
                "column_names": [],
                "descriptions": [],
                "data_types": []
            }
            
            # 递归分析JSON结构，识别可能的元数据字段
            def analyze_keys(data, path=""):
                if isinstance(data, dict):
                    for key, value in data.items():
                        key_lower = key.lower()
                        full_path = f"{path}.{key}" if path else key
                        
                        if any(keyword in key_lower for keyword in ["database", "db", "数据库"]):
                            candidates["database_names"].append(full_path)
                        elif any(keyword in key_lower for keyword in ["table", "表", "表名"]):
                            candidates["table_names"].append(full_path)
                        elif any(keyword in key_lower for keyword in ["column", "field", "字段", "列"]):
                            candidates["column_names"].append(full_path)
                        elif any(keyword in key_lower for keyword in ["desc", "description", "说明", "描述"]):
                            candidates["descriptions"].append(full_path)
                        elif any(keyword in key_lower for keyword in ["type", "类型", "数据类型"]):
                            candidates["data_types"].append(full_path)
                        
                        # 递归分析嵌套结构
                        if isinstance(value, (dict, list)):
                            analyze_keys(value, full_path)
                elif isinstance(data, list) and data:
                    # 分析数组的第一个元素
                    analyze_keys(data[0], path)
            
            analyze_keys(json_data)
            return candidates
            
        except Exception as e:
            logger.error(f"识别JSON元数据候选字段失败: {e}")
            return {}
    
    def get_supported_formats(self) -> Dict[str, List[str]]:
        """
        获取支持的文件格式
        
        Returns:
            Dict[str, List[str]]: 支持的格式列表
        """
        return self.supported_formats.copy()
    
    def is_supported_format(self, file_path: Path) -> bool:
        """
        检查文件格式是否支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持
        """
        file_extension = file_path.suffix.lower()
        for format_type, extensions in self.supported_formats.items():
            if file_extension in extensions:
                return True
        return False

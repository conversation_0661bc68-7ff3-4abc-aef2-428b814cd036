"""
元数据管理系统 - 企业级架构（完整重构版）

完全重构的模块化架构，集成所有元数据相关功能：

📁 核心模块：
├── crud/             # CRUD操作模块（按实体分组）
├── search/           # 搜索功能模块
├── templates/        # 文档模板管理模块
├── vector/           # 向量化管理模块
├── models/           # 数据模型（实体/请求/响应）
├── utils/            # 工具函数和辅助功能
├── docs/             # 文档和示例
└── tests/            # 测试文件

🚀 功能模块：
- 数据库管理（源数据库、指标数据库）
- 表管理（源表、指标表）
- 字段管理（源字段、指标字段）
- 码值集管理
- 数据主题管理
- 关联关系管理
- 文档模板管理（上传、下载、解析、入库）
- 向量化搜索

🎯 重构特性：
- 参照DD系统的企业级架构设计
- 完整的文档模板管理功能
- 统一的CRUD操作接口
- 智能搜索和向量化处理
- 模块化和可扩展设计
"""

from .crud import MetadataCrud
from .search import MetadataSearch

__all__ = [
    "MetadataCrud",
    "MetadataSearch"
]

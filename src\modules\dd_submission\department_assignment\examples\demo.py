"""
部门职责分配功能演示脚本

演示完整的部门职责分配流程，包括：
1. 数据准备
2. 单个分配演示
3. 批量分配演示
4. 结果验证
"""

import asyncio
import logging
import json
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from ..core.assignment_engine import DepartmentAssignmentLogic
from ..infrastructure.models import (
    DepartmentAssignmentRequest,
    BatchAssignmentRequest
)


class DepartmentAssignmentDemo:
    """部门职责分配演示类"""
    
    def __init__(self):
        """初始化演示类"""
        self.rdb_client = None
        self.vdb_client = None
        self.assignment_logic = None
    
    async def setup_demo_environment(self):
        """设置演示环境"""
        logger.info("设置演示环境...")
        
        # 这里应该初始化真实的数据库客户端
        # 为了演示，我们使用模拟客户端
        from .run_tests import MockRDBClient, MockVDBClient
        
        self.rdb_client = MockRDBClient("localhost", 3306, "hsbc_knowledge", "root", "")
        self.vdb_client = MockVDBClient()
        
        await self.rdb_client.connect()
        await self.vdb_client.connect()
        
        self.assignment_logic = DepartmentAssignmentLogic(self.rdb_client, self.vdb_client)
        
        logger.info("演示环境设置完成")
    
    async def demo_single_assignment(self):
        """演示单个部门分配"""
        logger.info("=" * 60)
        logger.info("演示1: 单个部门分配")
        logger.info("=" * 60)
        
        # 创建分配请求
        request = DepartmentAssignmentRequest(
            submission_id="DEMO_001",
            dr09="银行存款余额",
            dr17="银行存款账户的期末余额，包括活期存款和定期存款",
            set_value="SET_A",
            report_type="detail",
            submission_type="submission",
            dr01="ADS"
        )
        
        logger.info("输入参数:")
        logger.info(f"  submission_id: {request.submission_id}")
        logger.info(f"  dr09: {request.dr09}")
        logger.info(f"  dr17: {request.dr17}")
        logger.info(f"  set_value: {request.set_value}")
        logger.info(f"  report_type: {request.report_type}")
        logger.info(f"  submission_type: {request.submission_type}")
        logger.info(f"  dr01: {request.dr01}")
        
        # 执行分配
        logger.info("\n开始执行部门分配...")
        result = await self.assignment_logic.assign_department(request)
        
        # 显示结果
        logger.info("\n分配结果:")
        logger.info(f"  推荐部门: {result.recommended_department}")
        logger.info(f"  置信度: {result.confidence_level}")
        logger.info(f"  精确匹配: {result.exact_match_found}")
        logger.info(f"  搜索耗时: {result.search_time_ms:.2f}ms")
        
        if result.processing_notes:
            logger.info("  处理说明:")
            for note in result.processing_notes:
                logger.info(f"    - {note}")
        
        return result
    
    async def demo_batch_assignment(self):
        """演示批量部门分配"""
        logger.info("=" * 60)
        logger.info("演示2: 批量部门分配")
        logger.info("=" * 60)
        
        # 创建批量请求
        batch_request = BatchAssignmentRequest(
            report_code="g0107_beta_v1.0"
        )
        
        logger.info("批量分配参数:")
        logger.info(f"  report_code: {batch_request.report_code}")
        
        # 解析report_code
        dr07, version = batch_request.get_dr07_and_version()
        logger.info(f"  解析结果: dr07={dr07}, version={version}")
        
        # 执行批量分配
        logger.info("\n开始执行批量分配...")
        result = await self.assignment_logic.batch_assign_and_save(batch_request)
        
        # 显示结果
        logger.info("\n批量分配结果:")
        logger.info(f"  处理成功: {result['success']}")
        logger.info(f"  report_code: {result.get('report_code', 'N/A')}")
        
        if result.get('processing_summary'):
            summary = result['processing_summary']
            logger.info(f"  总记录数: {summary.get('total_records', 0)}")
            logger.info(f"  成功分配: {summary.get('successful_assignments', 0)}")
            logger.info(f"  失败分配: {summary.get('failed_assignments', 0)}")
            logger.info(f"  分配成功率: {summary.get('assignment_success_rate', 0):.2%}")
        
        if result.get('timing'):
            timing = result['timing']
            logger.info(f"  总耗时: {timing.get('total_time_ms', 0):.2f}ms")
        
        # 显示输出格式示例
        if result.get('batch_result') and result['batch_result'].assignment_items:
            logger.info("\n输出格式示例:")
            items = result['batch_result'].assignment_items[:2]  # 只显示前2个
            for i, item in enumerate(items):
                logger.info(f"  项目{i+1}:")
                logger.info(f"    entry_id: {item.entry_id}")
                logger.info(f"    entry_type: {item.entry_type}")
                logger.info(f"    DR22: {item.DR22}")
                logger.info(f"    BDR01: {item.BDR01}")
                logger.info(f"    BDR03: '{item.BDR03}'")
        
        return result
    
    async def demo_business_logic_explanation(self):
        """演示业务逻辑说明"""
        logger.info("=" * 60)
        logger.info("演示3: 业务逻辑说明")
        logger.info("=" * 60)
        
        logger.info("三层搜索逻辑:")
        logger.info("  第一层: 精确匹配")
        logger.info("    - 使用dr09和dr17进行完全精确匹配")
        logger.info("    - 不进行分词处理")
        logger.info("    - 不经过四层筛选")
        logger.info("    - 如果找到匹配，直接返回所有匹配结果")
        
        logger.info("\n  第二层: 混合搜索")
        logger.info("    - 对dr17进行分词处理")
        logger.info("    - 使用向量搜索和模糊搜索")
        logger.info("    - 需要经过四层业务筛选")
        logger.info("    - 支持相似度阈值过滤")
        
        logger.info("\n  第三层: TF-IDF部门推荐")
        logger.info("    - 基于dd_departments表进行语义匹配")
        logger.info("    - 使用改进的TF-IDF算法")
        logger.info("    - 作为兜底策略，保证返回推荐")
        
        logger.info("\n四层业务筛选:")
        logger.info("  第一层: 套系筛选")
        logger.info("    - survey为非标准套系")
        logger.info("    - 其他（SET_A/B/C、1104、DJZ、EAST、一表通）为标准套系")
        logger.info("    - 优先选择同类套系的结果")
        
        logger.info("\n  第二层: 报表类型筛选")
        logger.info("    - detail（明细）")
        logger.info("    - index（指标）")
        
        logger.info("\n  第三层: 提交类型筛选")
        logger.info("    - range（范围）")
        logger.info("    - submission（填报项）")
        
        logger.info("\n  第四层: 数据层筛选")
        logger.info("    - ADS、BDM、IDM、ADM、ODS、范围")
        
        logger.info("\n智能决策逻辑:")
        logger.info("  - 唯一匹配: 筛选后剩余唯一记录，直接推荐")
        logger.info("  - 多个匹配: 按评分选择最佳，检查一致性")
        logger.info("  - 精确匹配多结果: 保留所有结果用于多对象输出")
        logger.info("  - 回退匹配: 某层筛选后无结果，回退到上一层")
    
    async def demo_output_format(self):
        """演示输出格式"""
        logger.info("=" * 60)
        logger.info("演示4: 输出格式说明")
        logger.info("=" * 60)
        
        # 示例输出格式
        example_output = [
            {
                "entry_id": "TEST_001",
                "entry_type": "submission",
                "DR22": ["DEPT_FINANCE"],
                "BDR01": ["DEPT_FINANCE"],
                "BDR03": ""
            },
            {
                "entry_id": "TEST_002",
                "entry_type": "range", 
                "DR22": ["DEPT_CREDIT"],
                "BDR01": ["DEPT_CREDIT"],
                "BDR03": ""
            }
        ]
        
        logger.info("标准输出格式:")
        logger.info(json.dumps(example_output, indent=2, ensure_ascii=False))
        
        logger.info("\n字段说明:")
        logger.info("  entry_id: 搜索项的submission_id")
        logger.info("  entry_type: 搜索项的submission_type（不是固定的'ITEM'）")
        logger.info("  DR22: 部门ID数组")
        logger.info("  BDR01: 与DR22相同的部门ID数组")
        logger.info("  BDR03: 置空字段（空字符串）")
        
        logger.info("\n特殊情况:")
        logger.info("  - 精确匹配找到多个结果时，会生成多个输出对象")
        logger.info("  - 其他情况通常只有一个输出对象")
        logger.info("  - 如果没有找到匹配，DR22和BDR01为空数组")
    
    async def run_full_demo(self):
        """运行完整演示"""
        logger.info("开始部门职责分配功能演示")
        
        try:
            # 设置环境
            await self.setup_demo_environment()
            
            # 运行各个演示
            await self.demo_business_logic_explanation()
            await self.demo_single_assignment()
            await self.demo_batch_assignment()
            await self.demo_output_format()
            
            logger.info("=" * 60)
            logger.info("演示完成！")
            logger.info("=" * 60)
            
        except Exception as e:
            logger.error(f"演示失败: {e}")
            raise
        
        finally:
            # 清理资源
            if self.rdb_client:
                await self.rdb_client.close()
            if self.vdb_client:
                await self.vdb_client.close()


async def main():
    """主函数"""
    demo = DepartmentAssignmentDemo()
    await demo.run_full_demo()


if __name__ == "__main__":
    asyncio.run(main())

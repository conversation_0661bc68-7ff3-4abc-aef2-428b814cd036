import asyncio
import time
from contextlib import asynccontextmanager, contextmanager
from typing import Dict, Any, List, Optional, Sequence, AsyncGenerator, Union, Type
import logging

logger = logging.getLogger(__name__)

from sqlalchemy import create_engine, MetaData, Table, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy.pool import QueuePool

# 导入新的RDB抽象层
from .....base.rdb import (
    # 核心接口
    DatabaseClient, DatabaseType, TransactionIsolation,

    # 请求和响应模型
    QueryRequest, InsertRequest, UpdateRequest, DeleteRequest,
    QueryResponse, OperationResponse,

    # 异常
    ConnectionError as RDBConnectionError, TransactionError as RDBTransactionError,

    # 适配器
    DefaultResultAdapter, DefaultErrorAdapter
)

from .config import UniversalConnectionConfig
from .dialects import get_dialect, detect_dialect_from_url, DatabaseDialect
from .decorators import database_operation, auto_convert_batch_insert
from .exceptions import (
    UniversalSQLAlchemyError, ConnectionError, QueryError,
    TransactionError, wrap_database_error
)
from .pool_manager import PooledConnectionMixin, PoolManagerInterface


class UniversalSQLAlchemyClient(PooledConnectionMixin, DatabaseClient):
    """
    Universal SQLAlchemy client that works with all supported databases

    This client provides a unified interface for database operations while
    automatically adapting to the specific database dialect being used.

    Now implements the new RDB abstraction layer interface.
    """

    def __init__(self, config: UniversalConnectionConfig, pool_manager: Optional[PoolManagerInterface] = None):
        """
        Initialize the universal client with unified configuration

        Args:
            config: UniversalConnectionConfig instance
            pool_manager: 可选的池管理器实例，支持依赖注入
        """
        # 统一入口：只接受UniversalConnectionConfig
        if not isinstance(config, UniversalConnectionConfig):
            raise ValueError("config must be a UniversalConnectionConfig instance")

        # 先设置config，再调用父类初始化
        self.config = config

        # 调用父类初始化（包括PooledConnectionMixin），传入池管理器
        super().__init__(pool_manager=pool_manager)

        # 构建数据库URL
        self.database_url = self.config.build_database_url()

        # 智能检测数据库类型
        self.database_type = self._detect_database_type()
        self.dialect: DatabaseDialect = detect_dialect_from_url(self.database_url)

        # 创建适配器
        self.result_adapter = DefaultResultAdapter(self.database_type)
        self.error_adapter = DefaultErrorAdapter(self.database_type)

        # SQLAlchemy engines - 使用共享连接池
        self.sync_engine: Optional[Engine] = None
        self.async_engine: Optional[AsyncEngine] = None

        # Session factories
        self.sync_session_factory: Optional[sessionmaker] = None
        self.async_session_factory: Optional[async_sessionmaker] = None

        # Metadata for table reflection
        self.metadata = MetaData()

        # Table cache
        self._table_cache: Dict[str, Table] = {}

        # Connection state - 分别跟踪同步和异步连接状态
        self._sync_connected = False
        self._async_connected = False

        logger.info(f"Initialized Universal SQLAlchemy Client for {self.dialect.name}")

    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            # 不dispose共享引擎，只清理客户端状态
            # 共享引擎由全局管理器负责清理
            if hasattr(self, '_sync_connected'):
                self._sync_connected = False
            if hasattr(self, '_async_connected'):
                self._async_connected = False
        except Exception:
            # 忽略析构函数中的错误
            pass

    # ==================== RDB Interface Methods ====================

    def get_database_type(self) -> DatabaseType:
        """获取数据库类型"""
        return self.database_type

    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._sync_connected or self._async_connected

    # ==================== Connection Management ====================

    def _ensure_sync_engine(self) -> None:
        """确保同步引擎已初始化并自动连接"""
        if not self.sync_engine:
            try:
                self._create_sync_engine()
                if self.sync_engine is None:
                    raise ConnectionError(
                        f"Failed to create sync engine. "
                        f"Database: {self.database_type} | "
                        f"URL: {self.database_url} | "
                        f"Pool Manager: {getattr(self._pool_manager, 'instance_id', 'unknown')}"
                    )
                self._sync_connected = True
                logger.info("Auto-connected to database (sync)")
            except Exception as e:
                logger.error(f"Failed to ensure sync engine: {e}")
                raise ConnectionError(f"Sync engine initialization failed: {e}") from e
        elif not self._sync_connected:
            self._sync_connected = True
            logger.info("Auto-reconnected to database (sync)")

    async def _ensure_async_engine(self) -> None:
        """确保异步引擎已初始化并自动连接"""
        if not self.async_engine:
            try:
                await self._create_async_engine()
                if self.async_engine is None:
                    raise ConnectionError(
                        f"Failed to create async engine. "
                        f"Database: {self.database_type} | "
                        f"URL: {self.database_url} | "
                        f"Pool Manager: {getattr(self._pool_manager, 'instance_id', 'unknown')}"
                    )
                self._async_connected = True
                logger.info("Auto-connected to database (async)")
            except Exception as e:
                logger.error(f"Failed to ensure async engine: {e}")
                raise ConnectionError(f"Async engine initialization failed: {e}") from e
        elif not self._async_connected:
            self._async_connected = True
            logger.info("Auto-reconnected to database (async)")

    def connect(self) -> None:
        """Connect to the database (sync only)"""
        try:
            if not self.sync_engine:
                self._create_sync_engine()

            # Test sync connection
            self._test_sync_connection()

            self._sync_connected = True
            logger.info(f"Successfully connected to {self.dialect.name} database (sync)")

        except Exception as e:
            error = wrap_database_error(e, "sync connection")
            logger.error(f"Failed to connect to database (sync): {error}")
            raise error

    async def aconnect(self) -> None:
        """Connect to the database (async only)"""
        try:
            if not self.async_engine:
                await self._create_async_engine()

            # Test async connection
            await self._test_async_connection()

            self._async_connected = True
            logger.info(f"Successfully connected to {self.dialect.name} database (async)")

        except Exception as e:
            error = wrap_database_error(e, "async connection")
            logger.error(f"Failed to connect to database (async): {error}")
            raise error

    def disconnect(self) -> None:
        """Disconnect from the database (sync only)"""
        try:
            # 不dispose共享引擎，只清理客户端状态
            if self.sync_engine:
                self.sync_engine = None
                self.sync_session_factory = None

            self._table_cache.clear()
            self._sync_connected = False

            logger.info("Disconnected from database (sync)")

        except Exception as e:
            logger.error(f"Error during sync disconnect: {e}")
            # 在清理阶段，记录错误但不抛出异常，避免影响整体清理流程
            # 在正常使用阶段，应该抛出异常
            import inspect
            frame = inspect.currentframe()
            try:
                # 检查调用栈，如果是在清理过程中，不抛出异常
                is_cleanup = any('cleanup' in str(f.filename).lower() or 'cleanup' in str(f.function).lower()
                               for f in inspect.getouterframes(frame)[1:5])
                if not is_cleanup:
                    raise wrap_database_error(e, "sync disconnection")
            finally:
                del frame

    async def adisconnect(self) -> None:
        """Disconnect from the database (async only)"""
        try:
            # 不dispose共享引擎，只清理客户端状态
            if self.async_engine:
                self.async_engine = None
                self.async_session_factory = None

            self._table_cache.clear()
            self._async_connected = False

            logger.info("Disconnected from database (async)")

        except Exception as e:
            logger.error(f"Error during async disconnect: {e}")
            # 在清理阶段，记录错误但不抛出异常，避免影响整体清理流程
            import inspect
            frame = inspect.currentframe()
            try:
                # 检查调用栈，如果是在清理过程中，不抛出异常
                is_cleanup = any('cleanup' in str(f.filename).lower() or 'cleanup' in str(f.function).lower()
                               for f in inspect.getouterframes(frame)[1:5])
                if not is_cleanup:
                    raise wrap_database_error(e, "async disconnection")
            finally:
                del frame
    
    def _create_sync_engine(self) -> None:
        """Create synchronous SQLAlchemy engine using shared pool"""
        try:
            # 使用共享连接池
            sync_engine, _ = self._get_shared_engines()

            if sync_engine is None:
                raise ConnectionError(
                    f"Pool manager returned None for sync engine. "
                    f"Config: {self.config.dialect}://{self.config.host}:{self.config.port}/{self.config.database}"
                )

            self.sync_engine = sync_engine
            self.sync_session_factory = sessionmaker(
                bind=self.sync_engine,
                expire_on_commit=False
            )

            logger.debug(f"Created sync engine successfully: {type(self.sync_engine).__name__}")

        except Exception as e:
            logger.error(f"Failed to create sync engine: {e}")
            self.sync_engine = None
            self.sync_session_factory = None
            raise
    
    async def _create_async_engine(self) -> None:
        """Create asynchronous SQLAlchemy engine using shared pool"""
        try:
            # 使用共享连接池
            _, async_engine = self._get_shared_engines()

            if async_engine is None:
                raise ConnectionError(
                    f"Pool manager returned None for async engine. "
                    f"Config: {self.config.dialect}://{self.config.host}:{self.config.port}/{self.config.database}"
                )

            self.async_engine = async_engine
            self.async_session_factory = async_sessionmaker(
                bind=self.async_engine,
                expire_on_commit=False
            )

            logger.debug(f"Created async engine successfully: {type(self.async_engine).__name__}")

        except Exception as e:
            logger.error(f"Failed to create async engine: {e}")
            self.async_engine = None
            self.async_session_factory = None
            raise
    
    def _build_async_url(self) -> str:
        """Build async database URL"""
        if not self.dialect.async_driver:
            raise ConnectionError(f"Async driver not available for {self.dialect.name}")
        
        # Replace driver in URL with async driver
        url = self.database_url
        if '+' in url:
            scheme, rest = url.split('://', 1)
            dialect_name = scheme.split('+')[0]
            async_scheme = f"{dialect_name}+{self.dialect.async_driver}"
            return f"{async_scheme}://{rest}"
        else:
            # Add async driver
            scheme, rest = url.split('://', 1)
            async_scheme = f"{scheme}+{self.dialect.async_driver}"
            return f"{async_scheme}://{rest}"
    
    def _test_sync_connection(self) -> None:
        """Test sync database connection"""
        if not self.sync_engine:
            raise ConnectionError("Sync engine not available")

        with self.sync_engine.begin() as conn:
            conn.execute(text("SELECT 1"))

    async def _test_async_connection(self) -> None:
        """Test async database connection"""
        if not self.async_engine:
            raise ConnectionError("Async engine not available")

        async with self.async_engine.begin() as conn:
            await conn.execute(text("SELECT 1"))

    # ==================== RDB Interface Query Methods ====================

    @database_operation('query')
    def query(self, request: Union[QueryRequest, Dict[str, Any]]) -> QueryResponse:
        """
        执行查询（同步）- RDB接口方法

        Args:
            request: 查询请求，可以是QueryRequest实体或字典

        Returns:
            QueryResponse: 查询结果

        Examples:
            # 使用字典
            result = client.query({
                "table": "users",
                "columns": ["id", "name"],
                "filters": {"status": "active"},
                "limit": 10
            })

            # 使用实体
            request = QueryRequest(table="users", columns=["id", "name"])
            result = client.query(request)
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()
        self._ensure_sync_engine()
        try:
            # 构建SQL查询
            sql, params = self._build_select_sql(request)



            # 执行查询
            with self.sync_engine.begin() as conn:
                result = conn.execute(text(sql), params)
                rows = [dict(row._mapping) for row in result]

            # 创建响应
            execution_time = time.time() - start_time
            return QueryResponse(
                data=rows,
                total_count=len(rows),
                execution_time=execution_time,
                query_sql=sql,
                query_parameters=params,
                database_type=self.database_type
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "query",
                "table": request.table,
                "request": str(request)
            })

    @database_operation('query')
    async def aquery(self, request: Union[QueryRequest, Dict[str, Any]]) -> QueryResponse:
        """
        执行查询（异步）- RDB接口方法

        Args:
            request: 查询请求，可以是QueryRequest实体或字典

        Returns:
            QueryResponse: 查询结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()
        await self._ensure_async_engine()
        try:
            # 构建SQL查询
            sql, params = self._build_select_sql(request)

            # 安全检查：确保引擎不为None
            if self.async_engine is None:
                raise ConnectionError(
                    f"Async engine is None. Database: {self.database_type} | "
                    f"Connected: {self._async_connected}"
                )

            # 执行查询
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text(sql), params)
                rows = [dict(row._mapping) for row in result]

            # 创建响应
            execution_time = time.time() - start_time
            return QueryResponse(
                data=rows,
                total_count=len(rows),
                execution_time=execution_time,
                query_sql=sql,
                query_parameters=params,
                database_type=self.database_type
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "aquery",
                "table": request.table,
                "request": str(request)
            })

    # ==================== RDB Interface CRUD Methods ====================

    @database_operation('insert')
    def insert(self, request: Union[InsertRequest, Dict[str, Any]]) -> OperationResponse:
        """
        插入数据（同步）- RDB接口方法

        Args:
            request: 插入请求，可以是InsertRequest实体或字典

        Returns:
            OperationResponse: 操作结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()
        self._ensure_sync_engine()
        try:
            # 构建INSERT SQL
            sql, params = self._build_insert_sql(request)

            # 执行插入
            with self.sync_engine.begin() as conn:
                result = conn.execute(text(sql), params)
                affected_rows = result.rowcount

            # 创建响应
            execution_time = time.time() - start_time
            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=params
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "insert",
                "table": request.table,
                "data": str(request.data)
            })

    @database_operation('insert')
    async def ainsert(self, request: Union[InsertRequest, Dict[str, Any]]) -> OperationResponse:
        """
        插入数据（异步）- RDB接口方法

        Args:
            request: 插入请求，可以是InsertRequest实体或字典

        Returns:
            OperationResponse: 操作结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()
        await self._ensure_async_engine()
        try:
            # 构建INSERT SQL
            sql, params = self._build_insert_sql(request)

            # 执行插入
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text(sql), params)
                affected_rows = result.rowcount

            # 创建响应
            execution_time = time.time() - start_time
            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=params
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "ainsert",
                "table": request.table,
                "data": str(request.data)
            })

    @database_operation('update')
    def update(self, request: Union[UpdateRequest, Dict[str, Any]]) -> OperationResponse:
        """
        更新数据（同步）- RDB接口方法

        Args:
            request: 更新请求，可以是UpdateRequest实体或字典

        Returns:
            OperationResponse: 操作结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()
        self._ensure_sync_engine()
        try:
            # 构建UPDATE SQL
            sql, params = self._build_update_sql(request)

            # 执行更新
            with self.sync_engine.begin() as conn:
                result = conn.execute(text(sql), params)
                affected_rows = result.rowcount

            # 创建响应
            execution_time = time.time() - start_time
            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=params
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "update",
                "table": request.table,
                "data": str(request.data)
            })

    @database_operation('update')
    async def aupdate(self, request: Union[UpdateRequest, Dict[str, Any]]) -> OperationResponse:
        """
        更新数据（异步）- RDB接口方法

        Args:
            request: 更新请求，可以是UpdateRequest实体或字典

        Returns:
            OperationResponse: 操作结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()
        await self._ensure_async_engine()
        try:
            # 构建UPDATE SQL
            sql, params = self._build_update_sql(request)

            # 执行更新
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text(sql), params)
                affected_rows = result.rowcount

            # 创建响应
            execution_time = time.time() - start_time
            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=params
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "aupdate",
                "table": request.table,
                "data": str(request.data)
            })

    @database_operation('delete')
    def delete(self, request: Union[DeleteRequest, Dict[str, Any]]) -> OperationResponse:
        """
        删除数据（同步）- RDB接口方法

        Args:
            request: 删除请求，可以是DeleteRequest实体或字典

        Returns:
            OperationResponse: 操作结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()
        self._ensure_sync_engine()
        try:
            # 构建DELETE SQL
            sql, params = self._build_delete_sql(request)

            # 执行删除
            with self.sync_engine.begin() as conn:
                result = conn.execute(text(sql), params)
                affected_rows = result.rowcount

            # 创建响应
            execution_time = time.time() - start_time
            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=params
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "delete",
                "table": request.table
            })

    @database_operation('delete')
    async def adelete(self, request: Union[DeleteRequest, Dict[str, Any]]) -> OperationResponse:
        """
        删除数据（异步）- RDB接口方法

        Args:
            request: 删除请求，可以是DeleteRequest实体或字典

        Returns:
            OperationResponse: 操作结果
        """
        # 注意：装饰器已处理连接和转换
        start_time = time.time()
        await self._ensure_async_engine()
        try:
            # 构建DELETE SQL
            sql, params = self._build_delete_sql(request)

            # 执行删除
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text(sql), params)
                affected_rows = result.rowcount

            # 创建响应
            execution_time = time.time() - start_time
            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=params
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "adelete",
                "table": request.table
            })

    # ==================== RDB Interface Raw SQL Methods ====================

    def execute(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> OperationResponse:
        """执行原生SQL（同步）- RDB接口方法"""
        # 按需初始化同步引擎
        self._ensure_sync_engine()

        start_time = time.time()

        try:
            with self.sync_engine.begin() as conn:
                result = conn.execute(text(sql), parameters or {})
                affected_rows = result.rowcount

            execution_time = time.time() - start_time
            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=parameters
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "execute",
                "sql": sql,
                "parameters": parameters
            })

    async def aexecute(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> OperationResponse:
        """执行原生SQL（异步）- RDB接口方法"""
        await self._ensure_async_engine()

        start_time = time.time()

        try:
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text(sql), parameters or {})
                affected_rows = result.rowcount

            execution_time = time.time() - start_time
            return OperationResponse(
                success=True,
                affected_rows=affected_rows,
                execution_time=execution_time,
                operation_sql=sql,
                operation_parameters=parameters
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "aexecute",
                "sql": sql,
                "parameters": parameters
            })

    def fetch_all(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> QueryResponse:
        """获取所有结果（同步）- RDB接口方法"""
        # 按需初始化同步引擎
        self._ensure_sync_engine()

        start_time = time.time()

        try:
            with self.sync_engine.connect() as conn:
                result = conn.execute(text(sql), parameters or {})
                rows = [dict(row._mapping) for row in result]

            execution_time = time.time() - start_time
            return QueryResponse(
                data=rows,
                total_count=len(rows),
                execution_time=execution_time,
                query_sql=sql,
                query_parameters=parameters,
                database_type=self.database_type
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "fetch_all",
                "sql": sql,
                "parameters": parameters
            })

    async def afetch_all(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> QueryResponse:
        """获取所有结果（异步）- RDB接口方法"""
        await self._ensure_async_engine()

        start_time = time.time()

        try:
            async with self.async_engine.connect() as conn:
                result = await conn.execute(text(sql), parameters or {})
                rows = [dict(row._mapping) for row in result]

            execution_time = time.time() - start_time
            return QueryResponse(
                data=rows,
                total_count=len(rows),
                execution_time=execution_time,
                query_sql=sql,
                query_parameters=parameters,
                database_type=self.database_type
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "afetch_all",
                "sql": sql,
                "parameters": parameters
            })

    def fetch_one(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """获取单个结果（同步）- RDB接口方法"""
        response = self.fetch_all(sql, parameters)
        return response.data[0] if response.data else None

    async def afetch_one(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """获取单个结果（异步）- RDB接口方法"""
        response = await self.afetch_all(sql, parameters)
        return response.data[0] if response.data else None

    @contextmanager
    def transaction(self, isolation_level: Optional[TransactionIsolation] = None):
        """事务上下文管理器（同步）- RDB接口方法"""
        self._ensure_sync_engine()

        conn = self.sync_engine.connect()
        trans = conn.begin()

        try:
            yield conn
            trans.commit()
        except Exception as e:
            trans.rollback()
            raise RDBTransactionError(
                f"Transaction failed: {e}",
                original_error=e,
                database_type=self.database_type
            )
        finally:
            conn.close()

    @asynccontextmanager
    async def atransaction(self, isolation_level: Optional[TransactionIsolation] = None):
        """事务上下文管理器（异步）- RDB接口方法"""
        await self._ensure_async_engine()
        
        conn = await self.async_engine.connect()
        trans = await conn.begin()

        try:
            yield conn
            await trans.commit()
        except Exception as e:
            await trans.rollback()
            raise RDBTransactionError(
                f"Transaction failed: {e}",
                original_error=e,
                database_type=self.database_type
            )
        finally:
            await conn.close()

    def health_check(self) -> Dict[str, Any]:
        """健康检查 - RDB接口方法"""
        try:
            if not self.is_connected():
                return {
                    "status": "disconnected",
                    "database_type": self.database_type,
                    "error": "Not connected to database"
                }

            # 执行简单查询测试连接
            start_time = time.time()
            if self.sync_engine:
                with self.sync_engine.connect() as conn:
                    result = conn.execute(text("SELECT 1"))
                    result.fetchone()

            execution_time = time.time() - start_time

            return {
                "status": "healthy",
                "database_type": self.database_type,
                "response_time": execution_time,
                "connection_pool": {
                    "size": self.sync_engine.pool.size() if self.sync_engine else 0,
                    "checked_in": self.sync_engine.pool.checkedin() if self.sync_engine else 0,
                    "checked_out": self.sync_engine.pool.checkedout() if self.sync_engine else 0,
                }
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "database_type": self.database_type,
                "error": str(e)
            }



    # ==================== Configuration and Helper Methods ====================

    def _detect_database_type(self) -> DatabaseType:
        """智能检测数据库类型 - 改进错误处理，避免错误默认值"""
        # 1. 优先使用显式指定的dialect
        if self.config.dialect:
            dialect = self.config.dialect.lower()
            if dialect in ['mysql', 'mariadb']:
                return DatabaseType.MYSQL
            elif dialect in ['postgresql', 'postgres']:
                return DatabaseType.POSTGRESQL
            elif dialect == 'sqlite':
                return DatabaseType.SQLITE
            elif dialect == 'oracle':
                return DatabaseType.ORACLE
            elif dialect in ['mssql', 'sqlserver']:
                return DatabaseType.SQLSERVER
            else:
                raise ValueError(f"Unsupported dialect: {dialect}. Supported: mysql, postgresql, sqlite, oracle, mssql")
        
        # 2. 从database_url解析
        if self.database_url:
            url = self.database_url.lower()
            if 'mysql' in url or 'mariadb' in url:
                return DatabaseType.MYSQL
            elif 'postgresql' in url or 'postgres' in url:
                return DatabaseType.POSTGRESQL
            elif 'sqlite' in url:
                return DatabaseType.SQLITE
            elif 'oracle' in url:
                return DatabaseType.ORACLE
            elif 'mssql' in url or 'sqlserver' in url:
                return DatabaseType.SQLSERVER
        
        # 3. 无法确定数据库类型时抛出错误，而不是使用默认值
        raise ValueError(
            f"Cannot determine database type from config. "
            f"Please specify dialect explicitly in config or ensure database_url contains recognizable database type. "
            f"Config dialect: {getattr(self.config, 'dialect', 'None')}, "
            f"Database URL: {self.database_url}"
        )

    def _build_select_sql(self, request: QueryRequest) -> tuple[str, Dict[str, Any]]:
        """构建SELECT SQL语句"""
        # 构建列列表
        if request.columns:
            columns = ", ".join([self.dialect.quote_identifier(col) for col in request.columns])
        else:
            columns = "*"

        # 构建基础查询
        sql = f"SELECT {columns} FROM {self.dialect.quote_identifier(request.table)}"
        params = {}

        # 构建WHERE子句
        if request.filters:
            # 处理单个过滤器或过滤器组
            if hasattr(request.filters, 'filters'):
                # 这是一个QueryFilterGroup
                where_clause, where_params = self._build_where_clause(request.filters)
            else:
                # 这是一个单个QueryFilter，包装成QueryFilterGroup
                from .....base.rdb import QueryFilterGroup, LogicalOperator
                filter_group = QueryFilterGroup(
                    operator=LogicalOperator.AND,
                    filters=[request.filters]
                )
                where_clause, where_params = self._build_where_clause(filter_group)

            sql += f" WHERE {where_clause}"
            params.update(where_params)

        # 构建ORDER BY子句
        if request.sorts:
            order_clauses = []
            for sort in request.sorts:
                direction = "DESC" if sort.order.value == "desc" else "ASC"
                order_clauses.append(f"{self.dialect.quote_identifier(sort.field)} {direction}")
            sql += f" ORDER BY {', '.join(order_clauses)}"

        # 构建LIMIT和OFFSET
        if request.limit:
            sql += f" LIMIT {request.limit}"

        if request.offset:
            sql += f" OFFSET {request.offset}"

        return sql, params

    def _build_insert_sql(self, request: InsertRequest) -> tuple[str, Dict[str, Any]]:
        """构建INSERT SQL语句 - 修复批次边界处理问题"""
        if isinstance(request.data, list):
            # 批量插入
            if not request.data:
                raise ValueError("Insert data cannot be empty")

            # 数据验证和列标准化
            first_record = request.data[0]
            if not isinstance(first_record, dict):
                raise ValueError("Each record must be a dictionary")

            # 获取标准列顺序（按字母排序确保一致性）
            columns = sorted(first_record.keys())
            if not columns:
                raise ValueError("Records cannot be empty")

            # 验证所有记录都有相同的列
            for i, record in enumerate(request.data):
                if not isinstance(record, dict):
                    raise ValueError(f"Record {i} is not a dictionary")

                record_columns = set(record.keys())
                expected_columns = set(columns)

                if record_columns != expected_columns:
                    missing = expected_columns - record_columns
                    extra = record_columns - expected_columns
                    error_msg = f"Record {i} has inconsistent columns."
                    if missing:
                        error_msg += f" Missing: {sorted(missing)}."
                    if extra:
                        error_msg += f" Extra: {sorted(extra)}."
                    raise ValueError(error_msg)

            quoted_columns = [self.dialect.quote_identifier(col) for col in columns]

            # 构建VALUES子句 - 使用更安全的参数命名
            values_clauses = []
            params = {}

            for i, record in enumerate(request.data):
                value_placeholders = []
                for j, col in enumerate(columns):
                    # 使用行号和列号的组合确保参数名唯一
                    param_name = f"p_{i}_{j}"
                    value_placeholders.append(f":{param_name}")

                    # 获取值，处理None和缺失的情况
                    value = record.get(col)
                    params[param_name] = value

                values_clauses.append(f"({', '.join(value_placeholders)})")

            sql = f"INSERT INTO {self.dialect.quote_identifier(request.table)} ({', '.join(quoted_columns)}) VALUES {', '.join(values_clauses)}"

            # 调试信息（可选）
            if len(request.data) <= 20:  # 只对小批次输出调试信息
                import logging
                logger = logging.getLogger(__name__)
                logger.debug(f"Built INSERT SQL for {len(request.data)} records, {len(params)} parameters")

        else:
            # 单条插入
            if not isinstance(request.data, dict):
                raise ValueError("Single insert data must be a dictionary")

            columns = sorted(request.data.keys())  # 保持一致的列顺序
            if not columns:
                raise ValueError("Insert data cannot be empty")

            quoted_columns = [self.dialect.quote_identifier(col) for col in columns]
            placeholders = [f":{col}" for col in columns]

            sql = f"INSERT INTO {self.dialect.quote_identifier(request.table)} ({', '.join(quoted_columns)}) VALUES ({', '.join(placeholders)})"
            params = {col: request.data[col] for col in columns}  # 确保参数顺序一致

        return sql, params

    def _build_update_sql(self, request: UpdateRequest) -> tuple[str, Dict[str, Any]]:
        """构建UPDATE SQL语句"""
        # 构建SET子句
        set_clauses = []
        params = {}

        for col, value in request.data.items():
            set_clauses.append(f"{self.dialect.quote_identifier(col)} = :{col}")
            params[col] = value

        sql = f"UPDATE {self.dialect.quote_identifier(request.table)} SET {', '.join(set_clauses)}"

        # 构建WHERE子句
        if request.filters:
            # 处理单个过滤器或过滤器组
            if hasattr(request.filters, 'filters'):
                # 这是一个QueryFilterGroup
                where_clause, where_params = self._build_where_clause(request.filters)
            else:
                # 这是一个单个QueryFilter，包装成QueryFilterGroup
                from .....base.rdb import QueryFilterGroup, LogicalOperator
                filter_group = QueryFilterGroup(
                    operator=LogicalOperator.AND,
                    filters=[request.filters]
                )
                where_clause, where_params = self._build_where_clause(filter_group)

            sql += f" WHERE {where_clause}"
            # 避免参数名冲突
            for key, value in where_params.items():
                new_key = f"where_{key}"
                params[new_key] = value
                sql = sql.replace(f":{key}", f":{new_key}")

        return sql, params

    def _build_delete_sql(self, request: DeleteRequest) -> tuple[str, Dict[str, Any]]:
        """构建DELETE SQL语句"""
        sql = f"DELETE FROM {self.dialect.quote_identifier(request.table)}"
        params = {}

        # 构建WHERE子句
        if request.filters:
            # 处理单个过滤器或过滤器组
            if hasattr(request.filters, 'filters'):
                # 这是一个QueryFilterGroup
                where_clause, where_params = self._build_where_clause(request.filters)
            else:
                # 这是一个单个QueryFilter，包装成QueryFilterGroup
                from .....base.rdb import QueryFilterGroup, LogicalOperator
                filter_group = QueryFilterGroup(
                    operator=LogicalOperator.AND,
                    filters=[request.filters]
                )
                where_clause, where_params = self._build_where_clause(filter_group)

            sql += f" WHERE {where_clause}"
            params.update(where_params)

        return sql, params

    def _build_where_clause(self, filter_group) -> tuple[str, Dict[str, Any]]:
        """构建WHERE子句"""
        from .....base.rdb import QueryFilter, QueryFilterGroup, ComparisonOperator, LogicalOperator

        conditions = []
        params = {}
        param_counter = 0

        for filter_item in filter_group.filters:
            if isinstance(filter_item, QueryFilter):
                condition, filter_params = self._build_filter_condition(filter_item, param_counter)
                conditions.append(condition)
                params.update(filter_params)
                param_counter += len(filter_params)
            elif isinstance(filter_item, QueryFilterGroup):
                nested_condition, nested_params = self._build_where_clause(filter_item)
                conditions.append(f"({nested_condition})")
                params.update(nested_params)

        if filter_group.operator == LogicalOperator.AND:
            return " AND ".join(conditions), params
        else:
            return " OR ".join(conditions), params

    def _build_filter_condition(self, filter_obj, param_counter: int) -> tuple[str, Dict[str, Any]]:
        """构建单个过滤条件"""
        from .....base.rdb import ComparisonOperator

        column = self.dialect.quote_identifier(filter_obj.field)
        params = {}

        if filter_obj.operator == ComparisonOperator.EQ:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} = :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.NE:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} != :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.GT:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} > :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.GTE:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} >= :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.LT:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} < :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.LTE:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} <= :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.IN:
            if not isinstance(filter_obj.value, (list, tuple)):
                raise ValueError("IN operator requires a list or tuple value")

            placeholders = []
            for i, val in enumerate(filter_obj.value):
                param_name = f"param_{param_counter}_{i}"
                params[param_name] = val
                placeholders.append(f":{param_name}")

            return f"{column} IN ({', '.join(placeholders)})", params
        elif filter_obj.operator == ComparisonOperator.NOT_IN:
            if not isinstance(filter_obj.value, (list, tuple)):
                raise ValueError("NOT IN operator requires a list or tuple value")

            placeholders = []
            for i, val in enumerate(filter_obj.value):
                param_name = f"param_{param_counter}_{i}"
                params[param_name] = val
                placeholders.append(f":{param_name}")

            return f"{column} NOT IN ({', '.join(placeholders)})", params
        elif filter_obj.operator == ComparisonOperator.LIKE:
            param_name = f"param_{param_counter}"
            params[param_name] = filter_obj.value
            return f"{column} LIKE :{param_name}", params
        elif filter_obj.operator == ComparisonOperator.BETWEEN:
            if not isinstance(filter_obj.value, (list, tuple)) or len(filter_obj.value) != 2:
                raise ValueError("BETWEEN operator requires exactly 2 values")

            param_name1 = f"param_{param_counter}_start"
            param_name2 = f"param_{param_counter}_end"
            params[param_name1] = filter_obj.value[0]
            params[param_name2] = filter_obj.value[1]
            return f"{column} BETWEEN :{param_name1} AND :{param_name2}", params
        elif filter_obj.operator == ComparisonOperator.IS_NULL:
            return f"{column} IS NULL", params
        elif filter_obj.operator == ComparisonOperator.IS_NOT_NULL:
            return f"{column} IS NOT NULL", params
        else:
            raise ValueError(f"Unsupported operator: {filter_obj.operator}")

    def _build_batch_update_sql(self, table: str, updates: List[Dict[str, Any]]) -> List[tuple[str, Dict[str, Any]]]:
        """
        构建批量更新SQL语句 - 使用多个UPDATE语句（更简单实用）

        Args:
            table: 表名
            updates: 更新数据列表，格式: [{"data": {...}, "filters": {...}}, ...]

        Returns:
            List[tuple]: [(SQL语句, 参数字典), ...] 每个更新一个SQL语句

        Note:
            CASE WHEN方式虽然可以用一个SQL处理多个更新，但SQL会变得非常复杂
            使用多个UPDATE语句更简单、更易理解、更易调试
        """
        if not updates:
            raise ValueError("Updates cannot be empty")

        sql_list = []

        for i, update in enumerate(updates):
            if "data" not in update or not update["data"]:
                continue

            # 构建单个UPDATE语句
            data = update["data"]
            filters = update.get("filters", {})

            # 构建SET子句
            set_clauses = []
            params = {}

            for col, value in data.items():
                param_name = f"set_{col}_{i}"
                set_clauses.append(f"{self.dialect.quote_identifier(col)} = :{param_name}")
                params[param_name] = value

            sql = f"UPDATE {self.dialect.quote_identifier(table)} SET {', '.join(set_clauses)}"

            # 构建WHERE子句
            if filters:
                where_clauses = []
                for filter_col, filter_value in filters.items():
                    param_name = f"where_{filter_col}_{i}"
                    where_clauses.append(f"{self.dialect.quote_identifier(filter_col)} = :{param_name}")
                    params[param_name] = filter_value

                sql += f" WHERE {' AND '.join(where_clauses)}"

            sql_list.append((sql, params))

        return sql_list

    def _build_batch_delete_sql(self, table: str, ids: List[Union[int, str]], id_column: str) -> tuple[str, Dict[str, Any]]:
        """
        构建批量删除SQL语句 - 使用IN子句

        Args:
            table: 表名
            ids: ID列表
            id_column: ID列名

        Returns:
            tuple: (SQL语句, 参数字典)
        """
        if not ids:
            raise ValueError("IDs list cannot be empty")

        # 构建IN子句的参数
        placeholders = []
        params = {}

        for i, id_value in enumerate(ids):
            param_name = f"id_{i}"
            placeholders.append(f":{param_name}")
            params[param_name] = id_value

        # 构建DELETE语句
        sql = f"DELETE FROM {self.dialect.quote_identifier(table)} WHERE {self.dialect.quote_identifier(id_column)} IN ({', '.join(placeholders)})"

        return sql, params

    def _build_delete_request(self, table: str, condition: Union[Dict[str, Any], 'QueryFilterGroup']) -> 'DeleteRequest':
        """构建DeleteRequest对象"""
        from .....base.rdb import DeleteRequest, QueryFilterGroup

        if isinstance(condition, QueryFilterGroup):
            # 直接使用QueryFilterGroup
            filters = condition
        elif isinstance(condition, dict):
            # 转换字典条件为QueryFilterGroup
            filters = self._convert_dict_to_filter_group(condition)
        else:
            raise ValueError(f"Unsupported condition type: {type(condition)}")

        return DeleteRequest(table=table, filters=filters)

    def _convert_dict_to_filter_group(self, conditions: Dict[str, Any]) -> 'QueryFilterGroup':
        """将字典格式条件转换为QueryFilterGroup"""
        from .....base.rdb import QueryFilterGroup, QueryFilter, ComparisonOperator, LogicalOperator

        filters = []

        for field, value in conditions.items():
            if field.startswith("$"):
                # 处理逻辑操作符
                if field == "$and":
                    nested_filters = [self._convert_dict_to_filter_group(cond) for cond in value]
                    filters.extend(nested_filters.filters if hasattr(nested_filters, 'filters') else [nested_filters])
                elif field == "$or":
                    or_group = QueryFilterGroup(
                        operator=LogicalOperator.OR,
                        filters=[self._convert_dict_to_filter_group(cond) for cond in value]
                    )
                    filters.append(or_group)
            else:
                # 处理字段条件
                filter_obj = self._convert_field_condition(field, value)
                filters.append(filter_obj)

        return QueryFilterGroup(
            operator=LogicalOperator.AND,
            filters=filters
        )

    def _convert_field_condition(self, field: str, value: Any) -> 'QueryFilter':
        """转换单个字段条件"""
        from .....base.rdb import QueryFilter, ComparisonOperator

        # 操作符映射
        OPERATOR_MAP = {
            "$eq": ComparisonOperator.EQ,
            "$ne": ComparisonOperator.NE,
            "$gt": ComparisonOperator.GT,
            "$gte": ComparisonOperator.GTE,
            "$lt": ComparisonOperator.LT,
            "$lte": ComparisonOperator.LTE,
            "$in": ComparisonOperator.IN,
            "$nin": ComparisonOperator.NOT_IN,
            "$like": ComparisonOperator.LIKE,
            "$between": ComparisonOperator.BETWEEN,
            "$null": ComparisonOperator.IS_NULL,
            "$not_null": ComparisonOperator.IS_NOT_NULL,
        }

        if isinstance(value, dict):
            # 复杂条件：不再自动转换为BETWEEN，保留原始语义
            # 移除自动BETWEEN转换逻辑，避免开区间/闭区间语义混淆
            # 用户如需BETWEEN，应明确使用 {"$between": [start, end]}
            
            if len(value) == 1:
                # 单个操作符
                op_key, op_value = next(iter(value.items()))
                operator = OPERATOR_MAP.get(op_key, ComparisonOperator.EQ)

                # 特殊处理空值检查
                if op_key in ["$null", "$not_null"]:
                    op_value = None

                return QueryFilter(field=field, operator=operator, value=op_value)
            else:
                raise ValueError(f"Complex field conditions not supported: {field}: {value}. Use explicit QueryFilterGroup for multiple conditions.")
        else:
            # 简单等值条件
            return QueryFilter(field=field, operator=ComparisonOperator.EQ, value=value)

    def _build_update_request(self, table: str, update: Dict[str, Any]) -> 'UpdateRequest':
        """构建UpdateRequest对象"""
        from .....base.rdb import UpdateRequest, QueryFilterGroup

        if "data" not in update:
            raise ValueError("Update must contain 'data' field")

        data = update["data"]

        # 支持两种格式：filters（传统）和 where（新格式）
        if "where" in update:
            # 新格式
            where_conditions = update["where"]
            if isinstance(where_conditions, QueryFilterGroup):
                filters = where_conditions
            elif isinstance(where_conditions, dict):
                filters = self._convert_dict_to_filter_group(where_conditions)
            else:
                raise ValueError(f"Unsupported where conditions type: {type(where_conditions)}")
        elif "filters" in update:
            # 传统格式（向后兼容）
            filter_conditions = update["filters"]
            if isinstance(filter_conditions, QueryFilterGroup):
                filters = filter_conditions
            elif isinstance(filter_conditions, dict):
                # 检查是否为简单字典格式
                if self._is_simple_dict_filters(filter_conditions):
                    # 简单字典格式，转换为简单的AND条件组
                    filters = self._convert_simple_dict_to_filter_group(filter_conditions)
                else:
                    # 复杂字典格式，使用完整转换
                    filters = self._convert_dict_to_filter_group(filter_conditions)
            else:
                raise ValueError(f"Unsupported filters type: {type(filter_conditions)}")
        else:
            raise ValueError("Update must contain either 'where' or 'filters' field")

        return UpdateRequest(table=table, data=data, filters=filters)

    def _is_simple_dict_filters(self, filters: dict) -> bool:
        """检查是否为简单字典格式（用于向后兼容判断）"""
        return all(
            not isinstance(v, dict) or not any(k.startswith("$") for k in v.keys())
            for v in filters.values()
        )

    def _convert_simple_dict_to_filter_group(self, filters: dict) -> 'QueryFilterGroup':
        """转换简单字典格式为QueryFilterGroup（向后兼容）"""
        from .....base.rdb import QueryFilterGroup, QueryFilter, ComparisonOperator, LogicalOperator

        filter_list = []
        for field, value in filters.items():
            filter_list.append(QueryFilter(field=field, operator=ComparisonOperator.EQ, value=value))

        return QueryFilterGroup(
            operator=LogicalOperator.AND,
            filters=filter_list
        )



    # ==================== 批量操作方法 ====================

    @auto_convert_batch_insert
    def batch_insert(
        self,
        request: Union[InsertRequest, Dict[str, Any]],
        batch_size: int = 1000
    ) -> OperationResponse:
        """
        批量插入数据（同步）- 串行分批处理

        支持的调用方式：
            1. batch_insert(InsertRequest(...))
            2. batch_insert({"table": "table_name", "data": [...]})
            3. batch_insert(table="table_name", data=[...])

        Args:
            request: 插入请求或字典，data应该是列表格式 [{"col1": "val1"}, {"col2": "val2"}]
            batch_size: 每批处理的记录数，默认1000

        Returns:
            OperationResponse: 批量操作结果

        Examples:
            # 方式1: 使用Request对象
            request = InsertRequest(table="users", data=[{"name": "张三"}, {"name": "李四"}])
            result = client.batch_insert(request)

            # 方式2: 使用字典
            result = client.batch_insert({"table": "users", "data": [{"name": "张三"}]})

            # 方式3: 使用关键字参数（推荐）
            result = client.batch_insert(table="users", data=[{"name": "张三"}])

        Note:
            此方法使用同步串行处理，适用于简单场景。
            如需高性能并行处理，请使用 abatch_insert() 异步方法。
        """
        # 确保连接
        self._ensure_sync_engine()

        start_time = time.time()

        # 获取logger
        import logging
        logger = logging.getLogger(__name__)

        # 装饰器已经处理了请求转换，这里直接验证
        if not isinstance(request.data, list):
            raise ValueError("batch_insert requires request.data to be a list, e.g., [{'col1': 'val1'}, {'col2': 'val2'}]")

        if not request.data:
            raise ValueError("Insert data cannot be empty")

        # 数据完整性检查和日志
        input_record_count = len(request.data)
        expected_batches = (input_record_count + batch_size - 1) // batch_size

        logger.info(f"Starting batch_insert: table={request.table}, "
                   f"total_records={input_record_count}, batch_size={batch_size}, "
                   f"expected_batches={expected_batches}")

        total_affected = 0
        total_errors = []
        processed_records = 0
        successful_batches = 0

        try:
            # 分批处理（同步串行）
            for i in range(0, len(request.data), batch_size):
                batch_data = request.data[i:i + batch_size]
                batch_num = i // batch_size + 1
                batch_record_count = len(batch_data)

                logger.debug(f"Processing batch {batch_num}: records {i+1}-{i+batch_record_count} "
                           f"({batch_record_count} records)")

                try:
                    # 创建批次请求
                    batch_request = InsertRequest(
                        table=request.table,
                        data=batch_data
                    )

                    # 执行批次插入 - 直接调用现有的insert逻辑
                    batch_result = self.insert(batch_request)

                    # 验证批次结果
                    if batch_result.affected_rows != batch_record_count:
                        logger.warning(f"Batch {batch_num} record count mismatch: "
                                     f"expected={batch_record_count}, affected={batch_result.affected_rows}")

                    total_affected += batch_result.affected_rows
                    processed_records += batch_record_count
                    successful_batches += 1

                    logger.debug(f"Batch {batch_num} completed successfully: "
                               f"affected_rows={batch_result.affected_rows}")

                except Exception as e:
                    error_msg = f"Batch {batch_num} (records {i+1}-{i+batch_record_count}) failed: {str(e)}"
                    total_errors.append(error_msg)
                    processed_records += batch_record_count  # 仍然计入已处理（虽然失败）

                    logger.error(f"Batch {batch_num} failed: {str(e)}", exc_info=True)
                    # 同步版本继续处理所有批次，不中断

            execution_time = time.time() - start_time

            # 数据完整性验证
            if processed_records != input_record_count:
                logger.error(f"Data integrity check failed: "
                           f"input_records={input_record_count}, processed_records={processed_records}")
                total_errors.append(f"Data integrity error: processed {processed_records} but expected {input_record_count}")

            # 成功率统计
            success_rate = successful_batches / expected_batches if expected_batches > 0 else 0

            # 记录最终结果
            if len(total_errors) == 0:
                logger.info(f"Batch insert completed successfully: "
                          f"table={request.table}, total_affected={total_affected}, "
                          f"batches={successful_batches}/{expected_batches}, "
                          f"time={execution_time:.2f}s")
            else:
                logger.warning(f"Batch insert completed with errors: "
                             f"table={request.table}, total_affected={total_affected}, "
                             f"successful_batches={successful_batches}/{expected_batches}, "
                             f"failed_batches={len(total_errors)}, time={execution_time:.2f}s")

                # 记录所有错误
                for error in total_errors:
                    logger.error(f"Batch error: {error}")

            return OperationResponse(
                success=len(total_errors) == 0,
                affected_rows=total_affected,
                execution_time=execution_time,
                operation_sql=f"BATCH INSERT INTO {request.table}",
                operation_parameters={
                    "input_record_count": input_record_count,
                    "processed_record_count": processed_records,
                    "total_batches": expected_batches,
                    "successful_batches": successful_batches,
                    "failed_batches": len(total_errors),
                    "success_rate": success_rate,
                    "data_integrity_check": processed_records == input_record_count,
                    "errors": total_errors
                }
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "batch_insert",
                "table": request.table,
                "data_count": len(request.data),
                "batch_size": batch_size
            })

    @auto_convert_batch_insert
    async def abatch_insert(
        self,
        request: Union[InsertRequest, Dict[str, Any]],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> OperationResponse:
        """
        批量插入数据（异步）- 支持并行分批处理和多种调用方式

        支持的调用方式：
            1. await abatch_insert(InsertRequest(...))
            2. await abatch_insert({"table": "table_name", "data": [...]})
            3. await abatch_insert(table="table_name", data=[...])

        Args:
            request: 插入请求或字典，data应该是列表格式 [{"col1": "val1"}, {"col2": "val2"}]
            batch_size: 每批处理的记录数，默认1000
            max_concurrency: 最大并发批次数，默认5（基于连接池大小优化）
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            OperationResponse: 批量操作结果，包含详细的成功/失败信息

        Examples:
            # 方式1: 使用Request对象
            request = InsertRequest(table="users", data=[{"name": "张三"}, {"name": "李四"}])
            result = await client.abatch_insert(request)

            # 方式2: 使用字典
            result = await client.abatch_insert({"table": "users", "data": [{"name": "张三"}]})

            # 方式3: 使用关键字参数（推荐）
            result = await client.abatch_insert(table="users", data=[{"name": "张三"}])

            # 方式4: 自定义并发参数
            result = await client.abatch_insert(
                table="users",
                data=large_data_list,
                batch_size=500,
                max_concurrency=10  # 更高并发
            )
        """
        await self._ensure_async_engine()
        start_time = time.time()

        # 获取logger
        import logging
        logger = logging.getLogger(__name__)

        if not isinstance(request.data, list):
            raise ValueError("batch_insert requires request.data to be a list")

        if not request.data:
            raise ValueError("Insert data cannot be empty")

        # 数据完整性检查和日志
        input_record_count = len(request.data)
        expected_batches = (input_record_count + batch_size - 1) // batch_size

        logger.info(f"Starting abatch_insert: table={request.table}, "
                   f"total_records={input_record_count}, batch_size={batch_size}, "
                   f"expected_batches={expected_batches}, max_concurrency={max_concurrency}")

        # 导入asyncio用于并行处理
        import asyncio

        # 创建批次列表
        batches = []
        total_records_in_batches = 0
        for i in range(0, len(request.data), batch_size):
            batch_data = request.data[i:i + batch_size]
            batch_num = i // batch_size + 1
            batches.append((batch_num, batch_data))
            total_records_in_batches += len(batch_data)

            logger.debug(f"Created batch {batch_num}: records {i+1}-{i+len(batch_data)} "
                        f"({len(batch_data)} records)")

        # 验证批次分割的完整性
        if total_records_in_batches != input_record_count:
            logger.error(f"Batch splitting integrity check failed: "
                        f"input_records={input_record_count}, "
                        f"total_in_batches={total_records_in_batches}")
            raise ValueError(f"Batch splitting error: expected {input_record_count} records, "
                           f"but batches contain {total_records_in_batches} records")

        # 并行处理批次
        semaphore = asyncio.Semaphore(max_concurrency)

        async def process_batch(batch_num: int, batch_data: List[Dict[str, Any]]) -> Dict[str, Any]:
            """处理单个批次"""
            batch_record_count = len(batch_data)

            async with semaphore:
                logger.debug(f"Starting batch {batch_num} processing: {batch_record_count} records")

                try:
                    # 创建批次请求
                    batch_request = InsertRequest(
                        table=request.table,
                        data=batch_data
                    )

                    # 执行批次插入（使用独立连接）
                    async with self.async_engine.begin() as conn:
                        sql, params = self._build_insert_sql(batch_request)

                        logger.debug(f"Batch {batch_num} executing SQL with {len(params)} parameters")

                        result = await asyncio.wait_for(
                            conn.execute(text(sql), params),
                            timeout=timeout_per_batch
                        )

                        affected_rows = result.rowcount

                        # 验证批次结果
                        if affected_rows != batch_record_count:
                            logger.warning(f"Batch {batch_num} record count mismatch: "
                                         f"expected={batch_record_count}, affected={affected_rows}")

                        logger.debug(f"Batch {batch_num} completed successfully: "
                                   f"affected_rows={affected_rows}")

                        return {
                            "batch_num": batch_num,
                            "success": True,
                            "affected_rows": affected_rows,
                            "expected_rows": batch_record_count,
                            "error": None
                        }

                except Exception as e:
                    error_msg = str(e)
                    logger.error(f"Batch {batch_num} failed: {error_msg}", exc_info=True)

                    return {
                        "batch_num": batch_num,
                        "success": False,
                        "affected_rows": 0,
                        "expected_rows": batch_record_count,
                        "error": error_msg
                    }

        try:
            # 并行执行所有批次
            batch_results = await asyncio.gather(
                *[process_batch(batch_num, batch_data) for batch_num, batch_data in batches],
                return_exceptions=False
            )

            # 汇总结果和数据完整性验证
            total_affected = sum(result["affected_rows"] for result in batch_results)
            total_expected = sum(result["expected_rows"] for result in batch_results)
            successful_batches = [result for result in batch_results if result["success"]]
            failed_batches = [result for result in batch_results if not result["success"]]

            # 详细的错误信息
            total_errors = []
            for result in failed_batches:
                error_msg = f"Batch {result['batch_num']} (expected {result['expected_rows']} records) failed: {result['error']}"
                total_errors.append(error_msg)

            # 数据完整性检查
            data_integrity_issues = []
            if total_expected != input_record_count:
                issue = f"Expected records mismatch: input={input_record_count}, batch_total={total_expected}"
                data_integrity_issues.append(issue)
                logger.error(issue)

            # 检查每个成功批次的记录数
            for result in successful_batches:
                if result["affected_rows"] != result["expected_rows"]:
                    issue = f"Batch {result['batch_num']} affected rows mismatch: expected={result['expected_rows']}, affected={result['affected_rows']}"
                    data_integrity_issues.append(issue)
                    logger.warning(issue)

            # 成功率统计
            success_rate = len(successful_batches) / len(batches) if batches else 0

            execution_time = time.time() - start_time

            # 记录最终结果
            if len(total_errors) == 0 and len(data_integrity_issues) == 0:
                logger.info(f"Async batch insert completed successfully: "
                          f"table={request.table}, total_affected={total_affected}, "
                          f"batches={len(successful_batches)}/{len(batches)}, "
                          f"concurrency={max_concurrency}, time={execution_time:.2f}s")
            else:
                logger.warning(f"Async batch insert completed with issues: "
                             f"table={request.table}, total_affected={total_affected}, "
                             f"successful_batches={len(successful_batches)}/{len(batches)}, "
                             f"failed_batches={len(failed_batches)}, "
                             f"integrity_issues={len(data_integrity_issues)}, "
                             f"time={execution_time:.2f}s")

                # 记录所有错误和问题
                for error in total_errors:
                    logger.error(f"Batch error: {error}")
                for issue in data_integrity_issues:
                    logger.error(f"Integrity issue: {issue}")

            return OperationResponse(
                success=len(total_errors) == 0 and len(data_integrity_issues) == 0,
                affected_rows=total_affected,
                execution_time=execution_time,
                operation_sql=f"PARALLEL BATCH INSERT INTO {request.table}",
                operation_parameters={
                    "input_record_count": input_record_count,
                    "total_expected_records": total_expected,
                    "total_batches": len(batches),
                    "successful_batches": len(successful_batches),
                    "failed_batches": len(failed_batches),
                    "success_rate": success_rate,
                    "max_concurrency": max_concurrency,
                    "data_integrity_check": len(data_integrity_issues) == 0,
                    "data_integrity_issues": data_integrity_issues,
                    "errors": total_errors
                }
            )

        except Exception as e:
            raise self.error_adapter.adapt_error(e, {
                "operation": "abatch_insert",
                "table": request.table,
                "data_count": len(request.data),
                "batch_size": batch_size
            })

    def batch_update(
        self,
        table: str,
        updates: List[Dict[str, Any]],
        batch_size: int = 100
    ) -> OperationResponse:
        """
        批量更新数据（同步）- 串行分批处理，支持复杂条件

        Args:
            table: 表名
            updates: 更新数据列表，支持以下格式：
                - 传统格式: [{"data": {...}, "filters": {...}}]
                - 新格式: [{"data": {...}, "where": {...}}]
                - 复杂格式: [{"data": {...}, "where": QueryFilterGroup(...)}]
            batch_size: 每批处理的记录数，默认100

        Returns:
            OperationResponse: 批量操作结果

        Examples:
            # 传统格式（向后兼容）
            updates = [
                {"data": {"name": "张三", "age": 25}, "filters": {"id": 1}},
                {"data": {"name": "李四", "age": 30}, "filters": {"id": 2}}
            ]

            # 新格式
            updates = [
                {"data": {"status": "inactive"}, "where": {"age": {"$lt": 18}}},
                {"data": {"status": "archived"}, "where": {"last_login": {"$lt": "2023-01-01"}}}
            ]

        Note:
            此方法使用同步串行处理，适用于简单场景。
            如需高性能并行处理，请使用 abatch_update() 异步方法。
        """
        # 确保连接
        self._ensure_sync_engine()

        start_time = time.time()
        logger.info(f"开始批量更新操作: 表={table}, 更新数量={len(updates)}, 批次大小={batch_size}")

        if not updates:
            raise ValueError("Updates data cannot be empty")

        total_affected = 0
        total_errors = []
        successful_batches = 0

        try:
            # 分批处理
            total_batches = (len(updates) + batch_size - 1) // batch_size

            for i in range(0, len(updates), batch_size):
                batch_updates = updates[i:i + batch_size]
                batch_num = i // batch_size + 1

                try:
                    batch_affected = 0
                    with self.sync_engine.begin() as conn:
                        for update in batch_updates:
                            # 构建UpdateRequest
                            update_request = self._build_update_request(table, update)
                            sql, params = self._build_update_sql(update_request)

                            result = conn.execute(text(sql), params)
                            batch_affected += result.rowcount

                    total_affected += batch_affected
                    successful_batches += 1
                    logger.debug(f"批次 {batch_num}/{total_batches} 更新成功: 影响行数={batch_affected}")

                except Exception as e:
                    error_msg = f"Update batch {batch_num} failed: {str(e)}"
                    total_errors.append(error_msg)
                    logger.error(f"批次 {batch_num}/{total_batches} 更新失败: {str(e)}")

            execution_time = time.time() - start_time
            success = len(total_errors) == 0

            if success:
                logger.info(f"批量更新完成: 总影响行数={total_affected}, 执行时间={execution_time:.2f}秒")
            else:
                logger.warning(f"批量更新部分失败: 成功批次={successful_batches}/{total_batches}, 总影响行数={total_affected}")

            return OperationResponse(
                success=success,
                affected_rows=total_affected,
                execution_time=execution_time,
                operation_sql=f"BATCH UPDATE {table}",
                operation_parameters={
                    "total_batches": total_batches,
                    "successful_batches": successful_batches,
                    "failed_batches": len(total_errors),
                    "updates_count": len(updates),
                    "errors": total_errors
                }
            )

        except Exception as e:
            logger.error(f"批量更新操作失败: {str(e)}")
            raise self.error_adapter.adapt_error(e, {
                "operation": "batch_update",
                "table": table,
                "updates_count": len(updates),
                "batch_size": batch_size
            })

    async def abatch_update(
        self,
        table: str,
        updates: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 3,
        timeout_per_batch: float = 300.0
    ) -> OperationResponse:
        """
        批量更新数据（异步）- 并行分批处理，支持复杂条件

        Args:
            table: 表名
            updates: 更新数据列表，支持以下格式：
                - 传统格式: [{"data": {...}, "filters": {...}}]
                - 新格式: [{"data": {...}, "where": {...}}]
                - 复杂格式: [{"data": {...}, "where": QueryFilterGroup(...)}]
            batch_size: 每批处理的记录数，默认100
            max_concurrency: 最大并发批次数，默认3
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            OperationResponse: 批量操作结果，包含详细的成功/失败信息

        Examples:
            # 传统格式（向后兼容）
            updates = [
                {"data": {"name": "张三", "age": 25}, "filters": {"id": 1}},
                {"data": {"name": "李四", "age": 30}, "filters": {"id": 2}}
            ]

            # 新格式
            updates = [
                {"data": {"status": "inactive"}, "where": {"age": {"$lt": 18}}},
                {"data": {"status": "archived"}, "where": {"last_login": {"$lt": "2023-01-01"}}}
            ]
        """
        # 确保连接
        await self._ensure_async_engine()

        start_time = time.time()
        logger.info(f"开始异步批量更新操作: 表={table}, 更新数量={len(updates)}, 批次大小={batch_size}, 最大并发={max_concurrency}")

        if not updates:
            raise ValueError("Updates data cannot be empty")

        # 导入asyncio用于并行处理
        import asyncio

        # 创建批次列表
        batches = []
        total_batches = (len(updates) + batch_size - 1) // batch_size
        for i in range(0, len(updates), batch_size):
            batch_updates = updates[i:i + batch_size]
            batches.append((i // batch_size + 1, batch_updates))

        # 并行处理批次
        semaphore = asyncio.Semaphore(max_concurrency)

        async def process_batch(batch_num: int, batch_updates: List[Dict[str, Any]]) -> Dict[str, Any]:
            """处理单个批次"""
            async with semaphore:
                try:
                    batch_affected = 0
                    # 执行批次更新（使用独立连接）
                    async with self.async_engine.begin() as conn:
                        for update in batch_updates:
                            # 构建UpdateRequest
                            update_request = self._build_update_request(table, update)
                            sql, params = self._build_update_sql(update_request)

                            result = await asyncio.wait_for(
                                conn.execute(text(sql), params),
                                timeout=timeout_per_batch
                            )
                            batch_affected += result.rowcount

                    logger.debug(f"异步批次 {batch_num}/{total_batches} 更新成功: 影响行数={batch_affected}")
                    return {
                        "batch_num": batch_num,
                        "success": True,
                        "affected_rows": batch_affected,
                        "error": None
                    }

                except Exception as e:
                    logger.error(f"异步批次 {batch_num}/{total_batches} 更新失败: {str(e)}")
                    return {
                        "batch_num": batch_num,
                        "success": False,
                        "affected_rows": 0,
                        "error": str(e)
                    }

        try:
            # 并行执行所有批次
            batch_results = await asyncio.gather(
                *[process_batch(batch_num, batch_updates) for batch_num, batch_updates in batches],
                return_exceptions=False
            )

            # 汇总结果
            total_affected = sum(result["affected_rows"] for result in batch_results)
            failed_batches = [result for result in batch_results if not result["success"]]
            successful_batches = len(batches) - len(failed_batches)
            total_errors = [f"Batch {result['batch_num']} failed: {result['error']}"
                          for result in failed_batches]

            execution_time = time.time() - start_time
            success = len(total_errors) == 0

            if success:
                logger.info(f"异步批量更新完成: 总影响行数={total_affected}, 执行时间={execution_time:.2f}秒, 并发批次={max_concurrency}")
            else:
                logger.warning(f"异步批量更新部分失败: 成功批次={successful_batches}/{total_batches}, 总影响行数={total_affected}")

            return OperationResponse(
                success=success,
                affected_rows=total_affected,
                execution_time=execution_time,
                operation_sql=f"PARALLEL BATCH UPDATE {table}",
                operation_parameters={
                    "total_batches": total_batches,
                    "successful_batches": successful_batches,
                    "failed_batches": len(failed_batches),
                    "updates_count": len(updates),
                    "max_concurrency": max_concurrency,
                    "errors": total_errors
                }
            )

        except Exception as e:
            logger.error(f"异步批量更新操作失败: {str(e)}")
            raise self.error_adapter.adapt_error(e, {
                "operation": "abatch_update",
                "table": table,
                "updates_count": len(updates),
                "batch_size": batch_size
            })

    def batch_delete(
        self,
        table: str,
        conditions: List[Union[Dict[str, Any], 'QueryFilterGroup']],
        batch_size: int = 1000
    ) -> OperationResponse:
        """
        批量删除数据（同步）- 串行分批处理，支持复杂条件

        Args:
            table: 表名
            conditions: 删除条件列表，支持以下格式：
                - 简单条件: [{"id": 1}, {"status": "inactive"}]
                - 复杂条件: [{"age": {"$lt": 18}}, {"name": {"$like": "%test%"}}]
                - QueryFilterGroup: [QueryFilterGroup(...)]
            batch_size: 每批处理的记录数

        Returns:
            OperationResponse: 批量操作结果

        Examples:
            # 简单ID删除
            client.batch_delete("users", [{"id": 1}, {"id": 2}, {"id": 3}])

            # 条件删除
            client.batch_delete("users", [
                {"status": "inactive"},
                {"age": {"$lt": 18}},
                {"last_login": {"$lt": "2023-01-01"}}
            ])

        Note:
            此方法使用同步串行处理，适用于简单场景。
            如需高性能并行处理，请使用 abatch_delete() 异步方法。
        """
        # 确保连接
        self._ensure_sync_engine()

        start_time = time.time()
        logger.info(f"开始批量删除操作: 表={table}, 条件数量={len(conditions)}, 批次大小={batch_size}")

        if not conditions:
            raise ValueError("Conditions list cannot be empty")

        total_affected = 0
        total_errors = []
        successful_batches = 0

        try:
            # 分批处理条件
            total_batches = (len(conditions) + batch_size - 1) // batch_size

            for i in range(0, len(conditions), batch_size):
                batch_conditions = conditions[i:i + batch_size]
                batch_num = i // batch_size + 1

                try:
                    batch_affected = 0
                    with self.sync_engine.begin() as conn:
                        for condition in batch_conditions:
                            # 转换条件为DeleteRequest
                            delete_request = self._build_delete_request(table, condition)
                            sql, params = self._build_delete_sql(delete_request)

                            result = conn.execute(text(sql), params)
                            batch_affected += result.rowcount

                    total_affected += batch_affected
                    successful_batches += 1
                    logger.debug(f"批次 {batch_num}/{total_batches} 删除成功: 影响行数={batch_affected}")

                except Exception as e:
                    error_msg = f"Delete batch {batch_num} failed: {str(e)}"
                    total_errors.append(error_msg)
                    logger.error(f"批次 {batch_num}/{total_batches} 删除失败: {str(e)}")

            execution_time = time.time() - start_time
            success = len(total_errors) == 0

            if success:
                logger.info(f"批量删除完成: 总影响行数={total_affected}, 执行时间={execution_time:.2f}秒")
            else:
                logger.warning(f"批量删除部分失败: 成功批次={successful_batches}/{total_batches}, 总影响行数={total_affected}")

            return OperationResponse(
                success=success,
                affected_rows=total_affected,
                execution_time=execution_time,
                operation_sql=f"BATCH DELETE FROM {table}",
                operation_parameters={
                    "total_batches": total_batches,
                    "successful_batches": successful_batches,
                    "failed_batches": len(total_errors),
                    "conditions_count": len(conditions),
                    "errors": total_errors
                }
            )

        except Exception as e:
            logger.error(f"批量删除操作失败: {str(e)}")
            raise self.error_adapter.adapt_error(e, {
                "operation": "batch_delete",
                "table": table,
                "conditions_count": len(conditions),
                "batch_size": batch_size
            })

    async def abatch_delete(
        self,
        table: str,
        conditions: List[Union[Dict[str, Any], 'QueryFilterGroup']],
        batch_size: int = 1000,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> OperationResponse:
        """
        批量删除数据（异步）- 并行分批处理，支持复杂条件

        Args:
            table: 表名
            conditions: 删除条件列表，支持以下格式：
                - 简单条件: [{"id": 1}, {"status": "inactive"}]
                - 复杂条件: [{"age": {"$lt": 18}}, {"name": {"$like": "%test%"}}]
                - QueryFilterGroup: [QueryFilterGroup(...)]
            batch_size: 每批处理的记录数
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            OperationResponse: 批量操作结果，包含详细的成功/失败信息

        Examples:
            # 简单ID删除
            await client.abatch_delete("users", [{"id": 1}, {"id": 2}, {"id": 3}])

            # 条件删除
            await client.abatch_delete("users", [
                {"status": "inactive"},
                {"age": {"$lt": 18}},
                {"last_login": {"$lt": "2023-01-01"}}
            ])
        """
        # 确保连接
        await self._ensure_async_engine()

        start_time = time.time()
        logger.info(f"开始异步批量删除操作: 表={table}, 条件数量={len(conditions)}, 批次大小={batch_size}, 最大并发={max_concurrency}")

        if not conditions:
            raise ValueError("Conditions list cannot be empty")

        # 导入asyncio用于并行处理
        import asyncio

        # 创建批次列表
        batches = []
        total_batches = (len(conditions) + batch_size - 1) // batch_size
        for i in range(0, len(conditions), batch_size):
            batch_conditions = conditions[i:i + batch_size]
            batches.append((i // batch_size + 1, batch_conditions))

        # 并行处理批次
        semaphore = asyncio.Semaphore(max_concurrency)

        async def process_batch(batch_num: int, batch_conditions: List[Union[Dict[str, Any], 'QueryFilterGroup']]) -> Dict[str, Any]:
            """处理单个批次"""
            async with semaphore:
                try:
                    batch_affected = 0
                    # 执行批次删除（使用独立连接）
                    async with self.async_engine.begin() as conn:
                        for condition in batch_conditions:
                            # 转换条件为DeleteRequest
                            delete_request = self._build_delete_request(table, condition)
                            sql, params = self._build_delete_sql(delete_request)

                            result = await asyncio.wait_for(
                                conn.execute(text(sql), params),
                                timeout=timeout_per_batch
                            )
                            batch_affected += result.rowcount

                    logger.debug(f"异步批次 {batch_num}/{total_batches} 删除成功: 影响行数={batch_affected}")
                    return {
                        "batch_num": batch_num,
                        "success": True,
                        "affected_rows": batch_affected,
                        "error": None
                    }

                except Exception as e:
                    logger.error(f"异步批次 {batch_num}/{total_batches} 删除失败: {str(e)}")
                    return {
                        "batch_num": batch_num,
                        "success": False,
                        "affected_rows": 0,
                        "error": str(e)
                    }

        try:
            # 并行执行所有批次
            batch_results = await asyncio.gather(
                *[process_batch(batch_num, batch_conditions) for batch_num, batch_conditions in batches],
                return_exceptions=False
            )

            # 汇总结果
            total_affected = sum(result["affected_rows"] for result in batch_results)
            failed_batches = [result for result in batch_results if not result["success"]]
            successful_batches = len(batches) - len(failed_batches)
            total_errors = [f"Batch {result['batch_num']} failed: {result['error']}"
                          for result in failed_batches]

            execution_time = time.time() - start_time
            success = len(total_errors) == 0

            if success:
                logger.info(f"异步批量删除完成: 总影响行数={total_affected}, 执行时间={execution_time:.2f}秒, 并发批次={max_concurrency}")
            else:
                logger.warning(f"异步批量删除部分失败: 成功批次={successful_batches}/{total_batches}, 总影响行数={total_affected}")

            return OperationResponse(
                success=success,
                affected_rows=total_affected,
                execution_time=execution_time,
                operation_sql=f"PARALLEL BATCH DELETE FROM {table}",
                operation_parameters={
                    "total_batches": total_batches,
                    "successful_batches": successful_batches,
                    "failed_batches": len(failed_batches),
                    "conditions_count": len(conditions),
                    "max_concurrency": max_concurrency,
                    "errors": total_errors
                }
            )

        except Exception as e:
            logger.error(f"异步批量删除操作失败: {str(e)}")
            raise self.error_adapter.adapt_error(e, {
                "operation": "abatch_delete",
                "table": table,
                "conditions_count": len(conditions),
                "batch_size": batch_size
            })

    def _build_query_request(self, table: str, query: Dict[str, Any]) -> 'QueryRequest':
        """构建QueryRequest对象"""
        from .....base.rdb import QueryRequest, QueryFilterGroup

        # 获取列选择
        columns = query.get("data")
        if columns == ["*"] or columns == "*":
            columns = None  # None 表示 SELECT *

        # 获取过滤条件
        filters = None
        if "filters" in query and query["filters"]:
            filter_conditions = query["filters"]
            if isinstance(filter_conditions, QueryFilterGroup):
                filters = filter_conditions
            elif isinstance(filter_conditions, dict):
                # 检查是否为简单字典格式
                if self._is_simple_dict_filters(filter_conditions):
                    # 简单字典格式，转换为简单的AND条件组
                    filters = self._convert_simple_dict_to_filter_group(filter_conditions)
                else:
                    # 复杂字典格式，使用完整转换
                    filters = self._convert_dict_to_filter_group(filter_conditions)
            else:
                raise ValueError(f"Unsupported filters type: {type(filter_conditions)}")

        # 获取其他查询参数
        sorts = query.get("sorts")
        limit = query.get("limit")
        offset = query.get("offset")

        return QueryRequest(
            table=table,
            columns=columns,
            filters=filters,
            sorts=sorts,
            limit=limit,
            offset=offset
        )

    def batch_query(
        self,
        table: str,
        queries: List[Dict[str, Any]],
        batch_size: int = 100
    ) -> List[QueryResponse]:
        """
        批量查询数据（同步）- 串行分批处理，支持不同查询条件

        Args:
            table: 表名
            queries: 查询条件列表，支持以下格式：
                - [{"data": ["id", "name"], "filters": {"status": "active"}},
                   {"data": ["*"], "filters": {"age": {"$gt": 18}}}]
                - 每个查询可以包含：data, filters, sorts, limit, offset
            batch_size: 每批处理的查询数，默认100

        Returns:
            List[QueryResponse]: 每个查询的结果列表，保持与输入顺序一致

        Examples:
            # 批量查询不同条件的数据
            queries = [
                {"data": ["id", "name"], "filters": {"status": "active"}},
                {"data": ["id", "email"], "filters": {"department": "IT"}},
                {"data": ["*"], "filters": {"created_date": {"$gte": "2024-01-01"}}}
            ]
            results = client.batch_query("users", queries)

            # 支持复杂查询条件
            queries = [
                {
                    "data": ["id", "name", "age"],
                    "filters": {"age": {"$between": [18, 65]}},
                    "sorts": [{"field": "name", "order": "asc"}],
                    "limit": 10
                }
            ]
            results = client.batch_query("users", queries)

        Note:
            此方法使用同步串行处理，适用于简单场景。
            如需高性能并行处理，请使用 abatch_query() 异步方法。
            失败的查询将返回包含错误信息的空结果。
        """
        # 确保连接
        self._ensure_sync_engine()

        start_time = time.time()
        logger.info(f"开始批量查询操作: 表={table}, 查询数量={len(queries)}, 批次大小={batch_size}")

        if not queries:
            raise ValueError("Queries list cannot be empty")

        # 验证查询格式
        for i, query in enumerate(queries):
            if not isinstance(query, dict):
                raise ValueError(f"Query {i} must be a dictionary")
            if "filters" not in query and "data" not in query:
                raise ValueError(f"Query {i} must contain at least 'filters' or 'data' field")

        results = []
        total_errors = []
        successful_queries = 0
        processed_queries = 0

        try:
            # 分批处理查询
            total_batches = (len(queries) + batch_size - 1) // batch_size

            for i in range(0, len(queries), batch_size):
                batch_queries = queries[i:i + batch_size]
                batch_num = i // batch_size + 1

                logger.debug(f"处理批次 {batch_num}/{total_batches}: 查询 {i+1}-{i+len(batch_queries)}")

                # 处理批次中的每个查询
                for j, query in enumerate(batch_queries):
                    query_index = i + j
                    try:
                        # 构建QueryRequest
                        query_request = self._build_query_request(table, query)

                        # 执行查询
                        query_result = self.query(query_request)
                        results.append(query_result)
                        successful_queries += 1

                        logger.debug(f"查询 {query_index + 1} 成功: 返回 {query_result.total_count} 条记录")

                    except Exception as e:
                        error_msg = f"Query {query_index + 1} failed: {str(e)}"
                        total_errors.append(error_msg)

                        # 创建错误响应
                        error_response = QueryResponse(
                            data=[],
                            total_count=0,
                            execution_time=0.0,
                            query_sql="",
                            query_parameters={},
                            database_type=self.database_type
                        )
                        results.append(error_response)

                        logger.error(f"查询 {query_index + 1} 失败: {str(e)}")

                    processed_queries += 1

            execution_time = time.time() - start_time

            # 数据完整性验证
            if len(results) != len(queries):
                logger.error(f"结果数量不匹配: 期望={len(queries)}, 实际={len(results)}")
                raise ValueError(f"Result count mismatch: expected {len(queries)}, got {len(results)}")

            # 记录最终结果
            if len(total_errors) == 0:
                logger.info(f"批量查询完成: 表={table}, 成功查询={successful_queries}/{len(queries)}, "
                          f"执行时间={execution_time:.2f}秒")
            else:
                logger.warning(f"批量查询部分失败: 表={table}, 成功查询={successful_queries}/{len(queries)}, "
                             f"失败查询={len(total_errors)}, 执行时间={execution_time:.2f}秒")

                # 记录所有错误
                for error in total_errors:
                    logger.error(f"查询错误: {error}")

            return results

        except Exception as e:
            logger.error(f"批量查询操作失败: {str(e)}")
            raise self.error_adapter.adapt_error(e, {
                "operation": "batch_query",
                "table": table,
                "queries_count": len(queries),
                "batch_size": batch_size
            })

    async def abatch_query(
        self,
        table: str,
        queries: List[Dict[str, Any]],
        batch_size: int = 100,
        max_concurrency: int = 5,
        timeout_per_batch: float = 300.0
    ) -> List[QueryResponse]:
        """
        批量查询数据（异步）- 并行分批处理，支持不同查询条件

        Args:
            table: 表名
            queries: 查询条件列表，支持以下格式：
                - [{"data": ["id", "name"], "filters": {"status": "active"}},
                   {"data": ["*"], "filters": {"age": {"$gt": 18}}}]
                - 每个查询可以包含：data, filters, sorts, limit, offset
            batch_size: 每批处理的查询数，默认100
            max_concurrency: 最大并发批次数，默认5
            timeout_per_batch: 每批次超时时间（秒），默认300秒

        Returns:
            List[QueryResponse]: 每个查询的结果列表，保持与输入顺序一致

        Examples:
            # 批量查询不同条件的数据
            queries = [
                {"data": ["id", "name"], "filters": {"status": "active"}},
                {"data": ["id", "email"], "filters": {"department": "IT"}},
                {"data": ["*"], "filters": {"created_date": {"$gte": "2024-01-01"}}}
            ]
            results = await client.abatch_query("users", queries)

            # 支持复杂查询条件和并发控制
            results = await client.abatch_query(
                table="users",
                queries=large_queries_list,
                batch_size=50,
                max_concurrency=10  # 更高并发
            )

        Note:
            此方法使用异步并行处理，适用于高性能场景。
            失败的查询将返回包含错误信息的空结果。
        """
        # 确保连接
        await self._ensure_async_engine()

        start_time = time.time()
        logger.info(f"开始异步批量查询操作: 表={table}, 查询数量={len(queries)}, "
                   f"批次大小={batch_size}, 最大并发={max_concurrency}")

        if not queries:
            raise ValueError("Queries list cannot be empty")

        # 验证查询格式
        for i, query in enumerate(queries):
            if not isinstance(query, dict):
                raise ValueError(f"Query {i} must be a dictionary")
            if "filters" not in query and "data" not in query:
                raise ValueError(f"Query {i} must contain at least 'filters' or 'data' field")

        # 导入asyncio用于并行处理
        import asyncio

        # 创建批次列表，保持查询的原始索引
        batches = []
        total_batches = (len(queries) + batch_size - 1) // batch_size
        for i in range(0, len(queries), batch_size):
            batch_queries = queries[i:i + batch_size]
            batch_start_index = i
            batches.append((i // batch_size + 1, batch_start_index, batch_queries))

        # 初始化结果列表，保持与输入顺序一致
        results = [None] * len(queries)

        # 并行处理批次
        semaphore = asyncio.Semaphore(max_concurrency)

        async def process_batch(batch_num: int, batch_start_index: int, batch_queries: List[Dict[str, Any]]) -> Dict[str, Any]:
            """处理单个批次"""
            async with semaphore:
                logger.debug(f"开始处理异步批次 {batch_num}/{total_batches}: "
                           f"查询 {batch_start_index + 1}-{batch_start_index + len(batch_queries)}")

                batch_results = []
                batch_errors = []

                try:
                    # 处理批次中的每个查询
                    for j, query in enumerate(batch_queries):
                        query_index = batch_start_index + j
                        try:
                            # 构建QueryRequest
                            query_request = self._build_query_request(table, query)

                            # 执行查询（使用异步查询方法以保持连接一致性）
                            query_response = await asyncio.wait_for(
                                self.aquery(query_request),
                                timeout=timeout_per_batch
                            )

                            batch_results.append((query_index, query_response))
                            logger.debug(f"异步查询 {query_index + 1} 成功: 返回 {query_response.total_count} 条记录")

                        except Exception as e:
                            error_msg = f"Query {query_index + 1} failed: {str(e)}"
                            batch_errors.append(error_msg)

                            # 创建错误响应
                            error_response = QueryResponse(
                                data=[],
                                total_count=0,
                                execution_time=0.0,
                                query_sql="",
                                query_parameters={},
                                database_type=self.database_type
                            )
                            batch_results.append((query_index, error_response))

                            logger.error(f"异步查询 {query_index + 1} 失败: {str(e)}")

                    logger.debug(f"异步批次 {batch_num}/{total_batches} 处理完成: "
                               f"成功={len(batch_results) - len(batch_errors)}, 失败={len(batch_errors)}")

                    return {
                        "batch_num": batch_num,
                        "success": True,
                        "results": batch_results,
                        "errors": batch_errors
                    }

                except Exception as e:
                    logger.error(f"异步批次 {batch_num}/{total_batches} 处理失败: {str(e)}")

                    # 为整个批次创建错误响应
                    batch_error_results = []
                    for j in range(len(batch_queries)):
                        query_index = batch_start_index + j
                        error_response = QueryResponse(
                            data=[],
                            total_count=0,
                            execution_time=0.0,
                            query_sql="",
                            query_parameters={},
                            database_type=self.database_type
                        )
                        batch_error_results.append((query_index, error_response))

                    return {
                        "batch_num": batch_num,
                        "success": False,
                        "results": batch_error_results,
                        "errors": [f"Batch {batch_num} failed: {str(e)}"]
                    }

        try:
            # 并行执行所有批次
            batch_results = await asyncio.gather(
                *[process_batch(batch_num, batch_start_index, batch_queries)
                  for batch_num, batch_start_index, batch_queries in batches],
                return_exceptions=False
            )

            # 汇总结果，保持原始顺序
            total_errors = []
            successful_queries = 0

            for batch_result in batch_results:
                # 将批次结果放入正确的位置
                for query_index, query_response in batch_result["results"]:
                    results[query_index] = query_response
                    if query_response.data or query_response.total_count > 0:
                        successful_queries += 1

                # 收集错误信息
                total_errors.extend(batch_result["errors"])

            # 数据完整性验证
            if None in results:
                missing_indices = [i for i, result in enumerate(results) if result is None]
                logger.error(f"部分查询结果缺失: 索引={missing_indices}")
                raise ValueError(f"Missing results for queries at indices: {missing_indices}")

            execution_time = time.time() - start_time

            # 记录最终结果
            if len(total_errors) == 0:
                logger.info(f"异步批量查询完成: 表={table}, 成功查询={successful_queries}/{len(queries)}, "
                          f"执行时间={execution_time:.2f}秒, 并发批次={max_concurrency}")
            else:
                logger.warning(f"异步批量查询部分失败: 表={table}, 成功查询={successful_queries}/{len(queries)}, "
                             f"失败查询={len(total_errors)}, 执行时间={execution_time:.2f}秒")

                # 记录所有错误
                for error in total_errors:
                    logger.error(f"异步查询错误: {error}")

            return results

        except Exception as e:
            logger.error(f"异步批量查询操作失败: {str(e)}")
            raise self.error_adapter.adapt_error(e, {
                "operation": "abatch_query",
                "table": table,
                "queries_count": len(queries),
                "batch_size": batch_size
            })

    # ==================== Connection Status Properties ====================

    @property
    def is_sync_connected(self) -> bool:
        """Check if sync connection is established"""
        return self._sync_connected and self.sync_engine is not None

    @property
    def is_async_connected(self) -> bool:
        """Check if async connection is established"""
        return self._async_connected and self.async_engine is not None



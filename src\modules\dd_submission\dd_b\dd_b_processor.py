"""
DD-B处理器主接口

提供DD提交流程下一步处理的统一接口，包括：
- 数据查询和处理
- 字段自动填充
- 结果格式化

这是DD-B模块的主要接口文件。
"""

import time
import logging
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)

from modules.dd_submission.dd_b.core.data_processor import DataProcessor
from modules.dd_submission.dd_b.infrastructure.models import (
    DDBProcessRequest,
    DDBProcessResult,
    DDBRecord,
    ProcessingStatusEnum
)
from modules.dd_submission.dd_b.infrastructure.exceptions import (
    DDBError,
    DDBValidationError,
    handle_async_ddb_errors,
    DDBErrorFormatter
)

class DDBProcessor:
    """
    DD-B处理器主类
    
    提供DD提交流程下一步处理的完整功能，包括数据查询、
    字段检查、自动填充等核心业务逻辑。
    """
    
    def __init__(self, rdb_client: Any, vdb_client: Any = None, embedding_client: Any = None):
        """
        初始化DD-B处理器

        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端（可选）
            embedding_client: 向量化模型客户端（可选）
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
        self.data_processor = DataProcessor(rdb_client, vdb_client, embedding_client)

        # 记录是否启用了历史连接器功能
        self.history_enabled = vdb_client is not None

        logger.info(f"DD-B处理器初始化完成 (历史连接器: {'启用' if self.history_enabled else '禁用'})")
    
    @handle_async_ddb_errors
    async def process(self, request: DDBProcessRequest) -> DDBProcessResult:
        """
        处理DD-B请求
        
        Args:
            request: DD-B处理请求
            
        Returns:
            处理结果
        """
        try:
            logger.info(f"开始处理DD-B请求: {request.report_code} - {request.dept_id}")
            
            # 验证请求参数
            request.validate()
            
            # 执行数据处理
            result = await self.data_processor.process_records(request)
            
            logger.info(f"DD-B请求处理完成: {request.report_code} - {request.dept_id}")
            return result
            
        except DDBValidationError as e:
            logger.error(f"DD-B请求验证失败: {e}")
            raise
        except Exception as e:
            logger.error(f"DD-B请求处理失败: {e}")
            raise DDBError(f"DD-B请求处理失败: {str(e)}")
    
    @handle_async_ddb_errors
    async def process_simple(
        self, 
        report_code: str, 
        dept_id: str,
        **kwargs
    ) -> DDBProcessResult:
        """
        简化的处理接口
        
        Args:
            report_code: 报表代码
            dept_id: 部门ID
            **kwargs: 其他可选参数
            
        Returns:
            处理结果
        """
        request = DDBProcessRequest(
            report_code=report_code,
            dept_id=dept_id,
            **kwargs
        )
        
        return await self.process(request)
    
    @handle_async_ddb_errors
    async def get_records_summary(
        self, 
        report_code: str, 
        dept_id: str
    ) -> Dict[str, Any]:
        """
        获取记录摘要信息（不进行填充处理）
        
        Args:
            report_code: 报表代码
            dept_id: 部门ID
            
        Returns:
            记录摘要信息
        """
        request = DDBProcessRequest(
            report_code=report_code,
            dept_id=dept_id,
            enable_auto_fill=False,  # 禁用自动填充
            return_original_data=True
        )
        
        result = await self.process(request)
        
        return {
            "report_code": report_code,
            "dept_id": dept_id,
            "total_records": result.total_records_found,
            "complete_records": result.records_with_complete_main_fields,
            "incomplete_records": result.total_records_found - result.records_with_complete_main_fields,
            "processing_time_ms": result.processing_time_ms,
            "status": result.status.value
        }
    
    @handle_async_ddb_errors
    async def check_data_completeness(
        self, 
        report_code: str, 
        dept_id: str
    ) -> Dict[str, Any]:
        """
        检查数据完整性
        
        Args:
            report_code: 报表代码
            dept_id: 部门ID
            
        Returns:
            完整性检查结果
        """
        summary = await self.get_records_summary(report_code, dept_id)
        
        total = summary["total_records"]
        complete = summary["complete_records"]
        incomplete = summary["incomplete_records"]
        
        completeness_rate = (complete / total * 100) if total > 0 else 0
        
        return {
            "report_code": report_code,
            "dept_id": dept_id,
            "completeness_check": {
                "total_records": total,
                "complete_records": complete,
                "incomplete_records": incomplete,
                "completeness_rate": round(completeness_rate, 2),
                "needs_filling": incomplete > 0
            },
            "recommendation": {
                "action": "fill_required" if incomplete > 0 else "no_action_needed",
                "message": f"发现 {incomplete} 条记录需要填充" if incomplete > 0 else "所有记录都完整"
            }
        }
    
    async def get_health_status(self) -> Dict[str, Any]:
        """
        获取处理器健康状态
        
        Returns:
            健康状态信息
        """
        try:
            # 简单的健康检查：尝试连接数据库
            # 这里可以根据实际的数据库客户端API进行调整
            health_status = {
                "status": "healthy",
                "database_connection": "connected",
                "processor_ready": True,
                "timestamp": time.time()
            }
            
            logger.debug("DD-B处理器健康检查通过")
            return health_status
            
        except Exception as e:
            logger.error(f"DD-B处理器健康检查失败: {e}")
            return {
                "status": "unhealthy",
                "database_connection": "disconnected",
                "processor_ready": False,
                "error": str(e),
                "timestamp": time.time()
            }


# 为了保持向后兼容性，提供原有的类名别名
DDBProcessorLogic = DDBProcessor


# 便捷函数
async def process_dd_b_data(
    rdb_client: Any,
    report_code: str,
    dept_id: str,
    vdb_client: Any = None,
    embedding_client: Any = None,
    **kwargs
) -> DDBProcessResult:
    """便捷函数：处理DD-B数据"""
    processor = DDBProcessor(rdb_client, vdb_client, embedding_client)
    return await processor.process_simple(report_code, dept_id, **kwargs)


async def check_dd_b_completeness(
    rdb_client: Any,
    report_code: str,
    dept_id: str,
    vdb_client: Any = None,
    embedding_client: Any = None
) -> Dict[str, Any]:
    """便捷函数：检查DD-B数据完整性"""
    processor = DDBProcessor(rdb_client, vdb_client, embedding_client)
    return await processor.check_data_completeness(report_code, dept_id)


async def get_dd_b_summary(
    rdb_client: Any,
    report_code: str,
    dept_id: str,
    vdb_client: Any = None,
    embedding_client: Any = None
) -> Dict[str, Any]:
    """便捷函数：获取DD-B数据摘要"""
    processor = DDBProcessor(rdb_client, vdb_client, embedding_client)
    return await processor.get_records_summary(report_code, dept_id)

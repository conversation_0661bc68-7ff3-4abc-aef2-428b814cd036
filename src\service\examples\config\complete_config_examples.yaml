# 完整的动态配置示例
# 展示各种真实企业场景的配置结构

# ==================== 示例1：标准企业配置中心 ====================
enterprise_config_center:
  # 数据库连接配置
  _target_: base.db.implementations.rdb.universal.factory.create_mysql_client
  host: "config-center.enterprise.com"
  port: 3306
  database: "enterprise_config_management"
  username: "config_reader"
  password: "enterprise_secure_pass"
  
  # 查询配置（企业标准表结构）
  query_config:
    sql_template: |
      SELECT 
        cfg.configuration_identifier as config_key,
        cfg.configuration_payload as config_value,
        env.environment_code as environment,
        org.organization_code as tenant_id,
        cfg.priority_order as priority,
        cfg.created_by,
        cfg.last_updated
      FROM enterprise_configurations cfg
      JOIN deployment_environments env ON cfg.environment_id = env.id
      JOIN organizations org ON cfg.organization_id = org.id
      WHERE cfg.service_category = %(service_category)s
        AND cfg.configuration_type = %(config_type)s
        AND env.environment_code = %(environment)s
        AND org.organization_code = %(organization)s
        AND cfg.activation_status = %(status)s
      ORDER BY cfg.priority_order ASC, cfg.last_updated DESC
      LIMIT 1
    
    default_params:
      status: "ACTIVE"
      environment: "PRODUCTION"

# ==================== 示例2：微服务注册中心 ====================
microservice_registry:
  # PostgreSQL + JSON字段
  _target_: base.db.implementations.vs.pgvector.factory.create_pgvector_client
  host: "microservice-registry.k8s.cluster"
  port: 5432
  database: "service_mesh_registry"
  username: "service_mesh_reader"
  password: "k8s_secure_token"
  
  query_config:
    sql_template: |
      SELECT 
        service_metadata->>'service_name' as config_key,
        service_configuration::text as config_value,
        deployment_info->>'environment' as environment,
        service_metadata->>'namespace' as tenant_id,
        (service_metadata->>'priority')::int as priority
      FROM microservice_configurations
      WHERE service_metadata->>'service_type' = %(service_type)s
        AND service_metadata->>'service_name' = %(service_name)s
        AND deployment_info->>'environment' = %(environment)s
        AND service_metadata->>'namespace' = %(namespace)s
        AND service_status = %(status)s
      ORDER BY (service_metadata->>'priority')::int ASC
      LIMIT 1
    
    default_params:
      status: "RUNNING"
      environment: "prod"

# ==================== 示例3：云原生配置管理 ====================
cloud_native_config:
  _target_: base.db.implementations.rdb.universal.factory.create_mysql_client
  host: "cloud-config.multi-cloud.com"
  port: 3306
  database: "multi_cloud_configurations"
  username: "cloud_config_reader"
  password: "multi_cloud_secure_key"
  
  query_config:
    sql_template: |
      SELECT 
        CONCAT(cc.cloud_provider, '.', cc.region, '.', cc.service_name) as config_key,
        cc.configuration_data as config_value,
        cc.deployment_environment as environment,
        cc.tenant_workspace as tenant_id,
        cc.cost_priority as priority
      FROM cloud_configurations cc
      WHERE cc.cloud_provider = %(cloud_provider)s
        AND cc.region = %(region)s
        AND cc.service_category = %(service_category)s
        AND cc.service_name = %(service_name)s
        AND cc.deployment_environment = %(environment)s
        AND cc.tenant_workspace = %(workspace)s
        AND cc.configuration_status = %(status)s
      ORDER BY cc.cost_priority ASC, cc.performance_score DESC
      LIMIT 1
    
    default_params:
      status: "DEPLOYED"
      environment: "production"

# ==================== 示例4：遗留系统集成 ====================
legacy_mainframe_config:
  _target_: base.db.implementations.rdb.universal.factory.create_mysql_client
  host: "legacy-mainframe-db.company.com"
  port: 3306
  database: "LEGACY_SYS_CONFIG"
  username: "LEGACY_USER"
  password: "LEGACY_PASS_123"
  
  query_config:
    # 遗留系统的固定长度字段和大写命名
    sql_template: |
      SELECT 
        TRIM(CFG_ID) as config_key,
        TRIM(CFG_DATA) as config_value,
        TRIM(ENV_CODE) as environment,
        TRIM(DEPT_CODE) as tenant_id,
        CFG_PRIORITY as priority
      FROM LEGACY_CONFIG_TABLE
      WHERE UPPER(TRIM(SYS_TYPE)) = UPPER(%(system_type)s)
        AND UPPER(TRIM(CFG_TYPE)) = UPPER(%(config_type)s)
        AND UPPER(TRIM(ENV_CODE)) = UPPER(%(environment)s)
        AND UPPER(TRIM(DEPT_CODE)) = UPPER(%(department)s)
        AND CFG_STATUS = %(status)s
      ORDER BY CFG_PRIORITY ASC, LAST_UPD_DATE DESC
      LIMIT 1
    
    default_params:
      status: "A"  # 遗留系统用A表示Active
      environment: "PROD"

# ==================== 示例5：简单键值对表 ====================
simple_key_value_store:
  _target_: base.db.implementations.rdb.universal.factory.create_mysql_client
  host: "simple-config.company.com"
  port: 3306
  database: "app_configurations"
  username: "app_config_reader"
  password: "app_config_pass"
  
  query_config:
    sql_template: |
      SELECT 
        config_key,
        config_value,
        app_environment as environment,
        application_id as tenant_id
      FROM application_settings
      WHERE application_id = %(app_id)s
        AND config_key = %(config_key)s
        AND app_environment = %(environment)s
        AND is_enabled = %(enabled)s
    
    default_params:
      enabled: 1
      environment: "production"

# ==================== 示例6：多租户SaaS配置 ====================
saas_tenant_config:
  _target_: base.db.implementations.rdb.universal.factory.create_postgresql_client
  host: "saas-config.platform.com"
  port: 5432
  database: "saas_tenant_configurations"
  username: "saas_config_reader"
  password: "saas_secure_token"
  
  query_config:
    sql_template: |
      SELECT 
        tc.config_name as config_key,
        tc.config_data as config_value,
        tc.environment_tier as environment,
        t.tenant_uuid as tenant_id,
        tc.billing_priority as priority
      FROM tenant_configurations tc
      JOIN tenants t ON tc.tenant_id = t.id
      JOIN subscription_plans sp ON t.plan_id = sp.id
      WHERE t.tenant_uuid = %(tenant_uuid)s
        AND tc.config_category = %(config_category)s
        AND tc.config_name = %(config_name)s
        AND tc.environment_tier = %(environment)s
        AND sp.plan_type = %(plan_type)s
        AND tc.is_active = %(is_active)s
      ORDER BY tc.billing_priority ASC, tc.updated_at DESC
      LIMIT 1
    
    default_params:
      is_active: true
      environment: "production"
      plan_type: "enterprise"

# ==================== 使用示例 ====================
# 在Python代码中的使用方式：

# 示例1使用：
# params = {
#     "service_category": "DATABASE_SERVICE",
#     "config_type": "MYSQL_CONNECTION",
#     "organization": "HSBC_GLOBAL",
#     "environment": "PRODUCTION"
# }
# raw_config = await cfg.get_database_config(cfg.enterprise_config_center, params)

# 示例2使用：
# params = {
#     "service_type": "database_service",
#     "service_name": "mysql_connection_pool",
#     "namespace": "financial_services",
#     "environment": "prod"
# }
# raw_config = await cfg.get_database_config(cfg.microservice_registry, params)

# 示例3使用：
# params = {
#     "cloud_provider": "aws",
#     "region": "us-east-1",
#     "service_category": "database",
#     "service_name": "rds_mysql",
#     "workspace": "financial_prod"
# }
# raw_config = await cfg.get_database_config(cfg.cloud_native_config, params)

# 示例4使用：
# params = {
#     "system_type": "DATABASE",
#     "config_type": "MYSQL",
#     "department": "FINANCE",
#     "environment": "PROD"
# }
# raw_config = await cfg.get_database_config(cfg.legacy_mainframe_config, params)

# 示例5使用：
# params = {
#     "app_id": "financial_app",
#     "config_key": "database.mysql.connection",
#     "environment": "production"
# }
# raw_config = await cfg.get_database_config(cfg.simple_key_value_store, params)

# 示例6使用：
# params = {
#     "tenant_uuid": "hsbc-tenant-uuid-123",
#     "config_category": "database",
#     "config_name": "mysql_primary",
#     "environment": "production"
# }
# raw_config = await cfg.get_database_config(cfg.saas_tenant_config, params)

# ==================== 对应的转换函数示例 ====================
# 请参考 examples/transform_functions_examples.py 文件

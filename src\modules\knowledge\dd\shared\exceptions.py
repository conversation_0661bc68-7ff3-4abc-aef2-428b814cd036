"""
DD系统异常定义
"""

from typing import Optional, Dict, Any


class DDError(Exception):
    """DD系统基础异常"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class DDValidationError(DDError):
    """DD数据验证异常"""
    
    def __init__(
        self, 
        message: str, 
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        **kwargs
    ):
        super().__init__(message, error_code="DD_VALIDATION_ERROR", **kwargs)
        self.field_name = field_name
        self.field_value = field_value


class DDNotFoundError(DDError):
    """DD数据未找到异常"""
    
    def __init__(
        self, 
        message: str, 
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="DD_NOT_FOUND_ERROR", **kwargs)
        self.resource_type = resource_type
        self.resource_id = resource_id


class DDDatabaseError(DDError):
    """DD数据库操作异常"""
    
    def __init__(
        self, 
        message: str, 
        operation: Optional[str] = None,
        table_name: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="DD_DATABASE_ERROR", **kwargs)
        self.operation = operation
        self.table_name = table_name


class DDWorkflowError(DDError):
    """DD工作流异常"""
    
    def __init__(
        self, 
        message: str, 
        workflow_status: Optional[str] = None,
        submission_id: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="DD_WORKFLOW_ERROR", **kwargs)
        self.workflow_status = workflow_status
        self.submission_id = submission_id


class DDVectorizationError(DDError):
    """DD向量化异常"""
    
    def __init__(
        self, 
        message: str, 
        field_name: Optional[str] = None,
        content: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="DD_VECTORIZATION_ERROR", **kwargs)
        self.field_name = field_name
        self.content = content

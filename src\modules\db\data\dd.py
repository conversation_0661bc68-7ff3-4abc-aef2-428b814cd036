"""
G14.xlsx数据入库脚本
读取Excel文件并使用DD CRUD将数据插入到dd_submission_data表
"""

import asyncio
import pandas as pd
import sys
import os
from pathlib import Path
from datetime import datetime


# 添加项目根目录到Python路径
project_root = os.getcwd()
sys.path.insert(0, str(project_root))
from service import get_client
from modules.knowledge.dd.crud import DDCrud

from loguru import logger




async def load_g14_data_to_db():
    """读取G14.xlsx文件并将数据入库"""
    
    # Excel文件路径
    excel_path = r"D:\Code\shtlx\sync\hsbc-knowledge\src\modules\db\data\source\G53.xlsx"
    
    if not os.path.exists(excel_path):
        logger.error(f"Excel文件不存在: {excel_path}")
        return
    
    # 初始化客户端
    rdb_client=await get_client("database.rdbs.mysql")
    vdb_client=await get_client("database.vdbs.pgvector")
    embedding_client=await get_client("model.embeddings.moka-m3e-base")
    
    
    # 创建CRUD实例
    dd_crud = DDCrud(
        rdb_client=rdb_client,
        vdb_client=vdb_client,
        embedding_client=embedding_client
    )
    
    try:
        # 读取Excel文件
        logger.info(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)
        logger.info(f"Excel文件读取成功，共 {len(df)} 行数据，{len(df.columns)} 列")
        
                # 显示列名
        logger.info(f"列名: {list(df.columns)}")
        
        # 准备批量数据
        batch_data = []
        
        # 遍历每一行数据，准备批量插入的数据
        for index, row in df.iterrows():
            # 将pandas Series转换为字典，并处理NaN值
            submission_data = {}
            for col in df.columns:
                value = row[col]
                # 处理NaN值，转换为None或者空字符串
                if pd.isna(value):
                    submission_data[col.lower()] = None  # 字段名转换为小写
                else:
                    submission_data[col.lower()] = str(value)  # 转换为字符串
            
            batch_data.append(submission_data)
        
        logger.info(f"准备批量入库 {len(batch_data)} 条数据...")
        
        # 固定的knowledge_id
        knowledge_id = "80788d5f-3979-41f4-a570-7eebfb5870bb"
        
        # 批量创建填报数据
        submission_pks, vector_ids = await dd_crud.create_submission_data(batch_data, knowledge_id=knowledge_id)
        
        logger.info(f"✅ 批量入库成功! 插入了 {len(submission_pks)} 条记录，向量数={len(vector_ids)}")
        logger.info(f"插入的主键ID: {submission_pks}")
        
    except Exception as e:
        logger.error(f"数据入库过程中发生错误: {e}")
        
    finally:
        # 关闭数据库连接

        pass


async def main():
    """主函数"""
    logger.info("开始G14数据入库任务...")
    await load_g14_data_to_db()
    logger.info("G14数据入库任务完成!")


if __name__ == "__main__":
    asyncio.run(main())

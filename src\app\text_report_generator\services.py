"""
文本报告生成器核心服务

实现了三种选择参考报告路径的功能
"""

import asyncio
from typing import List, Dict, Optional, Any
import re
import logging
from collections import defaultdict
import json
from .models import (
    IndicatorInfo,
    ChapterInfo,
    HistoricalReportQuery,
    HistoricalReportQueryResult,
    ReferenceDocumentUpload,
    ReportConfirmationRequest,
)
from modules.knowledge.doc.operations.chunk_ops import ChunkOperation
from modules.knowledge.doc.operations.document_ops import DocumentOperation
from .chapter_utils import get_chapter_by_doc_id


def parse_markdown_to_chapters(content: str) -> List[ChapterInfo]:
    """
    TODO 临时实现，后续需要引用其他功能
    解析Markdown内容为章节，按一级标题(##)进行分割，并保留第一个章节前的内容
    """
    lines = content.split("\n")
    chapters = []
    current_title = ""
    current_content = []
    first_part = []

    chapter_cnt = 1
    for line in lines:
        if line.startswith("## "):
            if current_title:
                chapters.append(
                    ChapterInfo(
                        chapter_name=current_title.strip(),
                        chapter_content="\n".join(current_content).strip(),
                        chapter_summary="",
                        chapter_referenced_index_id=[],
                        chapter_index=chapter_cnt,
                    )
                )
                chapter_cnt += 1
                current_content = [current_title]
            elif current_content:  # 还没有遇到章节标题，current_content为前置内容
                first_part = current_content.copy()
                chapter_cnt += 1
                current_content = []
            current_title = line[2:].strip()
        else:
            current_content.append(line)
    if current_title:
        chapters.append(
            ChapterInfo(
                chapter_name=current_title.strip(),
                chapter_content="\n".join(current_content).strip(),
                chapter_summary="",
                chapter_referenced_index_id=[],
                chapter_index=chapter_cnt,
            )
        )
    elif current_content:  # 没有任何章节标题，全部作为前置内容
        first_part = current_content.copy()

    result = []
    if first_part and any(line.strip() for line in first_part):
        result.append(
            ChapterInfo(
                chapter_name="文档前言",
                chapter_content="\n".join(first_part).strip(),
                chapter_summary="",
                chapter_referenced_index_id=[],
                chapter_index=1,
            )
        )
    result.extend(chapters)
    if not result:
        result = [
            ChapterInfo(
                chapter_name="文档内容",
                chapter_content=content.strip(),
                chapter_summary="",
                chapter_referenced_index_id=[],
                chapter_index=1,
            )
        ]
    return result


def generate_summary(content: str, max_length: int = 100) -> str:
    content = content.strip()
    if len(content) <= max_length:
        return content
    return content[:max_length] + "..."


class HistoricalReportService:
    """
    历史报告查询服务
    基于文档名称的模糊查询，返回文档id列表，前端需要根据id获取文档信息
    """

    def __init__(
        self, document_ops: DocumentOperation, knowledge_id: Optional[str] = None
    ):
        self.document_ops = document_ops
        self.knowledge_id = knowledge_id or "text_report_generator"

    async def query_by_name(
        self, query: HistoricalReportQuery
    ) -> List[HistoricalReportQueryResult]:
        """
        根据报告名称查询历史报告，返回文档信息列表，不含各章节信息
        """
        try:
            results = []
            docs = await self.document_ops.search_documents(
                knowledge_id=query.knowledge_id or self.knowledge_id,
                doc_name_pattern=query.report_name,
                limit=query.limit,
                doc_name_search_type="like",
                offset=query.offset,
            )
            if not docs.data:
                return []
            for doc in docs.data:
                metadata = json.loads(doc.get("metadata", "{}"))
                doc_series_name = metadata.get("series_name", "")
                doc_report_name = doc.get("doc_name", "")
                doc_timestamp = doc.get("created_time", "")
                results.append(
                    HistoricalReportQueryResult(
                        doc_id=doc.get("doc_id", ""),
                        doc_series_name=doc_series_name,
                        doc_report_name=doc_report_name,
                        doc_timestamp=doc_timestamp,
                    )
                )
            return results
        except Exception as e:
            logging.error(f"历史报告查询失败: {e}")
            return []


class SemanticSearchService:
    """
    语义推荐服务
    基于向量数据库的近似匹配，根据报表名称和套系名称，进行近似匹配
    """

    def __init__(self, chunk_ops: ChunkOperation, knowledge_id: Optional[str] = None):
        self.chunk_ops = chunk_ops
        self.knowledge_id = knowledge_id or "text_report_generator"

    async def search_reports(
        self, series_name: str, report_name: str
    ) -> List[ChapterInfo]:
        """
        基于向量数据库的近似匹配，直接返回匹配度最高的文档，这是前端页面的默认显示
        """
        try:
            name_chunks = await self.chunk_ops.search_similar_chunks(
                query_text=report_name,
                knowledge_id=self.knowledge_id,
                doc_id=None,
                info_types=["document_name"],
                top_k=10,
                similarity_threshold=0.3,
            )
            series_chunks = await self.chunk_ops.search_similar_chunks(
                query_text=series_name,
                knowledge_id=self.knowledge_id,
                doc_id=None,
                info_types=["document_series_name"],
                top_k=10,
                similarity_threshold=0.3,
            )

            # chunk_id相同的合并，得分为两者相加，同时保留doc_id信息（以第一个出现的为准）
            chunks_dict = {}
            for chunk in name_chunks + series_chunks:
                chunk_id = chunk["chunk_id"]
                similarity_score = chunk["similarity_score"]
                doc_id = chunk.get("doc_id", "")
                if chunk_id not in chunks_dict:
                    chunks_dict[chunk_id] = {
                        "chunk_id": chunk_id,
                        "similarity_score": similarity_score,
                        "doc_id": doc_id,
                    }
                else:
                    chunks_dict[chunk_id]["similarity_score"] += similarity_score
            chunks = list(chunks_dict.values())
            chunks.sort(key=lambda x: x["similarity_score"], reverse=True)

            if not chunks:
                return []
            target_chunk = chunks[0]
            doc_id = target_chunk["doc_id"]
            return await get_chapter_by_doc_id(self.chunk_ops, doc_id)

        except Exception as e:
            logging.error(f"语义搜索失败: {e}")
            return []


class DocumentUploadService:
    """
    文档上传处理服务，耗时长
    将上传的文档解析为章节，并匹配指标，返回章节信息列表
    """

    def __init__(self, llm_client, knowledge_id: Optional[str] = None):
        self.llm_client = llm_client
        self.knowledge_id = knowledge_id or "text_report_generator"
        # 创建信号量，限制并发数为8
        self.semaphore = asyncio.Semaphore(8)

    async def process_uploaded_document(
        self, upload: ReferenceDocumentUpload, indicators: List[IndicatorInfo]
    ) -> List[ChapterInfo]:
        try:
            chapters = parse_markdown_to_chapters(upload.content)
            
            # 创建处理章节的任务列表，使用类级别的信号量
            tasks = [
                self._process_single_chapter(chapter, indicators, idx)
                for idx, chapter in enumerate(chapters, 1)
            ]
            
            # 并发执行所有章节处理任务
            processed_chapters = await asyncio.gather(*tasks)
            return processed_chapters
        except Exception as e:
            logging.error(f"文档处理失败: {e}")
            return []

    async def _process_single_chapter(
        self, chapter, indicators, chapter_index
    ) -> ChapterInfo:
        """处理单个章节，使用信号量控制并发"""
        # 并发执行章节摘要生成和指标匹配
        summary_task = self._get_chapter_summary(chapter.chapter_content)
        indicators_task = self._match_indicators_to_chapter(
            chapter.chapter_content, indicators
        )
        
        # 等待两个任务完成
        summary, matched_indicators = await asyncio.gather(
            summary_task, indicators_task
        )
        
        # 构造章节信息
        return ChapterInfo(
            chapter_name=chapter.chapter_name,
            chapter_summary=summary,
            chapter_content=chapter.chapter_content,
            chapter_referenced_index_id=matched_indicators,
            chapter_index=chapter_index,
        )

    async def _get_chapter_summary(self, content: str) -> str:
        try:
            from base.model_serve.model_runtime.entities import PromptMessage

            # 处理8000token上限的问题
            if len(content) <= 6000:
                prompt = f"请为以下内容生成简洁的摘要（200字以内）：\n\n{content}"
                messages = [PromptMessage(role="user", content=prompt)]
                result = await self.llm_client.ainvoke(
                    prompt_messages=messages, stream=False
                )
            else:
                content_pre = content[:2000]
                content_mid = content[
                    len(content) // 2 - 1000 : len(content) // 2 + 1000
                ]
                content_post = content[-2000:]
                prompt = f"请为以下内容生成简洁的摘要（200字以内）：\n\n内容前半部分：{content_pre}\n\n内容中间部分：{content_mid}\n\n内容后半部分：{content_post}"
                messages = [PromptMessage(role="user", content=prompt)]
                result = await self.llm_client.ainvoke(
                    prompt_messages=messages, stream=False
                )
            return result.message.content.strip()
        except Exception as e:
            logging.error(f"生成章节摘要失败: {e}")
            return generate_summary(content, 100)

    async def _match_indicators_to_chapter(
        self, chapter_content: str, indicators: List[IndicatorInfo]
    ) -> List[int]:
        try:
            # 防止指标过长，只包含名称
            indicators_text = [
                f"ID:{indicator.id} - {indicator.id_name}" for indicator in indicators
            ]
            content_list = [
                chapter_content[i : i + 4000]
                for i in range(0, len(chapter_content), 4000)
            ]
            # 拆分indicator_text，每段不超过4000字符
            indicator_list = []
            now_len = 0
            now_indicator = []
            for indicators in indicators_text:
                if now_len + len(indicators) > 4000:
                    indicator_list.append(now_indicator)
                    now_len = 0
                    now_indicator = [indicators]
                else:
                    now_len += len(indicators)
                    now_indicator.append(indicators)
            if now_indicator:
                indicator_list.append(now_indicator)

            # 创建处理任务列表
            tasks = []
            for content in content_list:
                for indicators in indicator_list:
                    task = self._process_indicator_batch_with_semaphore(
                        content, indicators
                    )
                    tasks.append(task)
            
            # 并发执行所有任务
            results = await asyncio.gather(*tasks)
            
            # 合并所有匹配的ID
            all_matched_ids = []
            for matched_ids in results:
                all_matched_ids.extend(matched_ids)
            return list(set(all_matched_ids))

        except Exception as e:
            logging.error(f"指标匹配失败: {e}")
            return []

    async def _process_indicator_batch(
        self,
        chapter_content: str,
        indicators_batch: List[str],
        llm_client,
    ) -> List[int]:
        """
        处理一批指标的匹配，返回匹配的指标ID列表
        """
        try:
            from base.model_serve.model_runtime.entities import (
                PromptMessage,
                PromptMessageRole,
            )

            prompt = f"""
            任务目标：
            请分析章节内容，判断以下哪些指标与该章节内容相关。
            只返回相关的指标ID列表，格式如：[1,3,5]。
            只有当指标与章节内容相关，且存在该指标的值时，才返回该指标ID。否则返回空列表。
                        
            案例1：
            传入文本："本月个人车贷总金额为10000元，较上月上涨10%。"
            传入指标：[ID:1 - 个人车贷, ID:2 - 个人房贷, ID:3 - 个人消费贷]
            返回：[1]
            案例2：
            传入文本："# 个人房贷业务介绍"
            传入指标：[ID:1 - 个人车贷, ID:2 - 个人房贷, ID:3 - 个人消费贷]
            返回：[]
            
            输入：
            章节内容：
            {chapter_content}
            可选指标：
            {chr(10).join(indicators_batch)}
            相关指标ID：
            """
            messages = [PromptMessage(role=PromptMessageRole.USER, content=prompt)]
            result = await llm_client.ainvoke(prompt_messages=messages, stream=False)
            response = result.message.content.strip()
            matched_ids = []
            # 指标格式为 f"ID:{indicator.id} - {indicator.id_name}" ，使用re提取ID

            batch_ids = []
            for ind in indicators_batch:
                match = re.match(r"ID:(.*?)\s*-\s*", ind)
                if match:
                    batch_ids.append(match.group(1))

            for match in re.findall(
                r"\d+", response
            ):  # TODO 需要确定前端返回的id格式，以修改匹配规则
                try:
                    indicator_id = match
                    if indicator_id in batch_ids:
                        matched_ids.append(indicator_id)
                except ValueError:
                    continue
            return matched_ids
        except Exception as e:
            logging.error(f"批处理指标匹配失败: {e}")
            return []
    async def _process_indicator_batch_with_semaphore(
        self, chapter_content: str, indicators_batch: List[str]
    ) -> List[int]:
        """使用信号量控制并发处理指标批次"""
        async with self.semaphore:
            return await self._process_indicator_batch(
                chapter_content, indicators_batch, self.llm_client
            )


class ReportGenerationService:
    """报告生成服务"""

    def __init__(self, llm_client, document_ops: DocumentOperation,chunk_ops: ChunkOperation, knowledge_id=None):
        self.llm_client = llm_client
        self.document_ops = document_ops
        self.chunk_ops = chunk_ops
        self.knowledge_id = knowledge_id or "text_report_generator"

    async def generate_chapter_content(
        self, chapter: ChapterInfo, indicators: List[IndicatorInfo]
    ) -> str:
        try:
            llm_client = self.llm_client
            indicator_map = {ind.id: ind for ind in indicators}
            target_indicators = [
                indicator_map[id]
                for id in chapter.chapter_referenced_index_id
                if id in indicator_map
            ]
            if not target_indicators:
                return chapter.chapter_content
            indicators_desc = "\n".join(
                [f"{ind.id_name}: {ind.id_value}" for ind in target_indicators]
            )
            from base.model_serve.model_runtime.entities import PromptMessage

            prompt = f"""
            请根据提供的指标信息，更新章节内容中的数据。
            保持原有文本结构和描述方式，只更新数值部分，确保返回的是一个合法的markdown。
            原始内容：
            {chapter.chapter_content}
            需要填入的指标列表：
            {indicators_desc}
            更新后的内容：
            """
            messages = [PromptMessage(role="user", content=prompt)]
            result = await llm_client.ainvoke(prompt_messages=messages, stream=False)
            return result.message.content.strip()
        except Exception as e:
            logging.error(f"章节内容生成失败: {e}")
            return chapter.chapter_content

    async def archive_document(self, archive: ReportConfirmationRequest) -> bool:
        """
        将确认后的报告归档到知识库，包含文档信息，章节信息
        """
        try:
            from modules.knowledge.doc.entities.api_models import DocumentCreate

            doc_create = DocumentCreate(
                knowledge_id=self.knowledge_id,
                doc_name=archive.report_name,
                doc_type=None,
                author=None,
                vector_similarity_weight=None,
                similarity_threshold=None,
                parse_type=None,
                status=None,
                parse_end_time=None,
                parse_message=None,
                doc_format=None,
                location=None,
                metadata=json.dumps({"series_name": archive.series_name}),
                created_time=archive.timestamp,
                updated_time=archive.timestamp,
                is_active=True,
            )
            doc_id = await self.document_ops.create_document(doc_create)

            # 各章节信息入库
            chapter_cnt = 0
            for chapter in archive.chapters:
                chunk_infos = [
                    {"info_type": "chapter_name", "info_value": chapter.chapter_name},
                    {
                        "info_type": "chapter_content",
                        "info_value": chapter.chapter_content,
                    },
                    {
                        "info_type": "chapter_summary",
                        "info_value": chapter.chapter_summary,
                    },
                    {
                        "info_type": "chapter_referenced_index_id",
                        "info_value": json.dumps(chapter.chapter_referenced_index_id),
                    },
                    {
                        "info_type": "chapter_index",
                        "info_value": str(chapter.chapter_index),
                    },
                ]
                chunk_id = await self.chunk_ops.create_chunk_with_info(
                    doc_id=doc_id, chunk_infos=chunk_infos
                )
                chapter_cnt += 1

            # 对于文档标题，由于需要向量化，额外创建根节点，存储chunk_info
            chunk_id = await self.chunk_ops.create_chunk_with_info_and_vector(
                knowledge_id=self.knowledge_id,
                doc_id=doc_id,
                chapter_layer="root",
                chunk_infos=[
                    {
                        "info_type": "document_name",
                        "info_value": archive.report_name,
                    },
                    {
                        "info_type": "document_series_name",
                        "info_value": archive.series_name,
                    },
                ],
            )
            return bool(doc_id)
        except Exception as e:
            logging.error(f"文档归档失败: {e}")
            return False


class TextReportGenerator:
    """文本报告生成器主类"""

    def __init__(
        self,
        rdb_client,
        vdb_client,
        embedding_client,
        llm_client,
        knowledge_id: Optional[str] = None,
    ):
        # 直接初始化ops
        self.document_ops = DocumentOperation(rdb_client=rdb_client)
        self.chunk_ops = ChunkOperation(
            rdb_client=rdb_client,
            vdb_client=vdb_client,
            embedding_client=embedding_client,
        )

        self.historical_service = HistoricalReportService(
            self.document_ops, knowledge_id
        )
        self.semantic_service = SemanticSearchService(self.chunk_ops, knowledge_id)
        self.upload_service = DocumentUploadService(llm_client)
        self.generation_service = ReportGenerationService(llm_client, self.document_ops, self.chunk_ops, knowledge_id)

    async def get_historical_report_by_name(
        self, query: HistoricalReportQuery
    ) -> List[HistoricalReportQueryResult]:
        return await self.historical_service.query_by_name(query)

    async def get_reference_chapters(
        self,
        method: str,
        params: Dict[str, Any],
        indicators: List[IndicatorInfo] | None = None,
    ) -> List[ChapterInfo]:
        if method == "semantic":
            return await self.semantic_service.search_reports(
                params.get("series_name", ""), params.get("report_name", "")
            )
        elif method == "upload":
            upload = ReferenceDocumentUpload(**params)
            return await self.upload_service.process_uploaded_document(
                upload, indicators
            )
        else:
            raise ValueError(f"不支持的参考报告选择方法: {method}")

    async def generate_report_content(
        self, chapters: List[ChapterInfo], indicators: List[IndicatorInfo]
    ) -> List[ChapterInfo]:
        generated_chapters = []
        for chapter in chapters:
            new_content = await self.generation_service.generate_chapter_content(
                chapter, indicators
            )
            new_chapter = ChapterInfo(
                chapter_name=chapter.chapter_name,
                chapter_summary=chapter.chapter_summary,
                chapter_content=new_content,
                chapter_referenced_index_id=chapter.chapter_referenced_index_id,
                chapter_index=chapter.chapter_index,
            )
            generated_chapters.append(new_chapter)
        return generated_chapters

    async def get_chapter_by_doc_id(self, doc_id: str) -> List[ChapterInfo]:
        return await get_chapter_by_doc_id(self.chunk_ops, doc_id)

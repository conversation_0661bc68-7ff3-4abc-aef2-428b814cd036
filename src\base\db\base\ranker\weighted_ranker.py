"""
加权平均排序器

实现加权平均算法，根据指定权重融合多个搜索结果。

参考Milvus的WeightedRanker实现。

作者: HSBC Knowledge Team
日期: 2025-01-14
"""

from typing import List, Dict, Any, Optional, Union
import logging
from collections import defaultdict

from .base_ranker import BaseRanker

logger = logging.getLogger(__name__)


class WeightedRanker(BaseRanker):
    """
    加权平均排序器
    
    使用指定权重对多个搜索结果进行加权平均融合。
    每个搜索结果的分数会被归一化到[0,1]范围，然后按权重计算加权平均。
    """
    
    def __init__(self, weights: Union[List[float], Dict[int, float]]):
        """
        初始化加权排序器
        
        Args:
            weights: 权重配置，可以是：
                    - List[float]: 按搜索顺序的权重列表 [0.6, 0.4]
                    - Dict[int, float]: 搜索索引到权重的映射 {0: 0.6, 1: 0.4}
        """
        super().__init__(f"WeightedRanker(weights={weights})")
        
        if isinstance(weights, list):
            if not weights:
                raise ValueError("权重列表不能为空")
            if any(w < 0 for w in weights):
                raise ValueError("权重不能为负数")
            self.weights = {i: w for i, w in enumerate(weights)}
        elif isinstance(weights, dict):
            if not weights:
                raise ValueError("权重字典不能为空")
            if any(w < 0 for w in weights.values()):
                raise ValueError("权重不能为负数")
            self.weights = weights.copy()
        else:
            raise ValueError("权重必须是列表或字典类型")
        
        # 归一化权重
        total_weight = sum(self.weights.values())
        if total_weight == 0:
            raise ValueError("权重总和不能为0")
        
        self.normalized_weights = {k: v / total_weight for k, v in self.weights.items()}
        
        logger.debug(f"初始化加权排序器，原始权重={self.weights}, "
                    f"归一化权重={self.normalized_weights}")
    
    def rank(self, 
             results_list: List[List[Dict[str, Any]]], 
             top_k: Optional[int] = None,
             **kwargs) -> List[Dict[str, Any]]:
        """
        使用加权平均算法对多个搜索结果进行重排序
        
        Args:
            results_list: 多个搜索结果列表
            top_k: 最终返回的结果数量
            **kwargs: 额外参数（加权排序不使用）
            
        Returns:
            加权平均重排序后的结果列表
        """
        # 验证输入
        self.validate_results(results_list)
        
        if not results_list:
            return []
        
        logger.debug(f"开始加权平均排序，输入{len(results_list)}个搜索结果列表")
        
        # 验证权重数量
        if len(results_list) > len(self.normalized_weights):
            logger.warning(f"搜索结果数量({len(results_list)})超过权重数量({len(self.normalized_weights)})")
            # 为缺失的搜索分配默认权重
            missing_weight = 0.1 / (len(results_list) - len(self.normalized_weights))
            for i in range(len(self.normalized_weights), len(results_list)):
                self.normalized_weights[i] = missing_weight
        
        # 收集所有唯一的结果项
        all_items = {}  # id -> 完整的result dict
        weighted_scores = defaultdict(float)  # id -> 加权累积分数
        score_details = defaultdict(list)  # id -> [(search_index, normalized_score, weight, contribution), ...]
        
        # 遍历每个搜索结果列表
        for search_index, results in enumerate(results_list):
            if search_index not in self.normalized_weights:
                logger.warning(f"搜索{search_index}没有对应的权重，跳过")
                continue
                
            weight = self.normalized_weights[search_index]
            logger.debug(f"处理第{search_index + 1}个搜索结果，包含{len(results)}个项目，权重={weight}")
            
            # 归一化当前搜索的分数
            normalized_results = self.normalize_scores(results.copy(), 'distance', reverse=True)
            
            for result in normalized_results:
                item_id = result['id']
                normalized_score = result.get('normalized_score', 0.0)
                original_score = self.get_score(result)
                
                # 计算加权贡献
                contribution = normalized_score * weight
                weighted_scores[item_id] += contribution
                
                # 记录分数详情
                score_details[item_id].append((search_index, normalized_score, weight, contribution))
                
                # 保存完整的结果项（使用第一次出现的版本）
                if item_id not in all_items:
                    all_items[item_id] = result.copy()
                
                logger.debug(f"项目{item_id}: 搜索{search_index + 1}, "
                           f"原始分数{original_score:.4f}, 归一化分数{normalized_score:.4f}, "
                           f"权重{weight:.4f}, 贡献{contribution:.4f}")
        
        # 创建最终结果
        final_results = []
        for item_id, total_weighted_score in weighted_scores.items():
            result = all_items[item_id].copy()
            
            # 添加加权分数和详细信息
            result['rank_score'] = total_weighted_score
            # 确保score在0-1范围内，distance也在合理范围内
            normalized_score = max(0.0, min(1.0, total_weighted_score))
            result['score'] = normalized_score
            result['distance'] = 1.0 - normalized_score  # 转换为distance格式（越小越好）
            
            # 添加权重详情到entity中
            if 'entity' not in result:
                result['entity'] = {}
            
            result['entity']['weighted_details'] = {
                'total_weighted_score': total_weighted_score,
                'weights_used': dict(self.normalized_weights),
                'score_details': [
                    {
                        'search_index': search_idx,
                        'normalized_score': norm_score,
                        'weight': weight,
                        'contribution': contrib
                    }
                    for search_idx, norm_score, weight, contrib in score_details[item_id]
                ],
                'fusion_method': 'weighted_average'
            }
            
            final_results.append(result)
        
        # 按加权分数降序排序
        final_results.sort(key=lambda x: x['rank_score'], reverse=True)
        
        # 应用top_k限制
        if top_k is not None and top_k > 0:
            final_results = final_results[:top_k]
        
        logger.info(f"加权平均排序完成，返回{len(final_results)}个结果")
        
        # 添加最终排名信息
        for final_rank, result in enumerate(final_results, 1):
            result['entity']['weighted_details']['final_rank'] = final_rank
        
        return final_results
    
    def explain_ranking(self, results: List[Dict[str, Any]]) -> str:
        """
        解释加权平均排序结果
        
        Args:
            results: 加权平均排序后的结果
            
        Returns:
            排序解释文本
        """
        explanation = [f"加权平均排序解释 (权重={self.normalized_weights}):\n"]
        
        for i, result in enumerate(results[:5], 1):  # 只解释前5个
            weighted_details = result.get('entity', {}).get('weighted_details', {})
            total_score = weighted_details.get('total_weighted_score', 0)
            
            explanation.append(f"{i}. ID={result['id']}, 总加权分数={total_score:.4f}")
            
            for detail in weighted_details.get('score_details', []):
                search_idx = detail['search_index']
                norm_score = detail['normalized_score']
                weight = detail['weight']
                contribution = detail['contribution']
                explanation.append(f"   搜索{search_idx + 1}: 归一化分数{norm_score:.4f} × 权重{weight:.4f} = {contribution:.4f}")
            
            explanation.append("")
        
        return "\n".join(explanation)

"""
向量存储模块 - 基于新service层的通用向量入库方案

本模块提供：
1. 基于新service层的统一向量存储接口
2. 支持批量和单条记录的向量入库
3. 自动处理向量化和数据库操作
4. 支持更新和删除操作
5. 集成update_time字段管理

设计原则：
- 使用新的service层进行数据库客户端管理
- 配置驱动，支持不同表结构和业务场景
- 提供简洁统一的接口
- 支持事务和错误恢复
"""

from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import asyncio
from loguru import logger

# 使用新的service层基础设施
from service.client.factory import get_client


@dataclass
class VectorStorageConfig:
    """向量存储配置"""
    # 数据库配置
    pgvector_config_name: str = "pgvector_metadata"  # DynamicProvider中的配置名
    embedding_model_name: str = "default"  # DynamicProvider中的embedding模型名

    # 表配置
    table_name: str = "md_table_embeddings"
    vector_field: str = "embedding"
    vector_dimension: int = 768
    
    # 关联字段配置
    knowledge_id_field: str = "knowledge_id"
    content_type_field: str = "content_type"
    
    # 时间字段配置
    create_time_field: str = "create_time"
    update_time_field: str = "update_time"
    
    # 批量处理配置
    batch_size: int = 100
    max_retries: int = 3


@dataclass
class VectorRecord:
    """向量记录数据结构"""
    # 必需字段
    knowledge_id: str
    content_type: str
    content_text: str
    
    # 关联ID字段（根据不同表类型会有不同的关联字段）
    relation_ids: Dict[str, Any] = field(default_factory=dict)
    
    # 可选字段
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 向量（可选，如果不提供会自动生成）
    embedding: Optional[List[float]] = None


@dataclass
class VectorStorageResult:
    """向量存储结果"""
    success: bool
    message: str
    inserted_count: int = 0
    updated_count: int = 0
    failed_count: int = 0
    failed_records: List[Dict[str, Any]] = field(default_factory=list)
    execution_time: float = 0.0


class VectorStorageService:
    """向量存储服务"""
    
    def __init__(self, config: Optional[VectorStorageConfig] = None):
        """
        初始化向量存储服务

        Args:
            config: 向量存储配置
        """
        self.config = config or VectorStorageConfig()
        
    async def get_pgvector_client(self, knowledge_id: str):
        """获取PGVector客户端"""
        try:
            # 使用新的service层获取向量数据库客户端
            client = get_client(
                client_type="database.vdbs.pgvector",
                knowledge_id=knowledge_id
            )

            return client
        except Exception as e:
            logger.error(f"获取PGVector客户端失败: {e}")
            raise
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        生成文本向量

        Args:
            texts: 文本列表

        Returns:
            向量列表
        """
        try:
            logger.debug(f"生成 {len(texts)} 个文本的向量")

            # 使用新的service层获取embedding服务
            embedding_client = get_client(
                client_type="model.embeddings.moka-m3e-base"
            )

            # 使用embedding服务生成向量
            embedding_result = await embedding_client.ainvoke(
                texts=texts,
                user="vector_storage_system"
            )
            
            if not embedding_result.embeddings:
                raise ValueError("向量生成失败：返回空结果")
            
            return embedding_result.embeddings
            
        except Exception as e:
            logger.error(f"向量生成失败: {e}")
            raise
    
    async def insert_vectors(
        self,
        records: List[VectorRecord],
        upsert: bool = False
    ) -> VectorStorageResult:
        """
        批量插入向量记录
        
        Args:
            records: 向量记录列表
            upsert: 是否使用upsert模式（更新已存在的记录）
            
        Returns:
            VectorStorageResult: 存储结果
        """
        start_time = datetime.now()
        result = VectorStorageResult(success=False, message="")
        
        try:
            if not records:
                result.success = True
                result.message = "没有记录需要插入"
                return result
            
            # 获取客户端（从第一条记录中获取knowledge_id）
            knowledge_id = records[0].knowledge_id
            client = await self.get_pgvector_client(knowledge_id)
            
            # 准备需要生成向量的文本
            texts_to_embed = []
            record_indices = []
            
            for i, record in enumerate(records):
                if record.embedding is None:
                    texts_to_embed.append(record.content_text)
                    record_indices.append(i)
            
            # 批量生成向量
            if texts_to_embed:
                embeddings = await self.generate_embeddings(texts_to_embed)
                for i, embedding in enumerate(embeddings):
                    records[record_indices[i]].embedding = embedding
            
            # 分批处理
            inserted_count = 0
            updated_count = 0
            failed_count = 0
            failed_records = []
            
            for i in range(0, len(records), self.config.batch_size):
                batch = records[i:i + self.config.batch_size]
                
                try:
                    batch_result = await self._insert_batch(client, batch, upsert)
                    inserted_count += batch_result["inserted"]
                    updated_count += batch_result["updated"]
                    
                except Exception as e:
                    logger.error(f"批次 {i//self.config.batch_size + 1} 插入失败: {e}")
                    failed_count += len(batch)
                    failed_records.extend([{
                        "record": record.__dict__,
                        "error": str(e)
                    } for record in batch])
            
            # 设置结果
            result.success = failed_count == 0
            result.inserted_count = inserted_count
            result.updated_count = updated_count
            result.failed_count = failed_count
            result.failed_records = failed_records
            result.message = f"插入完成: 成功 {inserted_count + updated_count}, 失败 {failed_count}"
            
        except Exception as e:
            logger.error(f"向量插入过程失败: {e}")
            result.message = f"插入失败: {str(e)}"
            result.failed_count = len(records)
            
        finally:
            result.execution_time = (datetime.now() - start_time).total_seconds()
            
        return result
    
    async def _insert_batch(
        self,
        client,
        batch: List[VectorRecord],
        upsert: bool
    ) -> Dict[str, int]:
        """
        插入单个批次的记录

        Args:
            client: 数据库客户端
            batch: 记录批次
            upsert: 是否使用upsert模式

        Returns:
            Dict包含inserted和updated的数量
        """
        # 将VectorRecord转换为PGVectorClient期望的字典格式
        data_list = []
        for record in batch:
            # 构建通用的数据字典，支持不同类型的向量表
            data_dict = {
                "embedding": record.embedding,
                "knowledge_id": record.knowledge_id,
                "content_type": record.content_type
            }

            # 添加关联ID字段（从relation_ids中提取）
            # 这些字段根据不同的向量表类型会有所不同：
            # - md_table_embeddings: source_type, table_id
            # - md_column_embeddings: source_type, table_id, column_id
            # - md_code_value_embeddings: code_set_id, code_value_id
            if record.relation_ids:
                for key, value in record.relation_ids.items():
                    data_dict[key] = value

            # 注意：不添加 metadata 字段到向量表中
            # 向量表只包含核心字段：embedding, knowledge_id, content_type 和 relation_ids
            # metadata 字段仅用于业务逻辑，不存储到向量数据库

            data_list.append(data_dict)

        try:
            if upsert:
                # 根据表类型动态确定冲突检测字段
                conflict_columns = self._get_conflict_columns()

                # 使用异步upsert方法
                success = await client.async_upsert(
                    collection_name=self.config.table_name,
                    data=data_list,
                    conflict_columns=conflict_columns,
                    partition_name=None  # 使用自动分区
                )
            else:
                # 使用异步insert方法
                success = await client.async_insert(
                    collection_name=self.config.table_name,
                    data=data_list,
                    partition_name=None  # 使用自动分区，让PostgreSQL自动路由
                )

            if success:
                return {"inserted": len(batch), "updated": 0}
            else:
                raise Exception("PGVectorClient async operation failed")

        except Exception as e:
            logger.error(f"Insert batch failed: {str(e)}")
            raise

    def _get_conflict_columns(self) -> List[str]:
        """
        根据表类型动态确定冲突检测字段

        Returns:
            冲突检测字段列表
        """
        table_name = self.config.table_name

        # 根据表名确定冲突检测字段（必须包含所有分区键）
        if table_name == "md_table_embeddings":
            return ['knowledge_id', 'source_type', 'db_id', 'table_id', 'content_type']
        elif table_name == "md_column_embeddings":
            return ['knowledge_id', 'source_type', 'table_id', 'column_id', 'content_type']
        elif table_name == "md_code_embeddings":
            return ['knowledge_id', 'code_set_id', 'code_value_id', 'content_type']
        elif table_name == "dd_embeddings":
            return ['knowledge_id', 'file_id', 'row_id', 'field_id']
        elif table_name == "doc_embeddings":
            return ['knowledge_id', 'doc_id', 'chunk_id', 'info_type']
        else:
            # 默认冲突检测字段
            logger.warning(f"未知的表类型: {table_name}, 使用默认冲突检测字段")
            return ['knowledge_id', 'content_type']
    
    def _build_insert_sql(self, batch: List[VectorRecord]) -> str:
        """构建插入SQL语句"""
        # 获取第一条记录的字段来构建SQL
        if not batch:
            raise ValueError("批次为空")
        
        first_record = batch[0]
        
        # 基础字段
        fields = [
            self.config.vector_field,
            self.config.knowledge_id_field,
            self.config.content_type_field,
            self.config.create_time_field,
            self.config.update_time_field
        ]
        
        # 添加关联字段
        fields.extend(first_record.relation_ids.keys())
        
        # 添加元数据字段
        fields.extend(first_record.metadata.keys())
        
        placeholders = ", ".join(["%s"] * len(fields))
        fields_str = ", ".join(fields)
        
        sql = f"""
        INSERT INTO {self.config.table_name} ({fields_str})
        VALUES ({placeholders})
        """
        
        return sql
    
    def _build_upsert_sql(self, batch: List[VectorRecord]) -> str:
        """构建upsert SQL语句"""
        # PostgreSQL的ON CONFLICT语法
        insert_sql = self._build_insert_sql(batch)
        
        # 假设使用knowledge_id和content_type作为冲突检测
        conflict_fields = [self.config.knowledge_id_field, self.config.content_type_field]
        
        # 需要更新的字段（除了主键字段）
        update_fields = [
            f"{self.config.vector_field} = EXCLUDED.{self.config.vector_field}",
            f"{self.config.update_time_field} = NOW()"
        ]
        
        upsert_sql = f"""
        {insert_sql}
        ON CONFLICT ({', '.join(conflict_fields)})
        DO UPDATE SET {', '.join(update_fields)}
        """
        
        return upsert_sql
    
    def _prepare_batch_params(self, batch: List[VectorRecord]) -> List[Tuple]:
        """准备批次参数"""
        params = []
        
        for record in batch:
            # 基础参数
            param_values = [
                record.embedding,  # vector
                record.knowledge_id,  # knowledge_id
                record.content_type,  # content_type
                datetime.now(),  # create_time
                datetime.now()   # update_time
            ]
            
            # 添加关联字段值
            param_values.extend(record.relation_ids.values())
            
            # 添加元数据字段值
            param_values.extend(record.metadata.values())
            
            params.append(tuple(param_values))
        
        return params


# 便捷函数
async def store_vectors(
    records: List[VectorRecord],
    config: Optional[VectorStorageConfig] = None,
    upsert: bool = False
) -> VectorStorageResult:
    """
    便捷的向量存储函数
    
    Args:
        records: 向量记录列表
        config: 存储配置
        upsert: 是否使用upsert模式
        
    Returns:
        VectorStorageResult: 存储结果
    """
    service = VectorStorageService(config)
    return await service.insert_vectors(records, upsert)

"""
DD-B字段聚合器

这个模块负责聚合多个Pipeline处理结果，实现复杂的字段聚合逻辑：

1. BDR09: 统计所有表范围的英文名 (set.union)
2. BDR10: 统计所有表范围的中文名 (set.union)  
3. BDR11: 统计所有字段信息 (dict.update)
4. SDR05,06,08,09: 对应的聚合逻辑
5. SDR12: 统计JOIN条件出现频次，>1/2则加入
6. BDR16: 使用聚合后的BDR09生成

特殊处理：
- 在聚合过程中保持原始格式（不str化）
- 最终输出时才转换为字符串格式
- RANGE记录的特殊处理逻辑
"""

import logging
from typing import Dict, List, Any, Set, Optional
from dataclasses import dataclass, field
from collections import Counter

from modules.dd_submission.dd_b.utils.pipeline_field_mapper import PipelineFieldMappingResult

logger = logging.getLogger(__name__)


@dataclass
class FieldAggregationResult:
    """字段聚合结果"""
    
    # 聚合统计信息
    total_records: int = 0
    successful_records: int = 0
    failed_records: int = 0
    
    # BDR字段聚合结果 (原始格式)
    bdr09_aggregated: Set[str] = field(default_factory=set)  # 表英文名集合
    bdr10_aggregated: Set[str] = field(default_factory=set)  # 表中文名集合
    bdr11_aggregated: Dict[str, str] = field(default_factory=dict)  # 字段信息合并
    bdr16_aggregated: str = ""  # 基于BDR09生成的描述
    
    # SDR字段聚合结果 (原始格式)
    sdr05_aggregated: Set[str] = field(default_factory=set)  # 与BDR09相同
    sdr06_aggregated: Set[str] = field(default_factory=set)  # 与BDR10相同
    sdr08_aggregated: Dict[str, str] = field(default_factory=dict)  # 与BDR11相同
    sdr09_aggregated: Dict[str, str] = field(default_factory=dict)  # 字段中文名合并
    sdr10_aggregated: str = ""  # 对于RANGE记录直接置空
    sdr12_candidates: List[str] = field(default_factory=list)  # SDR12候选值列表
    sdr12_aggregated: List[str] = field(default_factory=list)  # 频次>1/2的SDR12值
    
    def to_range_record_fields(self) -> Dict[str, str]:
        """生成RANGE记录的字段值（字符串格式）"""
        result = {}
        
        # BDR字段
        if self.bdr09_aggregated:
            bdr09_list = sorted(list(self.bdr09_aggregated))
            result['bdr09'] = str(bdr09_list)
            result['bdr16'] = f"表范围：{bdr09_list}"
        
        if self.bdr10_aggregated:
            bdr10_list = sorted(list(self.bdr10_aggregated))
            result['bdr10'] = str(bdr10_list)
        
        if self.bdr11_aggregated:
            result['bdr11'] = str(self.bdr11_aggregated)
        
        # SDR字段
        if self.sdr05_aggregated:
            sdr05_list = sorted(list(self.sdr05_aggregated))
            result['sdr05'] = str(sdr05_list)
        
        if self.sdr06_aggregated:
            sdr06_list = sorted(list(self.sdr06_aggregated))
            result['sdr06'] = str(sdr06_list)
        
        if self.sdr08_aggregated:
            result['sdr08'] = str(self.sdr08_aggregated)
        
        if self.sdr09_aggregated:
            result['sdr09'] = str(self.sdr09_aggregated)
        
        # RANGE特殊处理
        result['sdr10'] = ""  # 直接置空
        
        if self.sdr12_aggregated:
            result['sdr12'] = str(self.sdr12_aggregated)
        
        return result


class FieldAggregator:
    """字段聚合器"""
    
    def __init__(self, frequency_threshold: float = 0.5):
        """
        初始化聚合器
        
        Args:
            frequency_threshold: 频次阈值，默认0.5（即>1/2）
        """
        self.frequency_threshold = frequency_threshold
        logger.info(f"字段聚合器初始化完成: frequency_threshold={frequency_threshold}")
    
    def aggregate_mapping_results(
        self,
        mapping_results: List[PipelineFieldMappingResult]
    ) -> FieldAggregationResult:
        """
        聚合多个映射结果
        
        Args:
            mapping_results: Pipeline字段映射结果列表
            
        Returns:
            FieldAggregationResult: 聚合结果
        """
        logger.info(f"开始聚合字段: 输入记录数={len(mapping_results)}")
        
        aggregation = FieldAggregationResult()
        aggregation.total_records = len(mapping_results)
        
        # 统计成功和失败的记录
        successful_results = [r for r in mapping_results if r.mapping_success]
        failed_results = [r for r in mapping_results if not r.mapping_success]
        
        aggregation.successful_records = len(successful_results)
        aggregation.failed_records = len(failed_results)
        
        logger.info(f"记录统计: 成功={aggregation.successful_records}, "
                   f"失败={aggregation.failed_records}")
        
        # 聚合各个字段
        self._aggregate_bdr_fields(successful_results, aggregation)
        self._aggregate_sdr_fields(successful_results, aggregation)
        self._process_sdr12_frequency(aggregation)
        self._generate_bdr16(aggregation)
        
        logger.info(f"字段聚合完成: "
                   f"表数={len(aggregation.bdr09_aggregated)}, "
                   f"字段数={len(aggregation.bdr11_aggregated)}, "
                   f"SDR12候选数={len(aggregation.sdr12_candidates)}")
        
        return aggregation
    
    def _aggregate_bdr_fields(
        self,
        successful_results: List[PipelineFieldMappingResult],
        aggregation: FieldAggregationResult
    ):
        """聚合BDR字段"""
        logger.debug("开始聚合BDR字段")
        
        for result in successful_results:
            # BDR09: 表英文名集合
            if result.bdr09_raw:
                aggregation.bdr09_aggregated.update(result.bdr09_raw)
            
            # BDR10: 表中文名集合
            if result.bdr10_raw:
                aggregation.bdr10_aggregated.update(result.bdr10_raw)
            
            # BDR11: 字段信息合并
            if result.bdr11_raw:
                aggregation.bdr11_aggregated.update(result.bdr11_raw)
        
        logger.debug(f"BDR字段聚合完成: "
                    f"BDR09={len(aggregation.bdr09_aggregated)}个表, "
                    f"BDR10={len(aggregation.bdr10_aggregated)}个表中文名, "
                    f"BDR11={len(aggregation.bdr11_aggregated)}个字段")
    
    def _aggregate_sdr_fields(
        self,
        successful_results: List[PipelineFieldMappingResult],
        aggregation: FieldAggregationResult
    ):
        """聚合SDR字段"""
        logger.debug("开始聚合SDR字段")
        
        for result in successful_results:
            # SDR05: 与BDR09相同
            if result.sdr05_raw:
                aggregation.sdr05_aggregated.update(result.sdr05_raw)
            
            # SDR06: 与BDR10相同
            if result.sdr06_raw:
                aggregation.sdr06_aggregated.update(result.sdr06_raw)
            
            # SDR08: 与BDR11相同
            if result.sdr08_raw:
                aggregation.sdr08_aggregated.update(result.sdr08_raw)
            
            # SDR09: 字段中文名合并
            if result.sdr09_raw:
                aggregation.sdr09_aggregated.update(result.sdr09_raw)
            
            # SDR12: 收集候选值
            if result.sdr12_raw:
                aggregation.sdr12_candidates.append(result.sdr12_raw)
        
        logger.debug(f"SDR字段聚合完成: "
                    f"SDR05={len(aggregation.sdr05_aggregated)}个表, "
                    f"SDR09={len(aggregation.sdr09_aggregated)}个字段中文名, "
                    f"SDR12={len(aggregation.sdr12_candidates)}个候选值")
    
    def _process_sdr12_frequency(self, aggregation: FieldAggregationResult):
        """处理SDR12的频次统计"""
        if not aggregation.sdr12_candidates:
            logger.debug("SDR12候选值为空，跳过频次处理")
            return
        
        total_count = len(aggregation.sdr12_candidates)
        threshold_count = total_count * self.frequency_threshold
        
        # 统计每个值的出现次数
        counter = Counter(aggregation.sdr12_candidates)
        
        # 筛选出现次数大于阈值的值
        frequent_values = [
            value for value, count in counter.items()
            if count > threshold_count
        ]
        
        aggregation.sdr12_aggregated = frequent_values
        
        logger.debug(f"SDR12频次处理完成: "
                    f"总候选数={total_count}, "
                    f"阈值={threshold_count:.1f}, "
                    f"频繁值数={len(frequent_values)}")
        
        # 记录详细的频次信息
        for value, count in counter.most_common():
            is_frequent = count > threshold_count
            logger.debug(f"  '{value}': {count}次 {'✓' if is_frequent else '✗'}")
    
    def _generate_bdr16(self, aggregation: FieldAggregationResult):
        """生成BDR16字段"""
        if aggregation.bdr09_aggregated:
            table_list = sorted(list(aggregation.bdr09_aggregated))
            aggregation.bdr16_aggregated = f"表范围：{table_list}"
            logger.debug(f"BDR16生成完成: {aggregation.bdr16_aggregated}")
        else:
            logger.debug("BDR09为空，BDR16保持空值")
    
    def convert_to_string_format(
        self,
        mapping_results: List[PipelineFieldMappingResult]
    ) -> List[Dict[str, str]]:
        """
        将映射结果转换为字符串格式（用于普通记录）
        
        Args:
            mapping_results: 映射结果列表
            
        Returns:
            List[Dict[str, str]]: 字符串格式的结果列表
        """
        string_results = []
        
        for result in mapping_results:
            if result.mapping_success:
                string_result = result.to_string_format()
                string_result['record_id'] = str(result.record_id)
                string_results.append(string_result)
        
        logger.info(f"格式转换完成: {len(string_results)}个记录转换为字符串格式")
        return string_results

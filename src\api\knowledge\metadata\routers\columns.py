"""
元数据字段管理API路由

提供字段的完整CRUD操作接口，包括自动向量化处理。
参照DD系统的设计模式。
"""

from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Depends, Path
import logging

# 标准化日志导入
logger = logging.getLogger(__name__)

from api.knowledge.models.response_models import (
    ListResponse, CreateResponse, UpdateResponse, DeleteResponse, DetailResponse
)
from ..models import ColumnCreateRequest, ColumnUpdateRequest, ColumnTypeEnum
from ..dependencies import get_metadata_crud, validate_knowledge_id, validate_pagination

# 创建路由器
router = APIRouter(tags=["元数据字段管理"], prefix="/columns")


@router.post("/", response_model=CreateResponse, summary="创建字段")
async def create_column(
    knowledge_id: str = Query(..., description="知识库ID"),
    request: ColumnCreateRequest = ...,
    metadata_crud = Depends(get_metadata_crud)
):
    """
    创建新字段，自动进行向量化处理
    
    - **knowledge_id**: 知识库ID
    - **table_id**: 表ID
    - **column_name**: 字段名
    - **column_name_cn**: 字段中文名（将被向量化）
    - **column_desc**: 字段描述（将被向量化）
    - **data_type**: 数据类型
    - **data_example**: 数据样例
    - **column_type**: 字段类型
    - **is_primary_key**: 是否主键
    - **is_sensitive**: 是否敏感数据
    """
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        column_data = request.model_dump()
        column_id, vector_ids = await metadata_crud.create_column(knowledge_id, column_data)
        
        return CreateResponse(
            success=True,
            message="字段创建成功",
            data={
                "column_id": column_id,
                "knowledge_id": knowledge_id,
                "vector_ids": vector_ids,
                "vectorized_fields": len(vector_ids)
            }
        )
    except Exception as e:
        logger.error(f"创建字段失败: {e}")
        raise HTTPException(status_code=400, detail=f"创建字段失败: {str(e)}")


@router.get("/{column_id}", response_model=DetailResponse, summary="获取字段详情")
async def get_column(
    column_id: int = Path(..., description="字段ID"),
    knowledge_id: str = Query(..., description="知识库ID"),
    metadata_crud = Depends(get_metadata_crud)
):
    """获取字段详情"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        column = await metadata_crud.get_column(knowledge_id, column_id)
        
        if not column:
            raise HTTPException(status_code=404, detail=f"字段不存在: {column_id}")
        
        return DetailResponse(
            success=True,
            message="获取字段详情成功",
            data=column
        )
    except Exception as e:
        logger.error(f"获取字段详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取字段详情失败: {str(e)}")


@router.put("/{column_id}", response_model=UpdateResponse, summary="更新字段信息")
async def update_column(
    column_id: int = Path(..., description="字段ID"),
    knowledge_id: str = Query(..., description="知识库ID"),
    request: ColumnUpdateRequest = ...,
    metadata_crud = Depends(get_metadata_crud)
):
    """
    更新字段信息，如果更新了字段名称或描述，会自动更新向量
    
    - **column_id**: 字段ID
    - **knowledge_id**: 知识库ID
    - **column_name**: 字段名（可选）
    - **column_name_cn**: 字段中文名（可选，更新时会重新向量化）
    - **column_desc**: 字段描述（可选，更新时会重新向量化）
    - **data_type**: 数据类型（可选）
    - **data_example**: 数据样例（可选）
    - **column_type**: 字段类型（可选）
    - **is_primary_key**: 是否主键（可选）
    - **is_sensitive**: 是否敏感数据（可选）
    """
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        update_data = {k: v for k, v in request.model_dump().items() if v is not None}
        
        if not update_data:
            raise HTTPException(status_code=400, detail="没有提供要更新的字段")
        
        success, new_vector_ids = await metadata_crud.update_column(knowledge_id, column_id, update_data)
        
        if not success:
            raise HTTPException(status_code=400, detail="更新字段失败")
        
        return UpdateResponse(
            success=True,
            message="字段更新成功",
            data={
                "column_id": column_id,
                "knowledge_id": knowledge_id,
                "new_vector_ids": new_vector_ids,
                "updated_vectors": len(new_vector_ids)
            }
        )
    except Exception as e:
        logger.error(f"更新字段失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新字段失败: {str(e)}")


@router.delete("/{column_id}", response_model=DeleteResponse, summary="删除字段")
async def delete_column(
    column_id: int = Path(..., description="字段ID"),
    knowledge_id: str = Query(..., description="知识库ID"),
    metadata_crud = Depends(get_metadata_crud)
):
    """删除字段，同时删除相关的向量数据"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        success = await metadata_crud.delete_column(knowledge_id, column_id)
        
        return DeleteResponse(
            success=True,
            message="字段删除成功",
            data={"column_id": column_id, "knowledge_id": knowledge_id}
        )
    except Exception as e:
        logger.error(f"删除字段失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除字段失败: {str(e)}")


@router.get("/", response_model=ListResponse, summary="查询字段列表")
async def list_columns(
    knowledge_id: str = Query(..., description="知识库ID"),
    table_id: Optional[int] = Query(None, description="表ID过滤"),
    column_type: Optional[ColumnTypeEnum] = Query(None, description="字段类型过滤"),
    is_vectorized: Optional[bool] = Query(None, description="是否已向量化过滤"),
    is_primary_key: Optional[bool] = Query(None, description="是否主键过滤"),
    is_sensitive: Optional[bool] = Query(None, description="是否敏感数据过滤"),
    pagination = Depends(validate_pagination),
    metadata_crud = Depends(get_metadata_crud)
):
    """查询字段列表，支持分页和过滤"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        page, page_size, offset = pagination
        
        filters = {}
        if table_id is not None:
            filters["table_id"] = table_id
        if column_type is not None:
            filters["column_type"] = column_type.value
        if is_vectorized is not None:
            filters["is_vectorized"] = is_vectorized
        if is_primary_key is not None:
            filters["is_primary_key"] = is_primary_key
        if is_sensitive is not None:
            filters["is_sensitive"] = is_sensitive
        
        columns = await metadata_crud.list_columns(knowledge_id, **filters)
        
        total = len(columns)
        paginated_columns = columns[offset:offset + page_size]
        total_pages = (total + page_size - 1) // page_size
        
        return ListResponse(
            success=True,
            message="查询字段列表成功",
            data={
                "items": paginated_columns,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }
        )
    except Exception as e:
        logger.error(f"查询字段列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询字段列表失败: {str(e)}")

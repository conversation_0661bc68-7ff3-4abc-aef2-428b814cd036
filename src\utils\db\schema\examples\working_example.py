# -*- coding: utf-8 -*-
"""
修复后的 SchemaGenerator 工作示例

解决协程问题，提供您需要的核心功能：
1. 获取prompt列表（带token限制）
2. 获取数据库到表的映射
3. 获取表到列的映射
"""

import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from generate import SchemaGenerator


async def demo_core_functionality():
    """演示核心功能 - 您需要的主要功能"""
    print("🎯 核心功能演示")
    print("=" * 60)
    
    # 创建生成器
    generator = SchemaGenerator("source")
    
    # 步骤1: 加载数据（修复协程问题）
    print("\n📋 步骤1: 加载表数据...")
    table_names = ["adm_lon_varoius", "test_complete_db"]
    await generator.from_table_names(table_names, column_limit=5)
    print(f"✅ 成功加载 {len(table_names)} 个表的数据")
    
    # 步骤2: 获取简单的prompt列表
    print("\n📄 步骤2: 获取简单prompt列表...")
    simple_prompts = generator.split(token_size=7500)
    print(f"✅ 生成了 {len(simple_prompts)} 个prompt片段")
    
    for i, prompt in enumerate(simple_prompts):
        print(f"   Prompt {i+1}: {len(prompt)} 字符")
    
    # 步骤3: 获取带映射信息的prompt列表（您的核心需求）
    print("\n🎯 步骤3: 获取带映射信息的prompt列表...")
    prompt_data = generator.get_prompt_list_with_mappings(token_size=7500)
    print(f"✅ 生成了 {len(prompt_data)} 个带映射信息的prompt片段")
    
    # 步骤4: 处理每个prompt片段
    print("\n🔍 步骤4: 分析每个prompt片段的映射信息...")
    
    for i, item in enumerate(prompt_data):
        print(f"\n📊 Prompt片段 {i+1}:")
        print(f"   Prompt长度: {len(item['prompt'])} 字符")
        
        # 数据库到表的映射
        db_to_tables = item['db_to_tables']
        print(f"   数据库到表映射: {db_to_tables}")
        
        # 表到列的映射
        table_to_columns = item['table_to_columns']
        print(f"   表到列映射: {table_to_columns}")
        
        # 显示涉及的数据库和表
        databases = list(db_to_tables.keys())
        tables = list(table_to_columns.keys())
        print(f"   涉及数据库: {databases}")
        print(f"   涉及表: {tables}")
        
        # 显示每个表的列数
        for table, columns in table_to_columns.items():
            print(f"   表 '{table}' 包含 {len(columns)} 列: {columns}")
        
        print("-" * 40)


async def demo_backward_compatibility():
    """演示向后兼容性"""
    print("\n\n🔄 向后兼容性演示")
    print("=" * 60)
    
    generator = SchemaGenerator("source")
    
    # 使用旧的方法名（仍然有效）
    print("\n📋 使用旧方法名加载数据...")
    await generator.by_table_names(["贷款信息表", "交易记录表"], limit=3)
    print("✅ 旧方法名工作正常")
    
    # 使用旧的方法获取prompt列表
    print("\n📄 使用旧方法获取prompt列表...")
    old_prompts = generator.to_prompt_list(token_size=7500)
    print(f"✅ 旧方法生成了 {len(old_prompts)} 个prompt")
    
    # 使用新的映射功能（向后兼容）
    print("\n🎯 使用新的映射功能...")
    mapping_prompts = generator.to_prompt_list_with_mappings(token_size=7500)
    print(f"✅ 新映射功能生成了 {len(mapping_prompts)} 个带映射的prompt")


async def demo_real_usage_pattern():
    """演示真实使用模式"""
    print("\n\n🌍 真实使用模式演示")
    print("=" * 60)
    
    # 模拟您的实际使用场景
    generator = SchemaGenerator("source")
    
    # 加载银行业务相关的表
    print("\n📋 加载银行业务表...")
    business_tables = ["贷款信息表", "交易记录表", "客户信息表", "账户信息表"]
    await generator.from_table_names(business_tables, column_limit=6)
    
    # 获取适合LLM的prompt片段
    print("\n🤖 生成适合LLM的prompt片段...")
    llm_prompts = generator.get_prompt_list_with_mappings(token_size=6000)
    
    print(f"✅ 为 {len(business_tables)} 个表生成了 {len(llm_prompts)} 个LLM prompt")
    
    # 模拟发送给LLM的流程
    print("\n🚀 模拟LLM处理流程...")
    
    for i, prompt_item in enumerate(llm_prompts):
        print(f"\n处理Prompt {i+1}:")
        
        # 获取映射信息
        db_mappings = prompt_item['db_to_tables']
        table_mappings = prompt_item['table_to_columns']
        
        # 显示当前prompt涉及的范围
        total_dbs = len(db_mappings)
        total_tables = len(table_mappings)
        total_columns = sum(len(cols) for cols in table_mappings.values())
        
        print(f"  📊 涉及: {total_dbs}个数据库, {total_tables}个表, {total_columns}个列")
        print(f"  📏 Prompt大小: {len(prompt_item['prompt'])} 字符")
        
        # 这里是您实际调用LLM的地方
        # llm_response = call_llm_api(prompt_item['prompt'])
        
        # 根据映射信息处理LLM响应
        print(f"  🎯 映射信息:")
        for db_name, tables in db_mappings.items():
            print(f"    数据库 '{db_name}': {tables}")
        
        for table_name, columns in table_mappings.items():
            print(f"    表 '{table_name}': {len(columns)}列")
        
        print(f"  ✅ Prompt {i+1} 处理完成")
    
    print(f"\n🎉 所有 {len(llm_prompts)} 个prompt处理完成！")


def show_usage_summary():
    """显示使用总结"""
    print("\n\n📚 使用总结")
    print("=" * 60)
    
    print("\n✅ 问题修复:")
    print("  - 修复了协程调用问题")
    print("  - await generator.from_table_names() 然后 generator.split()")
    print("  - 不再是 await generator.from_table_names().split()")
    
    print("\n🎯 核心功能:")
    print("  1. generator.split(token_size) - 获取prompt列表")
    print("  2. generator.get_prompt_list_with_mappings(token_size) - 获取带映射的prompt")
    print("     返回格式: [{'prompt': str, 'db_to_tables': dict, 'table_to_columns': dict}]")
    
    print("\n📋 正确的使用模式:")
    print("  # 创建生成器")
    print("  generator = SchemaGenerator('source')")
    print("  ")
    print("  # 加载数据（注意await）")
    print("  await generator.from_table_names(['贷款信息表', '交易记录表'])")
    print("  ")
    print("  # 获取prompt列表")
    print("  prompts = generator.split(token_size=7500)")
    print("  ")
    print("  # 或获取带映射的prompt列表")
    print("  prompt_data = generator.get_prompt_list_with_mappings(token_size=7500)")
    print("  for item in prompt_data:")
    print("      send_to_llm(item['prompt'])")
    print("      process_mappings(item['db_to_tables'], item['table_to_columns'])")
    
    print("\n🔄 向后兼容:")
    print("  - 所有旧方法名仍然有效")
    print("  - generator.by_table_names() 替代 generator.from_table_names()")
    print("  - generator.to_prompt_list() 替代 generator.split()")
    print("  - generator.to_prompt_list_with_mappings() - 新功能")


async def main():
    """主函数"""
    print("🚀 修复后的 SchemaGenerator 工作示例")
    print("=" * 80)
    
    try:
        await demo_core_functionality()
        await demo_backward_compatibility()
        await demo_real_usage_pattern()
        show_usage_summary()
        
        print("\n\n🎉 所有演示完成！")
        print("现在您可以正确使用SchemaGenerator了。")
        
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())

"""
部门职责分配模块 - 重构版本

提供义务解读部门职责分配功能：
- 历史匹配搜索（精确匹配 + 混合搜索）
- 四层业务逻辑筛选（套系类型、报表类型、提交类型、数据层）
- 智能决策逻辑（唯一匹配、多个匹配、回退匹配）
- 数据回填处理（用户修改DD后的回填逻辑）

主要接口：
- DepartmentAssignment: 部门分配主接口
- DataBackfill: 数据回填主接口

重构说明：
- 将原有的多个文件重新组织为清晰的目录结构
- 提供两个主要接口文件，简化使用方式
- 保持向后兼容性，原有接口仍然可用
"""

# 主要接口
from .department_assignment import (
    DepartmentAssignment,
    assign_department_single,
    assign_department_batch,
    process_duty_distribution
)
from .data_backfill import (
    DataBackfill,
    process_data_backfill,
    validate_backfill_data,
    get_backfill_status
)

# 数据模型（从infrastructure导入）
from .infrastructure.models import (
    DepartmentAssignmentRequest,
    DepartmentAssignmentResult,
    HistoricalRecord,
    FilterResult,
    SearchStrategy,
    DepartmentRecommendation,
    KeywordExtractionResult,
    SearchConditionConfig,
    BatchAssignmentRequest,
    BatchAssignmentItem,
    BatchAssignmentResponse,
    BatchProcessingResult
)

# 常量和工具（从infrastructure导入）
from .infrastructure.constants import DepartmentAssignmentConstants, DepartmentAssignmentUtils

# 异常（从infrastructure导入）
from .infrastructure.exceptions import (
    DepartmentAssignmentError,
    DepartmentAssignmentValidationError,
    DepartmentAssignmentSearchError,
    DepartmentAssignmentFilterError
)

# 向后兼容性：保留原有的主要类名
from .core.assignment_engine import DepartmentAssignmentLogic
from .core.backfill_engine import DataBackfillLogic
from .core.database_operations import PostDistributionDBOperations
from .core.department_recommender import TFIDFDepartmentRecommender, get_department_recommender
from .infrastructure.nlp_processor import NLPProcessor, get_nlp_processor
from .infrastructure.search_builder import DynamicSearchBuilder, get_search_builder

__version__ = "2.0.0"  # 重构版本

__all__ = [
    # 主要接口（新）
    "DepartmentAssignment",
    "DataBackfill",

    # 便捷函数（新）
    "assign_department_single",
    "assign_department_batch",
    "process_duty_distribution",
    "process_data_backfill",
    "validate_backfill_data",
    "get_backfill_status",

    # 数据模型
    "DepartmentAssignmentRequest",
    "DepartmentAssignmentResult",
    "HistoricalRecord",
    "FilterResult",
    "SearchStrategy",
    "DepartmentRecommendation",
    "KeywordExtractionResult",
    "SearchConditionConfig",
    "BatchAssignmentRequest",
    "BatchAssignmentItem",
    "BatchAssignmentResponse",
    "BatchProcessingResult",

    # 常量和工具
    "DepartmentAssignmentConstants",
    "DepartmentAssignmentUtils",

    # 异常
    "DepartmentAssignmentError",
    "DepartmentAssignmentValidationError",
    "DepartmentAssignmentSearchError",
    "DepartmentAssignmentFilterError",

    # 向后兼容性
    "DepartmentAssignmentLogic",
    "DataBackfillLogic",
    "PostDistributionDBOperations",
    "TFIDFDepartmentRecommender",
    "get_department_recommender",
    "NLPProcessor",
    "get_nlp_processor",
    "DynamicSearchBuilder",
    "get_search_builder",
]

# 轻量化Pipeline框架

基于service层的企业级数据处理管道，专为NL2SQL任务设计。

## 🚀 特性

- **轻量化设计**: 替代重型WorkflowState，使用轻量级PipelineContext
- **四阶段处理**: 预处理 → 执行 → 解析 → 后处理的标准化流程
- **service层集成**: 充分利用现有的service层架构
- **灵活配置**: 支持标准、增强、最小化和自定义Pipeline
- **错误处理**: 完善的错误处理和重试机制
- **易于扩展**: 清晰的步骤接口，便于添加新功能

## 📋 支持的步骤

### 原有步骤（已迁移）
1. **KeyAssociatorStep** - 关键字关联，搜索相关catalog和SQL样例
2. **ColumnSelectorStep** - 列选择，从候选列中选择最相关的列
3. **SchemaGeneratorStep** - Schema生成，根据选择的列生成数据库schema
4. **SQLGeneratorStep** - SQL生成，根据schema和样例生成SQL查询

### 新增步骤
5. **DataSourceCollectorStep** - 数据源信息收集，根据dept_id收集表和列的元数据
6. **EnhancedSchemaGeneratorStep** - 增强Schema生成，基于收集的元数据生成完整schema
7. **BusinessLogicGeneratorStep** - 业务逻辑生成，生成JSON格式的业务逻辑描述

## 🛠 安装和使用

### 基础使用

```python
from pipeline import NL2SQLExecutor

# 创建执行器
executor = NL2SQLExecutor("standard")

# 执行查询
result = await executor.execute(
    user_question="查询2024年制造业贷款余额",
    hint="银保监会口径",
    candidate_columns={
        "loan_table": ["amount", "industry", "year"]
    }
)

print(f"生成的SQL: {result['data']['sql_candidates']}")
```

### 增强模式（包含数据源收集）

```python
# 创建增强执行器
executor = NL2SQLExecutor("enhanced", include_business_logic=True)

# 执行查询（需要dept_id）
result = await executor.execute(
    user_question="查询客户贷款信息统计",
    hint="银保监会口径",
    dept_id="DEPT001"  # 部门ID，用于数据源收集
)

print(f"业务逻辑: {result['data']['business_logic']}")
```

### 自定义Pipeline

```python
# 创建自定义Pipeline
executor = NL2SQLExecutor("custom", step_names=[
    "data_source_collector",
    "column_selector",
    "enhanced_schema_generator",
    "sql_generator",
    "business_logic_generator"
])

result = await executor.execute(
    user_question="统计各行业客户数量",
    dept_id="DEPT001"
)
```

## 🏗 Pipeline类型

### 1. 标准Pipeline (`standard`)
```
KeyAssociator → ColumnSelector → SchemaGenerator → SQLGenerator → [BusinessLogicGenerator]
```

### 2. 增强Pipeline (`enhanced`)
```
DataSourceCollector → KeyAssociator → ColumnSelector → EnhancedSchemaGenerator → SQLGenerator → [BusinessLogicGenerator]
```

### 3. 最小化Pipeline (`minimal`)
```
ColumnSelector → SchemaGenerator → SQLGenerator
```

### 4. 自定义Pipeline (`custom`)
根据指定的步骤名称组合

## 📊 数据流

### 输入数据
- `user_question`: 用户问题
- `hint`: 提示信息（默认：银保监会口径）
- `dept_id`: 部门ID（增强模式需要）
- `candidate_columns`: 候选列（标准模式需要）
- `keywords`: 关键字列表

### 输出数据
- `selected_columns`: 选择的列
- `db_schema`: 生成的数据库schema
- `sql_candidates`: SQL候选列表
- `business_logic`: 业务逻辑描述（JSON格式）
- `table_metadata`: 表元数据（增强模式）
- `column_metadata`: 列元数据（增强模式）

## 🔧 高级用法

### 直接使用Pipeline管理器

```python
from pipeline import create_nl2sql_pipeline, ContextFactory

# 创建Pipeline
pipeline = create_nl2sql_pipeline("standard")

# 创建上下文
context = ContextFactory.create_nl2sql_context(
    user_question="查询贷款信息",
    candidate_columns={"loan_table": ["amount"]}
)

# 执行
result = await pipeline.execute(context)
```

### 分步执行

```python
from pipeline import PipelineExecutor

executor = PipelineExecutor(pipeline)

# 执行单个步骤
result = await executor.execute_single_step(context, "column_selector")

# 执行步骤范围
result = await executor.execute_step_range(context, "start_step", "end_step")
```

### 错误处理策略

```python
# 设置错误处理策略
pipeline.set_error_strategy("continue_on_error")  # 或 "stop_on_error"
```

## 📝 业务逻辑输出格式

BusinessLogicGeneratorStep生成的JSON格式：

```json
{
    "表范围": "涉及的表信息",
    "计算逻辑": "LLM生成的计算逻辑描述",
    "条件": "LLM生成的查询条件说明",
    "维度": "LLM生成的数据维度分析",
    "整体逻辑描述": "LLM生成的完整业务逻辑说明"
}
```

## 🔍 调试和监控

### 查看执行结果

```python
result = await executor.execute(user_question="...")

print(f"执行成功: {result['success']}")
print(f"执行时间: {result['execution_time']:.2f}s")
print(f"执行步骤: {result['executed_steps']}")
print(f"失败步骤: {result['failed_steps']}")
print(f"步骤结果: {result['step_results']}")
```

### 查看上下文数据

```python
context = result['context']
print(f"用户问题: {context.user_question}")
print(f"选择的列: {context.get('selected_columns')}")
print(f"生成的SQL: {context.get('sql_candidates')}")
```

## 🧪 测试

运行使用示例：

```bash
cd src/pipeline/examples
python usage_examples.py
```

## 🤝 扩展开发

### 创建自定义步骤

```python
from pipeline.core.base_step import ToolStep
from pipeline.core.context import PipelineContext

class CustomStep(ToolStep):
    def __init__(self):
        super().__init__(
            name="custom_step",
            description="自定义步骤描述"
        )
    
    async def preprocess(self, context: PipelineContext) -> Dict[str, Any]:
        # 预处理逻辑
        return {"data": "preprocessed"}
    
    async def process(self, preprocessed_data: Dict[str, Any], context: PipelineContext) -> Any:
        # 核心处理逻辑
        return "processed_result"
    
    async def update_context(self, result: Any, context: PipelineContext) -> None:
        # 更新上下文
        context.set("custom_result", result)
```

### 添加到Pipeline

```python
from pipeline import PipelineBuilder

builder = PipelineBuilder("custom_pipeline")
builder.add_step(CustomStep())
pipeline = builder.build()
```

## 📚 更多示例

查看 `src/pipeline/examples/usage_examples.py` 获取更多详细示例。

## 🐛 故障排除

### 常见问题

1. **缺少dept_id**: 增强模式需要提供dept_id参数
2. **缺少candidate_columns**: 标准模式需要提供候选列
3. **数据库连接失败**: 检查service层的数据库配置
4. **LLM调用失败**: 检查模型服务是否正常运行

### 日志查看

Pipeline使用loguru进行日志记录，可以通过以下方式查看详细日志：

```python
from loguru import logger

# 设置日志级别
logger.add("pipeline.log", level="DEBUG")
```

## 📄 许可证

本项目遵循公司内部许可证。

#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：deep doc
@File    ：__init__.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/17 10:30 
@Desc    ： 
"""
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.config.parser import ConfigParser
from marker.output import text_from_rendered

from .docx_parser import IdealDocxParser as DocxParser
from .doc_parser import IdealDocParser as DocParser
from .markdown_parser import IdealMarkdownParser as MarkdownParser
from .pdf_parser import IdealPdfParser as PdfParser, PlainParser
from .wps_parser import IdealWpsParser as WpsParser

def initialize_converter(config):
    config_parser = ConfigParser(config)
    converter = PdfConverter(
        config=config_parser.generate_config_dict(),
        artifact_dict=create_model_dict(),
        processor_list=config_parser.get_processors(),
        renderer=config_parser.get_renderer(),
        llm_service=config_parser.get_llm_service(),
    )
    return converter

config = {"output_format": "markdown"}
converter = initialize_converter(config)

__all__ = [
    "DocxParser",
    "DocParser",
    "MarkdownParser",
    "PdfParser",
    "PlainParser",
    "WpsParser",
    "converter"
]


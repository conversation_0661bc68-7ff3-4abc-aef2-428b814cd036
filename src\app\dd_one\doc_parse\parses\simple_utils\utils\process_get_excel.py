import pandas as pd
import re
from typing import Dict, List, Tuple

tran_data = {
    "峁": "POS"
}


def process_excel(file_path: str) -> Tuple[Dict[str, List[Dict[str, str]]], List[str]]:
    """
    读取 Excel 文件的多个 Sheet，生成每个 Sheet 的字典列表。
    处理 name_x 中的 tran_data 值，转换为对应 key，处理后再转回原值。

    Args:
        file_path (str): Excel 文件路径（.xlsx 或 .xls）

    Returns:
        Tuple[Dict[str, List[Dict[str, str]]], List[str]]: 每个 Sheet 的处理后数据字典和 Sheet 名称列表
    """
    result = {}

    try:
        # 读取 Excel 文件的所有 Sheet
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        excel_data = pd.read_excel(file_path, sheet_name=None, dtype=str)

        for sheet_name, df in excel_data.items():
            # 清理列名（移除 BOM 字符）
            df.columns = [col.strip('\ufeff') for col in df.columns]
            seen = set()
            sheet_result = []

            for row in df.to_dict('records'):
                # 获取字段，设置默认空字符串并去除多余空格
                name_x = row.get('name_x', '').strip().replace('其中：', '')
                name_y = row.get('name_y', '').strip()
                id_y = row.get('id_y', '').strip()
                valid = row.get('valid', '').strip()
                original_position = row.get('original_position', '').strip()

                # 跳过重复行
                row_key = id_y + name_x
                if row_key in seen:
                    continue
                seen.add(row_key)

                # 保存原始 name_x，处理 tran_data 转换（value 转为 key）
                original_name_x = name_x
                for key, value in tran_data.items():
                    if value in name_x:
                        name_x = name_x.replace(value, key)

                # 提取 name（数字开头的内容）
                name_match = re.search(r'\d+(.*)', name_x)
                name = name_match.group(0).replace(' ', '') if name_match else ''

                # 提取 project_name（从第一个中文字符到末尾）
                project_match = re.search(r'[\u4e00-\u9fff].*', name_x)
                project_name = project_match.group(0).strip() if project_match else ''

                # 提取 name_id（中文字符前的部分，去除末尾点号）
                name_id = re.sub(r'[\u4e00-\u9fff].*', '', name_x).strip()
                if name_id.endswith('.'):
                    name_id = name_id[:-1]

                # 处理 id_y：清理、分割、过滤
                id_parts = id_y.replace('\n', '').replace(' ', '').split('<split>')
                id_parts = [part for part in id_parts if not (part and re.match(r'^[a-zA-Z]', part))]
                id_parts = list(set(id_parts))  # 去重
                y2 = '-'.join(id_parts) if id_parts else ''

                # 将 name_x 中的 key 转换回原始 value
                for key, value in tran_data.items():
                    if key in name:
                        name = name.replace(key, value)
                    if key in project_name:
                        project_name = project_name.replace(key, value)
                    if key in name_id:
                        name_id = name_id.replace(key, value)

                # 构建结果字典
                sheet_result.append({
                    "name": name,
                    "y1": name_y,
                    "y2": y2,
                    "valid": valid,
                    "project_name": project_name,
                    "name_id": name_id,
                    "original_position": original_position
                })

            result[sheet_name] = sheet_result

    except Exception as e:
        print(f"读取 Excel 文件 {file_path} 出错: {str(e)}")
        return {}, []

    return result, sheet_names


def excel_coordinate_to_a1(coord: str) -> str:
    """
    将 Excel 的 (x, y) 数值坐标转换为 A1 格式，其中 x 为行号，y 为列号（如 (1, 1) -> A1, (3, 2) -> B3）。

    参数:
        coord: 元组 (x, y)，x 为行号（从 1 开始），y 为列号（从 1 开始）

    返回:
        str: Excel 的 A1 格式坐标（如 "A1", "B3"）

    异常:
        ValueError: 如果 x 或 y 小于 1
    """
    if coord:
        x, y = coord.replace('(', '').replace(')', '').replace(" ", '').split(',')
        x, y = int(x), int(y)
        # 验证输入
        if x < 1 or y < 1:
            raise ValueError("行号和列号必须从 1 开始")

        # 转换列号为字母
        column_name = ""
        temp_y = y
        while temp_y > 0:
            temp_y -= 1  # 转换为 0-based 索引
            column_name = chr(65 + (temp_y % 26)) + column_name  # 65 是 'A' 的 ASCII 值
            temp_y //= 26

        # 拼接列名和行号
        return f"{column_name}{x}"
    else:
        return ""


def get_custom_indicat(indicator_data: List[Dict]) -> Tuple[Dict[str, List[Dict]], List[str]]:
    result = {}
    sheet_sort_map = {}

    # Process each indicator entry
    for item in sorted(indicator_data, key=lambda x: x['sheet_sort']):
        sheet_name = item['sheet_name']
        sheet_sort = item['sheet_sort']

        # Track minimum sheet_sort for each sheet_name
        sheet_sort_map[sheet_name] = min(sheet_sort_map.get(sheet_name, float('inf')), sheet_sort)

        # Extract and clean core fields
        name_x = item["col"][0].strip().replace('其中：', '')
        name_y = item["lines"][0].strip()
        id_y = '<split>'.join(item["lines"]).replace('\n', '').replace(' ', '')
        original_position = f"({item['mark']['x']},{item['mark']['y']})"

        # Apply translations to name_x
        for key, value in tran_data.items():
            name_x = name_x.replace(value, key)

        # Extract components using regex
        name = (re.search(r'\d+(.*)', name_x).group(0).replace(' ', '')
                if re.search(r'\d+(.*)', name_x) else '')
        project_name = (re.search(r'[\u4e00-\u9fff].*', name_x).group(0).strip()
                        if re.search(r'[\u4e00-\u9fff].*', name_x) else '')
        name_id = re.sub(r'[\u4e00-\u9fff].*', '', name_x).strip()
        name_id = name_id[:-1] if name_id.endswith('.') else name_id

        # Process id_y
        id_parts = [part for part in id_y.split('<split>')
                    if part and not re.match(r'^[a-zA-Z]', part)]
        y2 = '-'.join(set(id_parts)) if id_parts else ''

        # Reverse translations
        for key, value in tran_data.items():
            name = name.replace(key, value)
            project_name = project_name.replace(key, value)
            name_id = name_id.replace(key, value)

        # Create result dictionary
        entry = {
            "name": name,
            "y1": name_y,
            "y2": y2,
            "valid": True,
            "project_name": project_name,
            "name_id": name_id,
            "original_position": original_position
        }

        # Add to result dictionary
        result.setdefault(sheet_name, []).append(entry)

    # Generate ordered sheet_names list
    sheet_names = sorted(sheet_sort_map, key=lambda x: sheet_sort_map[x])

    return result, sheet_names

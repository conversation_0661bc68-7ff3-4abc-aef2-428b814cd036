"""
基础模型和枚举定义

定义RAG模块的基础数据结构和枚举类型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel
from dataclasses import dataclass


class DocumentType(str, Enum):
    """文档类型枚举"""
    OUTSIDE = "outside"  # 外部文档
    INSIDE = "inside"    # 内部文档


class DocumentStatus(str, Enum):
    """文档解析状态枚举"""
    PENDING = "pending"      # 待解析
    PROCESSING = "processing"  # 解析中
    COMPLETED = "completed"    # 解析完成
    FAILED = "failed"        # 解析失败
    CANCELLED = "cancelled"   # 已取消


class ParseType(str, Enum):
    """解析方式枚举"""
    AUTO = "auto"        # 自动识别
    PDF = "pdf"          # PDF解析
    DOCX = "docx"        # Word文档解析
    TXT = "txt"          # 纯文本解析
    OCR = "ocr"          # OCR识别解析


class DocumentFormat(str, Enum):
    """文件格式枚举"""
    PDF = "pdf"
    DOCX = "docx"
    DOC = "doc"
    TXT = "txt"
    MD = "md"
    HTML = "html"
    XML = "xml"


class CategoryStatus(str, Enum):
    """类别状态枚举"""
    ACTIVE = "active"      # 活跃
    INACTIVE = "inactive"  # 未激活
    ARCHIVED = "archived"  # 已归档


class RelationType(str, Enum):
    """关系类型枚举"""
    REFERENCE = "reference"    # 引用关系
    DEPENDENCY = "dependency"  # 依赖关系
    SIMILARITY = "similarity"  # 相似关系
    PARENT_CHILD = "parent_child"  # 父子关系
    RELATED = "related"        # 相关关系


class InfoType(str, Enum):
    """分块信息类型枚举"""
    CONTENT = "content"      # 内容
    KEYWORD = "keyword"      # 关键词
    SUMMARY = "summary"      # 摘要
    METADATA = "metadata"    # 元数据
    TAGS = "tags"           # 标签


class BaseAPIModel(BaseModel):
    """API模型基类，提供通用配置"""
    
    class Config:
        from_attributes = True  # 支持从ORM对象创建
        use_enum_values = True  # 枚举值序列化
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


# ==========================================
# 实体类定义 (参考DD模块封装模式)
# ==========================================

@dataclass
class ChunkInfo:
    """
    分块信息实体
    
    对应数据库表：doc_dev_chunks_info
    管理分块的详细信息，包括内容、摘要、关键词等
    """
    chunk_info_id: str
    chunk_id: str
    info_type: str  # content, summary, keyword, metadata, tags
    info_value: str
    created_time: Optional[datetime] = None
    updated_time: Optional[datetime] = None
    is_active: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "chunk_info_id": self.chunk_info_id,
            "chunk_id": self.chunk_id,
            "info_type": self.info_type,
            "info_value": self.info_value,
            "created_time": self.created_time,
            "updated_time": self.updated_time,
            "is_active": self.is_active,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ChunkInfo":
        """从字典创建实例"""
        return cls(
            chunk_info_id=data["chunk_info_id"],
            chunk_id=data["chunk_id"],
            info_type=data["info_type"],
            info_value=data["info_value"],
            created_time=data.get("created_time"),
            updated_time=data.get("updated_time"),
            is_active=data.get("is_active", True),
        )


@dataclass
class Chunk:
    """
    分块实体
    
    对应数据库表：doc_dev_chunks
    管理文档分块的基础信息和层级结构
    """
    chunk_id: str
    doc_id: str
    chapter_layer: Optional[str] = None  # 章节层级信息，如"第一章.第二节"
    parent_id: Optional[str] = None      # 父分块ID，用于构建树状结构
    created_time: Optional[datetime] = None
    updated_time: Optional[datetime] = None
    is_active: bool = True
    
    # 关联的分块信息列表 (运行时填充)
    chunk_infos: Optional[List[ChunkInfo]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "chunk_id": self.chunk_id,
            "doc_id": self.doc_id,
            "chapter_layer": self.chapter_layer,
            "parent_id": self.parent_id,
            "created_time": self.created_time,
            "updated_time": self.updated_time,
            "is_active": self.is_active,
        }
        
        # 如果有关联的分块信息，也包含进去
        if self.chunk_infos is not None:
            result["chunk_infos"] = [info.to_dict() for info in self.chunk_infos]
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Chunk":
        """从字典创建实例"""
        chunk = cls(
            chunk_id=data["chunk_id"],
            doc_id=data["doc_id"],
            chapter_layer=data.get("chapter_layer"),
            parent_id=data.get("parent_id"),
            created_time=data.get("created_time"),
            updated_time=data.get("updated_time"),
            is_active=data.get("is_active", True),
        )
        
        # 如果数据中包含分块信息，转换为ChunkInfo实例
        if "chunk_infos" in data and data["chunk_infos"]:
            chunk.chunk_infos = [
                ChunkInfo.from_dict(info_data) for info_data in data["chunk_infos"]
            ]
        
        return chunk
    
    def get_info_by_type(self, info_type: str) -> Optional[ChunkInfo]:
        """根据类型获取分块信息"""
        if not self.chunk_infos:
            return None
        
        for info in self.chunk_infos:
            if info.info_type == info_type:
                return info
        return None
    
    def get_content(self) -> Optional[str]:
        """获取内容信息"""
        content_info = self.get_info_by_type("content")
        return content_info.info_value if content_info else None
    
    def get_summary(self) -> Optional[str]:
        """获取摘要信息"""
        summary_info = self.get_info_by_type("summary")
        return summary_info.info_value if summary_info else None
    
    def get_keywords(self) -> Optional[str]:
        """获取关键词信息"""
        keyword_info = self.get_info_by_type("keyword")
        return keyword_info.info_value if keyword_info else None


@dataclass
class DocEmbedding:
    """
    文档向量实体
    
    对应数据库表：doc_embeddings
    管理分块信息的向量数据
    """
    id: Optional[int] = None
    knowledge_id: str = ""
    doc_id: str = ""
    chunk_id: str = ""
    chunk_info_id: str = ""
    info_type: str = ""  # content, summary, title等
    embedding: Optional[List[float]] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "knowledge_id": self.knowledge_id,
            "doc_id": self.doc_id,
            "chunk_id": self.chunk_id,
            "chunk_info_id": self.chunk_info_id,
            "info_type": self.info_type,
            "embedding": self.embedding,
            "create_time": self.create_time,
            "update_time": self.update_time,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DocEmbedding":
        """从字典创建实例"""
        return cls(
            id=data.get("id"),
            knowledge_id=data.get("knowledge_id", ""),
            doc_id=data.get("doc_id", ""),
            chunk_id=data.get("chunk_id", ""),
            chunk_info_id=data.get("chunk_info_id", ""),
            info_type=data.get("info_type", ""),
            embedding=data.get("embedding"),
            create_time=data.get("create_time"),
            update_time=data.get("update_time"),
        )
    
    def validate(self) -> bool:
        """验证实体数据完整性"""
        required_fields = [
            "knowledge_id", "doc_id", "chunk_id", 
            "chunk_info_id", "info_type"
        ]
        
        for field in required_fields:
            if not getattr(self, field):
                return False
        
        return self.embedding is not None and len(self.embedding) > 0 
"""
查询构建器

提供链式API构建复杂查询，参考Django ORM和SQLAlchemy的设计
"""

from typing import Any, Dict, List, Optional, Union
from copy import deepcopy

from ..core.types import DatabaseValue, DatabaseType, ComparisonOperator, LogicalOperator, SortOrder, JoinType
from ..core.models import (
    QueryRequest, QueryFilter, QueryFilterGroup, QuerySort, QueryJoin,
    QueryAggregation, QueryGroupBy, InsertRequest, UpdateRequest, DeleteRequest
)
from ..core.interfaces import QueryBuilder as QueryBuilderInterface
from ..core.exceptions import ValidationError


class DefaultQueryBuilder(QueryBuilderInterface):
    """默认查询构建器
    
    提供链式API构建复杂查询
    """
    
    def __init__(self, database_client=None):
        self._database_client = database_client
        self._reset()
    
    def _reset(self):
        """重置构建器状态"""
        self._table_name: Optional[str] = None
        self._columns: Optional[List[str]] = None
        self._filters: Optional[QueryFilterGroup] = None
        self._joins: List[QueryJoin] = []
        self._sorts: List[QuerySort] = []
        self._group_by: Optional[QueryGroupBy] = None
        self._aggregations: List[QueryAggregation] = []
        self._limit_value: Optional[int] = None
        self._offset_value: Optional[int] = None
        self._distinct_flag: bool = False
        self._for_update_flag: bool = False
        self._timeout_value: Optional[float] = None
    
    def table(self, table_name: str) -> 'DefaultQueryBuilder':
        """设置表名"""
        if not table_name:
            raise ValidationError("Table name cannot be empty")
        
        # 创建新实例以支持链式调用
        new_builder = self._clone()
        new_builder._table_name = table_name
        return new_builder
    
    def select(self, *columns: str) -> 'DefaultQueryBuilder':
        """选择列"""
        new_builder = self._clone()
        if columns:
            new_builder._columns = list(columns)
        else:
            new_builder._columns = None  # SELECT *
        return new_builder
    
    def where(self, field: str, operator: str, value: DatabaseValue) -> 'DefaultQueryBuilder':
        """添加WHERE条件"""
        if not field:
            raise ValidationError("Field name cannot be empty")
        
        try:
            op = ComparisonOperator(operator)
        except ValueError:
            raise ValidationError(f"Unsupported operator: {operator}")
        
        filter_obj = QueryFilter(field=field, operator=op, value=value)
        
        new_builder = self._clone()
        if new_builder._filters is None:
            new_builder._filters = QueryFilterGroup(
                operator=LogicalOperator.AND,
                filters=[filter_obj]
            )
        else:
            new_builder._filters.add_filter(filter_obj)
        
        return new_builder
    
    def where_in(self, field: str, values: List[DatabaseValue]) -> 'DefaultQueryBuilder':
        """添加WHERE IN条件"""
        return self.where(field, ComparisonOperator.IN.value, values)
    
    def where_not_in(self, field: str, values: List[DatabaseValue]) -> 'DefaultQueryBuilder':
        """添加WHERE NOT IN条件"""
        return self.where(field, ComparisonOperator.NOT_IN.value, values)
    
    def where_like(self, field: str, pattern: str) -> 'DefaultQueryBuilder':
        """添加WHERE LIKE条件"""
        return self.where(field, ComparisonOperator.LIKE.value, pattern)
    
    def where_between(self, field: str, start: DatabaseValue, end: DatabaseValue) -> 'DefaultQueryBuilder':
        """添加WHERE BETWEEN条件"""
        return self.where(field, ComparisonOperator.BETWEEN.value, [start, end])
    
    def where_null(self, field: str) -> 'DefaultQueryBuilder':
        """添加WHERE IS NULL条件"""
        return self.where(field, ComparisonOperator.IS_NULL.value, None)
    
    def where_not_null(self, field: str) -> 'DefaultQueryBuilder':
        """添加WHERE IS NOT NULL条件"""
        return self.where(field, ComparisonOperator.IS_NOT_NULL.value, None)
    
    def or_where(self, field: str, operator: str, value: DatabaseValue) -> 'DefaultQueryBuilder':
        """添加OR WHERE条件"""
        if not field:
            raise ValidationError("Field name cannot be empty")
        
        try:
            op = ComparisonOperator(operator)
        except ValueError:
            raise ValidationError(f"Unsupported operator: {operator}")
        
        filter_obj = QueryFilter(field=field, operator=op, value=value)
        
        new_builder = self._clone()
        if new_builder._filters is None:
            new_builder._filters = QueryFilterGroup(
                operator=LogicalOperator.OR,
                filters=[filter_obj]
            )
        else:
            # 如果当前是AND组，创建新的OR组
            if new_builder._filters.operator == LogicalOperator.AND:
                new_filters = QueryFilterGroup(
                    operator=LogicalOperator.OR,
                    filters=[new_builder._filters, filter_obj]
                )
                new_builder._filters = new_filters
            else:
                new_builder._filters.add_filter(filter_obj)
        
        return new_builder
    
    def join(self, table: str, on_condition: str, join_type: str = "inner") -> 'DefaultQueryBuilder':
        """添加JOIN"""
        if not table:
            raise ValidationError("Join table name cannot be empty")
        if not on_condition:
            raise ValidationError("Join condition cannot be empty")
        
        try:
            jt = JoinType(join_type.lower())
        except ValueError:
            raise ValidationError(f"Unsupported join type: {join_type}")
        
        join_obj = QueryJoin(table=table, join_type=jt, on_condition=on_condition)
        
        new_builder = self._clone()
        new_builder._joins.append(join_obj)
        return new_builder
    
    def left_join(self, table: str, on_condition: str) -> 'DefaultQueryBuilder':
        """添加LEFT JOIN"""
        return self.join(table, on_condition, "left")
    
    def right_join(self, table: str, on_condition: str) -> 'DefaultQueryBuilder':
        """添加RIGHT JOIN"""
        return self.join(table, on_condition, "right")
    
    def inner_join(self, table: str, on_condition: str) -> 'DefaultQueryBuilder':
        """添加INNER JOIN"""
        return self.join(table, on_condition, "inner")
    
    def order_by(self, field: str, direction: str = "asc") -> 'DefaultQueryBuilder':
        """添加排序"""
        if not field:
            raise ValidationError("Order by field cannot be empty")
        
        try:
            order = SortOrder(direction.lower())
        except ValueError:
            raise ValidationError(f"Unsupported sort direction: {direction}")
        
        sort_obj = QuerySort(field=field, order=order)
        
        new_builder = self._clone()
        new_builder._sorts.append(sort_obj)
        return new_builder
    
    def order_by_desc(self, field: str) -> 'DefaultQueryBuilder':
        """添加降序排序"""
        return self.order_by(field, "desc")
    
    def order_by_asc(self, field: str) -> 'DefaultQueryBuilder':
        """添加升序排序"""
        return self.order_by(field, "asc")
    
    def group_by(self, *fields: str) -> 'DefaultQueryBuilder':
        """添加GROUP BY"""
        if not fields:
            raise ValidationError("Group by fields cannot be empty")
        
        group_by_obj = QueryGroupBy(fields=list(fields))
        
        new_builder = self._clone()
        new_builder._group_by = group_by_obj
        return new_builder
    
    def having(self, field: str, operator: str, value: DatabaseValue) -> 'DefaultQueryBuilder':
        """添加HAVING条件"""
        new_builder = self._clone()

        if new_builder._group_by is None:
            raise ValidationError("HAVING requires GROUP BY")

        try:
            op = ComparisonOperator(operator)
        except ValueError:
            raise ValidationError(f"Unsupported operator: {operator}")

        filter_obj = QueryFilter(field=field, operator=op, value=value)

        if new_builder._group_by.having is None:
            new_builder._group_by.having = QueryFilterGroup(
                operator=LogicalOperator.AND,
                filters=[filter_obj]
            )
        else:
            new_builder._group_by.having.add_filter(filter_obj)

        return new_builder
    
    def limit(self, count: int) -> 'DefaultQueryBuilder':
        """设置LIMIT"""
        if count <= 0:
            raise ValidationError("Limit must be positive")
        
        new_builder = self._clone()
        new_builder._limit_value = count
        return new_builder
    
    def offset(self, count: int) -> 'DefaultQueryBuilder':
        """设置OFFSET"""
        if count < 0:
            raise ValidationError("Offset must be non-negative")
        
        new_builder = self._clone()
        new_builder._offset_value = count
        return new_builder
    
    def distinct(self) -> 'DefaultQueryBuilder':
        """设置DISTINCT"""
        new_builder = self._clone()
        new_builder._distinct_flag = True
        return new_builder
    
    def for_update(self) -> 'DefaultQueryBuilder':
        """设置FOR UPDATE"""
        new_builder = self._clone()
        new_builder._for_update_flag = True
        return new_builder
    
    def timeout(self, seconds: float) -> 'DefaultQueryBuilder':
        """设置查询超时"""
        if seconds <= 0:
            raise ValidationError("Timeout must be positive")

        new_builder = self._clone()
        new_builder._timeout_value = seconds
        return new_builder

    def add_filter(self, filter_obj: Union[QueryFilter, QueryFilterGroup]) -> 'DefaultQueryBuilder':
        """添加自定义过滤器"""
        new_builder = self._clone()
        if new_builder._filters is None:
            if isinstance(filter_obj, QueryFilterGroup):
                new_builder._filters = filter_obj
            else:
                new_builder._filters = QueryFilterGroup(
                    operator=LogicalOperator.AND,
                    filters=[filter_obj]
                )
        else:
            new_builder._filters.add_filter(filter_obj)

        return new_builder
    
    def build(self) -> QueryRequest:
        """构建查询请求"""
        if not self._table_name:
            raise ValidationError("Table name is required")
        
        return QueryRequest(
            table=self._table_name,
            columns=self._columns,
            filters=self._filters,
            joins=self._joins if self._joins else None,
            sorts=self._sorts if self._sorts else None,
            group_by=self._group_by,
            aggregations=self._aggregations if self._aggregations else None,
            limit=self._limit_value,
            offset=self._offset_value,
            distinct=self._distinct_flag,
            for_update=self._for_update_flag,
            timeout=self._timeout_value
        )
    
    def execute(self):
        """执行查询（同步）"""
        if not self._database_client:
            raise ValidationError("Database client is required for execution")
        
        request = self.build()
        return self._database_client.query(request)
    
    async def aexecute(self):
        """执行查询（异步）"""
        if not self._database_client:
            raise ValidationError("Database client is required for execution")
        
        request = self.build()
        return await self._database_client.aquery(request)
    
    def _clone(self) -> 'DefaultQueryBuilder':
        """克隆构建器"""
        new_builder = DefaultQueryBuilder(self._database_client)
        new_builder._table_name = self._table_name
        new_builder._columns = self._columns.copy() if self._columns else None
        new_builder._filters = deepcopy(self._filters) if self._filters else None
        new_builder._joins = self._joins.copy()
        new_builder._sorts = self._sorts.copy()
        new_builder._group_by = deepcopy(self._group_by) if self._group_by else None
        new_builder._aggregations = self._aggregations.copy()
        new_builder._limit_value = self._limit_value
        new_builder._offset_value = self._offset_value
        new_builder._distinct_flag = self._distinct_flag
        new_builder._for_update_flag = self._for_update_flag
        new_builder._timeout_value = self._timeout_value
        return new_builder
    
    def __repr__(self) -> str:
        return f"QueryBuilder(table={self._table_name})"

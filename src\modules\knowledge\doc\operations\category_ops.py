"""
CategoryOperation - 文档别名和类别相关的数据库操作类

提供以下功能：
- DocumentCategory 表的CRUD操作 (文档类别关联管理)
- Category 表的完整CRUD操作 (类别管理)
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from uuid import uuid4

from service import get_client, get_config
from .db_wrapper import DatabaseOperationWrapper

logger = logging.getLogger(__name__)


class DocumentCategoryOperation:
    """文档类别关联操作类 - 负责doc_dev_document_categories表的CRUD操作"""
    
    def __init__(self, rdb_client=None, rdb_config_path=None):
        """
        初始化文档类别关联操作类
        
        Args:
            rdb_client: 关系型数据库客户端，如果为None则自动从配置获取
            rdb_config_path: 数据库配置路径，用于覆盖默认配置
        """
        self._rdb_client = rdb_client
        self._rdb_config_path = rdb_config_path
        self._rdb_client_wrapper = None
        self.table_name = "doc_dev_document_categories"
    
    async def _ensure_rdb_client(self):
        """确保关系型数据库客户端已初始化"""
        if self._rdb_client_wrapper is None:
            if self._rdb_client is None:
                if self._rdb_config_path is None:
                    cfg = await get_config()
                    self._rdb_config_path = cfg.knowledge.doc.rdb
                
                self._rdb_client = await get_client(self._rdb_config_path)
                logger.debug(f"从配置获取RDB客户端: {self._rdb_config_path}")
            
            self._rdb_client_wrapper = DatabaseOperationWrapper(self._rdb_client)
        
        return self._rdb_client_wrapper
        
    async def create_document_category(
        self,
        doc_id: str,
        cate_id: str,
        cate_layer: int,
        doc_name: str,
        doc_status: Optional[str] = None
    ) -> int:
        """
        创建文档类别关联记录
        
        Args:
            doc_id: 文档ID
            cate_id: 类别ID
            cate_layer: 类别层级
            doc_name: 文档名称
            doc_status: 文档状态
            
        Returns:
            int: 创建的关联记录ID
        """
        try:
            category_data = {
                "doc_id": doc_id,
                "cate_id": cate_id,
                "cate_layer": cate_layer,
                "doc_name": doc_name,
                "doc_status": doc_status
            }
            
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.insert(self.table_name, [category_data])
            
            if success:
                # 获取刚插入的记录ID
                result = rdb_client.select(
                    table=self.table_name,
                    condition={"doc_id": doc_id, "cate_id": cate_id},
                    order_by=["id DESC"],
                    limit=1
                )
                
                if result:
                    category_id = result[0]["id"]
                    logger.info(f"文档类别关联创建成功: {category_id}")
                    return category_id
                else:
                    raise Exception("无法获取创建的关联记录ID")
            else:
                raise Exception("数据库插入操作失败")
                
        except Exception as e:
            logger.error(f"创建文档类别关联失败: {e}")
            raise Exception(f"创建文档类别关联失败: {str(e)}")
    
    async def get_categories_by_doc_id(self, doc_id: str) -> List[Dict[str, Any]]:
        """
        根据文档ID查询类别关联列表
        
        Args:
            doc_id: 文档ID
            
        Returns:
            List[Dict[str, Any]]: 类别关联记录列表
        """
        try:
            condition: Dict[str, Any] = {"doc_id": doc_id}
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.table_name,
                condition=condition,
                order_by=["cate_layer ASC"]
            )
            
            logger.debug(f"查询到 {len(result)} 个类别关联 (doc_id: {doc_id})")
            return result
            
        except Exception as e:
            logger.error(f"查询文档类别关联失败: {e}")
            raise Exception(f"查询文档类别关联失败: {str(e)}")
    
    async def get_documents_by_category_id(self, cate_id: str) -> List[Dict[str, Any]]:
        """
        根据类别ID查询文档关联列表
        
        Args:
            cate_id: 类别ID
            
        Returns:
            List[Dict[str, Any]]: 文档关联记录列表
        """
        try:
            condition: Dict[str, Any] = {"cate_id": cate_id}
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.table_name,
                condition=condition,
                order_by=["id DESC"]
            )
            
            logger.debug(f"查询到 {len(result)} 个文档关联 (cate_id: {cate_id})")
            return result
            
        except Exception as e:
            logger.error(f"查询类别文档关联失败: {e}")
            raise Exception(f"查询类别文档关联失败: {str(e)}")
    
    async def update_document_category(
        self,
        doc_id: str,
        cate_id: str,
        update_data: Dict[str, Any]
    ) -> bool:
        """
        更新文档类别关联记录
        
        Args:
            doc_id: 文档ID
            cate_id: 类别ID
            update_data: 要更新的数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.update(
                table=self.table_name,
                filter={"doc_id": doc_id, "cate_id": cate_id},
                data=update_data
            )
            
            if success:
                logger.info(f"文档类别关联更新成功: doc_id={doc_id}, cate_id={cate_id}")
            else:
                logger.warning(f"文档类别关联更新失败，可能不存在: doc_id={doc_id}, cate_id={cate_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"更新文档类别关联失败: {e}")
            raise Exception(f"更新文档类别关联失败: {str(e)}")
    
    async def delete_document_category(self, doc_id: str, cate_id: str) -> bool:
        """
        删除特定的文档类别关联
        
        Args:
            doc_id: 文档ID
            cate_id: 类别ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.delete(
                table=self.table_name,
                filter={"doc_id": doc_id, "cate_id": cate_id}
            )
            
            if success:
                logger.info(f"文档类别关联删除成功: doc_id={doc_id}, cate_id={cate_id}")
            else:
                logger.warning(f"文档类别关联删除失败，可能不存在: doc_id={doc_id}, cate_id={cate_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"删除文档类别关联失败: {e}")
            raise Exception(f"删除文档类别关联失败: {str(e)}")
    
    async def delete_categories_by_doc_id(self, doc_id: str) -> bool:
        """
        删除文档的所有类别关联
        
        Args:
            doc_id: 文档ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.delete(
                table=self.table_name,
                filter={"doc_id": doc_id}
            )
            
            if success:
                logger.info(f"文档所有类别关联删除成功: {doc_id}")
            else:
                logger.warning(f"文档类别关联删除失败，可能不存在: {doc_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"删除文档所有类别关联失败: {e}")
            raise Exception(f"删除文档所有类别关联失败: {str(e)}")


class CategoryOperation:
    """类别操作类 - 负责doc_dev_categories表的完整CRUD操作"""
    
    def __init__(self, rdb_client=None, rdb_config_path=None):
        """
        初始化类别操作类
        
        Args:
            rdb_client: 关系型数据库客户端，如果为None则自动从配置获取
            rdb_config_path: 数据库配置路径，用于覆盖默认配置
        """
        self._rdb_client = rdb_client
        self._rdb_config_path = rdb_config_path
        self._rdb_client_wrapper = None
        self.table_name = "doc_dev_categories"
        self.relationship_table_name = "doc_dev_category_relationship"
    
    async def _ensure_rdb_client(self):
        """确保关系型数据库客户端已初始化"""
        if self._rdb_client_wrapper is None:
            if self._rdb_client is None:
                if self._rdb_config_path is None:
                    cfg = await get_config()
                    self._rdb_config_path = cfg.knowledge.doc.rdb
                
                self._rdb_client = await get_client(self._rdb_config_path)
                logger.debug(f"从配置获取RDB客户端: {self._rdb_config_path}")
            
            self._rdb_client_wrapper = DatabaseOperationWrapper(self._rdb_client)
        
        return self._rdb_client_wrapper
        
    # ==========================================
    # 类别CRUD操作
    # ==========================================
    
    async def create_category(
        self,
        cate_id: str,
        cate_name: str,
        cate_layer: int,
        parent_id: Optional[str] = None,
        cate_status: str = "active"
    ) -> str:
        """
        创建类别
        
        Args:
            cate_id: 类别ID
            cate_name: 类别名称  
            cate_layer: 类别层级
            parent_id: 父类别ID
            cate_status: 类别状态
            
        Returns:
            str: 创建的类别ID
        """
        try:
            # 检查类别是否已存在
            existing_category = await self.get_category_by_id(cate_id)
            if existing_category:
                logger.info(f"类别已存在，跳过创建: {cate_id}")
                return cate_id
            
            category_data = {
                "cate_id": cate_id,
                "cate_name": cate_name,
                "cate_layer": cate_layer,
                "parent_id": parent_id,
                "cate_status": cate_status,
                "created_time": datetime.now(),
                "updated_time": None,
                "is_active": True
            }
            
            rdb_wrapper = await self._ensure_rdb_client()
            success = rdb_wrapper.insert(self.table_name, [category_data])
            if success:
                logger.info(f"类别创建成功: {cate_id}")
                return cate_id
            else:
                raise Exception("数据库插入操作失败")
                
        except Exception as e:
            logger.error(f"创建类别失败: {e}")
            raise Exception(f"创建类别失败: {str(e)}")
    
    async def get_category_by_id(self, cate_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID查询类别
        
        Args:
            cate_id: 类别ID
            
        Returns:
            Optional[Dict[str, Any]]: 类别记录，如果不存在则返回None
        """
        try:
            condition: Dict[str, Any] = {"cate_id": cate_id}
            rdb_wrapper = await self._ensure_rdb_client()
            result = rdb_wrapper.select(
                table=self.table_name,
                condition=condition,
                limit=1
            )
            
            if result:
                logger.debug(f"查询到类别: {cate_id}")
                return result[0]
            else:
                logger.debug(f"类别不存在: {cate_id}")
                return None
                
        except Exception as e:
            logger.error(f"查询类别失败: {e}")
            raise Exception(f"查询类别失败: {str(e)}")
    
    async def get_categories_by_layer(self, cate_layer: int) -> List[Dict[str, Any]]:
        """
        根据层级查询类别列表
        
        Args:
            cate_layer: 类别层级
            
        Returns:
            List[Dict[str, Any]]: 类别记录列表
        """
        try:
            condition: Dict[str, Any] = {"cate_layer": cate_layer}
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.table_name,
                condition=condition,
                order_by=["cate_name ASC"]
            )
            
            logger.debug(f"查询到 {len(result)} 个层级 {cate_layer} 的类别")
            return result
            
        except Exception as e:
            logger.error(f"查询类别列表失败: {e}")
            raise Exception(f"查询类别列表失败: {str(e)}")
    
    async def get_child_categories(self, parent_id: str) -> List[Dict[str, Any]]:
        """
        查询子类别列表
        
        Args:
            parent_id: 父类别ID
            
        Returns:
            List[Dict[str, Any]]: 子类别记录列表
        """
        try:
            condition: Dict[str, Any] = {"parent_id": parent_id}
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.table_name,
                condition=condition,
                order_by=["cate_name ASC"]
            )
            
            logger.debug(f"查询到 {len(result)} 个子类别 (parent_id: {parent_id})")
            return result
            
        except Exception as e:
            logger.error(f"查询子类别失败: {e}")
            raise Exception(f"查询子类别失败: {str(e)}")
    
    async def get_all_categories(
        self,
        cate_status: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        查询所有类别
        
        Args:
            cate_status: 类别状态过滤
            limit: 限制返回数量
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 类别记录列表
        """
        try:
            condition: Dict[str, Any] = {}
            if cate_status:
                condition["cate_status"] = cate_status
            
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.table_name,
                condition=condition if condition else None,
                limit=limit,
                offset=offset,
                order_by=["cate_layer ASC", "cate_name ASC"]
            )
            
            logger.debug(f"查询到 {len(result)} 个类别")
            return result
            
        except Exception as e:
            logger.error(f"查询所有类别失败: {e}")
            raise Exception(f"查询所有类别失败: {str(e)}")
    
    async def update_category(
        self,
        cate_id: str,
        update_data: Dict[str, Any]
    ) -> bool:
        """
        更新类别记录
        
        Args:
            cate_id: 类别ID
            update_data: 要更新的数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.update(
                table=self.table_name,
                filter={"cate_id": cate_id},
                data=update_data
            )
            
            if success:
                logger.info(f"类别更新成功: {cate_id}")
            else:
                logger.warning(f"类别更新失败，可能不存在: {cate_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"更新类别失败: {e}")
            raise Exception(f"更新类别失败: {str(e)}")
    
    async def delete_category(self, cate_id: str) -> bool:
        """
        删除类别记录
        
        Args:
            cate_id: 类别ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.delete(
                table=self.table_name,
                filter={"cate_id": cate_id}
            )
            
            if success:
                logger.info(f"类别删除成功: {cate_id}")
            else:
                logger.warning(f"类别删除失败，可能不存在: {cate_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"删除类别失败: {e}")
            raise Exception(f"删除类别失败: {str(e)}")
    
    async def search_categories_by_name(
        self,
        name_pattern: str,
        cate_status: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        根据名称模式搜索类别
        
        Args:
            name_pattern: 名称模式（用于LIKE搜索）
            cate_status: 类别状态过滤
            
        Returns:
            List[Dict[str, Any]]: 匹配的类别记录列表
        """
        try:
            # 注意：这里使用精确匹配，如需模糊搜索可能需要使用execute_query方法
            condition: Dict[str, Any] = {"cate_name": name_pattern}
            if cate_status:
                condition["cate_status"] = cate_status
            
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.table_name,
                condition=condition,
                order_by=["cate_layer ASC", "cate_name ASC"]
            )
            
            logger.debug(f"根据名称搜索到 {len(result)} 个类别")
            return result
            
        except Exception as e:
            logger.error(f"根据名称搜索类别失败: {e}")
            raise Exception(f"根据名称搜索类别失败: {str(e)}")
    
    # ==========================================
    # 类别关系管理操作
    # ==========================================
    
    async def create_category_relationship(
        self,
        source_cate_id: str,
        target_cate_id: str,
        rel_type: Optional[str] = None
    ) -> int:
        """
        创建类别关系记录
        
        Args:
            source_cate_id: 源类别ID
            target_cate_id: 目标类别ID
            rel_type: 关系类型
            
        Returns:
            int: 创建的关系记录ID
            
        Raises:
            Exception: 创建失败时抛出异常
        """
        try:
            # 先检查关系是否已存在
            existing_relationship = await self._check_relationship_exists(
                source_cate_id, target_cate_id, rel_type
            )
            if existing_relationship:
                logger.info(f"类别关系已存在，跳过创建: {existing_relationship['id']}")
                return existing_relationship['id']
            
            relationship_data = {
                "source_cate_id": source_cate_id,
                "target_cate_id": target_cate_id,
                "rel_type": rel_type
            }
            
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.insert(self.relationship_table_name, [relationship_data])
            
            if success:
                # 获取刚插入的记录ID
                result = rdb_client.select(
                    table=self.relationship_table_name,
                    condition={"source_cate_id": source_cate_id, "target_cate_id": target_cate_id, "rel_type": rel_type},
                    order_by=["id DESC"],
                    limit=1
                )
                
                if result:
                    relationship_id = result[0]["id"]
                    logger.info(f"类别关系创建成功: {relationship_id}")
                    return relationship_id
                else:
                    raise Exception("无法获取创建的关系记录ID")
            else:
                raise Exception("数据库插入操作失败")
                
        except Exception as e:
            # 处理重复键约束错误
            error_msg = str(e)
            if "Duplicate entry" in error_msg or "duplicate key" in error_msg:
                # 重复键错误，尝试获取已存在的记录
                try:
                    existing_relationship = await self._check_relationship_exists(
                        source_cate_id, target_cate_id, rel_type
                    )
                    if existing_relationship:
                        logger.info(f"类别关系已存在，返回现有ID: {existing_relationship['id']}")
                        return existing_relationship['id']
                except Exception:
                    pass  # 忽略查询错误，继续抛出原错误
            
            logger.error(f"创建类别关系失败: {e}")
            raise Exception(f"创建类别关系失败: {str(e)}")
    
    async def _check_relationship_exists(
        self,
        source_cate_id: str,
        target_cate_id: str,
        rel_type: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        检查类别关系是否已存在
        
        Args:
            source_cate_id: 源类别ID
            target_cate_id: 目标类别ID
            rel_type: 关系类型
            
        Returns:
            Optional[Dict[str, Any]]: 如果存在返回关系记录，否则返回None
        """
        try:
            condition = {
                "source_cate_id": source_cate_id,
                "target_cate_id": target_cate_id,
                "rel_type": rel_type
            }
            
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.relationship_table_name,
                condition=condition,
                limit=1
            )
            
            return result[0] if result else None
            
        except Exception as e:
            logger.error(f"检查类别关系存在性失败: {e}")
            return None
    
    async def get_category_relationships_by_source(
        self, 
        source_cate_id: str,
        rel_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        根据源类别ID查询关系列表
        
        Args:
            source_cate_id: 源类别ID
            rel_type: 关系类型过滤
            
        Returns:
            List[Dict[str, Any]]: 关系记录列表
        """
        try:
            condition: Dict[str, Any] = {"source_cate_id": source_cate_id}
            if rel_type:
                condition["rel_type"] = rel_type
                
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.relationship_table_name,
                condition=condition,
                order_by=["id ASC"]
            )
            
            logger.debug(f"查询到 {len(result)} 个关系记录 (source_cate_id: {source_cate_id})")
            return result
            
        except Exception as e:
            logger.error(f"查询类别关系失败: {e}")
            raise Exception(f"查询类别关系失败: {str(e)}")
    
    async def get_category_relationships_by_target(
        self, 
        target_cate_id: str,
        rel_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        根据目标类别ID查询关系列表
        
        Args:
            target_cate_id: 目标类别ID
            rel_type: 关系类型过滤
            
        Returns:
            List[Dict[str, Any]]: 关系记录列表
        """
        try:
            condition: Dict[str, Any] = {"target_cate_id": target_cate_id}
            if rel_type:
                condition["rel_type"] = rel_type
                
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.relationship_table_name,
                condition=condition,
                order_by=["id ASC"]
            )
            
            logger.debug(f"查询到 {len(result)} 个关系记录 (target_cate_id: {target_cate_id})")
            return result
            
        except Exception as e:
            logger.error(f"查询类别关系失败: {e}")
            raise Exception(f"查询类别关系失败: {str(e)}")
    
    async def get_all_category_relationships(
        self,
        rel_type: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        查询所有类别关系
        
        Args:
            rel_type: 关系类型过滤
            limit: 限制返回数量
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 关系记录列表
        """
        try:
            condition: Dict[str, Any] = {}
            if rel_type:
                condition["rel_type"] = rel_type
            
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.relationship_table_name,
                condition=condition if condition else None,
                limit=limit,
                offset=offset,
                order_by=["id ASC"]
            )
            
            logger.debug(f"查询到 {len(result)} 个类别关系")
            return result
            
        except Exception as e:
            logger.error(f"查询所有类别关系失败: {e}")
            raise Exception(f"查询所有类别关系失败: {str(e)}")
    
    async def delete_category_relationship(
        self, 
        relationship_id: int
    ) -> bool:
        """
        删除类别关系记录
        
        Args:
            relationship_id: 关系记录ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.delete(
                table=self.relationship_table_name,
                filter={"id": relationship_id}
            )
            
            if success:
                logger.info(f"类别关系删除成功: {relationship_id}")
            else:
                logger.warning(f"类别关系删除失败，可能不存在: {relationship_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"删除类别关系失败: {e}")
            raise Exception(f"删除类别关系失败: {str(e)}")
    
    async def delete_category_relationships_by_source(
        self, 
        source_cate_id: str,
        rel_type: Optional[str] = None
    ) -> bool:
        """
        删除源类别的所有关系记录
        
        Args:
            source_cate_id: 源类别ID
            rel_type: 关系类型，为None时删除所有类型的关系
            
        Returns:
            bool: 删除是否成功
        """
        try:
            filter_condition = {"source_cate_id": source_cate_id}
            if rel_type:
                filter_condition["rel_type"] = rel_type
                
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.delete(
                table=self.relationship_table_name,
                filter=filter_condition
            )
            
            if success:
                logger.info(f"源类别关系删除成功: {source_cate_id}")
            else:
                logger.warning(f"源类别关系删除失败，可能不存在: {source_cate_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"删除源类别关系失败: {e}")
            raise Exception(f"删除源类别关系失败: {str(e)}")
    
    async def delete_category_relationships_by_target(
        self, 
        target_cate_id: str,
        rel_type: Optional[str] = None
    ) -> bool:
        """
        删除目标类别的所有关系记录
        
        Args:
            target_cate_id: 目标类别ID
            rel_type: 关系类型，为None时删除所有类型的关系
            
        Returns:
            bool: 删除是否成功
        """
        try:
            filter_condition = {"target_cate_id": target_cate_id}
            if rel_type:
                filter_condition["rel_type"] = rel_type
                
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.delete(
                table=self.relationship_table_name,
                filter=filter_condition
            )
            
            if success:
                logger.info(f"目标类别关系删除成功: {target_cate_id}")
            else:
                logger.warning(f"目标类别关系删除失败，可能不存在: {target_cate_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"删除目标类别关系失败: {e}")
            raise Exception(f"删除目标类别关系失败: {str(e)}")


class CombinedCategoryOperation:
    """组合的类别操作类 - 包含所有类别相关操作"""
    
    def __init__(self, rdb_client=None, vdb_client=None, rdb_config_path=None, vdb_config_path=None):
        """
        初始化组合类别操作类
        
        Args:
            rdb_client: 关系型数据库客户端，如果为None则自动从配置获取
            vdb_client: 向量数据库客户端，如果为None则自动从配置获取
            rdb_config_path: 关系型数据库配置路径，用于覆盖默认配置
            vdb_config_path: 向量数据库配置路径，用于覆盖默认配置
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        
        # 初始化子操作类
        self.doc_category_ops = DocumentCategoryOperation(rdb_client, rdb_config_path)
        self.category_ops = CategoryOperation(rdb_client, rdb_config_path)


async def main():
    """测试函数 - 配置化使用示例"""
    try:
        print("📚 CategoryOperation 配置化使用示例")
        print("=" * 50)
        
        # 创建操作实例（自动从配置获取客户端）
        category_operation = CombinedCategoryOperation()
        
        # 测试类别操作
        print("\n=== 1. 测试类别操作 ===")
        cate_id = await category_operation.category_ops.create_category(
            cate_id="test-category-001",
            cate_name="测试类别",
            cate_layer=1,
            parent_id=None,
            cate_status="active"
        )
        print(f"✅ 创建类别成功，ID: {cate_id}")
        
        # 查询类别
        category = await category_operation.category_ops.get_category_by_id("test-category-001")
        print(f"✅ 查询到类别: {category}")
        
        # 测试文档类别关联操作
        print("\n=== 2. 测试文档类别关联操作 ===")
        category_id = await category_operation.doc_category_ops.create_document_category(
            doc_id="test-doc-001",
            cate_id="test-category-001",
            cate_layer=1,
            doc_name="测试文档",
            doc_status="active"
        )
        print(f"✅ 创建类别关联成功，ID: {category_id}")
        
        # 查询文档的类别关联
        doc_categories = await category_operation.doc_category_ops.get_categories_by_doc_id("test-doc-001")
        print(f"✅ 查询到类别关联: {len(doc_categories)} 个")
        
        # 测试类别关系操作
        print("\n=== 3. 测试类别关系操作 ===")
        # 先创建另一个类别用于建立关系
        cate_id_2 = await category_operation.category_ops.create_category(
            cate_id="test-category-002",
            cate_name="测试类别2",
            cate_layer=2,
            parent_id="test-category-001",
            cate_status="active"
        )
        print(f"✅ 创建第二个类别成功，ID: {cate_id_2}")
        
        # 创建类别关系
        rel_id = await category_operation.category_ops.create_category_relationship(
            source_cate_id="test-category-001",
            target_cate_id="test-category-002",
            rel_type="parent_child"
        )
        print(f"✅ 创建类别关系成功，ID: {rel_id}")
        
        # 查询类别关系
        relationships = await category_operation.category_ops.get_category_relationships_by_source("test-category-001")
        print(f"✅ 查询到源类别关系: {len(relationships)} 个")
        
        relationships = await category_operation.category_ops.get_category_relationships_by_target("test-category-002")
        print(f"✅ 查询到目标类别关系: {len(relationships)} 个")
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！CategoryOperation 配置化功能正常")
        
        print("\n💡 主要功能验证:")
        print("1. ✅ 内化客户端管理 - 自动获取RDB客户端")
        print("2. ✅ 配置驱动 - 通过 knowledge.doc.rdb 配置动态获取客户端")
        print("3. ✅ 文档类别关联 - 支持文档与类别的关联管理")
        print("4. ✅ 类别管理 - 支持类别的完整CRUD操作")
        print("5. ✅ 类别关系 - 支持类别间关系的管理")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")


if __name__ == "__main__":
    asyncio.run(main())

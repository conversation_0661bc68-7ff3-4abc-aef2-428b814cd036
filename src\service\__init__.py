"""
Service Layer - 统一服务层入口

提供简化的API接口，支持：
- 统一配置管理（静态+动态）
- 客户端管理（单例模式）
- 连接池管理（多连接模式）

"""

import asyncio
import logging
from typing import Any, Dict, Optional, Union
from omegaconf import DictConfig

from .config.manager import ConfigManager
from .client.factory import ClientFactory
from .exceptions import ServiceError, ConfigError, ClientError

logger = logging.getLogger(__name__)

# 全局实例
_config_manager: Optional[ConfigManager] = None
_client_factory: Optional[ClientFactory] = None


async def get_config(source: str = "hydra", **kwargs) -> ConfigManager:
    """
    获取统一配置管理器

    Args:
        source: 配置源 ("hydra", "database", "mixed")
        **kwargs: 额外参数

    Returns:
        ConfigManager实例

    Examples:
        >>> cfg = await get_config()  # 默认Hydra静态配置
        >>> cfg = await get_config("database")  # 数据库动态配置
        >>> cfg = await get_config("mixed")  # 混合配置
    """
    global _config_manager

    # 检查是否需要重新初始化
    if (_config_manager is None or
        not _config_manager._initialized or
        _config_manager._source != source):

        # 如果存在旧的配置管理器，先清理
        if _config_manager is not None:
            await _config_manager.cleanup()

        _config_manager = ConfigManager()
        await _config_manager.initialize(source=source, **kwargs)
        logger.info(f"配置管理器初始化完成，配置源: {source}")

    return _config_manager


async def get_client(config_path: Union[str, DictConfig],
                    priority: Optional[str] = None,
                    **kwargs) -> Any:
    """
    获取客户端实例 - 支持优先级的统一入口

    Args:
        config_path: 配置路径字符串或配置对象
        priority: 可选的服务优先级 ('high', 'standard', 'low')
        **kwargs: 额外参数

    Returns:
        客户端实例

    Examples:
        # 基础用法（向后兼容）
        >>> mysql_client = await get_client("database.rdbs.mysql")
        >>> pgvector_client = await get_client("database.vdbs.pgvector")

        # 使用优先级参数 - 新的推荐方式
        >>> high_mysql = await get_client("database.rdbs.mysql", priority='high')
        >>> low_pgvector = await get_client("database.vdbs.pgvector", priority='low')

        # 使用配置对象
        >>> cfg = await get_config()
        >>> mysql_client = await get_client(cfg.database.rdbs.mysql, priority='high')

        # 直接使用完整配置路径
        >>> high_mysql = await get_client("database.rdbs.mysql_high_priority")

        # 自定义配置
        >>> custom_config = OmegaConf.create({...})
        >>> custom_client = await get_client(custom_config)

    Note:
        - 支持优先级参数，实现物理隔离的连接池管理
        - 客户端内部自动管理连接池（Base层）
        - 支持单例模式，相同配置返回相同实例
        - 向后兼容：不提供priority参数时行为与原来完全一致
        - 优先级配置：high(大连接池), standard(平衡), low(小连接池)
    """
    global _client_factory

    if _client_factory is None:
        _client_factory = ClientFactory()
        await _client_factory.initialize()
        logger.info("客户端工厂初始化完成")

    try:
        client = await _client_factory.get_client(config_path, priority=priority, **kwargs)
        logger.debug(f"获取客户端成功: {type(client).__name__}")
        return client
    except Exception as e:
        logger.error(f"获取客户端失败: {e}")
        raise ClientError(f"Failed to get client: {e}") from e


# 便捷的优先级客户端获取函数 - 基于新的 get_client API
async def get_high_priority_client(config: Union[str, DictConfig], **kwargs) -> Any:
    """
    获取高优先级客户端 - 用于关键业务场景

    Args:
        config: 配置路径或配置对象
        **kwargs: 额外参数

    Returns:
        高优先级客户端实例

    Example:
        >>> mysql_client = await get_high_priority_client("database.rdbs.mysql")
        >>> pgvector_client = await get_high_priority_client("database.vdbs.pgvector")
    """
    return await get_client(config, priority="high", **kwargs)


async def get_standard_priority_client(config: Union[str, DictConfig], **kwargs) -> Any:
    """
    获取标准优先级客户端 - 用于常规业务场景

    Args:
        config: 配置路径或配置对象
        **kwargs: 额外参数

    Returns:
        标准优先级客户端实例

    Example:
        >>> mysql_client = await get_standard_priority_client("database.rdbs.mysql")
        >>> pgvector_client = await get_standard_priority_client("database.vdbs.pgvector")
    """
    return await get_client(config, priority="standard", **kwargs)


async def get_low_priority_client(config: Union[str, DictConfig], **kwargs) -> Any:
    """
    获取低优先级客户端 - 用于批处理和后台任务

    Args:
        config: 配置路径或配置对象
        **kwargs: 额外参数

    Returns:
        低优先级客户端实例

    Example:
        >>> mysql_client = await get_low_priority_client("database.rdbs.mysql")
        >>> pgvector_client = await get_low_priority_client("database.vdbs.pgvector")
    """
    return await get_client(config, priority="low", **kwargs)


async def get_provider(config_path: Union[str, DictConfig], **kwargs) -> Any:
    """
    ⚠️ DEPRECATED: 获取连接池提供者 - 此方法已完全废弃

    🔄 迁移指南：
    请使用 get_client() 方法替代 get_provider()

    旧用法 -> 新用法：

    # ❌ 旧的Provider用法
    provider = await get_provider("database.rdbs.mysql")
    async with provider.get_connection() as conn:
        result = await conn.afetch_all("SELECT * FROM users")

    # ✅ 新的Client用法
    client = await get_client("database.rdbs.mysql")
    result = await client.afetch_all("SELECT * FROM users")

    迁移优势：
    1. 更简洁的API - 无需 async with 上下文管理
    2. 更好的性能 - 移除了双层连接池（3.61倍性能提升）
    3. 统一的接口 - 数据库和AI模型使用相同的获取方式
    4. 符合业界标准 - 对齐Spring Boot、Django等主流框架

    Args:
        config_path: 配置路径或配置对象
        **kwargs: 额外参数

    Raises:
        NotImplementedError: Provider层已被完全移除
    """
    import warnings
    warnings.warn(
        "get_provider() has been completely removed. Use get_client() instead. "
        "Provider layer was removed due to over-engineering. "
        "See migration guide in docstring.",
        DeprecationWarning,
        stacklevel=2
    )

    raise NotImplementedError(
        "Provider layer has been completely removed as it was over-engineered. "
        "Please use get_client() instead. "
        "Migration: replace 'await get_provider(...)' + 'async with provider.get_connection()' "
        "with simple 'await get_client(...)' calls. "
        "See service/archive/README.md for detailed migration guide."
    )


async def cleanup():
    """
    清理所有服务层资源
    """
    global _config_manager, _client_factory

    try:
        logger.info("开始清理服务层资源...")

        # 清理客户端工厂
        if _client_factory:
            await _client_factory.cleanup()
            _client_factory = None
            logger.info("客户端工厂清理完成")

        # 清理配置管理器
        if _config_manager:
            await _config_manager.cleanup()
            _config_manager = None
            logger.info("配置管理器清理完成")

        # 给异步任务更多时间完成清理，特别是数据库连接
        import asyncio
        await asyncio.sleep(0.5)  # 增加等待时间

        # 强制垃圾回收，确保所有弱引用对象被清理
        import gc
        gc.collect()
        await asyncio.sleep(0.1)  # 再给一点时间

        logger.info("服务层资源清理完成")

    except Exception as e:
        # 清理过程中的错误不应该影响程序退出
        logger.warning(f"清理资源时出现警告: {e}")


def graceful_cleanup():
    """
    优雅清理资源（同步版本，用于程序退出时）
    """
    import asyncio
    import warnings

    try:
        # 尝试获取当前事件循环
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果事件循环正在运行，创建任务
            loop.create_task(cleanup())
        else:
            # 如果事件循环未运行，直接运行
            loop.run_until_complete(cleanup())
    except RuntimeError:
        # 如果没有事件循环或事件循环已关闭，忽略清理
        # 这种情况下连接会在进程退出时自动清理
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            pass


# 便捷函数
async def init_service(config_source: str = "hydra", **kwargs):
    """
    初始化服务层
    
    Args:
        config_source: 配置源
        **kwargs: 额外参数
    """
    logger.info(f"初始化服务层，配置源: {config_source}")
    await get_config(config_source, **kwargs)
    logger.info("服务层初始化完成")


# 上下文管理器
class ServiceContext:
    """服务层上下文管理器"""
    
    def __init__(self, config_source: str = "hydra", **kwargs):
        self.config_source = config_source
        self.kwargs = kwargs
    
    async def __aenter__(self):
        await init_service(self.config_source, **self.kwargs)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await cleanup()


# 清理函数注册标志
_cleanup_registered = False

def register_cleanup():
    """
    手动注册程序退出时的清理函数

    注意：只有在确定需要自动清理时才调用此函数
    通常在应用程序的主入口点调用
    """
    global _cleanup_registered
    if not _cleanup_registered:
        import atexit
        atexit.register(graceful_cleanup)
        _cleanup_registered = True


# 导出主要接口
__all__ = [
    "get_config",
    "get_client",  # 主要入口，现在支持 priority 参数
    "get_high_priority_client",
    "get_standard_priority_client",
    "get_low_priority_client",
    "get_provider",
    "cleanup",
    "register_cleanup",
    "init_service",
    "ServiceContext",
    "ServiceError",
    "ConfigError",
    "ClientError"
]

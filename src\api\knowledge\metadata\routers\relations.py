"""
元数据关联关系管理API路由

提供关联关系的完整CRUD操作接口，参照DD系统的设计模式。
"""

from fastapi import APIRouter, HTTPException, Query, Depends, Path
import logging

# 标准化日志导入
logger = logging.getLogger(__name__)

from api.knowledge.models.response_models import (
    ListResponse, CreateResponse, UpdateResponse, DeleteResponse, DetailResponse
)
from ..models import RelationCreateRequest, RelationUpdateRequest
from ..dependencies import get_metadata_crud, validate_knowledge_id, validate_pagination

# 创建路由器
router = APIRouter(tags=["元数据关联关系管理"], prefix="/relations")


@router.post("/", response_model=CreateResponse, summary="创建关联关系")
async def create_relation(
    knowledge_id: str = Query(..., description="知识库ID"),
    request: RelationCreateRequest = ...,
    metadata_crud = Depends(get_metadata_crud)
):
    """创建新关联关系"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        relation_data = request.model_dump()
        relation_id = await metadata_crud.create_relation(knowledge_id, relation_data)
        
        return CreateResponse(
            success=True,
            message="关联关系创建成功",
            data={"relation_id": relation_id, "knowledge_id": knowledge_id}
        )
    except Exception as e:
        logger.error(f"创建关联关系失败: {e}")
        raise HTTPException(status_code=400, detail=f"创建关联关系失败: {str(e)}")


@router.get("/{relation_id}", response_model=DetailResponse, summary="获取关联关系详情")
async def get_relation(
    relation_id: int = Path(..., description="关联关系ID"),
    knowledge_id: str = Query(..., description="知识库ID"),
    metadata_crud = Depends(get_metadata_crud)
):
    """获取关联关系详情"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        relation = await metadata_crud.get_relation(knowledge_id, relation_id)
        
        if not relation:
            raise HTTPException(status_code=404, detail=f"关联关系不存在: {relation_id}")
        
        return DetailResponse(
            success=True,
            message="获取关联关系详情成功",
            data=relation
        )
    except Exception as e:
        logger.error(f"获取关联关系详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取关联关系详情失败: {str(e)}")


@router.get("/", response_model=ListResponse, summary="查询关联关系列表")
async def list_relations(
    knowledge_id: str = Query(..., description="知识库ID"),
    pagination = Depends(validate_pagination),
    metadata_crud = Depends(get_metadata_crud)
):
    """查询关联关系列表"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        page, page_size, offset = pagination
        
        relations = await metadata_crud.list_relations(knowledge_id)
        
        total = len(relations)
        paginated_relations = relations[offset:offset + page_size]
        total_pages = (total + page_size - 1) // page_size
        
        return ListResponse(
            success=True,
            message="查询关联关系列表成功",
            data={
                "items": paginated_relations,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }
        )
    except Exception as e:
        logger.error(f"查询关联关系列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询关联关系列表失败: {str(e)}")

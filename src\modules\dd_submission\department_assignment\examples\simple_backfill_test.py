#!/usr/bin/env python3
"""
简化的数据回填优化测试

验证优化后的数据回填引擎基本功能
"""

import asyncio
import time
import logging
from datetime import datetime
from typing import List, Dict, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from service import get_client
from modules.dd_submission.department_assignment.core.optimized_backfill_engine import (
    OptimizedBackfillEngine, BackfillConfig
)


class SimpleBackfillTest:
    """简化的数据回填测试"""
    
    def __init__(self):
        self.rdb_client = None
        self.vdb_client = None
        self.optimized_engine = None
        self.test_timestamp = int(time.time())
        
    async def initialize(self):
        """初始化测试环境"""
        try:
            logger.info("🔧 初始化简化数据回填测试环境...")
            
            # 获取数据库客户端
            self.rdb_client = await get_client('database.rdbs.mysql')
            try:
                self.vdb_client = await get_client('database.vdbs.pgvector')
            except:
                logger.warning("向量数据库客户端初始化失败，继续使用关系数据库")
                self.vdb_client = None
            
            # 创建优化引擎
            test_config = BackfillConfig(
                batch_size=50,
                max_concurrency=2,
                timeout_per_batch=60.0,
                enable_transaction=True,
                enable_validation=True,
                fallback_enabled=True
            )
            self.optimized_engine = OptimizedBackfillEngine(
                self.rdb_client, self.vdb_client, test_config
            )
            
            logger.info("✅ 简化数据回填测试环境初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试环境初始化失败: {e}")
            return False

    async def test_basic_functionality(self) -> Dict[str, Any]:
        """测试基本功能"""
        logger.info("📋 测试：基本功能验证")
        
        test_results = {
            'test_name': '基本功能验证',
            'success': True,
            'details': [],
            'errors': []
        }
        
        try:
            # 生成少量测试数据
            test_data = self._generate_test_data(5)
            report_code = f"SIMPLE_TEST_{self.test_timestamp}_v1.0"
            
            logger.info(f"生成测试数据: {len(test_data)}条")
            
            # 准备测试环境
            await self._prepare_test_data(report_code, test_data)
            
            # 执行优化回填
            start_time = time.time()
            result = await self.optimized_engine.process_data_backfill(
                report_code, "test_step", test_data
            )
            end_time = time.time()
            
            execution_time = end_time - start_time
            
            # 验证结果
            if result.success:
                test_results['details'].append(f"处理成功: {result.total_count}条数据")
                test_results['details'].append(f"更新成功: {result.updated_count}条记录")
                test_results['details'].append(f"执行时间: {execution_time:.2f}秒")
                test_results['details'].append(f"处理速度: {result.total_count/execution_time:.1f}条/秒")
                
                # 获取性能统计
                stats = self.optimized_engine.get_performance_stats()
                test_results['details'].append(f"性能统计: {stats}")
                
            else:
                test_results['errors'].append(f"处理失败: {result.error_message}")
                test_results['success'] = False
            
            # 清理测试数据
            await self._cleanup_test_data(report_code, test_data)
            
            logger.info("✅ 基本功能验证完成")
            
        except Exception as e:
            logger.error(f"❌ 基本功能验证失败: {e}")
            test_results['success'] = False
            test_results['errors'].append(str(e))
        
        return test_results

    async def test_batch_operations(self) -> Dict[str, Any]:
        """测试批量操作"""
        logger.info("📋 测试：批量操作验证")
        
        test_results = {
            'test_name': '批量操作验证',
            'success': True,
            'details': [],
            'errors': []
        }
        
        try:
            # 测试不同大小的批量操作
            test_sizes = [5, 10, 20]
            
            for size in test_sizes:
                logger.info(f"  测试{size}条数据的批量操作")
                
                test_data = self._generate_test_data(size)
                report_code = f"BATCH_TEST_{size}_{self.test_timestamp}_v1.0"
                
                # 准备测试环境
                await self._prepare_test_data(report_code, test_data)
                
                # 执行批量操作
                start_time = time.time()
                result = await self.optimized_engine.process_data_backfill(
                    report_code, "batch_test", test_data
                )
                end_time = time.time()
                
                execution_time = end_time - start_time
                
                if result.success:
                    speed = result.total_count / execution_time if execution_time > 0 else 0
                    test_results['details'].append(
                        f"{size}条数据: {execution_time:.2f}秒, {speed:.1f}条/秒"
                    )
                else:
                    test_results['errors'].append(f"{size}条数据处理失败: {result.error_message}")
                    test_results['success'] = False
                
                # 清理测试数据
                await self._cleanup_test_data(report_code, test_data)
            
            logger.info("✅ 批量操作验证完成")
            
        except Exception as e:
            logger.error(f"❌ 批量操作验证失败: {e}")
            test_results['success'] = False
            test_results['errors'].append(str(e))
        
        return test_results

    def _generate_test_data(self, count: int) -> List[Dict[str, Any]]:
        """生成测试数据"""
        test_data = []
        
        for i in range(count):
            record = {
                'entry_id': f'SIMPLE_TEST_{self.test_timestamp}_{i:04d}',
                'entry_type': 'test_entry',
                'DR22': [f'DEPT_{i % 3}'],
                'BDR01': [f'BIZ_DEPT_{i % 2}'],
                'BDR03': [f'测试描述_{i}']
            }
            test_data.append(record)
        
        return test_data

    async def _prepare_test_data(self, report_code: str, test_data: List[Dict[str, Any]]):
        """准备测试数据"""
        try:
            # 解析report_code
            parts = report_code.split('_')
            dr07 = parts[0] if len(parts) >= 3 else 'SIMPLE'
            version = parts[-1] if len(parts) >= 3 else 'v1.0'
            
            # 创建post_distribution记录
            for item in test_data:
                sql = """
                INSERT INTO biz_dd_post_distribution 
                (pre_distribution_id, submission_id, submission_type, version, dept_id, 
                 dr01, dr07, dr22, bdr01, bdr03, create_time, update_time)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE update_time = VALUES(update_time)
                """
                
                params = (
                    1,
                    item['entry_id'],
                    'SUBMISSION',
                    version,
                    'TEST_DEPT',
                    'ADS',
                    dr07,
                    '原始部门',
                    '原始业务部门',
                    '原始描述',
                    datetime.now(),
                    datetime.now()
                )
                
                result = await self.rdb_client.aexecute(sql, params)
                if not result.success:
                    logger.error(f"插入数据失败: {result.error_message}")
            
            # 创建部门关联数据
            dept_ids = set()
            for item in test_data:
                dept_ids.update(item.get('DR22', []))
            
            for dept_id in dept_ids:
                sql = """
                INSERT INTO biz_dd_department_relation (dept_id, table_id)
                VALUES (%s, %s)
                ON DUPLICATE KEY UPDATE table_id = VALUES(table_id)
                """
                result = await self.rdb_client.aexecute(sql, (dept_id, 'test_table'))
                if not result.success:
                    logger.error(f"插入部门关联失败: {result.error_message}")
            
            logger.debug(f"准备测试数据完成: {len(test_data)}条")
            
        except Exception as e:
            logger.error(f"准备测试数据失败: {e}")
            raise

    async def _cleanup_test_data(self, report_code: str, test_data: List[Dict[str, Any]]):
        """清理测试数据"""
        try:
            # 清理post_distribution记录
            for item in test_data:
                await self.rdb_client.aexecute(
                    "DELETE FROM biz_dd_post_distribution WHERE submission_id = %s",
                    (item['entry_id'],)
                )
            
            # 清理部门关联数据
            dept_ids = set()
            for item in test_data:
                dept_ids.update(item.get('DR22', []))
            
            for dept_id in dept_ids:
                await self.rdb_client.aexecute(
                    "DELETE FROM biz_dd_department_relation WHERE dept_id = %s AND table_id = %s",
                    (dept_id, 'test_table')
                )
            
            logger.debug(f"清理测试数据完成: {len(test_data)}条")
            
        except Exception as e:
            logger.warning(f"清理测试数据失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 简化数据回填优化测试")
    print("=" * 60)
    print("测试目标：验证优化引擎基本功能")
    print("=" * 60)
    
    # 创建测试实例
    test = SimpleBackfillTest()
    
    # 初始化测试环境
    if not await test.initialize():
        print("❌ 测试环境初始化失败，退出测试")
        return False
    
    # 执行测试用例
    test_results = []
    
    try:
        # 测试1：基本功能验证
        result1 = await test.test_basic_functionality()
        test_results.append(result1)
        
        # 测试2：批量操作验证
        result2 = await test.test_batch_operations()
        test_results.append(result2)
        
    except Exception as e:
        logger.error(f"测试执行过程中发生异常: {e}")
        return False
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 简化数据回填优化测试报告")
    print("=" * 60)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results if result['success'])
    
    for result in test_results:
        status = "✅ 通过" if result['success'] else "❌ 失败"
        print(f"\n🔍 {result['test_name']}: {status}")
        
        if result['details']:
            for detail in result['details']:
                print(f"   📝 {detail}")
        
        if result['errors']:
            for error in result['errors']:
                print(f"   ❌ {error}")
    
    # 总结
    print(f"\n📈 测试总结:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests / total_tests * 100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！")
        print("✅ 数据回填优化引擎基本功能正常")
        print("✅ 批量操作机制工作正常")
        print("✅ 性能监控功能正常")
        return True
    else:
        print(f"\n⚠️ 有{total_tests - passed_tests}个测试失败")
        print("❌ 需要进一步调查和修复问题")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

#!/usr/bin/env python3
"""
DDCrud批量操作监控系统启动脚本

启动完整的监控系统，包括性能监控、告警和仪表板
"""

import os
import sys
import asyncio
import logging
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from modules.dd_submission.department_assignment.monitoring.performance_monitor import performance_monitor
from modules.dd_submission.department_assignment.monitoring.dashboard import MonitoringDashboard
from modules.dd_submission.department_assignment.monitoring.alert_config import (
    alert_notification_service, alert_config_manager
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MonitoringSystemManager:
    """监控系统管理器"""
    
    def __init__(self):
        self.dashboard = None
        self.monitoring_enabled = False
        
    def setup_environment(self):
        """设置监控环境"""
        logger.info("🔧 设置监控环境...")
        
        # 创建必要的目录
        monitoring_dir = Path(__file__).parent
        monitoring_dir.mkdir(exist_ok=True)
        
        # 创建日志文件
        log_files = ['performance_metrics.log', 'alerts.log']
        for log_file in log_files:
            log_path = monitoring_dir / log_file
            if not log_path.exists():
                log_path.touch()
        
        logger.info("✅ 监控环境设置完成")
    
    def check_dependencies(self):
        """检查依赖项"""
        logger.info("🔍 检查监控系统依赖...")
        
        missing_deps = []
        
        # 检查Flask（仪表板依赖）
        try:
            import flask
            logger.info("✅ Flask已安装")
        except ImportError:
            missing_deps.append("flask")
            logger.warning("❌ Flask未安装，仪表板功能不可用")
        
        # 检查requests（通知依赖）
        try:
            import requests
            logger.info("✅ requests已安装")
        except ImportError:
            missing_deps.append("requests")
            logger.warning("❌ requests未安装，部分通知功能不可用")
        
        if missing_deps:
            logger.warning(f"缺少依赖项: {', '.join(missing_deps)}")
            logger.info("安装命令: pip install " + " ".join(missing_deps))
            return False
        
        logger.info("✅ 所有依赖项检查通过")
        return True
    
    def load_configuration(self):
        """加载监控配置"""
        logger.info("📋 加载监控配置...")
        
        # 显示当前配置
        config_info = {
            'performance_baselines': performance_monitor.performance_baselines,
            'alert_rules_count': len(alert_config_manager.get_alert_rules()),
            'notification_config': {
                'email_enabled': alert_notification_service.config.email_enabled,
                'dingtalk_enabled': alert_notification_service.config.dingtalk_enabled,
                'wechat_enabled': alert_notification_service.config.wechat_enabled,
                'webhook_enabled': alert_notification_service.config.webhook_enabled
            }
        }
        
        logger.info("📊 当前配置:")
        logger.info(f"   性能基准: {config_info['performance_baselines']}")
        logger.info(f"   告警规则数量: {config_info['alert_rules_count']}")
        logger.info(f"   通知配置: {config_info['notification_config']}")
        
        return config_info
    
    def start_performance_monitoring(self):
        """启动性能监控"""
        logger.info("🚀 启动性能监控...")
        
        # 启用性能监控
        performance_monitor.enable_monitoring()
        
        # 记录一条测试数据
        performance_monitor.record_operation(
            operation_name="monitoring_system_startup",
            record_count=1,
            execution_time=0.1,
            database_calls=0,
            success=True
        )
        
        logger.info("✅ 性能监控已启动")
        self.monitoring_enabled = True
    
    def start_dashboard(self, host='localhost', port=5000, debug=False):
        """启动监控仪表板"""
        logger.info(f"🌐 启动监控仪表板: http://{host}:{port}")
        
        try:
            self.dashboard = MonitoringDashboard(host=host, port=port)
            
            if self.dashboard.app is None:
                logger.error("❌ 仪表板启动失败：Flask未安装")
                return False
            
            # 在单独的线程中启动仪表板
            import threading
            dashboard_thread = threading.Thread(
                target=self.dashboard.run,
                kwargs={'debug': debug},
                daemon=True
            )
            dashboard_thread.start()
            
            logger.info("✅ 监控仪表板已启动")
            return True
            
        except Exception as e:
            logger.error(f"❌ 仪表板启动失败: {e}")
            return False
    
    async def test_alert_system(self):
        """测试告警系统"""
        logger.info("🧪 测试告警系统...")
        
        try:
            # 发送测试告警
            test_metrics = {
                'operation_name': 'monitoring_system_test',
                'record_count': 100,
                'execution_time': 0.5,
                'records_per_second': 200.0,
                'success': True
            }
            
            results = await alert_notification_service.send_alert(
                rule_name="system_test",
                severity="low",
                message="监控系统测试告警",
                metrics=test_metrics
            )
            
            if results:
                logger.info("✅ 告警系统测试完成")
                for notification_type, success, error in results:
                    status = "成功" if success else f"失败: {error}"
                    logger.info(f"   {notification_type}: {status}")
            else:
                logger.info("ℹ️ 未配置任何通知方式")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 告警系统测试失败: {e}")
            return False
    
    async def run_monitoring_loop(self, interval=60):
        """运行监控循环"""
        logger.info(f"🔄 启动监控循环，间隔{interval}秒...")
        
        try:
            while self.monitoring_enabled:
                # 获取性能摘要
                summary = performance_monitor.get_performance_summary(60)
                
                if summary['total_operations'] > 0:
                    logger.info(
                        f"📊 监控摘要: "
                        f"操作数={summary['total_operations']}, "
                        f"成功率={summary['success_rate']*100:.1f}%, "
                        f"平均速度={summary['average_records_per_second']:.1f}条/秒, "
                        f"状态={summary['performance_status']}"
                    )
                
                await asyncio.sleep(interval)
                
        except KeyboardInterrupt:
            logger.info("👋 监控循环已停止")
        except Exception as e:
            logger.error(f"❌ 监控循环异常: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        logger.info("🛑 停止监控系统...")
        
        self.monitoring_enabled = False
        performance_monitor.disable_monitoring()
        
        logger.info("✅ 监控系统已停止")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DDCrud批量操作监控系统')
    parser.add_argument('--host', default='localhost', help='仪表板服务器地址')
    parser.add_argument('--port', type=int, default=5000, help='仪表板端口号')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    parser.add_argument('--no-dashboard', action='store_true', help='不启动仪表板')
    parser.add_argument('--test-only', action='store_true', help='仅运行测试')
    parser.add_argument('--interval', type=int, default=60, help='监控间隔（秒）')
    
    args = parser.parse_args()
    
    print("🚀 DDCrud批量操作监控系统")
    print("=" * 60)
    print("功能：实时监控批量操作性能，确保553倍性能提升效果")
    print("=" * 60)
    
    # 创建监控系统管理器
    manager = MonitoringSystemManager()
    
    try:
        # 1. 设置环境
        manager.setup_environment()
        
        # 2. 检查依赖
        if not manager.check_dependencies():
            if not args.test_only:
                print("⚠️ 部分功能可能不可用，继续启动...")
        
        # 3. 加载配置
        config = manager.load_configuration()
        
        # 4. 启动性能监控
        manager.start_performance_monitoring()
        
        # 5. 测试告警系统
        await manager.test_alert_system()
        
        if args.test_only:
            print("✅ 测试完成，退出")
            return
        
        # 6. 启动仪表板（可选）
        if not args.no_dashboard:
            dashboard_started = manager.start_dashboard(
                host=args.host,
                port=args.port,
                debug=args.debug
            )
            
            if dashboard_started:
                print(f"🌐 监控仪表板: http://{args.host}:{args.port}")
            else:
                print("⚠️ 仪表板启动失败，继续运行监控...")
        
        # 7. 运行监控循环
        print(f"🔄 监控系统运行中... (Ctrl+C 停止)")
        await manager.run_monitoring_loop(interval=args.interval)
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在停止监控系统...")
    except Exception as e:
        logger.error(f"❌ 监控系统运行异常: {e}")
    finally:
        manager.stop_monitoring()
        print("✅ 监控系统已停止")


if __name__ == "__main__":
    # 设置环境变量示例
    print("💡 环境变量配置示例:")
    print("export ALERT_EMAIL_ENABLED=true")
    print("export ALERT_SMTP_SERVER=smtp.example.com")
    print("export ALERT_SMTP_USERNAME=<EMAIL>")
    print("export ALERT_SMTP_PASSWORD=your_password")
    print("export ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>")
    print("export ALERT_DINGTALK_ENABLED=true")
    print("export ALERT_DINGTALK_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=xxx")
    print("")
    
    # 运行主程序
    asyncio.run(main())

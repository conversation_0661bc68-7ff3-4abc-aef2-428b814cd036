import logging
import os
import yaml  # 添加yaml库导入

from mysql.connector import connect, Error
from utils.db.db_config import DBConfig
from utils.db.mschema.database_env import DataBaseEnv
from utils.db.mschema.db_source import HITLSQLDatabase
from utils.db.db_util import init_db_conn
from typing import Optional

def execute_sql_candidates(db_env, sql_candidates: list[str]) -> dict:
    """
    执行多个SQL查询候选并返回它们的执行结果
    
    Args:
        db_env: 数据库环境
        sql_candidates: SQL查询候选列表
        
    Returns:
        执行结果字典，键为"A"、"B"、"C"等，值为包含sql、error和result字段的字典
    """
    execution_results = {}
    
    for i, sql_query in enumerate(sql_candidates):
        # 生成键名：A, B, C, D...
        key = chr(65 + i)  # ASCII码：A=65, B=66, ...
        
        result_dict = {"sql": sql_query, "error": None, "result": None}
        try:
            status, res = db_env.database.fetch(sql_query)
            if status:
                sql_res = db_env.database.fetch_truncated(sql_query, max_rows=100)
                markdown_res = db_env.database.trunc_result_to_markdown(sql_res)
                result_dict["result"] = markdown_res.strip()
            else:
                result_dict["error"] = res
        except Exception as e:
            result_dict["error"] = str(e)
            
        execution_results[key] = result_dict
    
    return execution_results


def execute_sql_candidates_with_rdb_client(
    db_env: Optional[DataBaseEnv] = None,
    sql_candidates: list[str] =[], 
    db_name: str = 'default'
    ) -> dict:
    """使用rdb_client执行SQL候选的便捷方法"""
    db_env = DataBaseEnv.from_rdb_client(db_name) if db_env is None else db_env
    return execute_sql_candidates(db_env, sql_candidates)


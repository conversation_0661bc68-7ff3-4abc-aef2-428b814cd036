# Knowledge Doc Module - 知识文档模块

Knowledge Doc模块提供了完整的文档管理和向量搜索功能，支持配置驱动的数据库客户端管理。

## 📚 功能特性

### 核心功能
- **文档管理**: 完整的文档CRUD操作，支持状态管理、进度跟踪
- **分块管理**: 智能分块处理，支持层次结构和信息分类
- **向量搜索**: 基于pgvector的语义搜索功能
- **类别管理**: 灵活的文档分类和关系管理
- **配置驱动**: 基于Hydra的动态配置管理

### 技术特性
- **内化客户端管理**: 操作类自动管理数据库连接
- **多数据库支持**: 同时支持MySQL、PostgreSQL、向量数据库
- **异步操作**: 全面支持async/await模式
- **业务聚合**: 多表操作的事务一致性保证
- **向量化集成**: 自动嵌入向量生成和存储

## 🏗️ 架构设计

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Knowledge Doc Module                      │
├─────────────────────────────────────────────────────────────┤
│  Operations Layer                                           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌───────────────┐ │
│  │  DocumentOps    │ │   ChunkOps      │ │ CategoryOps   │ │
│  │                 │ │                 │ │               │ │
│  │ • CRUD操作      │ │ • 业务聚合      │ │ • 分类管理    │ │
│  │ • 状态管理      │ │ • 向量搜索      │ │ • 关系管理    │ │
│  │ • 关系维护      │ │ • 批量处理      │ │ • 层级结构    │ │
│  └─────────────────┘ └─────────────────┘ └───────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Service Layer                                              │
│  ┌─────────────────┐ ┌─────────────────┐ ┌───────────────┐ │
│  │  get_client()   │ │   get_config()  │ │ Configuration │ │
│  │                 │ │                 │ │               │ │
│  │ • 客户端获取    │ │ • 配置加载      │ │ • Hydra集成   │ │
│  │ • 连接池管理    │ │ • 动态切换      │ │ • 类型验证    │ │
│  └─────────────────┘ └─────────────────┘ └───────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Database Layer                                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌───────────────┐ │
│  │     MySQL       │ │   PostgreSQL    │ │  Embedding    │ │
│  │                 │ │                 │ │               │ │
│  │ • 文档数据      │ │ • 向量存储      │ │ • 向量生成    │ │
│  │ • 关系管理      │ │ • 相似度搜索    │ │ • 模型推理    │ │
│  └─────────────────┘ └─────────────────┘ └───────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 配置架构

```
knowledge.doc:
    rdb: database.rdbs.mysql         # 关系型数据库配置
    vdb: database.vdbs.pgvector      # 向量数据库配置
    embedding: model.embeddings.moka-m3e-base  # 嵌入模型配置
```

## 🚀 快速开始

### 1. 基本使用（推荐方式）

```python
from modules.knowledge.doc.operations import DocumentOperation, ChunkOperation, CombinedCategoryOperation

# 使用默认配置（自动从 knowledge.doc.* 获取配置）
doc_ops = DocumentOperation()
chunk_ops = ChunkOperation()
category_ops = CombinedCategoryOperation()

# 创建文档
doc_id = await doc_ops.create_document(document_create_data)

# 创建分块并自动向量化
chunk_id = await chunk_ops.create_chunk_with_info_and_vector(
    knowledge_id="kb-001",
    doc_id=doc_id,
    chunk_infos=[
        {"info_type": "content", "info_value": "文档内容"},
        {"info_type": "summary", "info_value": "内容摘要"}
    ]
)

# 向量搜索
results = await chunk_ops.search_similar_chunks(
    query_text="搜索关键词",
    knowledge_id="kb-001",
    top_k=5
)
```

### 2. 自定义配置路径

```python
# 使用自定义配置路径
doc_ops = DocumentOperation(
    rdb_config_path="database.rdbs.mysql.custom",
    vdb_config_path="database.vdbs.pgvector.custom"
)

chunk_ops = ChunkOperation(
    rdb_config_path="database.rdbs.mysql.custom",
    vdb_config_path="database.vdbs.pgvector.custom",
    embedding_config_path="model.embeddings.custom"
)
```

### 3. 向后兼容（手动客户端注入）

```python
from service import get_client

# 手动获取客户端
rdb_client = await get_client("database.rdbs.mysql")
vdb_client = await get_client("database.vdbs.pgvector")
embedding_client = await get_client("model.embeddings.moka-m3e-base")

# 注入客户端
chunk_ops = ChunkOperation(
    rdb_client=rdb_client,
    vdb_client=vdb_client,
    embedding_client=embedding_client
)
```

## 📖 详细使用指南

### 文档操作 (DocumentOperation)

#### 创建文档
```python
from modules.knowledge.doc.entities.api_models import DocumentCreate
from modules.knowledge.doc.entities.base_models import DocumentStatus, ParseType, DocumentFormat

document_data = DocumentCreate(
    knowledge_id="kb-001",
    doc_name="示例文档.pdf",
    parse_type=ParseType.PDF,
    status=DocumentStatus.PENDING,
    doc_format=DocumentFormat.PDF,
    location="/uploads/document.pdf",
    is_active=True
)

doc_id = await doc_ops.create_document(document_data)
```

#### 查询和更新
```python
# 查询文档
document = await doc_ops.get_document_by_id(doc_id)

# 更新状态
await doc_ops.update_document_status(
    doc_id, 
    DocumentStatus.PROCESSING, 
    "开始解析"
)

# 更新进度
await doc_ops.update_document_progress(doc_id, chunk_nums=10, percentage=50.0)

# 查询知识库文档列表
documents = await doc_ops.get_documents_by_knowledge_id("kb-001")
```

### 分块操作 (ChunkOperation)

#### 创建分块和信息
```python
# 创建分块及信息（包含自动向量化）
chunk_id = await chunk_ops.create_chunk_with_info_and_vector(
    knowledge_id="kb-001",
    doc_id=doc_id,
    chapter_layer="第一章.第一节",
    chunk_infos=[
        {"info_type": "content", "info_value": "这是文档的主要内容..."},
        {"info_type": "summary", "info_value": "内容摘要"},
        {"info_type": "title", "info_value": "章节标题"},
        {"info_type": "keywords", "info_value": "关键词1,关键词2"}
    ]
)
```

#### 向量搜索
```python
# 基于文本的语义搜索
search_results = await chunk_ops.search_similar_chunks(
    query_text="机器学习算法",
    knowledge_id="kb-001",
    info_types=["content", "summary"],
    top_k=5,
    similarity_threshold=0.7
)

for result in search_results:
    print(f"分块ID: {result['chunk_id']}")
    print(f"相似度: {result['similarity_score']:.4f}")
    print(f"内容: {result['info_value'][:100]}...")

# 基于已有分块的相似搜索
similar_chunks = await chunk_ops.search_similar_chunks_by_chunk_info_id(
    chunk_info_id="chunk_info_001",
    knowledge_id="kb-001",
    top_k=3,
    exclude_self=True
)
```

#### 批量向量生成
```python
# 为文档的所有分块批量生成向量
result = await chunk_ops.batch_generate_vectors_for_document(
    knowledge_id="kb-001",
    doc_id=doc_id,
    info_types=["content", "summary", "title"]
)

print(f"总信息数: {result['total_infos']}")
print(f"成功向量化: {result['success_count']}")
print(f"失败数: {result['failed_count']}")
```

#### 后补向量生成
```python
# 为已存在的分块信息生成向量
success = await chunk_ops.add_vector_for_chunk_info(
    knowledge_id="kb-001",
    chunk_info_id="chunk_info_001",
    force_regenerate=False
)
```

### 类别操作 (CombinedCategoryOperation)

#### 类别管理
```python
category_ops = CombinedCategoryOperation()

# 创建类别
cate_id = await category_ops.category_ops.create_category(
    cate_id="tech-001",
    cate_name="技术文档",
    cate_layer=1,
    parent_id=None,
    cate_status="active"
)

# 创建子类别
child_cate_id = await category_ops.category_ops.create_category(
    cate_id="tech-ai-001",
    cate_name="人工智能",
    cate_layer=2,
    parent_id="tech-001",
    cate_status="active"
)
```

#### 文档分类关联
```python
# 将文档关联到类别
await category_ops.doc_category_ops.create_document_category(
    doc_id=doc_id,
    cate_id="tech-ai-001",
    cate_layer=2,
    doc_name="AI技术文档",
    doc_status="active"
)

# 查询文档的类别
categories = await category_ops.doc_category_ops.get_categories_by_doc_id(doc_id)

# 查询类别下的文档
documents = await category_ops.doc_category_ops.get_documents_by_category_id("tech-ai-001")
```

## 🔧 配置详解

### 数据库配置结构

```yaml
# src/config/config.yaml
knowledge:
  doc:
    rdb: database.rdbs.mysql              # MySQL配置路径
    vdb: database.vdbs.pgvector           # PGVector配置路径  
    embedding: model.embeddings.moka-m3e-base  # 嵌入模型配置路径
```

### 数据库表结构

#### 核心表
- `doc_dev_documents`: 文档基础信息表
- `doc_dev_chunks`: 分块基础信息表
- `doc_dev_chunks_info`: 分块详细信息表
- `doc_embeddings`: 向量存储表（PostgreSQL + pgvector）

#### 辅助表
- `doc_dev_document_alias`: 文档别名表
- `doc_dev_categories`: 类别管理表
- `doc_dev_document_categories`: 文档类别关联表
- `doc_dev_category_relationship`: 类别关系表
- `doc_dev_document_relationship`: 文档关系表

### 向量数据库模式

```sql
-- PostgreSQL with pgvector extension
CREATE TABLE public.doc_embeddings (
    id bigserial NOT NULL,
    knowledge_id varchar(255) NOT NULL,
    doc_id varchar(64) NOT NULL,
    chunk_id varchar(64) NOT NULL,
    chunk_info_id varchar(64) NOT NULL,
    info_type varchar(50) NOT NULL,
    embedding public.vector NOT NULL,
    create_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
    update_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT doc_embeddings_pkey PRIMARY KEY (id, knowledge_id)
);
```

## 🎯 业务场景示例

### 场景1: 完整的文档入库流程

```python
async def complete_document_ingestion_workflow():
    """完整的文档入库工作流程"""
    
    # 1. 创建操作实例
    doc_ops = DocumentOperation()
    chunk_ops = ChunkOperation()
    category_ops = CombinedCategoryOperation()
    
    # 2. 创建文档记录
    document_data = DocumentCreate(
        knowledge_id="enterprise-kb",
        doc_name="企业AI战略.pdf",
        parse_type=ParseType.PDF,
        status=DocumentStatus.PENDING,
        doc_format=DocumentFormat.PDF,
        location="/uploads/ai-strategy.pdf"
    )
    doc_id = await doc_ops.create_document(document_data)
    
    # 3. 文档解析和分块
    chunks_data = [
        {
            "chapter_layer": "第一章",
            "chunk_infos": [
                {"info_type": "title", "info_value": "AI战略概述"},
                {"info_type": "content", "info_value": "本章介绍企业AI战略的核心要素..."},
                {"info_type": "summary", "info_value": "AI战略核心要素概述"}
            ]
        },
        {
            "chapter_layer": "第二章", 
            "chunk_infos": [
                {"info_type": "title", "info_value": "技术实施路径"},
                {"info_type": "content", "info_value": "具体的AI技术实施步骤和方法..."},
                {"info_type": "summary", "info_value": "AI技术实施方法论"}
            ]
        }
    ]
    
    # 4. 批量创建分块（包含向量化）
    chunk_ids = []
    for chunk_data in chunks_data:
        chunk_id = await chunk_ops.create_chunk_with_info_and_vector(
            knowledge_id="enterprise-kb",
            doc_id=doc_id,
            chapter_layer=chunk_data["chapter_layer"],
            chunk_infos=chunk_data["chunk_infos"]
        )
        chunk_ids.append(chunk_id)
    
    # 5. 设置文档类别
    await category_ops.doc_category_ops.create_document_category(
        doc_id=doc_id,
        cate_id="strategy-docs",
        cate_layer=1,
        doc_name="企业AI战略",
        doc_status="active"
    )
    
    # 6. 更新文档状态
    await doc_ops.update_document_status(
        doc_id, 
        DocumentStatus.COMPLETED, 
        f"解析完成，共生成{len(chunk_ids)}个分块"
    )
    await doc_ops.update_document_progress(doc_id, len(chunk_ids), 100.0)
    
    print(f"✅ 文档入库完成: {doc_id}, 分块数: {len(chunk_ids)}")
    return doc_id, chunk_ids
```

### 场景2: 智能搜索和推荐

```python
async def intelligent_search_and_recommendation():
    """智能搜索和内容推荐"""
    
    chunk_ops = ChunkOperation()
    
    # 1. 用户搜索
    user_query = "如何制定AI战略"
    search_results = await chunk_ops.search_similar_chunks(
        query_text=user_query,
        knowledge_id="enterprise-kb",
        info_types=["content", "summary", "title"],
        top_k=5,
        similarity_threshold=0.6
    )
    
    print(f"🔍 搜索结果 (查询: '{user_query}'):")
    for i, result in enumerate(search_results, 1):
        print(f"{i}. [{result['info_type']}] 相似度: {result['similarity_score']:.3f}")
        print(f"   内容: {result['info_value'][:100]}...")
        print()
    
    # 2. 基于搜索结果的相关推荐
    if search_results:
        top_result = search_results[0]
        related_chunks = await chunk_ops.search_similar_chunks_by_chunk_info_id(
            chunk_info_id=top_result['chunk_info_id'],
            knowledge_id="enterprise-kb",
            top_k=3,
            exclude_self=True
        )
        
        print(f"📚 相关推荐:")
        for i, chunk in enumerate(related_chunks, 1):
            print(f"{i}. 相似度: {chunk['similarity_score']:.3f}")
            print(f"   内容: {chunk['info_value'][:80]}...")
            print()
```

### 场景3: 知识库统计和分析

```python
async def knowledge_base_analytics():
    """知识库统计分析"""
    
    doc_ops = DocumentOperation()
    chunk_ops = ChunkOperation()
    
    knowledge_id = "enterprise-kb"
    
    # 1. 文档统计
    total_docs = await doc_ops.count_documents(knowledge_id)
    completed_docs = await doc_ops.count_documents(knowledge_id, DocumentStatus.COMPLETED)
    
    print(f"📊 知识库统计 ({knowledge_id}):")
    print(f"   总文档数: {total_docs}")
    print(f"   已完成: {completed_docs}")
    print(f"   完成率: {completed_docs/total_docs*100:.1f}%" if total_docs > 0 else "")
    
    # 2. 分块信息统计
    documents = await doc_ops.get_documents_by_knowledge_id(knowledge_id)
    total_chunks = 0
    
    for doc in documents:
        chunks = await chunk_ops.get_chunks_by_document(doc['doc_id'])
        total_chunks += len(chunks)
        
        chunk_info_count = sum(len(chunk.get('chunk_infos', [])) for chunk in chunks)
        print(f"   - {doc['doc_name']}: {len(chunks)}个分块, {chunk_info_count}条信息")
    
    print(f"   总分块数: {total_chunks}")
```

## 🔄 迁移指南

### 从旧版本迁移

如果你正在使用旧版本的操作类，可以按以下步骤迁移：

#### 旧版本代码
```python
# 旧版本 - 手动管理客户端
rdb_client = await get_rdb_client()
chunk_operation = ChunkOperation(rdb_client)
```

#### 新版本代码
```python
# 新版本 - 自动客户端管理（推荐）
chunk_operation = ChunkOperation()  # 自动从配置获取

# 或者保持向后兼容
rdb_client = await get_client("database.rdbs.mysql")
chunk_operation = ChunkOperation(rdb_client=rdb_client)
```

### 配置文件更新

在 `src/config/config.yaml` 中添加：

```yaml
knowledge:
  doc:
    rdb: database.rdbs.mysql
    vdb: database.vdbs.pgvector  
    embedding: model.embeddings.moka-m3e-base
```

## 🚨 注意事项

### 性能优化建议

1. **向量生成**: 批量生成向量比单个生成效率更高
2. **搜索优化**: 合理设置相似度阈值和top_k参数
3. **连接池**: 客户端会自动复用连接，避免频繁创建实例
4. **索引优化**: 确保数据库表有适当的索引

### 错误处理

1. **向量生成失败**: 不会影响主流程，只记录错误日志
2. **数据库连接失败**: 会抛出异常，需要应用层处理
3. **配置缺失**: 会使用默认配置路径

### 最佳实践

1. **使用默认配置**: 优先使用配置驱动的方式，避免手动管理客户端
2. **业务聚合**: 使用复合操作方法，确保数据一致性
3. **异步编程**: 所有操作都支持async/await，充分利用异步优势
4. **批量操作**: 对于大量数据，使用批量方法提升性能

## 📚 相关文档

- [Service Layer Architecture](../../service/ARCHITECTURE.md)
- [Database Configuration Guide](../../config/database/)
- [Vector Search Implementation](../../base/db/base/vdb/)
- [Model Runtime Documentation](../../base/model_serve/)

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交变更 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../../../LICENSE) 文件了解详情。 

## 🏗️ 架构设计分析与改进建议

### 当前架构优缺点

#### 优点
- **简洁易用**: 操作类封装了复杂性，业务代码调用简单
- **配置驱动**: 通过Hydra自动管理客户端，减少样板代码
- **业务聚合**: 单个操作类管理相关联的多张表，保证数据一致性
- **向量集成**: 端到端的向量化和搜索能力

#### 缺点
- **紧耦合**: 操作类直接依赖具体的数据库客户端
- **扩展性差**: 难以支持多种数据源或自定义实现
- **测试困难**: 与外部服务强耦合，单元测试复杂
- **职责混合**: 单个方法同时处理业务逻辑和数据存储

### 与DD模块架构对比

| 维度 | Doc模块 | DD模块 | 建议方案 |
|------|---------|--------|----------|
| **复杂性** | 简单 | 复杂 | 适中 |
| **可扩展性** | 低 | 高 | 高 |
| **学习成本** | 低 | 高 | 中等 |
| **维护成本** | 低 | 中等 | 中等 |
| **依赖注入** | 无 | 全面 | 有选择的注入 |

### 改进建议

#### 1. 保持简洁的同时提高可扩展性

```python
# 推荐的新架构
class DocumentService:
    """文档服务类 - 业务门面"""
    
    def __init__(self, 
                 document_repo: Optional[DocumentRepository] = None,
                 chunk_repo: Optional[ChunkRepository] = None,
                 vector_repo: Optional[VectorRepository] = None):
        # 支持依赖注入，同时提供默认实现
        self.document_repo = document_repo or self._create_default_document_repo()
        self.chunk_repo = chunk_repo or self._create_default_chunk_repo()
        self.vector_repo = vector_repo or self._create_default_vector_repo()
    
    async def ingest_document(self, doc_data: DocumentCreate) -> str:
        """文档入库 - 业务方法"""
        # 业务逻辑编排，调用各个repository
        pass
    
    @classmethod
    async def create_default(cls) -> "DocumentService":
        """工厂方法 - 创建默认配置的服务"""
        return cls()  # 使用默认配置
```

#### 2. 引入适配器模式但不过度设计

```python
# 数据库客户端适配器
class DatabaseAdapter:
    """数据库适配器 - 统一不同客户端的API"""
    
    def __init__(self, client):
        self.client = client
        self._detect_client_type()
    
    async def insert(self, table: str, data: List[Dict]) -> bool:
        """统一的插入接口"""
        if self.client_type == "sqlalchemy":
            return await self._sqlalchemy_insert(table, data)
        elif self.client_type == "raw_mysql":
            return await self._raw_mysql_insert(table, data)
        # ... 其他客户端类型
```

#### 3. 分离关注点但避免过度拆分

```python
# Repository层 - 专注数据访问
class DocumentRepository:
    """文档数据访问层"""
    
    def __init__(self, db_adapter: DatabaseAdapter):
        self.db = db_adapter
    
    async def create(self, doc_data: Dict) -> str:
        """纯粹的数据创建操作"""
        pass

# Service层 - 业务逻辑编排
class DocumentService:
    """文档业务服务层"""
    
    async def ingest_document_with_vectorization(self, doc_data: DocumentCreate) -> str:
        """包含向量化的文档入库业务流程"""
        # 1. 创建文档
        doc_id = await self.document_repo.create(doc_data.dict())
        
        # 2. 解析和分块
        chunks = await self._parse_and_chunk(doc_data)
        
        # 3. 向量化
        await self._vectorize_chunks(chunks)
        
        return doc_id
```

#### 4. 配置和依赖注入的平衡

```python
# 支持配置驱动的同时允许自定义注入
class DocumentServiceFactory:
    """文档服务工厂"""
    
    @classmethod
    async def create_from_config(cls, config_override: Optional[Dict] = None) -> DocumentService:
        """从配置创建服务"""
        cfg = await get_config()
        if config_override:
            cfg.update(config_override)
        
        rdb_client = await get_client(cfg.knowledge.doc.rdb)
        vdb_client = await get_client(cfg.knowledge.doc.vdb)
        
        return DocumentService(
            document_repo=DocumentRepository(DatabaseAdapter(rdb_client)),
            vector_repo=VectorRepository(DatabaseAdapter(vdb_client))
        )
    
    @classmethod
    def create_with_clients(cls, rdb_client, vdb_client) -> DocumentService:
        """使用自定义客户端创建服务"""
        return DocumentService(
            document_repo=DocumentRepository(DatabaseAdapter(rdb_client)),
            vector_repo=VectorRepository(DatabaseAdapter(vdb_client))
        )
```

### 推荐的重构策略

#### 阶段1: 向后兼容的改进
1. 保持现有API不变
2. 内部重构为repository + service模式
3. 添加适配器层统一数据库API

#### 阶段2: 渐进式增强
1. 提供新的基于依赖注入的API
2. 同时支持配置驱动和手动注入
3. 添加更完善的测试支持

#### 阶段3: 完全优化
1. 逐步迁移到新API
2. 移除旧的紧耦合代码
3. 完善文档和示例

### 核心设计原则

1. **简单优于复杂**: 不引入不必要的抽象
2. **实用优于完美**: 满足当前需求的同时保留扩展性
3. **向后兼容**: 渐进式重构，不破坏现有代码
4. **可测试性**: 支持依赖注入以便于单元测试
5. **配置驱动**: 保持配置驱动的便利性

这种混合架构既保持了doc模块的简洁性，又吸收了dd模块的可扩展性优势，避免了过度设计的问题。 
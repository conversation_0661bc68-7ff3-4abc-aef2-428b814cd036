"""
DD系统搜索功能API路由

提供DD系统的智能搜索功能，包括：
- 向量搜索
- 混合搜索（向量+文本）
- 按数据项名称搜索
- 按需求口径搜索
"""

import time
from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from loguru import logger

from api.knowledge.models.response_models import StandardResponse
from ..models.requests import VectorSearchRequest, HybridSearchRequest
from ..models.responses import SearchResponse
from ..models.enums import DataLayerEnum
from ..dependencies.common import get_dd_search, get_search_params
from ..utils.helpers import format_response

# 创建路由器
router = APIRouter(tags=["DD搜索功能"], prefix="/search")


@router.post("/vector", response_model=StandardResponse, summary="向量搜索")
async def vector_search(
    request: VectorSearchRequest,
    dd_search = Depends(get_dd_search)
):
    """
    基于向量相似度的语义搜索
    
    - **query**: 搜索查询文本
    - **knowledge_id**: 知识库ID过滤（可选）
    - **data_layer**: 数据层过滤（可选）
    - **limit**: 返回结果数量限制（1-100）
    - **min_score**: 最小相似度分数（0.0-1.0）
    
    返回与查询文本语义最相似的填报数据记录。
    """
    try:
        start_time = time.time()
        
        # 执行向量搜索
        results = await dd_search.vector_search(
            query=request.query,
            knowledge_id=request.knowledge_id,
            data_layer=request.data_layer.value if request.data_layer else None,
            limit=request.limit,
            min_score=request.min_score
        )
        
        search_time = time.time() - start_time
        
        # 构建搜索结果
        search_results = []
        for result in results:
            search_results.append({
                "score": result.get("score", 0.0),
                "submission_data": result.get("submission_data", {}),
                "vector_info": {
                    "data_row_id": result.get("data_row_id"),
                    "field_id": result.get("field_id"),
                    "data_layer": result.get("data_layer")
                }
            })
        
        response_data = {
            "results": search_results,
            "total": len(search_results),
            "query": request.query,
            "search_time": round(search_time, 3),
            "search_type": "vector"
        }
        
        return StandardResponse(
            success=True,
            message=f"向量搜索完成，找到 {len(search_results)} 条结果",
            data=response_data
        )
    except Exception as e:
        logger.error(f"向量搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"向量搜索失败: {str(e)}")


@router.post("/hybrid", response_model=StandardResponse, summary="混合搜索")
async def hybrid_search(
    request: HybridSearchRequest,
    dd_search = Depends(get_dd_search)
):
    """
    混合搜索（向量搜索 + 文本搜索）
    
    - **query**: 搜索查询文本
    - **knowledge_id**: 知识库ID过滤（可选）
    - **data_layer**: 数据层过滤（可选）
    - **limit**: 返回结果数量限制（1-100）
    - **min_score**: 最小相似度分数（0.0-1.0）
    - **vector_weight**: 向量搜索权重（0.0-1.0）
    - **text_weight**: 文本搜索权重（0.0-1.0）
    
    结合向量语义搜索和传统文本搜索，提供更全面的搜索结果。
    """
    try:
        start_time = time.time()
        
        # 执行混合搜索
        results = await dd_search.hybrid_search(
            query=request.query,
            knowledge_id=request.knowledge_id,
            data_layer=request.data_layer.value if request.data_layer else None,
            limit=request.limit,
            min_score=request.min_score,
            vector_weight=request.vector_weight,
            text_weight=request.text_weight
        )
        
        search_time = time.time() - start_time
        
        # 构建搜索结果
        search_results = []
        for result in results:
            search_results.append({
                "score": result.get("combined_score", result.get("score", 0.0)),
                "submission_data": result.get("submission_data", {}),
                "vector_info": {
                    "data_row_id": result.get("data_row_id"),
                    "field_id": result.get("field_id"),
                    "data_layer": result.get("data_layer"),
                    "vector_score": result.get("vector_score"),
                    "text_score": result.get("text_score")
                }
            })
        
        response_data = {
            "results": search_results,
            "total": len(search_results),
            "query": request.query,
            "search_time": round(search_time, 3),
            "search_type": "hybrid",
            "weights": {
                "vector_weight": request.vector_weight,
                "text_weight": request.text_weight
            }
        }
        
        return StandardResponse(
            success=True,
            message=f"混合搜索完成，找到 {len(search_results)} 条结果",
            data=response_data
        )
    except Exception as e:
        logger.error(f"混合搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"混合搜索失败: {str(e)}")


@router.get("/by-data-item", response_model=StandardResponse, summary="按数据项名称搜索")
async def search_by_data_item_name(
    data_item_name: str = Query(..., description="数据项名称"),
    knowledge_id: Optional[str] = Query(None, description="知识库ID过滤"),
    data_layer: Optional[DataLayerEnum] = Query(None, description="数据层过滤"),
    limit: int = Query(10, description="返回结果数量限制", ge=1, le=100),
    dd_search = Depends(get_dd_search)
):
    """
    根据数据项名称进行搜索
    
    - **data_item_name**: 数据项名称（对应dr09字段）
    - **knowledge_id**: 知识库ID过滤（可选）
    - **data_layer**: 数据层过滤（可选）
    - **limit**: 返回结果数量限制（1-100）
    
    在dr09字段中搜索包含指定数据项名称的记录。
    """
    try:
        start_time = time.time()
        
        # 执行数据项名称搜索
        results = await dd_search.search_by_data_item_name(
            data_item_name=data_item_name,
            knowledge_id=knowledge_id,
            limit=limit
        )
        
        search_time = time.time() - start_time
        
        # 构建搜索结果
        search_results = []
        for result in results:
            search_results.append({
                "score": result.get("score", 1.0),  # 精确匹配给高分
                "submission_data": result.get("submission_data", {}),
                "match_info": {
                    "field": "dr09",
                    "match_type": "data_item_name",
                    "matched_text": data_item_name
                }
            })
        
        response_data = {
            "results": search_results,
            "total": len(search_results),
            "query": data_item_name,
            "search_time": round(search_time, 3),
            "search_type": "data_item_name"
        }
        
        return StandardResponse(
            success=True,
            message=f"数据项名称搜索完成，找到 {len(search_results)} 条结果",
            data=response_data
        )
    except Exception as e:
        logger.error(f"数据项名称搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"数据项名称搜索失败: {str(e)}")


@router.get("/by-requirement", response_model=StandardResponse, summary="按需求口径搜索")
async def search_by_requirement_rule(
    requirement_rule: str = Query(..., description="需求口径"),
    knowledge_id: Optional[str] = Query(None, description="知识库ID过滤"),
    data_layer: Optional[DataLayerEnum] = Query(None, description="数据层过滤"),
    limit: int = Query(10, description="返回结果数量限制", ge=1, le=100),
    dd_search = Depends(get_dd_search)
):
    """
    根据需求口径进行搜索
    
    - **requirement_rule**: 需求口径（对应dr17字段）
    - **knowledge_id**: 知识库ID过滤（可选）
    - **data_layer**: 数据层过滤（可选）
    - **limit**: 返回结果数量限制（1-100）
    
    在dr17字段中搜索包含指定需求口径的记录。
    """
    try:
        start_time = time.time()
        
        # 执行需求口径搜索
        results = await dd_search.search_by_requirement_rule(
            requirement_rule=requirement_rule,
            knowledge_id=knowledge_id,
            limit=limit
        )
        
        search_time = time.time() - start_time
        
        # 构建搜索结果
        search_results = []
        for result in results:
            search_results.append({
                "score": result.get("score", 1.0),  # 精确匹配给高分
                "submission_data": result.get("submission_data", {}),
                "match_info": {
                    "field": "dr17",
                    "match_type": "requirement_rule",
                    "matched_text": requirement_rule
                }
            })
        
        response_data = {
            "results": search_results,
            "total": len(search_results),
            "query": requirement_rule,
            "search_time": round(search_time, 3),
            "search_type": "requirement_rule"
        }
        
        return StandardResponse(
            success=True,
            message=f"需求口径搜索完成，找到 {len(search_results)} 条结果",
            data=response_data
        )
    except Exception as e:
        logger.error(f"需求口径搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"需求口径搜索失败: {str(e)}")

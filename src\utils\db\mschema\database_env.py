from typing import Optional
from utils.db.mschema.db_source import HITLSQLDatabase
from utils.db.rdb_adapter import RDBAdapter
from utils.db.common.clients import rdb_client

class DataBaseEnv:
    def __init__(self, database: Optional[HITLSQLDatabase] = None, db_name: str = 'default', rdb_client=None):
        """
        初始化数据库环境
        
        Args:
            database: 可选的HITLSQLDatabase实例，如果不提供则从rdb_client创建
            db_name: 数据库名称  
            rdb_client: 可选的rdb_client，如果不提供则使用全局的
        """
        if database is not None:
            # 使用传入的database（保持原有接口兼容）
            self.database = database
        else:
            # 从rdb_client创建database（新功能）
            self.database = RDBAdapter.create_hitl_database(db_name, rdb_client)
        
        # 保持原有属性不变
        self.dialect = self.database.dialect
        self.mschema = self.database.mschema
        self.db_name = self.database.db_name
        self.mschema_str = self.mschema.to_mschema()
    
    @classmethod
    def from_rdb_client(cls, db_name: str = 'default', rdb_client=rdb_client):
        """从rdb_client创建数据库环境的便捷方法"""
        return cls(db_name=db_name, rdb_client=rdb_client)
    
# # 方式1：新的使用方式（推荐）
# from utils.db.mschema.database_env import DataBaseEnv
# from utils.db.mschema.db_check import execute_sql_candidates

# db_env = DataBaseEnv.from_rdb_client('hsbc_data')
# results = execute_sql_candidates(db_env, ["SELECT 1", "SELECT 2"])

# # 方式2：最简便的方式
# from utils.db.mschema.db_check import execute_sql_candidates_with_rdb_client

# results = execute_sql_candidates_with_rdb_client(["SELECT 1", "SELECT 2"], 'hsbc_data')

# # 方式3：传统方式（完全兼容）
# from utils.db.mschema.database_env import DataBaseEnv
# from utils.db.mschema.db_source import HITLSQLDatabase

# database = HITLSQLDatabase(engine=my_engine, db_name='test')
# db_env = DataBaseEnv(database)  # 原有方式
# results = execute_sql_candidates(db_env, ["SELECT 1", "SELECT 2"])
"""
Metadata系统码值和关联CRUD操作

包含以下实体的CRUD操作：
- 码值集 (md_reference_code_set)
- 码值 (md_reference_code_value)
- 码值关联 (md_reference_code_relation)
"""

from typing import Any, Dict, List, Optional, Tuple, Union
import logging
from datetime import datetime

from .crud_base import MetadataCrudBase
from ..shared.exceptions import MetadataError, MetadataValidationError, MetadataNotFoundError, MetadataConflictError
from ..shared.constants import MetadataConstants, MetadataTableNames, MetadataCascadeRelations, MetadataVectorCollections
from ..shared.utils import MetadataUtils

logger = logging.getLogger(__name__)


class MetadataCrudCodes(MetadataCrudBase):
    """Metadata系统码值和关联CRUD操作"""

    # ==================== 码值集操作 ====================

    async def create_code_set(self, code_set_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """
        创建码值集

        Args:
            code_set_data: 码值集数据

        Returns:
            (码值集ID, 向量创建结果列表)
        """
        try:
            # 数据验证
            MetadataUtils.validate_code_set_data(code_set_data)

            # 检查是否已存在
            existing = await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                where={'knowledge_id': code_set_data['knowledge_id'], 'code_set_name': code_set_data['code_set_name']},
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"码值集已存在: {code_set_data['code_set_name']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(code_set_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                data=[code_set_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                raise MetadataError(f"创建码值集失败: {result.error}")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                where={'knowledge_id': code_set_data['knowledge_id'], 'code_set_name': code_set_data['code_set_name']},
                limit=1
            )

            code_set_id = inserted_records[0].get('id', 0) if inserted_records else 0

            # 码值集本身不创建向量，只有码值才创建向量
            vector_results = []

            logger.info(f"码值集创建成功: {code_set_data['code_set_name']} (ID: {code_set_id})")
            return code_set_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建码值集失败: {e}")
            raise MetadataError(f"创建码值集失败: {e}")

    async def get_code_set(self, code_set_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取码值集"""
        if code_set_id:
            where = {'id': code_set_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 code_set_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_REFERENCE_CODE_SET,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_code_set(self, code_set_data: Dict[str, Any], code_set_id: int = None, **where_conditions) -> bool:
        """更新码值集"""
        if code_set_id:
            where = {'id': code_set_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 code_set_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(code_set_data, is_update=True)

        try:
            updates = [{"data": code_set_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )
            return result.success and result.affected_rows > 0
        except Exception as e:
            logger.error(f"更新码值集失败: {e}")
            raise MetadataError(f"更新码值集失败: {e}")

    async def delete_code_set(self, code_set_id: int = None, **where_conditions) -> bool:
        """删除码值集"""
        if code_set_id:
            where = {'id': code_set_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 code_set_id 或其他删除条件")

        try:
            # 先删除相关码值的向量
            if self.vdb_client and code_set_id:
                try:
                    # 删除该码值集下所有码值的向量
                    collection_name = MetadataVectorCollections.MD_CODE_EMBEDDINGS
                    delete_expr = f"code_set_id = {code_set_id}"
                    await self.vdb_client.abatch_delete(
                        collection_name=collection_name,
                        exprs=[delete_expr]
                    )
                except Exception as e:
                    logger.warning(f"删除码值集相关向量失败: {e}")

            # 删除码值集记录（MySQL会自动级联删除相关码值和关联记录）
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除码值集失败: {e}")
            raise MetadataError(f"删除码值集失败: {e}")

    async def list_code_sets(
        self,
        knowledge_id: Optional[str] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询码值集列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_REFERENCE_CODE_SET,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    # ==================== 码值操作 ====================

    async def create_code_value(self, code_value_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建码值"""
        try:
            # 数据验证
            MetadataUtils.validate_code_value_data(code_value_data)

            # 检查是否已存在
            existing = await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                where={
                    'knowledge_id': code_value_data['knowledge_id'],
                    'code_set_id': code_value_data['code_set_id'],
                    'code_value': code_value_data['code_value']
                },
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"码值已存在: {code_value_data['code_value']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(code_value_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                data=[code_value_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                raise MetadataError(f"创建码值失败: {result.error}")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                where={
                    'knowledge_id': code_value_data['knowledge_id'],
                    'code_set_id': code_value_data['code_set_id'],
                    'code_value': code_value_data['code_value']
                },
                limit=1
            )

            code_value_id = inserted_records[0].get('id', 0) if inserted_records else 0

            # 处理向量化（只有码值才创建向量）
            vector_results = []
            if self.vdb_client and self.embedding_client and code_value_id:
                try:
                    vector_results = await self._create_vectors('code_value', code_value_id, code_value_data)
                except Exception as e:
                    logger.warning(f"码值向量化失败: {e}")

            logger.info(f"码值创建成功: {code_value_data['code_value']} (ID: {code_value_id})")
            return code_value_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建码值失败: {e}")
            raise MetadataError(f"创建码值失败: {e}")

    async def get_code_value(self, code_value_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取码值"""
        if code_value_id:
            where = {'id': code_value_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 code_value_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_code_value(self, code_value_data: Dict[str, Any], code_value_id: int = None, **where_conditions) -> bool:
        """更新码值"""
        if code_value_id:
            where = {'id': code_value_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 code_value_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(code_value_data, is_update=True)

        try:
            updates = [{"data": code_value_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )

            if result.success and result.affected_rows > 0:
                # 更新向量
                if self.vdb_client and self.embedding_client and code_value_id:
                    try:
                        await self._update_vectors('code_value', code_value_id, code_value_data)
                    except Exception as e:
                        logger.warning(f"码值向量更新失败: {e}")
                return True
            return False

        except Exception as e:
            logger.error(f"更新码值失败: {e}")
            raise MetadataError(f"更新码值失败: {e}")

    async def delete_code_value(self, code_value_id: int = None, **where_conditions) -> bool:
        """删除码值"""
        if code_value_id:
            where = {'id': code_value_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 code_value_id 或其他删除条件")

        try:
            # 先删除向量
            if self.vdb_client and code_value_id:
                await self._delete_vectors('code_value', code_value_id)

            # 删除码值记录
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除码值失败: {e}")
            raise MetadataError(f"删除码值失败: {e}")

    async def list_code_values(
        self,
        knowledge_id: Optional[str] = None,
        code_set_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询码值列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            code_set_id=code_set_id,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    # ==========================================
    # 码值关联 (Code Relation) CRUD 操作
    # ==========================================

    async def create_code_relation(self, relation_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """
        创建码值关联

        Args:
            relation_data: 码值关联数据

        Returns:
            Tuple[int, List[Dict[str, Any]]]: (关联ID, 向量化结果列表)
        """
        try:
            # 数据验证
            MetadataUtils.validate_code_relation_data(relation_data)

            # 检查重复
            existing = await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                where={
                    'column_id': relation_data['column_id'],
                    'code_set_id': relation_data['code_set_id']
                },
                limit=1
            )

            if existing:
                raise MetadataConflictError(f"码值关联已存在: 字段ID {relation_data['column_id']} 与码值集ID {relation_data['code_set_id']}")

            # 插入数据
            relation_data['create_time'] = datetime.now()
            relation_data['update_time'] = datetime.now()

            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                data=[relation_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success or result.affected_rows == 0:
                raise MetadataError("创建码值关联失败: 未知错误")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                where={
                    'column_id': relation_data['column_id'],
                    'code_set_id': relation_data['code_set_id']
                },
                limit=1
            )

            relation_id = inserted_records[0].get('id', 0) if inserted_records else 0

            # 向量化处理
            vector_results = []
            if self.embedding_client:
                try:
                    vector_result = await self._vectorize_record(
                        table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                        record_id=relation_id,
                        record_data=relation_data,
                        collection_name=MetadataVectorCollections.CODE_RELATION
                    )
                    if vector_result:
                        vector_results.append(vector_result)
                except Exception as e:
                    logger.warning(f"码值关联向量化失败: {e}")

            logger.info(f"码值关联创建成功: 字段ID{relation_data['column_id']} -> 码值集ID{relation_data['code_set_id']} (ID: {relation_id})")
            return relation_id, vector_results

        except Exception as e:
            logger.error(f"创建码值关联失败: {e}")
            raise MetadataError(f"创建码值关联失败: {e}")

    async def get_code_relation(self, relation_id: Optional[int] = None, **kwargs) -> Optional[Dict[str, Any]]:
        """
        获取码值关联信息

        Args:
            relation_id: 关联ID（主键查询）
            **kwargs: 其他查询条件

        Returns:
            Optional[Dict[str, Any]]: 码值关联信息
        """
        try:
            if relation_id:
                # 主键查询
                records = await self._aselect(
                    table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                    where={'id': relation_id},
                    limit=1
                )
            else:
                # 条件查询
                where_conditions = {}
                for key, value in kwargs.items():
                    if value is not None:
                        where_conditions[key] = value

                if not where_conditions:
                    return None

                records = await self._aselect(
                    table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                    where=where_conditions,
                    limit=1
                )

            return records[0] if records else None

        except Exception as e:
            logger.error(f"获取码值关联失败: {e}")
            return None

    async def update_code_relation(self, update_data: Dict[str, Any], relation_id: int) -> bool:
        """
        更新码值关联信息

        Args:
            update_data: 更新数据
            relation_id: 关联ID

        Returns:
            bool: 更新是否成功
        """
        try:
            update_data['update_time'] = datetime.now()

            where = {'id': relation_id}
            updates = [{"data": update_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )

            success = result.success and result.affected_rows > 0
            if success:
                logger.info(f"码值关联更新成功: ID {relation_id}")

            return success

        except Exception as e:
            logger.error(f"更新码值关联失败: {e}")
            return False

    async def delete_code_relation(self, relation_id: int) -> bool:
        """
        删除码值关联

        Args:
            relation_id: 关联ID

        Returns:
            bool: 删除是否成功
        """
        try:
            where = {'id': relation_id}
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            success = result.success and result.affected_rows > 0
            if success:
                logger.info(f"码值关联删除成功: ID {relation_id}")

            return success

        except Exception as e:
            logger.error(f"删除码值关联失败: {e}")
            return False

    async def list_code_relations(self, knowledge_id: Optional[str] = None,
                                 code_set_id: Optional[int] = None,
                                 column_id: Optional[int] = None,
                                 column_type: Optional[str] = None,
                                 limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        列出码值关联

        Args:
            knowledge_id: 知识库ID
            code_set_id: 码值集ID
            column_id: 字段ID
            column_type: 字段类型
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 码值关联列表
        """
        try:
            where_conditions = {}

            if knowledge_id:
                where_conditions['knowledge_id'] = knowledge_id
            if code_set_id:
                where_conditions['code_set_id'] = code_set_id
            if column_id:
                where_conditions['column_id'] = column_id
            if column_type:
                where_conditions['column_type'] = column_type

            return await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                where=where_conditions,
                order_by=['create_time DESC'],
                limit=limit,
                offset=offset
            )

        except Exception as e:
            logger.error(f"列出码值关联失败: {e}")
            return []

    async def batch_create_code_relations(self, relations_data: List[Dict[str, Any]]) -> List[int]:
        """
        批量创建码值关联

        Args:
            relations_data: 码值关联数据列表

        Returns:
            List[int]: 创建的关联ID列表
        """
        try:
            # 数据验证
            for relation_data in relations_data:
                MetadataUtils.validate_code_relation_data(relation_data)

            # 添加时间戳
            for relation_data in relations_data:
                relation_data['create_time'] = datetime.now()
                relation_data['update_time'] = datetime.now()

            # 批量插入
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                data=relations_data,
                batch_size=len(relations_data),
                max_concurrency=1
            )

            if not result.success:
                raise MetadataError("批量创建码值关联失败")

            # 获取插入的ID（简化处理）
            relation_ids = []
            for relation_data in relations_data:
                inserted_records = await self._aselect(
                    table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                    where={
                        'column_id': relation_data['column_id'],
                        'code_set_id': relation_data['code_set_id']
                    },
                    limit=1
                )
                relation_id = inserted_records[0].get('id', 0) if inserted_records else 0
                relation_ids.append(relation_id)

            logger.info(f"批量创建码值关联成功: {len(relation_ids)} 个")
            return relation_ids

        except Exception as e:
            logger.error(f"批量创建码值关联失败: {e}")
            raise MetadataError(f"批量创建码值关联失败: {e}")

    async def search_code_relations(self, search_term: str, knowledge_id: Optional[str] = None,
                                   limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        搜索码值关联

        Args:
            search_term: 搜索词
            knowledge_id: 知识库ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            where_conditions = {}

            if knowledge_id:
                where_conditions['knowledge_id'] = knowledge_id

            # 简化搜索：按备注字段搜索
            if search_term:
                where_conditions['comment'] = f"%{search_term}%"

            return await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_RELATION,
                where=where_conditions,
                order_by=['create_time DESC'],
                limit=limit,
                offset=offset
            )

        except Exception as e:
            logger.error(f"搜索码值关联失败: {e}")
            return []

    async def search_code_sets(self, search_term: str, knowledge_id: Optional[str] = None,
                              limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        搜索码值集

        Args:
            search_term: 搜索词
            knowledge_id: 知识库ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            where_conditions = {}

            if knowledge_id:
                where_conditions['knowledge_id'] = knowledge_id

            # 简化搜索：按码值集名称搜索
            if search_term:
                where_conditions['code_set_name'] = f"%{search_term}%"

            return await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_SET,
                where=where_conditions,
                order_by=['create_time DESC'],
                limit=limit,
                offset=offset
            )

        except Exception as e:
            logger.error(f"搜索码值集失败: {e}")
            return []

    async def search_code_values(self, search_term: str, knowledge_id: Optional[str] = None,
                                code_set_id: Optional[int] = None,
                                limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        搜索码值

        Args:
            search_term: 搜索词
            knowledge_id: 知识库ID
            code_set_id: 码值集ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            where_conditions = {}

            if knowledge_id:
                where_conditions['knowledge_id'] = knowledge_id
            if code_set_id:
                where_conditions['code_set_id'] = code_set_id

            # 简化搜索：按码值搜索
            if search_term:
                where_conditions['code_value'] = f"%{search_term}%"

            return await self._aselect(
                table=MetadataTableNames.MD_REFERENCE_CODE_VALUE,
                where=where_conditions,
                order_by=['create_time DESC'],
                limit=limit,
                offset=offset
            )

        except Exception as e:
            logger.error(f"搜索码值失败: {e}")
            return []

"""
轻量化Pipeline框架
基于service层的企业级数据处理管道
"""

from .core.context import PipelineContext, ContextFactory
from .core.base_step import BaseStep, LLMStep, ToolStep
from .core.manager import PipelineManager, PipelineBuilder, PipelineExecutor
from .factory import create_nl2sql_pipeline, NL2SQLExecutor, PipelineFactory

# 导入所有步骤
from .steps import (
    TableSelectorStep,
    ColumnSelectorStep,
    SchemaGeneratorStep,
    KeyAssociatorStep,
    SQLGeneratorStep,
    BusinessLogicGeneratorStep
)

__all__ = [
    # 核心组件
    'PipelineContext',
    'ContextFactory',
    'BaseStep', 
    'LLMStep',
    'ToolStep',
    'PipelineManager',
    'PipelineBuilder',
    'PipelineExecutor',
    
    # 工厂和执行器
    'PipelineFactory',
    'create_nl2sql_pipeline',
    'NL2SQLExecutor',
    
    # 所有步骤
    'ColumnSelectorStep',
    'SchemaGeneratorStep',
    'KeyAssociatorStep',
    'SQLGeneratorStep',
    'BusinessLogicGeneratorStep'
]

__version__ = "2.0.0"

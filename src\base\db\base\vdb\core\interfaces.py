"""
VDB核心接口定义

定义向量数据库抽象层的核心接口，确保不同实现的一致性
参考Milvus API设计，结合RDB的优秀架构模式

设计原则：
1. Milvus兼容 - API命名和行为与Milvus保持一致
2. 类型安全 - 完整的类型注解和协议定义
3. 异步优先 - 所有操作都支持同步和异步
4. 扩展性 - 支持不同向量数据库的特殊功能
5. 错误处理 - 统一的异常体系
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, AsyncContextManager, ContextManager
from contextlib import asynccontextmanager, contextmanager

from .types import (
    VectorDBValue, VectorDBRecord, VectorDBRecords, Vector, VectorArray,
    VectorDBConnection, VectorDBTransaction, VectorDBType,
    MetricType, IndexType, SearchType, RankingMethod
)
from .models import (
    CollectionSchema, FieldSchema, Entity, SearchResult,
    SearchRequest, QueryRequest, GetRequest, InsertRequest, DeleteRequest,
    HybridSearchRequest, ConnectionConfig
)


# ==================== 核心向量数据库接口 ====================

class VectorDatabaseClient(ABC):
    """向量数据库客户端抽象接口
    
    定义统一的向量数据库操作接口，支持同步和异步操作
    基于Milvus API设计，确保最大兼容性
    """
    
    # ==================== 连接管理 ====================
    
    @abstractmethod
    def connect(self, **kwargs) -> None:
        """建立向量数据库连接（同步）"""
        pass
    
    @abstractmethod
    async def aconnect(self, **kwargs) -> None:
        """建立向量数据库连接（异步）"""
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """断开向量数据库连接（同步）"""
        pass
    
    @abstractmethod
    async def adisconnect(self) -> None:
        """断开向量数据库连接（异步）"""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """检查连接状态"""
        pass
    
    @abstractmethod
    def get_database_type(self) -> VectorDBType:
        """获取数据库类型"""
        pass
    
    # ==================== 集合管理 ====================
    
    @abstractmethod
    def create_collection(self, collection_name: str, schema: CollectionSchema, 
                         **kwargs) -> bool:
        """创建集合（同步）
        
        Args:
            collection_name: 集合名称
            schema: 集合模式定义
            **kwargs: 额外参数（如索引配置等）
            
        Returns:
            是否创建成功
        """
        pass
    
    @abstractmethod
    async def acreate_collection(self, collection_name: str, schema: CollectionSchema,
                                **kwargs) -> bool:
        """创建集合（异步）"""
        pass
    
    @abstractmethod
    def drop_collection(self, collection_name: str) -> bool:
        """删除集合（同步）"""
        pass
    
    @abstractmethod
    async def adrop_collection(self, collection_name: str) -> bool:
        """删除集合（异步）"""
        pass
    
    @abstractmethod
    def has_collection(self, collection_name: str) -> bool:
        """检查集合是否存在（同步）"""
        pass
    
    @abstractmethod
    async def ahas_collection(self, collection_name: str) -> bool:
        """检查集合是否存在（异步）"""
        pass
    
    @abstractmethod
    def list_collections(self) -> List[str]:
        """列出所有集合（同步）"""
        pass
    
    @abstractmethod
    async def alist_collections(self) -> List[str]:
        """列出所有集合（异步）"""
        pass
    
    @abstractmethod
    def describe_collection(self, collection_name: str) -> CollectionSchema:
        """获取集合模式（同步）"""
        pass
    
    @abstractmethod
    async def adescribe_collection(self, collection_name: str) -> CollectionSchema:
        """获取集合模式（异步）"""
        pass
    
    # ==================== 数据操作 ====================
    
    @abstractmethod
    def insert(self, collection_name: str, data: List[Dict[str, Any]], 
               partition_name: Optional[str] = None, **kwargs) -> bool:
        """插入数据（同步）
        
        Args:
            collection_name: 集合名称
            data: 要插入的数据列表
            partition_name: 分区名称
            **kwargs: 额外参数
            
        Returns:
            是否插入成功
        """
        pass
    
    @abstractmethod
    async def ainsert(self, collection_name: str, data: List[Dict[str, Any]],
                     partition_name: Optional[str] = None, **kwargs) -> bool:
        """插入数据（异步）"""
        pass
    
    @abstractmethod
    def delete(self, collection_name: str, expr: str,
               partition_name: Optional[str] = None, **kwargs) -> bool:
        """删除数据（同步）
        
        Args:
            collection_name: 集合名称
            expr: 删除条件表达式
            partition_name: 分区名称
            **kwargs: 额外参数
            
        Returns:
            是否删除成功
        """
        pass
    
    @abstractmethod
    async def adelete(self, collection_name: str, expr: str,
                     partition_name: Optional[str] = None, **kwargs) -> bool:
        """删除数据（异步）"""
        pass
    
    # ==================== 查询操作 ====================
    
    @abstractmethod
    def search(self, collection_name: str, data: List[List[float]], 
               anns_field: str, limit: int = 10, expr: Optional[str] = None,
               output_fields: Optional[List[str]] = None,
               search_params: Optional[Dict[str, Any]] = None,
               **kwargs) -> List[List[SearchResult]]:
        """向量搜索（同步）
        
        Args:
            collection_name: 集合名称
            data: 查询向量列表
            anns_field: 向量字段名
            limit: 返回结果数量
            expr: 过滤表达式
            output_fields: 输出字段列表
            search_params: 搜索参数
            **kwargs: 额外参数
            
        Returns:
            搜索结果列表，每个查询向量对应一个结果列表
        """
        pass
    
    @abstractmethod
    async def asearch(self, collection_name: str, data: List[List[float]],
                     anns_field: str, limit: int = 10, expr: Optional[str] = None,
                     output_fields: Optional[List[str]] = None,
                     search_params: Optional[Dict[str, Any]] = None,
                     **kwargs) -> List[List[SearchResult]]:
        """向量搜索（异步）"""
        pass
    
    @abstractmethod
    def query(self, collection_name: str, expr: str,
              output_fields: Optional[List[str]] = None,
              limit: Optional[int] = None, offset: Optional[int] = None,
              **kwargs) -> List[Dict[str, Any]]:
        """标量查询（同步）
        
        Args:
            collection_name: 集合名称
            expr: 查询表达式
            output_fields: 输出字段列表
            limit: 结果数量限制
            offset: 偏移量
            **kwargs: 额外参数
            
        Returns:
            查询结果列表
        """
        pass
    
    @abstractmethod
    async def aquery(self, collection_name: str, expr: str,
                    output_fields: Optional[List[str]] = None,
                    limit: Optional[int] = None, offset: Optional[int] = None,
                    **kwargs) -> List[Dict[str, Any]]:
        """标量查询（异步）"""
        pass
    
    @abstractmethod
    def get(self, collection_name: str, ids: List[Any],
            output_fields: Optional[List[str]] = None,
            **kwargs) -> List[Dict[str, Any]]:
        """根据主键获取数据（同步）
        
        Args:
            collection_name: 集合名称
            ids: 主键ID列表
            output_fields: 输出字段列表
            **kwargs: 额外参数
            
        Returns:
            查询结果列表
        """
        pass
    
    @abstractmethod
    async def aget(self, collection_name: str, ids: List[Any],
                  output_fields: Optional[List[str]] = None,
                  **kwargs) -> List[Dict[str, Any]]:
        """根据主键获取数据（异步）"""
        pass
    
    # ==================== 混合搜索 ====================
    
    @abstractmethod
    def hybrid_search(self, collection_name: str, reqs: List[SearchRequest],
                     ranker: Any, limit: int = 10, **kwargs) -> List[SearchResult]:
        """混合搜索（同步）
        
        Args:
            collection_name: 集合名称
            reqs: 搜索请求列表
            ranker: 排序器实例
            limit: 最终返回结果数量
            **kwargs: 额外参数
            
        Returns:
            排序后的搜索结果列表
        """
        pass
    
    @abstractmethod
    async def ahybrid_search(self, collection_name: str, reqs: List[SearchRequest],
                            ranker: Any, limit: int = 10, **kwargs) -> List[SearchResult]:
        """混合搜索（异步）"""
        pass
    
    # ==================== 索引管理 ====================
    
    @abstractmethod
    def create_index(self, collection_name: str, field_name: str,
                    index_params: Dict[str, Any], **kwargs) -> bool:
        """创建索引（同步）"""
        pass
    
    @abstractmethod
    async def acreate_index(self, collection_name: str, field_name: str,
                           index_params: Dict[str, Any], **kwargs) -> bool:
        """创建索引（异步）"""
        pass
    
    @abstractmethod
    def drop_index(self, collection_name: str, field_name: str, **kwargs) -> bool:
        """删除索引（同步）"""
        pass
    
    @abstractmethod
    async def adrop_index(self, collection_name: str, field_name: str, **kwargs) -> bool:
        """删除索引（异步）"""
        pass
    
    # ==================== 工具方法 ====================
    
    def count(self, collection_name: str, expr: Optional[str] = None,
              **kwargs) -> int:
        """统计数据数量（同步）"""
        # 默认实现：通过query获取数量
        try:
            results = self.query(collection_name, expr or "1=1", limit=1, **kwargs)
            return len(results)
        except Exception:
            return 0
    
    async def acount(self, collection_name: str, expr: Optional[str] = None,
                    **kwargs) -> int:
        """统计数据数量（异步）"""
        try:
            results = await self.aquery(collection_name, expr or "1=1", limit=1, **kwargs)
            return len(results)
        except Exception:
            return 0
    
    def validate_connection(self) -> bool:
        """验证连接状态"""
        if not self.is_connected():
            from .exceptions import VectorDBException
            raise VectorDBException("数据库未连接，请先调用connect()方法")
        return True
    
    def get_db_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        return {
            "db_type": self.get_database_type().value,
            "connected": self.is_connected()
        }
    
    # ==================== 上下文管理器支持 ====================
    
    def __enter__(self):
        """同步上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """同步上下文管理器出口"""
        self.disconnect()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.aconnect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.adisconnect()


# ==================== 专用接口 ====================

class VectorSearchInterface(ABC):
    """向量搜索专用接口"""
    
    @abstractmethod
    def vector_search(self, request: SearchRequest) -> List[SearchResult]:
        """执行向量搜索（同步）"""
        pass
    
    @abstractmethod
    async def avector_search(self, request: SearchRequest) -> List[SearchResult]:
        """执行向量搜索（异步）"""
        pass


class CollectionManagerInterface(ABC):
    """集合管理专用接口"""
    
    @abstractmethod
    def manage_collection(self, operation: str, **kwargs) -> Any:
        """集合管理操作（同步）"""
        pass
    
    @abstractmethod
    async def amanage_collection(self, operation: str, **kwargs) -> Any:
        """集合管理操作（异步）"""
        pass


class IndexManagerInterface(ABC):
    """索引管理专用接口"""
    
    @abstractmethod
    def manage_index(self, operation: str, **kwargs) -> Any:
        """索引管理操作（同步）"""
        pass
    
    @abstractmethod
    async def amanage_index(self, operation: str, **kwargs) -> Any:
        """索引管理操作（异步）"""
        pass

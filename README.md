# 知识库管理服务 - API 文档

本文档提供了知识库管理服务的详细说明，包括环境配置、如何运行服务以及API接口的详细信息。

## 1. 项目简介

本服务是一个基于 FastAPI 构建的知识库管理系统，为生产环境提供稳定、高效的API。它旨在为前端应用提供全面的知识库管理功能，包括：

-   **知识库管理**：支持灵活的模型配置。
-   **元数据管理**：支持元数据模板（MetaData）、文档（Doc）和数据字典（DD）等多种类型。
-   **文档处理**：包括文档上传、解析、向量化和分块。
-   **智能搜索**：提供向量搜索和语义检索功能。
-   **数据治理**：确保企业级数据的完整性和一致性。

服务运行在端口 `30337` 上。

## 2. 环境配置

本项目使用 `uv` 作为包管理工具，以实现快速、可靠的依赖管理。

### 2.1. 安装 uv

如果您尚未安装 `uv`，请根据您的操作系统，按照 [官方安装指南](https://github.com/astral-sh/uv) 进行安装。例如，在 Linux 或 macOS 上，可以使用以下命令：

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 2.2. 创建虚拟环境

在项目根目录下，使用 `uv` 创建一个新的虚拟环境。推荐的 Python 版本为 `>=3.12`。

```bash
uv venv
```

这将在项目根目录下创建一个名为 `.venv` 的虚拟环境。

### 2.3. 激活虚拟环境

激活刚刚创建的虚拟环境：

**Linux / macOS:**
```bash
source .venv/bin/activate
```

**Windows (CMD):**
```bash
.venv\Scripts\activate
```

激活后，您的终端提示符前应显示 `(.venv)`。

### 2.4. 安装项目依赖

使用 `uv` 安装 `pyproject.toml` 文件中定义的所有依赖项。`uv` 会利用 `uv.lock` 文件来确保安装精确版本的依赖，保证环境的一致性。

```bash
uv pip install -r pyproject.toml
```
或者，由于 `pyproject.toml` 的 `[project]` 表是标准的，你可以直接同步环境：
```bash
uv pip sync
```

该命令会自动从 `pyproject.toml` 读取依赖并安装。`uv` 使用清华大学的 PyPI 镜像源（`https://pypi.tuna.tsinghua.edu.cn/simple`）以加快下载速度，该配置位于 `pyproject.toml` 的 `[[tool.uv.index]]` 部分。

### 2.5. 环境迁移

如果您需要在另一台机器上部署此项目，只需重复上述步骤即可。由于所有依赖都已在 `pyproject.toml` 中声明，并且 `uv.lock` 锁定了版本，因此可以轻松地在任何地方重建完全相同的环境。

1.  将项目文件（包括 `pyproject.toml` 和 `uv.lock`）复制到新机器。
2.  在新机器上安装 `uv`。
3.  执行 `uv venv` 和 `source .venv/bin/activate`。
4.  执行 `uv pip sync` 来安装依赖。

## 3. 如何运行服务

您可以通过两种方式启动本服务：

### 3.1. 直接运行主文件 (开发模式)

在激活虚拟环境后，可以直接运行生产环境的主程序文件：

```bash
python src/api/knowledge/main_production.py
```

您将在终端看到以下输出，表示服务已成功启动：

```
🚀 启动知识库管理服务 - 生产环境
📍 端口: 30337
🌐 文档: http://localhost:30337/docs
✅ 环境: Production (生产)
💼 用途: 前端应用API服务
```

### 3.2. 使用 Uvicorn 运行 (生产推荐)

在生产环境中，推荐使用 `uvicorn` 来运行 FastAPI 应用，因为它提供了更好的性能和稳定性。

在项目根目录下运行以下命令：

```bash
uvicorn src.api.knowledge.main_production:app --host 0.0.0.0 --port 30337 --reload
```
-   `--host 0.0.0.0`: 使服务可以从外部网络访问。
-   `--port 30337`: 指定服务运行的端口。
-   `--reload`: 在代码更改后自动重启服务，方便开发调试。在生产环境中可以移除此标志。

## 4. API 文档

本服务使用 FastAPI 自动生成交互式 API 文档。启动服务后，您可以通过以下链接访问：

-   **Swagger UI**: [http://localhost:30337/docs](http://localhost:30337/docs)
-   **ReDoc**: [http://localhost:30337/redoc](http://localhost:30337/redoc)

### 4.1. API 模块概览

服务包含了多个功能模块，每个模块都注册为独立的 API 路由。以下是主要模块的概览：

| 模块名称 | 描述 |
| :--- | :--- |
| **Knowledge Base** | 管理知识库的创建、读取、更新和删除 (CRUD) 操作。 |
| **Metadata Template** | 管理元数据模板，支持 `MetaData` 类型的知识库。 |
| **Metadata CRUD** | 提供对元数据（数据库、表、字段等）的完整 CRUD 操作。 |
| **Code Search** | 提供全局码值搜索功能。 |
| **DD Data Dictionary** | 管理 `DD` (数据字典) 类型的知识库。 |
| **Document Management** | 管理 `Doc` (文档) 类型知识库中的文档上传、解析和查询。 |
| **Chunk Management** | 管理文档分块 (Chunks) 的检索和操作。 |
| **Search** | 提供跨知识库的统一搜索接口。 |

根路径 `/` 提供了服务的健康状况、版本和主要端点列表。

### 4.2. 健康检查

可以通过访问 `/health` 端点来检查服务的健康状态。

## 5. 项目结构

以下是项目关键目录和文件的简介：

```
.
├── .venv/               # Python 虚拟环境
├── src/                 # 项目源代码
│   ├── api/             # API 路由和逻辑
│   │   └── knowledge/   # 知识库相关核心模块
│   │       ├── main_production.py  # 生产环境主入口
│   │       ├── ...      # 其他API子模块
│   ├── config/          # Hydra 配置文件
│   ├── modules/         # 核心业务逻辑模块
│   └── utils/           # 通用工具函数
├── .gitignore           # Git 忽略文件配置
├── pyproject.toml       # 项目配置和依赖项 (PEP 621)
└── uv.lock              # `uv` 生成的依赖锁定文件
``` 
"""
轻量化Pipeline步骤基类
基于service层的四阶段处理模式
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import time
import asyncio
import logging

logger = logging.getLogger(__name__)

from .context import PipelineContext

class BaseStep(ABC):
    """
    Pipeline步骤基类
    定义标准的四阶段处理流程：预处理 → 执行 → 解析 → 后处理
    """
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
    
    async def execute(self, context: PipelineContext) -> PipelineContext:
        """执行步骤的主流程"""
        start_time = time.time()
        context.current_step = self.name
        
        try:
            logger.info(f"开始执行步骤: {self.name}")
            
            # 阶段1: 预处理
            preprocessed_data = await self.preprocess(context)

            # 检查预处理结果
            if preprocessed_data is None:
                logger.warning(f"步骤 {self.name} 预处理返回None，跳过执行")
                # 记录跳过结果
                context.record_step_result(
                    step_name=self.name,
                    success=True,
                    data={}
                )
                return context

            # 阶段2: 核心执行
            execution_result = await self.process(preprocessed_data, context)
            
            # 阶段3: 结果解析
            parsed_result = await self.parse_result(execution_result, context)
            
            # 阶段4: 后处理
            final_result = await self.postprocess(parsed_result, context)
            
            # 更新上下文
            await self.update_context(final_result, context)
            
            execution_time = time.time() - start_time
            
            # 记录成功结果
            context.record_step_result(
                step_name=self.name,
                success=True,
                data=final_result
            )
            
            logger.info(f"步骤 {self.name} 执行成功，耗时 {execution_time:.2f}s")
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"步骤 {self.name} 执行失败: {str(e)}"
            logger.error(error_msg)
            
            # 记录失败结果
            context.record_step_result(
                step_name=self.name,
                success=False,
                error=error_msg
            )
            
            # 重新抛出异常，让调用者决定如何处理
            raise
        
        return context
    
    # ========== 四个阶段的抽象方法 ==========
    
    @abstractmethod
    async def preprocess(self, context: PipelineContext) -> Dict[str, Any]:
        """阶段1: 预处理 - 准备数据"""
        pass
    
    @abstractmethod
    async def process(self, preprocessed_data: Dict[str, Any], context: PipelineContext) -> Any:
        """阶段2: 核心处理逻辑"""
        pass
    
    async def parse_result(self, result: Any, context: PipelineContext) -> Any:
        """阶段3: 结果解析 - 默认直接返回"""
        return result
    
    async def postprocess(self, parsed_result: Any, context: PipelineContext) -> Any:
        """阶段4: 后处理 - 默认直接返回"""
        return parsed_result
    
    async def update_context(self, result: Any, context: PipelineContext) -> None:
        """更新上下文数据 - 默认存储到以步骤名命名的键中"""
        context.set(f"{self.name}_result", result)

class LLMStep(BaseStep):
    """
    LLM步骤基类
    专门处理需要调用大模型的步骤
    """

    def __init__(self,
                 name: str,
                 description: str = "",
                 template_name: Optional[str] = None,
                 parser_name: Optional[str] = None,
                 num_calls: int = 1):
        super().__init__(name, description)
        self.template_name = template_name
        self.parser_name = parser_name
        self.num_calls = num_calls

    async def process(self, preprocessed_data: Dict[str, Any], context: PipelineContext) -> List[str]:
        """LLM调用处理"""
        if not self.template_name:
            raise ValueError(f"LLM步骤 {self.name} 必须指定template_name")

        # 构建messages
        messages = await self.build_prompt(preprocessed_data, context)

        # 调用LLM
        responses = await self.call_llm(messages, context)

        return responses
    
    async def build_prompt(self, preprocessed_data: Dict[str, Any], context: PipelineContext) -> List[Dict[str, str]]:
        """构建LLM messages"""
        from utils.llm.prompts import get_prompt

        # 使用template_name作为前缀，分别加载system和user模板
        prompt_template_user = get_prompt(f"{self.template_name}_user")
        prompt_template_system = get_prompt(f"{self.template_name}_system")

        # 获取格式化变量
        format_vars = self.get_prompt_variables(preprocessed_data, context)

        # 分别格式化system和user prompt
        formatted_user = prompt_template_user.format_prompt(**format_vars['user']).to_string()
        formatted_system = prompt_template_system.format_prompt(**format_vars['system']).to_string()

        # 构建messages数组
        messages = [
            {"role": "system", "content": formatted_system},
            {"role": "user", "content": formatted_user}
        ]

        return messages

    def get_prompt_variables(self, preprocessed_data: Dict[str, Any], context: PipelineContext) -> Dict[str, Dict[str, Any]]:
        """
        获取用于格式化prompt的变量

        Args:
            preprocessed_data: 预处理的数据
            context: Pipeline上下文

        Returns:
            包含'system'和'user'键的字典，每个键对应相应模板的变量
        """
        # 如果preprocessed_data已经是正确的格式（包含system和user键），直接使用
        if isinstance(preprocessed_data, dict) and ("system" in preprocessed_data or "user" in preprocessed_data):
            return {
                "system": preprocessed_data.get("system", {}),
                "user": preprocessed_data.get("user", {})
            }

        # 否则，将所有数据同时提供给system和user模板
        # 这是为了向后兼容可能的平坦格式
        return {
            "system": preprocessed_data,
            "user": preprocessed_data
        }

    async def call_llm(self, messages: List[Dict[str, str]], context: PipelineContext) -> List[str]:
        """调用LLM（使用service层）"""
        from service import get_client

        # 获取LLM客户端
        llm_client = await get_client(f"model.llms.{context.model_name}")

        # 并发调用
        tasks = []
        for _ in range(self.num_calls):
            task = self._single_llm_call(llm_client, messages)
            tasks.append(task)

        responses = await asyncio.gather(*tasks, return_exceptions=True)

        # 过滤异常
        valid_responses = [r for r in responses if not isinstance(r, Exception)]

        if not valid_responses:
            raise Exception("所有LLM调用都失败了")

        return valid_responses
    
    async def _single_llm_call(self, llm_client, messages: List[Dict[str, str]]) -> str:
        """单次LLM调用"""
        try:
            # 使用PromptMessage格式
            from base.model_serve.model_runtime.entities.message_entities import (
                UserPromptMessage, SystemPromptMessage
            )

            # 转换messages为PromptMessage格式
            prompt_messages = []
            for msg in messages:
                if msg["role"] == "system":
                    prompt_messages.append(SystemPromptMessage(content=msg["content"]))
                elif msg["role"] == "user":
                    prompt_messages.append(UserPromptMessage(content=msg["content"]))

            response = await llm_client.ainvoke(prompt_messages=prompt_messages, stream=False)
            return response.message.content if hasattr(response, 'message') else str(response)
        except ImportError:
            # 如果没有PromptMessage，尝试使用messages参数直接调用
            response = await llm_client.ainvoke(messages=messages)
            return response.content if hasattr(response, 'content') else str(response)
    
    async def parse_result(self, result: List[str], context: PipelineContext) -> List[Any]:
        """解析LLM结果"""
        if not self.parser_name:
            return result
        
        from utils.llm.parsers import get_parser
        parser = get_parser(self.parser_name)
        
        parsed_results = []
        for response in result:
            try:
                parsed = parser.parse(response)
                parsed_results.append(parsed)
            except Exception as e:
                logger.warning(f"解析失败: {e}, 原始输出: {response[:100]}...")
                parsed_results.append({"error": str(e), "raw": response})
        
        return parsed_results

class ToolStep(BaseStep):
    """
    工具步骤基类
    专门处理不需要LLM的工具类步骤
    """
    
    def __init__(self, name: str, description: str = ""):
        super().__init__(name, description)
    
    async def parse_result(self, result: Any, context: PipelineContext) -> Any:
        """工具步骤通常不需要特殊解析"""
        return result

# ==================== 步骤装饰器 ====================

def step_cache(cache_key_func=None):
    """步骤缓存装饰器"""
    def decorator(step_class):
        original_execute = step_class.execute
        
        async def cached_execute(self, context: PipelineContext):
            # 生成缓存键
            if cache_key_func:
                cache_key = cache_key_func(self, context)
            else:
                cache_key = f"{self.name}_{hash(context.user_question)}"
            
            # 检查缓存
            cached_result = context.get_cache(cache_key)
            if cached_result is not None:
                logger.info(f"步骤 {self.name} 使用缓存结果")
                context.record_step_result(
                    step_name=self.name,
                    success=True,
                    data=cached_result
                )
                await self.update_context(cached_result, context)
                return context
            
            # 执行步骤
            result_context = await original_execute(self, context)
            
            # 缓存结果
            if context.is_step_successful(self.name):
                step_result = context.get_step_result(self.name)
                context.set_cache(cache_key, step_result.get("data"))
            
            return result_context
        
        step_class.execute = cached_execute
        return step_class
    
    return decorator

def step_retry(max_retries: int = 3, delay: float = 1.0):
    """步骤重试装饰器"""
    def decorator(step_class):
        original_execute = step_class.execute
        
        async def retry_execute(self, context: PipelineContext):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await original_execute(self, context)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"步骤 {self.name} 第 {attempt + 1} 次尝试失败，{delay}s后重试: {e}")
                        await asyncio.sleep(delay)
                    else:
                        logger.error(f"步骤 {self.name} 重试 {max_retries} 次后仍然失败")
            
            raise last_exception
        
        step_class.execute = retry_execute
        return step_class
    
    return decorator

import json
import numpy as np
from typing import Dict, List, Any, Union, Optional
from loguru import logger
from service import get_client
from utils.db.get_all_content import get_all_content
from utils.db.get_partitionkey import get_or_create_partition_key

# 尝试导入可选的模块，如果不存在则跳过
try:
    from modules.pg_database.utils.process_sql_query import process_sql_query
    from modules.pg_database.utils.conditional_correction import vector_search
    LEGACY_MODULES_AVAILABLE = True
except ImportError:
    logger.warning("Legacy modules not available, some functions may not work")
    LEGACY_MODULES_AVAILABLE = False

    # 提供占位符函数
    def process_sql_query(*args, **kwargs):
        raise NotImplementedError("Legacy module not available")

    def vector_search(*args, **kwargs):
        raise NotImplementedError("Legacy module not available")

# 默认客户端获取函数 - 使用service层统一管理
async def _get_default_pgvector_client():
    """获取默认PgVector客户端"""
    return await get_client("database.vdbs.pgvector", priority="standard")

async def _get_default_mysql_client():
    """获取默认MySQL客户端"""
    return await get_client("database.rdbs.mysql", priority="standard")

# 兼容性：保持原有的同步接口
_default_clients = {
    'pgvector': None,  # 将在运行时异步获取
    'mysql': None      # 将在运行时异步获取
}

async def get_default_client(db_type: str = 'pgvector') -> Any:
    """
    获取默认客户端 - 迁移到service层

    Args:
        db_type: 数据库类型 ('pgvector', 'mysql')

    Returns:
        客户端实例
    """
    if db_type == 'pgvector':
        return await _get_default_pgvector_client()
    elif db_type == 'mysql':
        return await _get_default_mysql_client()
    else:
        raise ValueError(f"不支持的数据库类型: {db_type}")

def get_default_client_sync(db_type: str = 'pgvector') -> Any:
    """
    同步版本的默认客户端获取（兼容性保留）

    注意：这是一个兼容性函数，建议使用异步版本
    """
    import asyncio
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果事件循环正在运行，无法直接调用异步函数
            logger.warning(f"在运行的事件循环中调用同步客户端获取，返回None。请使用异步版本 get_default_client()")
            return None
        else:
            return loop.run_until_complete(get_default_client(db_type))
    except RuntimeError:
        logger.warning(f"无法获取事件循环，返回None。请使用异步版本 get_default_client()")
        return None

async def search_vector_db(query: str, **kwargs) -> List[Dict]:
    """
    通用向量数据库普通搜索函数，支持不同类型的向量数据库的单字段搜索。

    Args:
        query (str): 搜索查询文本
        **kwargs: 各数据库所需的特定参数
            - db_type (str): 数据库类型，默认为'pgvector'
            - client (Any, optional): 自定义数据库客户端实例，如不提供则使用默认客户端

    Returns:
        List[Dict]: 搜索结果列表
    """
    # 获取数据库类型
    db_type = kwargs.get('db_type', 'pgvector').lower()

    # 获取客户端，优先使用传入的客户端，否则使用默认客户端
    client = kwargs.get('client')
    if client is None:
        try:
            client = await get_default_client(db_type)
        except Exception as e:
            logger.error(f"获取默认客户端失败，数据库类型: {db_type}, 错误: {e}")
            raise ValueError(f"获取默认客户端失败，数据库类型: {db_type}")

    if client is None:
        logger.error(f"无法获取客户端，数据库类型: {db_type}")
        raise ValueError(f"无法获取客户端，数据库类型: {db_type}")
    
    # 根据数据库类型分发到不同处理函数
    if db_type == 'pgvector':
        return _search_pgvector(query, client, **kwargs)
    elif db_type == 'milvus':
        return _search_milvus(query, client, **kwargs)
    # 可以添加其他数据库类型的处理
    else:
        logger.error(f"不支持的数据库类型: {db_type}")
        raise ValueError(f"不支持的数据库类型: {db_type}")


def _search_pgvector(query: str, client: Any, **kwargs) -> List[Dict]:
    """
    使用PgVector进行普通向量搜索的内部函数
    
    Args:
        query (str): 搜索查询文本
        client (Any): PgVector客户端实例
        **kwargs: PgVector特定参数
    
    Returns:
        List[Dict]: 搜索结果列表
    """
    from modules.pg_database.pgvector.rag import rag_search
    from modules.pg_database.llm.llm_chat import get_embeddings
    
    # 获取PgVector特定参数
    table_name = kwargs.get('table_name')
    if not table_name:
        logger.error("PgVector搜索必须提供table_name参数")
        raise ValueError("PgVector搜索必须提供table_name参数")
        
    embedding_function = kwargs.get('embedding_function', get_embeddings)
    vector_field = kwargs.get('vector_field', 'embedding_user_question')
    output_fields = kwargs.get('output_fields', ['user_question','knowledge_evidence','sql'])
    limit = kwargs.get('limit', 3)
    expr = kwargs.get('expr', '')
    partition_name = kwargs.get('partition_name', 9888)
    metric_type = kwargs.get('metric_type', 'cosine')
    
    # 获取嵌入向量
    try:
        embedding_result = embedding_function(query)
        embedding_data = embedding_result["data"][0]['embedding']
        embedding_vector = embedding_data
        
        # 执行普通搜索
        search_results = rag_search(
            vector_client=client,
            table_name=table_name,
            search_data=embedding_vector,
            vector_field=vector_field,
            output_fields=output_fields,
            expr=expr,
            partition_name=partition_name,
            limit=limit,
            metric_type=metric_type
        )
        
        # 格式化结果
        formatted_results = []
        for item in search_results:
            if isinstance(item, list):
                formatted_entry = {field: item[i] for i, field in enumerate(output_fields)}
                if len(item) > len(output_fields):
                    formatted_entry['distance'] = item[-1]
                formatted_results.append(formatted_entry)
            elif isinstance(item, dict):
                formatted_entry = {field: item[field] for field in output_fields if field in item}
                if 'distance' in item:
                    formatted_entry['distance'] = item['distance']
                formatted_results.append(formatted_entry)
            else:
                logger.warning(f"未知的结果类型: {type(item)}")
        
        logger.info(f"PgVector普通搜索结果: {formatted_results}")
        return formatted_results
    
    except Exception as e:
        logger.error(f"PgVector普通搜索失败: {str(e)}")
        raise Exception(f"PgVector普通搜索失败: {str(e)}")


def _search_milvus(query: str, client: Any, **kwargs) -> List[Dict]:
    """
    使用Milvus进行普通向量搜索的内部函数
    
    Args:
        query (str): 搜索查询文本
        client (Any): Milvus客户端实例
        **kwargs: Milvus特定参数
    
    Returns:
        List[Dict]: 搜索结果列表
    """
    # 获取Milvus特定参数
    collection_name = kwargs.get('collection_name')
    if not collection_name:
        logger.error("Milvus搜索必须提供collection_name参数")
        raise ValueError("Milvus搜索必须提供collection_name参数")
        
    # 这里需要实现Milvus特定的搜索逻辑
    # ...
    
    # 返回搜索结果的占位代码
    return [{"message": "Milvus普通搜索功能尚未实现"}]

def hybrid_search_vector_db(query: str, **kwargs) -> List[Dict]:
    """
    通用向量数据库混合搜索函数，支持不同类型的向量数据库的混合(hybrid)搜索。
    
    Args:
        query (str): 搜索查询文本
        **kwargs: 各数据库所需的特定参数
            - db_type (str): 数据库类型，默认为'pgvector'
            - client (Any, optional): 自定义数据库客户端实例，如不提供则使用默认客户端
    
    Returns:
        List[Dict]: 混合搜索结果列表
    """
    # 获取数据库类型
    db_type = kwargs.get('db_type', 'pgvector').lower()
    
    # 获取客户端，优先使用传入的客户端，否则使用默认客户端
    vector_client = kwargs.get('vector_client')
    if vector_client is None:
        if db_type in _default_clients:
            vector_client = _default_clients[db_type]
        else:
            logger.error(f"未提供客户端且没有默认客户端可用于数据库类型: {db_type}")
            raise ValueError(f"未提供客户端且没有默认客户端可用于数据库类型: {db_type}")
    mysql_client = kwargs.get('mysql_client', _default_clients['mysql'])
    # 根据数据库类型分发到不同处理函数
    if db_type == 'pgvector':
        kwargs['pgvector_client']=vector_client
        kwargs['mysql_client']=mysql_client
        return _hybrid_search_pgvector(query, **kwargs)
    elif db_type == 'milvus':
        return _hybrid_search_milvus(query, vector_client, **kwargs)
    # 可以添加其他数据库类型的处理
    else:
        logger.error(f"不支持的数据库类型: {db_type}")
        raise ValueError(f"不支持的数据库类型: {db_type}")


def _hybrid_search_pgvector(query: str, **kwargs) -> List[Dict]:
    """
    使用PgVector进行混合向量搜索的内部函数，通过get_all_content函数实现
    
    Args:
        query (str): 搜索查询文本
        client (Any): PgVector客户端实例
        **kwargs: PgVector特定参数
    
    Returns:
        List[Dict]: 混合搜索结果列表
    """
    # 获取必要参数
    
    # 从kwargs获取所需参数
    mysql_name = kwargs.get('mysql_name', 'model_info')
    partition = kwargs.get('partition', '')
    # 默认搜索类型
    search_types = kwargs.get('search_types', ['col_name_cn', 'col_desc', 'col_name'])
    # 获取表达式
    pgvector_client = kwargs.get('pgvector_client', _default_clients['pgvector'])
    mysql_client = kwargs.get('mysql_client', _default_clients['mysql'])
    topk = kwargs.get('topk', 3)
    try:
        # 调用get_all_content函数
        search_results = get_all_content(
            pg_client=pgvector_client,
            mysql_client=mysql_client,
            mysql_name=mysql_name,
            partition=partition,
            serach_content=query,
            type=search_types,
            topk=topk
        )
        
        logger.info(f"PgVector混合搜索结果通过get_all_content获取: {search_results}")
        return search_results
    
    except Exception as e:
        logger.error(f"PgVector混合搜索失败: {str(e)}")
        raise Exception(f"PgVector混合搜索失败: {str(e)}")


def _hybrid_search_milvus(query: str, client: Any, **kwargs) -> List[Dict]:
    """
    使用Milvus进行混合向量搜索的内部函数
    
    Args:
        query (str): 搜索查询文本
        client (Any): Milvus客户端实例
        **kwargs: Milvus特定参数
    
    Returns:
        List[Dict]: 混合搜索结果列表
    """
    # 获取Milvus特定参数
    collection_name = kwargs.get('collection_name')
    if not collection_name:
        logger.error("Milvus混合搜索必须提供collection_name参数")
        raise ValueError("Milvus混合搜索必须提供collection_name参数")
        
    # 这里需要实现Milvus特定的搜索逻辑
    # ...
    
    # 返回搜索结果的占位代码
    return [{"message": "Milvus混合搜索功能尚未实现"}]


def parse_search_results(search_results: List[Dict], format_type: str = 'default') -> Union[List[Dict], Dict, str]:
    """
    解析和格式化搜索结果
    
    Args:
        search_results (List[Dict]): 搜索结果列表
        format_type (str): 格式化类型，可选值：'default', 'json', 'text'
    
    Returns:
        Union[List[Dict], Dict, str]: 格式化后的结果
    """
    if not search_results:
        if format_type == 'json':
            return {}
        elif format_type == 'text':
            return ""
        return []
    
    if format_type == 'json':
        return json.loads(json.dumps(search_results, ensure_ascii=False))
    
    elif format_type == 'text':
        text_result = ""
        for i, result in enumerate(search_results):
            text_result += f"结果 {i+1}:\n"
            for key, value in result.items():
                if key != 'distance':  # 可选择性忽略距离值
                    text_result += f"  {key}: {value}\n"
            text_result += f"  相似度: {1-result.get('distance', 0):.4f}\n\n"
        return text_result.strip()
    
    # 默认返回原始列表
    return search_results



def parse_sql(sql, pgvector_client=_default_clients['pgvector'], mysql_client=_default_clients['mysql'],model_id:str=""):
    """
    解析SQL并返回处理结果
    
    参数:
        sql (str): 要解析的SQL查询
        pgvector_client: pgvector客户端实例，用于语义搜索
        
    返回:
        tuple: (new_formula, technical_caliber, index_formula, corrected_sql)
    """
    
    # 处理SQL查询
    new_formula, technical_caliber, index_formula, corrected_sql = process_sql_query(sql, pgvector_client, mysql_client,model_id)
    
    return new_formula, technical_caliber, index_formula, corrected_sql

def condition_corrector(pgvector_client: Any =_default_clients['pgvector'],
                        table_name: str = "",
                        value: str = "",
                        col_name: str = "",
                        distance: float = 0.5):
    return vector_search(pgvector_client, table_name, value, col_name, distance)

def get_partition_key(content: str, mysql_client: Any):
    return get_or_create_partition_key(content, mysql_client)
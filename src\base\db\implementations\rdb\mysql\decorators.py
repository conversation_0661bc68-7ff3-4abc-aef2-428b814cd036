"""
MySQL数据库操作装饰器

基于Universal架构设计的MySQL专用装饰器
提供自动连接管理、错误处理、重试机制等功能
"""

import asyncio
import time
import functools
from typing import Any, Callable, Dict, Optional, Union
import logging

logger = logging.getLogger(__name__)

from ....base.rdb import (
    QueryRequest, InsertRequest, UpdateRequest, DeleteRequest
)
from .exceptions import (
    wrap_mysql_error, MySQLError, MySQLConnectionError, 
    is_retryable_error, MySQLTimeoutError
)


def mysql_operation(operation_type: str, auto_connect: bool = True, 
                   max_retries: int = 3, retry_delay: float = 1.0):
    """
    MySQL操作装饰器
    
    提供自动连接管理、错误处理、重试机制等功能
    
    Args:
        operation_type: 操作类型（用于日志和错误上下文）
        auto_connect: 是否自动建立连接
        max_retries: 最大重试次数
        retry_delay: 重试延迟（秒）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(self, *args, **kwargs):
            # 自动连接管理
            if auto_connect:
                await self._ensure_async_connection()
            
            # 参数转换
            if args and isinstance(args[0], dict):
                # 将字典转换为对应的请求对象
                args = list(args)
                args[0] = _convert_dict_to_request(args[0], operation_type)
            
            # 重试机制
            last_error = None
            for attempt in range(max_retries + 1):
                try:
                    start_time = time.time()
                    result = await func(self, *args, **kwargs)
                    execution_time = time.time() - start_time
                    
                    # 记录成功日志
                    logger.debug(f"MySQL {operation_type} completed in {execution_time:.3f}s")
                    return result
                    
                except Exception as e:
                    last_error = e
                    
                    # 包装错误
                    if not isinstance(e, MySQLError):
                        e = wrap_mysql_error(e, operation_type)
                    
                    # 判断是否可重试
                    if attempt < max_retries and is_retryable_error(e):
                        logger.warning(f"MySQL {operation_type} failed (attempt {attempt + 1}/{max_retries + 1}): {e}")
                        await asyncio.sleep(retry_delay * (2 ** attempt))  # 指数退避
                        
                        # 如果是连接错误，尝试重新连接
                        if isinstance(e, MySQLConnectionError):
                            try:
                                await self._reconnect_async()
                            except Exception as reconnect_error:
                                logger.error(f"Failed to reconnect: {reconnect_error}")
                        continue
                    else:
                        # 不可重试或达到最大重试次数
                        logger.error(f"MySQL {operation_type} failed permanently: {e}")
                        raise e
            
            # 如果所有重试都失败了
            raise last_error

        @functools.wraps(func)
        def sync_wrapper(self, *args, **kwargs):
            # 自动连接管理
            if auto_connect:
                self._ensure_sync_connection()
            
            # 参数转换
            if args and isinstance(args[0], dict):
                # 将字典转换为对应的请求对象
                args = list(args)
                args[0] = _convert_dict_to_request(args[0], operation_type)
            
            # 重试机制
            last_error = None
            for attempt in range(max_retries + 1):
                try:
                    start_time = time.time()
                    result = func(self, *args, **kwargs)
                    execution_time = time.time() - start_time
                    
                    # 记录成功日志
                    logger.debug(f"MySQL {operation_type} completed in {execution_time:.3f}s")
                    return result
                    
                except Exception as e:
                    last_error = e
                    
                    # 包装错误
                    if not isinstance(e, MySQLError):
                        e = wrap_mysql_error(e, operation_type)
                    
                    # 判断是否可重试
                    if attempt < max_retries and is_retryable_error(e):
                        logger.warning(f"MySQL {operation_type} failed (attempt {attempt + 1}/{max_retries + 1}): {e}")
                        time.sleep(retry_delay * (2 ** attempt))  # 指数退避
                        
                        # 如果是连接错误，尝试重新连接
                        if isinstance(e, MySQLConnectionError):
                            try:
                                self._reconnect_sync()
                            except Exception as reconnect_error:
                                logger.error(f"Failed to reconnect: {reconnect_error}")
                        continue
                    else:
                        # 不可重试或达到最大重试次数
                        logger.error(f"MySQL {operation_type} failed permanently: {e}")
                        raise e
            
            # 如果所有重试都失败了
            raise last_error

        # 根据函数类型返回相应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def _convert_dict_to_request(data: Dict[str, Any], operation_type: str) -> Union[
    QueryRequest, InsertRequest, UpdateRequest, DeleteRequest
]:
    """将字典转换为对应的请求对象"""
    if operation_type == 'query':
        return QueryRequest(**data)
    elif operation_type == 'insert':
        return InsertRequest(**data)
    elif operation_type == 'update':
        return UpdateRequest(**data)
    elif operation_type == 'delete':
        return DeleteRequest(**data)
    else:
        raise ValueError(f"Unknown operation type: {operation_type}")


def mysql_transaction(isolation_level: Optional[str] = None, 
                     auto_rollback: bool = True):
    """
    MySQL事务装饰器
    
    自动管理事务的开始、提交和回滚
    
    Args:
        isolation_level: 事务隔离级别
        auto_rollback: 发生异常时是否自动回滚
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(self, *args, **kwargs):
            async with self.atransaction(isolation_level):
                return await func(self, *args, **kwargs)

        @functools.wraps(func)
        def sync_wrapper(self, *args, **kwargs):
            with self.transaction(isolation_level):
                return func(self, *args, **kwargs)

        # 根据函数类型返回相应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def mysql_performance_monitor(log_slow_queries: bool = True, 
                             slow_query_threshold: float = 1.0):
    """
    MySQL性能监控装饰器
    
    监控查询执行时间，记录慢查询
    
    Args:
        log_slow_queries: 是否记录慢查询
        slow_query_threshold: 慢查询阈值（秒）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(self, *args, **kwargs):
            start_time = time.time()
            try:
                result = await func(self, *args, **kwargs)
                return result
            finally:
                execution_time = time.time() - start_time
                
                if log_slow_queries and execution_time > slow_query_threshold:
                    logger.warning(
                        f"Slow MySQL query detected: {func.__name__} "
                        f"took {execution_time:.3f}s (threshold: {slow_query_threshold}s)"
                    )
                
                # 记录性能指标
                if hasattr(self, '_performance_stats'):
                    self._performance_stats.setdefault('query_times', []).append(execution_time)

        @functools.wraps(func)
        def sync_wrapper(self, *args, **kwargs):
            start_time = time.time()
            try:
                result = func(self, *args, **kwargs)
                return result
            finally:
                execution_time = time.time() - start_time
                
                if log_slow_queries and execution_time > slow_query_threshold:
                    logger.warning(
                        f"Slow MySQL query detected: {func.__name__} "
                        f"took {execution_time:.3f}s (threshold: {slow_query_threshold}s)"
                    )
                
                # 记录性能指标
                if hasattr(self, '_performance_stats'):
                    self._performance_stats.setdefault('query_times', []).append(execution_time)

        # 根据函数类型返回相应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def mysql_cache(cache_key_func: Optional[Callable] = None, 
               ttl: int = 300, 
               enabled: bool = True):
    """
    MySQL查询缓存装饰器
    
    缓存查询结果以提高性能
    
    Args:
        cache_key_func: 生成缓存键的函数
        ttl: 缓存生存时间（秒）
        enabled: 是否启用缓存
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(self, *args, **kwargs):
            if not enabled or not hasattr(self, '_cache'):
                return await func(self, *args, **kwargs)
            
            # 生成缓存键
            if cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 检查缓存
            cached_result = self._cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_result
            
            # 执行查询并缓存结果
            result = await func(self, *args, **kwargs)
            self._cache.set(cache_key, result, ttl)
            logger.debug(f"Cached result for {cache_key}")
            
            return result

        @functools.wraps(func)
        def sync_wrapper(self, *args, **kwargs):
            if not enabled or not hasattr(self, '_cache'):
                return func(self, *args, **kwargs)
            
            # 生成缓存键
            if cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 检查缓存
            cached_result = self._cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_result
            
            # 执行查询并缓存结果
            result = func(self, *args, **kwargs)
            self._cache.set(cache_key, result, ttl)
            logger.debug(f"Cached result for {cache_key}")
            
            return result

        # 根据函数类型返回相应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def mysql_timeout(timeout_seconds: float):
    """
    MySQL操作超时装饰器
    
    为MySQL操作设置超时限制
    
    Args:
        timeout_seconds: 超时时间（秒）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(self, *args, **kwargs):
            try:
                return await asyncio.wait_for(
                    func(self, *args, **kwargs), 
                    timeout=timeout_seconds
                )
            except asyncio.TimeoutError:
                raise MySQLTimeoutError(
                    f"MySQL operation {func.__name__} timed out after {timeout_seconds}s"
                )

        @functools.wraps(func)
        def sync_wrapper(self, *args, **kwargs):
            # 同步版本的超时实现比较复杂，这里简化处理
            # 在实际应用中可能需要使用signal或threading.Timer
            return func(self, *args, **kwargs)

        # 根据函数类型返回相应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator

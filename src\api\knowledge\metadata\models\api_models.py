"""
元数据API特有的数据模型

定义API层特有的数据模型，补充modules层的基础模型。
"""

from typing import Any, Dict, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


# ==================== 搜索相关模型 ====================

class MetadataSearchRequest(BaseModel):
    """元数据搜索请求模型"""
    query: str = Field(..., description="搜索查询", example="客户信息")
    knowledge_id: Optional[str] = Field(None, description="知识库ID过滤")
    entity_type: Optional[str] = Field(None, description="实体类型过滤", example="column")
    search_type: str = Field("hybrid", description="搜索类型：vector/text/hybrid")
    limit: int = Field(10, description="返回结果数量限制", ge=1, le=100)
    min_score: float = Field(0.5, description="最小相似度分数", ge=0.0, le=1.0)
    vector_weight: float = Field(0.7, description="向量搜索权重", ge=0.0, le=1.0)
    text_weight: float = Field(0.3, description="文本搜索权重", ge=0.0, le=1.0)

    class Config:
        json_schema_extra = {
            "example": {
                "query": "客户信息",
                "entity_type": "column",
                "search_type": "hybrid",
                "limit": 10,
                "min_score": 0.5,
                "vector_weight": 0.7,
                "text_weight": 0.3
            }
        }


class MetadataSearchResultItem(BaseModel):
    """元数据搜索结果项模型"""
    score: float = Field(..., description="相似度分数")
    entity_type: str = Field(..., description="实体类型")
    entity_data: Dict[str, Any] = Field(..., description="实体数据")
    vector_info: Optional[Dict[str, Any]] = Field(None, description="向量信息")

    class Config:
        json_schema_extra = {
            "example": {
                "score": 0.85,
                "entity_type": "column",
                "entity_data": {
                    "column_id": 1,
                    "column_name": "customer_name",
                    "column_name_cn": "客户姓名"
                },
                "vector_info": {
                    "vector_id": "vec_001",
                    "field": "column_name_cn"
                }
            }
        }


class MetadataSearchResponse(BaseModel):
    """元数据搜索响应模型"""
    results: List[MetadataSearchResultItem] = Field(..., description="搜索结果列表")
    total: int = Field(..., description="总结果数")
    query: str = Field(..., description="搜索查询")
    search_time: float = Field(..., description="搜索耗时(秒)")
    search_type: str = Field(..., description="搜索类型")

    class Config:
        json_schema_extra = {
            "example": {
                "results": [
                    {
                        "score": 0.85,
                        "entity_type": "column",
                        "entity_data": {
                            "column_id": 1,
                            "column_name": "customer_name"
                        }
                    }
                ],
                "total": 1,
                "query": "客户姓名",
                "search_time": 0.123,
                "search_type": "hybrid"
            }
        }


# ==================== 统计相关模型 ====================

class MetadataStatsResponse(BaseModel):
    """元数据统计响应模型"""
    knowledge_id: str = Field(..., description="知识库ID")
    databases: int = Field(..., description="数据库数量")
    tables: int = Field(..., description="表数量")
    columns: int = Field(..., description="字段数量")
    code_sets: int = Field(..., description="码值集数量")
    data_subjects: int = Field(..., description="数据主题数量")
    relations: int = Field(..., description="关联关系数量")
    vectorized_entities: int = Field(..., description="已向量化实体数量")
    total_vectors: int = Field(..., description="总向量数量")
    last_update: Optional[datetime] = Field(None, description="最后更新时间")

    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_id": "kb_001",
                "databases": 5,
                "tables": 25,
                "columns": 150,
                "code_sets": 10,
                "data_subjects": 8,
                "relations": 30,
                "vectorized_entities": 120,
                "total_vectors": 240,
                "last_update": "2024-01-01T10:00:00"
            }
        }


# ==================== 系统管理相关模型 ====================

class MetadataHealthResponse(BaseModel):
    """元数据系统健康检查响应模型"""
    status: str = Field(..., description="系统状态")
    service: str = Field(..., description="服务名称")
    version: str = Field(..., description="版本号")
    modules: List[str] = Field(..., description="功能模块列表")
    database_status: Dict[str, str] = Field(..., description="数据库状态")

    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "service": "元数据管理系统",
                "version": "2.0.0",
                "modules": [
                    "数据库管理",
                    "表管理",
                    "字段管理",
                    "码值集管理",
                    "数据主题管理",
                    "关联关系管理",
                    "搜索功能"
                ],
                "database_status": {
                    "mysql": "connected",
                    "pgvector": "connected"
                }
            }
        }


class MetadataOverviewResponse(BaseModel):
    """元数据系统概览响应模型"""
    service: str = Field(..., description="服务名称")
    description: str = Field(..., description="服务描述")
    api_groups: Dict[str, Any] = Field(..., description="API分组信息")
    features: List[str] = Field(..., description="功能特性列表")

    class Config:
        json_schema_extra = {
            "example": {
                "service": "元数据管理系统",
                "description": "提供元数据的完整管理和搜索功能",
                "api_groups": {
                    "数据库管理": {
                        "prefix": "/api/knowledge/metadata/databases",
                        "endpoints": ["POST /", "GET /{db_id}", "PUT /{db_id}"]
                    }
                },
                "features": [
                    "自动向量化处理",
                    "智能语义搜索",
                    "RESTful API设计"
                ]
            }
        }


# ==================== 批量操作相关模型 ====================

class BatchOperationStatus(BaseModel):
    """批量操作状态模型"""
    operation_id: str = Field(..., description="操作ID")
    status: str = Field(..., description="操作状态：pending/processing/completed/failed")
    progress: float = Field(..., description="进度百分比", ge=0.0, le=100.0)
    total_items: int = Field(..., description="总项目数")
    processed_items: int = Field(..., description="已处理项目数")
    success_items: int = Field(..., description="成功项目数")
    failed_items: int = Field(..., description="失败项目数")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    error_message: Optional[str] = Field(None, description="错误信息")

    class Config:
        json_schema_extra = {
            "example": {
                "operation_id": "batch_001",
                "status": "processing",
                "progress": 75.0,
                "total_items": 100,
                "processed_items": 75,
                "success_items": 70,
                "failed_items": 5,
                "start_time": "2024-01-01T10:00:00",
                "end_time": None,
                "error_message": None
            }
        }

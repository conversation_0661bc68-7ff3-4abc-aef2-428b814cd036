from abc import ABC, abstractmethod
from contextlib import asynccontextmanager, contextmanager
from typing import (
    Any, Dict, List, Optional, Union, AsyncContextManager, 
    ContextManager, TypeVar, Generic, Sequence, Mapping
)
from sqlalchemy import Table, Select, Insert, Update, Delete, MetaData
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

# 类型变量
T = TypeVar('T')
RowType = Dict[str, Any]
QueryResult = List[RowType]

class ConnectionConfig(ABC):
    """数据库连接配置的抽象基类"""
    pass

class QueryBuilder(ABC):
    """查询构建器的抽象基类"""
    
    @abstractmethod
    def select(self, *columns) -> 'QueryBuilder':
        """选择列"""
        pass
    
    @abstractmethod
    def where(self, condition) -> 'QueryBuilder':
        """添加WHERE条件"""
        pass
    
    @abstractmethod
    def order_by(self, *columns) -> 'QueryBuilder':
        """添加排序"""
        pass
    
    @abstractmethod
    def limit(self, count: int) -> 'QueryBuilder':
        """限制结果数量"""
        pass
    
    @abstractmethod
    async def fetch_all(self) -> QueryResult:
        """执行查询并返回所有结果"""
        pass
    
    @abstractmethod
    async def fetch_one(self) -> Optional[RowType]:
        """执行查询并返回单个结果"""
        pass

class ModernRDBClient(ABC):
    """
    现代化的关系型数据库客户端抽象基类
    
    设计原则：
    1. 最小化抽象 - 只定义核心必需的方法
    2. 统一命名 - 使用一致的方法命名规范
    3. 默认实现 - 提供合理的默认实现
    4. 现代化 - 支持SQLAlchemy 2.0+和异步编程
    5. 类型安全 - 完整的类型提示
    """
    
    # ==================== 核心抽象方法 ====================
    
    @abstractmethod
    async def connect(self, config: ConnectionConfig) -> None:
        """
        连接到数据库
        
        Args:
            config: 数据库连接配置
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """断开数据库连接"""
        pass
    
    @abstractmethod
    def get_table(self, table_name: str) -> Table:
        """
        获取表对象（支持自动反射）
        
        Args:
            table_name: 表名
            
        Returns:
            SQLAlchemy Table对象
        """
        pass
    
    @abstractmethod
    def query(self, table_name: str) -> QueryBuilder:
        """
        获取查询构建器
        
        Args:
            table_name: 表名
            
        Returns:
            查询构建器实例
        """
        pass
    
    # ==================== CRUD操作（核心抽象）====================
    
    @abstractmethod
    async def insert(
        self, 
        table_name: str, 
        data: Union[RowType, Sequence[RowType]]
    ) -> Union[Any, List[Any]]:
        """
        插入数据
        
        Args:
            table_name: 表名
            data: 要插入的数据，可以是单条记录或多条记录
            
        Returns:
            插入的记录ID或ID列表
        """
        pass
    
    @abstractmethod
    async def select(
        self,
        table_name: str,
        columns: Optional[Sequence[str]] = None,
        where: Optional[Dict[str, Any]] = None,
        order_by: Optional[Sequence[str]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> QueryResult:
        """
        查询数据
        
        Args:
            table_name: 表名
            columns: 要查询的列名列表，None表示查询所有列
            where: 查询条件字典
            order_by: 排序字段列表
            limit: 限制返回记录数
            offset: 偏移量
            
        Returns:
            查询结果列表
        """
        pass
    
    @abstractmethod
    async def update(
        self,
        table_name: str,
        values: RowType,
        where: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        更新数据
        
        Args:
            table_name: 表名
            values: 要更新的值
            where: 更新条件
            
        Returns:
            受影响的行数
        """
        pass
    
    @abstractmethod
    async def delete(
        self,
        table_name: str,
        where: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        删除数据
        
        Args:
            table_name: 表名
            where: 删除条件
            
        Returns:
            受影响的行数
        """
        pass
    
    # ==================== 事务管理 ====================
    
    @abstractmethod
    @asynccontextmanager
    async def transaction(self) -> AsyncContextManager[AsyncSession]:
        """
        异步事务上下文管理器
        
        Returns:
            异步会话对象
        """
        pass
    
    # ==================== 高级操作（提供默认实现）====================
    
    async def upsert(
        self,
        table_name: str,
        data: Union[RowType, Sequence[RowType]],
        conflict_columns: Optional[Sequence[str]] = None,
        update_columns: Optional[Sequence[str]] = None
    ) -> Union[Any, List[Any]]:
        """
        插入或更新数据（默认实现）
        
        Args:
            table_name: 表名
            data: 要插入/更新的数据
            conflict_columns: 冲突检测列
            update_columns: 要更新的列
            
        Returns:
            操作结果
        """
        # 子类可以重写此方法以提供更高效的实现
        # 默认实现：先尝试插入，失败则更新
        try:
            return await self.insert(table_name, data)
        except Exception:
            # 简化的更新逻辑，实际实现会更复杂
            if isinstance(data, dict):
                data = [data]
            results = []
            for record in data:
                if conflict_columns:
                    where_clause = {col: record[col] for col in conflict_columns if col in record}
                    update_data = {col: record[col] for col in update_columns or record.keys() 
                                 if col not in (conflict_columns or [])}
                    if update_data:
                        await self.update(table_name, update_data, where_clause)
                results.append(None)  # 更新操作通常不返回ID
            return results[0] if len(results) == 1 else results
    
    async def bulk_insert(
        self,
        table_name: str,
        data: Sequence[RowType],
        batch_size: int = 1000
    ) -> List[Any]:
        """
        批量插入数据（默认实现）
        
        Args:
            table_name: 表名
            data: 要插入的数据列表
            batch_size: 批次大小
            
        Returns:
            插入的ID列表
        """
        results = []
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]
            batch_results = await self.insert(table_name, batch)
            if isinstance(batch_results, list):
                results.extend(batch_results)
            else:
                results.append(batch_results)
        return results

    async def count(
        self,
        table_name: str,
        where: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        计算记录数（默认实现）

        Args:
            table_name: 表名
            where: 查询条件

        Returns:
            记录数
        """
        # 子类可以重写以提供更高效的实现
        results = await self.select(table_name, columns=['COUNT(*) as count'], where=where)
        return results[0]['count'] if results else 0

    async def exists(
        self,
        table_name: str,
        where: Dict[str, Any]
    ) -> bool:
        """
        检查记录是否存在（默认实现）

        Args:
            table_name: 表名
            where: 查询条件

        Returns:
            是否存在
        """
        count = await self.count(table_name, where)
        return count > 0

    # ==================== 原生SQL执行（可选实现）====================

    async def execute_query(
        self,
        query: Union[str, Select],
        params: Optional[Mapping[str, Any]] = None
    ) -> QueryResult:
        """
        执行查询语句（可选实现）

        Args:
            query: SQL查询语句或SQLAlchemy Select对象
            params: 查询参数

        Returns:
            查询结果
        """
        raise NotImplementedError("execute_query not implemented")

    async def execute_command(
        self,
        command: Union[str, Insert, Update, Delete],
        params: Optional[Mapping[str, Any]] = None
    ) -> int:
        """
        执行命令语句（可选实现）

        Args:
            command: SQL命令语句或SQLAlchemy命令对象
            params: 命令参数

        Returns:
            受影响的行数
        """
        raise NotImplementedError("execute_command not implemented")

    # ==================== 表管理（可选实现）====================

    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在（可选实现）

        Args:
            table_name: 表名

        Returns:
            表是否存在
        """
        raise NotImplementedError("table_exists not implemented")

    def list_tables(self) -> List[str]:
        """
        列出所有表（可选实现）

        Returns:
            表名列表
        """
        raise NotImplementedError("list_tables not implemented")

    # ==================== 连接状态管理 ====================

    @property
    def is_connected(self) -> bool:
        """
        检查是否已连接（默认实现）

        Returns:
            是否已连接
        """
        return hasattr(self, '_engine') and self._engine is not None

    async def ping(self) -> bool:
        """
        测试连接是否正常（默认实现）

        Returns:
            连接是否正常
        """
        try:
            await self.execute_query("SELECT 1")
            return True
        except Exception:
            return False

    # ==================== 同步接口支持（可选）====================

    @contextmanager
    def sync_transaction(self) -> ContextManager[Session]:
        """
        同步事务上下文管理器（可选实现）

        Returns:
            同步会话对象
        """
        raise NotImplementedError("sync_transaction not implemented")

    def sync_select(
        self,
        table_name: str,
        columns: Optional[Sequence[str]] = None,
        where: Optional[Dict[str, Any]] = None,
        order_by: Optional[Sequence[str]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> QueryResult:
        """
        同步查询数据（可选实现）
        """
        raise NotImplementedError("sync_select not implemented")

    # ==================== 工具方法 ====================

    def __enter__(self):
        """同步上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """同步上下文管理器出口"""
        pass

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()

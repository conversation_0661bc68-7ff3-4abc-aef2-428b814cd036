"""
元数据表管理API路由

提供表的完整CRUD操作接口，参照DD系统的设计模式。
"""

from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Depends, Path
import logging

# 标准化日志导入
logger = logging.getLogger(__name__)

from api.knowledge.models.response_models import (
    ListResponse, CreateResponse, UpdateResponse, DeleteResponse, DetailResponse
)
from ..models import TableCreateRequest, TableUpdateRequest, TableTypeEnum
from ..dependencies import get_metadata_crud, validate_knowledge_id, validate_pagination

# 创建路由器
router = APIRouter(tags=["元数据表管理"], prefix="/tables")


@router.post("/", response_model=CreateResponse, summary="创建表")
async def create_table(
    knowledge_id: str = Query(..., description="知识库ID"),
    request: TableCreateRequest = ...,
    metadata_crud = Depends(get_metadata_crud)
):
    """创建新表"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        table_data = request.model_dump()
        table_id = await metadata_crud.create_table(knowledge_id, table_data)
        
        return CreateResponse(
            success=True,
            message="表创建成功",
            data={"table_id": table_id, "knowledge_id": knowledge_id}
        )
    except Exception as e:
        logger.error(f"创建表失败: {e}")
        raise HTTPException(status_code=400, detail=f"创建表失败: {str(e)}")


@router.get("/{table_id}", response_model=DetailResponse, summary="获取表详情")
async def get_table(
    table_id: int = Path(..., description="表ID"),
    knowledge_id: str = Query(..., description="知识库ID"),
    metadata_crud = Depends(get_metadata_crud)
):
    """获取表详情"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        table = await metadata_crud.get_table(knowledge_id, table_id)
        
        if not table:
            raise HTTPException(status_code=404, detail=f"表不存在: {table_id}")
        
        return DetailResponse(
            success=True,
            message="获取表详情成功",
            data=table
        )
    except Exception as e:
        logger.error(f"获取表详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取表详情失败: {str(e)}")


@router.put("/{table_id}", response_model=UpdateResponse, summary="更新表信息")
async def update_table(
    table_id: int = Path(..., description="表ID"),
    knowledge_id: str = Query(..., description="知识库ID"),
    request: TableUpdateRequest = ...,
    metadata_crud = Depends(get_metadata_crud)
):
    """更新表信息"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        update_data = {k: v for k, v in request.model_dump().items() if v is not None}
        
        if not update_data:
            raise HTTPException(status_code=400, detail="没有提供要更新的字段")
        
        success = await metadata_crud.update_table(knowledge_id, table_id, update_data)
        
        return UpdateResponse(
            success=True,
            message="表更新成功",
            data={"table_id": table_id, "knowledge_id": knowledge_id}
        )
    except Exception as e:
        logger.error(f"更新表失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新表失败: {str(e)}")


@router.delete("/{table_id}", response_model=DeleteResponse, summary="删除表")
async def delete_table(
    table_id: int = Path(..., description="表ID"),
    knowledge_id: str = Query(..., description="知识库ID"),
    metadata_crud = Depends(get_metadata_crud)
):
    """删除表"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        success = await metadata_crud.delete_table(knowledge_id, table_id)
        
        return DeleteResponse(
            success=True,
            message="表删除成功",
            data={"table_id": table_id, "knowledge_id": knowledge_id}
        )
    except Exception as e:
        logger.error(f"删除表失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除表失败: {str(e)}")


@router.get("/", response_model=ListResponse, summary="查询表列表")
async def list_tables(
    knowledge_id: str = Query(..., description="知识库ID"),
    db_id: Optional[int] = Query(None, description="数据库ID过滤"),
    table_type: Optional[TableTypeEnum] = Query(None, description="表类型过滤"),
    is_active: Optional[bool] = Query(None, description="激活状态过滤"),
    pagination = Depends(validate_pagination),
    metadata_crud = Depends(get_metadata_crud)
):
    """查询表列表"""
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        page, page_size, offset = pagination
        
        filters = {}
        if db_id is not None:
            filters["db_id"] = db_id
        if table_type is not None:
            filters["table_type"] = table_type.value
        if is_active is not None:
            filters["is_active"] = is_active
        
        tables = await metadata_crud.list_tables(knowledge_id, **filters)
        
        total = len(tables)
        paginated_tables = tables[offset:offset + page_size]
        total_pages = (total + page_size - 1) // page_size
        
        return ListResponse(
            success=True,
            message="查询表列表成功",
            data={
                "items": paginated_tables,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }
        )
    except Exception as e:
        logger.error(f"查询表列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询表列表失败: {str(e)}")

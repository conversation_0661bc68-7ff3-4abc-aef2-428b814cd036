"""
DD SQL推荐服务数据模型
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class SQLRecommendRequest(BaseModel):
    """SQL推荐请求模型"""
    report_code: str = Field(..., description="报表代码，如G0107_beta_..，对应version")
    dept_id: str = Field(..., description="部门ID")
    entry_id: str = Field(..., description="条目ID，对应submission_id")


class SQLRecommendResponse(BaseModel):
    """SQL推荐响应模型"""
    sql: str = Field(..., description="推荐的SQL语句")


class QuestionRecommendRequest(BaseModel):
    """问题推荐请求模型"""
    report_code: str = Field(..., description="报表代码，如G0107_beta_..，对应version")
    dept_id: str = Field(..., description="部门ID")
    entry_id: str = Field(..., description="条目ID，对应submission_id")


class QuestionRecommendResponse(BaseModel):
    """问题推荐响应模型"""
    question_list: List[str] = Field(..., description="推荐的问题列表")


class SQLGenerateRequest(BaseModel):
    """SQL生成请求模型"""
    report_code: str = Field(..., description="报表代码，如G0107_beta_..，对应version")
    dept_id: str = Field(..., description="部门ID")
    entry_id: str = Field(..., description="条目ID，对应submission_id")
    question_list: List[str] = Field(..., description="问题列表")


class SQLGenerateResponse(BaseModel):
    """SQL生成响应模型"""
    sql_list: List[str] = Field(..., description="生成的SQL列表")


class SingleSQLGenerateRequest(BaseModel):
    """单个SQL生成请求模型"""
    report_code: str = Field(..., description="报表代码，如G0107_beta_..，对应version")
    dept_id: str = Field(..., description="部门ID")
    entry_id: str = Field(..., description="条目ID，对应submission_id")
    question: str = Field(..., description="问题")


class SingleSQLGenerateResponse(BaseModel):
    """单个SQL生成响应模型"""
    sql: str = Field(..., description="生成的SQL语句")


class SQLIntegrationRequest(BaseModel):
    """SQL整合请求模型"""
    report_code: str = Field(..., description="报表代码，如G0107_beta_..，对应version")
    dept_id: str = Field(..., description="部门ID")
    entry_id: str = Field(..., description="条目ID，对应submission_id")
    question_list: List[str] = Field(..., description="问题列表")
    sql_list: List[str] = Field(..., description="SQL列表")


class SQLIntegrationResponse(BaseModel):
    """SQL整合响应模型"""
    sql: str = Field(..., description="整合后的SQL语句")
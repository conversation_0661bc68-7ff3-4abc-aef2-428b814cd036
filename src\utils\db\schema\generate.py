# -*- coding: utf-8 -*-
"""
重构版 Schema 生成器

简化版本，直接集成service层

业务假设：
- table_name 在系统中全局唯一，可以直接用于WHERE条件查询而无需额外的唯一性约束
- 这个假设简化了table_names参数的实现逻辑，避免了复杂的去重和冲突处理
"""

import logging
import random
from typing import List, Dict, Any, Optional, Tuple, Union, Set, Generator, Callable
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class SchemaState:
    """
    Schema生成状态管理类

    用于跟踪智能分割过程中的状态信息，支持迭代处理和数据来源跟踪
    """
    input_method: str  # 输入方法：from_table_names, from_table_ids, from_column_ids, from_candidate_columns
    original_params: Dict[str, Any]  # 原始输入参数
    processed_items: Set[str] = field(default_factory=set)  # 已处理的项目（表名或列名）
    remaining_tables: List[Dict] = field(default_factory=list)  # 剩余的表信息
    remaining_columns: List[Dict] = field(default_factory=list)  # 剩余的列信息
    source_mapping: Dict[str, str] = field(default_factory=dict)  # 项目到来源的映射
    total_items: int = 0  # 总项目数
    processed_count: int = 0  # 已处理项目数

    def add_processed_item(self, item: str) -> None:
        """添加已处理的项目"""
        self.processed_items.add(item)
        self.processed_count = len(self.processed_items)

    def is_complete(self) -> bool:
        """检查是否已完成所有处理"""
        return self.processed_count >= self.total_items

    def get_progress(self) -> float:
        """获取处理进度（0.0-1.0）"""
        if self.total_items == 0:
            return 1.0
        return self.processed_count / self.total_items


class SchemaGenerator:
    """
    重构版 Schema 生成器 - 优雅、简洁、强大

    业务假设：
    - table_name 在系统中全局唯一，可以直接用于WHERE条件查询

    核心特性：
    - 统一的智能分割接口
    - 自动化的批量处理
    - 完整的上下文保持
    - 灵活的状态管理

    简单使用：
        generator = SchemaGenerator("source", knowledge_id="xxx")

        # 一行代码获取完整分割结果
        schemas = await generator.from_table_names(["users", "orders"]).split(token_size=7500)

        # 自动处理所有数据
        for schema in await generator.from_table_ids([34, 65]).process_all():
            send_to_llm(schema)
    """

    def __init__(self,
                 source_type: str,
                 knowledge_id: Optional[str] = None,
                 client=None):
        """
        初始化Schema生成器

        Args:
            source_type: 数据源类型，'source' 或 'index'
            knowledge_id: 可选的知识库ID
            client: 可选的数据库客户端
        """
        if source_type not in ['source', 'index']:
            raise ValueError("source_type must be 'source' or 'index'")

        self.source_type = source_type
        self.knowledge_id = knowledge_id
        self.client = client

        # 内部数据存储
        self._tables_info: List[Dict] = []
        self._columns_info: List[Dict] = []
        self._input_method: Optional[str] = None
        self._original_params: Dict[str, Any] = {}

        # 数据库表名配置
        self.table_prefix = 'md_source' if source_type == 'source' else 'md_index'
        self.db_table = f"{self.table_prefix}_database"
        self.tables_table = f"{self.table_prefix}_tables"
        self.columns_table = f"{self.table_prefix}_columns"

    # ==================== 核心数据加载方法 ====================

    async def from_table_names(self,
                              table_names: List[str],
                              column_limit: int = 5) -> 'SchemaGenerator':
        """
        从表名列表加载数据（推荐使用）

        Args:
            table_names: 表名列表，如 ["users", "orders"]
            column_limit: 每个表的列数量限制，默认5个

        Returns:
            self: 支持链式调用

        Example:
            schemas = await generator.from_table_names(["users", "orders"]).split()
        """
        await self._load_data_by_table_names(table_names, column_limit)
        return self

    async def from_table_ids(self,
                            table_ids: List[int],
                            column_limit: int = 5) -> 'SchemaGenerator':
        """
        从表ID列表加载数据

        Args:
            table_ids: 表ID列表，如 [34, 65]
            column_limit: 每个表的列数量限制，默认5个

        Returns:
            self: 支持链式调用

        Example:
            schemas = await generator.from_table_ids([34, 65]).split()
        """
        await self._load_data_by_table_ids(table_ids, column_limit)
        return self

    async def from_column_ids(self, column_ids: List[int]) -> 'SchemaGenerator':
        """
        从列ID列表加载数据

        Args:
            column_ids: 列ID列表，如 [25, 27, 29, 30]

        Returns:
            self: 支持链式调用

        Example:
            schemas = await generator.from_column_ids([25, 27]).split()
        """
        await self._load_data_by_column_ids(column_ids)
        return self

    async def from_candidate_columns(self,
                                   candidate_columns: Dict[str, List[str]]) -> 'SchemaGenerator':
        """
        从候选列字典加载数据

        Args:
            candidate_columns: 候选列字典，格式为 {"table_name": ["col1", "col2"]}

        Returns:
            self: 支持链式调用

        Example:
            candidates = {"users": ["id", "name"], "orders": ["id", "amount"]}
            schemas = await generator.from_candidate_columns(candidates).split()
        """
        await self._load_data_by_candidate_columns(candidate_columns)
        return self

    # ==================== 核心输出方法 ====================

    def split(self, token_size: int = 7500) -> List[str]:
        """
        智能分割为多个完整的schema片段（主要方法）

        Args:
            token_size: 每个片段的token限制，默认7500

        Returns:
            分割后的schema字符串列表，每个都包含完整上下文

        Example:
            generator = SchemaGenerator("source")
            await generator.from_table_names(["users"])
            schemas = generator.split(token_size=7500)
            for schema in schemas:
                send_to_llm(schema)  # 每个都是独立可用的
        """
        return self._split_with_complete_context(token_size)

    def get_prompt_list_with_mappings(self, token_size: int = 7500) -> List[Dict[str, Any]]:
        """
        获取带映射信息的prompt列表（新增方法）

        Args:
            token_size: 每个片段的token限制，默认7500

        Returns:
            包含prompt和映射信息的字典列表，格式为：
            [
                {
                    "prompt": "schema字符串",
                    "db_to_tables": {"db1": ["table1", "table2"], ...},
                    "table_to_columns": {"table1": ["col1", "col2"], ...},
                    "db_to_tables_id": {db_id1: [table_id1, table_id2], ...},
                    "table_to_columns_id": {table_id1: [col_id1, col_id2], ...}
                },
                ...
            ]

        Example:
            generator = SchemaGenerator("source")
            await generator.from_table_names(["贷款信息表", "交易记录表"])
            prompt_data = generator.get_prompt_list_with_mappings(token_size=7500)
            for item in prompt_data:
                send_to_llm(item["prompt"])
                process_mappings(item["db_to_tables"], item["table_to_columns"])
                process_id_mappings(item["db_to_tables_id"], item["table_to_columns_id"])
        """
        return self._split_with_mappings(token_size)

    def get_complete_schema(self) -> str:
        """
        获取完整的schema字符串（不分割）

        Returns:
            完整的schema字符串

        Example:
            schema = await generator.from_table_names(["users"]).get_complete_schema()
        """
        return self._build_complete_schema()

    async def process_all(self,
                         token_size: int = 7500,
                         max_batches: int = 100) -> List[str]:
        """
        自动处理所有数据，返回所有schema片段（简化接口）

        Args:
            token_size: 每个片段的token限制，默认7500
            max_batches: 最大批次数，防止无限循环，默认100

        Returns:
            所有schema片段的列表

        Example:
            schemas = await generator.from_table_names(["users", "orders"]).process_all()
            for schema in schemas:
                process_schema(schema)
        """
        return self.split(token_size)

    def process_with_callback(self,
                             token_size: int = 7500,
                             callback: Optional[Callable[[str, int, int], bool]] = None) -> List[str]:
        """
        带回调函数的处理（高级接口）

        Args:
            token_size: 每个片段的token限制，默认7500
            callback: 回调函数，签名为 (schema, batch_index, total_batches) -> should_continue

        Returns:
            处理的schema片段列表

        Example:
            def progress_callback(schema, batch_index, total_batches):
                print(f"处理进度: {batch_index+1}/{total_batches}")
                return True

            schemas = generator.process_with_callback(callback=progress_callback)
        """
        schemas = self.split(token_size)

        if callback:
            processed_schemas = []
            for i, schema in enumerate(schemas):
                should_continue = callback(schema, i, len(schemas))
                processed_schemas.append(schema)
                if not should_continue:
                    break
            return processed_schemas

        return schemas

    async def get_candidates(self) -> Dict[str, Any]:
        """
        生成候选数据结构

        Returns:
            包含候选数据的字典，格式为：
            {
                "candidate_tables": {"db_name1": ["Table1", "Table2"], ...},
                "candidate_columns": {"table_name1": ["column1", "column2"], ...},
                "candidate_tables_ids": {"db_id1": [table_id_1, table_id_2], ...},
                "candidate_columns_ids": {table_id_1: [column_id_1, column_id_2], ...}
            }

        Example:
            candidates = await generator.from_table_names(["users"]).get_candidates()
            print(candidates["candidate_columns"])
        """
        return self._generate_candidates_data()

    # ==================== 内部实现方法 ====================

    async def _get_client(self):
        """获取数据库客户端"""
        if self.client is None:
            from service import get_client
            self.client = await get_client("database.rdbs.mysql")
        return self.client

    async def _load_data_by_table_names(self, table_names: List[str], column_limit: int):
        """通过表名加载数据"""
        self._input_method = "from_table_names"
        self._original_params = {"table_names": table_names, "column_limit": column_limit}

        client = await self._get_client()

        # 查询表信息
        self._tables_info = await _fetch_tables_info_by_names(
            client, self.tables_table, self.db_table, table_names, self.knowledge_id
        )

        if not self._tables_info:
            logger.warning(f"No tables found for names: {table_names}")
            return

        # 查询列信息
        columns_info = await _fetch_columns_info(
            client, self.columns_table, self._tables_info, self.knowledge_id
        )

        # 应用列数量限制
        self._columns_info = _apply_column_limit(columns_info, column_limit)

    async def _load_data_by_table_ids(self, table_ids: List[int], column_limit: int):
        """通过表ID加载数据"""
        self._input_method = "from_table_ids"
        self._original_params = {"table_ids": table_ids, "column_limit": column_limit}

        client = await self._get_client()

        # 查询表信息
        self._tables_info = await _fetch_tables_info_by_ids(
            client, self.tables_table, self.db_table, table_ids, self.knowledge_id
        )

        if not self._tables_info:
            logger.warning(f"No tables found for IDs: {table_ids}")
            return

        # 查询列信息
        columns_info = await _fetch_columns_info(
            client, self.columns_table, self._tables_info, self.knowledge_id
        )

        # 应用列数量限制
        self._columns_info = _apply_column_limit(columns_info, column_limit)

    async def _load_data_by_column_ids(self, column_ids: List[int]):
        """通过列ID加载数据"""
        self._input_method = "from_column_ids"
        self._original_params = {"column_ids": column_ids}

        client = await self._get_client()

        # 查询列信息及关联的表和数据库信息
        self._columns_info = await _fetch_columns_by_ids(
            client, self.columns_table, self.tables_table, self.db_table,
            column_ids, self.knowledge_id
        )

        if not self._columns_info:
            logger.warning(f"No columns found for IDs: {column_ids}")
            return

        # 按表分组列信息
        self._tables_info = _group_columns_by_table(self._columns_info)

    async def _load_data_by_candidate_columns(self, candidate_columns: Dict[str, List[str]]):
        """通过候选列字典加载数据"""
        self._input_method = "from_candidate_columns"
        self._original_params = {"candidate_columns": candidate_columns}

        client = await self._get_client()

        # 首先获取所有涉及的表名
        table_names = list(candidate_columns.keys())

        # 查询表信息
        self._tables_info = await _fetch_tables_info_by_names(
            client, self.tables_table, self.db_table, table_names, self.knowledge_id
        )

        if not self._tables_info:
            logger.warning(f"No tables found for names: {table_names}")
            return

        # 根据候选列查询具体的列信息
        self._columns_info = await _fetch_columns_by_candidate_columns(
            client, self.columns_table, self._tables_info, candidate_columns, self.knowledge_id
        )

    def _split_with_complete_context(self, token_size: int) -> List[str]:
        """按token限制分割，确保每个片段都有完整上下文"""
        if not self._tables_info or not self._columns_info:
            return ["【Schema】\n# No data found"]

        # 按token限制将列分组
        column_groups = self._group_columns_by_token_limit(token_size)

        if not column_groups:
            return ["【Schema】\n# No data found"]

        schemas = []

        for group in column_groups:
            # 获取当前组涉及的表
            table_ids = list(set(col.get('table_id') for col in group))
            involved_tables = [t for t in self._tables_info if t.get('table_id') in table_ids]

            # 为当前组构建完整的上下文
            schema = self._build_complete_context_for_columns(group, involved_tables)
            schemas.append(schema)

        return schemas

    def _build_complete_schema(self) -> str:
        """构建完整的schema字符串"""
        return _build_schema_string(self._tables_info, self._columns_info)

    def _build_complete_context_for_columns(self, columns: List[Dict], tables_info: List[Dict]) -> str:
        """为指定的列构建完整的上下文信息"""
        if not columns or not tables_info:
            return "【Schema】\n# No data found"

        # 按数据库和表分组
        tables_by_db = {}
        for table in tables_info:
            db_id = table.get('db_id', 'unknown')
            if db_id not in tables_by_db:
                tables_by_db[db_id] = {
                    'db_info': table,
                    'tables': []
                }
            tables_by_db[db_id]['tables'].append(table)

        # 按表分组列信息
        columns_by_table = {}
        for col in columns:
            table_id = col['table_id']
            if table_id not in columns_by_table:
                columns_by_table[table_id] = []
            columns_by_table[table_id].append(col)

        schema_parts = ["【Schema】"]

        # 构建完整的schema
        for db_id, db_group in tables_by_db.items():
            db_info = db_group['db_info']
            tables = db_group['tables']

            # 添加数据库信息
            db_name = db_info.get('db_name', '未知数据库')
            db_desc = db_info.get('db_desc') or db_info.get('db_name_cn', '数据库描述')
            schema_parts.append(f"【DB】 {db_name}, {db_desc}")

            # 处理该数据库下的表
            for table in tables:
                table_id = table['table_id']
                table_columns = columns_by_table.get(table_id, [])

                if not table_columns:
                    continue

                # 构建表信息
                table_name = table['table_name']
                table_desc = table.get('table_desc') or table.get('table_name_cn', '自动导入的表')
                schema_parts.append(f"# Table: {table_name}, {table_desc}")
                schema_parts.append("[")

                # 构建列信息
                column_lines = []
                for col in table_columns:
                    column_schema = self._build_single_column_schema(col)
                    column_lines.append(column_schema)

                if column_lines:
                    schema_parts.append(",\n".join(column_lines))

                schema_parts.append("]")

        return "\n".join(schema_parts)

    def _split_with_mappings(self, token_size: int) -> List[Dict[str, Any]]:
        """按token限制分割，同时生成映射信息"""
        if not self._tables_info or not self._columns_info:
            return [{
                "prompt": "【Schema】\n# No data found",
                "db_to_tables": {},
                "table_to_columns": {},
                "db_to_tables_id": {},
                "table_to_columns_id": {}
            }]

        # 按token限制将列分组
        column_groups = self._group_columns_by_token_limit(token_size)

        if not column_groups:
            return [{
                "prompt": "【Schema】\n# No data found",
                "db_to_tables": {},
                "table_to_columns": {},
                "db_to_tables_id": {},
                "table_to_columns_id": {}
            }]

        result = []

        for group in column_groups:
            # 获取当前组涉及的表
            table_ids = list(set(col.get('table_id') for col in group))
            involved_tables = [t for t in self._tables_info if t.get('table_id') in table_ids]

            # 为当前组构建完整的上下文
            prompt = self._build_complete_context_for_columns(group, involved_tables)

            # 生成映射信息（始终包含ID映射）
            mappings = self._generate_mappings_for_group(group, involved_tables)

            result.append({
                "prompt": prompt,
                **mappings
            })

        return result

    def _generate_mappings_for_group(self,
                                   columns: List[Dict],
                                   tables: List[Dict]) -> Dict[str, Any]:
        """为指定的列和表生成映射信息"""
        mappings = {}

        # 生成 db_to_tables 映射 (名称)
        db_to_tables = {}
        for table in tables:
            db_name = table.get('db_name', 'unknown')
            table_name = table.get('table_name', 'unknown')
            if db_name not in db_to_tables:
                db_to_tables[db_name] = []
            if table_name not in db_to_tables[db_name]:
                db_to_tables[db_name].append(table_name)
        mappings['db_to_tables'] = db_to_tables

        # 生成 table_to_columns 映射 (名称)
        table_to_columns = {}
        for col in columns:
            table_name = col.get('table_name', 'unknown')
            column_name = col.get('column_name', 'unknown')
            if table_name not in table_to_columns:
                table_to_columns[table_name] = []
            if column_name not in table_to_columns[table_name]:
                table_to_columns[table_name].append(column_name)
        mappings['table_to_columns'] = table_to_columns

        # 生成 db_to_tables_id 映射 (ID) - 始终生成
        db_to_tables_id = {}
        for table in tables:
            db_id = table.get('db_id')
            table_id = table.get('table_id')
            if db_id is not None and table_id is not None:
                if db_id not in db_to_tables_id:
                    db_to_tables_id[db_id] = []
                if table_id not in db_to_tables_id[db_id]:
                    db_to_tables_id[db_id].append(table_id)
        mappings['db_to_tables_id'] = db_to_tables_id

        # 生成 table_to_columns_id 映射 (ID) - 始终生成
        table_to_columns_id = {}
        for col in columns:
            table_id = col.get('table_id')
            column_id = col.get('column_id')
            if table_id is not None and column_id is not None:
                if table_id not in table_to_columns_id:
                    table_to_columns_id[table_id] = []
                if column_id not in table_to_columns_id[table_id]:
                    table_to_columns_id[table_id].append(column_id)
        mappings['table_to_columns_id'] = table_to_columns_id

        return mappings

    def _group_columns_by_token_limit(self, token_size: int) -> List[List[Dict]]:
        """按token限制将列分组"""
        if not self._columns_info:
            return []

        groups = []
        current_group = []
        current_size = 0

        # 基础上下文的估算大小（数据库信息、表头等）
        base_context_size = 200

        for col in self._columns_info:
            # 估算单列的大小
            column_schema = self._build_single_column_schema(col)
            column_size = len(column_schema)

            # 检查是否需要开始新组
            estimated_total_size = current_size + column_size + base_context_size
            if estimated_total_size > token_size and current_group:
                # 当前组已满，开始新组
                groups.append(current_group)
                current_group = [col]
                current_size = column_size
            else:
                # 添加到当前组
                current_group.append(col)
                current_size += column_size

        # 添加最后一组
        if current_group:
            groups.append(current_group)

        return groups

    def _build_single_column_schema(self, col: Dict) -> str:
        """构建单个列的schema描述"""
        column_name = col['column_name']
        data_type = col.get('data_type', '').upper()
        column_name_cn = col.get('column_name_cn', '无中文名')
        column_desc = col.get('column_desc', '无描述')
        is_primary_key = col.get('is_primary_key', False)
        is_sensitive = col.get('is_sensitive', False)
        data_example = col.get('data_example')

        # 构建列字符串
        comment = f"{column_name_cn}, {column_desc}" if column_name_cn != '无中文名' else column_desc
        col_parts = [f"({column_name}"]

        if data_type:
            col_parts.append(f":{data_type}")

        col_parts.append(f", {comment}")

        if is_primary_key:
            col_parts.append(", Primary Key")

        if is_sensitive:
            col_parts.append(", Sensitive Data")

        if data_example:
            col_parts.append(f", Examples: [{str(data_example)}]")

        col_parts.append(")")
        return "".join(col_parts)

    def _generate_candidates_data(self) -> Dict[str, Any]:
        """生成候选数据结构"""
        if not self._tables_info or not self._columns_info:
            return {
                "candidate_tables": {},
                "candidate_columns": {},
                "candidate_tables_ids": {},
                "candidate_columns_ids": {}
            }

        # 生成candidate_tables (按db_name分组)
        candidate_tables = {}
        for table in self._tables_info:
            db_name = table.get('db_name', 'unknown')
            table_name = table.get('table_name', 'unknown')
            if db_name not in candidate_tables:
                candidate_tables[db_name] = []
            if table_name not in candidate_tables[db_name]:
                candidate_tables[db_name].append(table_name)

        # 生成candidate_columns (按table_name分组)
        candidate_columns = {}
        for col in self._columns_info:
            table_name = col.get('table_name', 'unknown')
            column_name = col.get('column_name', 'unknown')
            if table_name not in candidate_columns:
                candidate_columns[table_name] = []
            if column_name not in candidate_columns[table_name]:
                candidate_columns[table_name].append(column_name)

        # 生成candidate_tables_ids (按db_id分组)
        candidate_tables_ids = {}
        for table in self._tables_info:
            db_id = table.get('db_id', 'unknown')
            table_id = table.get('table_id')
            if db_id not in candidate_tables_ids:
                candidate_tables_ids[db_id] = []
            if table_id is not None and table_id not in candidate_tables_ids[db_id]:
                candidate_tables_ids[db_id].append(table_id)

        # 生成candidate_columns_ids (按table_id分组)
        candidate_columns_ids = {}
        for col in self._columns_info:
            table_id = col.get('table_id')
            column_id = col.get('column_id')
            if table_id is not None:
                if table_id not in candidate_columns_ids:
                    candidate_columns_ids[table_id] = []
                if column_id is not None and column_id not in candidate_columns_ids[table_id]:
                    candidate_columns_ids[table_id].append(column_id)

        return {
            "candidate_tables": candidate_tables,
            "candidate_columns": candidate_columns,
            "candidate_tables_ids": candidate_tables_ids,
            "candidate_columns_ids": candidate_columns_ids
        }


# ==================== 现代化API - 无向后兼容 ====================


# ==================== 辅助函数 ====================

async def _fetch_tables_info_by_names(client, tables_table: str, db_table: str, table_names: List[str], knowledge_id: Optional[str] = None) -> List[Dict]:
    """
    根据表名查询表信息

    业务假设：table_name 在系统中全局唯一，可以直接用于WHERE条件查询
    """
    # 使用命名参数
    table_params = {}
    table_placeholders = []

    # 为每个表名创建命名参数
    for i, table_name in enumerate(table_names):
        param_name = f"table_name_{i}"
        table_params[param_name] = table_name
        table_placeholders.append(f":{param_name}")

    placeholders_str = ', '.join(table_placeholders)

    # 构建WHERE条件
    where_conditions = [f"t.table_name IN ({placeholders_str})"]
    if knowledge_id is not None:
        table_params['knowledge_id'] = knowledge_id
        where_conditions.append("t.knowledge_id = :knowledge_id")

    where_clause = " AND ".join(where_conditions)

    sql = f"""
    SELECT
        t.table_id,
        t.table_name,
        t.table_name_cn,
        t.table_desc,
        t.db_id,
        d.db_name,
        d.db_name_cn,
        d.db_desc
    FROM {tables_table} t
    LEFT JOIN {db_table} d ON t.db_id = d.db_id
    WHERE {where_clause}
    """

    # 添加调试信息
    logger.info(f"🔍 根据表名查询表信息:")
    logger.info(f"   请求的表名: {table_names}")
    logger.info(f"   SQL: {sql}")
    logger.info(f"   参数: {table_params}")

    result = await client.afetch_all(sql, table_params)

    logger.info(f"📊 表查询结果:")
    logger.info(f"   返回行数: {len(result.data) if result.data else 0}")
    if result.data:
        found_table_names = [row['table_name'] for row in result.data]
        logger.info(f"   找到的表名: {found_table_names}")
        missing_table_names = set(table_names) - set(found_table_names)
        if missing_table_names:
            logger.warning(f"   缺失的表名: {list(missing_table_names)}")
    else:
        logger.warning(f"   没有找到任何表数据")

    return result.data if result.data else []


async def _fetch_columns_info(client, columns_table: str, tables_info: List[Dict], knowledge_id: Optional[str] = None) -> List[Dict]:
    """查询列信息，包含表名信息"""
    table_ids = [str(table['table_id']) for table in tables_info]

    # 使用命名参数
    column_params = {}
    table_id_placeholders = []

    # 为每个table_id创建命名参数
    for i, table_id in enumerate(table_ids):
        param_name = f"table_id_{i}"
        column_params[param_name] = table_id
        table_id_placeholders.append(f":{param_name}")

    placeholders_str = ', '.join(table_id_placeholders)

    # 构建WHERE条件
    where_conditions = [f"c.table_id IN ({placeholders_str})"]
    if knowledge_id is not None:
        column_params['knowledge_id'] = knowledge_id
        where_conditions.append("c.knowledge_id = :knowledge_id")

    where_clause = " AND ".join(where_conditions)

    # 构建表名到表ID的映射，用于添加表名信息
    table_id_to_name = {table['table_id']: table['table_name'] for table in tables_info}

    sql = f"""
    SELECT
        c.column_id,
        c.table_id,
        c.column_name,
        c.column_name_cn,
        c.column_desc,
        c.data_type,
        c.data_example,
        c.is_primary_key,
        c.is_sensitive
    FROM {columns_table} c
    WHERE {where_clause}
    ORDER BY c.table_id, c.column_id
    """

    result = await client.afetch_all(sql, column_params)

    # 为每个列添加表名信息
    if result.data:
        for col in result.data:
            table_id = col.get('table_id')
            col['table_name'] = table_id_to_name.get(table_id, 'unknown')

    return result.data if result.data else []


async def _fetch_tables_info_by_ids(client, tables_table: str, db_table: str, table_ids: List[int], knowledge_id: Optional[str] = None) -> List[Dict]:
    """根据表ID查询表信息"""
    # 使用命名参数
    table_params = {}
    table_placeholders = []

    # 为每个table_id创建命名参数
    for i, table_id in enumerate(table_ids):
        param_name = f"table_id_{i}"
        table_params[param_name] = str(table_id)
        table_placeholders.append(f":{param_name}")

    placeholders_str = ', '.join(table_placeholders)

    # 构建WHERE条件
    where_conditions = [f"t.table_id IN ({placeholders_str})"]
    if knowledge_id is not None:
        table_params['knowledge_id'] = knowledge_id
        where_conditions.append("t.knowledge_id = :knowledge_id")

    where_clause = " AND ".join(where_conditions)

    sql = f"""
    SELECT
        t.table_id,
        t.table_name,
        t.table_name_cn,
        t.table_desc,
        t.db_id,
        d.db_name,
        d.db_name_cn,
        d.db_desc
    FROM {tables_table} t
    LEFT JOIN {db_table} d ON t.db_id = d.db_id
    WHERE {where_clause}
    """

    result = await client.afetch_all(sql, table_params)
    return result.data if result.data else []


async def _fetch_columns_by_candidate_columns(client, columns_table: str, tables_info: List[Dict],
                                            candidate_columns: Dict[str, List[str]], knowledge_id: Optional[str] = None) -> List[Dict]:
    """根据候选列字典查询列信息"""
    if not tables_info or not candidate_columns:
        return []

    # 构建表名到表ID的映射
    table_name_to_id = {table['table_name']: table['table_id'] for table in tables_info}

    # 构建查询条件
    conditions = []
    params = {}
    param_counter = 0

    for table_name, column_names in candidate_columns.items():
        if table_name not in table_name_to_id:
            continue

        table_id = table_name_to_id[table_name]

        # 为每个列名创建条件
        column_placeholders = []
        for column_name in column_names:
            param_name = f"column_name_{param_counter}"
            params[param_name] = column_name
            column_placeholders.append(f":{param_name}")
            param_counter += 1

        table_id_param = f"table_id_{param_counter}"
        params[table_id_param] = str(table_id)
        param_counter += 1

        condition = f"(table_id = :{table_id_param} AND column_name IN ({', '.join(column_placeholders)}))"
        conditions.append(condition)

    if not conditions:
        return []

    # 构建WHERE条件
    where_conditions = [f"({' OR '.join(conditions)})"]
    if knowledge_id is not None:
        params['knowledge_id'] = knowledge_id
        where_conditions.append("knowledge_id = :knowledge_id")

    where_clause = " AND ".join(where_conditions)

    sql = f"""
    SELECT
        column_id,
        table_id,
        column_name,
        column_name_cn,
        column_desc,
        data_type,
        data_example,
        is_primary_key,
        is_sensitive
    FROM {columns_table}
    WHERE {where_clause}
    ORDER BY table_id, column_id
    """

    result = await client.afetch_all(sql, params)

    # 为每个列添加表名信息
    if result.data:
        table_id_to_name = {table['table_id']: table['table_name'] for table in tables_info}
        for col in result.data:
            table_id = col.get('table_id')
            col['table_name'] = table_id_to_name.get(table_id, 'unknown')

    return result.data if result.data else []


async def _fetch_columns_by_ids(client, columns_table: str, tables_table: str, db_table: str, column_ids: List[int], knowledge_id: Optional[str] = None) -> List[Dict]:
    """根据列ID查询列信息及关联的表和数据库信息"""
    # 使用命名参数
    column_params = {}
    column_placeholders = []

    # 为每个column_id创建命名参数
    for i, column_id in enumerate(column_ids):
        param_name = f"column_id_{i}"
        column_params[param_name] = str(column_id)
        column_placeholders.append(f":{param_name}")

    placeholders_str = ', '.join(column_placeholders)

    # 构建WHERE条件
    where_conditions = [f"c.column_id IN ({placeholders_str})"]
    if knowledge_id is not None:
        column_params['knowledge_id'] = knowledge_id
        where_conditions.append("c.knowledge_id = :knowledge_id")

    where_clause = " AND ".join(where_conditions)

    sql = f"""
    SELECT
        c.column_id,
        c.table_id,
        c.column_name,
        c.column_name_cn,
        c.column_desc,
        c.data_type,
        c.data_example,
        c.is_primary_key,
        c.is_sensitive,
        t.table_name,
        t.table_name_cn,
        t.table_desc,
        t.db_id,
        d.db_name,
        d.db_name_cn,
        d.db_desc
    FROM {columns_table} c
    LEFT JOIN {tables_table} t ON c.table_id = t.table_id
    LEFT JOIN {db_table} d ON t.db_id = d.db_id
    WHERE {where_clause}
    ORDER BY c.table_id, c.column_id
    """

    result = await client.afetch_all(sql, column_params)
    return result.data if result.data else []


def _group_columns_by_table(columns_info: List[Dict]) -> List[Dict]:
    """按表分组列信息，生成表信息列表"""
    tables_dict = {}

    for col in columns_info:
        table_id = col.get('table_id')
        if table_id not in tables_dict:
            tables_dict[table_id] = {
                'table_id': table_id,
                'table_name': col.get('table_name', 'unknown'),
                'table_name_cn': col.get('table_name_cn', ''),
                'table_desc': col.get('table_desc', ''),
                'db_id': col.get('db_id'),
                'db_name': col.get('db_name', 'unknown'),
                'db_name_cn': col.get('db_name_cn', ''),
                'db_desc': col.get('db_desc', '')
            }

    return list(tables_dict.values())


def _apply_column_limit(columns_info: List[Dict], limit: int) -> List[Dict]:
    """应用列数量限制"""
    if limit <= 0:
        return columns_info

    # 按表分组
    columns_by_table = {}
    for col in columns_info:
        table_id = col['table_id']
        if table_id not in columns_by_table:
            columns_by_table[table_id] = []
        columns_by_table[table_id].append(col)

    # 对每个表应用限制
    limited_columns = []
    for table_id, table_columns in columns_by_table.items():
        # 排除不需要的列
        excluded_names = {'create_time', 'update_time', 'id'}
        filtered_columns = []
        primary_key_columns = []

        for col in table_columns:
            column_name = col.get('column_name', '').lower()
            is_primary_key = col.get('is_primary_key', False)

            # 主键列单独处理
            if is_primary_key:
                primary_key_columns.append(col)
            # 排除时间字段和ID字段（非主键）
            elif column_name not in excluded_names and not column_name.endswith('_id'):
                filtered_columns.append(col)

        # 随机选择列
        if len(filtered_columns) > limit:
            selected_columns = random.sample(filtered_columns, limit)
        else:
            selected_columns = filtered_columns

        # 添加主键列（不计入limit）
        limited_columns.extend(primary_key_columns)
        limited_columns.extend(selected_columns)

    return limited_columns


def _build_schema_string(tables_info: List[Dict], columns_info: List[Dict]) -> str:
    """构建完整的Schema字符串"""
    if not tables_info:
        return "【Schema】\n# No tables found"

    # 按数据库分组表信息
    tables_by_db = {}
    for table in tables_info:
        db_id = table.get('db_id', 'unknown')
        if db_id not in tables_by_db:
            tables_by_db[db_id] = {
                'db_info': table,
                'tables': []
            }
        tables_by_db[db_id]['tables'].append(table)

    # 按表分组列信息
    columns_by_table = {}
    for col in columns_info:
        table_id = col['table_id']
        if table_id not in columns_by_table:
            columns_by_table[table_id] = []
        columns_by_table[table_id].append(col)

    schema_parts = ["【Schema】"]

    # 逐个处理数据库和表
    for db_id, db_group in tables_by_db.items():
        db_info = db_group['db_info']
        tables = db_group['tables']

        # 构建数据库信息
        db_name = db_info.get('db_name', '未知数据库')
        db_desc = db_info.get('db_desc') or db_info.get('db_name_cn', '数据库描述')
        schema_parts.append(f"【DB】 {db_name}, {db_desc}")

        # 处理该数据库下的表
        for table in tables:
            table_schema = _build_single_table_schema(table, columns_by_table.get(table['table_id'], []))
            schema_parts.append(table_schema)

    return "\n".join(schema_parts)


def _build_single_table_schema(table: Dict, columns: List[Dict]) -> str:
    """构建单个表的Schema字符串"""
    table_name = table['table_name']
    table_desc = table.get('table_desc') or table.get('table_name_cn', '自动导入的表')

    schema_parts = [f"# Table: {table_name}, {table_desc}", "["]

    column_lines = []
    for col in columns:
        column_name = col['column_name']
        data_type = col.get('data_type', '').upper()
        column_name_cn = col.get('column_name_cn', '无中文名')
        column_desc = col.get('column_desc', '无描述')
        is_primary_key = col.get('is_primary_key', False)
        is_sensitive = col.get('is_sensitive', False)
        data_example = col.get('data_example')

        # 构建列字符串
        comment = f"{column_name_cn}, {column_desc}" if column_name_cn != '无中文名' else column_desc
        col_parts = [f"({column_name}"]

        if data_type:
            col_parts.append(f":{data_type}")

        col_parts.append(f", {comment}")

        if is_primary_key:
            col_parts.append(", Primary Key")

        if is_sensitive:
            col_parts.append(", Sensitive Data")

        if data_example:
            col_parts.append(f", Examples: [{str(data_example)}]")

        col_parts.append(")")
        column_lines.append("".join(col_parts))

    if column_lines:
        schema_parts.append(",\n".join(column_lines))
    else:
        schema_parts.append("# No columns found for this table")

    schema_parts.append("]")

    return "\n".join(schema_parts)


# ==================== 测试代码 ====================

if __name__ == "__main__":
    import asyncio
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

    async def test_schema_generator():
        """测试重构后的SchemaGenerator"""
        print("🧪 开始测试重构后的SchemaGenerator...")

        try:
            # 测试基本功能
            generator = SchemaGenerator("source", knowledge_id="test_kb")

            # 测试from_table_names
            print("\n📋 测试 from_table_names...")
            await generator.from_table_names(["users", "orders"], column_limit=3)

            # 测试split方法
            print("\n✂️ 测试 split 方法...")
            schemas = generator.split(token_size=1000)
            print(f"分割结果: {len(schemas)} 个片段")
            for i, schema in enumerate(schemas):
                print(f"片段 {i+1}: {len(schema)} 字符")
                print(schema[:200] + "..." if len(schema) > 200 else schema)
                print("-" * 50)

            # 测试get_complete_schema
            print("\n📄 测试 get_complete_schema...")
            complete_schema = generator.get_complete_schema()
            print(f"完整schema: {len(complete_schema)} 字符")

            # 测试get_candidates
            print("\n🎯 测试 get_candidates...")
            candidates = await generator.get_candidates()
            print(f"候选数据: {list(candidates.keys())}")

            # 测试向后兼容方法
            print("\n🔄 测试向后兼容方法...")
            legacy_generator = SchemaGenerator("source", knowledge_id="test_kb")
            await legacy_generator.by_table_names(["users"], limit=2)
            legacy_schema = legacy_generator.to_schema()
            print(f"向后兼容schema: {len(legacy_schema)} 字符")

            print("\n✅ 所有测试完成！")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

    # 运行测试
    asyncio.run(test_schema_generator())
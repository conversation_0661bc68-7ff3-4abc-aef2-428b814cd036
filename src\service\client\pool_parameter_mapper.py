"""
连接池参数映射器

解决不同数据库实现之间连接池参数不兼容的问题
"""

import logging
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class DatabaseType(Enum):
    """数据库类型枚举"""
    SQLALCHEMY = "sqlalchemy"
    PGVECTOR = "pgvector"
    AIOMYSQL = "aiomysql"
    ASYNCPG = "asyncpg"


@dataclass
class PoolParameters:
    """标准化的连接池参数"""
    # 基础连接数
    base_connections: int = 20
    # 最大连接数
    max_connections: int = 60
    # 连接超时时间(秒)
    timeout: float = 30.0
    # 连接回收时间(秒)
    recycle: int = 3600
    # 连接预检查
    pre_ping: bool = True
    # 额外参数
    extra_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.extra_params is None:
            self.extra_params = {}


class PoolParameterMapper:
    """
    连接池参数映射器
    
    负责将通用的连接池配置映射到不同数据库实现的特定参数
    """
    
    @staticmethod
    def normalize_config(config: Dict[str, Any]) -> PoolParameters:
        """
        将配置标准化为通用的连接池参数
        
        Args:
            config: 原始配置字典
            
        Returns:
            PoolParameters: 标准化的连接池参数
        """
        # 检测配置类型并提取参数
        if 'pool_size' in config:
            # SQLAlchemy风格配置
            base_connections = config.get('pool_size', 20)
            max_overflow = config.get('max_overflow', 40)
            max_connections = base_connections + max_overflow
        elif 'min_connections' in config and 'max_connections' in config:
            # PGVector风格配置
            base_connections = config.get('min_connections', 5)
            max_connections = config.get('max_connections', 25)
        elif 'minsize' in config and 'maxsize' in config:
            # aiomysql风格配置
            base_connections = config.get('minsize', 10)
            max_connections = config.get('maxsize', 50)
        else:
            # 使用默认值
            base_connections = 20
            max_connections = 60
        
        return PoolParameters(
            base_connections=base_connections,
            max_connections=max_connections,
            timeout=config.get('pool_timeout', config.get('timeout', 30.0)),
            recycle=config.get('pool_recycle', config.get('recycle', 3600)),
            pre_ping=config.get('pool_pre_ping', config.get('pre_ping', True)),
            extra_params={k: v for k, v in config.items() 
                         if k not in ['pool_size', 'max_overflow', 'min_connections', 
                                    'max_connections', 'minsize', 'maxsize', 
                                    'pool_timeout', 'timeout', 'pool_recycle', 
                                    'recycle', 'pool_pre_ping', 'pre_ping']}
        )
    
    @staticmethod
    def map_to_sqlalchemy(params: Union[PoolParameters, Dict[str, Any]]) -> Dict[str, Any]:
        """
        映射到SQLAlchemy参数
        
        Args:
            params: 标准化参数或原始配置
            
        Returns:
            SQLAlchemy引擎参数字典
        """
        if isinstance(params, dict):
            # 如果已经是SQLAlchemy格式，直接返回
            if 'pool_size' in params:
                return {
                    'pool_size': params.get('pool_size', 20),
                    'max_overflow': params.get('max_overflow', 40),
                    'pool_timeout': params.get('pool_timeout', 30.0),
                    'pool_recycle': params.get('pool_recycle', 3600),
                    'pool_pre_ping': params.get('pool_pre_ping', True),
                    **{k: v for k, v in params.items() 
                       if k not in ['pool_size', 'max_overflow', 'pool_timeout', 
                                  'pool_recycle', 'pool_pre_ping']}
                }
            params = PoolParameterMapper.normalize_config(params)
        
        # 计算SQLAlchemy参数
        pool_size = params.base_connections
        max_overflow = max(0, params.max_connections - params.base_connections)
        
        result = {
            'pool_size': pool_size,
            'max_overflow': max_overflow,
            'pool_timeout': params.timeout,
            'pool_recycle': params.recycle,
            'pool_pre_ping': params.pre_ping,
            **params.extra_params
        }
        
        logger.debug(f"映射到SQLAlchemy: base={params.base_connections}, max={params.max_connections} -> pool_size={pool_size}, max_overflow={max_overflow}")
        return result
    
    @staticmethod
    def map_to_pgvector(params: Union[PoolParameters, Dict[str, Any]]) -> Dict[str, Any]:
        """
        映射到PGVector参数
        
        Args:
            params: 标准化参数或原始配置
            
        Returns:
            PGVector连接参数字典
        """
        if isinstance(params, dict):
            # 如果已经是PGVector格式，直接返回
            if 'min_connections' in params and 'max_connections' in params:
                return {
                    'min_connections': params.get('min_connections', 5),
                    'max_connections': params.get('max_connections', 25),
                    'pool_timeout': params.get('pool_timeout', 30.0),
                    'pool_recycle': params.get('pool_recycle', 3600),
                    'pool_pre_ping': params.get('pool_pre_ping', True),
                    **{k: v for k, v in params.items() 
                       if k not in ['min_connections', 'max_connections', 'pool_timeout', 
                                  'pool_recycle', 'pool_pre_ping']}
                }
            params = PoolParameterMapper.normalize_config(params)
        
        # 计算PGVector参数
        min_connections = max(1, params.base_connections // 4)  # 保持较少的最小连接
        max_connections = params.max_connections
        
        result = {
            'min_connections': min_connections,
            'max_connections': max_connections,
            'pool_timeout': params.timeout,
            'pool_recycle': params.recycle,
            'pool_pre_ping': params.pre_ping,
            **params.extra_params
        }
        
        logger.debug(f"映射到PGVector: base={params.base_connections}, max={params.max_connections} -> min_connections={min_connections}, max_connections={max_connections}")
        return result
    
    @staticmethod
    def map_to_aiomysql(params: Union[PoolParameters, Dict[str, Any]]) -> Dict[str, Any]:
        """
        映射到aiomysql参数
        
        Args:
            params: 标准化参数或原始配置
            
        Returns:
            aiomysql连接池参数字典
        """
        if isinstance(params, dict):
            # 如果已经是aiomysql格式，直接返回
            if 'minsize' in params and 'maxsize' in params:
                return {
                    'minsize': params.get('minsize', 10),
                    'maxsize': params.get('maxsize', 50),
                    'pool_recycle': params.get('pool_recycle', 3600),
                    **{k: v for k, v in params.items() 
                       if k not in ['minsize', 'maxsize', 'pool_recycle']}
                }
            params = PoolParameterMapper.normalize_config(params)
        
        # 计算aiomysql参数
        minsize = max(1, params.base_connections // 2)  # aiomysql建议较小的最小值
        maxsize = params.max_connections
        
        result = {
            'minsize': minsize,
            'maxsize': maxsize,
            'pool_recycle': params.recycle,
            **params.extra_params
        }
        
        logger.debug(f"映射到aiomysql: base={params.base_connections}, max={params.max_connections} -> minsize={minsize}, maxsize={maxsize}")
        return result
    
    @staticmethod
    def map_to_asyncpg(params: Union[PoolParameters, Dict[str, Any]]) -> Dict[str, Any]:
        """
        映射到asyncpg参数
        
        Args:
            params: 标准化参数或原始配置
            
        Returns:
            asyncpg连接池参数字典
        """
        if isinstance(params, dict):
            # 如果已经是asyncpg格式，直接返回
            if 'min_size' in params and 'max_size' in params:
                return {
                    'min_size': params.get('min_size', 5),
                    'max_size': params.get('max_size', 25),
                    'command_timeout': params.get('command_timeout', 60),
                    **{k: v for k, v in params.items() 
                       if k not in ['min_size', 'max_size', 'command_timeout']}
                }
            params = PoolParameterMapper.normalize_config(params)
        
        # 计算asyncpg参数
        min_size = max(1, params.base_connections // 4)
        max_size = params.max_connections
        
        result = {
            'min_size': min_size,
            'max_size': max_size,
            'command_timeout': min(params.timeout * 2, 60),  # asyncpg使用命令超时
            **params.extra_params
        }
        
        logger.debug(f"映射到asyncpg: base={params.base_connections}, max={params.max_connections} -> min_size={min_size}, max_size={max_size}")
        return result
    
    @staticmethod
    def auto_map(config: Dict[str, Any], target_type: DatabaseType) -> Dict[str, Any]:
        """
        自动映射配置到目标数据库类型
        
        Args:
            config: 原始配置
            target_type: 目标数据库类型
            
        Returns:
            映射后的配置
        """
        mapping_functions = {
            DatabaseType.SQLALCHEMY: PoolParameterMapper.map_to_sqlalchemy,
            DatabaseType.PGVECTOR: PoolParameterMapper.map_to_pgvector,
            DatabaseType.AIOMYSQL: PoolParameterMapper.map_to_aiomysql,
            DatabaseType.ASYNCPG: PoolParameterMapper.map_to_asyncpg,
        }
        
        if target_type not in mapping_functions:
            raise ValueError(f"不支持的数据库类型: {target_type}")
        
        mapper_func = mapping_functions[target_type]
        result = mapper_func(config)
        
        logger.info(f"自动映射配置到 {target_type.value}: {len(result)} 个参数")
        return result
    
    @staticmethod
    def detect_config_type(config: Dict[str, Any]) -> Optional[DatabaseType]:
        """
        检测配置类型
        
        Args:
            config: 配置字典
            
        Returns:
            检测到的数据库类型，如果无法检测则返回None
        """
        if 'pool_size' in config and 'max_overflow' in config:
            return DatabaseType.SQLALCHEMY
        elif 'min_connections' in config and 'max_connections' in config:
            return DatabaseType.PGVECTOR
        elif 'minsize' in config and 'maxsize' in config:
            return DatabaseType.AIOMYSQL
        elif 'min_size' in config and 'max_size' in config:
            return DatabaseType.ASYNCPG
        else:
            return None
    
    @staticmethod
    def validate_config(config: Dict[str, Any], target_type: DatabaseType) -> bool:
        """
        验证配置是否适用于目标数据库类型
        
        Args:
            config: 配置字典
            target_type: 目标数据库类型
            
        Returns:
            是否有效
        """
        try:
            PoolParameterMapper.auto_map(config, target_type)
            return True
        except Exception as e:
            logger.warning(f"配置验证失败: {e}")
            return False


# 便捷函数
def map_pool_config(config: Dict[str, Any], target_db_type: str) -> Dict[str, Any]:
    """
    便捷函数：映射连接池配置
    
    Args:
        config: 原始配置
        target_db_type: 目标数据库类型字符串
        
    Returns:
        映射后的配置
    """
    type_mapping = {
        'sqlalchemy': DatabaseType.SQLALCHEMY,
        'mysql': DatabaseType.SQLALCHEMY,  # MySQL通常使用SQLAlchemy
        'postgresql': DatabaseType.SQLALCHEMY,  # PostgreSQL也可以使用SQLAlchemy
        'pgvector': DatabaseType.PGVECTOR,
        'aiomysql': DatabaseType.AIOMYSQL,
        'asyncpg': DatabaseType.ASYNCPG,
    }
    
    target_type = type_mapping.get(target_db_type.lower())
    if not target_type:
        raise ValueError(f"不支持的数据库类型: {target_db_type}")
    
    return PoolParameterMapper.auto_map(config, target_type)

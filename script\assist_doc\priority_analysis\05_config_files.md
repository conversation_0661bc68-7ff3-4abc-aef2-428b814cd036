# 配置文件系统详解

## 1. 配置文件结构

### 1.1 目录结构
```
src/config/
├── config.yaml
├── database/
│   ├── defaults.yaml
│   ├── rdbs/
│   │   └── mysql/
│   │       ├── connection.yaml
│   │       ├── defaults.yaml
│   │       ├── params/
│   │       │   ├── cache.yaml
│   │       │   ├── pool.yaml
│   │       │   └── transaction.yaml
│   │       ├── performance.yaml
│   │       └── priority/
│   │           ├── high.yaml
│   │           ├── standard.yaml
│   │           └── low.yaml
│   └── vdbs/
│       └── pgvector/
│           ├── connection.yaml
│           ├── defaults.yaml
│           ├── params/
│           │   ├── index.yaml
│           │   ├── search.yaml
│           │   └── vector.yaml
│           ├── performance.yaml
│           └── priority/
│               ├── high.yaml
│               ├── standard.yaml
│               └── low.yaml
```

### 1.2 配置文件层级
1. **全局配置** (`config.yaml`)：项目级别的全局配置
2. **数据库默认配置** (`database/defaults.yaml`)：所有数据库的默认配置
3. **关系型数据库配置** (`database/rdbs/mysql/`)：MySQL相关的配置
4. **向量数据库配置** (`database/vdbs/pgvector/`)：PGVector相关的配置
5. **优先级配置** (`priority/`)：不同优先级的资源配置

## 2. 优先级配置文件详解

### 2.1 MySQL 高优先级配置
#### 2.1.1 文件位置
`src/config/database/rdbs/mysql/priority/high.yaml`

#### 2.1.2 配置内容
```yaml
# @package _global_
# MySQL高优先级配置

# 高优先级连接池配置
pool_size: 50
max_overflow: 100
pool_timeout: 10.0
pool_recycle: 1800

# 高优先级缓存配置
cache_size: 2500
cache_ttl: 7200

# 优先级标识
priority: "high"
service_tier: "critical"
```

### 2.2 MySQL 标准优先级配置
#### 2.2.1 文件位置
`src/config/database/rdbs/mysql/priority/standard.yaml`

#### 2.2.2 配置内容
```yaml
# @package _global_
# MySQL标准优先级配置

# 标准优先级使用基础配置的默认值
# 优先级标识
priority: "standard"
service_tier: "normal"
```

### 2.3 MySQL 低优先级配置
#### 2.3.1 文件位置
`src/config/database/rdbs/mysql/priority/low.yaml`

#### 2.3.2 配置内容
```yaml
# @package _global_
# MySQL低优先级配置

# 低优先级连接池配置
pool_size: 5
max_overflow: 10
pool_timeout: 60.0
pool_recycle: 7200
pool_pre_ping: false

# 低优先级缓存配置
cache_size: 500
cache_ttl: 1800

# 优先级标识
priority: "low"
service_tier: "background"
```

### 2.4 PGVector 高优先级配置
#### 2.4.1 文件位置
`src/config/database/vdbs/pgvector/priority/high.yaml`

#### 2.4.2 配置内容
```yaml
# @package _global_
# PGVector高优先级配置

# 高优先级连接池配置
min_connections: 15
max_connections: 100
pool_timeout: 10.0
pool_recycle: 1800

# 高优先级缓存配置
cache_size: 2500
cache_ttl: 7200

# 高优先级搜索配置
search_params:
  default_limit: 20
  max_limit: 500
  similarity_threshold: 0.8

# 优先级标识
priority: "high"
service_tier: "critical"
```

### 2.5 PGVector 标准优先级配置
#### 2.5.1 文件位置
`src/config/database/vdbs/pgvector/priority/standard.yaml`

#### 2.5.2 配置内容
```yaml
# @package _global_
# PGVector标准优先级配置

# 标准优先级使用基础配置的默认值
# 优先级标识
priority: "standard"
service_tier: "normal"
```

### 2.6 PGVector 低优先级配置
#### 2.6.1 文件位置
`src/config/database/vdbs/pgvector/priority/low.yaml`

#### 2.6.2 配置内容
```yaml
# @package _global_
# PGVector低优先级配置

# 低优先级连接池配置
min_connections: 2
max_connections: 10
pool_timeout: 60.0
pool_recycle: 7200
pool_pre_ping: false

# 低优先级缓存配置
cache_size: 500
cache_ttl: 1800

# 低优先级搜索配置
search_params:
  default_limit: 5
  max_limit: 50
  similarity_threshold: 0.6

# 优先级标识
priority: "low"
service_tier: "background"
```

## 3. 配置合并机制

### 3.1 配置加载顺序
1. **基础配置**：加载默认配置文件
2. **优先级配置**：根据指定的优先级加载对应的配置文件
3. **运行时配置**：应用运行时的配置覆盖

### 3.2 配置合并示例
当请求高优先级的 MySQL 客户端时，系统会：
1. 加载 `database/rdbs/mysql/defaults.yaml` 中的基础配置
2. 加载 `database/rdbs/mysql/priority/high.yaml` 中的高优先级配置
3. 合并两个配置，高优先级配置覆盖基础配置中的同名项

## 4. 配置文件使用示例

### 4.1 Service 层配置使用
```python
# 获取高优先级MySQL客户端
high_client = await get_client("database.rdbs.mysql", priority='high')

# 系统内部会：
# 1. 解析配置路径为 database.rdbs.mysql_high_priority
# 2. 加载对应的配置文件
# 3. 创建使用高优先级配置的客户端实例
```

### 4.2 直接使用配置路径
```python
# 直接使用完整配置路径
high_client = await get_client("database.rdbs.mysql_high_priority")
low_client = await get_client("database.vdbs.pgvector_low_priority")
```

## 5. 配置验证和错误处理

### 5.1 配置路径验证
在 `PriorityConfigMapper` 中实现了配置路径的验证：

```python
@classmethod
def get_config_path(cls, 
                   db_type: str, 
                   priority: ServicePriority) -> str:
    """
    获取配置路径
    
    Args:
        db_type: 数据库类型 ('mysql', 'pgvector')
        priority: 服务优先级
        
    Returns:
        配置路径字符串
        
    Raises:
        ValueError: 不支持的数据库类型
    """
    # 标准化数据库类型
    normalized_db_type = db_type.lower().strip()
    
    if normalized_db_type not in cls.DATABASE_TYPES:
        supported_types = list(cls.DATABASE_TYPES.keys())
        raise ValueError(f"不支持的数据库类型: {db_type}. 支持的类型: {supported_types}")
    
    # 构建配置路径
    # ...
```

### 5.2 配置文件存在性检查
虽然当前实现中没有实际检查配置文件是否存在，但在企业级应用中应该添加此功能：

```python
@classmethod
def validate_configuration_exists(cls, config_path: ConfigPath) -> bool:
    """验证配置文件是否存在"""
    # 这里可以添加实际的文件存在性检查
    # 目前返回 True，实际实现中应该检查配置文件
    return True
```
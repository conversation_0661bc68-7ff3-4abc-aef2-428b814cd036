"""
CRUD集成验证脚本

验证DD部门职责分配模块与最新CRUD实现的集成是否正确
"""

import asyncio
import logging
from typing import Dict, Any, List
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入相关模块
try:
    from modules.knowledge.dd.crud import DDCrud
    from modules.knowledge.dd.shared.constants import DDTableNames
    from modules.knowledge.dd.shared.exceptions import DDError
    logger.info("✅ 成功导入最新CRUD模块")
except ImportError as e:
    logger.error(f"❌ 导入CRUD模块失败: {e}")
    exit(1)

try:
    from ..core.assignment_engine import DepartmentAssignmentLogic
    from ..core.database_operations import PostDistributionDBOperations
    from ..core.department_recommender import TFIDFDepartmentRecommender
    from ..infrastructure.models import DepartmentAssignmentRequest, BatchAssignmentRequest
    logger.info("✅ 成功导入部门职责分配模块")
except ImportError as e:
    logger.error(f"❌ 导入部门职责分配模块失败: {e}")
    exit(1)


class CRUDIntegrationVerifier:
    """CRUD集成验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.rdb_client = None
        self.vdb_client = None
        self.dd_crud = None
        self.assignment_logic = None
        
    async def setup_mock_clients(self):
        """设置模拟客户端"""
        logger.info("设置模拟数据库客户端...")
        
        # 使用简单的模拟客户端
        from .run_tests import MockRDBClient, MockVDBClient
        
        self.rdb_client = MockRDBClient("localhost", 3306, "hsbc_knowledge", "root", "")
        self.vdb_client = MockVDBClient()
        
        await self.rdb_client.connect()
        await self.vdb_client.connect()
        
        # 初始化CRUD和业务逻辑实例
        self.dd_crud = DDCrud(self.rdb_client, self.vdb_client)
        self.assignment_logic = DepartmentAssignmentLogic(self.rdb_client, self.vdb_client)
        
        logger.info("✅ 模拟客户端设置完成")
    
    async def verify_table_names(self):
        """验证表名常量"""
        logger.info("=" * 50)
        logger.info("验证表名常量")
        logger.info("=" * 50)
        
        expected_tables = {
            'BIZ_PRE_DISTRIBUTION': 'biz_dd_pre_distribution',
            'BIZ_POST_DISTRIBUTION': 'biz_dd_post_distribution', 
            'BIZ_DEPARTMENTS': 'dd_departments',
            'KB_SUBMISSION_DATA': 'dd_submission_data'
        }
        
        for attr_name, expected_value in expected_tables.items():
            actual_value = getattr(DDTableNames, attr_name, None)
            if actual_value == expected_value:
                logger.info(f"✅ {attr_name}: {actual_value}")
            else:
                logger.error(f"❌ {attr_name}: 期望 {expected_value}, 实际 {actual_value}")
    
    async def verify_crud_methods(self):
        """验证CRUD方法可用性"""
        logger.info("=" * 50)
        logger.info("验证CRUD方法可用性")
        logger.info("=" * 50)
        
        crud_methods = [
            'list_departments',
            'list_pre_distributions', 
            'create_post_distribution',
            'update_post_distribution'
        ]
        
        for method_name in crud_methods:
            if hasattr(self.dd_crud, method_name):
                logger.info(f"✅ {method_name}: 方法存在")
            else:
                logger.error(f"❌ {method_name}: 方法不存在")
    
    async def verify_assignment_logic_integration(self):
        """验证分配逻辑集成"""
        logger.info("=" * 50)
        logger.info("验证分配逻辑集成")
        logger.info("=" * 50)
        
        try:
            # 检查是否正确初始化了CRUD实例
            if hasattr(self.assignment_logic, 'dd_crud'):
                logger.info("✅ assignment_logic.dd_crud: 已初始化")
            else:
                logger.error("❌ assignment_logic.dd_crud: 未初始化")
            
            # 检查数据库操作类是否正确初始化
            if hasattr(self.assignment_logic.db_operations, 'dd_crud'):
                logger.info("✅ db_operations.dd_crud: 已初始化")
            else:
                logger.error("❌ db_operations.dd_crud: 未初始化")
            
            # 检查部门推荐器是否正确初始化
            if hasattr(self.assignment_logic.department_recommender, 'dd_crud'):
                logger.info("✅ department_recommender.dd_crud: 已初始化")
            else:
                logger.error("❌ department_recommender.dd_crud: 未初始化")
                
        except Exception as e:
            logger.error(f"❌ 验证分配逻辑集成失败: {e}")
    
    async def verify_database_operations(self):
        """验证数据库操作"""
        logger.info("=" * 50)
        logger.info("验证数据库操作")
        logger.info("=" * 50)
        
        try:
            # 创建数据库操作实例
            db_ops = PostDistributionDBOperations(self.rdb_client, self.vdb_client)
            
            # 检查表名是否正确
            if db_ops.table_name == DDTableNames.BIZ_POST_DISTRIBUTION:
                logger.info(f"✅ 表名正确: {db_ops.table_name}")
            else:
                logger.error(f"❌ 表名错误: {db_ops.table_name}")
            
            # 检查CRUD实例
            if hasattr(db_ops, 'dd_crud') and db_ops.dd_crud is not None:
                logger.info("✅ CRUD实例已正确初始化")
            else:
                logger.error("❌ CRUD实例未正确初始化")
                
        except Exception as e:
            logger.error(f"❌ 验证数据库操作失败: {e}")
    
    async def verify_department_recommender(self):
        """验证部门推荐器"""
        logger.info("=" * 50)
        logger.info("验证部门推荐器")
        logger.info("=" * 50)
        
        try:
            # 创建部门推荐器实例
            recommender = TFIDFDepartmentRecommender(self.rdb_client)
            
            # 检查CRUD实例
            if hasattr(recommender, 'dd_crud') and recommender.dd_crud is not None:
                logger.info("✅ 部门推荐器CRUD实例已正确初始化")
            else:
                logger.error("❌ 部门推荐器CRUD实例未正确初始化")
            
            # 测试部门数据获取方法
            try:
                departments = await recommender._get_departments_data()
                logger.info(f"✅ 部门数据获取方法正常: 返回{len(departments)}条记录")
            except Exception as e:
                logger.warning(f"⚠️ 部门数据获取方法测试失败（可能是模拟客户端限制）: {e}")
                
        except Exception as e:
            logger.error(f"❌ 验证部门推荐器失败: {e}")
    
    async def verify_request_models(self):
        """验证请求模型"""
        logger.info("=" * 50)
        logger.info("验证请求模型")
        logger.info("=" * 50)
        
        try:
            # 测试单个分配请求
            request = DepartmentAssignmentRequest(
                submission_id="TEST_001",
                dr09="测试数据项",
                dr17="测试数据项定义",
                set_value="SET_A",
                report_type="detail",
                submission_type="submission",
                dr01="ADS"
            )
            
            request.validate()
            logger.info("✅ DepartmentAssignmentRequest: 验证通过")
            
            # 测试批量分配请求
            batch_request = BatchAssignmentRequest(
                report_code="g0107_beta_v1.0"
            )
            
            batch_request.validate()
            dr07, version = batch_request.get_dr07_and_version()
            logger.info(f"✅ BatchAssignmentRequest: 验证通过 (dr07={dr07}, version={version})")
            
        except Exception as e:
            logger.error(f"❌ 验证请求模型失败: {e}")
    
    async def run_full_verification(self):
        """运行完整验证"""
        logger.info("开始CRUD集成验证")
        
        try:
            # 设置环境
            await self.setup_mock_clients()
            
            # 运行各项验证
            await self.verify_table_names()
            await self.verify_crud_methods()
            await self.verify_assignment_logic_integration()
            await self.verify_database_operations()
            await self.verify_department_recommender()
            await self.verify_request_models()
            
            logger.info("=" * 50)
            logger.info("✅ CRUD集成验证完成")
            logger.info("=" * 50)
            
        except Exception as e:
            logger.error(f"❌ 验证过程中发生错误: {e}")
            logger.error(traceback.format_exc())
            
        finally:
            # 清理资源
            if self.rdb_client:
                await self.rdb_client.close()
            if self.vdb_client:
                await self.vdb_client.close()


async def main():
    """主函数"""
    verifier = CRUDIntegrationVerifier()
    await verifier.run_full_verification()


if __name__ == "__main__":
    asyncio.run(main())

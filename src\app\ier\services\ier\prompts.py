#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：src 
@File    ：prompts.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/23 15:05 
@Desc    ： 
"""
import json

def extract_json_from_response(response: str):
    try:
        # 尝试直接解析整个响应为 JSON
        return json.loads(response)
    except json.JSONDecodeError:
        pass

    try:
        # 提取 ```json 中的内容
        # print('我开始解析咯',response)
        start_idx = response.find("```json") + len("```json")
        end_idx = response.find("```", start_idx)
        if end_idx == -1:
            end_idx = len(response)
        json_str = response[start_idx:end_idx].strip().replace('\'', '"')
        return json.loads(json_str)
    except Exception as e:
        return {}

law_info_extraction_prompt = """
角色：你是一名法规/条例内容提炼专家。
任务：
    - 1.提取如下关键信息: 法规标题(law_title)、法规文号(law_number)、发文机关(agency)、发布日期(release_date)、法规总结(analyze_res)
    - 2.法规总结:对以下内容进行精炼概括，在保留关键信息的前提下，用简洁的语言生成概要，清晰呈现文章的核心要点与重要细节。
要求：
    - 输出格式为json,json需要包含法规标题(law_title)、法规文号(law_number)、发文机关(agency)、发布日期(release_date)、法规总结(analyze_res)
    - 法规标题(law_title) 如果提取到多个标题,返回最有可能的那一个
    - 发布日期(release_date) 需要统一规范为 yyyy-MM-dd 格式，如果没有具体到哪一天，则默认为 01 号，例如 “2024年6月” -> “2024-06-01”
    - 法规总结(analyze_res)字数控制在300字左右
    - 法规关键信息提取为空输出:null
    - 概要表述需清晰易懂，着重突出文章核心观点。
    - 剔除无关及重复信息，确保内容精炼。
    - 用自己的语言进行总结，同时完整涵盖原文关键要素。
    - 使用中文作答
#### 法规内容
{content}
```json
{{
    "law_title": "法规标题",
    "law_number": "法规文号",
    "agency": "发文机关",
    "release_date": "发布日期",
    "analyze": "法规总结",
}}
```
"""

is_main_file_prompt = """
角色：你是一名法规/条例判断分析专家。
任务：
    - 根据以下文件名，判断该文件是否为附件。
    - 根据文件的标题和法规号，判断该文件是否为主文件。
    - 返回一个包含两个键的json对象：
        - "is_attachment": 布尔值，表示该文件是否为附件。
        - "main_label": 布尔值，表示该文件是否为主文件。
要求：
    - 输出格式为json。
    - 如果文件有明确标题和法规号，并且不是附件，则认为是主文件；
    - 如果文件没有明确的标题，则认为不是主文件；
    - 如果文件没有法规号，则认为不是主文件；
    - 如果文件是附件，则认为不是主文件。
    - 使用中文作答
#### 文件信息
文件名: {file_name}
标题: {title}
法规号: {law_number}

####
这是输出格式:
```json
{{
    "is_attachment": true或者false,
    "main_label": true或者false
}}
```
"""

law_info_classification_prompt = """
角色：你是一名法规/条例分类专家。
任务：
    - 根据以下标题和总结内容，确定最合适的法规分类。
    - 返回一个包含法规分类名称的列表。
要求：
    - 输出格式为json，json需要包含一个名为"categories"的键，其值是一个包含分类名称的字符串列表。
    - 如果无法确定任何分类，则返回空列表。
    - 使用中文作答

#### 法规分类及其关键词
反洗钱: 反洗钱、洗钱
反恐怖融资: 反恐怖融资、恐怖融资
反贿赂反腐败: 反贿赂、反腐败、反商业贿赂、腐败
反逃税: 反逃税、逃税
电信网络诈骗: 
案件防控: 案防
制裁: 
内部审计: 内部审计、内审
五篇大文章: 五篇大文章、绿色金融、科技金融、养老金融、普惠金融、数字金融
绿色金融: 气候变化、转型金融、低碳转型
支付结算: 
消费者权益保护: 消费者权益保护、消费者保护、消保
小微、民营企业: 小微企业、民营企业、民企
数字货币: 
人工智能: 
合规管理: 
信贷管理: 贷款、融资、信贷
征信管理: 
网络安全: 
资本管理: 
账户管理: 
数据安全/质量: 数据安全、源头数据、数据管理、数据质量
公司治理: 
知识产权: 
外包管理: 外包、第三方
国际收支: 
RCPMIS: 
外汇管理: 
跨境人民币: 
基金销售: 
员工行为管理: 员工管理、行为管理
重大事项、重要信息: 重大事项、重要信息
声誉风险管理: 声誉
安全保卫: 
银行卡管理: 
电子银行业务: 
资金池业务: 
房贷业务: 

#### 法规内容
标题: {title}
总结: {summary}

####
这是输出格式:
```json
{{
    "categories": []
}}
```
"""

law_name_extract_prompt = """
角色：你是一名法规/条例提取专家。
任务：
    - 根据以下关联关键词列表、废止关键词列表以及法规内容，提取出本法规的关联法规和本法规发布后废止的法规。
    - 返回一个相关结果的字典列表。
    - law_match_type(法规匹配):
        - 1: 关联法规
        - 2: 废止法规
    - 如果 法规名后面有相关的发文信息 从中解析 发文机构(law_authority) 发文日期(law_year) 发文号(law_number) 
要求：
    - 输出格式为json
    - 没有匹配到返回空字典
    - 使用中文作答

#### 法规内容
关联关键词: {related_keyword}
废止关键词: {repealed_keyword}
法规内容: {content}

####
这是输出格式:
```json
{{
    "result": [
        {{
            "law_name":"",
            "law_number":"",
            "law_authority":"",
            "law_year":"",
            "law_match_type":""
        }}
    ]
}}
```
"""

law_match_reason_prompt = """
请根据以下两段法规文本的语义关联，用不多于50字的自然语言解释匹配原因：
chunk1:{chunk1}
chunk2:{chunk2}
只需输出理由本身，不要包含其他说明。
"""

law_question_type_match_prompt = """
角色：你是一名问题类型判断专家。
任务：
    - 判断用户提供的question的类型
    - 1：判断法规A与法规B有什么关联？
    - 2：判断法规A与法规B有什么差异？
    - 3：详细介绍法规A讲了什么？
    - 4：法规A的第几条/第几款说了什么？
    - 5：某段实践类涉及xxx的法规有哪些？
    - 6：其他类型
    
要求：
    - 输出格式为json
    - 如果没有匹配到前5种类型，返回q_type就为类型6
    - 没有匹配就返回
        {{ 
            "q_type": 6
        {{ 


#### 用户的问题
question: {question}

####
这是输出格式:
```json
{{
    "q_type":""
}}
```
"""

law_question_extract_law_name_prompt = """
角色：你是一名法规名提取专家。
任务：
    - 会提供一个可能包含法规名的问题，从中提取出法规名
    - 根据当前的知识和专家所具备的能力来回答用户的问题(question)
    
要求：
    - 用中文回答
    - 法规名两端不包含书名号等符号
    - 没有提取到返回
    {{
        "law_names":[]
    }}
    
#### 用户的问题
question: {question}

####
这是输出格式:
```json
{{
    "law_names":["", ""]
}}
```
"""

law_question_extract_date_prompt = """
角色：你是一名日期专家。
任务：
    - 会提供一个可能包含日期的问题和今天的日期，从中提取出日期或者时间段
    - 根据当前的知识和专家所具备的能力来回答用户的问题(question)
    - 如果提取到的不是一个范围日期 date_start和date_end 的值保持一致
要求：
    - 用中文回答
    - 没有提取到返回
    {{
        "date_start":""，
        "date_end":""
    }}

#### 用户的问题
question: {question}
today_date:{today_date}

####
这是输出格式:
```json
{{
    "date_start":"2025-07-28"，
    "date_end":"2025-07-29"
}}
```
"""

law_question_extract_pos_prompt = """
角色：你是一名法规信息提取专家。
任务：
    - 会提供一个问题，里面会问到类似，法规A的第几条/第几款讲了什么，需要你帮我提取第几条/第几款的数值
    - 没有提取到返回-1
    
要求：
    - 用中文回答
    - 只返回数值，不能返回 "第一条" 或 "第1条"
    - 没有提取到返回
    {{
        "pos": -1,
    }}

#### 用户的问题
question: {question}

####
这是输出格式:
```json
{{
    "pos": ""
}}
```
"""

chatbot_law_association_type_prompt = """ 
角色：你是一名法规关联专家。
任务：
    - 会提供最多5对，法规A和法规B想关联的信息(related_info)作为知识库，用来作为法规关联的依据
    - 根据当前的知识和专家所具备的能力来回答用户的问题(question)
    - history 为前三轮对话可以参考
    
要求：
    - 用中文回答
    
#### 用户的问题
question: {question}
related_info: {related_info}
history:{history}
"""

chatbot_law_difference_type_prompt = """
角色：你是一名法规差异比对专家。
任务：
    - 会提供多段法规的摘要，用来判断这些法规之间的差异
    - 根据当前的知识和专家所具备的能力来回答用户的问题(question)
    - history 为前三轮对话可以参考
    
要求：
    - 用中文回答
    
#### 用户的问题
question: {question}
summary_info: {summary_info}
history:{history}
"""

chatbot_law_positioning_type_prompt = """
角色：你是一名法规信息提取专家。
任务：
    - 会提供一段法规文件的内容(law_info)，需要你去根据内容回答用户的问题(question)
    - 用户的问题一般为法规A的第几条/第几款说了什么，回答这个法规的第几款/第几条具体的内容
    - history 为前三轮对话可以参考
        
要求：
    - 用中文回答
    
#### 用户的问题
question: {question}
law_info: {law_info}
history:{history}
"""

chatbot_law_time_judgment_type_prompt = """
角色：你是一名法规类型判断专家。
任务：
    - 会提供某段时间内的所有法规(laws)作为知识
    - 根据已知的内容回答’用户关于哪些法规涉及xxx的问题‘
    - 用户问题为question
    - history 为前三轮对话可以参考
    
要求：
    - 用中文回答
    
#### 用户的问题
question: {question}
laws: {laws}
history:{history}
"""

chatbot_other_type_prompt = """
角色：你是一名金融法规专家。
任务：
    - 回答用户询问的金融法规相关的问题以及其他问题。
    - 用户问题为question
    - history 为前三轮对话可以参考
    
要求：
    - 用中文回答
    
#### 用户的问题
question: {question}
history:{history}
"""



if __name__ == '__main__':
    prompt_dict = {'in': {'law_file_id': '6', 'law_id': '3', 'law_type': 'outside'}, 'out': {'law_file_id': '11', 'law_id': '4', 'law_type': 'outside'}, 'reason_list': [{'reason': '两段法规均要求金融机构境外分支加强合规管理，设立独立合 规部门，符合当地法律监管要求。', 'score': 92.40307854646753, 'in_mapping_content': '第二十九条 金融机构的境外金融分支机构及境外金融子公 司,应当遵循东道国(地区)法律法规 和监管要求,并且设立独立 的合规管理部门或者符合履职需要的合规岗位,负责根据境外 业务、市场情况、相关司法辖区法律法规以及执法环境等因 素,识别和防范合规风险,培养专业合规人 才。', 'out_mapping_content': '第二十四条 商业银行境外分支机构或附属机构应加强合规管理职能，合规管理职能的组织结构应符合当地的法律和监管要求。'}, {'reason': '两段法规均及金融机构适用范围，涵盖商业银行、政策性银行、金融资产管理公司等，但chunk2列举更详细且包括外资银行。', 'score': 93.65896188380854, 'in_mapping_content': '第二条 依法由国家金融监督管理总局及其派出机构监管的 政策性银行、商业银行、金融资产管理公司、企业集团财务公 司、金融租赁公司、汽车金融公司、消费金融公司、货币经纪 公司、信托公司、 理财公司、金融资产投资公司、保险公司 (包括再保险公司)、保险资产管理公司、保险集团(控股)公 司、相互保险组织等机构(以下统称金融机构)适用本办法。', 'out_mapping_content': '第二条 在中华人民共和国境内设立的中资商业银行、外资独资银行、中外合资银行和外国银行分行适用本指引。 在中华人民共和国境内设立的政策性银行、金融资产管理公司、城市信用合作社、农村信用合作社、信托投资公司、企业集团财务公司、金融租赁公司、汽车金融公司、货币经纪公司、邮政储蓄机构以及经银监会批准设立的其他金融机构参照本指引执行。'}, {'reason': '两法规均要求农村信用合作社和外国银行分行参照执行相关指引。', 'score': 90.7119555349932, 'in_mapping_content': '第五十五条 金融控股公司、农村合作银行、农村信用合作社、外国银行分行、外国再保险公司分公司以及其他由国家金 融监督管理总局及其派出机构监管的金融机构根据行业特点和 监管要求参照执行。', 'out_mapping_content': '第二条 在中华人民共和国境内设立的中资商业银行、外资独资银行、中外合资银行和外国银行分行适用本指引。 在中华人民共和国境内设立的政策性银行、金融资产管理公司、城市信用合作社、农村信 用合作社、信托投资公司、企业集团财务公司、金融租赁公司、汽车金融公司、货币经纪公司、邮政储蓄机构以及经银监会批准设立的其他金融机构参照本指引执行。'}, {'reason': '两段法均涉及法规生效或适用的具体说明，但chunk1规定了表述方式，chunk2明确了实施时间。', 'score': 85.90618650766227, 'in_mapping_content': '第五十七条 本办法所称"以上""以下"   均包含本数。', 'out_mapping_content': '第三十一条 本指引自发布之日起实施。</p>'}]}



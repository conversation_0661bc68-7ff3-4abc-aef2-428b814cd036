"""
码值系统完整CRUD测试

严格按照实际表结构进行测试：
- md_reference_code_set: 码值集表
- md_reference_code_value: 码值表  
- md_reference_code_relation: 码值关联表

测试内容：
- 完整的CRUD操作（创建、读取、更新、删除）
- 依赖关系处理（码值集 → 码值 → 码值关联）
- 级联删除处理
- 数据验证测试
- 错误处理测试
- 批量操作测试
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局测试数据存储
test_data_store = {
    'knowledge_id': None,
    'code_set_id': None,
    'code_value_ids': [],
    'code_relation_ids': []
}


async def setup_test_environment():
    """设置测试环境，创建测试知识库"""
    print("🔧 设置测试环境")
    print("-" * 40)

    try:
        # 获取客户端
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
        from service import get_client
        from modules.knowledge.knowledge.crud import KnowledgeCrud

        rdb_client = await get_client("database.rdbs.mysql")
        knowledge_crud = KnowledgeCrud(rdb_client)

        # 创建测试知识库
        timestamp = int(time.time())
        test_kb_data = {
            'knowledge_name': f'码值系统测试知识库_{timestamp}',
            'knowledge_type': 'MetaData',
            'knowledge_desc': '码值系统CRUD测试知识库',
            'models': {
                'embedding': 'moka-m3e-base'
            }
        }

        knowledge_id = await knowledge_crud.create_knowledge_base(test_kb_data)
        if not knowledge_id:
            raise Exception("创建测试知识库失败")

        test_data_store['knowledge_id'] = knowledge_id
        print(f"   ✅ 创建测试知识库: {knowledge_id}")

        return rdb_client, knowledge_id

    except Exception as e:
        logger.error(f"设置测试环境失败: {e}")
        raise


async def cleanup_test_environment():
    """清理测试环境"""
    print("\n🧹 清理测试环境")
    print("-" * 40)

    try:
        if test_data_store['knowledge_id']:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            from modules.knowledge.knowledge.crud import KnowledgeCrud

            rdb_client = await get_client("database.rdbs.mysql")
            knowledge_crud = KnowledgeCrud(rdb_client)

            # 删除测试知识库（会级联删除所有相关数据）
            await knowledge_crud.delete_knowledge_base(test_data_store['knowledge_id'])
            print(f"   ✅ 删除测试知识库: {test_data_store['knowledge_id']}")

    except Exception as e:
        logger.error(f"清理测试环境失败: {e}")


async def test_code_set_crud(rdb_client, knowledge_id: str):
    """测试码值集的完整CRUD操作"""
    print("\n1️⃣ 测试码值集CRUD操作:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_codes import MetadataCrudCodes
        
        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的
        
        codes_crud = MetadataCrudCodes(rdb_client, vdb_client, embedding_client)

        # 1. 创建码值集（使用正确的字段结构）
        timestamp = int(time.time())
        test_code_set_data = {
            'knowledge_id': knowledge_id,
            'code_set_name': f'test_code_set_{timestamp}',
            'code_set_desc': '测试码值集描述',
            'code_set_type': 'ENUM',
            'is_active': True
        }

        code_set_id, vector_results = await codes_crud.create_code_set(test_code_set_data)
        if not code_set_id or code_set_id <= 0:
            raise Exception("创建码值集失败：返回的ID无效")
        
        test_data_store['code_set_id'] = code_set_id
        print(f"   ✅ 创建码值集: {code_set_id}")
        print(f"   📊 向量化结果: {len(vector_results)} 个向量")

        # 2. 获取码值集（主键查询）
        code_set = await codes_crud.get_code_set(code_set_id)
        if not code_set or not code_set.get('code_set_name'):
            raise Exception("主键查询码值集失败：未返回有效数据")
        print(f"   ✅ 主键获取码值集: {code_set['code_set_name']}")

        # 3. 获取码值集（条件查询）
        code_set_by_name = await codes_crud.get_code_set(
            knowledge_id=knowledge_id,
            code_set_name=test_code_set_data['code_set_name']
        )
        if not code_set_by_name or not code_set_by_name.get('id'):
            raise Exception("条件查询码值集失败：未返回有效数据")
        print(f"   ✅ 条件查询码值集: {code_set_by_name['id']}")

        # 4. 更新码值集
        update_success = await codes_crud.update_code_set(
            {'code_set_desc': '更新后的码值集描述'},
            code_set_id=code_set_id
        )
        if not update_success:
            raise Exception("更新码值集失败：返回False")
        print(f"   ✅ 更新码值集: {update_success}")

        # 5. 验证更新
        updated_code_set = await codes_crud.get_code_set(code_set_id)
        if not updated_code_set or '更新后的码值集描述' not in updated_code_set.get('code_set_desc', ''):
            raise Exception("验证更新失败：描述未正确更新")
        print(f"   ✅ 验证更新: {updated_code_set['code_set_desc']}")

        # 6. 列出码值集
        code_sets_list = await codes_crud.list_code_sets(knowledge_id=knowledge_id)
        if not code_sets_list or len(code_sets_list) == 0:
            raise Exception("列出码值集失败：未返回数据")
        print(f"   ✅ 列出码值集: {len(code_sets_list)} 个")

        return True

    except Exception as e:
        print(f"   ❌ 码值集CRUD测试失败: {e}")
        return False


async def test_code_value_crud(rdb_client, knowledge_id: str, code_set_id: int):
    """测试码值的完整CRUD操作"""
    print("\n2️⃣ 测试码值CRUD操作:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_codes import MetadataCrudCodes
        
        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的
        
        codes_crud = MetadataCrudCodes(rdb_client, vdb_client, embedding_client)

        # 1. 创建码值（使用正确的字段结构）
        timestamp = int(time.time())
        test_code_value_data = {
            'knowledge_id': knowledge_id,
            'code_set_id': code_set_id,
            'code_value': f'TEST_CODE_{timestamp}',
            'code_desc': '测试码值描述',
            'code_value_cn': f'测试码值_{timestamp}',
            'comment': '测试码值备注',
            'is_active': True
        }

        code_value_id, vector_results = await codes_crud.create_code_value(test_code_value_data)
        if not code_value_id or code_value_id <= 0:
            raise Exception("创建码值失败：返回的ID无效")
        
        test_data_store['code_value_ids'].append(code_value_id)
        print(f"   ✅ 创建码值: {code_value_id}")
        print(f"   📊 向量化结果: {len(vector_results)} 个向量")

        # 2. 获取码值（主键查询）
        code_value = await codes_crud.get_code_value(code_value_id)
        if not code_value or not code_value.get('code_value'):
            raise Exception("主键查询码值失败：未返回有效数据")
        print(f"   ✅ 主键获取码值: {code_value['code_value']}")

        # 3. 更新码值
        update_success = await codes_crud.update_code_value(
            {'code_desc': '更新后的码值描述'},
            code_value_id=code_value_id
        )
        if not update_success:
            raise Exception("更新码值失败：返回False")
        print(f"   ✅ 更新码值: {update_success}")

        # 4. 列出码值
        code_values_list = await codes_crud.list_code_values(code_set_id=code_set_id)
        if not code_values_list or len(code_values_list) == 0:
            raise Exception("列出码值失败：未返回数据")
        print(f"   ✅ 列出码值: {len(code_values_list)} 个")

        return True

    except Exception as e:
        print(f"   ❌ 码值CRUD测试失败: {e}")
        return False


async def test_search_functionality(rdb_client, knowledge_id: str):
    """测试搜索功能"""
    print("\n3️⃣ 测试搜索功能:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_codes import MetadataCrudCodes

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client

            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的

        codes_crud = MetadataCrudCodes(rdb_client, vdb_client, embedding_client)

        # 搜索码值集
        search_results = await codes_crud.search_code_sets(
            search_term="test",
            knowledge_id=knowledge_id,
            limit=10
        )
        print(f"   ✅ 搜索码值集: 找到 {len(search_results)} 个结果")

        # 搜索码值
        if test_data_store['code_set_id']:
            value_results = await codes_crud.search_code_values(
                search_term="TEST",
                knowledge_id=knowledge_id,
                code_set_id=test_data_store['code_set_id'],
                limit=10
            )
            print(f"   ✅ 搜索码值: 找到 {len(value_results)} 个结果")

        # 测试分页查询
        page1_results = await codes_crud.list_code_sets(
            knowledge_id=knowledge_id,
            limit=1,
            offset=0
        )
        print(f"   ✅ 分页查询(第1页): {len(page1_results)} 个结果")

        return True

    except Exception as e:
        print(f"   ❌ 搜索功能测试失败: {e}")
        return False


async def test_vector_search_functionality(rdb_client, knowledge_id: str):
    """测试向量搜索功能"""
    print("\n4️⃣ 测试向量搜索功能:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.search import MetadataSearch

        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client

            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            print(f"   ⚠️  向量化客户端未配置，跳过向量搜索测试: {e}")
            return True

        search_engine = MetadataSearch(rdb_client, vdb_client, embedding_client)

        # 测试码值的向量搜索
        code_results = await search_engine.search_codes_by_vector(
            query="测试码值数据",
            knowledge_id=knowledge_id,
            limit=5,
            min_score=0.3
        )
        print(f"   ✅ 码值向量搜索: 找到 {len(code_results)} 个结果")

        # 显示搜索结果详情
        for i, result in enumerate(code_results[:2]):  # 只显示前2个结果
            score = result.get('score', 0)
            entity_data = result.get('entity_data', {})
            code_value = entity_data.get('code_value', 'Unknown')
            code_set_info = entity_data.get('code_set_info', {})
            code_set_name = code_set_info.get('code_set_name', 'Unknown')
            print(f"     结果{i+1}: {code_value} (码值集: {code_set_name}, 相似度: {score:.3f})")

        # 测试综合向量搜索（包含码值）
        all_results = await search_engine.vector_search(
            query="码值测试",
            knowledge_id=knowledge_id,
            entity_types=['code_value'],
            limit=10,
            min_score=0.3
        )
        print(f"   ✅ 综合向量搜索: 找到 {len(all_results)} 个结果")

        # 测试搜索结果的相似度排序
        if all_results:
            scores = [result.get('score', 0) for result in all_results]
            is_sorted = all(scores[i] >= scores[i+1] for i in range(len(scores)-1))
            print(f"   ✅ 相似度排序: {'正确' if is_sorted else '错误'}")

        # 测试向量搜索的性能
        import time
        start_time = time.time()
        performance_results = await search_engine.search_codes_by_vector(
            query="性能测试",
            knowledge_id=knowledge_id,
            limit=20,
            min_score=0.2
        )
        end_time = time.time()
        search_time = end_time - start_time
        print(f"   ✅ 向量搜索性能: {search_time:.3f}秒 (找到 {len(performance_results)} 个结果)")

        return True

    except Exception as e:
        print(f"   ❌ 向量搜索功能测试失败: {e}")
        return False


async def test_error_handling(rdb_client):
    """测试错误处理"""
    print("\n5️⃣ 测试错误处理:")
    print("-" * 40)

    try:
        from modules.knowledge.metadata.crud_modules.crud_codes import MetadataCrudCodes
        
        # 获取向量化客户端（可选）
        vdb_client = None
        embedding_client = None
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            from service import get_client
            
            vdb_client = await get_client("database.vdbs.pgvector")
            embedding_client = await get_client("model.embeddings.moka-m3e-base")
        except Exception as e:
            pass  # 向量化客户端是可选的
        
        codes_crud = MetadataCrudCodes(rdb_client, vdb_client, embedding_client)

        # 测试获取不存在的记录
        non_existent = await codes_crud.get_code_set(code_set_id=999999)
        if non_existent is not None:
            raise Exception("获取不存在记录应该返回None")
        print(f"   ✅ 获取不存在记录: 正确返回None")

        # 测试更新不存在的记录
        update_result = await codes_crud.update_code_set(
            {'code_set_desc': '测试更新'},
            code_set_id=999999
        )
        if update_result:
            raise Exception("更新不存在记录应该返回False")
        print(f"   ✅ 更新不存在记录: 正确返回False")

        # 测试删除不存在的记录
        delete_result = await codes_crud.delete_code_set(code_set_id=999999)
        if delete_result:
            raise Exception("删除不存在记录应该返回False")
        print(f"   ✅ 删除不存在记录: 正确返回False")

        return True

    except Exception as e:
        print(f"   ❌ 错误处理测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 码值系统完整CRUD测试")
    print("=" * 80)

    try:
        # 1. 设置测试环境
        rdb_client, knowledge_id = await setup_test_environment()

        # 2. 执行所有测试
        test_results = []
        
        # 码值集CRUD操作测试
        result1 = await test_code_set_crud(rdb_client, knowledge_id)
        test_results.append(("码值集CRUD", result1))

        # 码值CRUD操作测试
        if test_data_store['code_set_id']:
            result2 = await test_code_value_crud(rdb_client, knowledge_id, test_data_store['code_set_id'])
            test_results.append(("码值CRUD", result2))

        # 搜索功能测试
        result3 = await test_search_functionality(rdb_client, knowledge_id)
        test_results.append(("搜索功能", result3))

        # 向量搜索功能测试
        result4 = await test_vector_search_functionality(rdb_client, knowledge_id)
        test_results.append(("向量搜索功能", result4))

        # 错误处理测试
        result5 = await test_error_handling(rdb_client)
        test_results.append(("错误处理", result5))

        # 3. 输出测试结果
        print("\n" + "=" * 80)
        print("📊 测试结果汇总")
        print("=" * 80)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False

        if all_passed:
            print("\n🎉 所有测试通过！码值系统CRUD功能正常")
        else:
            print("\n⚠️  部分测试失败，请检查日志")

    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        print(f"\n❌ 测试执行失败: {e}")
    
    finally:
        # 4. 清理测试环境
        await cleanup_test_environment()


if __name__ == "__main__":
    asyncio.run(main())

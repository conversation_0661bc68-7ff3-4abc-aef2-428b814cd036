"""
Metadata系统核心数据库/表/字段CRUD操作

包含以下实体的CRUD操作：
- 源数据库 (md_source_database)
- 指标数据库 (md_index_database) 
- 源表 (md_source_tables)
- 指标表 (md_index_tables)
- 源字段 (md_source_columns)
- 指标字段 (md_index_columns)
"""

from typing import Any, Dict, List, Optional, Tuple, Union
import logging
from datetime import datetime

from .crud_base import MetadataCrudBase
from ..shared.exceptions import MetadataError, MetadataValidationError, MetadataNotFoundError, MetadataConflictError
from ..shared.constants import MetadataConstants, MetadataTableNames, MetadataCascadeRelations, MetadataVectorCollections
from ..shared.utils import MetadataUtils

logger = logging.getLogger(__name__)


class MetadataCrudMeta(MetadataCrudBase):
    """Metadata系统核心数据库/表/字段CRUD操作"""

    # ==================== 源数据库操作 ====================

    async def create_source_database(self, db_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """
        创建源数据库

        Args:
            db_data: 数据库数据，必须包含：
                - knowledge_id: 知识库ID
                - db_name: 数据库名称
                - data_layer: 数据层标识 (adm/bdm/ods/ads)
                - db_name_cn: 数据库中文名称（可选）
                - db_desc: 数据库描述（可选）
                - is_active: 是否激活（可选，默认1）

        Returns:
            (数据库ID, 向量创建结果列表)

        Raises:
            MetadataValidationError: 数据验证失败
            MetadataConflictError: 数据库已存在
            MetadataError: 创建失败
        """
        try:
            # 数据验证
            MetadataUtils.validate_source_database_data(db_data)

            # 检查是否已存在（根据业务唯一键）
            existing = await self._aselect(
                table=MetadataTableNames.MD_SOURCE_DATABASE,
                where={'knowledge_id': db_data['knowledge_id'], 'db_name': db_data['db_name']},
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"源数据库已存在: {db_data['db_name']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(db_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_SOURCE_DATABASE,
                data=[db_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                error_msg = getattr(result, 'error', '未知错误')
                raise MetadataError(f"创建源数据库失败: {error_msg}")

            # 获取插入的ID（使用主键字段 db_id）
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_SOURCE_DATABASE,
                where={'knowledge_id': db_data['knowledge_id'], 'db_name': db_data['db_name']},
                limit=1
            )

            db_id = inserted_records[0].get('db_id', 0) if inserted_records else 0

            # 处理向量化
            vector_results = []
            if self.vdb_client and self.embedding_client and db_id:
                try:
                    vector_results = await self._create_vectors('source_database', db_id, db_data)
                except Exception as e:
                    logger.warning(f"源数据库向量化失败: {e}")

            logger.info(f"源数据库创建成功: {db_data['db_name']} (ID: {db_id})")
            return db_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建源数据库失败: {e}")
            raise MetadataError(f"创建源数据库失败: {e}")

    async def get_source_database(self, db_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """
        获取源数据库

        Args:
            db_id: 数据库ID（可选）
            **where_conditions: 其他查询条件

        Returns:
            数据库信息字典，如果不存在则返回None

        Raises:
            MetadataValidationError: 查询条件无效
        """
        if db_id:
            where = {'db_id': db_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 db_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_SOURCE_DATABASE,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_source_database(self, db_data: Dict[str, Any], db_id: int = None, **where_conditions) -> bool:
        """更新源数据库"""
        if db_id:
            where = {'db_id': db_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 db_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(db_data, is_update=True)

        try:
            # 使用批量更新（单条，优化参数）
            updates = [{"data": db_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_SOURCE_DATABASE,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )

            if result.success and result.affected_rows > 0:
                # 更新向量
                if self.vdb_client and self.embedding_client and db_id:
                    try:
                        # 获取完整的实体数据
                        full_data = await self.get_source_database(db_id)
                        if full_data:
                            await self._update_vectors('source_database', db_id, db_data, full_data)
                    except Exception as e:
                        logger.warning(f"源数据库向量更新失败: {e}")
                return True
            return False

        except Exception as e:
            logger.error(f"更新源数据库失败: {e}")
            raise MetadataError(f"更新源数据库失败: {e}")

    async def delete_source_database(self, db_id: int = None, **where_conditions) -> bool:
        """删除源数据库"""
        if db_id:
            where = {'db_id': db_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 db_id 或其他删除条件")

        try:
            # 先删除向量（级联删除相关表和字段的向量）
            if self.vdb_client and db_id:
                await self._cascade_delete_vectors('source_database', db_id)

            # 删除数据库记录（MySQL会自动级联删除相关表和字段）
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_SOURCE_DATABASE,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除源数据库失败: {e}")
            raise MetadataError(f"删除源数据库失败: {e}")

    async def list_source_databases(
        self,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询源数据库列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            data_layer=data_layer,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_SOURCE_DATABASE,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    async def batch_list_source_databases(
        self,
        query_conditions: List[Dict[str, Any]]
    ) -> List[List[Dict[str, Any]]]:
        """
        批量查询源数据库列表 - 用于需要同时执行多个不同查询条件的场景
        
        Args:
            query_conditions: 查询条件列表，每个条件包含 knowledge_id, data_layer, is_active, limit, offset 等
            
        Returns:
            每个查询条件对应的结果列表
        """
        if not query_conditions:
            return []
            
        try:
            # 构建批量查询请求
            queries = []
            for condition in query_conditions:
                where = MetadataUtils.build_search_filters(
                    knowledge_id=condition.get('knowledge_id'),
                    data_layer=condition.get('data_layer'),
                    is_active=condition.get('is_active'),
                    **{k: v for k, v in condition.items() if k not in ['knowledge_id', 'data_layer', 'is_active', 'limit', 'offset']}
                )
                
                query = {"table": MetadataTableNames.MD_SOURCE_DATABASE}
                if where:
                    query["filters"] = where
                if condition.get('limit'):
                    query["limit"] = condition['limit']
                if condition.get('offset'):
                    query["offset"] = condition['offset']
                query["sorts"] = [{"field": "create_time", "order": "desc"}]
                
                queries.append(query)
            
            # 执行批量查询
            batch_results = await self.rdb_client.abatch_query(
                table=MetadataTableNames.MD_SOURCE_DATABASE,
                queries=queries
            )
            
            # 提取结果数据
            return [result.data if result else [] for result in batch_results]
            
        except Exception as e:
            logger.error(f"批量查询源数据库列表失败: {e}")
            raise MetadataError(f"批量查询源数据库列表失败: {e}")

    # ==================== 批量操作 ====================

    async def batch_create_source_databases(self, databases_data: List[Dict[str, Any]]) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """
        批量创建源数据库

        Args:
            databases_data: 数据库数据列表

        Returns:
            (数据库ID列表, 向量创建结果列表)
        """
        if not databases_data:
            return [], []

        try:
            # 数据验证和预处理
            processed_data = []
            for db_data in databases_data:
                MetadataUtils.validate_source_database_data(db_data)
                MetadataUtils.add_timestamps(db_data)
                processed_data.append(db_data)

            # 批量插入
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_SOURCE_DATABASE,
                data=processed_data
            )

            if not result.success:
                raise MetadataError(f"批量创建源数据库失败: {result.error}")

            # 优化：使用单次批量查询获取所有插入的ID
            if processed_data:
                # 构建批量查询条件
                where_conditions = []
                for db_data in processed_data:
                    where_conditions.append({
                        'knowledge_id': db_data['knowledge_id'], 
                        'db_name': db_data['db_name']
                    })
                
                # 使用批量查询获取所有ID
                try:
                    batch_results = await self.rdb_client.abatch_query(
                        table=MetadataTableNames.MD_SOURCE_DATABASE,
                        queries=[{"filters": where_cond, "limit": 1} for where_cond in where_conditions]
                    )
                    
                    db_ids = []
                    for i, query_result in enumerate(batch_results):
                        if query_result.data:
                            db_ids.append(query_result.data[0].get('db_id', 0))
                        else:
                            db_ids.append(0)
                            logger.warning(f"未找到插入的数据库记录: {where_conditions[i]}")
                            
                except Exception as e:
                    logger.warning(f"批量查询插入ID失败，回退到逐个查询: {e}")
                    # 回退到原来的逐个查询方式
                    db_ids = []
                    for db_data in processed_data:
                        inserted_records = await self._aselect(
                            table=MetadataTableNames.MD_SOURCE_DATABASE,
                            where={'knowledge_id': db_data['knowledge_id'], 'db_name': db_data['db_name']},
                            limit=1
                        )
                        if inserted_records:
                            db_ids.append(inserted_records[0].get('db_id', 0))
                        else:
                            db_ids.append(0)
            else:
                db_ids = []

            # 处理向量化
            all_vector_results = []
            if self.vdb_client and self.embedding_client:
                for i, db_data in enumerate(processed_data):
                    db_id = db_ids[i]
                    if db_id:
                        try:
                            vector_results = await self._create_vectors('source_database', db_id, db_data)
                            all_vector_results.append(vector_results)
                        except Exception as e:
                            logger.warning(f"源数据库向量化失败: {e}")
                            all_vector_results.append([])
                    else:
                        all_vector_results.append([])

            logger.info(f"批量创建源数据库成功: {len(db_ids)} 个数据库")
            return db_ids, all_vector_results

        except Exception as e:
            logger.error(f"批量创建源数据库失败: {e}")
            raise MetadataError(f"批量创建源数据库失败: {e}")

    # ==================== 指标数据库操作 ====================

    async def create_index_database(self, db_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建指标数据库"""
        try:
            # 数据验证
            MetadataUtils.validate_index_database_data(db_data)

            # 检查是否已存在
            existing = await self._aselect(
                table=MetadataTableNames.MD_INDEX_DATABASE,
                where={'knowledge_id': db_data['knowledge_id'], 'db_name': db_data['db_name']},
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"指标数据库已存在: {db_data['db_name']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(db_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_INDEX_DATABASE,
                data=[db_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                raise MetadataError(f"创建指标数据库失败: {result.error}")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_INDEX_DATABASE,
                where={'knowledge_id': db_data['knowledge_id'], 'db_name': db_data['db_name']},
                limit=1
            )

            db_id = inserted_records[0].get('db_id', 0) if inserted_records else 0

            # 处理向量化
            vector_results = []
            if self.vdb_client and self.embedding_client and db_id:
                try:
                    vector_results = await self._create_vectors('index_database', db_id, db_data)
                except Exception as e:
                    logger.warning(f"指标数据库向量化失败: {e}")

            logger.info(f"指标数据库创建成功: {db_data['db_name']} (ID: {db_id})")
            return db_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建指标数据库失败: {e}")
            raise MetadataError(f"创建指标数据库失败: {e}")

    async def get_index_database(self, db_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取指标数据库"""
        if db_id:
            where = {'db_id': db_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 db_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_INDEX_DATABASE,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_index_database(self, db_data: Dict[str, Any], db_id: int = None, **where_conditions) -> bool:
        """更新指标数据库"""
        if db_id:
            where = {'db_id': db_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 db_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(db_data, is_update=True)

        try:
            # 使用批量更新（单条，优化参数）
            updates = [{"data": db_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_INDEX_DATABASE,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )

            if result.success and result.affected_rows > 0:
                # 更新向量
                if self.vdb_client and self.embedding_client and db_id:
                    try:
                        # 获取完整的实体数据
                        full_data = await self.get_index_database(db_id)
                        if full_data:
                            await self._update_vectors('index_database', db_id, db_data, full_data)
                    except Exception as e:
                        logger.warning(f"指标数据库向量更新失败: {e}")
                return True
            return False

        except Exception as e:
            logger.error(f"更新指标数据库失败: {e}")
            raise MetadataError(f"更新指标数据库失败: {e}")

    async def delete_index_database(self, db_id: int = None, **where_conditions) -> bool:
        """删除指标数据库"""
        if db_id:
            where = {'db_id': db_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 db_id 或其他删除条件")

        try:
            # 先删除向量（级联删除相关表和字段的向量）
            if self.vdb_client and db_id:
                await self._cascade_delete_vectors('index_database', db_id)

            # 删除数据库记录（MySQL会自动级联删除相关表和字段）
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_INDEX_DATABASE,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除指标数据库失败: {e}")
            raise MetadataError(f"删除指标数据库失败: {e}")

    async def list_index_databases(
        self,
        knowledge_id: Optional[str] = None,
        data_layer: Optional[str] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询指标数据库列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            data_layer=data_layer,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_INDEX_DATABASE,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    # ==================== 源表操作 ====================

    async def create_source_table(self, table_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建源表"""
        try:
            # 数据验证
            MetadataUtils.validate_source_table_data(table_data)

            # 检查是否已存在
            existing = await self._aselect(
                table=MetadataTableNames.MD_SOURCE_TABLES,
                where={
                    'knowledge_id': table_data['knowledge_id'],
                    'db_id': table_data['db_id'],
                    'table_name': table_data['table_name']
                },
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"源表已存在: {table_data['table_name']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(table_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_SOURCE_TABLES,
                data=[table_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                error_msg = getattr(result, 'error', '未知错误')
                raise MetadataError(f"创建源表失败: {error_msg}")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_SOURCE_TABLES,
                where={
                    'knowledge_id': table_data['knowledge_id'],
                    'db_id': table_data['db_id'],
                    'table_name': table_data['table_name']
                },
                limit=1
            )

            table_id = inserted_records[0].get('table_id', 0) if inserted_records else 0

            # 处理向量化
            vector_results = []
            if self.vdb_client and self.embedding_client and table_id:
                try:
                    vector_results = await self._create_vectors('source_table', table_id, table_data)
                except Exception as e:
                    logger.warning(f"源表向量化失败: {e}")

            logger.info(f"源表创建成功: {table_data['table_name']} (ID: {table_id})")
            return table_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建源表失败: {e}")
            raise MetadataError(f"创建源表失败: {e}")

    async def get_source_table(self, table_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取源表"""
        if table_id:
            where = {'table_id': table_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 table_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_SOURCE_TABLES,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_source_table(self, table_data: Dict[str, Any], table_id: int = None, **where_conditions) -> bool:
        """更新源表"""
        if table_id:
            where = {'table_id': table_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 table_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(table_data, is_update=True)

        try:
            # 使用批量更新（单条，优化参数）
            updates = [{"data": table_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_SOURCE_TABLES,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )

            if result.success and result.affected_rows > 0:
                # 更新向量
                if self.vdb_client and self.embedding_client and table_id:
                    try:
                        # 获取完整的实体数据
                        full_data = await self.get_source_table(table_id)
                        if full_data:
                            await self._update_vectors('source_table', table_id, table_data, full_data)
                    except Exception as e:
                        logger.warning(f"源表向量更新失败: {e}")
                return True
            return False

        except Exception as e:
            logger.error(f"更新源表失败: {e}")
            raise MetadataError(f"更新源表失败: {e}")

    async def delete_source_table(self, table_id: int = None, **where_conditions) -> bool:
        """删除源表"""
        if table_id:
            where = {'table_id': table_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 table_id 或其他删除条件")

        try:
            # 先删除向量（级联删除相关字段的向量）
            if self.vdb_client and table_id:
                await self._cascade_delete_vectors('source_table', table_id)

            # 删除表记录（MySQL会自动级联删除相关字段）
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_SOURCE_TABLES,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除源表失败: {e}")
            raise MetadataError(f"删除源表失败: {e}")

    async def list_source_tables(
        self,
        knowledge_id: Optional[str] = None,
        db_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询源表列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            db_id=db_id,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_SOURCE_TABLES,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    # ==================== 指标表操作 ====================

    async def create_index_table(self, table_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """创建指标表"""
        try:
            # 数据验证
            MetadataUtils.validate_index_table_data(table_data)

            # 检查是否已存在
            existing = await self._aselect(
                table=MetadataTableNames.MD_INDEX_TABLES,
                where={
                    'knowledge_id': table_data['knowledge_id'],
                    'db_id': table_data['db_id'],
                    'table_name': table_data['table_name']
                },
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"指标表已存在: {table_data['table_name']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(table_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_INDEX_TABLES,
                data=[table_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                raise MetadataError(f"创建指标表失败: {result.error}")

            # 获取插入的ID
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_INDEX_TABLES,
                where={
                    'knowledge_id': table_data['knowledge_id'],
                    'db_id': table_data['db_id'],
                    'table_name': table_data['table_name']
                },
                limit=1
            )

            table_id = inserted_records[0].get('table_id', 0) if inserted_records else 0

            # 处理向量化
            vector_results = []
            if self.vdb_client and self.embedding_client and table_id:
                try:
                    vector_results = await self._create_vectors('index_table', table_id, table_data)
                except Exception as e:
                    logger.warning(f"指标表向量化失败: {e}")

            logger.info(f"指标表创建成功: {table_data['table_name']} (ID: {table_id})")
            return table_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建指标表失败: {e}")
            raise MetadataError(f"创建指标表失败: {e}")

    async def get_index_table(self, table_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取指标表"""
        if table_id:
            where = {'table_id': table_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 table_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_INDEX_TABLES,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_index_table(self, table_data: Dict[str, Any], table_id: int = None, **where_conditions) -> bool:
        """更新指标表"""
        if table_id:
            where = {'table_id': table_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 table_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(table_data, is_update=True)

        try:
            # 使用批量更新（单条，优化参数）
            updates = [{"data": table_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_INDEX_TABLES,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )

            if result.success and result.affected_rows > 0:
                # 更新向量
                if self.vdb_client and self.embedding_client and table_id:
                    try:
                        await self._update_vectors('index_table', table_id, table_data)
                    except Exception as e:
                        logger.warning(f"指标表向量更新失败: {e}")
                return True
            return False

        except Exception as e:
            logger.error(f"更新指标表失败: {e}")
            raise MetadataError(f"更新指标表失败: {e}")

    async def delete_index_table(self, table_id: int = None, **where_conditions) -> bool:
        """删除指标表"""
        if table_id:
            where = {'table_id': table_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 table_id 或其他删除条件")

        try:
            # 先删除向量（级联删除相关字段的向量）
            if self.vdb_client and table_id:
                await self._cascade_delete_vectors('index_table', table_id)

            # 删除表记录（MySQL会自动级联删除相关字段）
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_INDEX_TABLES,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除指标表失败: {e}")
            raise MetadataError(f"删除指标表失败: {e}")

    async def list_index_tables(
        self,
        knowledge_id: Optional[str] = None,
        db_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询指标表列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            db_id=db_id,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_INDEX_TABLES,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    # ==================== 源字段操作 ====================

    async def create_source_column(self, column_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """
        创建源字段

        Args:
            column_data: 字段数据，必须包含：
                - knowledge_id: 知识库ID
                - table_id: 表ID
                - column_name: 字段名
                - column_name_cn: 字段中文名（可选）
                - column_desc: 字段描述（可选）
                - data_type: 数据类型（可选）
                - data_example: 数据样例（可选）
                - is_primary_key: 是否主键（可选，默认0）
                - is_sensitive: 是否敏感数据（可选，默认0）

        Returns:
            (字段ID, 向量创建结果列表)

        Raises:
            MetadataValidationError: 数据验证失败
            MetadataConflictError: 字段已存在
            MetadataError: 创建失败
        """
        try:
            # 数据验证
            MetadataUtils.validate_source_column_data(column_data)

            # 检查是否已存在（根据业务唯一键）
            existing = await self._aselect(
                table=MetadataTableNames.MD_SOURCE_COLUMNS,
                where={
                    'knowledge_id': column_data['knowledge_id'],
                    'table_id': column_data['table_id'],
                    'column_name': column_data['column_name']
                },
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"源字段已存在: {column_data['column_name']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(column_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_SOURCE_COLUMNS,
                data=[column_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                error_msg = getattr(result, 'error', '未知错误')
                raise MetadataError(f"创建源字段失败: {error_msg}")

            # 获取插入的ID（使用主键字段 column_id）
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_SOURCE_COLUMNS,
                where={
                    'knowledge_id': column_data['knowledge_id'],
                    'table_id': column_data['table_id'],
                    'column_name': column_data['column_name']
                },
                limit=1
            )

            column_id = inserted_records[0].get('column_id', 0) if inserted_records else 0

            # 处理向量化
            vector_results = []
            if self.vdb_client and self.embedding_client and column_id:
                try:
                    vector_results = await self._create_vectors('source_column', column_id, column_data)
                except Exception as e:
                    logger.warning(f"源字段向量化失败: {e}")

            logger.info(f"源字段创建成功: {column_data['column_name']} (ID: {column_id})")
            return column_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建源字段失败: {e}")
            raise MetadataError(f"创建源字段失败: {e}")

    async def get_source_column(self, column_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """获取源字段"""
        if column_id:
            where = {'column_id': column_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 column_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_SOURCE_COLUMNS,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_source_column(self, column_data: Dict[str, Any], column_id: int = None, **where_conditions) -> bool:
        """更新源字段"""
        if column_id:
            where = {'column_id': column_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 column_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(column_data, is_update=True)

        try:
            # 使用批量更新（单条，优化参数）
            updates = [{"data": column_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_SOURCE_COLUMNS,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )

            if result.success and result.affected_rows > 0:
                # 更新向量
                if self.vdb_client and self.embedding_client and column_id:
                    try:
                        # 获取完整的实体数据
                        full_data = await self.get_source_column(column_id)
                        if full_data:
                            await self._update_vectors('source_column', column_id, column_data, full_data)
                    except Exception as e:
                        logger.warning(f"源字段向量更新失败: {e}")
                return True
            return False

        except Exception as e:
            logger.error(f"更新源字段失败: {e}")
            raise MetadataError(f"更新源字段失败: {e}")

    async def delete_source_column(self, column_id: int = None, **where_conditions) -> bool:
        """删除源字段"""
        if column_id:
            where = {'column_id': column_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 column_id 或其他删除条件")

        try:
            # 先删除向量
            if self.vdb_client and column_id:
                await self._delete_vectors('source_column', column_id)

            # 删除字段记录
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_SOURCE_COLUMNS,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除源字段失败: {e}")
            raise MetadataError(f"删除源字段失败: {e}")

    async def list_source_columns(
        self,
        knowledge_id: Optional[str] = None,
        table_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """查询源字段列表"""
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            table_id=table_id,
            is_active=is_active,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_SOURCE_COLUMNS,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    # ==================== 指标字段操作 ====================

    async def create_index_column(self, column_data: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
        """
        创建指标字段

        Args:
            column_data: 字段数据，必须包含：
                - knowledge_id: 知识库ID
                - table_id: 表ID
                - column_name: 字段名
                - column_name_cn: 字段中文名（可选）
                - index_type: 指标类型（可选）atom-原子指标，compute-计算指标
                - column_desc: 字段描述（可选）
                - data_type: 数据类型（可选）
                - data_example: 数据样例（可选）
                - comment: 备注说明（可选）
                - is_primary_key: 是否主键（可选，默认0）
                - is_sensitive: 是否敏感数据（可选，默认0）

        Returns:
            (字段ID, 向量创建结果列表)

        Raises:
            MetadataValidationError: 数据验证失败
            MetadataConflictError: 字段已存在
            MetadataError: 创建失败
        """
        try:
            # 数据验证
            MetadataUtils.validate_index_column_data(column_data)

            # 检查是否已存在（根据业务唯一键）
            existing = await self._aselect(
                table=MetadataTableNames.MD_INDEX_COLUMNS,
                where={
                    'knowledge_id': column_data['knowledge_id'],
                    'table_id': column_data['table_id'],
                    'column_name': column_data['column_name']
                },
                limit=1
            )
            if existing:
                raise MetadataConflictError(f"指标字段已存在: {column_data['column_name']}")

            # 添加时间戳
            MetadataUtils.add_timestamps(column_data)

            # 使用批量插入（单条，优化参数）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_INDEX_COLUMNS,
                data=[column_data],
                batch_size=1,
                max_concurrency=1
            )

            if not result.success:
                error_msg = getattr(result, 'error', '未知错误')
                raise MetadataError(f"创建指标字段失败: {error_msg}")

            # 获取插入的ID（使用主键字段 column_id）
            inserted_records = await self._aselect(
                table=MetadataTableNames.MD_INDEX_COLUMNS,
                where={
                    'knowledge_id': column_data['knowledge_id'],
                    'table_id': column_data['table_id'],
                    'column_name': column_data['column_name']
                },
                limit=1
            )

            column_id = inserted_records[0].get('column_id', 0) if inserted_records else 0

            # 处理向量化
            vector_results = []
            if self.vdb_client and self.embedding_client and column_id:
                try:
                    vector_results = await self._create_vectors('index_column', column_id, column_data)
                except Exception as e:
                    logger.warning(f"指标字段向量化失败: {e}")

            logger.info(f"指标字段创建成功: {column_data['column_name']} (ID: {column_id})")
            return column_id, vector_results

        except (MetadataValidationError, MetadataConflictError):
            raise
        except Exception as e:
            logger.error(f"创建指标字段失败: {e}")
            raise MetadataError(f"创建指标字段失败: {e}")

    async def get_index_column(self, column_id: int = None, **where_conditions) -> Optional[Dict[str, Any]]:
        """
        获取指标字段

        Args:
            column_id: 字段ID（可选）
            **where_conditions: 其他查询条件

        Returns:
            字段信息字典，如果不存在则返回None

        Raises:
            MetadataValidationError: 查询条件无效
        """
        if column_id:
            where = {'column_id': column_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 column_id 或其他查询条件")

        results = await self._aselect(
            table=MetadataTableNames.MD_INDEX_COLUMNS,
            where=where,
            limit=1
        )
        return results[0] if results else None

    async def update_index_column(self, column_data: Dict[str, Any], column_id: int = None, **where_conditions) -> bool:
        """
        更新指标字段

        Args:
            column_data: 更新的字段数据
            column_id: 字段ID（可选）
            **where_conditions: 其他更新条件

        Returns:
            更新是否成功

        Raises:
            MetadataValidationError: 更新条件无效
            MetadataError: 更新失败
        """
        if column_id:
            where = {'column_id': column_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 column_id 或其他更新条件")

        # 添加更新时间
        MetadataUtils.add_timestamps(column_data, is_update=True)

        try:
            # 使用批量更新（单条，优化参数）
            updates = [{"data": column_data, "filters": where}]
            result = await self.rdb_client.abatch_update(
                table=MetadataTableNames.MD_INDEX_COLUMNS,
                updates=updates,
                batch_size=1,
                max_concurrency=1
            )

            if result.success and result.affected_rows > 0:
                # 更新向量
                if self.vdb_client and self.embedding_client and column_id:
                    try:
                        # 获取完整的实体数据
                        full_data = await self.get_index_column(column_id)
                        if full_data:
                            await self._update_vectors('index_column', column_id, column_data, full_data)
                    except Exception as e:
                        logger.warning(f"指标字段向量更新失败: {e}")
                return True
            return False

        except Exception as e:
            logger.error(f"更新指标字段失败: {e}")
            raise MetadataError(f"更新指标字段失败: {e}")

    async def delete_index_column(self, column_id: int = None, **where_conditions) -> bool:
        """
        删除指标字段

        Args:
            column_id: 字段ID（可选）
            **where_conditions: 其他删除条件

        Returns:
            删除是否成功

        Raises:
            MetadataValidationError: 删除条件无效
            MetadataError: 删除失败
        """
        if column_id:
            where = {'column_id': column_id}
        elif where_conditions:
            where = where_conditions
        else:
            raise MetadataValidationError("必须提供 column_id 或其他删除条件")

        try:
            result = await self.rdb_client.abatch_delete(
                table=MetadataTableNames.MD_INDEX_COLUMNS,
                conditions=[where],
                batch_size=1,
                max_concurrency=1
            )

            return result.success and result.affected_rows > 0

        except Exception as e:
            logger.error(f"删除指标字段失败: {e}")
            raise MetadataError(f"删除指标字段失败: {e}")

    async def list_index_columns(
        self,
        knowledge_id: Optional[str] = None,
        table_id: Optional[int] = None,
        index_type: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        **filters
    ) -> List[Dict[str, Any]]:
        """
        查询指标字段列表

        Args:
            knowledge_id: 知识库ID（可选）
            table_id: 表ID（可选）
            index_type: 指标类型（可选）
            limit: 限制数量（可选）
            offset: 偏移量（可选）
            **filters: 其他过滤条件

        Returns:
            字段信息列表
        """
        where = MetadataUtils.build_search_filters(
            knowledge_id=knowledge_id,
            table_id=table_id,
            index_type=index_type,
            **filters
        )

        return await self._aselect(
            table=MetadataTableNames.MD_INDEX_COLUMNS,
            where=where if where else None,
            order_by=['create_time DESC'],
            limit=limit,
            offset=offset
        )

    # ==========================================
    # 搜索功能
    # ==========================================

    async def search_source_databases(self, search_term: str, knowledge_id: Optional[str] = None,
                                     limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        搜索源数据库

        Args:
            search_term: 搜索词
            knowledge_id: 知识库ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            where_conditions = {}

            if knowledge_id:
                where_conditions['knowledge_id'] = knowledge_id

            # 简化搜索：按数据库名称和描述搜索
            if search_term:
                # 这里可以扩展为更复杂的搜索逻辑
                where_conditions['db_name'] = f"%{search_term}%"

            return await self._aselect(
                table=MetadataTableNames.MD_SOURCE_DATABASE,
                where=where_conditions,
                order_by=['create_time DESC'],
                limit=limit,
                offset=offset
            )

        except Exception as e:
            logger.error(f"搜索源数据库失败: {e}")
            return []

    async def search_source_tables(self, search_term: str, knowledge_id: Optional[str] = None,
                                  db_id: Optional[int] = None,
                                  limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        搜索源表

        Args:
            search_term: 搜索词
            knowledge_id: 知识库ID
            db_id: 数据库ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            where_conditions = {}

            if knowledge_id:
                where_conditions['knowledge_id'] = knowledge_id
            if db_id:
                where_conditions['db_id'] = db_id

            # 简化搜索：按表名称搜索
            if search_term:
                where_conditions['table_name'] = f"%{search_term}%"

            return await self._aselect(
                table=MetadataTableNames.MD_SOURCE_TABLES,
                where=where_conditions,
                order_by=['create_time DESC'],
                limit=limit,
                offset=offset
            )

        except Exception as e:
            logger.error(f"搜索源表失败: {e}")
            return []

    async def search_source_columns(self, search_term: str, knowledge_id: Optional[str] = None,
                                   table_id: Optional[int] = None,
                                   limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        搜索源字段

        Args:
            search_term: 搜索词
            knowledge_id: 知识库ID
            table_id: 表ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            where_conditions = {}

            if knowledge_id:
                where_conditions['knowledge_id'] = knowledge_id
            if table_id:
                where_conditions['table_id'] = table_id

            # 简化搜索：按字段名称搜索
            if search_term:
                where_conditions['column_name'] = f"%{search_term}%"

            return await self._aselect(
                table=MetadataTableNames.MD_SOURCE_COLUMNS,
                where=where_conditions,
                order_by=['create_time DESC'],
                limit=limit,
                offset=offset
            )

        except Exception as e:
            logger.error(f"搜索源字段失败: {e}")
            return []

    # ==================== 新增批量操作方法 ====================

    async def batch_create_source_tables(self, tables_data: List[Dict[str, Any]]) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """
        批量创建源表

        Args:
            tables_data: 表数据列表，每个表数据必须包含：
                - knowledge_id: 知识库ID
                - db_id: 数据库ID
                - table_name: 表名
                - table_name_cn: 表中文名（可选）
                - table_desc: 表描述（可选）
                - is_active: 是否激活（可选，默认1）

        Returns:
            (表ID列表, 向量创建结果列表)

        Raises:
            MetadataValidationError: 数据验证失败
            MetadataError: 创建失败
        """
        if not tables_data:
            return [], []

        try:
            # 数据验证和预处理
            processed_data = []
            for table_data in tables_data:
                MetadataUtils.validate_source_table_data(table_data)
                MetadataUtils.add_timestamps(table_data)
                processed_data.append(table_data)

            # 批量插入（使用较小的批次大小以减少数据错误的影响）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_SOURCE_TABLES,
                data=processed_data,
                batch_size=500,  # 使用较小的批次大小
                max_concurrency=5
            )

            if not result.success:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', '未知错误'))
                raise MetadataError(f"批量创建源表失败: {error_msg}")

            # 优化：使用abatch_query高性能并行查询获取所有插入的ID（真正的批量查询）
            table_ids = []
            if processed_data:
                try:
                    # 分批处理以优化并行性能 - 每批处理表
                    outer_batch_size = 500  # 每批处理500个表的ID查询

                    for i in range(0, len(processed_data), outer_batch_size):
                        batch_data = processed_data[i:i + outer_batch_size]
                        
                        # 使用高性能abatch_query进行真正的并行批量查询
                        # 构建并行查询列表
                        queries = []
                        for table_data in batch_data:
                            query = {
                                "data": ["table_id", "knowledge_id", "db_id", "table_name"],  # 查询需要的所有字段
                                "filters": {
                                    'knowledge_id': table_data['knowledge_id'],
                                    'db_id': table_data['db_id'],
                                    'table_name': table_data['table_name']
                                },
                                "limit": 1
                            }
                            queries.append(query)
                        
                        # 执行并行批量查询
                        query_responses = await self.rdb_client.abatch_query(
                            table=MetadataTableNames.MD_SOURCE_TABLES,
                            queries=queries,
                            batch_size=500,  # 每个子批次100个查询
                            max_concurrency=5  # 最大并发数
                        )
                        
                        # 提取结果
                        batch_results = []
                        for response in query_responses:
                            if response.data:
                                batch_results.extend(response.data)
                        
                        logger.debug(f"使用abatch_query查询表ID: 批次={len(batch_data)}, 查询数={len(queries)}, 结果={len(batch_results)}")
                        
                        # 建立映射以保持顺序
                        id_map = {}
                        for record in batch_results:
                            key = (record['knowledge_id'], record['db_id'], record['table_name'])
                            id_map[key] = record.get('table_id', 0)
                        
                        # 按原始顺序获取ID
                        for table_data in batch_data:
                            key = (table_data['knowledge_id'], table_data['db_id'], table_data['table_name'])
                            table_ids.append(id_map.get(key, 0))
                        
                        logger.debug(f"批量查询表ID完成: 批次大小={len(batch_data)}, 找到ID数={len([id for id in id_map.values() if id > 0])}")
                            
                except Exception as e:
                    logger.warning(f"批量查询插入ID失败，回退到逐个查询: {e}")
                    # 回退到逐个查询方式（保持向后兼容）
                    table_ids = []
                    for table_data in processed_data:
                        inserted_records = await self._aselect(
                            table=MetadataTableNames.MD_SOURCE_TABLES,
                            where={
                                'knowledge_id': table_data['knowledge_id'],
                                'db_id': table_data['db_id'],
                                'table_name': table_data['table_name']
                            },
                            limit=1
                        )
                        if inserted_records:
                            table_ids.append(inserted_records[0].get('table_id', 0))
                        else:
                            table_ids.append(0)

            # 处理向量化
            all_vector_results = []
            if self.vdb_client and self.embedding_client:
                for i, table_data in enumerate(processed_data):
                    table_id = table_ids[i]
                    if table_id:
                        try:
                            vector_results = await self._create_vectors('source_table', table_id, table_data)
                            all_vector_results.append(vector_results)
                        except Exception as e:
                            logger.warning(f"源表向量化失败: {e}")
                            all_vector_results.append([])
                    else:
                        all_vector_results.append([])

            logger.info(f"批量创建源表成功: {len(table_ids)} 个表")
            return table_ids, all_vector_results

        except Exception as e:
            logger.error(f"批量创建源表失败: {e}")
            raise MetadataError(f"批量创建源表失败: {e}")

    async def batch_create_source_columns(self, columns_data: List[Dict[str, Any]]) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """
        批量创建源字段

        Args:
            columns_data: 字段数据列表，每个字段数据必须包含：
                - knowledge_id: 知识库ID
                - table_id: 表ID
                - column_name: 字段名
                - column_name_cn: 字段中文名（可选）
                - column_desc: 字段描述（可选）
                - data_type: 数据类型（可选）
                - data_example: 数据样例（可选）
                - is_vectorized: 是否已向量化（可选，默认0）
                - is_primary_key: 是否主键（可选，默认0）
                - is_sensitive: 是否敏感数据（可选，默认0）

        Returns:
            (字段ID列表, 向量创建结果列表)

        Raises:
            MetadataValidationError: 数据验证失败
            MetadataError: 创建失败
        """
        if not columns_data:
            return [], []

        try:
            # 数据验证和预处理
            processed_data = []
            for column_data in columns_data:
                MetadataUtils.validate_source_column_data(column_data)
                MetadataUtils.add_timestamps(column_data)
                processed_data.append(column_data)

            # 批量插入（使用较小的批次大小以减少字段长度错误的影响）
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_SOURCE_COLUMNS,
                data=processed_data,
                batch_size=500,  # 使用较小的批次大小
                max_concurrency=5
            )

            if not result.success:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', '未知错误'))
                raise MetadataError(f"批量创建源字段失败: {error_msg}")

            # 优化：使用abatch_query高性能并行查询获取所有插入的ID（真正的批量查询）
            column_ids = []
            if processed_data:
                try:
                    # 分批处理以优化并行性能 - 每批处理字段
                    outer_batch_size = 500  # 每批处理250个字段的ID查询
                    
                    for i in range(0, len(processed_data), outer_batch_size):
                        batch_data = processed_data[i:i + outer_batch_size]
                        
                        # 使用高性能abatch_query进行真正的并行批量查询
                        # 构建并行查询列表
                        queries = []
                        for column_data in batch_data:
                            query = {
                                "data": ["column_id", "knowledge_id", "table_id", "column_name"],  # 查询需要的所有字段
                                "filters": {
                                    'knowledge_id': column_data['knowledge_id'],
                                    'table_id': column_data['table_id'],
                                    'column_name': column_data['column_name']
                                },
                                "limit": 1
                            }
                            queries.append(query)
                        
                        # 执行并行批量查询
                        query_responses = await self.rdb_client.abatch_query(
                            table=MetadataTableNames.MD_SOURCE_COLUMNS,
                            queries=queries,
                            batch_size=500,  # 每个子批次50个查询
                            max_concurrency=5  # 最大并发数
                        )
                        
                        # 提取结果
                        batch_results = []
                        for response in query_responses:
                            if response.data:
                                batch_results.extend(response.data)
                        
                        logger.debug(f"使用abatch_query查询字段ID: 批次={len(batch_data)}, 查询数={len(queries)}, 结果={len(batch_results)}")
                        
                        # 建立映射以保持顺序
                        id_map = {}
                        for record in batch_results:
                            key = (record['knowledge_id'], record['table_id'], record['column_name'])
                            id_map[key] = record.get('column_id', 0)
                        
                        # 按原始顺序获取ID
                        for column_data in batch_data:
                            key = (column_data['knowledge_id'], column_data['table_id'], column_data['column_name'])
                            column_ids.append(id_map.get(key, 0))
                        
                        logger.debug(f"批量查询字段ID完成: 批次大小={len(batch_data)}, 找到ID数={len([id for id in id_map.values() if id > 0])}")
                            
                except Exception as e:
                    logger.warning(f"批量查询插入ID失败，回退到逐个查询: {e}")
                    # 回退到逐个查询方式（保持向后兼容）
                    column_ids = []
                    for column_data in processed_data:
                        inserted_records = await self._aselect(
                            table=MetadataTableNames.MD_SOURCE_COLUMNS,
                            where={
                                'knowledge_id': column_data['knowledge_id'],
                                'table_id': column_data['table_id'],
                                'column_name': column_data['column_name']
                            },
                            limit=1
                        )
                        if inserted_records:
                            column_ids.append(inserted_records[0].get('column_id', 0))
                        else:
                            column_ids.append(0)

            # 处理向量化
            all_vector_results = []
            if self.vdb_client and self.embedding_client:
                for i, column_data in enumerate(processed_data):
                    column_id = column_ids[i]
                    if column_id:
                        try:
                            vector_results = await self._create_vectors('source_column', column_id, column_data)
                            all_vector_results.append(vector_results)
                        except Exception as e:
                            logger.warning(f"源字段向量化失败: {e}")
                            all_vector_results.append([])
                    else:
                        all_vector_results.append([])

            logger.info(f"批量创建源字段成功: {len(column_ids)} 个字段，其中成功 {sum(1 for id in column_ids if id > 0)} 个")
            return column_ids, all_vector_results

        except Exception as e:
            logger.error(f"批量创建源字段失败: {e}")
            raise MetadataError(f"批量创建源字段失败: {e}")

    async def batch_create_index_tables(self, tables_data: List[Dict[str, Any]]) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """
        批量创建指标表

        Args:
            tables_data: 表数据列表

        Returns:
            (表ID列表, 向量创建结果列表)
        """
        if not tables_data:
            return [], []

        try:
            # 数据验证和预处理
            processed_data = []
            for table_data in tables_data:
                MetadataUtils.validate_index_table_data(table_data)
                MetadataUtils.add_timestamps(table_data)
                processed_data.append(table_data)

            # 批量插入
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_INDEX_TABLES,
                data=processed_data
            )

            if not result.success:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', '未知错误'))
                raise MetadataError(f"批量创建指标表失败: {error_msg}")

            # 获取插入的ID
            table_ids = []
            if processed_data:
                try:
                    where_conditions = []
                    for table_data in processed_data:
                        where_conditions.append({
                            'knowledge_id': table_data['knowledge_id'],
                            'db_id': table_data['db_id'], 
                            'table_name': table_data['table_name']
                        })
                    
                    batch_results = await self.rdb_client.abatch_query(
                        table=MetadataTableNames.MD_INDEX_TABLES,
                        queries=[{"filters": where_cond, "limit": 1} for where_cond in where_conditions]
                    )
                    
                    for query_result in batch_results:
                        if query_result.data:
                            table_ids.append(query_result.data[0].get('table_id', 0))
                        else:
                            table_ids.append(0)
                            
                except Exception as e:
                    logger.warning(f"批量查询插入ID失败: {e}")
                    for table_data in processed_data:
                        inserted_records = await self._aselect(
                            table=MetadataTableNames.MD_INDEX_TABLES,
                            where={
                                'knowledge_id': table_data['knowledge_id'],
                                'db_id': table_data['db_id'],
                                'table_name': table_data['table_name']
                            },
                            limit=1
                        )
                        table_ids.append(inserted_records[0].get('table_id', 0) if inserted_records else 0)

            # 处理向量化
            all_vector_results = []
            if self.vdb_client and self.embedding_client:
                for i, table_data in enumerate(processed_data):
                    table_id = table_ids[i]
                    if table_id:
                        try:
                            vector_results = await self._create_vectors('index_table', table_id, table_data)
                            all_vector_results.append(vector_results)
                        except Exception as e:
                            logger.warning(f"指标表向量化失败: {e}")
                            all_vector_results.append([])
                    else:
                        all_vector_results.append([])

            logger.info(f"批量创建指标表成功: {len(table_ids)} 个表")
            return table_ids, all_vector_results

        except Exception as e:
            logger.error(f"批量创建指标表失败: {e}")
            raise MetadataError(f"批量创建指标表失败: {e}")

    async def batch_create_index_columns(self, columns_data: List[Dict[str, Any]]) -> Tuple[List[int], List[List[Dict[str, Any]]]]:
        """
        批量创建指标字段

        Args:
            columns_data: 字段数据列表

        Returns:
            (字段ID列表, 向量创建结果列表)
        """
        if not columns_data:
            return [], []

        try:
            # 数据验证和预处理
            processed_data = []
            for column_data in columns_data:
                MetadataUtils.validate_index_column_data(column_data)
                MetadataUtils.add_timestamps(column_data)
                processed_data.append(column_data)

            # 批量插入
            result = await self.rdb_client.abatch_insert(
                table=MetadataTableNames.MD_INDEX_COLUMNS,
                data=processed_data
            )

            if not result.success:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', '未知错误'))
                raise MetadataError(f"批量创建指标字段失败: {error_msg}")

            # 获取插入的ID
            column_ids = []
            if processed_data:
                try:
                    where_conditions = []
                    for column_data in processed_data:
                        where_conditions.append({
                            'knowledge_id': column_data['knowledge_id'],
                            'table_id': column_data['table_id'], 
                            'column_name': column_data['column_name']
                        })
                    
                    batch_results = await self.rdb_client.abatch_query(
                        table=MetadataTableNames.MD_INDEX_COLUMNS,
                        queries=[{"filters": where_cond, "limit": 1} for where_cond in where_conditions]
                    )
                    
                    for query_result in batch_results:
                        if query_result.data:
                            column_ids.append(query_result.data[0].get('column_id', 0))
                        else:
                            column_ids.append(0)
                            
                except Exception as e:
                    logger.warning(f"批量查询插入ID失败: {e}")
                    for column_data in processed_data:
                        inserted_records = await self._aselect(
                            table=MetadataTableNames.MD_INDEX_COLUMNS,
                            where={
                                'knowledge_id': column_data['knowledge_id'],
                                'table_id': column_data['table_id'],
                                'column_name': column_data['column_name']
                            },
                            limit=1
                        )
                        column_ids.append(inserted_records[0].get('column_id', 0) if inserted_records else 0)

            # 处理向量化
            all_vector_results = []
            if self.vdb_client and self.embedding_client:
                for i, column_data in enumerate(processed_data):
                    column_id = column_ids[i]
                    if column_id:
                        try:
                            vector_results = await self._create_vectors('index_column', column_id, column_data)
                            all_vector_results.append(vector_results)
                        except Exception as e:
                            logger.warning(f"指标字段向量化失败: {e}")
                            all_vector_results.append([])
                    else:
                        all_vector_results.append([])

            logger.info(f"批量创建指标字段成功: {len(column_ids)} 个字段")
            return column_ids, all_vector_results

        except Exception as e:
            logger.error(f"批量创建指标字段失败: {e}")
            raise MetadataError(f"批量创建指标字段失败: {e}")

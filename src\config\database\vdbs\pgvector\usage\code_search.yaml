# @package usage.code_search
# 码值向量搜索配置

table_name: "md_code_embeddings"

search_schema:
  vector_field: "embedding"
  topk: 50
  metric_type: "cosine"
  output_fields:
    - "id"
    - "knowledge_id"
    - "code_set_id"
    - "code_value_id"
    - "content_type"
    - "create_time"
  expr: ""
  partition_name: ""

query_schema:
  topk: 50
  expr: ""
  partition_name: ""
  output_fields:
    - "id"
    - "knowledge_id"
    - "code_set_id"
    - "code_value_id"
    - "content_type"
    - "create_time"

# 码值搜索特定配置
code_search_config:
  # 支持的内容类型
  supported_content_types:
    - "code"      # 码值本身
    - "text"      # 显示文本
  
  # 默认搜索过滤条件
  default_filters:
    # 默认搜索所有内容类型
    content_types: ["code", "text"]
    # 启用全局搜索（不强制指定字段）
    global_search_enabled: true
    # 字段范围搜索变为可选
    column_scope_enabled: true
    column_scope_required: false
    # 可以按码值集ID过滤
    code_set_scope_enabled: true
  
  # 搜索结果排序
  result_ordering:
    # 主要排序：相似度得分（降序）
    primary: "similarity_score DESC"
    # 次要排序：码值集ID（升序）
    secondary: "code_set_id ASC"
    # 第三排序：码值ID（升序）
    tertiary: "code_value_id ASC"
  
  # 码值集级精确定位优化
  code_set_scope_optimization:
    # 启用码值集级范围搜索
    enabled: true
    # 当指定码值集ID时，优先在该码值集范围内搜索
    prefer_code_set_scope: true
    # 支持全局搜索
    support_global_search: true
  
  # SQL条件矫正配置
  sql_correction_config:
    # 启用SQL条件矫正功能
    enabled: true
    # 相似度阈值，超过此阈值才进行矫正建议
    similarity_threshold: 0.7
    # 最大建议数量
    max_suggestions: 5

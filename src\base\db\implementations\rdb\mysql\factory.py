"""
MySQL数据库客户端工厂

基于Universal架构设计的MySQL专用工厂
支持字典配置和便捷创建方法，提供Hydra集成
"""

from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

from .client import MySQLClient
from .config import MySQLConnectionConfig


# ==================== 主要创建方法 ====================

def create_mysql_client_from_dict(config_dict: Dict[str, Any]) -> MySQLClient:
    """
    从字典创建MySQL客户端（推荐方式）

    Args:
        config_dict: 配置字典，支持所有MySQL连接参数

    Returns:
        MySQLClient实例

    Example:
        >>> config = {
        ...     "host": "localhost",
        ...     "port": 3306,
        ...     "database": "mydb",
        ...     "username": "user",
        ...     "password": "pass",
        ...     "pool_size": 10,
        ...     "charset": "utf8mb4"
        ... }
        >>> client = create_mysql_client_from_dict(config)
        >>> result = client.execute("SELECT 1")
    """
    config = MySQLConnectionConfig.from_dict(config_dict)
    client = MySQLClient(config)

    logger.info(f"Created MySQL client from dict config for {config.host}:{config.port}")
    return client


# ==================== 便捷的创建方法 ====================

def create_mysql_client(
    host: str,
    database: str,
    username: str,
    password: str,
    port: int = 3306,
    charset: str = "utf8mb4",
    **options
) -> MySQLClient:
    """
    创建MySQL客户端（便捷方法）

    Args:
        host: MySQL主机
        database: 数据库名
        username: 用户名
        password: 密码
        port: 端口号（默认3306）
        charset: 字符集（默认utf8mb4）
        **options: 其他配置选项

    Returns:
        MySQLClient实例

    Example:
        >>> client = create_mysql_client(
        ...     "localhost", "mydb", "user", "pass"
        ... )
        >>> result = client.execute("SELECT 1")
    """
    config_dict = {
        "host": host,
        "database": database,
        "username": username,
        "password": password,
        "port": port,
        "charset": charset,
        **options
    }

    # 添加MySQL特定选项
    if 'mysql_options' not in config_dict:
        config_dict['mysql_options'] = {}

    config_dict['mysql_options'].update({
        'charset': charset,
        'use_unicode': True
    })

    return create_mysql_client_from_dict(config_dict)


def create_mysql_client_from_url(
    database_url: str,
    **options
) -> MySQLClient:
    """
    从数据库URL创建MySQL客户端

    Args:
        database_url: MySQL数据库URL
        **options: 其他配置选项

    Returns:
        MySQLClient实例

    Example:
        >>> client = create_mysql_client_from_url(
        ...     "mysql://user:pass@localhost:3306/mydb"
        ... )
        >>> result = client.execute("SELECT 1")
    """
    config_dict = {
        "database_url": database_url,
        **options
    }

    return create_mysql_client_from_dict(config_dict)


# ==================== 高级创建方法 ====================

def create_mysql_client_with_pool(
    host: str,
    database: str,
    username: str,
    password: str,
    port: int = 3306,
    pool_size: int = 10,
    max_overflow: int = 20,
    pool_timeout: float = 30.0,
    **options
) -> MySQLClient:
    """
    创建带连接池配置的MySQL客户端

    Args:
        host: MySQL主机
        database: 数据库名
        username: 用户名
        password: 密码
        port: 端口号
        pool_size: 连接池大小
        max_overflow: 最大溢出连接数
        pool_timeout: 连接池超时时间
        **options: 其他配置选项

    Returns:
        MySQLClient实例
    """
    config_dict = {
        "host": host,
        "database": database,
        "username": username,
        "password": password,
        "port": port,
        "pool_size": pool_size,
        "max_overflow": max_overflow,
        "pool_timeout": pool_timeout,
        **options
    }

    return create_mysql_client_from_dict(config_dict)


def create_mysql_client_with_ssl(
    host: str,
    database: str,
    username: str,
    password: str,
    ssl_cert: str,
    ssl_key: str,
    ssl_ca: str,
    port: int = 3306,
    **options
) -> MySQLClient:
    """
    创建带SSL配置的MySQL客户端

    Args:
        host: MySQL主机
        database: 数据库名
        username: 用户名
        password: 密码
        ssl_cert: SSL证书文件路径
        ssl_key: SSL私钥文件路径
        ssl_ca: SSL CA文件路径
        port: 端口号
        **options: 其他配置选项

    Returns:
        MySQLClient实例
    """
    config_dict = {
        "host": host,
        "database": database,
        "username": username,
        "password": password,
        "port": port,
        "ssl_enabled": True,
        "ssl_cert": ssl_cert,
        "ssl_key": ssl_key,
        "ssl_ca": ssl_ca,
        **options
    }

    return create_mysql_client_from_dict(config_dict)


# ==================== 配置辅助函数 ====================

def create_mysql_config_from_dict(config_dict: Dict[str, Any]) -> MySQLConnectionConfig:
    """
    从字典创建MySQL配置（便捷方法）

    Args:
        config_dict: 配置字典

    Returns:
        MySQLConnectionConfig实例

    Example:
        >>> config = create_mysql_config_from_dict({
        ...     "host": "localhost",
        ...     "database": "mydb",
        ...     "username": "user",
        ...     "password": "pass"
        ... })
    """
    return MySQLConnectionConfig.from_dict(config_dict)


def get_mysql_default_config() -> Dict[str, Any]:
    """
    获取MySQL默认配置

    Returns:
        默认配置字典
    """
    return {
        "port": 3306,
        "charset": "utf8mb4",
        "use_unicode": True,
        "autocommit": False,
        "pool_size": 10,
        "max_overflow": 20,
        "pool_timeout": 30.0,
        "pool_recycle": 3600,
        "pool_pre_ping": True,
        "enable_cache": True,
        "cache_size": 1000,
        "max_retries": 3,
        "retry_delay": 1.0,
        "echo": False
    }


def validate_mysql_config(config_dict: Dict[str, Any]) -> bool:
    """
    验证MySQL配置

    Args:
        config_dict: 配置字典

    Returns:
        配置是否有效

    Raises:
        ValueError: 配置无效时抛出
    """
    try:
        MySQLConnectionConfig.from_dict(config_dict)
        return True
    except Exception as e:
        raise ValueError(f"Invalid MySQL configuration: {e}")


# ==================== Hydra集成函数 ====================

def create_mysql_client_hydra(
    host: str,
    port: int,
    database: str,
    username: str,
    password: str = "",
    **kwargs
) -> MySQLClient:
    """
    Hydra兼容的MySQL客户端创建函数

    这是一个Hydra兼容的工厂函数，用于通过配置文件创建客户端。

    Args:
        host: 数据库主机地址
        port: 数据库端口
        database: 数据库名称
        username: 用户名
        password: 密码
        **kwargs: 其他配置参数

    Returns:
        MySQLClient实例

    Examples:
        >>> # 通过Hydra配置创建
        >>> client = hydra.utils.instantiate(config)

        >>> # 直接调用创建
        >>> client = create_mysql_client_hydra(
        ...     host="localhost",
        ...     port=3306,
        ...     database="mydb",
        ...     username="user",
        ...     password="password"
        ... )
    """
    try:
        config_dict = {
            "host": host,
            "port": port,
            "database": database,
            "username": username,
            "password": password,
            **kwargs
        }

        logger.info(f"创建MySQL客户端: {host}:{port}/{database}")

        client = create_mysql_client_from_dict(config_dict)

        logger.debug(f"MySQL客户端创建成功: {type(client).__name__}")
        return client

    except Exception as e:
        logger.error(f"创建MySQL客户端失败: {e}")
        raise


# ==================== 便捷别名 ====================

# 主要创建方法的别名
create_client = create_mysql_client_from_dict
create_client_from_dict = create_mysql_client_from_dict
create_client_from_url = create_mysql_client_from_url

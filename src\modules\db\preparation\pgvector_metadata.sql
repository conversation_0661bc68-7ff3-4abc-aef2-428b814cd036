-- ==========================================
-- 元数据领域（Metadata）- PostgreSQL + pgvector 向量数据库
-- ==========================================
-- 
-- 说明：
-- 1. 本脚本专门用于创建元数据（Metadata）相关的向量表。
-- 2. 包含表级、列级、码值级的向量表定义。
-- 
-- 创建时间：2025-07-04
-- ==========================================

-- 启用pgvector扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- ==========================================
-- 1. 元数据表级向量搜索表 (Metadata Table-Level Embeddings)
-- ==========================================
DROP TABLE IF EXISTS md_table_embeddings CASCADE;
CREATE TABLE md_table_embeddings (
    id BIGSERIAL,
    embedding VECTOR(768) NOT NULL,
    
    -- 必要的关联键
    knowledge_id VARCHAR(255) NOT NULL,
    source_type VARCHAR(20) NOT NULL,
    db_id BIGINT NOT NULL,
    table_id BIGINT NOT NULL,
    
    -- 向量化内容标识
    content_type VARCHAR(50) NOT NULL,
    
    create_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    update_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    PRIMARY KEY (knowledge_id, source_type, db_id, id)
) PARTITION BY HASH (knowledge_id, source_type, db_id);

-- 注释
COMMENT ON TABLE md_table_embeddings IS '元数据表级向量搜索表，存储表名、中文名、描述的向量化信息';
-- ... (omitting other comments for brevity)

-- 分区
DO $$
BEGIN
    FOR i IN 0..127 LOOP
        EXECUTE format('CREATE TABLE md_table_embeddings_part_%s PARTITION OF md_table_embeddings FOR VALUES WITH (modulus 128, remainder %s)',
                      lpad(i::text, 3, '0'), i);
    END LOOP;
END $$;

-- 索引
CREATE INDEX idx_md_table_embeddings_hnsw ON md_table_embeddings USING HNSW (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 200);
CREATE INDEX idx_md_table_embeddings_table_id ON md_table_embeddings (table_id);
CREATE UNIQUE INDEX idx_md_table_embeddings_unique ON md_table_embeddings (knowledge_id, source_type, db_id, table_id, content_type);


-- ==========================================
-- 2. 元数据列级向量搜索表 (Metadata Column-Level Embeddings)
-- ==========================================
DROP TABLE IF EXISTS md_column_embeddings CASCADE;
CREATE TABLE md_column_embeddings (
    id BIGSERIAL,
    embedding VECTOR(768) NOT NULL,
    
    knowledge_id VARCHAR(255) NOT NULL,
    source_type VARCHAR(20) NOT NULL,
    table_id BIGINT NOT NULL,
    column_id BIGINT NOT NULL,
    
    content_type VARCHAR(50) NOT NULL,

    create_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    update_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    PRIMARY KEY (knowledge_id, source_type, table_id, id)
) PARTITION BY HASH (knowledge_id, source_type, table_id);

-- 分区
DO $$
BEGIN
    FOR i IN 0..127 LOOP
        EXECUTE format('CREATE TABLE md_column_embeddings_part_%s PARTITION OF md_column_embeddings FOR VALUES WITH (modulus 128, remainder %s)',
                      lpad(i::text, 3, '0'), i);
    END LOOP;
END $$;

-- 索引
CREATE INDEX idx_md_column_embeddings_hnsw ON md_column_embeddings USING HNSW (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 200);
CREATE INDEX idx_md_column_embeddings_ids ON md_column_embeddings (table_id, column_id);
CREATE UNIQUE INDEX idx_md_column_embeddings_unique ON md_column_embeddings (knowledge_id, source_type, table_id, column_id, content_type);


-- ==========================================
-- 3. 元数据码值向量搜索表 (Metadata Code Value Embeddings)
-- 重新设计：以码值集为核心，完全解绑字段依赖
-- ==========================================
DROP TABLE IF EXISTS md_code_embeddings CASCADE;
CREATE TABLE md_code_embeddings (
    id BIGSERIAL,
    embedding VECTOR(768) NOT NULL,

    -- 核心关联键：以码值集为中心
    knowledge_id VARCHAR(255) NOT NULL,
    code_set_id BIGINT NOT NULL,
    code_value_id BIGINT NOT NULL,

    -- 向量化内容标识
    content_type VARCHAR(20) NOT NULL,  -- 'code' 或 'text'

    create_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    update_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    PRIMARY KEY (knowledge_id, code_set_id, id)
) PARTITION BY HASH (knowledge_id, code_set_id);

-- 分区：按知识库和码值集分区
DO $$
BEGIN
    FOR i IN 0..127 LOOP
        EXECUTE format('CREATE TABLE md_code_embeddings_part_%s PARTITION OF md_code_embeddings FOR VALUES WITH (modulus 128, remainder %s)',
                      lpad(i::text, 3, '0'), i);
    END LOOP;
END $$;

-- 索引优化：以码值集为核心，简洁高效
CREATE INDEX idx_md_code_embeddings_hnsw ON md_code_embeddings USING HNSW (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 200);
CREATE INDEX idx_md_code_embeddings_code_set ON md_code_embeddings (code_set_id, code_value_id);
CREATE UNIQUE INDEX idx_md_code_embeddings_unique ON md_code_embeddings (knowledge_id, code_set_id, code_value_id, content_type);


-- ==========================================
-- 查询视图
-- ==========================================

CREATE OR REPLACE VIEW v_md_table_search AS
SELECT
    te.id,
    te.embedding,
    te.knowledge_id,
    te.source_type,
    te.table_id,
    te.content_type
FROM md_table_embeddings te;

CREATE OR REPLACE VIEW v_md_column_search AS
SELECT
    ce.id,
    ce.embedding,
    ce.knowledge_id,
    ce.source_type,
    ce.table_id,
    ce.column_id,
    ce.content_type
FROM md_column_embeddings ce;

-- 码值搜索视图：简洁的向量搜索视图（不依赖关系型数据库表）
CREATE OR REPLACE VIEW v_md_code_search AS
SELECT
    ce.id,
    ce.embedding,
    ce.knowledge_id,
    ce.code_set_id,
    ce.code_value_id,
    ce.content_type,
    ce.create_time,
    ce.update_time
FROM md_code_embeddings ce;

-- ==========================================
-- 通用函数和视图
-- ==========================================
CREATE OR REPLACE VIEW v_partition_stats AS
SELECT
    schemaname,
    tablename as table_name,
    COUNT(*) as partition_count,
    'HASH(knowledge_id, source_type, entity_id)' as partition_strategy,
    'Enterprise Standard: 128 partitions for scalability' as design_rationale
FROM pg_tables
WHERE tablename LIKE 'md_%_embeddings_part_%'
GROUP BY schemaname, tablename;

CREATE OR REPLACE FUNCTION get_partition_info(table_name text)
RETURNS TABLE(
    partition_name text,
    partition_expression text,
    row_count bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        schemaname||'.'||tablename as partition_name,
        pg_get_expr(c.relpartbound, c.oid) as partition_expression,
        pg_class.reltuples::bigint as row_count
    FROM pg_inherits
    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
    JOIN pg_class c ON pg_inherits.inhrelid = c.oid
    JOIN pg_tables ON pg_tables.tablename = c.relname
    WHERE parent.relname = table_name
    ORDER BY c.relname;
END;
$$ LANGUAGE plpgsql; 
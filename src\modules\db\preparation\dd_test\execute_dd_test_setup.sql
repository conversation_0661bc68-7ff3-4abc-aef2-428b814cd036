-- ==========================================
-- DD测试环境完整部署脚本 (VARCHAR兼容版)
-- ==========================================
--
-- 说明：
-- 1. 创建DD系统所需的所有表结构（使用VARCHAR类型编码字段）
-- 2. 插入基础测试数据（VARCHAR兼容格式）
-- 3. 为DD批量部门职责分配系统提供完整的测试环境
-- 4. 兼容新的VARCHAR字段类型（已移除ENUM约束）
--
-- 执行顺序：
-- 1. 创建表结构（VARCHAR类型编码字段）
-- 2. 插入基础报表数据
-- 3. 插入部门数据
-- 4. 插入部门关系数据
-- 5. 准备批量测试数据插入（VARCHAR兼容）
--
-- 重要变更：
-- - 所有编码字段从ENUM类型改为VARCHAR类型
-- - 测试数据与新表结构完全兼容
-- - 保持原有的业务逻辑和功能
--
-- 创建时间：2024-01-15
-- 版本：v2.0.0 (VARCHAR兼容版)
-- ==========================================

-- ==========================================
-- 1. 创建DD系统表结构
-- ==========================================
-- 注意：新表结构使用VARCHAR类型替代ENUM类型，提供更大灵活性



-- ==========================================
-- 2. 插入基础报表数据 (dd_report_data)
-- ==========================================

INSERT INTO dd_report_data (
    knowledge_id, version, report_name, report_code, report_layer,
    report_department, report_type, `set`, is_manual
) VALUES
('58b452bc-24ca-46b2-89fb-cce1d68068c6', 'v1.0', '客户信息管理报表', 'G0107', 'ADS', '零售银行部', 'detail', 'SET_A', 0),
('58b452bc-24ca-46b2-89fb-cce1d68068c6', 'v1.0', '风险指标统计报表', 'G0108', 'BDM', '风险控制部', 'index', 'SET_B', 0),
('58b452bc-24ca-46b2-89fb-cce1d68068c6', 'v1.0', 'IT系统监控报表', 'G0109', 'IDM', 'IT技术部', 'detail', 'SET_C', 1);

-- ==========================================
-- 3. 插入部门数据 (dd_departments)
-- ==========================================

INSERT INTO dd_departments (dept_id, dept_name, dept_desc, dept_type, is_active) VALUES
-- 测试部门（修复字段名称和数据类型）
('TEST_RETAIL', '零售银行部', '负责个人客户业务管理和服务', 'normal', 1),
('TEST_CORPORATE', '公司银行部', '负责企业客户业务管理和服务', 'normal', 1),
('TEST_TREASURY', '资金财务部', '负责资金管理和财务核算', 'normal', 1),
('TEST_RISK_CTRL', '风险控制部', '负责风险识别、评估和控制', 'normal', 1),
('TEST_COMPLIANCE', '合规管理部', '负责合规管理和监督', 'mandatory', 1),
('TEST_OPERATIONS', '运营管理部', '负责业务运营和流程管理', 'normal', 1),
('TEST_IT', 'IT技术部', '负责信息技术系统建设和维护', 'normal', 1),
('TEST_DATA_MGT', '数据管理部', '负责数据治理和管理', 'management', 1),
('TEST_DBA', '数据库管理部', '负责数据库系统管理和维护', 'normal', 1),
('TEST_CUSTOMER_SVC', '客户服务部', '负责客户服务和支持', 'normal', 1),
('TEST_MARKETING', '市场营销部', '负责市场营销和推广', 'normal', 1),
('TEST_RRMS', '风险管理部', '负责监管报送和风险管理', 'mandatory', 1),
('TEST_SECURITY', '信息安全部', '负责信息安全管理和防护', 'normal', 1),
('TEST_AUDIT', '内部审计部', '负责内部审计和监督', 'normal', 1),
('TEST_LEGAL', '法律合规部', '负责法律事务和合规管理', 'mandatory', 1);

-- ==========================================
-- 4. 插入部门关系数据 (dd_departments_relation)
-- ==========================================

INSERT INTO dd_departments_relation (dept_id, table_id) VALUES
-- 表ID=16 (客户信息) - 多部门候选
('TEST_RETAIL', 16),
('TEST_CORPORATE', 16),
('TEST_CUSTOMER_SVC', 16),

-- 表ID=2 (交易记录) - 风险财务相关
('TEST_TREASURY', 2),
('TEST_RRMS', 2),
('TEST_RISK_CTRL', 2),

-- 表ID=31 (用户档案) - 运营服务相关
('TEST_OPERATIONS', 31),
('TEST_RETAIL', 31),
('TEST_CUSTOMER_SVC', 31),

-- 表ID=4 (风险汇总) - 风险合规相关
('TEST_RISK_CTRL', 4),
('TEST_RRMS', 4),
('TEST_COMPLIANCE', 4),

-- 表ID=3 (系统日志) - IT数据相关
('TEST_IT', 3),
('TEST_DATA_MGT', 3),
('TEST_DBA', 3),

-- 表ID=33 (API测试) - IT技术专属
('TEST_IT', 33),

-- 表ID=35 (客户资产) - 财务营销相关
('TEST_TREASURY', 35),
('TEST_MARKETING', 35),

-- 表ID=36 (渠道管理) - 运营相关
('TEST_OPERATIONS', 36),

-- 表ID=38 (贷款信息) - 财务合规相关
('TEST_TREASURY', 38),
('TEST_COMPLIANCE', 38),

-- 表ID=43 (基础配置) - IT数据相关
('TEST_IT', 43),
('TEST_DATA_MGT', 43);

-- ==========================================
-- 5. 验证基础数据
-- ==========================================

-- 验证报表数据
SELECT 'dd_report_data验证' as table_name, COUNT(*) as record_count FROM dd_report_data;

-- 验证部门数据  
SELECT 'dd_departments验证' as table_name, COUNT(*) as record_count FROM dd_departments;

-- 验证部门关系数据
SELECT 'dd_departments_relation验证' as table_name, COUNT(*) as record_count FROM dd_departments_relation;

-- 显示部门关系分布
SELECT table_id, COUNT(*) as dept_count 
FROM dd_departments_relation 
GROUP BY table_id 
ORDER BY table_id;

-- ==========================================
-- 6. 准备批量测试数据插入
-- ==========================================

SELECT '基础环境部署完成' as status, 
       '请继续执行批量测试数据插入脚本' as next_step,
       NOW() as completion_time;

-- 下一步执行：
-- SOURCE insert_dd_submission_data_bulk.sql;
-- SOURCE insert_dd_submission_data_bulk_extension.sql;


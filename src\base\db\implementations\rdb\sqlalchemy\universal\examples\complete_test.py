#!/usr/bin/env python3
"""
Universal SQLAlchemy客户端完整测试

测试所有功能：
1. 工厂方法创建客户端
2. 字典输入自动转换
3. 按需引擎初始化
4. 所有CRUD操作（同步/异步）
5. 事务管理
6. 连接管理
7. 健康检查
8. 性能测试
9. 安全测试
10. 多实例隔离测试
11. 缓存冲突测试
12. 错误处理测试
"""

import sys
import os
import time
import asyncio
import threading
import socket
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any
import logging

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '../../../../../../..')
sys.path.insert(0, os.path.abspath(project_root))

# 配置日志
logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_database_availability(host: str, port: int, timeout: float = 1.0) -> bool:
    """检查数据库服务是否可用"""
    try:
        with socket.create_connection((host, port), timeout=timeout):
            return True
    except (socket.error, socket.timeout, OSError):
        return False


def test_factory_methods():
    """测试1: 工厂方法创建客户端"""
    print("🧪 测试1: 工厂方法创建客户端")
    print("=" * 60)
    
    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import (
            create_mysql_client, create_postgresql_client, create_sqlite_client,
            create_client_from_dict
        )

        # 测试所有工厂方法（使用重构后的API）
        factories = [
            ("MySQL工厂", lambda: create_mysql_client(
                host="**************", port=37615, database="hsbc_data",
                username="root", password="idea@1008"
            )),
            ("PostgreSQL工厂", lambda: create_postgresql_client(
                host="localhost", database="test", username="postgres", password="pass"
            )),
            ("SQLite工厂", lambda: create_sqlite_client(":memory:")),
            ("字典工厂(MySQL)", lambda: create_client_from_dict({
                "dialect": "mysql",
                "host": "**************",
                "port": 37615,
                "database": "hsbc_data",
                "username": "root",
                "password": "idea@1008"
            })),
            ("字典工厂(SQLite)", lambda: create_client_from_dict({
                "dialect": "sqlite",
                "host": "",
                "port": 0,
                "database": ":memory:",
                "username": "",
                "password": ""
            }))
        ]
        
        for name, factory in factories:
            print(f"\n📋 测试{name}")
            client = factory()
            print(f"✅ 客户端创建成功")
            print(f"   - 数据库类型: {client.get_database_type()}")
            print(f"   - 方言: {client.dialect.name}")
            print(f"   - 初始连接状态: {client.is_connected()}")
            
            # 验证所有必需方法存在
            required_methods = [
                'query', 'aquery', 'insert', 'ainsert', 'update', 'aupdate',
                'delete', 'adelete', 'execute', 'aexecute', 'fetch_all', 'afetch_all',
                'fetch_one', 'afetch_one', 'transaction', 'atransaction',
                'connect', 'aconnect', 'disconnect', 'adisconnect',
                'is_connected', 'get_database_type', 'health_check'
            ]
            
            missing_methods = [m for m in required_methods if not hasattr(client, m)]
            if missing_methods:
                print(f"❌ 缺少方法: {missing_methods}")
                return False
            else:
                print(f"✅ 接口完整: {len(required_methods)}个方法")
        
        return True
        
    except Exception as e:
        print(f"❌ 工厂方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dict_input_conversion():
    """测试2: 字典输入自动转换"""
    print("\n🧪 测试2: 字典输入自动转换")
    print("=" * 60)
    
    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_sqlite_client
        
        client = create_sqlite_client(":memory:")
        
        # 创建测试表
        print("\n📋 创建测试表")
        client.execute("""
            CREATE TABLE test_conversion (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                value REAL,
                status TEXT,
                tags TEXT
            )
        """)
        
        # 插入测试数据
        client.execute("""
            INSERT INTO test_conversion (name, value, status, tags) VALUES
            ('item1', 100.0, 'active', 'tag1,tag2'),
            ('item2', 200.0, 'inactive', 'tag2,tag3'),
            ('item3', 150.0, 'pending', 'tag1,tag3')
        """)
        print("✅ 测试表创建成功")
        
        # 测试字典查询转换
        print("\n📋 测试字典查询转换")
        
        # 简单字典过滤器
        simple_query = {
            "table": "test_conversion",
            "columns": ["id", "name", "value"],
            "filters": {"status": "active"},
            "limit": 10
        }
        result = client.query(simple_query)
        print(f"✅ 简单字典查询: 找到 {len(result.data)} 条记录")
        print(f"   SQL: {result.query_sql}")
        if result.data:
            print(f"   结果示例: {result.data[0]}")

        # 智能LIKE检测
        like_query = {
            "table": "test_conversion",
            "filters": {"name": "item%"}  # 应该自动转换为LIKE
        }
        result = client.query(like_query)
        print(f"✅ 智能LIKE检测: 找到 {len(result.data)} 条记录")
        print(f"   SQL: {result.query_sql}")
        if result.data:
            print(f"   结果示例: {result.data[0]}")

        # 复杂过滤器
        complex_query = {
            "table": "test_conversion",
            "filters": {
                "operator": "AND",
                "conditions": [
                    {"field": "value", "op": "GT", "value": 120.0},
                    {"field": "status", "op": "IN", "value": ["active", "pending"]}
                ]
            }
        }
        result = client.query(complex_query)
        print(f"✅ 复杂过滤器: 找到 {len(result.data)} 条记录")
        print(f"   SQL: {result.query_sql}")
        if result.data:
            print(f"   结果示例: {result.data[0]}")
        
        # 测试字典插入转换
        print("\n📋 测试字典插入转换")
        insert_dict = {
            "table": "test_conversion",
            "data": {"name": "new_item", "value": 300.0, "status": "active"}
        }
        result = client.insert(insert_dict)
        print(f"✅ 字典插入: 影响 {result.affected_rows} 行")
        
        # 测试字典更新转换
        print("\n📋 测试字典更新转换")
        update_dict = {
            "table": "test_conversion",
            "data": {"value": 350.0},
            "filters": {"name": "new_item"}
        }
        result = client.update(update_dict)
        print(f"✅ 字典更新: 影响 {result.affected_rows} 行")
        
        # 测试字典删除转换
        print("\n📋 测试字典删除转换")
        delete_dict = {
            "table": "test_conversion",
            "filters": {"name": "new_item"}
        }
        result = client.delete(delete_dict)
        print(f"✅ 字典删除: 影响 {result.affected_rows} 行")
        
        return True
        
    except Exception as e:
        print(f"❌ 字典转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_lazy_initialization():
    """测试3: 按需引擎初始化"""
    print("\n🧪 测试3: 按需引擎初始化")
    print("=" * 60)
    
    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_sqlite_client
        
        # 创建客户端但不连接
        client = create_sqlite_client(":memory:")
        
        print(f"📋 初始状态检查")
        print(f"✅ 同步引擎: {client.sync_engine is not None}")
        print(f"✅ 异步引擎: {client.async_engine is not None}")
        print(f"✅ 连接状态: {client.is_connected()}")
        
        # 第一次同步操作应该自动初始化同步引擎
        print(f"\n📋 触发同步引擎初始化")
        client.execute("CREATE TABLE test_lazy (id INTEGER PRIMARY KEY)")
        print(f"✅ 同步引擎: {client.sync_engine is not None}")
        print(f"✅ 异步引擎: {client.async_engine is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ 按需初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_async_operations():
    """测试4: 异步操作"""
    print("\n🧪 测试4: 异步操作")
    print("=" * 60)
    
    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client
        
        # 使用真实数据库测试异步操作
        client = create_mysql_client(
            host="**************", port=37615, database="hsbc_data",
            username="root", password="idea@1008"
        )
        
        print(f"📋 初始异步状态: {client.async_engine is not None}")
        
        # 创建测试表
        await client.aexecute("""
            CREATE TABLE IF NOT EXISTS test_async (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100),
                value DECIMAL(10,2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ 异步创建表成功")
        
        # 清理旧数据
        await client.aexecute("DELETE FROM test_async WHERE name LIKE 'async_test_%'")
        
        # 异步插入
        insert_result = await client.ainsert({
            "table": "test_async",
            "data": {"name": "async_test_1", "value": 99.99}
        })
        print(f"✅ 异步插入: 影响 {insert_result.affected_rows} 行")
        
        # 异步查询
        query_result = await client.aquery({
            "table": "test_async",
            "filters": {"name": "async_test_%"},
            "limit": 10
        })
        print(f"✅ 异步查询: 找到 {len(query_result.data)} 条记录")
        print(f"   SQL: {query_result.query_sql}")
        if query_result.data:
            print("   查询结果:")
            for i, row in enumerate(query_result.data):
                print(f"     {i+1}. {dict(row)}")

        # 测试异步fetch方法
        print(f"\n📋 测试异步fetch方法")

        # 异步fetch_all
        afetch_all_result = await client.afetch_all("SELECT name, value FROM test_async WHERE name LIKE 'async_test_%' ORDER BY value DESC")
        print(f"✅ afetch_all方法: 获取 {len(afetch_all_result.data)} 条记录")
        print(f"   SQL: {afetch_all_result.query_sql}")
        if afetch_all_result.data:
            print("   获取结果:")
            for i, row in enumerate(afetch_all_result.data):
                print(f"     {i+1}. {dict(row)}")

        # 异步fetch_one
        afetch_one_result = await client.afetch_one("SELECT name, value FROM test_async WHERE name = 'async_test_1'")
        print(f"✅ afetch_one方法: 获取 {'1' if afetch_one_result else '0'} 条记录")
        print(f"   SQL: SELECT name, value FROM test_async WHERE name = 'async_test_1'")
        if afetch_one_result:
            print(f"   获取结果: {afetch_one_result}")
        
        # 异步更新
        update_result = await client.aupdate({
            "table": "test_async",
            "data": {"value": 199.99},
            "filters": {"name": "async_test_1"}
        })
        print(f"✅ 异步更新: 影响 {update_result.affected_rows} 行")
        
        # 异步删除
        delete_result = await client.adelete({
            "table": "test_async",
            "filters": {"name": "async_test_1"}
        })
        print(f"✅ 异步删除: 影响 {delete_result.affected_rows} 行")
        
        # 异步事务
        print(f"\n📋 测试异步事务")
        async with client.atransaction() as conn:
            from sqlalchemy import text
            await conn.execute(text(
                "INSERT INTO test_async (name, value) VALUES (:name, :value)"
            ), {"name": "async_trans", "value": 500.0})
            print("✅ 异步事务执行成功")
        
        # 清理
        await client.aexecute("DELETE FROM test_async WHERE name LIKE 'async_%'")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_connection_management():
    """测试5: 连接管理"""
    print("\n🧪 测试5: 连接管理")
    print("=" * 60)
    
    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client
        
        client = create_mysql_client(
            host="**************", port=37615, database="hsbc_data",
            username="root", password="idea@1008"
        )
        
        # 测试连接状态
        print(f"📋 初始连接状态: {client.is_connected()}")
        
        # 手动连接
        client.connect()
        print(f"✅ 手动连接后: {client.is_connected()}")
        
        # 健康检查
        health = client.health_check()
        print(f"✅ 健康检查: {health['status']}")
        if 'response_time' in health:
            print(f"   响应时间: {health['response_time']:.4f}秒")
        
        # 测试连接池信息
        if 'connection_pool' in health:
            pool_info = health['connection_pool']
            print(f"   连接池大小: {pool_info.get('size', 'N/A')}")
            print(f"   已检入连接: {pool_info.get('checked_in', 'N/A')}")
            print(f"   已检出连接: {pool_info.get('checked_out', 'N/A')}")
        
        # 断开连接
        client.disconnect()
        print(f"✅ 断开连接后: {client.is_connected()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_all_crud_operations():
    """测试6: 完整CRUD操作"""
    print("\n🧪 测试6: 完整CRUD操作")
    print("=" * 60)

    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client

        client = create_mysql_client(
            host="**************", port=37615, database="hsbc_data",
            username="root", password="idea@1008"
        )

        # 创建测试表
        print("\n📋 创建测试表")
        client.execute("""
            CREATE TABLE IF NOT EXISTS test_crud (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                value DECIMAL(10,2),
                status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
                tags JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY uk_name (name),
                INDEX idx_status (status)
            )
        """)

        # 清理旧数据
        client.execute("DELETE FROM test_crud WHERE name LIKE 'crud_test_%'")
        print("✅ 测试表准备完成")

        # 测试INSERT操作
        print("\n📋 测试INSERT操作")

        # 单条插入
        single_insert = {
            "table": "test_crud",
            "data": {
                "name": "crud_test_single",
                "value": 100.50,
                "status": "active",
                "tags": '{"type": "test", "priority": "high"}'
            }
        }
        result = client.insert(single_insert)
        print(f"✅ 单条插入: 影响 {result.affected_rows} 行")

        # 批量插入
        batch_data = []
        for i in range(5):
            batch_data.append({
                "name": f"crud_test_batch_{i}",
                "value": 200.0 + i * 10,
                "status": ["active", "inactive", "pending"][i % 3],
                "tags": f'{{"batch": {i}, "type": "batch_test"}}'
            })

        batch_insert = {
            "table": "test_crud",
            "data": batch_data
        }
        result = client.insert(batch_insert)
        print(f"✅ 批量插入: 影响 {result.affected_rows} 行")

        # 测试SELECT操作
        print("\n📋 测试SELECT操作")

        # 基本查询
        basic_query = {
            "table": "test_crud",
            "columns": ["id", "name", "value", "status"],
            "filters": {"name": "crud_test_%"},
            "sorts": [{"field": "value", "order": "desc"}],
            "limit": 10
        }
        result = client.query(basic_query)
        print(f"✅ 基本查询: 找到 {len(result.data)} 条记录")
        print(f"   SQL: {result.query_sql}")
        if result.data:
            print("   查询结果:")
            for i, row in enumerate(result.data[:3]):  # 显示前3条
                print(f"     {i+1}. {dict(row)}")
            if len(result.data) > 3:
                print(f"     ... 还有 {len(result.data)-3} 条记录")

        # 复杂查询
        complex_query = {
            "table": "test_crud",
            "filters": {
                "operator": "AND",
                "conditions": [
                    {"field": "value", "op": "BETWEEN", "value": [200.0, 250.0]},
                    {"field": "status", "op": "IN", "value": ["active", "pending"]},
                    {"field": "name", "op": "LIKE", "value": "crud_test_batch_%"}
                ]
            }
        }
        result = client.query(complex_query)
        print(f"✅ 复杂查询: 找到 {len(result.data)} 条记录")
        print(f"   SQL: {result.query_sql}")
        if result.data:
            print("   查询结果:")
            for i, row in enumerate(result.data):
                print(f"     {i+1}. {dict(row)}")

        # 测试UPDATE操作
        print("\n📋 测试UPDATE操作")

        # 单条更新
        single_update = {
            "table": "test_crud",
            "data": {"value": 999.99, "status": "inactive"},
            "filters": {"name": "crud_test_single"}
        }
        result = client.update(single_update)
        print(f"✅ 单条更新: 影响 {result.affected_rows} 行")

        # 批量更新
        batch_update = {
            "table": "test_crud",
            "data": {"status": "pending"},
            "filters": {
                "operator": "AND",
                "conditions": [
                    {"field": "value", "op": "LT", "value": 230.0},
                    {"field": "name", "op": "LIKE", "value": "crud_test_batch_%"}
                ]
            }
        }
        result = client.update(batch_update)
        print(f"✅ 批量更新: 影响 {result.affected_rows} 行")

        # 测试DELETE操作
        print("\n📋 测试DELETE操作")

        # 条件删除
        delete_request = {
            "table": "test_crud",
            "filters": {"status": "inactive"}
        }
        result = client.delete(delete_request)
        print(f"✅ 条件删除: 影响 {result.affected_rows} 行")

        # 测试execute和fetch方法
        print("\n📋 测试execute和fetch方法")

        # 测试execute方法
        execute_result = client.execute("SELECT COUNT(*) as total FROM test_crud WHERE name LIKE 'crud_test_%'")
        print(f"✅ execute方法: 影响 {execute_result.affected_rows} 行")
        print(f"   SQL: {execute_result.operation_sql}")

        # 测试fetch_all方法
        fetch_all_result = client.fetch_all("SELECT name, value, status FROM test_crud WHERE name LIKE 'crud_test_%' ORDER BY value DESC LIMIT 5")
        print(f"✅ fetch_all方法: 获取 {len(fetch_all_result.data)} 条记录")
        print(f"   SQL: {fetch_all_result.query_sql}")
        if fetch_all_result.data:
            print("   获取结果:")
            for i, row in enumerate(fetch_all_result.data):
                print(f"     {i+1}. {dict(row)}")

        # 测试fetch_one方法
        fetch_one_result = client.fetch_one("SELECT name, value FROM test_crud WHERE name LIKE 'crud_test_%' ORDER BY value DESC LIMIT 1")
        print(f"✅ fetch_one方法: 获取 {'1' if fetch_one_result else '0'} 条记录")
        print(f"   SQL: SELECT name, value FROM test_crud WHERE name LIKE 'crud_test_%' ORDER BY value DESC LIMIT 1")
        if fetch_one_result:
            print(f"   获取结果: {fetch_one_result}")

        # 清理剩余数据
        delete_result = client.execute("DELETE FROM test_crud WHERE name LIKE 'crud_test_%'")
        print(f"✅ 清理完成: 删除 {delete_result.affected_rows} 行")

        return True

    except Exception as e:
        print(f"❌ CRUD操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_transaction_management():
    """测试7: 事务管理"""
    print("\n🧪 测试7: 事务管理")
    print("=" * 60)

    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client

        client = create_mysql_client(
            host="**************", port=37615, database="hsbc_data",
            username="root", password="idea@1008"
        )

        # 创建测试表
        client.execute("""
            CREATE TABLE IF NOT EXISTS test_transaction (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100),
                value INT
            )
        """)

        # 清理旧数据
        client.execute("DELETE FROM test_transaction WHERE name LIKE 'trans_test_%'")

        # 测试成功事务
        print("\n📋 测试成功事务")
        with client.transaction() as conn:
            from sqlalchemy import text
            conn.execute(text("INSERT INTO test_transaction (name, value) VALUES (:name, :value)"),
                        {"name": "trans_test_1", "value": 100})
            conn.execute(text("INSERT INTO test_transaction (name, value) VALUES (:name, :value)"),
                        {"name": "trans_test_2", "value": 200})

        # 验证事务提交
        result = client.query({
            "table": "test_transaction",
            "filters": {"name": "trans_test_%"}
        })
        print(f"✅ 成功事务: 插入 {len(result.data)} 条记录")
        if result.data:
            print("   事务提交结果:")
            for i, row in enumerate(result.data):
                print(f"     {i+1}. {dict(row)}")

        # 测试回滚事务
        print("\n📋 测试回滚事务")
        try:
            with client.transaction() as conn:
                from sqlalchemy import text
                conn.execute(text("INSERT INTO test_transaction (name, value) VALUES (:name, :value)"),
                            {"name": "trans_test_rollback", "value": 300})
                # 故意引发错误
                raise Exception("测试回滚")
        except Exception as e:
            # 检查是否是我们期望的回滚异常（可能被包装在TransactionError中）
            if "测试回滚" in str(e) or "Transaction failed" in str(e):
                print("✅ 事务回滚触发成功")
                print(f"   异常类型: {type(e).__name__}")
                print(f"   异常信息: {str(e)}")
            else:
                # 如果不是期望的异常，重新抛出
                raise

        # 验证回滚
        result = client.query({
            "table": "test_transaction",
            "filters": {"name": "trans_test_rollback"}
        })
        print(f"✅ 回滚验证: 找到 {len(result.data)} 条记录 (应该为0)")

        # 清理
        client.execute("DELETE FROM test_transaction WHERE name LIKE 'trans_test_%'")

        return True

    except Exception as e:
        print(f"❌ 事务管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance():
    """测试8: 性能测试"""
    print("\n🧪 测试8: 性能测试")
    print("=" * 60)

    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client

        client = create_mysql_client(
            host="**************", port=37615, database="hsbc_data",
            username="root", password="idea@1008"
        )

        # 创建测试表
        client.execute("""
            CREATE TABLE IF NOT EXISTS test_performance (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100),
                value DECIMAL(10,2),
                data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_name (name)
            )
        """)

        # 清理旧数据
        client.execute("DELETE FROM test_performance WHERE name LIKE 'perf_test_%'")

        # 批量插入性能测试
        print("\n📋 批量插入性能测试")
        batch_size = 100
        batch_data = []
        for i in range(batch_size):
            batch_data.append({
                "name": f"perf_test_{i:04d}",
                "value": 100.0 + i,
                "data": f"测试数据_{i}" * 10  # 增加数据量
            })

        start_time = time.time()
        result = client.insert({
            "table": "test_performance",
            "data": batch_data
        })
        insert_time = time.time() - start_time

        print(f"✅ 批量插入: {batch_size}条记录，耗时 {insert_time:.4f}秒")
        print(f"   平均每条: {insert_time/batch_size*1000:.2f}毫秒")
        print(f"   吞吐量: {batch_size/insert_time:.0f}条/秒")

        # 查询性能测试
        print("\n📋 查询性能测试")

        # 简单查询
        start_time = time.time()
        result = client.query({
            "table": "test_performance",
            "filters": {"name": "perf_test_%"},
            "limit": 50
        })
        query_time = time.time() - start_time
        print(f"✅ 简单查询: 找到 {len(result.data)} 条记录，耗时 {query_time:.4f}秒")

        # 复杂查询
        start_time = time.time()
        result = client.query({
            "table": "test_performance",
            "filters": {
                "operator": "AND",
                "conditions": [
                    {"field": "value", "op": "BETWEEN", "value": [150.0, 180.0]},
                    {"field": "name", "op": "LIKE", "value": "perf_test_%"}
                ]
            },
            "sorts": [{"field": "value", "order": "desc"}]
        })
        complex_query_time = time.time() - start_time
        print(f"✅ 复杂查询: 找到 {len(result.data)} 条记录，耗时 {complex_query_time:.4f}秒")

        # 更新性能测试
        print("\n📋 更新性能测试")
        start_time = time.time()
        result = client.update({
            "table": "test_performance",
            "data": {"value": 999.99},
            "filters": {
                "operator": "AND",
                "conditions": [
                    {"field": "value", "op": "LT", "value": 120.0}
                ]
            }
        })
        update_time = time.time() - start_time
        print(f"✅ 批量更新: 影响 {result.affected_rows} 行，耗时 {update_time:.4f}秒")

        # 清理
        start_time = time.time()
        client.execute("DELETE FROM test_performance WHERE name LIKE 'perf_test_%'")
        delete_time = time.time() - start_time
        print(f"✅ 批量删除: 耗时 {delete_time:.4f}秒")

        return True

    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multi_instance_isolation():
    """测试9: 多实例隔离测试"""
    print("\n🧪 测试9: 多实例隔离测试")
    print("=" * 60)

    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client

        # 创建多个客户端实例
        clients = []
        for i in range(3):
            client = create_mysql_client(
                host="**************", port=37615, database="hsbc_data",
                username="root", password="idea@1008"
            )
            clients.append(client)

        print(f"📋 创建了 {len(clients)} 个客户端实例")

        # 测试实例隔离
        print("\n📋 测试实例隔离")

        # 每个实例执行不同操作
        for i, client in enumerate(clients):
            # 创建独立的测试表
            table_name = f"test_isolation_{i}"
            client.execute(f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    instance_id INT,
                    data VARCHAR(100)
                )
            """)

            # 插入实例特定数据
            client.insert({
                "table": table_name,
                "data": {"instance_id": i, "data": f"instance_{i}_data"}
            })

            print(f"✅ 实例 {i}: 创建表 {table_name} 并插入数据")

        # 验证实例间不互相干扰
        print("\n📋 验证实例间隔离")
        for i, client in enumerate(clients):
            table_name = f"test_isolation_{i}"
            result = client.query({
                "table": table_name,
                "filters": {"instance_id": i}
            })
            print(f"✅ 实例 {i}: 查询到 {len(result.data)} 条记录")

            # 验证其他实例的表不会被意外访问
            for j in range(len(clients)):
                if i != j:
                    other_table = f"test_isolation_{j}"
                    try:
                        # 这应该能正常工作，因为表确实存在
                        other_result = client.query({"table": other_table})
                        print(f"   实例 {i} 可以访问实例 {j} 的表: {len(other_result.data)} 条记录")
                    except Exception:
                        print(f"   实例 {i} 无法访问实例 {j} 的表 (正常)")

        # 测试并发操作
        print("\n📋 测试并发操作")

        def concurrent_operation(client_index):
            client = clients[client_index]
            table_name = f"test_isolation_{client_index}"

            # 并发插入
            for i in range(10):
                client.insert({
                    "table": table_name,
                    "data": {
                        "instance_id": client_index,
                        "data": f"concurrent_{client_index}_{i}"
                    }
                })

            # 并发查询
            result = client.query({
                "table": table_name,
                "filters": {"instance_id": client_index}
            })

            return len(result.data)

        # 使用线程池执行并发操作
        with ThreadPoolExecutor(max_workers=len(clients)) as executor:
            futures = [
                executor.submit(concurrent_operation, i)
                for i in range(len(clients))
            ]

            results = []
            for future in as_completed(futures):
                results.append(future.result())

        print(f"✅ 并发操作完成: {results}")

        # 清理测试表
        for i in range(len(clients)):
            table_name = f"test_isolation_{i}"
            clients[i].execute(f"DROP TABLE IF EXISTS {table_name}")
            print(f"✅ 清理表 {table_name}")

        return True

    except Exception as e:
        print(f"❌ 多实例隔离测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_error_handling():
    """测试10: 错误处理测试"""
    print("\n🧪 测试10: 错误处理测试")
    print("=" * 60)

    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client

        client = create_mysql_client(
            host="**************", port=37615, database="hsbc_data",
            username="root", password="idea@1008"
        )

        # 测试SQL语法错误
        print("\n📋 测试SQL语法错误")
        try:
            client.execute("INVALID SQL SYNTAX")
            print("❌ 应该抛出异常")
            return False
        except Exception as e:
            print(f"✅ 正确捕获SQL语法错误: {type(e).__name__}")

        # 测试表不存在错误
        print("\n📋 测试表不存在错误")
        try:
            client.query({"table": "non_existent_table_12345"})
            print("❌ 应该抛出异常")
            return False
        except Exception as e:
            print(f"✅ 正确捕获表不存在错误: {type(e).__name__}")

        # 测试字段不存在错误
        print("\n📋 测试字段不存在错误")
        try:
            # 先创建一个测试表
            client.execute("""
                CREATE TABLE IF NOT EXISTS test_error_handling (
                    id INT PRIMARY KEY,
                    name VARCHAR(100)
                )
            """)

            # 查询不存在的字段
            client.query({
                "table": "test_error_handling",
                "columns": ["non_existent_column"]
            })
            print("❌ 应该抛出异常")
            return False
        except Exception as e:
            print(f"✅ 正确捕获字段不存在错误: {type(e).__name__}")

        # 测试连接错误
        print("\n📋 测试连接错误")
        try:
            bad_client = create_mysql_client(
                host="invalid_host_12345", port=3306, database="test",
                username="user", password="pass"
            )
            bad_client.execute("SELECT 1")
            print("❌ 应该抛出连接异常")
            return False
        except Exception as e:
            print(f"✅ 正确捕获连接错误: {type(e).__name__}")

        # 清理
        client.execute("DROP TABLE IF EXISTS test_error_handling")

        return True

    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 Universal SQLAlchemy客户端完整测试开始")
    print("=" * 80)

    # 检查数据库连接
    print("📡 检查数据库连接...")
    mysql_available = check_database_availability("**************", 37615, timeout=3.0)
    print(f"MySQL数据库: {'✅ 可用' if mysql_available else '❌ 不可用'}")

    if not mysql_available:
        print("⚠️  MySQL数据库不可用，某些测试将被跳过")

    # 测试列表
    tests = [
        ("工厂方法创建客户端", test_factory_methods),
        ("字典输入自动转换", test_dict_input_conversion),
        ("按需引擎初始化", test_lazy_initialization),
        ("异步操作", test_async_operations),
        ("连接管理", test_connection_management),
        ("完整CRUD操作", test_all_crud_operations),
        ("事务管理", test_transaction_management),
        ("性能测试", test_performance),
        ("多实例隔离测试", test_multi_instance_isolation),
        ("错误处理测试", test_error_handling),
    ]

    # 执行测试
    results = {}
    total_tests = len(tests)
    passed_tests = 0

    for i, (test_name, test_func) in enumerate(tests, 1):
        print(f"\n{'='*80}")
        print(f"执行测试 {i}/{total_tests}: {test_name}")
        print(f"{'='*80}")

        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()

            results[test_name] = result
            if result:
                passed_tests += 1
                print(f"✅ 测试 {i} 通过: {test_name}")
            else:
                print(f"❌ 测试 {i} 失败: {test_name}")

        except Exception as e:
            results[test_name] = False
            print(f"❌ 测试 {i} 异常: {test_name}")
            print(f"   错误: {e}")
            import traceback
            traceback.print_exc()

    # 输出测试总结
    print(f"\n{'='*80}")
    print("🏁 测试总结")
    print(f"{'='*80}")
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")

    print(f"\n📊 详细结果:")
    for i, (test_name, result) in enumerate(results.items(), 1):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i:2d}. {status} - {test_name}")

    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！Universal SQLAlchemy客户端功能完整且稳定。")
        return True
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    import asyncio

    # 运行测试
    success = asyncio.run(main())

    # 设置退出码
    exit_code = 0 if success else 1
    print(f"\n程序退出，退出码: {exit_code}")
    exit(exit_code)

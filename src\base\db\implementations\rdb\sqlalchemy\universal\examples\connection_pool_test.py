#!/usr/bin/env python3
"""
连接池管理测试

测试内容：
1. 连接池自动释放
2. 多实例连接池隔离
3. 手动vs自动关闭策略
4. 连接池泄漏检测
5. 异常情况下的资源清理
6. 连接池状态监控
7. 垃圾回收时的资源清理
"""

import sys
import os
import time
import asyncio
import gc
import threading
import weakref
from typing import List, Dict, Any
import logging

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '../../../../../../..')
sys.path.insert(0, os.path.abspath(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def get_connection_pool_info(client):
    """获取连接池信息"""
    info = {}
    
    if hasattr(client, 'sync_engine') and client.sync_engine:
        pool = client.sync_engine.pool
        info['sync_pool'] = {
            'size': pool.size(),
            'checked_in': pool.checkedin(),
            'checked_out': pool.checkedout(),
            'overflow': pool.overflow(),
            'invalid': pool.invalid(),
        }
    
    if hasattr(client, 'async_engine') and client.async_engine:
        pool = client.async_engine.pool
        info['async_pool'] = {
            'size': pool.size(),
            'checked_in': pool.checkedin(),
            'checked_out': pool.checkedout(),
            'overflow': pool.overflow(),
            'invalid': pool.invalid(),
        }
    
    return info


def test_manual_disconnect():
    """测试1: 手动断开连接"""
    print("🧪 测试1: 手动断开连接")
    print("=" * 60)
    
    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client
        
        client = create_mysql_client(
            host="**************", port=37615, database="hsbc_data",
            username="root", password="idea@1008"
        )
        
        # 建立连接
        client.connect()
        print(f"✅ 连接建立: {client.is_connected()}")
        
        # 获取连接池信息
        pool_info_before = get_connection_pool_info(client)
        print(f"📊 连接前池状态: {pool_info_before}")
        
        # 执行一些操作
        result = client.execute("SELECT 1 as test")
        print(f"✅ 执行查询成功: {result.affected_rows}")
        
        # 获取使用后的连接池信息
        pool_info_after = get_connection_pool_info(client)
        print(f"📊 使用后池状态: {pool_info_after}")
        
        # 手动断开连接
        client.disconnect()
        print(f"✅ 手动断开连接: {client.is_connected()}")
        
        # 检查连接池是否正确释放
        pool_info_disconnected = get_connection_pool_info(client)
        print(f"📊 断开后池状态: {pool_info_disconnected}")
        
        return True
        
    except Exception as e:
        print(f"❌ 手动断开连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_automatic_cleanup():
    """测试2: 自动清理（析构函数）"""
    print("\n🧪 测试2: 自动清理（析构函数）")
    print("=" * 60)
    
    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client
        
        # 创建弱引用来监控对象销毁
        client_refs = []
        
        def create_and_use_client():
            client = create_mysql_client(
                host="**************", port=37615, database="hsbc_data",
                username="root", password="idea@1008"
            )
            
            # 创建弱引用
            ref = weakref.ref(client)
            client_refs.append(ref)
            
            # 使用客户端
            client.connect()
            result = client.execute("SELECT 1 as test")
            print(f"✅ 客户端使用成功: {result.affected_rows}")
            
            # 获取连接池信息
            pool_info = get_connection_pool_info(client)
            print(f"📊 使用时池状态: {pool_info}")
            
            return client
        
        # 创建客户端
        client = create_and_use_client()
        print(f"✅ 客户端创建完成，引用计数: {len(client_refs)}")
        
        # 删除引用，触发析构函数
        del client
        
        # 强制垃圾回收
        gc.collect()
        time.sleep(0.1)  # 给析构函数一些时间
        
        # 检查弱引用是否已失效
        alive_refs = [ref for ref in client_refs if ref() is not None]
        print(f"✅ 对象销毁检查: {len(alive_refs)} 个对象仍存活（应该为0）")
        
        return len(alive_refs) == 0
        
    except Exception as e:
        print(f"❌ 自动清理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multiple_instances_isolation():
    """测试3: 多实例连接池隔离"""
    print("\n🧪 测试3: 多实例连接池隔离")
    print("=" * 60)
    
    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client
        
        # 创建多个客户端实例
        clients = []
        for i in range(3):
            client = create_mysql_client(
                host="**************", port=37615, database="hsbc_data",
                username="root", password="idea@1008"
            )
            clients.append(client)
            client.connect()
            print(f"✅ 客户端 {i} 连接成功")
        
        # 获取所有连接池信息
        all_pool_info = []
        for i, client in enumerate(clients):
            pool_info = get_connection_pool_info(client)
            all_pool_info.append(pool_info)
            print(f"📊 客户端 {i} 池状态: {pool_info}")
        
        # 执行操作
        for i, client in enumerate(clients):
            result = client.execute(f"SELECT {i+1} as client_id")
            print(f"✅ 客户端 {i} 执行成功: {result.affected_rows}")
        
        # 断开第一个客户端
        clients[0].disconnect()
        print(f"✅ 客户端 0 断开连接")
        
        # 检查其他客户端是否受影响
        for i in range(1, len(clients)):
            try:
                result = clients[i].execute(f"SELECT {i+1} as still_working")
                print(f"✅ 客户端 {i} 仍正常工作: {result.affected_rows}")
            except Exception as e:
                print(f"❌ 客户端 {i} 受到影响: {e}")
                return False
        
        # 清理所有客户端
        for i, client in enumerate(clients[1:], 1):
            client.disconnect()
            print(f"✅ 客户端 {i} 断开连接")
        
        return True
        
    except Exception as e:
        print(f"❌ 多实例隔离测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_async_cleanup():
    """测试4: 异步连接清理"""
    print("\n🧪 测试4: 异步连接清理")
    print("=" * 60)
    
    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client
        
        client = create_mysql_client(
            host="**************", port=37615, database="hsbc_data",
            username="root", password="idea@1008"
        )
        
        # 建立异步连接
        await client.aconnect()
        print(f"✅ 异步连接建立: {client.is_connected()}")
        
        # 获取连接池信息
        pool_info_before = get_connection_pool_info(client)
        print(f"📊 连接前池状态: {pool_info_before}")
        
        # 执行异步操作
        result = await client.aexecute("SELECT 1 as async_test")
        print(f"✅ 异步查询成功: {result.affected_rows}")
        
        # 获取使用后的连接池信息
        pool_info_after = get_connection_pool_info(client)
        print(f"📊 使用后池状态: {pool_info_after}")
        
        # 异步断开连接
        await client.adisconnect()
        print(f"✅ 异步断开连接: {client.is_connected()}")
        
        # 检查连接池是否正确释放
        pool_info_disconnected = get_connection_pool_info(client)
        print(f"📊 断开后池状态: {pool_info_disconnected}")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步清理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_exception_cleanup():
    """测试5: 异常情况下的资源清理"""
    print("\n🧪 测试5: 异常情况下的资源清理")
    print("=" * 60)
    
    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client
        
        client = create_mysql_client(
            host="**************", port=37615, database="hsbc_data",
            username="root", password="idea@1008"
        )
        
        client.connect()
        print(f"✅ 连接建立: {client.is_connected()}")
        
        # 获取连接池信息
        pool_info_before = get_connection_pool_info(client)
        print(f"📊 异常前池状态: {pool_info_before}")
        
        # 故意执行错误的SQL
        try:
            client.execute("INVALID SQL SYNTAX")
        except Exception as e:
            print(f"✅ 预期异常捕获: {type(e).__name__}")
        
        # 检查连接池状态是否正常
        pool_info_after_error = get_connection_pool_info(client)
        print(f"📊 异常后池状态: {pool_info_after_error}")
        
        # 验证连接仍然可用
        result = client.execute("SELECT 1 as recovery_test")
        print(f"✅ 异常后恢复成功: {result.affected_rows}")
        
        # 正常断开连接
        client.disconnect()
        print(f"✅ 正常断开连接: {client.is_connected()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 异常清理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_connection_leak_detection():
    """测试6: 连接泄漏检测"""
    print("\n🧪 测试6: 连接泄漏检测")
    print("=" * 60)
    
    try:
        from base.db.implementations.rdb.sqlalchemy.universal.factory import create_mysql_client
        
        # 记录初始状态
        initial_clients = []
        
        # 创建多个客户端并使用
        for i in range(5):
            client = create_mysql_client(
                host="**************", port=37615, database="hsbc_data",
                username="root", password="idea@1008"
            )
            client.connect()
            
            # 执行查询
            result = client.execute(f"SELECT {i} as iteration")
            print(f"✅ 客户端 {i} 查询成功: {result.affected_rows}")
            
            # 获取连接池信息
            pool_info = get_connection_pool_info(client)
            print(f"📊 客户端 {i} 池状态: {pool_info}")
            
            initial_clients.append(client)
        
        # 手动断开一半连接
        for i in range(0, len(initial_clients), 2):
            initial_clients[i].disconnect()
            print(f"✅ 客户端 {i} 手动断开")
        
        # 删除所有引用，让另一半通过析构函数清理
        del initial_clients
        gc.collect()
        time.sleep(0.2)
        
        print("✅ 连接泄漏检测完成")
        
        # 创建新客户端验证系统状态
        test_client = create_mysql_client(
            host="**************", port=37615, database="hsbc_data",
            username="root", password="idea@1008"
        )
        test_client.connect()
        result = test_client.execute("SELECT 'leak_test' as test")
        print(f"✅ 泄漏检测后新连接正常: {result.affected_rows}")
        
        test_client.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ 连接泄漏检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 连接池管理测试开始")
    print("=" * 80)

    # 测试列表
    tests = [
        ("手动断开连接", test_manual_disconnect),
        ("自动清理（析构函数）", test_automatic_cleanup),
        ("多实例连接池隔离", test_multiple_instances_isolation),
        ("异步连接清理", test_async_cleanup),
        ("异常情况下的资源清理", test_exception_cleanup),
        ("连接泄漏检测", test_connection_leak_detection),
    ]

    # 执行测试
    results = {}
    total_tests = len(tests)
    passed_tests = 0

    for i, (test_name, test_func) in enumerate(tests, 1):
        print(f"\n{'='*80}")
        print(f"执行测试 {i}/{total_tests}: {test_name}")
        print(f"{'='*80}")

        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()

            results[test_name] = result
            if result:
                passed_tests += 1
                print(f"✅ 测试 {i} 通过: {test_name}")
            else:
                print(f"❌ 测试 {i} 失败: {test_name}")

        except Exception as e:
            results[test_name] = False
            print(f"❌ 测试 {i} 异常: {test_name}")
            print(f"   错误: {e}")
            import traceback
            traceback.print_exc()

    # 输出测试总结
    print(f"\n{'='*80}")
    print("🏁 连接池管理测试总结")
    print(f"{'='*80}")
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")

    print(f"\n📊 详细结果:")
    for i, (test_name, result) in enumerate(results.items(), 1):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i:2d}. {status} - {test_name}")

    return passed_tests == total_tests

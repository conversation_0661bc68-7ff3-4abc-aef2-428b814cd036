"""
元数据系统管理API路由

提供元数据系统的管理功能，参照DD系统的设计模式。
"""

from fastapi import APIRouter, Query, Depends
import logging

# 标准化日志导入
logger = logging.getLogger(__name__)

from ..models import MetadataHealthResponse, MetadataOverviewResponse, MetadataStatsResponse
from ..dependencies import get_metadata_crud, validate_knowledge_id

# 创建路由器
router = APIRouter(tags=["元数据系统管理"])


@router.get("/health", response_model=MetadataHealthResponse, summary="元数据系统健康检查")
async def health_check():
    """
    元数据系统健康检查端点
    
    返回系统状态信息，包括：
    - 系统运行状态
    - 服务名称和版本
    - 功能模块列表
    - 数据库连接状态
    """
    return MetadataHealthResponse(
        status="healthy",
        service="元数据管理系统",
        version="2.0.0",
        modules=[
            "数据库管理",
            "表管理",
            "字段管理",
            "码值集管理",
            "数据主题管理",
            "关联关系管理",
            "搜索功能"
        ],
        database_status={
            "mysql": "connected",
            "pgvector": "connected"
        }
    )


@router.get("/overview", response_model=MetadataOverviewResponse, summary="元数据系统API概览")
async def api_overview():
    """
    元数据系统API概览
    
    返回所有可用的API端点信息，包括：
    - API分组信息
    - 端点列表
    - 功能特性
    """
    return MetadataOverviewResponse(
        service="元数据管理系统",
        description="提供元数据的完整管理和搜索功能，包括CRUD操作和智能搜索功能",
        api_groups={
            "数据库管理": {
                "prefix": "/api/knowledge/metadata/databases",
                "endpoints": [
                    "POST / - 创建数据库",
                    "GET /{db_id} - 获取数据库详情",
                    "PUT /{db_id} - 更新数据库信息",
                    "DELETE /{db_id} - 删除数据库",
                    "GET / - 查询数据库列表"
                ],
                "description": "完整的数据库CRUD操作，支持源数据库和指标数据库管理"
            },
            "表管理": {
                "prefix": "/api/knowledge/metadata/tables",
                "endpoints": [
                    "POST / - 创建表",
                    "GET /{table_id} - 获取表详情",
                    "PUT /{table_id} - 更新表信息",
                    "DELETE /{table_id} - 删除表",
                    "GET / - 查询表列表"
                ],
                "description": "完整的表CRUD操作，支持源表和指标表管理"
            },
            "字段管理": {
                "prefix": "/api/knowledge/metadata/columns",
                "endpoints": [
                    "POST / - 创建字段（含向量化）",
                    "GET /{column_id} - 获取字段详情",
                    "PUT /{column_id} - 更新字段信息",
                    "DELETE /{column_id} - 删除字段",
                    "GET / - 查询字段列表"
                ],
                "description": "字段的完整生命周期管理，自动向量化字段名称和描述"
            },
            "码值集管理": {
                "prefix": "/api/knowledge/metadata/code-sets",
                "endpoints": [
                    "POST / - 创建码值集",
                    "GET /{code_set_id} - 获取码值集详情",
                    "PUT /{code_set_id} - 更新码值集信息",
                    "DELETE /{code_set_id} - 删除码值集",
                    "GET / - 查询码值集列表"
                ],
                "description": "码值集和码值的完整管理"
            },
            "数据主题管理": {
                "prefix": "/api/knowledge/metadata/data-subjects",
                "endpoints": [
                    "POST / - 创建数据主题",
                    "GET /{subject_id} - 获取数据主题详情",
                    "PUT /{subject_id} - 更新数据主题信息",
                    "DELETE /{subject_id} - 删除数据主题",
                    "GET / - 查询数据主题列表"
                ],
                "description": "数据主题的完整管理，支持业务主题分类"
            },
            "关联关系管理": {
                "prefix": "/api/knowledge/metadata/relations",
                "endpoints": [
                    "POST / - 创建关联关系",
                    "GET /{relation_id} - 获取关联关系详情",
                    "PUT /{relation_id} - 更新关联关系信息",
                    "DELETE /{relation_id} - 删除关联关系",
                    "GET / - 查询关联关系列表"
                ],
                "description": "实体间关联关系的完整管理"
            },
            "搜索功能": {
                "prefix": "/api/knowledge/metadata/search",
                "endpoints": [
                    "POST /vector - 向量搜索",
                    "POST /hybrid - 混合搜索",
                    "GET /by-name - 按名称搜索",
                    "GET /by-description - 按描述搜索"
                ],
                "description": "智能搜索功能，支持语义搜索和精确搜索"
            }
        },
        features=[
            "自动向量化处理（字段名称、描述）",
            "智能语义搜索",
            "混合搜索（向量+文本）",
            "分页查询支持",
            "多维度过滤",
            "RESTful API设计",
            "完整的OpenAPI文档",
            "统一错误处理",
            "参数验证",
            "依赖注入架构",
            "模块化设计",
            "向后兼容性"
        ]
    )


@router.get("/status", summary="系统状态详情")
async def system_status():
    """
    获取系统详细状态信息
    
    返回更详细的系统运行状态，包括：
    - 各模块状态
    - 数据库连接状态
    - 性能指标
    """
    return {
        "system": {
            "name": "元数据管理系统",
            "version": "2.0.0",
            "status": "running",
            "uptime": "系统运行中"
        },
        "modules": {
            "databases": {"status": "active", "description": "数据库管理模块"},
            "tables": {"status": "active", "description": "表管理模块"},
            "columns": {"status": "active", "description": "字段管理模块"},
            "code_sets": {"status": "active", "description": "码值集管理模块"},
            "data_subjects": {"status": "active", "description": "数据主题管理模块"},
            "relations": {"status": "active", "description": "关联关系管理模块"},
            "search": {"status": "active", "description": "搜索功能模块"}
        },
        "database": {
            "mysql": {"status": "connected", "description": "关系数据库"},
            "pgvector": {"status": "connected", "description": "向量数据库"}
        },
        "api": {
            "total_endpoints": 35,
            "documentation": "/docs",
            "base_url": "/api/knowledge/metadata"
        }
    }


@router.get("/metrics", summary="系统指标")
async def system_metrics():
    """
    获取系统性能指标
    
    返回系统的性能和使用统计信息
    """
    return {
        "api_metrics": {
            "total_requests": "统计中",
            "average_response_time": "统计中",
            "error_rate": "统计中"
        },
        "database_metrics": {
            "connection_pool_size": "统计中",
            "active_connections": "统计中",
            "query_performance": "统计中"
        },
        "search_metrics": {
            "vector_searches": "统计中",
            "hybrid_searches": "统计中",
            "average_search_time": "统计中"
        },
        "vectorization_metrics": {
            "total_vectors": "统计中",
            "vectorization_rate": "统计中",
            "vector_sync_status": "统计中"
        },
        "note": "详细指标统计功能待实现"
    }


@router.get("/statistics", response_model=MetadataStatsResponse, summary="元数据统计信息")
async def get_statistics(
    knowledge_id: str = Query(..., description="知识库ID"),
    metadata_crud = Depends(get_metadata_crud)
):
    """
    获取指定知识库的元数据统计信息
    
    - **knowledge_id**: 知识库ID
    
    返回该知识库的详细统计信息，包括各类实体数量和向量化状态。
    """
    try:
        knowledge_id = validate_knowledge_id(knowledge_id)
        stats = await metadata_crud.get_statistics(knowledge_id)
        
        return MetadataStatsResponse(**stats)
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

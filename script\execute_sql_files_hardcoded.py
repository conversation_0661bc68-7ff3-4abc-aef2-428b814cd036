#!/usr/bin/env python3
"""
SQL执行脚本 - 硬编码版本

为modules/db/preparation目录下的特定SQL文件提供执行函数
每个SQL文件对应一个执行函数，可以单独调用
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import List

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent / 'src'
sys.path.insert(0, str(project_root))

from service import get_client, cleanup


async def execute_pgvector_all():
    """执行 pgvector_all.sql"""
    return await _execute_sql_file("pgvector_all.sql", "database.vdbs.pgvector")


async def execute_mysql_metadata():
    """执行 mysql_metadata.sql"""
    return await _execute_sql_file("mysql_metadata.sql", "database.rdbs.mysql")


async def execute_mysql_financial():
    """执行 mysql_financial.sql"""
    return await _execute_sql_file("mysql_financial.sql", "database.rdbs.mysql")


async def execute_mysql_user():
    """执行 mysql_user.sql"""
    return await _execute_sql_file("mysql_user.sql", "database.rdbs.mysql")


async def execute_mysql_system():
    """执行 mysql_system.sql"""
    return await _execute_sql_file("mysql_system.sql", "database.rdbs.mysql")


async def execute_mysql_report():
    """执行 mysql_report.sql"""
    return await _execute_sql_file("mysql_report.sql", "database.rdbs.mysql")


async def execute_mysql_analysis():
    """执行 mysql_analysis.sql"""
    return await _execute_sql_file("mysql_analysis.sql", "database.rdbs.mysql")


async def execute_mysql_logging():
    """执行 mysql_logging.sql"""
    return await _execute_sql_file("mysql_logging.sql", "database.rdbs.mysql")


async def execute_mysql_audit():
    """执行 mysql_audit.sql"""
    return await _execute_sql_file("mysql_audit.sql", "database.rdbs.mysql")


async def execute_mysql_config():
    """执行 mysql_config.sql"""
    return await _execute_sql_file("mysql_config.sql", "database.rdbs.mysql")


async def execute_mysql_temp_test():
    """执行 mysql_temp_test.sql"""
    return await _execute_sql_file("mysql_temp_test.sql", "database.rdbs.mysql")


async def _execute_sql_file(filename: str, db_config: str) -> bool:
    """
    执行指定的SQL文件
    
    Args:
        filename: SQL文件名
        db_config: 数据库配置路径
        
    Returns:
        执行是否成功
    """
    try:
        # 获取项目根目录
        project_root = Path(__file__).parent.parent
        sql_file_path = project_root / "src" / "modules" / "db" / "preparation" / filename
        
        if not sql_file_path.exists():
            print(f"❌ SQL文件不存在: {sql_file_path}")
            return False
        
        print(f"📝 执行文件: {filename}")
        
        # 读取SQL文件内容
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 获取数据库客户端
        client = await get_client(db_config)
        print(f"🔌 连接数据库: {db_config}")
        
        # 分割SQL语句并执行
        statements = _split_sql_statements(sql_content)
        print(f"📋 SQL语句数量: {len(statements)}")
        
        executed_count = 0
        for i, statement in enumerate(statements):
            statement = statement.strip()
            if not statement:
                continue
                
            try:
                # 执行SQL语句
                if statement.upper().startswith(('SELECT', 'SHOW', 'DESC')):
                    # 查询语句
                    result = await client.afetch_all(statement)
                    print(f"   查询语句 {i+1}: {len(result.data) if result.data else 0} 行结果")
                else:
                    # 执行语句
                    result = await client.aexecute(statement)
                    affected_rows = getattr(result, 'affected_rows', 0) or 0
                    print(f"   执行语句 {i+1}: 影响 {affected_rows} 行")
                
                executed_count += 1
                
            except Exception as e:
                # 某些语句可能预期会出错（如DROP IF EXISTS）
                if "DROP" in statement.upper() and "does not exist" in str(e).lower():
                    print(f"   语句 {i+1}: 表不存在，跳过 (正常情况)")
                    executed_count += 1
                    continue
                else:
                    print(f"❌ 执行语句 {i+1} 失败: {e}")
                    print(f"   SQL: {statement[:100]}...")
                    return False
        
        print(f"✅ 成功执行 {executed_count}/{len(statements)} 条语句")
        return True
        
    except Exception as e:
        print(f"❌ 执行SQL文件失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def _split_sql_statements(sql_content: str) -> List[str]:
    """
    分割SQL语句
    
    Args:
        sql_content: SQL内容
        
    Returns:
        SQL语句列表
    """
    # 简单的SQL语句分割（按分号分割）
    statements = []
    current_statement = ""
    in_string = False
    string_char = None
    i = 0
    
    while i < len(sql_content):
        char = sql_content[i]
        
        # 处理字符串
        if char in ("'", '"') and (i == 0 or sql_content[i-1] != '\\'):
            if not in_string:
                in_string = True
                string_char = char
            elif char == string_char:
                in_string = False
                string_char = None
        # 处理注释
        elif char == '-' and i + 1 < len(sql_content) and sql_content[i+1] == '-' and not in_string:
            # 单行注释
            newline_pos = sql_content.find('\n', i)
            if newline_pos == -1:
                break  # 注释到文件末尾
            i = newline_pos
        elif char == '/' and i + 1 < len(sql_content) and sql_content[i+1] == '*' and not in_string:
            # 多行注释
            end_comment_pos = sql_content.find('*/', i + 2)
            if end_comment_pos == -1:
                break  # 注释未闭合
            i = end_comment_pos + 1
        # 处理语句分隔符
        elif char == ';' and not in_string:
            current_statement = current_statement.strip()
            if current_statement:
                statements.append(current_statement)
            current_statement = ""
        else:
            current_statement += char
            
        i += 1
    
    # 添加最后一个语句（如果没有分号结尾）
    current_statement = current_statement.strip()
    if current_statement:
        statements.append(current_statement)
    
    return statements


async def main():
    """主函数 - 演示如何使用"""
    print("=" * 60)
    print("🗄️  SQL文件执行脚本 (硬编码版本)")
    print("=" * 60)
    
    # 示例：执行特定的SQL文件
    print("\n📋 可用的执行函数:")
    print("1.  execute_pgvector_all()     - pgvector_all.sql")
    print("2.  execute_mysql_metadata()   - mysql_metadata.sql")
    print("3.  execute_mysql_financial()  - mysql_financial.sql")
    print("4.  execute_mysql_user()       - mysql_user.sql")
    print("5.  execute_mysql_system()     - mysql_system.sql")
    print("6.  execute_mysql_report()     - mysql_report.sql")
    print("7.  execute_mysql_analysis()   - mysql_analysis.sql")
    print("8.  execute_mysql_logging()    - mysql_logging.sql")
    print("9.  execute_mysql_audit()      - mysql_audit.sql")
    print("10. execute_mysql_config()     - mysql_config.sql")
    print("11. execute_mysql_temp_test()  - mysql_temp_test.sql")
    
    print("\n🔧 使用示例:")
    print("await execute_pgvector_all()")
    print("await execute_mysql_temp_test()")
    
    # 清理资源
    try:
        await cleanup()
        print("\n🧹 资源清理完成")
    except Exception as e:
        print(f"\n⚠️ 资源清理时发生错误: {e}")


# 便捷函数列表
SQL_FUNCTIONS = {
    "pgvector_all": execute_pgvector_all,
    "mysql_metadata": execute_mysql_metadata,
    "mysql_financial": execute_mysql_financial,
    "mysql_user": execute_mysql_user,
    "mysql_system": execute_mysql_system,
    "mysql_report": execute_mysql_report,
    "mysql_analysis": execute_mysql_analysis,
    "mysql_logging": execute_mysql_logging,
    "mysql_audit": execute_mysql_audit,
    "mysql_config": execute_mysql_config,
    "mysql_temp_test": execute_mysql_temp_test,
}


if __name__ == "__main__":
    # 如果直接运行脚本，显示帮助信息
    if len(sys.argv) == 1:
        asyncio.run(main())
    else:
        # 如果提供了参数，执行对应的函数
        func_name = sys.argv[1]
        if func_name in SQL_FUNCTIONS:
            result = asyncio.run(SQL_FUNCTIONS[func_name]())
            sys.exit(0 if result else 1)
        else:
            print(f"❌ 未知的函数: {func_name}")
            print("可用函数:", list(SQL_FUNCTIONS.keys()))
            sys.exit(1)
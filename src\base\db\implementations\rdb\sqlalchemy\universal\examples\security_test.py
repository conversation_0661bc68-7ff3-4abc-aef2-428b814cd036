#!/usr/bin/env python3
"""
Universal SQLAlchemy安全测试

测试SQL注入防护和标识符验证
"""

import sys
import os

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '../../../../../../..')
sys.path.insert(0, os.path.abspath(project_root))

import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_identifier_validation():
    """测试标识符验证"""
    print("🔒 测试1: 标识符验证")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.dialects.mysql import MySQLDialect
    
    dialect = MySQLDialect()
    
    # 测试合法标识符
    valid_identifiers = [
        "user_name",
        "UserName", 
        "_private",
        "table123",
        "a",
        "column_with_underscores"
    ]
    
    print("✅ 测试合法标识符:")
    for identifier in valid_identifiers:
        try:
            quoted = dialect.quote_identifier(identifier)
            print(f"   {identifier} -> {quoted}")
        except Exception as e:
            print(f"❌ 意外失败: {identifier} -> {e}")
            return False
    
    # 测试危险标识符
    dangerous_identifiers = [
        "name`; DROP TABLE users; --",  # SQL注入尝试
        "col'; DELETE FROM users; --",   # 另一种注入
        "table/*comment*/name",          # 注释注入
        "col\x00name",                   # 空字节注入
        "col\nname",                     # 换行符
        "SELECT",                        # SQL关键字
        "DROP",                          # 危险关键字
        "",                              # 空标识符
        "a" * 65,                        # 过长标识符
        "123invalid",                    # 数字开头
        "col-name",                      # 包含连字符
        "col name",                      # 包含空格
    ]
    
    print("\n🚨 测试危险标识符:")
    for identifier in dangerous_identifiers:
        try:
            quoted = dialect.quote_identifier(identifier)
            print(f"❌ 应该被拒绝但通过了: {identifier} -> {quoted}")
            return False
        except ValueError as e:
            print(f"✅ 正确拒绝: {identifier[:20]}... -> {str(e)[:50]}...")
        except Exception as e:
            print(f"⚠️  意外异常: {identifier} -> {type(e).__name__}: {e}")
    
    return True


def test_sql_injection_prevention():
    """测试SQL注入防护"""
    print("\n🔒 测试2: SQL注入防护")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.factory import create_sqlite_client
    from base.db.base.rdb import QueryRequest
    
    # 使用SQLite内存数据库进行安全测试
    client = create_sqlite_client(":memory:")
    
    # 创建测试表
    client.execute("""
        CREATE TABLE test_security (
            id INTEGER PRIMARY KEY,
            name TEXT,
            value TEXT
        )
    """)
    
    # 插入测试数据
    client.execute("INSERT INTO test_security (name, value) VALUES ('test', 'data')")
    
    # 测试恶意列名
    malicious_columns = [
        "name`; DROP TABLE test_security; --",
        "name'; DELETE FROM test_security; --",
        "name/**/UNION/**/SELECT/**/1,2,3--",
    ]
    
    print("🚨 测试恶意列名:")
    for malicious_col in malicious_columns:
        try:
            request = QueryRequest(
                table="test_security",
                columns=[malicious_col]
            )
            result = client.query(request)
            print(f"❌ 恶意查询成功（不应该）: {malicious_col[:30]}...")
            return False
        except ValueError as e:
            print(f"✅ 正确阻止恶意列名: {str(e)[:50]}...")
        except Exception as e:
            print(f"⚠️  其他异常: {type(e).__name__}: {str(e)[:50]}...")
    
    # 验证表仍然存在且数据完整
    try:
        result = client.query(QueryRequest(table="test_security"))
        if result.data and len(result.data) > 0:
            print("✅ 数据完整性验证通过")
        else:
            print("❌ 数据可能被破坏")
            return False
    except Exception as e:
        print(f"❌ 数据完整性验证失败: {e}")
        return False
    
    client.disconnect()
    return True


def test_quote_escaping():
    """测试引号转义"""
    print("\n🔒 测试3: 引号转义")
    print("=" * 60)
    
    from base.db.implementations.rdb.sqlalchemy.universal.dialects.mysql import MySQLDialect
    from base.db.implementations.rdb.sqlalchemy.universal.dialects.postgresql import PostgreSQLDialect
    from base.db.implementations.rdb.sqlalchemy.universal.dialects.sqlite import SQLiteDialect
    
    dialects = [
        ("MySQL", MySQLDialect()),
        ("PostgreSQL", PostgreSQLDialect()),
        ("SQLite", SQLiteDialect())
    ]
    
    # 测试包含引号的合法标识符
    test_cases = [
        "col_with_backtick",  # 这个应该通过基础验证
    ]
    
    for dialect_name, dialect in dialects:
        print(f"\n📊 测试 {dialect_name} 方言:")
        quote_char = dialect.get_identifier_quote_char()
        print(f"   引号字符: {quote_char}")
        
        for identifier in test_cases:
            try:
                quoted = dialect.quote_identifier(identifier)
                print(f"   {identifier} -> {quoted}")
            except Exception as e:
                print(f"   ❌ {identifier} -> {e}")
    
    return True


def test_performance_impact():
    """测试安全验证的性能影响"""
    print("\n🔒 测试4: 性能影响")
    print("=" * 60)
    
    import time
    from base.db.implementations.rdb.sqlalchemy.universal.dialects.mysql import MySQLDialect
    
    dialect = MySQLDialect()
    
    # 测试大量合法标识符的性能
    identifiers = [f"column_{i}" for i in range(1000)]
    
    start_time = time.time()
    for identifier in identifiers:
        try:
            dialect.quote_identifier(identifier)
        except Exception:
            pass
    end_time = time.time()
    
    duration = end_time - start_time
    print(f"✅ 处理1000个标识符耗时: {duration:.4f}秒")
    print(f"   平均每个标识符: {duration/1000*1000:.4f}毫秒")
    
    if duration < 0.1:  # 100ms内完成1000个标识符验证
        print("✅ 性能影响可接受")
        return True
    else:
        print("⚠️  性能影响较大，可能需要优化")
        return False


def main():
    """主测试函数"""
    print("🔒 Universal SQLAlchemy安全测试开始")
    print("=" * 80)
    
    tests = [
        ("标识符验证", test_identifier_validation),
        ("SQL注入防护", test_sql_injection_prevention),
        ("引号转义", test_quote_escaping),
        ("性能影响", test_performance_impact),
    ]
    
    results = {}
    passed = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            results[test_name] = False
            print(f"❌ {test_name} - 异常: {e}")
            import traceback
            traceback.print_exc()
    
    # 测试总结
    total = len(tests)
    print(f"\n{'='*80}")
    print("🔒 安全测试总结")
    print(f"{'='*80}")
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    print(f"\n🛡️ 安全改进总结:")
    print("✅ 标识符验证：防止SQL关键字和危险字符")
    print("✅ 引号转义：防止引号注入攻击")
    print("✅ 长度限制：防止缓冲区溢出")
    print("✅ 字符验证：只允许安全字符")
    print("✅ 性能优化：验证开销可接受")
    
    if passed == total:
        print(f"\n🎉 所有安全测试通过！系统安全性得到显著提升。")
        return True
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查安全实现。")
        return False


if __name__ == "__main__":
    success = main()
    exit_code = 0 if success else 1
    print(f"\n程序退出，退出码: {exit_code}")
    exit(exit_code)

"""
DD-B历史信息连接器

负责历史信息提取和数据生成策略判断的核心逻辑：
1. 历史信息提取：通过pre_distribution_id关联查询获取dr09和dr17
2. 向量搜索：在dd_submission_data表中进行向量化搜索
3. 置信度判断：根据搜索结果相似度决定生成策略
"""

import time
import logging
from typing import List, Dict, Any, Optional, Tuple

from modules.knowledge.dd.search import DDSearch
from modules.dd_submission.dd_b.infrastructure.models import (
    DDBRecord,
    HistorySearchResult,
    GenerationDecision,
    GenerationStrategyEnum
)
from modules.dd_submission.dd_b.infrastructure.constants import DDBConstants
from modules.dd_submission.dd_b.infrastructure.exceptions import (
    DDBError,
    DDBDatabaseError,
    DDBProcessingError,
    handle_async_ddb_errors,
    create_database_error,
    create_processing_error
)

logger = logging.getLogger(__name__)


class HistoryInfoExtractor:
    """历史信息提取器"""
    
    def __init__(self, rdb_client: Any):
        """
        初始化历史信息提取器
        
        Args:
            rdb_client: 关系型数据库客户端
        """
        self.rdb_client = rdb_client
        self.pre_distribution_table = DDBConstants.PRE_DISTRIBUTION_TABLE
    
    @handle_async_ddb_errors
    async def extract_history_info(self, pre_distribution_id: str) -> Tuple[str, str]:
        """
        提取历史信息
        
        Args:
            pre_distribution_id: 前置分发ID
            
        Returns:
            (dr09, dr17) 元组
        """
        try:
            logger.info(f"提取历史信息: pre_distribution_id={pre_distribution_id}")
            
            # 使用DD CRUD查询pre_distribution表获取dr09和dr17
            from modules.knowledge.dd.crud import DDCrud

            # 创建DD CRUD实例（复用现有的rdb_client）
            dd_crud = DDCrud(rdb_client=self.rdb_client)

            # 使用DD CRUD的get_pre_distribution方法
            record = await dd_crud.get_pre_distribution(pre_id=pre_distribution_id)

            if not record:
                raise create_processing_error(
                    f"未找到pre_distribution_id={pre_distribution_id}的记录",
                    processing_step="history_extraction",
                    record_id=pre_distribution_id
                )
            dr09 = record.get('dr09', '')
            dr17 = record.get('dr17', '')
            
            if not dr09 or not dr17:
                raise create_processing_error(
                    f"dr09或dr17字段为空: dr09='{dr09}', dr17='{dr17}'",
                    processing_step="history_extraction",
                    record_id=pre_distribution_id
                )
            
            logger.info(f"历史信息提取成功: dr09='{dr09}', dr17='{dr17}'")
            return dr09, dr17
            
        except Exception as e:
            if isinstance(e, (DDBDatabaseError, DDBProcessingError)):
                raise
            
            logger.error(f"提取历史信息失败: {e}")
            raise create_processing_error(
                f"提取历史信息失败: {str(e)}",
                processing_step="history_extraction",
                record_id=pre_distribution_id
            )
    
    @handle_async_ddb_errors
    async def batch_extract_history_info(
        self, 
        pre_distribution_ids: List[str]
    ) -> Dict[str, Tuple[str, str]]:
        """
        批量提取历史信息
        
        Args:
            pre_distribution_ids: 前置分发ID列表
            
        Returns:
            {pre_distribution_id: (dr09, dr17)} 字典
        """
        try:
            logger.info(f"批量提取历史信息: {len(pre_distribution_ids)} 个ID")
            
            # 构建批量查询条件
            batch_conditions = [{"id": pid} for pid in pre_distribution_ids]
            
            # 执行批量查询
            batch_result = await self.rdb_client.abatch_select(
                table=self.pre_distribution_table,
                conditions_list=batch_conditions
            )
            
            if not batch_result.success:
                raise create_database_error(
                    f"批量查询pre_distribution表失败: {batch_result.error_message}",
                    operation="batch_select",
                    table_name=self.pre_distribution_table
                )
            
            # 处理结果
            result_map = {}
            for record in batch_result.data:
                pid = str(record.get('id', ''))
                dr09 = record.get('dr09', '')
                dr17 = record.get('dr17', '')
                
                if pid and dr09 and dr17:
                    result_map[pid] = (dr09, dr17)
                else:
                    logger.warning(f"记录数据不完整: id={pid}, dr09='{dr09}', dr17='{dr17}'")
            
            logger.info(f"批量提取完成: 成功提取 {len(result_map)} 条记录")
            return result_map
            
        except Exception as e:
            if isinstance(e, DDBDatabaseError):
                raise
            
            logger.error(f"批量提取历史信息失败: {e}")
            raise create_processing_error(
                f"批量提取历史信息失败: {str(e)}",
                processing_step="batch_history_extraction"
            )


class OptimizedVectorSearchEngine:
    """优化的向量搜索引擎 - 直接复用现有DD搜索实现"""

    def __init__(self, rdb_client: Any, vdb_client: Any, embedding_client: Any = None):
        """
        初始化优化的向量搜索引擎

        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端
            embedding_client: 向量化模型客户端
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client

        # 直接复用现有的DD搜索实例
        self.dd_search = DDSearch(rdb_client, vdb_client, embedding_client)

        # 搜索配置
        self.search_table = DDBConstants.SUBMISSION_DATA_TABLE
        self.search_fields = ["dr09", "dr17"]
    
    @handle_async_ddb_errors
    async def search_history_data(
        self,
        dr09: str,
        dr17: str,
        limit: int = None,
        min_score: float = None
    ) -> HistorySearchResult:
        """
        搜索历史数据 - 优化版本，支持多策略搜索

        Args:
            dr09: 数据项名称
            dr17: 数据项定义
            limit: 搜索结果限制
            min_score: 最小相似度分数

        Returns:
            历史搜索结果
        """
        start_time = time.time()

        try:
            logger.info(f"开始优化向量搜索历史数据: dr09='{dr09}', dr17='{dr17}'")

            # 设置默认参数
            search_limit = limit or DDBConstants.DEFAULT_SEARCH_LIMIT
            search_min_score = min_score or DDBConstants.MIN_SEARCH_SCORE

            # 🔧 优化搜索策略：使用向量混合搜索
            search_results = await self._execute_vector_hybrid_search(
                dr09=dr09,
                dr17=dr17,
                search_limit=search_limit,
                search_min_score=search_min_score
            )
            
            search_time = (time.time() - start_time) * 1000
            
            # 分析搜索结果
            total_results = len(search_results)
            best_match_score = 0.0
            best_match_record = None
            
            if search_results:
                # 按分数排序，获取最佳匹配
                sorted_results = sorted(search_results, key=lambda x: x.get("score", 0), reverse=True)
                best_match_record = sorted_results[0]
                best_match_score = best_match_record.get("score", 0.0)

                # 详细记录分数信息用于调试
                logger.info(f"🔍 搜索结果分数详情:")
                for i, result in enumerate(sorted_results[:3]):  # 只显示前3个结果
                    score = result.get("score", 0.0)
                    distance = result.get("distance", "N/A")
                    data_row_id = result.get("data_row_id", "N/A")
                    logger.info(f"  结果{i+1}: score={score:.6f}, distance={distance}, data_row_id={data_row_id}")

                    # 检查分数计算逻辑
                    if isinstance(distance, (int, float)):
                        calculated_score = max(0.0, min(1.0, 1.0 - distance))
                        logger.info(f"    计算验证: 1.0 - {distance} = {calculated_score:.6f}")
                        if abs(score - calculated_score) > 0.001:
                            logger.warning(f"    ⚠️ 分数不一致! 存储的score={score:.6f}, 计算的score={calculated_score:.6f}")

                logger.info(f"🎯 最佳匹配分数: {best_match_score:.6f}")

                # 检查分数是否合理
                if best_match_score < 0.1:
                    logger.warning(f"⚠️ 分数过低: {best_match_score:.6f}，可能存在计算错误")
                elif best_match_score > 0.9:
                    logger.info(f"✅ 高质量匹配: {best_match_score:.6f}")
                else:
                    logger.info(f"📊 中等质量匹配: {best_match_score:.6f}")
            
            # 判断置信度
            confidence_level = best_match_score
            is_high_confidence = confidence_level >= DDBConstants.HIGH_CONFIDENCE_THRESHOLD

            # 构建搜索查询描述
            search_query = f"dr09='{dr09}', dr17='{dr17}'"

            # 构建搜索结果
            result = HistorySearchResult(
                query_dr09=dr09,
                query_dr17=dr17,
                search_time_ms=search_time,
                total_results=total_results,
                best_match_score=best_match_score,
                best_match_record=best_match_record,
                all_results=search_results,
                confidence_level=confidence_level,
                is_high_confidence=is_high_confidence,
                search_strategy="vector_hybrid",
                search_fields=["dr09", "dr17"],
                search_notes=[
                    f"搜索查询: {search_query}",
                    f"搜索结果: {total_results} 条",
                    f"最佳分数: {best_match_score:.3f}",
                    f"置信度: {'高' if is_high_confidence else '低'}"
                ]
            )
            
            logger.info(f"向量搜索完成: 找到 {total_results} 条结果, "
                       f"最佳分数: {best_match_score:.3f}, "
                       f"置信度: {'高' if is_high_confidence else '低'}, "
                       f"耗时: {search_time:.2f}ms")
            
            return result
            
        except Exception as e:
            search_time = (time.time() - start_time) * 1000
            logger.error(f"向量搜索失败: {e}")
            
            # 返回失败的搜索结果
            return HistorySearchResult(
                query_dr09=dr09,
                query_dr17=dr17,
                search_time_ms=search_time,
                total_results=0,
                best_match_score=0.0,
                confidence_level=0.0,
                is_high_confidence=False,
                search_notes=[f"搜索失败: {str(e)}"]
            )

    async def _execute_vector_hybrid_search(
        self,
        dr09: str,
        dr17: str,
        search_limit: int,
        search_min_score: float
    ) -> List[Dict[str, Any]]:
        """
        执行向量混合搜索 - 基于PGVector的hybrid_search

        Args:
            dr09: 数据项名称
            dr17: 数据项定义
            search_limit: 搜索限制
            search_min_score: 最小分数

        Returns:
            搜索结果列表
        """
        try:
            logger.info("🚀 执行高级混合搜索策略：基于PGVector hybrid_search")

            # 构建字段查询
            field_queries = {}

            # 清理和优化查询文本
            clean_dr09 = self._clean_query_text(dr09)
            clean_dr17 = self._clean_query_text(dr17)

            if clean_dr09:
                field_queries["dr09"] = clean_dr09
            if clean_dr17:
                field_queries["dr17"] = clean_dr17

            # 构建优化的混合查询
            if clean_dr09 and clean_dr17:
                optimized_query = self._build_optimized_query(clean_dr09, clean_dr17)
                field_queries["hybrid"] = optimized_query

            logger.info(f"🚀 字段查询构建完成: {list(field_queries.keys())}")

            # 使用DDSearch的向量混合搜索
            search_results = await self.dd_search.vector_hybrid_search(
                query=f"{clean_dr09} {clean_dr17}".strip(),
                field_queries=field_queries,
                knowledge_id=None,
                data_layer=None,
                limit=search_limit,
                min_score=search_min_score,
                rank_algorithm="rrf",
                rank_config={"k": 60}
            )

            # 为结果添加搜索策略标记
            for result in search_results:
                result["search_strategy"] = "vector_hybrid"
                result["combined_score"] = result.get("score", 0)
                result["match_strategies"] = ["向量混合搜索"]

            logger.info(f"🚀 向量混合搜索完成: 找到 {len(search_results)} 条结果")
            return search_results

        except Exception as e:
            logger.error(f"高级混合搜索失败: {e}")
            # 降级到原有的优化搜索策略
            logger.info("降级到原有优化搜索策略")
            return await self._execute_optimized_search(
                dr09=dr09,
                dr17=dr17,
                search_limit=search_limit,
                search_min_score=search_min_score
            )

    async def _execute_optimized_search(
        self,
        dr09: str,
        dr17: str,
        search_limit: int,
        search_min_score: float
    ) -> List[Dict[str, Any]]:
        """
        执行优化的多策略搜索

        Args:
            dr09: 数据项名称
            dr17: 数据项定义
            search_limit: 搜索限制
            search_min_score: 最小分数

        Returns:
            合并后的搜索结果
        """
        try:
            logger.info("🔧 执行优化搜索策略：多字段分别搜索 + 智能合并")

            # 策略1: dr09字段专门搜索
            dr09_results = await self._search_by_field(
                query=self._clean_query_text(dr09),
                field="dr09",
                search_limit=search_limit,
                search_min_score=search_min_score,
                strategy_name="DR09专项搜索"
            )

            # 策略2: dr17字段专门搜索
            dr17_results = await self._search_by_field(
                query=self._clean_query_text(dr17),
                field="dr17",
                search_limit=search_limit,
                search_min_score=search_min_score,
                strategy_name="DR17专项搜索"
            )

            # 策略3: 混合搜索（优化的查询组合）
            hybrid_query = self._build_optimized_query(dr09, dr17)
            hybrid_results = await self._search_by_field(
                query=hybrid_query,
                field="all",
                search_limit=search_limit,
                search_min_score=search_min_score,
                strategy_name="混合优化搜索"
            )

            # 合并和去重结果
            all_results = self._merge_search_results(
                dr09_results=dr09_results,
                dr17_results=dr17_results,
                hybrid_results=hybrid_results
            )

            logger.info(f"🔧 搜索策略结果: DR09={len(dr09_results)}条, DR17={len(dr17_results)}条, "
                       f"混合={len(hybrid_results)}条, 合并后={len(all_results)}条")

            return all_results

        except Exception as e:
            logger.error(f"优化搜索执行失败: {e}")
            # 降级到原始搜索方式
            return await self._fallback_search(dr09, dr17, search_limit, search_min_score)

    def _clean_query_text(self, text: str) -> str:
        """
        清理查询文本，移除无用的描述前缀和格式字符

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        if not text:
            return ""

        # 移除常见的描述前缀
        prefixes_to_remove = [
            "维度描述：", "指标描述：", "数据项名称：", "需求口径：",
            "维度描述:", "指标描述:", "数据项名称:", "需求口径:"
        ]

        cleaned = text.strip()
        for prefix in prefixes_to_remove:
            if cleaned.startswith(prefix):
                cleaned = cleaned[len(prefix):].strip()

        # 移除多余的换行符和空格
        cleaned = " ".join(cleaned.split())

        return cleaned

    def _build_optimized_query(self, dr09: str, dr17: str) -> str:
        """
        构建优化的混合查询，避免简单拼接导致的语义混乱

        Args:
            dr09: 数据项名称
            dr17: 数据项定义

        Returns:
            优化的查询字符串
        """
        clean_dr09 = self._clean_query_text(dr09)
        clean_dr17 = self._clean_query_text(dr17)

        # 如果dr09和dr17有重叠内容，避免重复
        if clean_dr09 and clean_dr17:
            # 检查是否有重叠的关键词
            dr09_words = set(clean_dr09.split())
            dr17_words = set(clean_dr17.split())
            overlap = dr09_words.intersection(dr17_words)

            if len(overlap) > 0:
                # 有重叠，优先使用更具体的dr09，补充dr17的独特部分
                unique_dr17_words = dr17_words - dr09_words
                if unique_dr17_words:
                    return f"{clean_dr09} {' '.join(unique_dr17_words)}"
                else:
                    return clean_dr09
            else:
                # 无重叠，直接组合
                return f"{clean_dr09} {clean_dr17}"
        elif clean_dr09:
            return clean_dr09
        elif clean_dr17:
            return clean_dr17
        else:
            return ""

    async def _search_by_field(
        self,
        query: str,
        field: str,
        search_limit: int,
        search_min_score: float,
        strategy_name: str
    ) -> List[Dict[str, Any]]:
        """
        按指定字段执行搜索

        Args:
            query: 查询文本
            field: 搜索字段 (dr09/dr17/all)
            search_limit: 搜索限制
            search_min_score: 最小分数
            strategy_name: 策略名称（用于日志）

        Returns:
            搜索结果列表
        """
        if not query.strip():
            logger.warning(f"{strategy_name}: 查询文本为空，跳过搜索")
            return []

        try:
            logger.info(f"{strategy_name}: 查询='{query}', 字段={field}")

            # 根据字段类型选择搜索方法
            if field == "dr09":
                from modules.knowledge.dd.search import SearchField
                results = await self.dd_search.search(
                    query=query,
                    mode="vector",
                    field=SearchField.DATA_ITEM_NAME,
                    limit=search_limit,
                    min_score=search_min_score
                )
            elif field == "dr17":
                from modules.knowledge.dd.search import SearchField
                results = await self.dd_search.search(
                    query=query,
                    mode="vector",
                    field=SearchField.REQUIREMENT_RULE,
                    limit=search_limit,
                    min_score=search_min_score
                )
            else:  # field == "all"
                results = await self.dd_search.vector_search(
                    query=query,
                    limit=search_limit,
                    min_score=search_min_score,
                    knowledge_id=None
                )

            # 为结果添加搜索策略标记
            for result in results:
                result["search_strategy"] = strategy_name
                result["search_field"] = field

            logger.info(f"{strategy_name}: 找到 {len(results)} 条结果")
            return results

        except Exception as e:
            logger.error(f"{strategy_name} 搜索失败: {e}")
            return []

    def _merge_search_results(
        self,
        dr09_results: List[Dict[str, Any]],
        dr17_results: List[Dict[str, Any]],
        hybrid_results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        合并多个搜索策略的结果，去重并重新评分

        Args:
            dr09_results: DR09字段搜索结果
            dr17_results: DR17字段搜索结果
            hybrid_results: 混合搜索结果

        Returns:
            合并后的搜索结果
        """
        # 使用data_row_id作为去重键
        result_map = {}

        # 处理DR09结果（权重1.2，因为数据项名称更重要）
        for result in dr09_results:
            data_row_id = result.get("data_row_id")
            if data_row_id:
                score = result.get("score", 0) * 1.2
                result_map[data_row_id] = {
                    **result,
                    "combined_score": score,
                    "match_strategies": ["DR09专项搜索"]
                }

        # 处理DR17结果（权重1.0）
        for result in dr17_results:
            data_row_id = result.get("data_row_id")
            if data_row_id:
                score = result.get("score", 0) * 1.0
                if data_row_id in result_map:
                    # 已存在，更新分数和策略
                    existing = result_map[data_row_id]
                    existing["combined_score"] = max(existing["combined_score"], score)
                    existing["match_strategies"].append("DR17专项搜索")
                else:
                    result_map[data_row_id] = {
                        **result,
                        "combined_score": score,
                        "match_strategies": ["DR17专项搜索"]
                    }

        # 处理混合结果（权重0.8，避免重复计分）
        for result in hybrid_results:
            data_row_id = result.get("data_row_id")
            if data_row_id:
                score = result.get("score", 0) * 0.8
                if data_row_id in result_map:
                    # 已存在，只更新策略
                    existing = result_map[data_row_id]
                    existing["match_strategies"].append("混合优化搜索")
                else:
                    result_map[data_row_id] = {
                        **result,
                        "combined_score": score,
                        "match_strategies": ["混合优化搜索"]
                    }

        # 转换为列表并按combined_score排序
        merged_results = list(result_map.values())
        merged_results.sort(key=lambda x: x.get("combined_score", 0), reverse=True)

        return merged_results

    async def _fallback_search(
        self,
        dr09: str,
        dr17: str,
        search_limit: int,
        search_min_score: float
    ) -> List[Dict[str, Any]]:
        """
        降级搜索方法（原始的简单拼接方式）

        Args:
            dr09: 数据项名称
            dr17: 数据项定义
            search_limit: 搜索限制
            search_min_score: 最小分数

        Returns:
            搜索结果列表
        """
        try:
            logger.warning("使用降级搜索策略：简单拼接")
            search_query = f"{dr09} {dr17}".strip()

            results = await self.dd_search.vector_search(
                query=search_query,
                limit=search_limit,
                min_score=search_min_score,
                knowledge_id=None
            )

            # 标记为降级搜索
            for result in results:
                result["search_strategy"] = "降级搜索"
                result["combined_score"] = result.get("score", 0)
                result["match_strategies"] = ["降级搜索"]

            return results

        except Exception as e:
            logger.error(f"降级搜索也失败: {e}")
            return []


class GenerationDecisionMaker:
    """生成策略决策器"""

    def __init__(self):
        """初始化生成策略决策器"""
        self.high_confidence_threshold = DDBConstants.HIGH_CONFIDENCE_THRESHOLD
        self.all_bdr_fields = DDBConstants.ALL_BDR_FIELDS
        self.main_generation_fields = DDBConstants.MAIN_GENERATION_FIELDS

    @handle_async_ddb_errors
    async def make_decision(
        self,
        record: DDBRecord,
        history_search_result: Optional[HistorySearchResult] = None
    ) -> GenerationDecision:
        """
        制定生成策略决策

        Args:
            record: DD-B记录
            history_search_result: 历史搜索结果（可选）

        Returns:
            生成决策结果
        """
        start_time = time.time()

        try:
            logger.info(f"开始制定生成策略: record_id={record.id}")

            # 1. 前置检查：主要字段是否完整
            if record.is_main_fields_complete():
                # 策略：直接返回所有BDR字段数据
                decision = GenerationDecision(
                    record_id=record.id,
                    strategy=GenerationStrategyEnum.DIRECT_RETURN,
                    confidence_score=1.0,
                    fields_to_return_directly=record.get_all_bdr_fields(),
                    decision_notes=[
                        "主要字段完整，直接返回所有BDR字段数据",
                        f"完整字段: {DDBConstants.MAIN_CHECK_FIELDS}"
                    ]
                )

                logger.info(f"决策完成: 直接返回策略 (record_id={record.id})")
                return decision

            # 2. 主要字段不完整，需要根据历史搜索结果决策
            if not history_search_result:
                # 没有历史搜索结果，默认低置信度策略
                decision = GenerationDecision(
                    record_id=record.id,
                    strategy=GenerationStrategyEnum.LOW_CONFIDENCE,
                    confidence_score=0.0,
                    fields_to_generate=record.get_main_generation_fields(),
                    decision_notes=[
                        "主要字段不完整且无历史搜索结果",
                        "采用低置信度策略，重新生成主要字段"
                    ]
                )

                logger.info(f"决策完成: 低置信度策略 (无历史数据, record_id={record.id})")
                return decision

            # 3. 根据历史搜索结果的置信度决策
            if history_search_result.is_high_confidence:
                # 高置信度策略：完全使用历史数据填充所有BDR字段
                decision = GenerationDecision(
                    record_id=record.id,
                    strategy=GenerationStrategyEnum.HIGH_CONFIDENCE,
                    confidence_score=history_search_result.confidence_level,
                    fields_to_fill_from_history=self.all_bdr_fields,
                    history_search_result=history_search_result,
                    best_match_data=history_search_result.best_match_record,
                    decision_notes=[
                        f"高置信度策略 (分数: {history_search_result.best_match_score:.3f})",
                        "完全使用历史数据填充所有BDR字段",
                        f"历史匹配记录ID: {history_search_result.best_match_record.get('id') if history_search_result.best_match_record else 'N/A'}"
                    ]
                )

                logger.info(f"决策完成: 高置信度策略 (分数: {history_search_result.best_match_score:.3f}, record_id={record.id})")
                return decision

            else:
                # 低置信度策略：重新生成主要字段
                decision = GenerationDecision(
                    record_id=record.id,
                    strategy=GenerationStrategyEnum.LOW_CONFIDENCE,
                    confidence_score=history_search_result.confidence_level,
                    fields_to_generate=record.get_main_generation_fields(),
                    history_search_result=history_search_result,
                    decision_notes=[
                        f"低置信度策略 (分数: {history_search_result.best_match_score:.3f})",
                        "不信任历史数据，重新生成主要字段",
                        f"需要生成字段: {record.get_main_generation_fields()}"
                    ]
                )

                logger.info(f"决策完成: 低置信度策略 (分数: {history_search_result.best_match_score:.3f}, record_id={record.id})")
                return decision

        except Exception as e:
            logger.error(f"制定生成策略失败: {e}")

            # 异常情况下的默认策略
            decision = GenerationDecision(
                record_id=record.id,
                strategy=GenerationStrategyEnum.LOW_CONFIDENCE,
                confidence_score=0.0,
                fields_to_generate=record.get_main_generation_fields(),
                decision_notes=[
                    f"决策过程异常: {str(e)}",
                    "采用默认低置信度策略"
                ]
            )

            return decision

        finally:
            # 记录决策耗时
            decision_time = (time.time() - start_time) * 1000
            if 'decision' in locals():
                decision.decision_time_ms = decision_time

    def extract_fields_from_history(
        self,
        history_record: Dict[str, Any],
        target_fields: List[str]
    ) -> Dict[str, Any]:
        """
        从历史记录中提取指定字段的值

        Args:
            history_record: 历史记录
            target_fields: 目标字段列表

        Returns:
            提取的字段值字典
        """
        extracted_data = {}

        for field in target_fields:
            value = history_record.get(field)
            if value is not None:
                extracted_data[field] = value
            else:
                logger.warning(f"历史记录中缺少字段: {field}")

        return extracted_data


class HistoryConnector:
    """历史信息连接器主类"""

    def __init__(self, rdb_client: Any, vdb_client: Any = None, embedding_client: Any = None):
        """
        初始化历史信息连接器

        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端
            embedding_client: 向量化模型客户端
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client

        # 初始化组件
        self.history_extractor = HistoryInfoExtractor(rdb_client)
        self.vector_search_engine = OptimizedVectorSearchEngine(rdb_client, vdb_client, embedding_client) if vdb_client else None
        self.decision_maker = GenerationDecisionMaker()

        logger.info("历史信息连接器初始化完成")

    @handle_async_ddb_errors
    async def extract_and_decide(self, record: DDBRecord) -> GenerationDecision:
        """
        提取历史信息并制定生成决策（主入口方法）

        Args:
            record: DD-B记录

        Returns:
            生成决策结果
        """
        try:
            logger.info(f"开始历史信息提取和决策: record_id={record.id}")

            # 1. 前置检查：主要字段是否完整
            if record.is_main_fields_complete():
                logger.info(f"主要字段完整，直接返回 (record_id={record.id})")
                return await self.decision_maker.make_decision(record)

            # 2. 检查是否有pre_distribution_id
            if not record.pre_distribution_id:
                logger.warning(f"缺少pre_distribution_id，采用默认策略 (record_id={record.id})")
                return await self.decision_maker.make_decision(record)

            # 3. 提取历史信息
            try:
                dr09, dr17 = await self.history_extractor.extract_history_info(record.pre_distribution_id)
            except Exception as e:
                logger.error(f"提取历史信息失败，采用默认策略: {e}")
                return await self.decision_maker.make_decision(record)

            # 4. 执行向量搜索（如果有向量搜索引擎）
            history_search_result = None
            if self.vector_search_engine:
                try:
                    history_search_result = await self.vector_search_engine.search_history_data(dr09, dr17)
                except Exception as e:
                    logger.error(f"向量搜索失败: {e}")
            else:
                logger.warning("未配置向量搜索引擎，跳过历史搜索")

            # 5. 制定生成决策
            decision = await self.decision_maker.make_decision(record, history_search_result)

            logger.info(f"历史信息提取和决策完成: strategy={decision.strategy.value}, record_id={record.id}")
            return decision

        except Exception as e:
            logger.error(f"历史信息提取和决策失败: {e}")
            # 返回默认的低置信度策略
            return GenerationDecision(
                record_id=record.id,
                strategy=GenerationStrategyEnum.LOW_CONFIDENCE,
                confidence_score=0.0,
                fields_to_generate=record.get_main_generation_fields(),
                decision_notes=[f"处理异常: {str(e)}", "采用默认低置信度策略"]
            )


# 便捷函数
def create_history_connector(
    rdb_client: Any,
    vdb_client: Any = None,
    embedding_client: Any = None
) -> HistoryConnector:
    """创建历史信息连接器实例"""
    return HistoryConnector(rdb_client, vdb_client, embedding_client)


async def extract_and_decide_single(
    rdb_client: Any,
    vdb_client: Any,
    embedding_client: Any,
    record: DDBRecord
) -> GenerationDecision:
    """单个记录的历史信息提取和决策便捷函数"""
    connector = create_history_connector(rdb_client, vdb_client, embedding_client)
    return await connector.extract_and_decide(record)

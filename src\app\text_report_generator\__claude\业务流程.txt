报表生成后，用户点击【报告生成】
传入参数：
	套系名称，报表名称，报表携带的所有指标信息（指标id，指标名，指标描述，指标数值（不包明细指标））

进入报表生成页面，右侧操作界面提供三种方式供用户选择仿写的参考报告
	1. 用户通过名称查询历史已落知识库的报告
	2. 根据传入的套系名称，报表名称，在知识库中检索推荐（时间戳最大，即用户编辑时间最新）的报告
	3. 用户自行上传参考文档
		i. 	使用外规内化开发的文档解析模块，获取一份markdown格式的文档
		ii.	仅取markdown的第一个层级，一般为章节层级，将文档拆解为数个章节
		iii.对于每个章节，交给llm获取
			① 章节内容的摘要
			② 传入指标描述列表与章节内容，llm匹配需要填入该章节的指标id列表。（由于有8000token限制，需要拆分指标列表，进行多次匹配，将多次匹配的结果合并返回）
根据用户操作，后端返回【章节标题，章节摘要，章节内容，匹配指标id】的json给前端，渲染左侧编辑页面

用户在左侧编辑区域进行编辑确认后，点击【生成报告】按钮
    前端传入参数：
        套系名称，报表名称，指标信息，用户编辑后的章节内容，各章节匹配的指标id列表
	后端根据用户确认过的各章节内容，由llm进行各章节内容填空，返回给前端确认

前端用户点击【确认文档】后，将确认后的【章节标题，章节摘要，章节内容，匹配指标id】传送给后端，后端将该内容加上时间戳，归档进入知识库

mermaid
graph TD
    subgraph "阶段一：选择参考报告"
        A(用户点击【报告生成】) --> B[传入参数<br/>- 套系名称<br/>- 报表名称<br/>- 指标信息];
        B --> C[进入报告生成页面];
        C --> D{右侧操作界面<br/>选择仿写参考报告的方式};
        D -- "按名称查询" --> E[查询知识库中的历史报告];
        D -- "系统推荐" --> F[根据套系/报表名称<br/>检索知识库中最新的报告];
        D -- "自行上传" --> G[用户上传参考文档];
    end

    subgraph " "
        subgraph "上传文档处理子流程"
            G --> G1[文档解析模块<br/>获取Markdown格式文档];
            G1 --> G2[按一级标题拆分为章节];
            G2 --> G3{循环处理每个章节};
            G3 -- "对每个章节" --> G4[调用LLM<br/>1. 获取章节摘要<br/>2. 匹配指标ID列表];
            G4 -- "指标列表过长时" --> G5[拆分指标列表，多次匹配后合并结果];
            G5 --> G6[生成章节数据];
            G3 -- "所有章节处理完毕" --> G7[整合所有章节数据];
        end
    end

    subgraph "阶段二：编辑与生成"
        E --> H{后端返回JSON<br/>【章节标题, 章节摘要, 章节内容, 匹配指标id】};
        F --> H;
        G7 --> H;

        H --> I[前端在左侧渲染编辑页面];
        I --> J[用户编辑并确认内容];
        J --> K(用户点击【生成报告】);
        K --> L[将确认后的章节数据传送给后端];

        subgraph "后端最终处理"
            L --> N[调用LLM<br/>根据章节内容与指标进行填空];
        end

        N --> O[返回最终生成的报告内容给前端];
    end
    O --> D
    O --> P[用户点击【确认报告】]
    P --> Q[为报告内容添加时间戳<br/>归档进入知识库]

    Start((开始)) --> A;
    Q --> End((结束));
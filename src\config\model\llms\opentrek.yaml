# Hydra's instantiation target. This tells <PERSON>ydra which class to create.
_target_: base.model_serve.model_runtime.model_providers.llm_model.opentrek_llm.OpenTrekLLM

# --- Parameters for OpenTrekLLM.__init__ ---
# These will be passed directly as arguments to the constructor.
api_key: "empty"
base_url: "http://218.78.129.173:30167/v1/chat/completions"


# in the application logic if needed.
model_name: "qwen_vllm"
provider: "opentrek" # Keep provider name for logic that might need it

model_parameters:
  max_tokens: 8192
  temperature: 0.9 

# Stream setting remains at the top level of the llm config
stream: true
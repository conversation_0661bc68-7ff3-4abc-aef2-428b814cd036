"""
DD-B验证器主接口

提供DD-B数据验证功能，包括：
- 数据格式验证
- 业务规则验证
- 数据一致性检查

这是DD-B模块的验证接口文件。
"""

import time
import logging
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)

from modules.dd_submission.dd_b.infrastructure.models import (
    DDBValidationRequest,
    DDBValidationResult,
    DDBRecord
)
from modules.dd_submission.dd_b.infrastructure.constants import DDBUtils, DDBFieldTypes, DDBConstants
from modules.dd_submission.dd_b.infrastructure.exceptions import (
    DDBError,
    DDBValidationError,
    handle_async_ddb_errors,
    create_validation_error
)


class DDBValidator:
    """
    DD-B验证器主类
    
    提供DD-B数据的全面验证功能，包括格式验证、
    业务规则验证和数据一致性检查。
    """
    
    def __init__(self):
        """初始化DD-B验证器"""
        self.required_fields = DDBFieldTypes.REQUIRED_FIELDS
        self.nullable_fields = DDBFieldTypes.NULLABLE_FIELDS
        self.main_check_fields = DDBConstants.MAIN_CHECK_FIELDS
        
        logger.info("DD-B验证器初始化完成")
    
    @handle_async_ddb_errors
    async def validate(self, request: DDBValidationRequest) -> DDBValidationResult:
        """
        验证DD-B数据
        
        Args:
            request: 验证请求
            
        Returns:
            验证结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始验证DD-B数据: {len(request.records)} 条记录")
            
            # 验证请求参数
            request.validate()
            
            # 执行验证
            validation_errors = []
            validation_warnings = []
            valid_records = []
            invalid_records = []
            
            for i, record in enumerate(request.records):
                record_errors = await self._validate_single_record(record, i)
                
                if record_errors:
                    validation_errors.extend(record_errors)
                    invalid_records.append(record)
                else:
                    valid_records.append(record)
            
            # 数据一致性检查
            if request.check_data_consistency:
                consistency_warnings = self._check_data_consistency(request.records)
                validation_warnings.extend(consistency_warnings)
            
            # 构建结果
            validation_time = (time.time() - start_time) * 1000
            is_valid = len(validation_errors) == 0
            
            if not is_valid and request.strict_validation:
                # 严格模式下，有错误就失败
                is_valid = False
            
            result = DDBValidationResult(
                request=request,
                is_valid=is_valid,
                validation_errors=validation_errors,
                validation_warnings=validation_warnings,
                valid_records=valid_records,
                invalid_records=invalid_records,
                total_records=len(request.records),
                valid_count=len(valid_records),
                invalid_count=len(invalid_records),
                validation_time_ms=validation_time,
                validation_notes=[
                    f"验证了 {len(request.records)} 条记录",
                    f"有效记录: {len(valid_records)} 条",
                    f"无效记录: {len(invalid_records)} 条",
                    f"发现 {len(validation_errors)} 个错误",
                    f"发现 {len(validation_warnings)} 个警告"
                ]
            )
            
            logger.info(f"DD-B数据验证完成，耗时 {validation_time:.2f}ms")
            
            return result
            
        except Exception as e:
            validation_time = (time.time() - start_time) * 1000
            logger.error(f"DD-B数据验证失败: {e}")
            
            return DDBValidationResult(
                request=request,
                is_valid=False,
                validation_errors=[f"验证过程发生错误: {str(e)}"],
                total_records=len(request.records) if request.records else 0,
                valid_count=0,
                invalid_count=len(request.records) if request.records else 0,
                validation_time_ms=validation_time,
                validation_notes=[f"验证失败: {str(e)}"]
            )
    
    async def _validate_single_record(self, record: DDBRecord, index: int) -> List[str]:
        """
        验证单个记录
        
        Args:
            record: 记录对象
            index: 记录索引
            
        Returns:
            错误列表
        """
        errors = []
        record_prefix = f"记录 {index + 1}"
        
        # 1. 基本字段验证
        basic_errors = self._validate_basic_fields(record, record_prefix)
        errors.extend(basic_errors)
        
        # 2. 业务规则验证
        business_errors = self._validate_business_rules(record, record_prefix)
        errors.extend(business_errors)
        
        # 3. 数据格式验证
        format_errors = self._validate_data_formats(record, record_prefix)
        errors.extend(format_errors)
        
        return errors
    
    def _validate_basic_fields(self, record: DDBRecord, record_prefix: str) -> List[str]:
        """验证基本字段"""
        errors = []
        
        # 检查必填字段
        if not record.report_code or not record.report_code.strip():
            errors.append(f"{record_prefix}: report_code 不能为空")
        
        if not record.dept_id or not record.dept_id.strip():
            errors.append(f"{record_prefix}: dept_id 不能为空")
        
        return errors
    
    def _validate_business_rules(self, record: DDBRecord, record_prefix: str) -> List[str]:
        """验证业务规则"""
        errors = []
        
        # 检查主要字段的业务逻辑
        main_fields_values = [
            getattr(record, field) for field in self.main_check_fields
        ]
        
        # 如果主要字段有值，应该都有值（业务一致性）
        non_empty_main_fields = [v for v in main_fields_values if v and v.strip()]
        if 0 < len(non_empty_main_fields) < len(self.main_check_fields):
            errors.append(f"{record_prefix}: 主要字段 {self.main_check_fields} 应该全部有值或全部为空")
        
        return errors
    
    def _validate_data_formats(self, record: DDBRecord, record_prefix: str) -> List[str]:
        """验证数据格式"""
        errors = []
        
        # 验证report_code格式
        if record.report_code:
            if not DDBUtils.validate_dept_id(record.dept_id):
                errors.append(f"{record_prefix}: dept_id 格式不正确")
        
        # 可以添加更多格式验证规则
        
        return errors
    
    def _check_data_consistency(self, records: List[DDBRecord]) -> List[str]:
        """检查数据一致性"""
        warnings = []
        
        if not records:
            return warnings
        
        # 检查report_code一致性
        report_codes = set(r.report_code for r in records if r.report_code)
        if len(report_codes) > 1:
            warnings.append(f"发现多个不同的 report_code: {list(report_codes)}")
        
        # 检查dept_id一致性
        dept_ids = set(r.dept_id for r in records if r.dept_id)
        if len(dept_ids) > 1:
            warnings.append(f"发现多个不同的 dept_id: {list(dept_ids)}")
        
        return warnings
    
    @handle_async_ddb_errors
    async def validate_simple(
        self,
        report_code: str,
        dept_id: str,
        records: List[DDBRecord],
        **kwargs
    ) -> DDBValidationResult:
        """
        简化的验证接口
        
        Args:
            report_code: 报表代码
            dept_id: 部门ID
            records: 记录列表
            **kwargs: 其他可选参数
            
        Returns:
            验证结果
        """
        request = DDBValidationRequest(
            report_code=report_code,
            dept_id=dept_id,
            records=records,
            **kwargs
        )
        
        return await self.validate(request)
    
    async def quick_validate(self, records: List[DDBRecord]) -> Dict[str, Any]:
        """
        快速验证（只检查基本格式）
        
        Args:
            records: 记录列表
            
        Returns:
            快速验证结果
        """
        if not records:
            return {
                "is_valid": False,
                "error": "记录列表为空",
                "total_records": 0
            }
        
        errors = []
        for i, record in enumerate(records):
            record_errors = DDBUtils.validate_record_data(record.__dict__)
            if record_errors:
                errors.extend([f"记录 {i + 1}: {err}" for err in record_errors])
        
        return {
            "is_valid": len(errors) == 0,
            "total_records": len(records),
            "error_count": len(errors),
            "errors": errors[:10],  # 只返回前10个错误
            "has_more_errors": len(errors) > 10
        }


# 为了保持向后兼容性，提供原有的类名别名
DDBValidatorLogic = DDBValidator


# 便捷函数
async def validate_dd_b_data(
    report_code: str,
    dept_id: str,
    records: List[DDBRecord],
    **kwargs
) -> DDBValidationResult:
    """便捷函数：验证DD-B数据"""
    validator = DDBValidator()
    return await validator.validate_simple(report_code, dept_id, records, **kwargs)


async def quick_validate_dd_b_data(records: List[DDBRecord]) -> Dict[str, Any]:
    """便捷函数：快速验证DD-B数据"""
    validator = DDBValidator()
    return await validator.quick_validate(records)


def validate_dd_b_record_format(record: DDBRecord) -> List[str]:
    """便捷函数：验证单个记录格式"""
    return DDBUtils.validate_record_data(record.__dict__)

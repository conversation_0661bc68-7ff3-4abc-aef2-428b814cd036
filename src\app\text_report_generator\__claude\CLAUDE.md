@app\text_report_generator\业务流程.txt 这个文件是业务流程指引
你需要先理解这个业务场景，再看接下来的开发任务
开发背景：我们负责的是后端功能开发，需要注意，我们目前主要着重进行功能的开发，而不是接口的设计，因为接口的设计会放在\api路径下完成，\app路径主要是完成功能的实现，例如其他功能模块的调用，基础功能（llm，db）的调用等。
对于基础功能，框架主要使用get_client实现，可以通过src\service\examples\simple_usage.py查看简单的使用案例。
我们操作的对象主要是文档，这是知识库中的一类数据，关于这个对象的数据库操作可以在src\modules\knowledge\doc中找到
业务流程工作要点（按用户操作顺序）：
对于前端传入的内容，我们需要对指标信息进行数据模型的设计。
进入用户选取仿写文档流程：
路径一. 通过名称查询历史知识库报告，这部分直接通过调用modules\knowledge\doc的基础方法即可，我们可以通过限定knowledgeid来限定只选用这个业务范围的知识
路径二. 通过套系名称，报表名称进行语义查询；由于一般需要生成的报告，和历史的报告名称有非常小的差别，如需要生成【2025年3月贷款总览】，知识库中已有的是【2025年2月贷款总览】，我们希望以向量搜索的方式，进行一个近似匹配的效果。由于这部分的数据库表设计还不完善，这个功能可以先raise notimplemented
路径三，用户自行上传参考文档，这是一个相对复杂的功能。i.文档解析模块假设他有了，你可以写一个parse方法，直接返回一段有层级结构的md，后续工作中我们会真的替换为对应功能模块。ii.对于markdown本身的层级解析，是需要实现的。iii.llm循环获取章节内容的摘要，指标信息的匹配，这部分也需要实际调用基础llm功能完成。同时注意生产环境中的llm具有8000token的限制，需要根据这个特性，对可能比较长的指标list进行拆分。
以上三种路径，都会返回一个统一的，各章节信息的数据结构，需要完成这个数据结构的设计
用户点击生成：
实际是循环处理各个章节的内容，以下是一个具体实例。
假设我们拿到了一个章节内容信息：{“chapter_name”:'贷款总览'，“chapter_summary”:"描述了贷款情况"，“chapter_content”:"本月，农林牧相关的长期贷款为10000元，农林牧相关的短期贷款为5000元，上期总贷款金额为11000元，总贷款金额较上期上涨",'referenced_index_id':[1,2,3,4]}，指标信息(从在流程一开始拿到的里，通过id获取)：[{'id':1,'id_name':'农林牧短期','id_desc':'描述了本期农林牧短期贷款',"id_value":15000}....]，那么把这两个信息拼在一起，content是旧数据，需要让llm填充content新数据，应该得到新content:"本月，农林牧相关的长期贷款为11000元，农林牧相关的短期贷款为15000元，上期总贷款金额为20000元，总贷款金额较上期下降"
生成填充好的各段内容。
用户点击确认文档：
同样拿到各章节信息，此时的各章节内容都是用户已经确认的，直接进行入库操作即可。

根据以上指引，在\app\text_report_generator下生成对应代码
为了确保每次工作都能有迹可循，同时防止网络波动的影响，每次完成一块功能，你需要将需要完成的任务，目前已完成的功能，你对项目的理解，项目开发中的注意事项，都写入（或更新）\app\text_report_generator\开发进度.txt中，
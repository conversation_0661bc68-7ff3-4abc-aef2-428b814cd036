"""
PGVector连接管理器

专为PGVector数据库设计的连接管理器，简化原有的多层架构，
将SessionManager和ConnectionManager的职责合并，提供统一的连接池管理和会话管理功能。

设计原则：
1. 单一职责 - 统一管理所有连接相关功能
2. 简化架构 - 减少不必要的层次
3. 向后兼容 - 保持原有API接口不变
4. 性能优化 - 优化连接池配置和管理
5. 企业级 - 支持监控、健康检查、故障恢复

作者: HSBC Knowledge Team
日期: 2025-01-17
"""

import asyncio
import time
import logging
from contextlib import contextmanager, asynccontextmanager
from typing import Optional, Dict, Any, Union, AsyncGenerator, Generator
import psycopg2
from psycopg2.pool import SimpleConnectionPool, ThreadedConnectionPool
from psycopg2.extras import RealDictCursor
import asyncpg

from .config import PGVectorConnectionConfig
from .exceptions import ConnectionError, PoolError, TimeoutError, handle_pgvector_error

logger = logging.getLogger(__name__)


class PGVectorConnectionManager:
    """
    PGVector连接管理器

    专为PGVector数据库设计的连接管理器，合并原有SessionManager和ConnectionManager的功能，提供：
    1. 统一的同步/异步连接池管理
    2. 连接生命周期管理
    3. 健康检查和监控
    4. 自动重连和故障恢复
    5. 性能统计和优化建议
    """
    
    def __init__(self, config: PGVectorConnectionConfig, **kwargs):
        """
        初始化PGVector连接管理器

        Args:
            config: 数据库连接配置
            **kwargs: 额外配置参数
        """
        self.config = config
        self.kwargs = kwargs
        
        # 连接池
        self.sync_pool: Optional[Union[SimpleConnectionPool, ThreadedConnectionPool]] = None
        self.async_pool: Optional[asyncpg.Pool] = None
        
        # 连接状态管理
        self._is_sync_connected = False
        self._is_async_connected = False
        self._connection_lock = asyncio.Lock()
        
        # 健康检查和监控
        self._last_health_check = 0
        self._health_check_interval = kwargs.get('health_check_interval', 30)
        
        # 性能统计
        self._connection_stats = {
            'total_connections': 0,
            'failed_connections': 0,
            'reconnections': 0,
            'last_connect_time': None,
            'last_disconnect_time': None,
            'sync_operations': 0,
            'async_operations': 0,
            'total_operation_time': 0.0
        }
        
        logger.info(f"初始化PGVector连接管理器: {config.host}:{config.port}")
    
    # ==================== 连接状态属性 ====================
    
    @property
    def is_connected(self) -> bool:
        """检查同步连接是否已建立"""
        return self._is_sync_connected and self.sync_pool is not None
    
    @property
    def is_aconnected(self) -> bool:
        """检查异步连接是否已建立"""
        return self._is_async_connected and self.async_pool is not None
    
    @property
    def is_any_connected(self) -> bool:
        """检查是否有任何连接已建立（同步或异步）"""
        return self.is_connected or self.is_aconnected
    
    # ==================== 同步连接管理 ====================
    
    @handle_pgvector_error
    def connect(self, **kwargs) -> None:
        """建立同步连接池"""
        if self.sync_pool is not None:
            logger.warning("同步连接池已存在")
            return
        
        try:
            start_time = time.time()
            logger.info(f"开始建立同步连接池: {self.config.host}:{self.config.port}")
            
            # 企业级优化配置
            min_conn = getattr(self.config, 'min_connections', 5)
            max_conn = getattr(self.config, 'max_connections', 25)
            
            connection_params = {
                'host': self.config.host,
                'port': self.config.port,
                'user': getattr(self.config, 'username', getattr(self.config, 'user', 'postgres')),
                'password': self.config.password,
                'database': self.config.database
            }
            
            # 创建连接池
            if max_conn > 1:
                self.sync_pool = ThreadedConnectionPool(
                    minconn=min_conn,
                    maxconn=max_conn,
                    **connection_params
                )
            else:
                self.sync_pool = SimpleConnectionPool(
                    minconn=min_conn,
                    maxconn=max_conn,
                    **connection_params
                )
            
            # 测试连接
            self._test_sync_connection()
            
            # 更新状态和统计
            self._is_sync_connected = True
            self._connection_stats['total_connections'] += 1
            self._connection_stats['last_connect_time'] = time.time()
            
            connect_time = time.time() - start_time
            logger.info(f"同步连接池建立成功，连接数: {min_conn}-{max_conn}，耗时: {connect_time:.3f}秒")
            
        except Exception as e:
            self._connection_stats['failed_connections'] += 1
            logger.error(f"建立同步连接池失败: {e}")
            raise ConnectionError(f"Failed to create sync connection pool: {e}") from e
    
    def _test_sync_connection(self):
        """测试同步连接"""
        test_conn = None
        try:
            test_conn = self.sync_pool.getconn()
            if test_conn is None:
                raise ConnectionError("无法从连接池获取测试连接")
            
            with test_conn.cursor() as cur:
                cur.execute("SELECT 1")
                result = cur.fetchone()
                if result[0] != 1:
                    raise ConnectionError("连接测试失败")
        finally:
            if test_conn:
                self.sync_pool.putconn(test_conn)
    
    @contextmanager
    def get_sync_connection(self):
        """获取同步连接的上下文管理器"""
        if not self.is_connected:
            raise ConnectionError("同步连接池未建立")

        conn = None
        try:
            start_time = time.time()
            conn = self.sync_pool.getconn()
            if conn is None:
                raise PoolError("无法从连接池获取连接")

            self._connection_stats['sync_operations'] += 1
            yield conn

        except Exception as e:
            logger.error(f"同步连接操作失败: {e}")
            raise
        finally:
            if conn:
                try:
                    self.sync_pool.putconn(conn)
                    operation_time = time.time() - start_time
                    self._connection_stats['total_operation_time'] += operation_time
                except Exception as e:
                    logger.warning(f"归还同步连接失败: {e}")

    @contextmanager
    def transaction(self):
        """同步事务上下文管理器"""
        if not self.is_connected:
            raise ConnectionError("同步连接池未建立")

        conn = None
        try:
            start_time = time.time()
            conn = self.sync_pool.getconn()
            if conn is None:
                raise PoolError("无法从连接池获取连接")

            # 开始事务
            conn.autocommit = False
            self._connection_stats['sync_operations'] += 1

            yield conn

            # 提交事务
            conn.commit()
            logger.debug("同步事务提交成功")

        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                    logger.debug("同步事务回滚成功")
                except Exception as rollback_error:
                    logger.error(f"同步事务回滚失败: {rollback_error}")
            logger.error(f"同步事务失败: {e}")
            raise
        finally:
            if conn:
                try:
                    conn.autocommit = True  # 恢复自动提交
                    self.sync_pool.putconn(conn)
                    operation_time = time.time() - start_time
                    self._connection_stats['total_operation_time'] += operation_time
                except Exception as e:
                    logger.warning(f"归还同步连接失败: {e}")
    
    # ==================== 异步连接管理 ====================
    
    @handle_pgvector_error
    async def aconnect(self, **kwargs) -> None:
        """建立异步连接池"""
        async with self._connection_lock:
            if self.async_pool is not None:
                logger.warning("异步连接池已存在")
                return
            
            try:
                start_time = time.time()
                logger.info(f"开始建立异步连接池: {self.config.host}:{self.config.port}")
                
                # 企业级优化配置
                min_conn = getattr(self.config, 'min_connections', 5)
                max_conn = getattr(self.config, 'max_connections', 25)
                
                connection_params = {
                    'host': self.config.host,
                    'port': self.config.port,
                    'user': getattr(self.config, 'username', getattr(self.config, 'user', 'postgres')),
                    'password': self.config.password,
                    'database': self.config.database,
                    'min_size': min_conn,
                    'max_size': max_conn,
                    'command_timeout': kwargs.get('command_timeout', 60),
                    'max_inactive_connection_lifetime': kwargs.get('max_inactive_connection_lifetime', 300)
                }
                
                # 创建异步连接池
                self.async_pool = await asyncpg.create_pool(**connection_params)
                
                # 测试连接
                await self._test_async_connection()
                
                # 更新状态和统计
                self._is_async_connected = True
                self._connection_stats['total_connections'] += 1
                self._connection_stats['last_connect_time'] = time.time()
                
                connect_time = time.time() - start_time
                logger.info(f"异步连接池建立成功，连接数: {min_conn}-{max_conn}，耗时: {connect_time:.3f}秒")
                
            except Exception as e:
                self._connection_stats['failed_connections'] += 1
                logger.error(f"建立异步连接池失败: {e}")
                raise ConnectionError(f"Failed to create async connection pool: {e}") from e
    
    async def _test_async_connection(self):
        """测试异步连接"""
        async with self.async_pool.acquire() as conn:
            result = await conn.fetchval("SELECT 1")
            if result != 1:
                raise ConnectionError("异步连接测试失败")
    
    @asynccontextmanager
    async def get_async_connection(self):
        """获取异步连接的上下文管理器"""
        if not self.is_aconnected:
            raise ConnectionError("异步连接池未建立")

        start_time = time.time()
        try:
            async with self.async_pool.acquire() as conn:
                self._connection_stats['async_operations'] += 1
                yield conn
        except Exception as e:
            logger.error(f"异步连接操作失败: {e}")
            raise
        finally:
            operation_time = time.time() - start_time
            self._connection_stats['total_operation_time'] += operation_time

    @asynccontextmanager
    async def atransaction(self):
        """异步事务上下文管理器"""
        if not self.is_aconnected:
            raise ConnectionError("异步连接池未建立")

        start_time = time.time()
        try:
            async with self.async_pool.acquire() as conn:
                # 开始事务
                async with conn.transaction():
                    self._connection_stats['async_operations'] += 1
                    yield conn
                    # asyncpg的transaction()上下文管理器会自动提交
                    logger.debug("异步事务提交成功")
        except Exception as e:
            # asyncpg的transaction()上下文管理器会自动回滚
            logger.error(f"异步事务失败: {e}")
            logger.debug("异步事务回滚成功")
            raise
        finally:
            operation_time = time.time() - start_time
            self._connection_stats['total_operation_time'] += operation_time

    # ==================== 断开连接管理 ====================

    def disconnect(self) -> None:
        """断开同步连接池"""
        if self.sync_pool is not None:
            try:
                self.sync_pool.closeall()
                self.sync_pool = None
                self._is_sync_connected = False
                self._connection_stats['last_disconnect_time'] = time.time()
                logger.info("同步连接池已断开")
            except Exception as e:
                logger.warning(f"断开同步连接池时出错: {e}")

    async def adisconnect(self) -> None:
        """断开异步连接池"""
        async with self._connection_lock:
            if self.async_pool is not None:
                try:
                    await self.async_pool.close()
                    self.async_pool = None
                    self._is_async_connected = False
                    self._connection_stats['last_disconnect_time'] = time.time()
                    logger.info("异步连接池已断开")
                except Exception as e:
                    logger.warning(f"断开异步连接池时出错: {e}")

    def disconnect_all(self) -> None:
        """断开所有连接"""
        self.disconnect()
        # 异步断开需要在异步上下文中调用
        if self.async_pool is not None:
            logger.warning("异步连接池需要在异步上下文中断开")

    # ==================== 健康检查和监控 ====================

    def health_check(self) -> Dict[str, Any]:
        """执行健康检查 - 主要用于综合健康检查"""
        current_time = time.time()

        # 检查是否需要执行健康检查
        if current_time - self._last_health_check < self._health_check_interval:
            return self._get_cached_health_status()

        # 只检查同步连接状态，避免在同步方法中调用异步方法
        sync_health = self._check_sync_health()

        health_status = {
            'timestamp': current_time,
            'sync_connection': sync_health,
            'overall_status': 'healthy' if sync_health['status'] == 'healthy' else 'unhealthy',
            'healthy': sync_health['status'] == 'healthy',  # 为测试兼容性添加
            'response_time': sync_health.get('response_time', 0)
        }

        self._last_health_check = current_time
        return health_status

    async def ahealth_check(self) -> Dict[str, Any]:
        """执行异步健康检查"""
        # 检查异步连接状态
        async_health = await self._check_async_health()

        health_status = {
            'timestamp': time.time(),
            'async_connection': async_health,
            'overall_status': 'healthy' if async_health['status'] == 'healthy' else 'unhealthy',
            'healthy': async_health['status'] == 'healthy',  # 为测试兼容性添加
            'response_time': async_health.get('response_time', 0)
        }

        return health_status

    def _check_sync_health(self) -> Dict[str, Any]:
        """检查同步连接健康状态"""
        if not self.is_connected:
            return {'status': 'disconnected', 'error': 'No sync connection pool'}

        try:
            with self.get_sync_connection() as conn:
                with conn.cursor() as cur:
                    start_time = time.time()
                    cur.execute("SELECT 1")
                    result = cur.fetchone()
                    response_time = time.time() - start_time

                    if result[0] == 1:
                        return {
                            'status': 'healthy',
                            'response_time': response_time,
                            'pool_info': self._get_sync_pool_info()
                        }
                    else:
                        return {'status': 'error', 'error': 'Health check query failed'}
        except Exception as e:
            return {'status': 'error', 'error': str(e)}

    async def _check_async_health(self) -> Dict[str, Any]:
        """检查异步连接健康状态"""
        if not self.is_aconnected:
            return {'status': 'disconnected', 'error': 'No async connection pool'}

        try:
            async with self.get_async_connection() as conn:
                start_time = time.time()
                result = await conn.fetchval("SELECT 1")
                response_time = time.time() - start_time

                if result == 1:
                    return {
                        'status': 'healthy',
                        'response_time': response_time,
                        'pool_info': self._get_async_pool_info()
                    }
                else:
                    return {'status': 'error', 'error': 'Health check query failed'}
        except Exception as e:
            return {'status': 'error', 'error': str(e)}

    def _get_cached_health_status(self) -> Dict[str, Any]:
        """获取缓存的健康状态"""
        return {
            'timestamp': self._last_health_check,
            'sync_connection': {'status': 'healthy' if self.is_connected else 'disconnected'},
            'async_connection': {'status': 'healthy' if self.is_aconnected else 'disconnected'},
            'overall_status': 'cached',
            'note': 'Cached status, use force_health_check for real-time status'
        }

    def _get_sync_pool_info(self) -> Dict[str, Any]:
        """获取同步连接池信息"""
        if not self.sync_pool:
            return {}

        try:
            return {
                'minconn': getattr(self.sync_pool, 'minconn', 'unknown'),
                'maxconn': getattr(self.sync_pool, 'maxconn', 'unknown'),
                'pool_type': type(self.sync_pool).__name__
            }
        except Exception:
            return {'error': 'Unable to get pool info'}

    def _get_async_pool_info(self) -> Dict[str, Any]:
        """获取异步连接池信息"""
        if not self.async_pool:
            return {}

        try:
            return {
                'min_size': getattr(self.async_pool, '_min_size', 'unknown'),
                'max_size': getattr(self.async_pool, '_max_size', 'unknown'),
                'current_size': len(getattr(self.async_pool, '_holders', [])),
                'pool_type': type(self.async_pool).__name__
            }
        except Exception:
            return {'error': 'Unable to get pool info'}

    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        stats = self._connection_stats.copy()
        stats.update({
            'sync_connected': self.is_connected,
            'async_connected': self.is_aconnected,
            'health_check_interval': self._health_check_interval,
            'last_health_check': self._last_health_check,
            'avg_operation_time': (
                stats['total_operation_time'] / max(1, stats['sync_operations'] + stats['async_operations'])
            )
        })
        return stats

    # ==================== 向后兼容接口 ====================

    def _ensure_connected(self) -> None:
        """确保连接已建立（向后兼容）"""
        if not self.is_any_connected:
            self.connect()

    async def _aensure_connected(self) -> None:
        """确保异步连接已建立（向后兼容）"""
        if not self.is_aconnected:
            await self.aconnect()

    def sync_health_check(self, timeout: float = 5.0) -> Dict[str, Any]:
        """同步健康检查（向后兼容）"""
        # 确保连接已建立
        self._ensure_connected()
        return self._check_sync_health()

    async def async_health_check(self, timeout: float = 5.0) -> Dict[str, Any]:
        """异步健康检查（向后兼容）"""
        # 确保异步连接已建立
        await self._aensure_connected()
        return await self._check_async_health()

    @contextmanager
    def get_cursor(self):
        """获取同步游标（向后兼容）"""
        with self.get_sync_connection() as conn:
            with conn.cursor() as cursor:
                yield cursor

    @asynccontextmanager
    async def get_async_cursor(self):
        """获取异步游标（向后兼容）"""
        async with self.get_async_connection() as conn:
            yield conn  # asyncpg连接对象本身就是cursor

    # ==================== 清理和资源管理 ====================

    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            self.disconnect_all()
        except Exception as e:
            logger.warning(f"清理连接资源时出错: {e}")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.aconnect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.adisconnect()

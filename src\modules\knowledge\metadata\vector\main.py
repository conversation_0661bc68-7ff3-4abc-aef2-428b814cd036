"""
元数据向量管理器

参考DD系统的架构模式，提供元数据的向量化管理功能。
集成新的CRUD系统和数据库客户端API。
"""

import sys
from pathlib import Path
from typing import Any, Dict, List, Optional
import logging
from datetime import datetime

# 标准化日志导入
logger = logging.getLogger(__name__)

# 添加项目根目录到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from ..shared.constants import MetadataConstants, MetadataTableNames
from ..shared.utils import MetadataUtils


class MetadataVectorManager:
    """
    元数据向量管理器

    参考DD系统的架构模式，提供元数据的向量化管理功能，包括：
    - 向量同步和批量处理
    - 向量状态管理
    - 向量统计和监控
    - 与新CRUD系统的集成
    """

    def __init__(self, rdb_client, vdb_client, embedding_client=None):
        """
        初始化元数据向量管理器

        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端
            embedding_client: 向量化模型客户端（可选）
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client
        logger.info("元数据向量管理器初始化完成")
    
    async def sync_vectors(
        self,
        knowledge_id: str,
        entity_type: str,
        entity_ids: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """
        同步向量化数据

        Args:
            knowledge_id: 知识库ID
            entity_type: 实体类型（如 'source_column', 'index_column'）
            entity_ids: 实体ID列表，None表示同步所有

        Returns:
            Dict[str, Any]: 同步结果
        """
        try:
            logger.info(f"同步向量化数据: knowledge_id={knowledge_id}, entity_type={entity_type}")

            if not self.vdb_client or not self.embedding_client:
                logger.warning("向量客户端未配置，跳过向量同步")
                return {
                    "knowledge_id": knowledge_id,
                    "entity_type": entity_type,
                    "total": 0,
                    "success": 0,
                    "failed": 0,
                    "errors": ["向量客户端未配置"]
                }

            # 获取需要同步的实体数据
            entities = await self._get_entities_for_sync(knowledge_id, entity_type, entity_ids)

            result = {
                "knowledge_id": knowledge_id,
                "entity_type": entity_type,
                "total": len(entities),
                "success": 0,
                "failed": 0,
                "errors": []
            }

            # 批量处理向量化
            for entity in entities:
                try:
                    await self._sync_entity_vectors(entity, entity_type)
                    result["success"] += 1
                except Exception as e:
                    result["failed"] += 1
                    result["errors"].append(f"实体 {entity.get('id', 'unknown')} 同步失败: {e}")
                    logger.warning(f"实体向量同步失败: {e}")

            logger.info(f"向量同步完成: {result}")
            return result

        except Exception as e:
            logger.error(f"向量同步失败: {e}")
            raise
    
    async def get_vector_status(
        self, 
        knowledge_id: str, 
        entity_type: str, 
        entity_id: int
    ) -> Dict[str, Any]:
        """
        获取向量化状态
        
        Args:
            knowledge_id: 知识库ID
            entity_type: 实体类型
            entity_id: 实体ID
            
        Returns:
            Dict[str, Any]: 向量化状态
        """
        try:
            logger.info(f"获取向量化状态: knowledge_id={knowledge_id}, entity_type={entity_type}, entity_id={entity_id}")
            
            # 这里应该查询实际的向量化状态
            status = {
                "knowledge_id": knowledge_id,
                "entity_type": entity_type,
                "entity_id": entity_id,
                "sync_status": "completed",
                "vector_id": f"vec_{entity_type}_{entity_id}",
                "sync_time": "2024-01-01T10:00:00"
            }
            
            return status
            
        except Exception as e:
            logger.error(f"获取向量化状态失败: {e}")
            raise
    
    async def get_statistics(self, knowledge_id: str) -> Dict[str, Any]:
        """
        获取向量化统计信息

        Args:
            knowledge_id: 知识库ID

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            logger.info(f"获取向量化统计信息: knowledge_id={knowledge_id}")

            # 使用新的数据库客户端API查询实际统计信息
            stats = {
                "knowledge_id": knowledge_id,
                "vectorized_databases": 0,
                "vectorized_tables": 0,
                "vectorized_columns": 0,
                "vectorized_templates": 0,
                "total_vectors": 0,
                "pending_sync": 0,
                "failed_sync": 0
            }

            # 统计各类实体的向量化情况
            entity_tables = [
                (MetadataTableNames.MD_SOURCE_DATABASE, "vectorized_databases"),
                (MetadataTableNames.MD_INDEX_DATABASE, "vectorized_databases"),
                (MetadataTableNames.MD_SOURCE_TABLES, "vectorized_tables"),
                (MetadataTableNames.MD_INDEX_TABLES, "vectorized_tables"),
                (MetadataTableNames.MD_SOURCE_COLUMNS, "vectorized_columns"),
                (MetadataTableNames.MD_INDEX_COLUMNS, "vectorized_columns")
            ]

            for table_name, stat_key in entity_tables:
                try:
                    query_request = {
                        "table": table_name,
                        "filters": {"knowledge_id": knowledge_id},
                        "limit": 1000  # 假设不会超过这个数量
                    }

                    result = await self.rdb_client.aquery(query_request)
                    count = len(result.data) if result else 0
                    stats[stat_key] += count

                except Exception as e:
                    logger.warning(f"统计表 {table_name} 失败: {e}")

            # 计算总向量数（假设每个实体有多个向量化字段）
            stats["total_vectors"] = (
                stats["vectorized_databases"] * 2 +  # db_desc, comment
                stats["vectorized_tables"] * 2 +     # table_desc, comment
                stats["vectorized_columns"] * 2      # column_desc, comment
            )

            logger.info(f"向量化统计信息: {stats}")
            return stats

        except Exception as e:
            logger.error(f"获取向量化统计信息失败: {e}")
            raise

    # ==================== 私有方法 ====================

    async def _get_entities_for_sync(
        self,
        knowledge_id: str,
        entity_type: str,
        entity_ids: Optional[List[int]] = None
    ) -> List[Dict[str, Any]]:
        """获取需要同步的实体数据"""
        try:
            # 根据实体类型确定表名
            table_mapping = {
                'source_database': MetadataTableNames.MD_SOURCE_DATABASE,
                'index_database': MetadataTableNames.MD_INDEX_DATABASE,
                'source_table': MetadataTableNames.MD_SOURCE_TABLES,
                'index_table': MetadataTableNames.MD_INDEX_TABLES,
                'source_column': MetadataTableNames.MD_SOURCE_COLUMNS,
                'index_column': MetadataTableNames.MD_INDEX_COLUMNS,
                'code_set': MetadataTableNames.MD_REFERENCE_CODE_SET,
                'data_subject': MetadataTableNames.MD_DATA_SUBJECT
            }

            if entity_type not in table_mapping:
                raise ValueError(f"不支持的实体类型: {entity_type}")

            table_name = table_mapping[entity_type]

            # 构建查询条件
            filters = {"knowledge_id": knowledge_id}
            if entity_ids:
                # 这里需要根据实体类型确定ID字段名
                id_field_mapping = {
                    'source_database': 'db_id',
                    'index_database': 'db_id',
                    'source_table': 'table_id',
                    'index_table': 'table_id',
                    'source_column': 'column_id',
                    'index_column': 'column_id',
                    'code_set': 'code_set_id',
                    'data_subject': 'subject_id'
                }
                id_field = id_field_mapping.get(entity_type, 'id')
                # 注意：这里简化处理，实际可能需要使用IN查询
                if len(entity_ids) == 1:
                    filters[id_field] = entity_ids[0]

            query_request = {
                "table": table_name,
                "filters": filters,
                "limit": 1000  # 限制批量处理数量
            }

            result = await self.rdb_client.aquery(query_request)
            return result.data if result else []

        except Exception as e:
            logger.error(f"获取同步实体数据失败: {e}")
            raise

    async def _sync_entity_vectors(self, entity: Dict[str, Any], entity_type: str) -> None:
        """同步单个实体的向量数据"""
        try:
            # 提取需要向量化的内容
            vectorized_content = MetadataUtils.extract_vectorized_content(entity)

            if not vectorized_content:
                logger.debug(f"实体无需向量化内容: {entity.get('id', 'unknown')}")
                return

            # TODO: 实现实际的向量化逻辑
            # 这里应该调用embedding_client生成向量，然后存储到vdb_client
            # for field_code, content in vectorized_content.items():
            #     vector = await self.embedding_client.embed(content)
            #     await self.vdb_client.store_vector(
            #         vector_id=f"{entity_type}_{entity.get('id')}_{field_code}",
            #         vector=vector,
            #         metadata={
            #             "entity_type": entity_type,
            #             "entity_id": entity.get('id'),
            #             "field_code": field_code,
            #             "knowledge_id": entity.get('knowledge_id')
            #         }
            #     )

            logger.debug(f"实体向量同步完成: {entity.get('id', 'unknown')}")

        except Exception as e:
            logger.error(f"实体向量同步失败: {e}")
            raise

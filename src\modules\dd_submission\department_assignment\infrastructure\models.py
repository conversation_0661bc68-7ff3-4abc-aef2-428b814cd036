"""
部门职责分配数据模型

定义义务解读部门职责分配的请求、响应和业务实体模型
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class SetTypeEnum(str, Enum):
    """套系类型枚举"""
    STANDARD_1104 = "1104"
    STANDARD_DJZ = "DJZ"
    STANDARD_EAST = "EAST"
    STANDARD_YBT = "一表通"
    SURVEY = "survey"
    OTHER = "other"


class ReportTypeEnum(str, Enum):
    """报表类型枚举"""
    DETAIL = "detail"      # 明细
    INDEX = "index"        # 指标


class SubmissionTypeEnum(str, Enum):
    """提交类型枚举"""
    RANGE = "range"        # 范围
    SUBMISSION = "submission"  # 填报项


class DataLayerEnum(str, Enum):
    """数据层枚举"""
    ADS = "ADS"
    BDM = "BDM"
    IDM = "IDM"
    ADM = "ADM"
    ODS = "ODS"
    RANGE = "范围"


class FilterLayerEnum(str, Enum):
    """筛选层级枚举"""
    SET_TYPE = "set_type"           # 第一层：套系类型筛选
    REPORT_TYPE = "report_type"     # 第二层：报表类型筛选
    SUBMISSION_TYPE = "submission_type"  # 第三层：提交类型筛选
    DATA_LAYER = "data_layer"       # 第四层：数据层筛选


@dataclass
class DepartmentAssignmentRequest:
    """部门职责分配请求模型"""

    # 必填字段 - 来自dd_pre_distribution表
    submission_id: str                      # 填报项ID
    dr09: str                              # 数据项名称
    dr17: str                              # 数据项定义

    # 四层筛选字段
    set_value: str                         # 套系值（用于第一层筛选）
    report_type: str                       # 报表类型（用于第二层筛选）
    submission_type: str                   # 提交类型（用于第三层筛选）
    dr01: str                             # 数据层（用于第四层筛选）

    # 可选字段
    knowledge_id: Optional[str] = None     # 知识库ID
    data_layer: Optional[str] = None       # 数据层（用于搜索范围限制）
    version: Optional[str] = None          # 版本

    # 前置搜索条件（新增）
    dr07: Optional[str] = None             # 数据需求编号（用于前置搜索）
    pre_search_version: Optional[str] = None  # 前置搜索版本

    # 搜索配置参数
    limit: int = 100                       # 搜索结果数量限制（设置较大值）
    min_score: float = 0.3                 # 最小相似度分数（降低阈值获取更多候选）
    similarity_threshold: float = 0.5      # 相似度阈值（用于混合搜索）
    vector_weight: float = 0.6             # 向量搜索权重
    text_weight: float = 0.4               # 文本搜索权重

    # 部门推荐配置（新增）
    enable_department_recommendation: bool = True  # 启用部门推荐兜底策略
    dept_recommendation_threshold: float = 0.5     # 部门推荐阈值
    dept_recommendation_top_k: int = 3             # 部门推荐Top-K数量
    dept_min_confidence: float = 0.1               # 部门推荐最小置信度
    
    def validate(self) -> None:
        """验证请求参数"""
        if not self.submission_id or not self.submission_id.strip():
            raise ValueError("submission_id不能为空")
        if not self.dr09 or not self.dr09.strip():
            raise ValueError("dr09（数据项名称）不能为空")
        if not self.dr17 or not self.dr17.strip():
            raise ValueError("dr17（数据项定义）不能为空")
        if not self.set_value or not self.set_value.strip():
            raise ValueError("set_value（套系值）不能为空")
        if not self.report_type or not self.report_type.strip():
            raise ValueError("report_type（报表类型）不能为空")
        if not self.submission_type or not self.submission_type.strip():
            raise ValueError("submission_type（提交类型）不能为空")
        if not self.dr01 or not self.dr01.strip():
            raise ValueError("dr01（数据层）不能为空")


@dataclass
class HistoricalRecord:
    """历史记录模型"""

    # 基础信息
    submission_id: str                     # 填报项ID
    score: float                          # 相似度分数
    match_type: str                       # 匹配类型（exact/hybrid）
    matched_fields: List[str]             # 匹配到的字段

    # 搜索关键字段（与dd_submission_data表对应）
    dr09: Optional[str] = None            # 搜索字段1
    dr17: Optional[str] = None            # 搜索字段2

    # 四层筛选字段
    set_value: Optional[str] = None       # 套系值（对应dd_submission_data.type字段）
    report_type: Optional[str] = None     # 报表类型
    submission_type: Optional[str] = None # 提交类型（对应dd_submission_data.type字段）
    dr01: Optional[str] = None           # 数据层

    # 业务字段
    dr22: Optional[str] = None            # 业务部门（目标字段）

    # 相似度分数（兼容性）
    similarity_score: Optional[float] = None  # 兼容旧的字段名

    # DD字段数据（完整的dd_submission_data记录）
    dd_fields: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """初始化后处理"""
        # 兼容性处理：如果设置了similarity_score，同步到score
        if self.similarity_score is not None and self.score != self.similarity_score:
            self.score = self.similarity_score
    
    # 元数据
    create_time: Optional[datetime] = None
    version: Optional[str] = None
    data_layer: Optional[str] = None


@dataclass
class FilterResult:
    """筛选结果模型"""
    
    layer: FilterLayerEnum                 # 筛选层级
    layer_name: str                       # 层级名称
    filter_criteria: Dict[str, Any]       # 筛选条件
    before_count: int                     # 筛选前记录数
    after_count: int                      # 筛选后记录数
    filtered_records: List[HistoricalRecord]  # 筛选后的记录
    is_final_layer: bool = False          # 是否为最终层
    stopped_early: bool = False           # 是否提前停止（找到唯一结果）


@dataclass
class DepartmentAssignmentResult:
    """部门职责分配结果模型"""
    
    # 原始请求
    request: DepartmentAssignmentRequest
    
    # 搜索阶段结果
    exact_match_found: bool = False        # 是否找到精确匹配
    exact_match_count: int = 0             # 精确匹配数量
    multiple_exact_matches: bool = False   # 是否有多个精确匹配结果
    hybrid_search_count: int = 0           # 混合搜索数量
    total_candidates: int = 0              # 总候选数量
    
    # 四层筛选结果
    filter_results: List[FilterResult] = field(default_factory=list)
    
    # 最终结果
    final_records: List[HistoricalRecord] = field(default_factory=list)
    recommended_department: Optional[str] = None  # 推荐的业务部门（dr22）
    confidence_level: str = "unknown"     # 置信度级别（unique/multiple/fallback）

    # 新增：部门推荐结果
    department_recommendations: List['DepartmentRecommendation'] = field(default_factory=list)  # 部门推荐列表
    used_department_recommendation: bool = False  # 是否使用了部门推荐

    # 新增：关键词信息
    extracted_keywords: Dict[str, List[str]] = field(default_factory=dict)  # 提取的关键词

    # 处理信息
    search_time_ms: float = 0.0           # 搜索耗时
    processing_notes: List[str] = field(default_factory=list)  # 处理说明
    
    def add_processing_note(self, note: str) -> None:
        """添加处理说明"""
        self.processing_notes.append(note)
    
    def get_department_assignment(self) -> Dict[str, Any]:
        """获取部门分配结果（前端格式）"""
        result = {
            "recommended_department": self.recommended_department,
            "confidence_level": self.confidence_level,
            "total_candidates": self.total_candidates,
            "final_count": len(self.final_records),
            "processing_summary": self.processing_notes
        }
        
        if self.final_records:
            result["final_records"] = [
                {
                    "submission_id": record.submission_id,
                    "score": record.score,
                    "dr22": record.dr22,
                    "match_type": record.match_type
                }
                for record in self.final_records
            ]
        
        return result


@dataclass
class DepartmentRecommendation:
    """部门推荐结果模型"""
    dept_id: str                           # 部门ID (修正：varchar类型)
    dept_name: str                         # 部门名称
    dept_description: str                  # 部门描述
    dept_type: Optional[str] = None        # 部门类型
    confidence_score: float = 0.0          # 置信度分数
    matched_keywords: List[str] = field(default_factory=list)  # 匹配的关键词
    matching_reason: str = ""              # 推荐理由
    tfidf_score: float = 0.0              # TF-IDF分数
    keyword_match_score: float = 0.0      # 关键词匹配分数


@dataclass
class SearchConditionConfig:
    """搜索条件配置模型"""
    dr07: Optional[str] = None             # 数据需求编号
    version: Optional[str] = None          # 版本
    additional_filters: Dict[str, Any] = field(default_factory=dict)  # 额外筛选条件
    enable_dynamic_search: bool = True     # 启用动态搜索
    search_strategy: str = "comprehensive"  # 搜索策略（comprehensive/focused/broad）


@dataclass
class KeywordExtractionResult:
    """关键词提取结果模型"""
    dr09_keywords: List[str] = field(default_factory=list)      # dr09关键词
    dr17_keywords: List[str] = field(default_factory=list)      # dr17关键词
    combined_keywords: List[str] = field(default_factory=list)  # 合并关键词
    all_keywords: List[str] = field(default_factory=list)       # 所有关键词
    extraction_method: str = "jieba"       # 提取方法
    extraction_time_ms: float = 0.0        # 提取耗时


@dataclass
class BatchAssignmentRequest:
    """批量部门分配请求模型"""
    report_code: str                       # 报表代码，格式: {dr07}_beta_{version}

    def validate(self) -> None:
        """验证请求参数"""
        if not self.report_code or not self.report_code.strip():
            raise ValueError("report_code不能为空")

        # 验证report_code格式
        parts = self.report_code.split('_beta_')
        if len(parts) != 2:
            raise ValueError("report_code格式错误，应为: {dr07}_beta_{version}")

        dr07, version = parts
        if not dr07.strip() or not version.strip():
            raise ValueError("dr07和version不能为空")

    def get_dr07_and_version(self) -> tuple[str, str]:
        """从report_code中提取dr07和version"""
        parts = self.report_code.split('_beta_')
        return parts[0].strip(), parts[1].strip()


@dataclass
class BatchAssignmentItem:
    """批量分配结果项模型"""
    entry_id: str                          # 填报项ID (submission_id)
    entry_type: str                        # 填报项类型（使用搜索项的submission_type）
    DR22: List[str] = field(default_factory=list)  # 推荐的业务部门ID数组
    BDR01: List[str] = field(default_factory=list) # 与DR22相同的部门ID数组
    BDR03: str = ""                        # 置空字段，可以是空字符串

    def __post_init__(self):
        """后处理：确保BDR01与DR22保持一致"""
        if self.DR22 and not self.BDR01:
            self.BDR01 = self.DR22.copy()


@dataclass
class BatchAssignmentResponse:
    """批量部门分配响应模型"""
    report_code: str                       # 报表代码
    items: List[BatchAssignmentItem] = field(default_factory=list)  # 分配结果项列表

    def add_item(self, item: BatchAssignmentItem) -> None:
        """添加分配结果项"""
        self.items.append(item)

    def get_total_count(self) -> int:
        """获取总项目数"""
        return len(self.items)


@dataclass
class BatchProcessingResult:
    """批量处理结果模型（内部使用）"""
    report_code: str                       # 报表代码
    dr07: str                             # 数据需求编号
    version: str                          # 版本号

    # 处理统计
    total_records: int = 0                 # 总记录数
    processed_records: int = 0             # 已处理记录数
    successful_assignments: int = 0        # 成功分配数
    failed_assignments: int = 0            # 失败分配数

    # 处理结果
    assignment_items: List[BatchAssignmentItem] = field(default_factory=list)

    # 处理信息
    processing_time_ms: float = 0.0       # 处理耗时
    error_messages: List[str] = field(default_factory=list)  # 错误信息

    def add_assignment_item(self, item: BatchAssignmentItem) -> None:
        """添加分配结果项"""
        self.assignment_items.append(item)
        self.successful_assignments += 1

    def add_error(self, error_msg: str) -> None:
        """添加错误信息"""
        self.error_messages.append(error_msg)
        self.failed_assignments += 1

    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.processed_records == 0:
            return 0.0
        return self.successful_assignments / self.processed_records

    def to_response(self) -> BatchAssignmentResponse:
        """转换为API响应格式"""
        return BatchAssignmentResponse(
            report_code=self.report_code,
            items=self.assignment_items
        )


@dataclass
class SearchStrategy:
    """搜索策略配置"""
    
    enable_exact_match: bool = True        # 启用精确匹配
    enable_hybrid_search: bool = True      # 启用混合搜索
    exact_match_priority: bool = True      # 精确匹配优先
    hybrid_search_limit: int = 100        # 混合搜索限制
    min_similarity_threshold: float = 0.3  # 最小相似度阈值

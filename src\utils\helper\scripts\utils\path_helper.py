#!/usr/bin/env python3
"""
Scripts路径处理工具

为scripts目录下的所有脚本提供统一的路径处理，
使其能够正确导入src目录下的模块。

使用方法：
    from scripts.utils.path_helper import setup_src_path
    setup_src_path()
    
    # 现在可以正常导入src模块了
    from modules.knowledge.dd import DDCrud
    from service import get_client
"""

import sys
from pathlib import Path


def setup_src_path():
    """
    设置src目录到Python路径
    
    自动检测项目结构并添加src目录到sys.path，
    使scripts可以导入src下的所有模块。
    """
    # 获取当前脚本的路径
    current_file = Path(__file__).resolve()
    
    # 从scripts/utils/path_helper.py向上找到项目根目录
    project_root = current_file.parent.parent.parent
    src_dir = project_root / "src"
    
    # 验证src目录存在
    if not src_dir.exists():
        raise FileNotFoundError(f"找不到src目录: {src_dir}")
    
    # 添加src目录到Python路径
    src_path = str(src_dir)
    if src_path not in sys.path:
        sys.path.insert(0, src_path)
        print(f"✅ 已添加src路径: {src_path}")
    
    return src_dir


def get_project_root():
    """获取项目根目录路径"""
    current_file = Path(__file__).resolve()
    return current_file.parent.parent.parent


def get_src_dir():
    """获取src目录路径"""
    return get_project_root() / "src"


def get_config_dir():
    """获取config目录路径"""
    return get_project_root() / "config"


# 为了方便使用，提供一个快捷函数
def init_script_env():
    """
    初始化脚本环境
    
    设置所有必要的路径和环境变量，
    让脚本可以像在src目录内一样工作。
    """
    src_dir = setup_src_path()
    
    # 设置一些有用的环境变量
    import os
    os.environ["PROJECT_ROOT"] = str(get_project_root())
    os.environ["SRC_DIR"] = str(src_dir)
    os.environ["CONFIG_DIR"] = str(get_config_dir())
    
    print(f"🚀 脚本环境初始化完成")
    print(f"   项目根目录: {get_project_root()}")
    print(f"   源码目录: {src_dir}")
    print(f"   配置目录: {get_config_dir()}")


if __name__ == "__main__":
    # 测试路径设置
    init_script_env()
    
    # 测试导入
    try:
        print("\n🧪 测试模块导入:")
        
        # 测试service导入
        from service import get_client
        print("✅ service模块导入成功")
        
        # 测试utils导入  
        from utils.common.logger import setup_enterprise_logger
        print("✅ utils模块导入成功")
        
        # 测试modules导入
        from modules.knowledge.dd import DDCrud
        print("✅ modules模块导入成功")
        
        print("\n🎉 所有模块导入测试通过！")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        sys.exit(1) 
"""
API响应模型定义

提供统一的API响应模型，包含合适的示例数据，
用于生成正确的OpenAPI文档。
"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")


class StandardResponse(BaseResponse):
    """标准响应模型"""
    data: Optional[Any] = Field(default=None, description="响应数据")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "操作成功",
                "data": {}
            }
        }


class ListResponse(BaseResponse):
    """列表响应模型"""
    data: Dict[str, Any] = Field(..., description="列表数据")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "查询成功",
                "data": {
                    "items": [],
                    "total": 0,
                    "page": 1,
                    "page_size": 20,
                    "total_pages": 0
                }
            }
        }


class CreateResponse(BaseResponse):
    """创建响应模型"""
    data: Dict[str, Any] = Field(..., description="创建结果")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "创建成功",
                "data": {
                    "id": 1
                }
            }
        }


class UpdateResponse(BaseResponse):
    """更新响应模型"""
    data: Dict[str, Any] = Field(..., description="更新结果")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "更新成功",
                "data": {
                    "id": 1
                }
            }
        }


class DeleteResponse(BaseResponse):
    """删除响应模型"""
    data: Dict[str, Any] = Field(..., description="删除结果")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "删除成功",
                "data": {
                    "id": 1
                }
            }
        }


class DetailResponse(BaseResponse):
    """详情响应模型"""
    data: Dict[str, Any] = Field(..., description="详情数据")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "查询成功",
                "data": {
                    "id": 1,
                    "name": "示例名称",
                    "create_time": "2024-01-01T10:00:00",
                    "update_time": "2024-01-01T10:00:00"
                }
            }
        }


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    data: Optional[Any] = Field(default=None, description="错误详情")

    class Config:
        schema_extra = {
            "example": {
                "success": False,
                "message": "操作失败",
                "data": None
            }
        }


class BatchResponse(BaseResponse):
    """批量操作响应模型"""
    data: Dict[str, Any] = Field(..., description="批量操作结果")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "批量操作完成",
                "data": {
                    "total": 10,
                    "success": 8,
                    "failed": 2,
                    "results": []
                }
            }
        }


class StatisticsResponse(BaseResponse):
    """统计响应模型"""
    data: Dict[str, Any] = Field(..., description="统计数据")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取统计信息成功",
                "data": {
                    "total_databases": 5,
                    "total_tables": 50,
                    "total_columns": 500,
                    "active_items": 480,
                    "inactive_items": 20
                }
            }
        }


class InfoResponse(BaseResponse):
    """信息响应模型"""
    data: Dict[str, Any] = Field(..., description="信息数据")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取信息成功",
                "data": {
                    "api_name": "API名称",
                    "version": "1.0.0",
                    "description": "API描述",
                    "supported_operations": []
                }
            }
        }


# 特定业务响应模型

class ColumnRelationListResponse(ListResponse):
    """列关联列表响应模型"""
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取列关联列表成功",
                "data": {
                    "items": [
                        {
                            "relation_id": 1,
                            "source_db_name": "CORE_DB",
                            "source_table_name": "customer_info",
                            "source_column_name": "customer_id",
                            "target_db_name": "CORE_DB",
                            "target_table_name": "account_info",
                            "target_column_name": "customer_id",
                            "relation_type": "FK",
                            "comment": "客户信息表与账户信息表的客户ID关联",
                            "create_time": "2024-01-01T10:00:00",
                            "update_time": "2024-01-01T10:00:00"
                        }
                    ],
                    "total": 1,
                    "page": 1,
                    "page_size": 20,
                    "total_pages": 1
                }
            }
        }


class ColumnRelationCreateResponse(CreateResponse):
    """列关联创建响应模型"""
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "创建列关联成功",
                "data": {
                    "source_column_id": 123,
                    "target_column_id": 456,
                    "relation_type": "FK"
                }
            }
        }


class ColumnRelationDetailResponse(DetailResponse):
    """列关联详情响应模型"""
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取列关联详情成功",
                "data": {
                    "relation_id": 1,
                    "source_db_name": "CORE_DB",
                    "source_table_name": "customer_info",
                    "source_column_name": "customer_id",
                    "target_db_name": "CORE_DB",
                    "target_table_name": "account_info",
                    "target_column_name": "customer_id",
                    "relation_type": "FK",
                    "comment": "客户信息表与账户信息表的客户ID关联",
                    "create_time": "2024-01-01T10:00:00",
                    "update_time": "2024-01-01T10:00:00"
                }
            }
        }


class DatabaseListResponse(ListResponse):
    """数据库列表响应模型"""
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取数据库列表成功",
                "data": {
                    "items": [
                        {
                            "db_id": 1,
                            "knowledge_id": "********-1234-1234-1234-************",
                            "db_name": "CORE_DB",
                            "db_name_cn": "核心数据库",
                            "data_layer": "ods",
                            "db_desc": "核心业务数据库",
                            "is_active": True,
                            "create_time": "2024-01-01T10:00:00",
                            "update_time": "2024-01-01T10:00:00"
                        }
                    ],
                    "total": 1,
                    "page": 1,
                    "page_size": 20,
                    "total_pages": 1
                }
            }
        }


class TableListResponse(ListResponse):
    """表列表响应模型"""
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取表列表成功",
                "data": {
                    "items": [
                        {
                            "table_id": 1,
                            "knowledge_id": "********-1234-1234-1234-************",
                            "db_id": 1,
                            "table_name": "customer_info",
                            "table_name_cn": "客户信息表",
                            "table_desc": "存储客户基本信息",
                            "is_active": True,
                            "create_time": "2024-01-01T10:00:00",
                            "update_time": "2024-01-01T10:00:00"
                        }
                    ],
                    "total": 1,
                    "page": 1,
                    "page_size": 20,
                    "total_pages": 1
                }
            }
        }


class ColumnListResponse(ListResponse):
    """字段列表响应模型"""
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取字段列表成功",
                "data": {
                    "items": [
                        {
                            "column_id": 1,
                            "knowledge_id": "********-1234-1234-1234-************",
                            "table_id": 1,
                            "column_name": "customer_id",
                            "column_name_cn": "客户ID",
                            "column_desc": "客户唯一标识",
                            "data_type": "VARCHAR(32)",
                            "is_primary_key": True,
                            "is_vectorized": False,
                            "create_time": "2024-01-01T10:00:00",
                            "update_time": "2024-01-01T10:00:00"
                        }
                    ],
                    "total": 1,
                    "page": 1,
                    "page_size": 20,
                    "total_pages": 1
                }
            }
        }

"""
文档管理API

提供文档的CRUD操作，包括：
1. 文档上传（支持文件上传和解析）
2. 文档列表查询（支持分页和筛选）
3. 文档详情查询
4. 文档更新
5. 文档删除
6. 文档搜索（按名称模糊搜索）
"""

from typing import List, Optional, Any
from fastapi import APIRouter, Query, Depends, UploadFile, File, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from loguru import logger

from modules.doc.operations.doc_ops import DocumentOperations
from modules.knowledge.doc.entities import (
    APIResponse, Document, CreateDocumentRequest, UpdateDocumentRequest
)


# 创建路由器
router = APIRouter(prefix="/knowledge/doc/documents", tags=["文档管理"])


# 请求/响应模型
class DocumentUploadRequest(BaseModel):
    """文档上传请求"""
    doc_name: Optional[str] = Field(None, description="文档名称（可选，不提供则使用文件名）")
    parse_type: str = Field(..., description="解析方式：chunk_normal, chunk_detail等")
    doc_type: str = Field(default="inside", description="文档类型：inside-内部，outside-外部")
    author: Optional[str] = Field(None, description="文档作者")


class DocumentListRequest(BaseModel):
    """文档列表查询请求"""
    knowledge_id: str = Field(..., description="知识库ID")
    user_id: str = Field(..., description="用户ID")
    page: int = Field(default=1, ge=1, description="页码（从1开始）")
    page_size: int = Field(default=20, ge=1, le=100, description="每页大小（1-100）")
    status: Optional[str] = Field(None, description="文档状态筛选")
    doc_type: Optional[str] = Field(None, description="文档类型筛选")


class DocumentResponse(BaseModel):
    """文档响应"""
    doc_id: str
    user_id: str
    knowledge_id: str
    doc_name: str
    doc_type: str
    author: Optional[str] = None
    vector_similarity_weight: float
    similarity_threshold: float
    chunk_nums: int
    parse_type: str
    status: str
    location: str
    doc_format: str
    metadata: Optional[str] = None
    percentage: float
    parse_end_time: Optional[str] = None
    parse_message: Optional[str] = None
    task_id: Optional[str] = None
    doc_ocr_result_path: Optional[str] = None
    is_active: bool
    create_time: Optional[str] = None


class DocumentListResponse(BaseModel):
    """文档列表响应"""
    success: bool = True
    message: str = "查询成功"
    data: List[DocumentResponse]
    total: int
    page: int
    page_size: int


class DocumentDetailResponse(BaseModel):
    """文档详情响应"""
    success: bool = True
    message: str = "查询成功"
    data: DocumentResponse


class DocumentSearchRequest(BaseModel):
    """文档搜索请求"""
    knowledge_id: str = Field(..., description="知识库ID")
    user_id: str = Field(..., description="用户ID")
    query: str = Field(..., min_length=1, description="搜索关键词")


class DocumentSearchResponse(BaseModel):
    """文档搜索响应"""
    success: bool = True
    message: str = "搜索成功"
    data: List[DocumentResponse]
    total: int


class StandardResponse(BaseModel):
    """标准响应"""
    success: bool
    message: str
    data: Optional[Any] = None


class ErrorResponse(BaseModel):
    """错误响应"""
    success: bool = False
    message: str
    data: Optional[Any] = None


def get_document_operations():
    """获取文档操作实例"""
    return DocumentOperations()


def create_error_response(message: str, status_code: int = 500) -> JSONResponse:
    """创建统一格式的错误响应"""
    return JSONResponse(
        status_code=status_code,
        content=ErrorResponse(message=message).model_dump()
    )


@router.post("/upload", response_model=StandardResponse)
async def upload_document(
    file: UploadFile = File(...),
    knowledge_id: str = Form(...),
    user_id: str = Form(...),
    doc_name: Optional[str] = Form(None),
    parse_type: str = Form(...),
    doc_type: str = Form(default="inside"),
    author: Optional[str] = Form(None),
    ops: DocumentOperations = Depends(get_document_operations)
):
    """
    上传文档并解析

    支持上传各种格式的文档（PDF、Word、TXT等），自动进行解析和分块处理。

    **支持的解析方式：**
    - chunk_normal：普通分块解析
    - chunk_detail：详细分块解析

    **文档类型：**
    - inside：内部文档
    - outside：外部文档

    **示例：**
    ```
    POST /knowledge/doc/documents/upload
    Content-Type: multipart/form-data

    file: <文件数据>
    knowledge_id: "kb_123"
    user_id: "user_123"
    doc_name: "技术文档.pdf"
    parse_type: "chunk_normal"
    doc_type: "inside"
    author: "张三"
    ```
    """
    try:
        # 验证文件
        if not file.filename:
            return create_error_response("文件名不能为空", 400)
        
        # 使用文件名作为默认文档名
        if not doc_name:
            doc_name = file.filename
        
        # 调用文档操作服务
        result = await ops.upload_and_parse_document(
            user_id=user_id,
            knowledge_id=knowledge_id,
            file=file,
            parse_type=parse_type,
            embedding_types=["content"]  # 默认对内容进行向量化
        )
        
        if result.get("status") == "success":
            return StandardResponse(
                success=True,
                message="文档上传成功",
                data={
                    "doc_id": result.get("doc_id"),
                    "task_id": result.get("task_id"),
                    "status": result.get("doc_status", "processing")
                }
            )
        else:
            return create_error_response(result.get("message", "文档上传失败"), 500)
        
    except Exception as e:
        logger.error(f"文档上传失败: {str(e)}")
        return create_error_response(f"文档上传失败: {str(e)}", 500)


@router.get("/", response_model=DocumentListResponse)
async def list_documents(
    knowledge_id: str = Query(..., description="知识库ID"),
    user_id: str = Query(..., description="用户ID"),
    page: int = Query(1, ge=1, description="页码（从1开始）"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小（1-100）"),
    status: Optional[str] = Query(None, description="文档状态筛选"),
    doc_type: Optional[str] = Query(None, description="文档类型筛选"),
    ops: DocumentOperations = Depends(get_document_operations)
):
    """
    查询文档列表

    支持按状态和类型进行筛选，支持分页查询。

    **文档状态：**
    - pending：待处理
    - processing：处理中
    - success：处理成功
    - failed：处理失败

    **文档类型：**
    - inside：内部文档
    - outside：外部文档
    """
    try:
        result = await ops.get_document_list(
            user_id=user_id,
            knowledge_id=knowledge_id,
            page=page,
            page_size=page_size
        )
        
        if result.get("status") == "success":
            documents = result.get("data", [])
            total = result.get("total", 0)
            
            # 转换为响应格式并应用过滤
            filtered_documents = []
            for doc in documents:
                # 状态过滤
                if status and doc.get("status") != status:
                    continue
                
                # 类型过滤
                if doc_type and doc.get("doc_type") != doc_type:
                    continue
                
                doc_response = DocumentResponse(
                    doc_id=doc["doc_id"],
                    user_id=doc["user_id"],
                    knowledge_id=doc["knowledge_id"],
                    doc_name=doc["doc_name"],
                    doc_type=doc.get("doc_type", "inside"),
                    author=doc.get("author"),
                    vector_similarity_weight=doc.get("vector_similarity_weight", 1.0),
                    similarity_threshold=doc.get("similarity_threshold", 0.5),
                    chunk_nums=doc.get("chunk_nums", 0),
                    parse_type=doc["parse_type"],
                    status=doc["status"],
                    location=doc["location"],
                    doc_format=doc["doc_format"],
                    metadata=doc.get("metadata"),
                    percentage=doc.get("percentage", 0.0),
                    parse_end_time=doc.get("parse_end_time"),
                    parse_message=doc.get("parse_message"),
                    task_id=doc.get("task_id"),
                    doc_ocr_result_path=doc.get("doc_ocr_result_path"),
                    is_active=doc.get("is_active", True),
                    create_time=doc.get("create_time")
                )
                filtered_documents.append(doc_response)
            
            return DocumentListResponse(
                data=filtered_documents,
                total=len(filtered_documents),  # 注意：这里是过滤后的数量
                page=page,
                page_size=page_size
            )
        else:
            return create_error_response(result.get("message", "查询文档列表失败"), 500)
        
    except Exception as e:
        logger.error(f"查询文档列表失败: {str(e)}")
        return create_error_response(f"查询文档列表失败: {str(e)}", 500)


@router.get("/{doc_id}", response_model=DocumentDetailResponse)
async def get_document_detail(
    doc_id: str,
    user_id: str = Query(..., description="用户ID"),
    ops: DocumentOperations = Depends(get_document_operations)
):
    """
    获取文档详情

    根据文档ID获取详细信息。
    """
    try:
        document = await ops.get_document_detail(user_id=user_id, doc_id=doc_id)
        
        if not document:
            return create_error_response("文档不存在", 404)
        
        doc_response = DocumentResponse(
            doc_id=document.doc_id,
            user_id=document.user_id,
            knowledge_id=document.knowledge_id,
            doc_name=document.doc_name,
            doc_type=document.doc_type,
            author=document.author,
            vector_similarity_weight=document.vector_similarity_weight,
            similarity_threshold=document.similarity_threshold,
            chunk_nums=document.chunk_nums,
            parse_type=document.parse_type,
            status=document.status,
            location=document.location,
            doc_format=document.doc_format,
            metadata=document.metadata,
            percentage=document.percentage,
            parse_end_time=document.parse_end_time.isoformat() if document.parse_end_time else None,
            parse_message=document.parse_message,
            task_id=document.task_id,
            doc_ocr_result_path=document.doc_ocr_result_path,
            is_active=document.is_active,
            create_time=document.create_time.isoformat() if document.create_time else None
        )
        
        return DocumentDetailResponse(data=doc_response)
        
    except Exception as e:
        logger.error(f"获取文档详情失败: {doc_id}, 错误: {str(e)}")
        return create_error_response(f"获取文档详情失败: {str(e)}", 500)


@router.put("/{doc_id}", response_model=StandardResponse)
async def update_document(
    doc_id: str,
    request: UpdateDocumentRequest,
    user_id: str = Query(..., description="用户ID"),
    ops: DocumentOperations = Depends(get_document_operations)
):
    """
    更新文档信息
    
    可以更新文档的名称、作者、相似度参数等信息。
    """
    try:
        result = await ops.update_document(
            user_id=user_id,
            doc_id=doc_id,
            request=request
        )
        
        if result.get("status") == "success":
            return StandardResponse(
                success=True,
                message="文档更新成功"
            )
        else:
            return create_error_response(result.get("message", "文档更新失败"), 400)
        
    except Exception as e:
        logger.error(f"更新文档失败: {doc_id}, 错误: {str(e)}")
        return create_error_response(f"更新文档失败: {str(e)}", 500)


@router.delete("/{doc_id}", response_model=StandardResponse)
async def delete_document(
    doc_id: str,
    user_id: str = Query(..., description="用户ID"),
    ops: DocumentOperations = Depends(get_document_operations)
):
    """
    删除文档

    删除指定的文档，相关的分块和向量数据也会被级联删除。
    """
    try:
        result = await ops.delete_document(user_id=user_id, doc_id=doc_id)
        
        if result.get("status") == "success":
            return StandardResponse(
                success=True,
                message="文档删除成功"
            )
        else:
            return create_error_response(result.get("message", "文档删除失败"), 400)
        
    except Exception as e:
        logger.error(f"删除文档失败: {doc_id}, 错误: {str(e)}")
        return create_error_response(f"删除文档失败: {str(e)}", 500)


@router.post("/search", response_model=DocumentSearchResponse)
async def search_documents(
    request: DocumentSearchRequest,
    ops: DocumentOperations = Depends(get_document_operations)
):
    """
    搜索文档

    根据文档名称进行模糊搜索。
    """
    try:
        result = await ops.search_documents(
            user_id=request.user_id,
            knowledge_id=request.knowledge_id,
            query=request.query,
            top_k=50  # 搜索结果上限
        )
        
        if result.get("status") == "success":
            documents = result.get("data", [])
            
            # 转换为响应格式
            doc_responses = []
            for doc in documents:
                doc_response = DocumentResponse(
                    doc_id=doc["doc_id"],
                    user_id=doc["user_id"],
                    knowledge_id=doc["knowledge_id"],
                    doc_name=doc["doc_name"],
                    doc_type=doc.get("doc_type", "inside"),
                    author=doc.get("author"),
                    vector_similarity_weight=doc.get("vector_similarity_weight", 1.0),
                    similarity_threshold=doc.get("similarity_threshold", 0.5),
                    chunk_nums=doc.get("chunk_nums", 0),
                    parse_type=doc["parse_type"],
                    status=doc["status"],
                    location=doc["location"],
                    doc_format=doc["doc_format"],
                    metadata=doc.get("metadata"),
                    percentage=doc.get("percentage", 0.0),
                    parse_end_time=doc.get("parse_end_time"),
                    parse_message=doc.get("parse_message"),
                    task_id=doc.get("task_id"),
                    doc_ocr_result_path=doc.get("doc_ocr_result_path"),
                    is_active=doc.get("is_active", True),
                    create_time=doc.get("create_time")
                )
                doc_responses.append(doc_response)
            
            return DocumentSearchResponse(
                data=doc_responses,
                total=len(doc_responses)
            )
        else:
            return create_error_response(result.get("message", "搜索文档失败"), 500)
        
    except Exception as e:
        logger.error(f"搜索文档失败: {request.query}, 错误: {str(e)}")
        return create_error_response(f"搜索文档失败: {str(e)}", 500)


@router.get("/{doc_id}/chunks", response_model=StandardResponse)
async def get_document_chunks(
    doc_id: str,
    user_id: str = Query(..., description="用户ID"),
    ops: DocumentOperations = Depends(get_document_operations)
):
    """
    获取文档的分块列表

    返回指定文档的所有分块信息。
    """
    try:
        result = await ops.get_document_chunks(user_id=user_id, doc_id=doc_id)
        
        if result.get("status") == "success":
            return StandardResponse(
                success=True,
                message="查询成功",
                data=result.get("data", [])
            )
        else:
            return create_error_response(result.get("message", "查询文档分块失败"), 500)
        
    except Exception as e:
        logger.error(f"查询文档分块失败: {doc_id}, 错误: {str(e)}")
        return create_error_response(f"查询文档分块失败: {str(e)}", 500) 
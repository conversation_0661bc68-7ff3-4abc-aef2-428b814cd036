"""
传统排序算法实现

本模块包含：
1. RRF (Reciprocal Rank Fusion) 排序
2. 加权排序 
3. 混合加权排序

设计原则：
- 统一的输入输出接口，兼容rerank模型的参数格式
- 保持与老算法的逻辑一致性
- 支持灵活的配置参数
"""

from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field
from collections import defaultdict
from loguru import logger
import json


@dataclass
class RankResult:
    """排序结果"""
    query: str
    docs: List[Dict[str, Any]]
    scores: List[float]
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass 
class RankConfig:
    """排序配置基类"""
    score_threshold: Optional[float] = None
    top_n: Optional[int] = None
    
    
@dataclass
class RRFConfig(RankConfig):
    """RRF排序配置"""
    k: int = 60  # RRF参数k


@dataclass  
class WeightedConfig(RankConfig):
    """加权排序配置"""
    weights: Dict[str, float] = field(default_factory=dict)  # {field_name: weight}


@dataclass
class HybridWeightedConfig(RankConfig):
    """混合加权排序配置"""
    weight_combinations: List[Dict[str, float]] = field(default_factory=list)  # 多组权重配置
    intermediate_top_k: int = 10  # 每组权重的中间结果数量


class BaseRanker:
    """排序器基类"""
    
    def __init__(self, config: RankConfig):
        self.config = config
        
    def rank(self, query: str, docs: List[Dict[str, Any]], **kwargs) -> RankResult:
        """
        执行排序
        
        Args:
            query: 查询文本
            docs: 文档列表，每个文档应包含搜索结果和distance等信息
            **kwargs: 额外参数
            
        Returns:
            RankResult: 排序结果
        """
        raise NotImplementedError
        
    def _apply_threshold_and_limit(self, results: List[Dict[str, Any]], scores: List[float]) -> tuple:
        """应用分数阈值和数量限制"""
        # 应用分数阈值
        if self.config.score_threshold is not None:
            filtered_results = []
            filtered_scores = []
            for result, score in zip(results, scores):
                if score >= self.config.score_threshold:
                    filtered_results.append(result)
                    filtered_scores.append(score)
            results, scores = filtered_results, filtered_scores
        
        # 应用数量限制
        if self.config.top_n is not None:
            results = results[:self.config.top_n]
            scores = scores[:self.config.top_n]
            
        return results, scores


class RRFRanker(BaseRanker):
    """RRF (Reciprocal Rank Fusion) 排序器"""
    
    def __init__(self, config: RRFConfig):
        super().__init__(config)
        self.k = config.k
        
    def rank(self, query: str, docs: List[Dict[str, Any]], **kwargs) -> RankResult:
        """
        执行RRF排序
        
        Args:
            query: 查询文本 
            docs: 文档列表，每个文档包含多个源的搜索结果
            **kwargs: 额外参数，可包含data_dict格式的分组数据
            
        Returns:
            RankResult: RRF排序结果
        """
        try:
            logger.debug(f"开始RRF排序: query='{query}', docs_count={len(docs)}")
            
            # 如果传入的是data_dict格式（兼容老接口）
            data_dict = kwargs.get('data_dict')
            if data_dict:
                return self._rank_from_data_dict(query, data_dict)
            
            # 新格式：docs是单一的结果列表
            if not docs:
                return RankResult(query=query, docs=[], scores=[], metadata={"status": "empty"})
            
            # 假设docs中每个文档都有一个rank信息，或者我们需要从不同源合并
            # 这里需要根据实际数据结构调整
            logger.warning("RRF排序的新格式实现需要根据具体数据结构调整")
            
            # 暂时返回原始排序
            scores = [1.0 / (i + 1) for i in range(len(docs))]  # 简单的递减分数
            results, scores = self._apply_threshold_and_limit(docs, scores)
            
            return RankResult(
                query=query,
                docs=results,
                scores=scores,
                metadata={"algorithm": "rrf", "k": self.k}
            )
            
        except Exception as e:
            logger.error(f"RRF排序失败: {str(e)}")
            return RankResult(
                query=query,
                docs=[],
                scores=[],
                metadata={"status": "error", "error": str(e)}
            )
    
    def _rank_from_data_dict(self, query: str, data_dict: Dict[str, List[Dict]]) -> RankResult:
        """从data_dict格式执行RRF排序（兼容老接口）"""
        try:
            # 获取分组键
            group_by = self._get_group_by_keys(data_dict)
            if not group_by:
                return RankResult(query=query, docs=[], scores=[], metadata={"status": "no_group_keys"})
            
            # 构建排序列表
            ranked_lists = []
            item_to_dict = {}
            
            for col, points in data_dict.items():
                ranked_list = []
                for point in points:
                    key = tuple(point[k] for k in group_by)
                    ranked_list.append(key)
                    item_to_dict[key] = point
                ranked_lists.append(ranked_list)
            
            # 计算RRF分数
            all_items = set().union(*ranked_lists)
            scores = {}
            distances = defaultdict(list)
            
            for item in all_items:
                rrf_score = sum(
                    1 / (self.k + ranked_list.index(item) + 1)
                    for ranked_list in ranked_lists if item in ranked_list
                )
                scores[item] = rrf_score
                distances[item] = [
                    item_to_dict[item]['distance'] 
                    for _ in range(sum(1 for rl in ranked_lists if item in rl))
                ]
            
            # 构建结果
            results = []
            result_scores = []
            for item in all_items:
                rrf_score = scores[item]
                avg_distance = sum(distances[item]) / len(distances[item]) if distances[item] else 1.0
                combined_score = avg_distance / rrf_score if rrf_score > 0 else float('inf')
                
                point_dict = item_to_dict[item].copy()
                point_dict['distance'] = combined_score
                point_dict['rrf_score'] = rrf_score
                point_dict['avg_distance'] = avg_distance
                
                results.append(point_dict)
                result_scores.append(rrf_score)  # 使用RRF分数作为最终分数
            
            # 按组合分数升序排序（距离越小越好）
            sorted_pairs = sorted(zip(results, result_scores), key=lambda x: x[0]['distance'])
            results = [pair[0] for pair in sorted_pairs]
            result_scores = [pair[1] for pair in sorted_pairs]
            
            # 应用阈值和限制
            results, result_scores = self._apply_threshold_and_limit(results, result_scores)
            
            logger.info(f"RRF排序完成: 原始={len(all_items)}, 最终={len(results)}")
            
            return RankResult(
                query=query,
                docs=results,
                scores=result_scores,
                metadata={
                    "algorithm": "rrf",
                    "k": self.k,
                    "original_count": len(all_items),
                    "group_by": group_by
                }
            )
            
        except Exception as e:
            logger.error(f"RRF排序(data_dict)失败: {str(e)}")
            return RankResult(
                query=query,
                docs=[],
                scores=[],
                metadata={"status": "error", "error": str(e)}
            )
    
    def _get_group_by_keys(self, data_dict: Dict[str, List[Dict]]) -> List[str]:
        """获取分组键"""
        for col, points in data_dict.items():
            if points:
                return [k for k in points[0].keys() if k != 'distance']
        return []


class WeightedRanker(BaseRanker):
    """加权排序器"""
    
    def __init__(self, config: WeightedConfig):
        super().__init__(config)
        self.weights = config.weights
        
    def rank(self, query: str, docs: List[Dict[str, Any]], **kwargs) -> RankResult:
        """
        执行加权排序
        
        Args:
            query: 查询文本
            docs: 文档列表
            **kwargs: 额外参数，可包含data_dict格式的分组数据
            
        Returns:
            RankResult: 加权排序结果
        """
        try:
            logger.debug(f"开始加权排序: query='{query}', docs_count={len(docs)}")
            
            # 如果传入的是data_dict格式（兼容老接口）
            data_dict = kwargs.get('data_dict')
            if data_dict:
                return self._rank_from_data_dict(query, data_dict)
            
            # 新格式处理
            if not docs:
                return RankResult(query=query, docs=[], scores=[], metadata={"status": "empty"})
            
            # 简单的加权处理（需要根据实际需求调整）
            logger.warning("加权排序的新格式实现需要根据具体数据结构调整")
            
            scores = [1.0 for _ in docs]  # 默认分数
            results, scores = self._apply_threshold_and_limit(docs, scores)
            
            return RankResult(
                query=query,
                docs=results,
                scores=scores,
                metadata={"algorithm": "weighted", "weights": self.weights}
            )
            
        except Exception as e:
            logger.error(f"加权排序失败: {str(e)}")
            return RankResult(
                query=query,
                docs=[],
                scores=[],
                metadata={"status": "error", "error": str(e)}
            )
    
    def _rank_from_data_dict(self, query: str, data_dict: Dict[str, List[Dict]]) -> RankResult:
        """从data_dict格式执行加权排序（兼容老接口）"""
        try:
            # 获取分组键
            group_by = self._get_group_by_keys(data_dict)
            if not group_by:
                return RankResult(query=query, docs=[], scores=[], metadata={"status": "no_group_keys"})
            
            # 转换权重格式
            weights = {col: float(self.weights[col]) for col in self.weights if col != 'k'}
            all_cols = set(weights.keys())
            
            # 分组处理
            grouped = defaultdict(list)
            for col, points in data_dict.items():
                if col in weights:
                    for point in points:
                        key = tuple(point[k] for k in group_by)
                        grouped[key].append((col, point['distance']))
            
            # 计算加权分数
            results = []
            result_scores = []
            
            for key, existing in grouped.items():
                existing_sum = sum(weights[col] * d_i for col, d_i in existing)
                existing_cols = {col for col, _ in existing}
                missing_sum = sum(weights.get(col, 0) for col in all_cols - existing_cols)
                combined_d = existing_sum + missing_sum
                
                # 构建结果点
                point_dict = {k: v for k, v in zip(group_by, key)}
                point_dict['distance'] = combined_d
                
                results.append(point_dict)
                result_scores.append(1.0 / (1.0 + combined_d))  # 转换为分数（距离越小分数越高）
            
            # 按距离升序排序
            sorted_pairs = sorted(zip(results, result_scores), key=lambda x: x[0]['distance'])
            results = [pair[0] for pair in sorted_pairs]
            result_scores = [pair[1] for pair in sorted_pairs]
            
            # 应用阈值和限制
            results, result_scores = self._apply_threshold_and_limit(results, result_scores)
            
            logger.info(f"加权排序完成: 结果数量={len(results)}")
            
            return RankResult(
                query=query,
                docs=results,
                scores=result_scores,
                metadata={
                    "algorithm": "weighted",
                    "weights": weights,
                    "group_by": group_by
                }
            )
            
        except Exception as e:
            logger.error(f"加权排序(data_dict)失败: {str(e)}")
            return RankResult(
                query=query,
                docs=[],
                scores=[],
                metadata={"status": "error", "error": str(e)}
            )
    
    def _get_group_by_keys(self, data_dict: Dict[str, List[Dict]]) -> List[str]:
        """获取分组键"""
        for col, points in data_dict.items():
            if points:
                return [k for k in points[0].keys() if k != 'distance']
        return []


class HybridWeightedRanker(BaseRanker):
    """混合加权排序器"""
    
    def __init__(self, config: HybridWeightedConfig):
        super().__init__(config)
        self.weight_combinations = config.weight_combinations
        self.intermediate_top_k = config.intermediate_top_k
        
    def rank(self, query: str, docs: List[Dict[str, Any]], **kwargs) -> RankResult:
        """
        执行混合加权排序
        
        Args:
            query: 查询文本
            docs: 文档列表
            **kwargs: 额外参数，可包含data_dict格式的分组数据
            
        Returns:
            RankResult: 混合加权排序结果
        """
        try:
            logger.debug(f"开始混合加权排序: query='{query}', docs_count={len(docs)}")
            
            # 如果传入的是data_dict格式（兼容老接口）
            data_dict = kwargs.get('data_dict')
            if data_dict:
                return self._rank_from_data_dict(query, data_dict)
            
            # 新格式处理
            if not docs:
                return RankResult(query=query, docs=[], scores=[], metadata={"status": "empty"})
            
            logger.warning("混合加权排序的新格式实现需要根据具体数据结构调整")
            
            scores = [1.0 for _ in docs]
            results, scores = self._apply_threshold_and_limit(docs, scores)
            
            return RankResult(
                query=query,
                docs=results,
                scores=scores,
                metadata={"algorithm": "hybrid_weighted", "combinations": self.weight_combinations}
            )
            
        except Exception as e:
            logger.error(f"混合加权排序失败: {str(e)}")
            return RankResult(
                query=query,
                docs=[],
                scores=[],
                metadata={"status": "error", "error": str(e)}
            )
    
    def _rank_from_data_dict(self, query: str, data_dict: Dict[str, List[Dict]]) -> RankResult:
        """从data_dict格式执行混合加权排序（兼容老接口）"""
        try:
            if not self.weight_combinations:
                raise ValueError("混合加权排序需要至少一组权重配置")
            
            # 获取分组键
            group_by = self._get_group_by_keys(data_dict)
            if not group_by:
                return RankResult(query=query, docs=[], scores=[], metadata={"status": "no_group_keys"})
            
            # 对每个权重组合执行加权排序
            intermediate_results = []
            
            for weight_dict in self.weight_combinations:
                weights = {k: float(v) for k, v in weight_dict.items()}
                all_cols = set(weights.keys())
                
                # 分组处理
                grouped = defaultdict(list)
                for col, points in data_dict.items():
                    if col in weights:
                        for point in points:
                            key = tuple(point[k] for k in group_by)
                            grouped[key].append((col, point['distance']))
                
                # 计算加权结果
                weighted_results = []
                for key, existing in grouped.items():
                    existing_sum = sum(weights[col] * d_i for col, d_i in existing)
                    existing_cols = {col for col, _ in existing}
                    missing_sum = sum(weights.get(col, 0) for col in all_cols - existing_cols)
                    combined_d = existing_sum + missing_sum
                    
                    point_dict = {k: v for k, v in zip(group_by, key)}
                    point_dict['distance'] = combined_d
                    weighted_results.append(point_dict)
                
                # 按距离升序排序并取前intermediate_top_k条
                sorted_results = sorted(weighted_results, key=lambda p: p['distance'])
                intermediate_results.extend(sorted_results[:self.intermediate_top_k])
            
            # 去重并按距离重排
            unique_results = {}
            for point in intermediate_results:
                key = tuple(point[k] for k in group_by)
                if key not in unique_results or point['distance'] < unique_results[key]['distance']:
                    unique_results[key] = point
            
            # 最终排序
            final_results = sorted(unique_results.values(), key=lambda p: p['distance'])
            final_scores = [1.0 / (1.0 + result['distance']) for result in final_results]
            
            # 应用阈值和限制  
            final_results, final_scores = self._apply_threshold_and_limit(final_results, final_scores)
            
            logger.info(f"混合加权排序完成: 中间结果={len(intermediate_results)}, 最终结果={len(final_results)}")
            
            return RankResult(
                query=query,
                docs=final_results,
                scores=final_scores,
                metadata={
                    "algorithm": "hybrid_weighted",
                    "weight_combinations": self.weight_combinations,
                    "intermediate_top_k": self.intermediate_top_k,
                    "group_by": group_by
                }
            )
            
        except Exception as e:
            logger.error(f"混合加权排序(data_dict)失败: {str(e)}")
            return RankResult(
                query=query,
                docs=[],
                scores=[],
                metadata={"status": "error", "error": str(e)}
            )
    
    def _get_group_by_keys(self, data_dict: Dict[str, List[Dict]]) -> List[str]:
        """获取分组键"""
        for col, points in data_dict.items():
            if points:
                return [k for k in points[0].keys() if k != 'distance']
        return []


# 便捷函数
def create_ranker(algorithm: str, config: Dict[str, Any]) -> BaseRanker:
    """
    创建排序器的工厂函数
    
    Args:
        algorithm: 算法类型 ('rrf', 'weighted', 'hybrid_weighted')
        config: 配置参数
        
    Returns:
        BaseRanker: 对应的排序器实例
    """
    if algorithm == 'rrf':
        return RRFRanker(RRFConfig(**config))
    elif algorithm == 'weighted':
        return WeightedRanker(WeightedConfig(**config))
    elif algorithm == 'hybrid_weighted':
        return HybridWeightedRanker(HybridWeightedConfig(**config))
    else:
        raise ValueError(f"不支持的排序算法: {algorithm}") 
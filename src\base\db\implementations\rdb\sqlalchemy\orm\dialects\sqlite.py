"""
ORM SQLite方言实现

基于Universal的SQLite方言，适配ORM的SQLAlchemy实现
"""

from typing import Dict, Any, List, Optional, Tuple
from .base import DatabaseDialect, DatabaseFeatures, FeatureSupport


class SQLiteDialect(DatabaseDialect):
    """SQLite database dialect implementation for ORM"""
    
    @property
    def name(self) -> str:
        return "sqlite"
    
    @property
    def description(self) -> str:
        return "SQLite Database (3.8+)"
    
    @property
    def default_port(self) -> Optional[int]:
        return None  # SQLite doesn't use ports
    
    @property
    def default_driver(self) -> str:
        return "pysqlite"
    
    @property
    def async_driver(self) -> Optional[str]:
        return "aiosqlite"
    
    @property
    def features(self) -> DatabaseFeatures:
        return DatabaseFeatures(
            json_support=FeatureSupport.PARTIAL,   # JSON1 extension
            array_support=FeatureSupport.NONE,     # No native array support
            window_functions=FeatureSupport.FULL,  # SQLite 3.25+
            cte_support=FeatureSupport.FULL,       # Common Table Expressions
            full_text_search=FeatureSupport.FULL,  # FTS5 extension
            partitioning=FeatureSupport.NONE,      # No native partitioning
            upsert_support=FeatureSupport.FULL,    # INSERT OR REPLACE, ON CONFLICT
            returning_clause=FeatureSupport.FULL,  # RETURNING clause (3.35+)
            bulk_insert=FeatureSupport.PARTIAL,    # Limited bulk operations
            async_support=FeatureSupport.FULL      # aiosqlite
        )
    
    def quote_identifier(self, identifier: str) -> str:
        """Quote SQLite identifier with double quotes"""
        if '"' in identifier:
            # Escape existing quotes by doubling them
            identifier = identifier.replace('"', '""')
        return f'"{identifier}"'
    
    def escape_string(self, value: str) -> str:
        """Escape SQLite string literal"""
        if "'" in value:
            # Escape single quotes by doubling them
            value = value.replace("'", "''")
        return f"'{value}'"
    
    def format_limit_offset(self, limit: Optional[int] = None, offset: Optional[int] = None) -> str:
        """Format SQLite LIMIT/OFFSET clause"""
        parts = []
        
        if limit is not None:
            parts.append(f"LIMIT {limit}")
        
        if offset is not None:
            if limit is None:
                # SQLite requires LIMIT when using OFFSET
                parts.append("LIMIT -1")
            parts.append(f"OFFSET {offset}")
        
        return " ".join(parts)
    
    def get_column_type_mapping(self) -> Dict[str, str]:
        """Get SQLite column type mapping"""
        return {
            'string': 'TEXT',
            'text': 'TEXT',
            'integer': 'INTEGER',
            'int': 'INTEGER',
            'bigint': 'INTEGER',
            'float': 'REAL',
            'double': 'REAL',
            'decimal': 'NUMERIC',
            'boolean': 'INTEGER',  # SQLite uses INTEGER for boolean
            'bool': 'INTEGER',
            'date': 'TEXT',        # SQLite stores dates as TEXT
            'datetime': 'TEXT',
            'timestamp': 'TEXT',
            'time': 'TEXT',
            'json': 'TEXT',        # JSON stored as TEXT
            'blob': 'BLOB',
            'binary': 'BLOB'
        }
    
    def supports_feature(self, feature: str) -> bool:
        """Check if SQLite supports a specific feature"""
        supported_features = {
            'transactions': True,
            'savepoints': True,
            'foreign_keys': True,
            'check_constraints': True,
            'unique_constraints': True,
            'indexes': True,
            'triggers': True,
            'views': True,
            'temp_tables': True,
            'attach_database': True,
            'pragma': True,
            'explain_query_plan': True,
            'vacuum': True,
            'analyze': True,
            'wal_mode': True,
            'json_functions': True,  # With JSON1 extension
            'fts': True,             # With FTS5 extension
            'rtree': True,           # With R*Tree extension
            'window_functions': True, # SQLite 3.25+
            'cte': True,             # Common Table Expressions
            'upsert': True,          # INSERT OR REPLACE, ON CONFLICT
            'returning': True        # RETURNING clause (3.35+)
        }
        
        return supported_features.get(feature, False)
    
    def get_connection_options(self) -> Dict[str, Any]:
        """Get SQLite-specific connection options"""
        return {
            'check_same_thread': False,  # Allow multi-threading
            'timeout': 20.0,             # Connection timeout
            'isolation_level': None,     # Autocommit mode
            'detect_types': 0,           # Type detection
            'cached_statements': 100,    # Statement cache size
        }
    
    def get_pragma_settings(self) -> Dict[str, Any]:
        """Get recommended SQLite PRAGMA settings"""
        return {
            'foreign_keys': 'ON',        # Enable foreign key constraints
            'journal_mode': 'WAL',       # Write-Ahead Logging
            'synchronous': 'NORMAL',     # Balance between safety and speed
            'cache_size': -64000,        # 64MB cache (negative = KB)
            'temp_store': 'MEMORY',      # Store temp tables in memory
            'mmap_size': 268435456,      # 256MB memory-mapped I/O
            'optimize': None,            # Run PRAGMA optimize on close
        }
    
    def build_connection_string(self, **kwargs) -> str:
        """Build SQLite connection string"""
        database = kwargs.get('database', ':memory:')
        
        # For file databases, ensure proper path format
        if database != ':memory:' and not database.startswith('/'):
            # Relative path - make it absolute for consistency
            import os
            database = os.path.abspath(database)
        
        return f"sqlite:///{database}"
    
    def get_table_exists_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to check if table exists"""
        return f"SELECT name FROM sqlite_master WHERE type='table' AND name={self.escape_string(table_name)}"

    def get_column_info_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to retrieve column information"""
        return f"PRAGMA table_info({self.quote_identifier(table_name)})"

    def get_primary_key_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to retrieve primary key information"""
        return f"PRAGMA table_info({self.quote_identifier(table_name)})"

    def get_foreign_key_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to retrieve foreign key information"""
        return f"PRAGMA foreign_key_list({self.quote_identifier(table_name)})"

    def get_index_info_query(self, table_name: str, schema: Optional[str] = None) -> str:
        """Get query to retrieve index information"""
        return f"PRAGMA index_list({self.quote_identifier(table_name)})"

    def get_table_info_query(self, table_name: str) -> str:
        """Get query to retrieve table information"""
        return f"PRAGMA table_info({self.quote_identifier(table_name)})"

    def get_foreign_key_info_query(self, table_name: str) -> str:
        """Get query to retrieve foreign key information"""
        return f"PRAGMA foreign_key_list({self.quote_identifier(table_name)})"
    
    def optimize_query(self, query: str) -> str:
        """Optimize SQLite query"""
        # Basic SQLite query optimizations
        optimizations = [
            # Use LIMIT when possible
            ('SELECT COUNT(*)', 'SELECT COUNT(*) LIMIT 1'),
            # Use EXISTS instead of COUNT for existence checks
            ('COUNT(*) > 0', 'EXISTS'),
        ]
        
        optimized_query = query
        for old, new in optimizations:
            if old in optimized_query:
                optimized_query = optimized_query.replace(old, new)
        
        return optimized_query
    
    def get_explain_query(self, query: str) -> str:
        """Get EXPLAIN query for SQLite"""
        return f"EXPLAIN QUERY PLAN {query}"
    
    def parse_explain_output(self, explain_result: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Parse SQLite EXPLAIN QUERY PLAN output"""
        analysis = {
            'query_plan': [],
            'table_scans': 0,
            'index_usage': [],
            'estimated_cost': 0,
            'warnings': []
        }
        
        for row in explain_result:
            plan_detail = {
                'id': row.get('id', 0),
                'parent': row.get('parent', 0),
                'detail': row.get('detail', ''),
                'operation': 'unknown'
            }
            
            detail = row.get('detail', '').lower()
            
            # Analyze operation type
            if 'scan table' in detail:
                plan_detail['operation'] = 'table_scan'
                analysis['table_scans'] += 1
                if 'using index' not in detail:
                    analysis['warnings'].append(f"Full table scan detected: {detail}")
            elif 'search table' in detail:
                plan_detail['operation'] = 'index_search'
                if 'using index' in detail:
                    index_name = detail.split('using index')[1].strip().split()[0]
                    analysis['index_usage'].append(index_name)
            elif 'use temp b-tree' in detail:
                plan_detail['operation'] = 'temp_btree'
                analysis['warnings'].append("Temporary B-tree created for sorting/grouping")
            
            analysis['query_plan'].append(plan_detail)
        
        return analysis
    
    def get_database_size_query(self) -> str:
        """Get query to check database size"""
        return "SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()"
    
    def get_vacuum_query(self) -> str:
        """Get VACUUM query for SQLite"""
        return "VACUUM"
    
    def get_analyze_query(self, table_name: Optional[str] = None) -> str:
        """Get ANALYZE query for SQLite"""
        if table_name:
            return f"ANALYZE {self.quote_identifier(table_name)}"
        return "ANALYZE"

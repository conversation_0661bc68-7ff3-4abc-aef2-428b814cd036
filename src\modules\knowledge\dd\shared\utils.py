"""
DD系统工具函数
"""

import re
import hashlib
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
import json

from .constants import DDConstants, DDFieldCategories


class DDUtils:
    """DD系统工具类"""
    
    @staticmethod
    def generate_submission_id(
        knowledge_id: str, 
        dr07: str, 
        version: str,
        record_type: str = "SUBMISSION"
    ) -> str:
        """
        生成submission_id
        
        Args:
            knowledge_id: 知识库ID
            dr07: 表名ID
            version: 版本
            record_type: 记录类型
            
        Returns:
            生成的submission_id
        """
        # 使用MD5生成唯一ID
        content = f"{knowledge_id}_{dr07}_{version}_{record_type}_{datetime.now().isoformat()}"
        hash_obj = hashlib.md5(content.encode())
        return f"SUB_{hash_obj.hexdigest()[:16].upper()}"
    
    @staticmethod
    def validate_field_code(field_code: str) -> bool:
        """
        验证字段编码是否有效
        
        Args:
            field_code: 字段编码
            
        Returns:
            是否有效
        """
        return field_code.lower() in [f.lower() for f in DDFieldCategories.ALL_DD_FIELDS]
    
    @staticmethod
    def get_field_category(field_code: str) -> Optional[str]:
        """
        获取字段分类
        
        Args:
            field_code: 字段编码
            
        Returns:
            字段分类 (A/B/C/D) 或 None
        """
        field_lower = field_code.lower()
        
        if field_lower in [f.lower() for f in DDFieldCategories.A_FIELDS]:
            return "A"
        elif field_lower in [f.lower() for f in DDFieldCategories.B_FIELDS]:
            return "B"
        elif field_lower in [f.lower() for f in DDFieldCategories.C_FIELDS]:
            return "C"
        elif field_lower in [f.lower() for f in DDFieldCategories.D_FIELDS]:
            return "D"
        
        return None
    
    @staticmethod
    def is_vectorized_field(field_code: str) -> bool:
        """
        检查字段是否需要向量化
        
        Args:
            field_code: 字段编码
            
        Returns:
            是否需要向量化
        """
        return field_code.lower() in [f.lower() for f in DDConstants.VECTORIZED_FIELDS]
    
    @staticmethod
    def extract_vectorized_content(data: Dict[str, Any]) -> Dict[str, str]:
        """
        提取需要向量化的内容
        
        Args:
            data: 数据字典
            
        Returns:
            需要向量化的字段内容
        """
        vectorized_content = {}
        
        for field in DDConstants.VECTORIZED_FIELDS:
            if field in data and data[field]:
                vectorized_content[field] = str(data[field])
        
        return vectorized_content
    
    @staticmethod
    def clean_text_for_vectorization(text: str) -> str:
        """
        清理文本用于向量化
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符（保留中文、英文、数字、基本标点）
        text = re.sub(r'[^\u4e00-\u9fff\w\s\.,;:!?()（），。；：！？]', '', text)
        
        return text
    
    @staticmethod
    def validate_enum_value(value: str, enum_list: List[str]) -> bool:
        """
        验证枚举值（已废弃）

        注意：由于表结构修改，编码字段现在使用VARCHAR类型，
        不再进行严格的枚举值验证。此方法保留以兼容现有代码。

        Args:
            value: 待验证的值
            enum_list: 枚举值列表（已忽略）

        Returns:
            始终返回True（不再进行枚举验证）
        """
        # 只进行基本的非空验证
        return value is not None and isinstance(value, str) and len(value.strip()) > 0
    
    @staticmethod
    def format_dd_data_for_display(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化DD数据用于显示
        
        Args:
            data: 原始数据
            
        Returns:
            格式化后的数据
        """
        formatted_data = {}
        
        for key, value in data.items():
            if value is None:
                formatted_data[key] = ""
            elif isinstance(value, datetime):
                formatted_data[key] = value.strftime("%Y-%m-%d %H:%M:%S")
            elif isinstance(value, (dict, list)):
                formatted_data[key] = json.dumps(value, ensure_ascii=False, indent=2)
            else:
                formatted_data[key] = str(value)
        
        return formatted_data
    
    @staticmethod
    def build_search_filters(
        knowledge_id: Optional[str] = None,
        version: Optional[str] = None,
        data_layer: Optional[str] = None,
        record_type: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        构建搜索过滤条件
        
        Args:
            knowledge_id: 知识库ID
            version: 版本
            data_layer: 数据层
            record_type: 记录类型
            **kwargs: 其他过滤条件
            
        Returns:
            过滤条件字典
        """
        filters = {}
        
        if knowledge_id:
            filters["knowledge_id"] = knowledge_id
        if version:
            filters["version"] = version
        if data_layer:
            filters["dr01"] = data_layer
        if record_type:
            filters["type"] = record_type
        
        # 添加其他过滤条件
        for key, value in kwargs.items():
            if value is not None:
                filters[key] = value
        
        return filters

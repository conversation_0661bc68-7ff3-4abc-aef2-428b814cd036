# Service Layer - 统一服务层

## 🎯 系统概述

这是一个**统一的企业级服务层**，提供简单易用的API来管理数据库客户端、连接池和动态配置：

- ✅ **简单易用** - `cfg.xxx` 一行代码注册和使用
- ✅ **统一接口** - 无论MySQL、PostgreSQL、PgVector都是相同的API
- ✅ **动态配置** - 支持从数据库动态加载配置，零假设设计
- ✅ **连接池管理** - 自动管理连接池，支持负载均衡和健康检查
- ✅ **企业级特性** - 支持多租户、多环境、配置缓存等

## 🚀 快速开始（2步搞定）

### 第1步：配置注册（Hydra配置文件）

```yaml
# config/database/rdbs/mysql.yaml
_target_: base.db.implementations.rdb.universal.factory.create_mysql_client
host: "your-mysql-host.com"
port: 3306
database: "your_database"
username: "your_user"
password: "your_password"
pool_params:
  size: 10
  max_overflow: 20
```

### 第2步：简单使用

```python
import asyncio
from service import get_client, get_config

async def main():
    # 方式1：字符串路径（推荐，最简洁）
    mysql_client = await get_client("database.rdbs.mysql")
    result = await mysql_client.execute_query("SELECT * FROM users LIMIT 5")
    print(f"查询结果: {result}")

    # 方式2：配置对象（适合复杂场景）
    cfg = await get_config("mixed")
    mysql_client = await get_client(cfg.database.rdbs.mysql)
    result = await mysql_client.execute_query("SELECT * FROM users LIMIT 5")
    print(f"查询结果: {result}")

    # 方式3：直接访问配置
    mysql_config = cfg.database.rdbs.mysql
    print(f"数据库配置: {mysql_config.host}:{mysql_config.port}")

# 运行
asyncio.run(main())
```

**两种方式都支持，选择您喜欢的！** 🎉

## 📋 核心API方法

### 1. get_client() - 获取数据库客户端（推荐）

```python
# 获取MySQL客户端
mysql_client = await get_client("database.rdbs.mysql")

# 获取PgVector客户端
pgvector_client = await get_client("database.vdbs.pgvector")

# 执行查询
result = await mysql_client.execute_query("SELECT * FROM users LIMIT 10")
```

### 2. get_config() - 获取配置管理器

```python
# 获取配置管理器
cfg = await get_config("mixed")

# 访问配置
mysql_config = cfg.database.rdbs.mysql
print(f"数据库: {mysql_config.host}:{mysql_config.port}")
```

### 3. 动态配置（高级功能）

```python
# 查询动态配置
raw_config = await cfg.get_database_config(cfg.dynamic_config_db, params)

# 转换和合并配置
transformed = cfg.transform_dynamic_config(raw_config, my_transform_func)
final_config = await cfg.merge_configs(cfg._config, transformed)
```

## 🏢 实际使用场景

### 场景1：数据查询操作

```python
import asyncio
from service import get_client

async def query_users():
    # 获取MySQL客户端
    mysql_client = await get_client("database.rdbs.mysql")

    # 执行查询
    users = await mysql_client.execute_query(
        "SELECT id, name, email FROM users WHERE status = %s LIMIT %s",
        ("active", 10)
    )

    return users

# 使用
users = await query_users()
print(f"找到 {len(users)} 个用户")
```

### 场景2：向量搜索操作

```python
async def semantic_search(query_text):
    # 获取PgVector客户端
    pgvector_client = await get_client("database.vdbs.pgvector")

    # 执行向量搜索
    results = await pgvector_client.similarity_search(
        query_vector=query_embedding,
        top_k=5,
        threshold=0.8
    )

    return results

# 使用
results = await semantic_search("查找相关文档")
```

### 场景3：多数据库操作

```python
async def hybrid_search(user_id, query_text):
    # 同时使用多个数据库
    mysql_client = await get_client("database.rdbs.mysql")
    pgvector_client = await get_client("database.vdbs.pgvector")

    # 从MySQL获取用户信息
    user = await mysql_client.execute_query(
        "SELECT * FROM users WHERE id = %s", (user_id,)
    )

    # 从PgVector进行语义搜索
    search_results = await pgvector_client.similarity_search(
        query_vector=query_embedding,
        top_k=10
    )

    return {
        "user": user[0] if user else None,
        "search_results": search_results
    }
```

### 场景4：配置驱动的客户端选择

```python
async def get_database_for_tenant(tenant_id):
    # 获取配置管理器
    cfg = await get_config("mixed")

    # 根据租户选择不同的数据库配置
    if tenant_id.startswith("enterprise_"):
        db_config = cfg.database.rdbs.mysql_enterprise
    else:
        db_config = cfg.database.rdbs.mysql_standard

    # 获取对应的客户端
    client = await get_client(db_config)
    return client
```

## 🔧 转换函数示例

### JSON格式转换

```python
def json_transform(raw_config):
    import json
    config_key = raw_config.get('config_key', '')
    config_value = raw_config.get('config_value', '{}')
    
    parsed = json.loads(config_value)
    parts = config_key.split('.')
    
    result = {}
    current = result
    for i, part in enumerate(parts):
        if i == len(parts) - 1:
            current[part] = parsed
        else:
            current[part] = {}
            current = current[part]
    return result
```

### 键值对格式转换

```python
def kv_transform(raw_config):
    config_key = raw_config.get('config_key', '')
    config_data = raw_config.get('config_value', '')
    
    # 解析: "host=mysql.com;port=3306;db=mydb"
    parsed = {}
    for pair in config_data.split(';'):
        if '=' in pair:
            key, value = pair.split('=', 1)
            parsed[key.strip()] = value.strip()
    
    if config_key.startswith('database.'):
        parts = config_key.split('.')
        return {
            "database": {
                parts[1]: {
                    parts[2]: parsed
                }
            }
        }
    return {config_key: parsed}
```

### 遗留系统转换

```python
def legacy_transform(raw_config):
    config_id = raw_config.get('config_key', '').strip()
    config_data = raw_config.get('config_value', '').strip()
    
    # 解析固定长度格式
    parsed = {}
    for pair in config_data.split(';'):
        if '=' in pair:
            key, value = pair.split('=', 1)
            parsed[key.strip().lower()] = value.strip()
    
    # 字段映射
    field_mapping = {
        "host": "host", "port": "port", 
        "db": "database", "user": "username"
    }
    
    modern_config = {}
    for legacy_key, legacy_value in parsed.items():
        modern_key = field_mapping.get(legacy_key, legacy_key)
        modern_config[modern_key] = legacy_value
    
    if config_id.startswith('DB_'):
        db_type = config_id.split('_')[1].lower()
        return {
            "database": {
                "rdbs": {
                    db_type: {
                        **modern_config,
                        "_legacy_metadata": {
                            "source": "legacy_mainframe",
                            "original_id": config_id
                        }
                    }
                }
            }
        }
    return {"legacy_config": {config_id: modern_config}}
```

## 📁 文件结构

```
service/
├── config/                     # 配置管理层
│   ├── manager.py             # 核心配置管理器
│   ├── loader.py              # 配置加载器
│   └── ...
├── client/                     # 客户端管理层
│   ├── factory.py             # 客户端工厂
│   ├── registry.py            # 客户端注册表
│   └── ...
├── provider/                   # 连接池提供层
│   ├── pool_manager.py        # 连接池管理器
│   ├── load_balancer.py       # 负载均衡器
│   └── ...
├── examples/                   # 示例和测试
│   ├── client/                # 客户端使用示例
│   │   ├── simple_usage.py    # 简单使用演示
│   │   └── hydra_registration.py # Hydra配置注册
│   ├── config/                # 动态配置示例
│   │   ├── complete_config_examples.yaml
│   │   └── transform_functions_examples.py
│   └── provider/              # 连接池和性能示例
│       ├── async_test_time.py # 异步性能测试
│       └── test_universal_performance.py
└── README.md                  # 本文档
```

## 🧪 测试验证

### 基本功能测试
```bash
cd src
python service/examples/client/simple_usage.py
```

### 配置注册演示
```bash
cd src
python service/examples/client/hydra_registration.py
```

### 动态配置测试
```bash
cd src
python service/examples/config/test_simple_demo.py
```

### 性能测试
```bash
cd src
python service/examples/provider/async_test_time.py
```

## 🎯 核心优势

1. **零假设设计** - 不假设任何表结构、字段名或数据格式
2. **完全配置驱动** - 所有逻辑都通过配置文件控制
3. **企业级适应性** - 适应任何现有的配置管理系统
4. **渐进式迁移** - 可以与现有系统无缝集成
5. **简单易用** - 3步即可完成集成

## 🔍 故障排除

### 常见问题

1. **SQL参数绑定错误**
   - 检查SQL模板中的参数名称
   - 确保params字典包含所有必需的参数

2. **转换函数异常**
   - 添加异常处理逻辑
   - 验证原始数据格式

3. **配置合并问题**
   - 检查转换后的配置结构
   - 确保层级结构正确

这个系统的核心理念是：**框架提供机制，用户提供策略** - 您可以适应任何现有的配置管理系统，而不需要修改您的数据库结构或业务逻辑。

"""
RDB核心接口定义

定义数据库抽象层的核心接口，确保不同实现的一致性
参考Repository模式、encode/databases、SQLAlchemy等优秀设计
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, AsyncContextManager, ContextManager
from contextlib import asynccontextmanager, contextmanager

from .types import (
    DatabaseValue, DatabaseRecord, DatabaseRecords,
    DatabaseConnection, DatabaseTransaction, DatabasePool,
    DatabaseType, TransactionIsolation
)
from .models import (
    QueryRequest, QueryResponse,
    InsertRequest, UpdateRequest, DeleteRequest, OperationResponse,
    ConnectionConfig
)


# ==================== 核心数据库接口 ====================

class DatabaseClient(ABC):
    """数据库客户端抽象接口
    
    定义统一的数据库操作接口，支持同步和异步操作
    """
    
    # ==================== 连接管理 ====================
    
    @abstractmethod
    def connect(self) -> None:
        """建立数据库连接（同步）"""
        pass
    
    @abstractmethod
    async def aconnect(self) -> None:
        """建立数据库连接（异步）"""
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """断开数据库连接（同步）"""
        pass
    
    @abstractmethod
    async def adisconnect(self) -> None:
        """断开数据库连接（异步）"""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """检查连接状态"""
        pass
    
    @abstractmethod
    def get_database_type(self) -> DatabaseType:
        """获取数据库类型"""
        pass
    
    # ==================== 查询操作 ====================
    
    @abstractmethod
    def query(self, request: QueryRequest) -> QueryResponse:
        """执行查询（同步）"""
        pass
    
    @abstractmethod
    async def aquery(self, request: QueryRequest) -> QueryResponse:
        """执行查询（异步）"""
        pass
    
    # ==================== CRUD操作 ====================
    
    @abstractmethod
    def insert(self, request: InsertRequest) -> OperationResponse:
        """插入数据（同步）"""
        pass
    
    @abstractmethod
    async def ainsert(self, request: InsertRequest) -> OperationResponse:
        """插入数据（异步）"""
        pass
    
    @abstractmethod
    def update(self, request: UpdateRequest) -> OperationResponse:
        """更新数据（同步）"""
        pass
    
    @abstractmethod
    async def aupdate(self, request: UpdateRequest) -> OperationResponse:
        """更新数据（异步）"""
        pass
    
    @abstractmethod
    def delete(self, request: DeleteRequest) -> OperationResponse:
        """删除数据（同步）"""
        pass
    
    @abstractmethod
    async def adelete(self, request: DeleteRequest) -> OperationResponse:
        """删除数据（异步）"""
        pass
    
    # ==================== 原生SQL执行 ====================
    
    @abstractmethod
    def execute(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> OperationResponse:
        """执行原生SQL（同步）"""
        pass
    
    @abstractmethod
    async def aexecute(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> OperationResponse:
        """执行原生SQL（异步）"""
        pass
    
    @abstractmethod
    def fetch_all(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> QueryResponse:
        """获取所有结果（同步）"""
        pass
    
    @abstractmethod
    async def afetch_all(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> QueryResponse:
        """获取所有结果（异步）"""
        pass
    
    @abstractmethod
    def fetch_one(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> Optional[DatabaseRecord]:
        """获取单个结果（同步）"""
        pass
    
    @abstractmethod
    async def afetch_one(self, sql: str, parameters: Optional[Dict[str, Any]] = None) -> Optional[DatabaseRecord]:
        """获取单个结果（异步）"""
        pass
    
    # ==================== 事务管理 ====================
    
    @abstractmethod
    @contextmanager
    def transaction(self, isolation_level: Optional[TransactionIsolation] = None) -> ContextManager[DatabaseTransaction]:
        """事务上下文管理器（同步）"""
        pass
    
    @abstractmethod
    @asynccontextmanager
    async def atransaction(self, isolation_level: Optional[TransactionIsolation] = None) -> AsyncContextManager[DatabaseTransaction]:
        """事务上下文管理器（异步）"""
        pass
    
    # ==================== 健康检查 ====================
    
    @abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        pass


# ==================== 查询构建器接口 ====================

class QueryBuilder(ABC):
    """查询构建器接口
    
    提供链式API构建复杂查询
    """
    
    @abstractmethod
    def table(self, table_name: str) -> 'QueryBuilder':
        """设置表名"""
        pass
    
    @abstractmethod
    def select(self, *columns: str) -> 'QueryBuilder':
        """选择列"""
        pass
    
    @abstractmethod
    def where(self, field: str, operator: str, value: DatabaseValue) -> 'QueryBuilder':
        """添加WHERE条件"""
        pass
    
    @abstractmethod
    def join(self, table: str, on_condition: str, join_type: str = "inner") -> 'QueryBuilder':
        """添加JOIN"""
        pass
    
    @abstractmethod
    def order_by(self, field: str, direction: str = "asc") -> 'QueryBuilder':
        """添加排序"""
        pass
    
    @abstractmethod
    def limit(self, count: int) -> 'QueryBuilder':
        """设置LIMIT"""
        pass
    
    @abstractmethod
    def offset(self, count: int) -> 'QueryBuilder':
        """设置OFFSET"""
        pass
    
    @abstractmethod
    def build(self) -> QueryRequest:
        """构建查询请求"""
        pass
    
    @abstractmethod
    def execute(self) -> QueryResponse:
        """执行查询（同步）"""
        pass
    
    @abstractmethod
    async def aexecute(self) -> QueryResponse:
        """执行查询（异步）"""
        pass


# ==================== 数据库方言接口 ====================

class DatabaseDialect(ABC):
    """数据库方言接口
    
    处理不同数据库的SQL差异
    """
    
    @property
    @abstractmethod
    def name(self) -> str:
        """方言名称"""
        pass
    
    @property
    @abstractmethod
    def database_type(self) -> DatabaseType:
        """数据库类型"""
        pass
    
    @abstractmethod
    def build_query_sql(self, request: QueryRequest) -> tuple[str, Dict[str, Any]]:
        """构建查询SQL"""
        pass
    
    @abstractmethod
    def build_insert_sql(self, request: InsertRequest) -> tuple[str, Dict[str, Any]]:
        """构建插入SQL"""
        pass
    
    @abstractmethod
    def build_update_sql(self, request: UpdateRequest) -> tuple[str, Dict[str, Any]]:
        """构建更新SQL"""
        pass
    
    @abstractmethod
    def build_delete_sql(self, request: DeleteRequest) -> tuple[str, Dict[str, Any]]:
        """构建删除SQL"""
        pass
    
    @abstractmethod
    def quote_identifier(self, identifier: str) -> str:
        """引用标识符"""
        pass
    
    @abstractmethod
    def format_value(self, value: DatabaseValue) -> str:
        """格式化值"""
        pass
    
    @abstractmethod
    def get_supported_features(self) -> Dict[str, bool]:
        """获取支持的功能"""
        pass


# ==================== 连接池接口 ====================

class ConnectionPoolManager(ABC):
    """连接池管理器接口"""
    
    @abstractmethod
    async def create_pool(self, config: ConnectionConfig) -> DatabasePool:
        """创建连接池"""
        pass
    
    @abstractmethod
    async def close_pool(self, pool: DatabasePool) -> None:
        """关闭连接池"""
        pass
    
    @abstractmethod
    async def get_connection(self, pool: DatabasePool) -> DatabaseConnection:
        """获取连接"""
        pass
    
    @abstractmethod
    async def release_connection(self, pool: DatabasePool, connection: DatabaseConnection) -> None:
        """释放连接"""
        pass
    
    @abstractmethod
    def get_pool_status(self, pool: DatabasePool) -> Dict[str, Any]:
        """获取连接池状态"""
        pass


# ==================== 结果适配器接口 ====================

class ResultAdapter(ABC):
    """结果适配器接口
    
    将数据库特定结果转换为统一格式
    """
    
    @abstractmethod
    def adapt_query_result(self, raw_result: Any, metadata: Optional[Dict[str, Any]] = None) -> QueryResponse:
        """适配查询结果"""
        pass
    
    @abstractmethod
    def adapt_operation_result(self, raw_result: Any, metadata: Optional[Dict[str, Any]] = None) -> OperationResponse:
        """适配操作结果"""
        pass
    
    @abstractmethod
    def adapt_record(self, raw_record: Any) -> DatabaseRecord:
        """适配单条记录"""
        pass
    
    @abstractmethod
    def adapt_value(self, raw_value: Any) -> DatabaseValue:
        """适配单个值"""
        pass


# ==================== 工厂接口 ====================

class DatabaseClientFactory(ABC):
    """数据库客户端工厂接口"""
    
    @abstractmethod
    def create_client(self, config: ConnectionConfig) -> DatabaseClient:
        """创建数据库客户端"""
        pass
    
    @abstractmethod
    def get_supported_database_types(self) -> List[DatabaseType]:
        """获取支持的数据库类型"""
        pass
    
    @abstractmethod
    def validate_config(self, config: ConnectionConfig) -> bool:
        """验证配置"""
        pass


# ==================== 导出接口 ====================

__all__ = [
    # 核心接口
    "DatabaseClient",
    "QueryBuilder",
    "DatabaseDialect",
    "ConnectionPoolManager",
    "ResultAdapter",
    "DatabaseClientFactory",
]

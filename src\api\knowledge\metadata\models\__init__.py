"""
元数据API数据模型

包含所有元数据API相关的数据模型定义，参照DD系统的设计：
- requests: 请求模型（用于API输入）
- responses: 响应模型（用于API输出）
- enums: 枚举定义（状态、类型等）

注意：这里的模型主要用于API层，与modules层的模型保持兼容。
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel
from enum import Enum

# 从modules层导入基础模型，确保兼容性
# from modules.knowledge.metadata.models import *

# API特有的模型可以在这里扩展
# from .response_models import *

# 基础Pydantic模型类
# 枚举类定义
class DatabaseTypeEnum(str, Enum):
    SOURCE = "source"
    METRIC = "metric"

class TableTypeEnum(str, Enum):
    SOURCE = "source"
    METRIC = "metric"

class ColumnTypeEnum(str, Enum):
    SOURCE = "source"
    METRIC = "metric"

class DataSubjectTypeEnum(str, Enum):
    BUSINESS = "business"
    TECHNICAL = "technical"

class RelationTypeEnum(str, Enum):
    FOREIGN_KEY = "foreign_key"
    REFERENCE = "reference"

class UpdateModeEnum(str, Enum):
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"

class VectorSyncStatusEnum(str, Enum):
    PENDING = "pending"
    SYNCED = "synced"
    FAILED = "failed"

# 请求模型类
class DatabaseCreateRequest(BaseModel):
    db_name: str
    db_name_cn: Optional[str] = None
    data_layer: str
    db_type: DatabaseTypeEnum

class DatabaseUpdateRequest(BaseModel):
    db_name: Optional[str] = None
    db_name_cn: Optional[str] = None
    data_layer: Optional[str] = None
    db_type: Optional[DatabaseTypeEnum] = None

class TableCreateRequest(BaseModel):
    table_name: str
    table_name_cn: Optional[str] = None
    table_type: TableTypeEnum

class TableUpdateRequest(BaseModel):
    table_name: Optional[str] = None
    table_name_cn: Optional[str] = None
    table_type: Optional[TableTypeEnum] = None

class ColumnCreateRequest(BaseModel):
    column_name: str
    column_name_cn: Optional[str] = None
    column_type: ColumnTypeEnum
    data_type: Optional[str] = None
    column_desc: Optional[str] = None

class ColumnUpdateRequest(BaseModel):
    column_name: Optional[str] = None
    column_name_cn: Optional[str] = None
    column_type: Optional[ColumnTypeEnum] = None
    data_type: Optional[str] = None
    column_desc: Optional[str] = None

class CodeSetCreateRequest(BaseModel):
    code_set_name: str
    code_set_desc: Optional[str] = None

class CodeSetUpdateRequest(BaseModel):
    code_set_name: Optional[str] = None
    code_set_desc: Optional[str] = None

class DataSubjectCreateRequest(BaseModel):
    subject_name: str
    subject_desc: Optional[str] = None
    subject_type: DataSubjectTypeEnum

class DataSubjectUpdateRequest(BaseModel):
    subject_name: Optional[str] = None
    subject_desc: Optional[str] = None
    subject_type: Optional[DataSubjectTypeEnum] = None

class RelationCreateRequest(BaseModel):
    relation_name: str
    relation_type: RelationTypeEnum
    source_entity_id: int
    target_entity_id: int

class RelationUpdateRequest(BaseModel):
    relation_name: Optional[str] = None
    relation_type: Optional[RelationTypeEnum] = None

class MetadataSearchRequest(BaseModel):
    query: str
    entity_type: Optional[str] = None
    search_type: str = "hybrid"
    limit: int = 10
    min_score: float = 0.5

class BatchCrudRequest(BaseModel):
    entity_type: str
    data_list: List[Dict[str, Any]]

# 实体模型类
class DatabaseEntity(BaseModel):
    db_id: int
    db_name: str
    db_name_cn: Optional[str] = None
    data_layer: str
    db_type: DatabaseTypeEnum

class TableEntity(BaseModel):
    table_id: int
    table_name: str
    table_name_cn: Optional[str] = None
    table_type: TableTypeEnum

class ColumnEntity(BaseModel):
    column_id: int
    column_name: str
    column_name_cn: Optional[str] = None
    column_type: ColumnTypeEnum
    data_type: Optional[str] = None
    column_desc: Optional[str] = None

class CodeSetEntity(BaseModel):
    code_set_id: int
    code_set_name: str
    code_set_desc: Optional[str] = None

class DataSubjectEntity(BaseModel):
    subject_id: int
    subject_name: str
    subject_desc: Optional[str] = None
    subject_type: DataSubjectTypeEnum

class RelationEntity(BaseModel):
    relation_id: int
    relation_name: str
    relation_type: RelationTypeEnum
    source_entity_id: int
    target_entity_id: int

# 响应模型类
class DatabaseResponse(BaseModel):
    success: bool
    message: str
    data: Optional[DatabaseEntity] = None

class TableResponse(BaseModel):
    success: bool
    message: str
    data: Optional[TableEntity] = None

class ColumnResponse(BaseModel):
    success: bool
    message: str
    data: Optional[ColumnEntity] = None

class CodeSetResponse(BaseModel):
    success: bool
    message: str
    data: Optional[CodeSetEntity] = None

class DataSubjectResponse(BaseModel):
    success: bool
    message: str
    data: Optional[DataSubjectEntity] = None

class RelationResponse(BaseModel):
    success: bool
    message: str
    data: Optional[RelationEntity] = None

class CrudResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None

class BatchCrudResponse(BaseModel):
    success: bool
    message: str
    data: Dict[str, Any]

class VectorSyncResponse(BaseModel):
    success: bool
    message: str
    data: Dict[str, Any]

class MetadataSearchResponse(BaseModel):
    success: bool
    message: str
    data: List[Dict[str, Any]]

class MetadataStatsResponse(BaseModel):
    success: bool
    message: str
    data: Dict[str, Any]

# 系统管理相关响应模型
class MetadataHealthResponse(BaseModel):
    status: str
    service: str
    version: str
    modules: Optional[List[str]] = None
    database_status: Optional[Dict[str, str]] = None

class MetadataOverviewResponse(BaseModel):
    service: str
    description: str
    api_groups: Optional[Dict[str, Any]] = None
    features: Optional[List[str]] = None

__all__ = [
    # 从modules层继承的模型
    "DatabaseTypeEnum",
    "TableTypeEnum", 
    "ColumnTypeEnum",
    "DataSubjectTypeEnum",
    "RelationTypeEnum",
    "UpdateModeEnum",
    "VectorSyncStatusEnum",
    
    "DatabaseEntity",
    "TableEntity",
    "ColumnEntity",
    "CodeSetEntity",
    "DataSubjectEntity",
    "RelationEntity",
    
    "DatabaseCreateRequest",
    "DatabaseUpdateRequest",
    "TableCreateRequest",
    "TableUpdateRequest",
    "ColumnCreateRequest",
    "ColumnUpdateRequest",
    "CodeSetCreateRequest",
    "CodeSetUpdateRequest",
    "DataSubjectCreateRequest",
    "DataSubjectUpdateRequest",
    "RelationCreateRequest",
    "RelationUpdateRequest",
    "BatchCrudRequest",
    
    "DatabaseResponse",
    "TableResponse",
    "ColumnResponse",
    "CodeSetResponse",
    "DataSubjectResponse",
    "RelationResponse",
    "CrudResponse",
    "BatchCrudResponse",
    "VectorSyncResponse",
    
    # API特有的模型
    "MetadataSearchRequest",
    "MetadataSearchResponse",
    "MetadataStatsResponse",
    "MetadataHealthResponse",
    "MetadataOverviewResponse",
]

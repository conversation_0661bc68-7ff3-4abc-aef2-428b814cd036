"""
DD数据需求管理系统 API模块

重构后的模块化架构，提供DD系统的完整API接口：

📁 目录结构：
├── routers/          # API路由模块（按功能分组）
├── models/           # 数据模型（请求/响应/枚举）
├── dependencies/     # 依赖注入和通用功能
├── utils/           # 工具函数和辅助功能
├── docs/            # 文档和示例
└── tests/           # 测试文件

🚀 功能模块：
- 部门管理CRUD API
- 填报数据管理CRUD API（含自动向量化）
- 智能搜索API（向量/混合/精确搜索）
- 分发数据管理API
- 报表数据管理API
- 系统管理API（健康检查/概览/状态）
"""

from .main import router

__all__ = [
    "router",
]

"""
DD SQL推荐测试数据生成脚本
用于生成biz_dd_pre_distribution、biz_dd_post_distribution和历史数据表的测试数据
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import List, Dict, Any
import sys
import os
from modules.knowledge.dd.crud import DDCrud
from service import get_client
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def insert_test_data_to_db():
    """重构：严格主外键依赖顺序插入测试数据，只生成三条pre表数据"""
    try:
        logger.info("开始插入测试数据到数据库...")

        # 获取数据库客户端
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        embedding_client = await get_client("model.embeddings.moka-m3e-base")
        dd_crud = DDCrud(rdb_client, vdb_client, embedding_client)

        # 1. 生成一条report_data（知识库表）
        report_data = {
            "knowledge_id": "a26b2448-ec03-4b4e-9989-76be746eb171",
            "version": "v1.0",
            "report_name": "测试报表",
            "report_code": "TEST_REPORT",
            "report_layer": "ADS",
            "report_department": "114",
            "report_type": "detail",
            "set": "1104",
            "is_manual": 0,
            "create_time": datetime.now(),
            "update_time": datetime.now()
        }
        report_data_id = await dd_crud.create_report_data(report_data)
        logger.info(f"插入dd_report_data，id={report_data_id}")

        # 2. 生成三条pre_distribution（dd-a表）
        pre_items = [
            {
                "submission_id": f"SUBMIT_00{i+1}",
                "submission_type": "SUBMISSION",
                "report_type": "detail",
                "set": "1104",
                "version": "G0107_release_v1.0",
                "dr01": "ADS",
                "dr09": ["客户基本信息表", "贷款余额统计", "交易流水记录"][i],
                "dr17": [
                    "获取所有客户的姓名、身份证号、联系电话和地址信息",
                    "统计各产品类型的贷款余额，按月度进行汇si总",
                    "获取所有交易的流水记录，包括交易时间、金额、类型等信息"
                ][i],
                "create_time": datetime.now(),
                "update_time": datetime.now()
            } for i in range(3)
        ]
        pre_ids = []
        for pre in pre_items:
            pre_id = await dd_crud.create_pre_distribution(pre)
            pre_ids.append(pre_id)
        logger.info(f"插入biz_dd_pre_distribution数据 {len(pre_ids)} 条")

        # 3. 对每条pre生成一条post_distribution（dd-b/c表）
        post_ids = []
        for i, pre_id in enumerate(pre_ids):
            dept_id = "114"
            post = {
                "pre_distribution_id": pre_id,
                "submission_id": pre_items[i]["submission_id"],
                "submission_type": pre_items[i]["submission_type"],
                "report_type": pre_items[i]["report_type"],
                "set": pre_items[i]["set"],
                "version": pre_items[i]["version"],
                "dept_id": dept_id,
                "dr01": pre_items[i]["dr01"],
                "dr07": f"TABLE_{i+1:03d}",
                "dr22": json.dumps([dept_id]),
                "bdr05": "数据采集->数据清洗->数据处理->数据输出",
                "bdr17": "通过客户ID关联客户基本信息表和交易表，获取完整的客户交易信息",
                "sdr10": "SELECT c.name, c.id_card, c.phone, c.address, t.amount, t.trans_type FROM customer c JOIN transaction t ON c.customer_id = t.customer_id WHERE t.trans_date >= '2023-01-01'",
                "create_time": datetime.now(),
                "update_time": datetime.now()
            }
            post_id = await dd_crud.create_post_distribution(post)
            post_ids.append(post_id)
        logger.info(f"插入biz_dd_post_distribution数据 {len(post_ids)} 条")

        # 4. 对每条pre生成一条submission_data（明细表）
        submission_ids = []
        for i, pre in enumerate(pre_items):
            submission = {
                "submission_id": pre["submission_id"],
                "report_data_id": report_data_id,
                "version": pre["version"],
                "type": pre["submission_type"],
                "dr01": pre["dr01"],
                "dr09": pre["dr09"],
                "dr17": pre["dr17"],
                "sdr10": "SELECT c.name, c.id_card, c.phone, c.address, t.amount, t.trans_type FROM customer c JOIN transaction t ON c.customer_id = t.customer_id WHERE t.trans_date >= '2023-01-01'",  # 可根据需要自定义
                "create_time": datetime.now(),
                "update_time": datetime.now()
            }
            submission_id, _ = await dd_crud.create_submission_data(submission)
            submission_ids.append(submission_id)
        logger.info(f"插入dd_submission_data数据 {len(submission_ids)} 条")

        # 5. 向量数据由create_submission_data自动处理，无需手动插入
        logger.info("向量数据已自动生成，无需手动插入")
        logger.info("测试数据插入完成")

    except Exception as e:
        logger.error(f"插入测试数据失败: {e}")
        raise

if __name__ == "__main__":
    # 运行测试数据生成
    asyncio.run(insert_test_data_to_db())
"""
This module provides a client for interacting with the OpenTrek Large Language Model (LLM).
It includes features such as high-performance asynchronous streaming, connection pooling via a shared
session manager, automatic retries with exponential backoff, and detailed performance metrics.
The client is designed to be robust and efficient for production environments.
"""

from typing import Optional, Union, Generator, AsyncGenerator, List, Dict, Any, Callable
import aiohttp
import json
import asyncio
import logging
import requests
import time
import uuid
import psutil
import threading
from collections.abc import Generator, Sequence, AsyncGenerator
from collections import deque
from pydantic import PrivateAttr, Field

from ..__base.large_language_model import LargeLanguageModel
from ...entities import PromptMessage, PromptMessageTool, LLMResult, LLMResultChunk, AssistantPromptMessage, \
    LLMResultChunkDelta
from base.model_serve.model_runtime.callbacks.base_callback import Callback
from base.model_serve.model_runtime.entities.llm_entities import LLMUsage
from ...utils.http_session_manager import (
    HTTPSessionManager, HTTPSessionConfig, HTTPConnectionManager,
    HTTPMetrics, get_session_manager
)

logger = logging.getLogger(__name__)


class StreamMetrics:
    """流式处理指标收集器"""
    def __init__(self):
        self.chunks_processed = 0
        self.bytes_processed = 0
        self.callback_errors = 0
        self.callback_total_time = 0.0
        self.memory_peak = 0
        self.start_time = time.perf_counter()
        self._lock = threading.Lock()
    
    def record_chunk(self, size: int):
        with self._lock:
            self.chunks_processed += 1
            self.bytes_processed += size
    
    def record_callback_time(self, duration: float):
        with self._lock:
            self.callback_total_time += duration
    
    def record_callback_error(self):
        with self._lock:
            self.callback_errors += 1
    
    def update_memory_peak(self):
        current_memory = psutil.Process().memory_info().rss
        if current_memory > self.memory_peak:
            self.memory_peak = current_memory
    
    def get_stats(self) -> Dict[str, Any]:
        with self._lock:
            elapsed = time.perf_counter() - self.start_time
            return {
                "chunks_processed": self.chunks_processed,
                "bytes_processed": self.bytes_processed,
                "callback_errors": self.callback_errors,
                "callback_avg_time": self.callback_total_time / max(1, self.chunks_processed),
                "processing_rate": self.chunks_processed / max(0.001, elapsed),
                "memory_peak_mb": self.memory_peak / 1024 / 1024,
                "elapsed_time": elapsed
            }


class LLMResultChunkPool:
    """LLMResultChunk对象池，减少GC压力"""
    def __init__(self, max_size: int = 1000):
        self._pool = deque(maxlen=max_size)
        self._lock = threading.Lock()
    
    def get_chunk(self, model: str, index: int, content: str, finish_reason: str = "") -> LLMResultChunk:
        with self._lock:
            if self._pool:
                chunk = self._pool.popleft()
                # 重置对象状态
                chunk.model = model
                chunk.delta.index = index
                chunk.delta.message.content = content
                chunk.delta.finish_reason = finish_reason
                return chunk
        
        # 池为空时创建新对象
        message = AssistantPromptMessage(content=content, tool_calls=[])
        return LLMResultChunk(
            model=model,
            delta=LLMResultChunkDelta(
                index=index,
                message=message,
                finish_reason=finish_reason
            )
        )
    
    def return_chunk(self, chunk: LLMResultChunk):
        """归还对象到池中"""
        with self._lock:
            if len(self._pool) < self._pool.maxlen:
                self._pool.append(chunk)


class OpentrekStream:
    """高性能Opentrek流式响应处理器 - 专注于批量处理和内存优化"""

    # 类级对象池，所有实例共享
    _chunk_pool = LLMResultChunkPool()

    def __init__(self, response: aiohttp.ClientResponse, session: aiohttp.ClientSession, 
                 model: str, buffer_size: int = 64 * 1024):
        self.response = response
        self.session = session
        self.model = model
        self.buffer_size = buffer_size
        self.index = 0
        self._closed = False
        self._buffer = b''
        self._metrics = StreamMetrics()
        self._encoding = 'utf-8'  # 预设编码避免重复检测
        logger.debug(f"OpentrekStream initialized: model={model}, buffer_size={buffer_size}")

    def __aiter__(self):
        return self

    async def __anext__(self) -> LLMResultChunk:
        if self._closed:
            raise StopAsyncIteration
            
        try:
            # 批量读取优化 - 一次读取更大块数据
            while True:
                chunk = await self._read_buffer_chunk()
                if chunk:
                    return chunk
                    
                # 缓冲区没有完整数据，读取更多
                try:
                    # 添加超时保护
                    data = await asyncio.wait_for(
                        self.response.content.read(self.buffer_size),
                        timeout=45.0  # 企业级优化：30 → 45秒超时 (更宽松)
                    )
                except (asyncio.TimeoutError, asyncio.CancelledError) as e:
                    logger.warning(f"OpentrekStream read timeout/cancelled: {e}")
                    await self._close()
                    raise StopAsyncIteration
                except aiohttp.ClientConnectionError as e:
                    logger.warning(f"OpentrekStream connection closed: {e}")
                    await self._close()
                    raise StopAsyncIteration

                if not data:
                    await self._close()
                    raise StopAsyncIteration

                self._buffer += data
                self._metrics.record_chunk(len(data))
                self._metrics.update_memory_peak()
                
        except StopAsyncIteration:
            await self._close()
            raise
        except (asyncio.TimeoutError, asyncio.CancelledError) as e:
            await self._close()
            logger.warning(f"OpentrekStream cancelled/timeout: {e}")
            raise StopAsyncIteration
        except aiohttp.ClientConnectionError as e:
            await self._close()
            logger.warning(f"OpentrekStream connection error: {e}")
            raise StopAsyncIteration
        except Exception as e:
            await self._close()
            logger.error(f"OpentrekStream error: {e}")
            raise StopAsyncIteration

    async def _read_buffer_chunk(self) -> Optional[LLMResultChunk]:
        """从缓冲区解析完整的SSE消息"""
        while b'\n' in self._buffer:
            line_bytes, self._buffer = self._buffer.split(b'\n', 1)
            
            # 避免频繁decode - 直接处理bytes
            if not line_bytes:
                continue
                
            try:
                line = line_bytes.decode(self._encoding)
            except UnicodeDecodeError:
                logger.warning(f"Failed to decode line: {line_bytes[:50]}")
                continue
            
            line = line.strip()
            
            # 检查结束标记
            if line == "data: [DONE]":
                await self._close()
                raise StopAsyncIteration

            # 处理SSE数据
            if line.startswith("data: "):
                chunk = await self._parse_sse_data(line[6:])
                if chunk:
                    return chunk
        
        return None

    async def _parse_sse_data(self, data_str: str) -> Optional[LLMResultChunk]:
        """解析SSE数据为LLMResultChunk - 使用对象池优化"""
        if not data_str.strip():
            return None
            
        try:
            chunk_data = json.loads(data_str)
            choices = chunk_data.get("choices", [])
            
            if not choices:
                return None
                
            choice = choices[0]
            delta = choice.get("delta", {})
            content = delta.get("content")
            
            if content:
                # 使用对象池获取chunk，减少GC压力
                chunk = self._chunk_pool.get_chunk(
                    model=self.model,
                    index=self.index,
                    content=content,
                    finish_reason=choice.get("finish_reason", "")
                )
                self.index += 1
                return chunk
                
        except json.JSONDecodeError as e:
            logger.warning("Failed to parse SSE JSON: {}... Error: {}", data_str[:100], e)
            
        return None

    async def _close(self):
        """清理资源并记录统计信息"""
        if not self._closed:
            self._closed = True
            stats = self._metrics.get_stats()
            logger.info("OpentrekStream closed - chunks: %d, bytes: %d, errors: %d, rate: %.2f/s",
                       stats.get('chunks_processed', 0),
                       stats.get('bytes_processed', 0),
                       stats.get('callback_errors', 0),
                       stats.get('processing_rate', 0.0))

            # 正确关闭HTTP响应
            try:
                if hasattr(self.response, 'close') and not self.response.closed:
                    self.response.close()
                    logger.debug("HTTP response closed successfully")
            except Exception as e:
                logger.warning(f"Error closing response: {e}")

            # 释放响应资源
            try:
                if hasattr(self.response, 'release'):
                    await self.response.release()
                    logger.debug("HTTP response released successfully")
            except Exception as e:
                logger.warning(f"Error releasing response: {e}")

    def get_metrics(self) -> Dict[str, Any]:
        """获取流式处理指标"""
        return self._metrics.get_stats()


class CallbackProcessor:
    """优化的回调处理器 - 支持类型缓存和错误统计"""
    
    def __init__(self, max_concurrent_callbacks: int = 100):
        self._callback_types = {}  # 普通字典缓存回调类型检查
        self._callback_semaphore = asyncio.Semaphore(max_concurrent_callbacks)
        self._error_count = 0
        self._total_callbacks = 0
        self._lock = asyncio.Lock()
    
    async def process_callback(self, callback: Callable, content: str, metrics: StreamMetrics):
        """处理单个回调，支持并发控制和错误统计"""
        if not callback:
            return
            
        async with self._callback_semaphore:  # 控制并发回调数量
            start_time = time.perf_counter()
            
            try:
                async with self._lock:
                    self._total_callbacks += 1
                
                # 检查回调类型（使用缓存优化）
                callback_key = id(callback)
                if callback_key not in self._callback_types:
                    self._callback_types[callback_key] = asyncio.iscoroutinefunction(callback)
                
                is_async = self._callback_types[callback_key]
                
                if is_async:
                    await callback(content)
                else:
                    callback(content)
                    
            except Exception as e:
                async with self._lock:
                    self._error_count += 1
                metrics.record_callback_error()
                logger.warning(f"Callback error (content: {content[:50]}...): {e}")
            finally:
                duration = time.perf_counter() - start_time
                metrics.record_callback_time(duration)
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取回调处理统计"""
        async with self._lock:
            return {
                "total_callbacks": self._total_callbacks,
                "error_count": self._error_count,
                "error_rate": self._error_count / max(1, self._total_callbacks),
                "cached_callback_types": len(self._callback_types)
            }


# 旧的ConnectionManager和SessionManager类已移除，现在使用统一的HTTPSessionManager


class OpenTrekLLM(LargeLanguageModel):

    # --- Field Declarations for Pydantic ---
    base_url: str
    api_key: str
    model_name: str
    provider: str
    model_parameters: Dict[str, Any] = Field(default_factory=dict)
    stream: bool = True

    # Internal state, managed via PrivateAttr to avoid Pydantic validation
    _headers: Dict[str, str] = PrivateAttr()
    _session_manager: HTTPSessionManager = PrivateAttr()
    _connection_manager: HTTPConnectionManager = PrivateAttr()
    _callback_processor: CallbackProcessor = PrivateAttr()
    _metrics: HTTPMetrics = PrivateAttr()
    _instance_id: str = PrivateAttr()

    def __init__(self, **kwargs):
        """
        Initializes the OpenTrekLLM client.
        Pydantic will validate public fields from kwargs first, then this code runs.
        """
        super().__init__(**kwargs)

        # 生成实例唯一ID
        self._instance_id = f"opentrek_{id(self)}"

        # 创建LLM专用的HTTP会话配置
        session_config = HTTPSessionConfig.for_llm(
            limit=150,
            limit_per_host=40,
            total_timeout=240.0,
            max_retries=3,
            base_timeout=60.0  # LLM需要更长的基础超时时间
        )

        # 获取统一的HTTP会话管理器
        self._session_manager = get_session_manager(
            config=session_config,
            instance_id=self._instance_id,
            model_type="llm"
        )

        # 初始化连接管理器和指标
        self._connection_manager = self._session_manager.get_connection_manager()
        self._metrics = HTTPMetrics()

        # 设置请求头
        self._headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # 初始化回调处理器
        self._callback_processor = CallbackProcessor()

        logger.debug(f"OpenTrekLLM initialized: model={self.model_name}, session_manager={self._session_manager.instance_id}")

    @property
    def async_session(self) -> aiohttp.ClientSession:
        """通过统一HTTP会话管理器获取session"""
        return self._session_manager.async_session

    @property
    def sync_session(self) -> requests.Session:
        """通过统一HTTP会话管理器获取同步session"""
        return self._session_manager.sync_session
    
    def invoke(
        self,
        prompt_messages: list[PromptMessage],
        model: Optional[str] = None,
        credentials: Optional[dict] = None,
        model_parameters: Optional[dict] = None,
        tools: Optional[list[PromptMessageTool]] = None,
        stop: Optional[list[str]] = None,
        stream: Optional[bool] = None,
        user: Optional[str] = None,
        callbacks: Optional[list[Callback]] = None,
    ) -> Union[LLMResult, Generator[LLMResultChunk, None, None]]:
        # --- Start of new logic: Default + Override ---
        model = model or self.model_name
        stream = stream if stream is not None else self.stream
        
        # Merge model parameters: instance defaults < invoke parameters
        merged_params = self.model_parameters.copy()
        if model_parameters:
            merged_params.update(model_parameters)
        # --- End of new logic ---

        self.started_at = time.perf_counter()
        callbacks = callbacks or []
        self._trigger_before_invoke_callbacks(
            model=model, 
            credentials=credentials, 
            prompt_messages=prompt_messages,
            model_parameters=merged_params, 
            tools=tools, 
            stop=stop,
            stream=stream, 
            user=user, 
            callbacks=callbacks,
        )

        try:
            # Here we call our own `invoke_llm` method
            result = self.invoke_llm(
                tenant_id=getattr(self, 'tenant_id', 'unknown'), # get from instance attribute
                user_id=user or "unknown",
                provider=getattr(self, 'provider_name', 'opentrek'), # get from instance attribute
                model=model,
                credentials=credentials,
                model_parameters=merged_params,
                prompt_messages=prompt_messages,
                tools=tools,
                stop=list(stop) if stop else None,
                stream=stream,
            )
            # Dify's original logic has a complex handling of Generator, we simplify it here for now
            # In our client model, the return value is directly the Generator or LLMResult
            return result
        except Exception as e:
            self._trigger_invoke_error_callbacks(
                model=model, 
                ex=e, 
                credentials=credentials, 
                prompt_messages=prompt_messages,
                model_parameters=merged_params, 
                tools=tools, 
                stop=stop,
                stream=stream, 
                user=user, 
                callbacks=callbacks,
            )
            raise self._transform_invoke_error(e)

    def invoke_llm(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict, # `credentials` are passed in for compatibility with Dify, but we mainly use the configuration in `__init__`
            model_parameters: dict,
            prompt_messages: List[PromptMessage],
            tools: Optional[List[PromptMessageTool]] = None,
            stop: Optional[List[str]] = None,
            stream: bool = True,
    ) -> Union[LLMResult, Generator[LLMResultChunk, None, None]]:
        """调用大语言模型，生成响应或流式响应。"""
        messages = [
            {"role": msg.role.value if hasattr(msg.role, 'value') else msg.role, "content": msg.content}
            for msg in prompt_messages
        ]

        max_tokens = model_parameters.get("max_tokens", 2048)
        temperature = model_parameters.get("temperature", 0.9)

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
        }
        if stop:
            payload['stop'] = stop
        if tools:
             # Logic for handling tools needs to be added here based on the actual situation
            pass

        try:
            if stream:
                def stream_generator() -> Generator[LLMResultChunk, None, None]:
                    response = self._sync_session.post(self.base_url, json=payload, headers=self._headers, timeout=None, verify=False, stream=True)
                    response.raise_for_status() # Raise an exception for bad status codes
                    
                    buffer = b''
                    for chunk in response.iter_content(chunk_size=8192):
                        if not chunk:
                            continue
                        buffer += chunk
                        while b'\n' in buffer:
                            line, buffer = buffer.split(b'\n', 1)
                            data = line.decode('utf-8').strip()
                            if not data or data == "data: [DONE]":
                                continue
                            if data.startswith("data: "):
                                data_json = json.loads(data[6:])
                                if "choices" in data_json and data_json["choices"]:
                                    delta = data_json["choices"][0].get("delta", {})
                                    finish_reason = data_json["choices"][0].get("finish_reason", "")
                                    if "content" in delta:
                                        content_chunk = delta["content"]
                                        message = AssistantPromptMessage(
                                            content=content_chunk, 
                                            tool_calls=[]
                                            )
                                        yield LLMResultChunk(
                                            model=model,
                                            delta=LLMResultChunkDelta(
                                                index=0, # Simplified index
                                                message=message,
                                                finish_reason=finish_reason
                                            )
                                        )
                return stream_generator()
            else:
                response = self._sync_session.post(self.base_url, json=payload, headers=self._headers, timeout=None, verify=False)
                response.raise_for_status()
                response_data = response.json()
                choices = response_data.get("choices", [])
                content_result = choices[0]["message"]["content"] if choices else ""

                return LLMResult(
                    model=model,
                    prompt_messages=prompt_messages,
                    message=AssistantPromptMessage(
                        content=content_result.strip() if content_result else "",
                        tool_calls=[],
                    ),
                    usage=self._calc_response_usage(
                        model=model,
                        credentials=credentials,
                        prompt_tokens=0,  # response.usage.prompt_tokens
                        completion_tokens=0,  # response.usage.completion_tokens
                    )
                )

        except requests.RequestException as e:
            logger.error(f"调用 LLM 失败: {str(e)}")
            raise Exception(f"获取 LLM 响应失败: {str(e)}")

    async def ainvoke(
        self,
        prompt_messages: list[PromptMessage],
        model: Optional[str] = None,
        credentials: Optional[dict] = None, # `credentials` are passed in for compatibility with Dify, but we mainly use the configuration in `__init__`
        model_parameters: Optional[dict] = None,
        tools: Optional[list[PromptMessageTool]] = None,
        stop: Optional[list[str]] = None,
        stream: Optional[bool] = None,
        user: Optional[str] = None,
        callbacks: Optional[list[Callback]] = None,
    ) -> Union[LLMResult, AsyncGenerator[LLMResultChunk, None]]:
        # --- Start of new logic: Default + Override ---
        model = model or self.model_name
        stream = stream if stream is not None else self.stream

        # Merge model parameters: instance defaults < invoke parameters
        merged_params = self.model_parameters.copy()
        if model_parameters:
            merged_params.update(model_parameters)
        # --- End of new logic ---

        self.started_at = time.perf_counter()
        callbacks = callbacks or []
        self._trigger_before_invoke_callbacks(
            model=model, 
            credentials=credentials, 
            prompt_messages=prompt_messages,
            model_parameters=merged_params, 
            tools=tools, 
            stop=stop,
            stream=stream, 
            user=user, 
            callbacks=callbacks,
        )
        try:
            result = await self.ainvoke_llm(
                tenant_id=getattr(self, 'tenant_id', 'unknown'),
                user_id=user or "unknown",
                provider=getattr(self, 'provider_name', 'opentrek'),
                model=model,
                credentials=credentials,
                model_parameters=merged_params,
                prompt_messages=prompt_messages,
                tools=tools,
                stop=list(stop) if stop else None,
                stream=stream,
            )
            return result
        except Exception as e:
            self._trigger_invoke_error_callbacks(
                model=model, 
                ex=e, 
                credentials=credentials, 
                prompt_messages=prompt_messages,
                model_parameters=merged_params, 
                tools=tools, 
                stop=stop,
                stream=stream, 
                user=user, 
                callbacks=callbacks,
            )
            raise self._transform_invoke_error(e)

    async def ainvoke_llm(
            self,
            tenant_id: str,
            user_id: str,
            provider: str,
            model: str,
            credentials: dict,
            model_parameters: dict,
            prompt_messages: list[PromptMessage],
            tools: Optional[list['PromptMessageTool']] = None,
            stop: Optional[list[str]] = None,
            stream: bool = True,
    ) -> Union[LLMResult, AsyncGenerator[LLMResultChunk, None]]:
        messages = [{"role": msg.role.value if hasattr(msg.role, 'value') else msg.role, "content": msg.content} for msg in prompt_messages]

        max_tokens = model_parameters.get("max_tokens", 2048)
        temperature = model_parameters.get("temperature", 0.9)

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
        }
        if stop:
            payload['stop'] = stop
        if tools:
            pass # tool handling logic

        try:
            if stream:
                # 流式响应需要直接HTTP响应对象，不能使用连接管理器
                return await self._handle_stream_request(payload, model)
            else:
                # 非流式响应使用连接管理器进行重试请求
                # 注意：make_request_with_retry 返回已解析的字典，不是HTTP响应对象
                response_data = await self._connection_manager.make_request_with_retry(
                    self.async_session, self.base_url, self._headers, payload
                )

                # response_data 已经是解析后的字典，无需再调用 .json()
                choices = response_data.get('choices', [])
                content_result = choices[0].get('message', {}).get('content', '') if choices else ""

                return LLMResult(
                    model=model,
                    prompt_messages=prompt_messages,
                    message=AssistantPromptMessage(
                        content=content_result.strip() if content_result else "",
                        tool_calls=[],
                    ),
                    usage=self._calc_response_usage(
                        model=model,
                        credentials=credentials,
                        prompt_tokens=0, 
                        completion_tokens=0)
                )
        except Exception as e:
            logger.error(f"Async request failed: {str(e)}")
            raise Exception(f"Failed to get LLM response: {str(e)}")

    async def _handle_stream_request(self, payload: dict, model: str) -> OpentrekStream:
        """
        处理流式请求 - 直接使用HTTP响应对象

        流式响应需要原始的HTTP响应对象来进行流式读取，
        不能使用连接管理器的解析后字典
        """
        try:
            # 直接使用session进行流式请求，不通过连接管理器
            # 注意：不使用async with，让OpentrekStream管理响应的生命周期
            response = await self.async_session.post(
                self.base_url,
                headers=self._headers,
                json=payload
            )
            response.raise_for_status()
            # 返回OpentrekStream，它会负责管理响应对象的关闭
            return OpentrekStream(response, self.async_session, model)

        except Exception as e:
            logger.error(f"Stream request failed: {e}")
            raise Exception(f"Failed to get LLM stream response: {str(e)}")

    async def astream(
        self,
        prompt_messages: list[PromptMessage],
        callback: Callable[[str], None],
        model: Optional[str] = None,
        credentials: Optional[dict] = None,
        model_parameters: Optional[dict] = None,
        tools: Optional[list[PromptMessageTool]] = None,
        stop: Optional[list[str]] = None,
        user: Optional[str] = None,
        max_memory_mb: int = 500,  # 内存峰值控制
    ) -> str:
        """
        高性能异步流式调用LLM，支持实时回调函数处理流式内容
        
        Args:
            prompt_messages: 提示消息列表
            callback: 回调函数，接收每个流式内容块
            model: 模型名称
            credentials: 模型凭证
            model_parameters: 模型参数
            tools: 工具列表
            stop: 停止词列表
            user: 用户ID
            max_memory_mb: 最大内存使用限制(MB)
            
        Returns:
            完整的响应内容
        """
        # 使用默认配置
        model = model or self.model_name
        
        # 合并模型参数
        merged_params = self.model_parameters.copy()
        if model_parameters:
            merged_params.update(model_parameters)
        
        # 优化：使用list进行字符串拼接，避免O(n²)复杂度
        content_parts = []
        total_content_size = 0
        
        try:
            # 调用流式LLM，获取异步生成器
            stream_generator = await self.ainvoke_llm(
                tenant_id=getattr(self, 'tenant_id', 'unknown'),
                user_id=user or "unknown",
                provider=getattr(self, 'provider_name', 'opentrek'),
                model=model,
                credentials=credentials,
                model_parameters=merged_params,
                prompt_messages=prompt_messages,
                tools=tools,
                stop=list(stop) if stop else None,
                stream=True,  # 强制流式
            )
            
            # 获取流式处理指标
            metrics = stream_generator._metrics if hasattr(stream_generator, '_metrics') else StreamMetrics()
            
            # 处理流式响应 - 优化版本（带超时保护）
            try:
                async for chunk in stream_generator:
                    # 检查chunk结构，适配LLMResultChunk的数据结构
                    content = None
                    if hasattr(chunk, 'delta') and chunk.delta and hasattr(chunk.delta, 'message'):
                        content = chunk.delta.message.content
                    
                    if content:
                        content_parts.append(content)
                        total_content_size += len(content.encode('utf-8'))
                        
                        # 内存峰值控制 - 背压机制
                        if total_content_size > max_memory_mb * 1024 * 1024:
                            logger.warning(f"Content size ({total_content_size/1024/1024:.1f}MB) exceeds limit ({max_memory_mb}MB)")
                            # 触发背压：暂停处理或分批处理
                            await asyncio.sleep(0.01)  # 小延迟减缓处理速度
                        
                        # 使用优化的回调处理器
                        if callback:
                            await self._callback_processor.process_callback(callback, content, metrics)
            
            except (asyncio.TimeoutError, asyncio.CancelledError) as e:
                logger.warning(f"Stream processing cancelled/timeout: {e}")
                # 返回已收集的内容
                pass
            
            # O(n)复杂度的字符串拼接
            full_content = ''.join(content_parts)
            
            # 记录最终统计信息
            if hasattr(stream_generator, '_metrics'):
                final_stats = stream_generator.get_metrics()
                callback_stats = await self._callback_processor.get_stats()
                logger.info("astream completed - content_size: {}, chunks: {}, callbacks: {}, errors: {}",
                            len(full_content), 
                            final_stats.get('chunks_processed', 0),
                            callback_stats.get('total_callbacks', 0),
                            callback_stats.get('error_count', 0))
            
            return full_content
            
        except Exception as e:
            logger.error(f"Stream call failed: {e}")
            raise self._transform_invoke_error(e)

    async def close(self):
        """手动关闭resources - 通常不需要调用"""
        # 注意：不直接关闭session，让HTTPSessionManager管理
        logger.debug(f"OpenTrekLLM instance {self._instance_id} close() called - resources managed by HTTPSessionManager")

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # 在context manager中也不关闭session
        logger.debug("OpenTrekLLM context manager exit - resources managed by HTTPSessionManager")

    def __del__(self):
        # 清理时只记录日志，不强制关闭session
        logger.debug(f"OpenTrekLLM instance {self._instance_id} being garbage collected")

    async def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        callback_stats = await self._callback_processor.get_stats()
        return {
            "callback_processor": callback_stats,
            "connection_manager": {
                "max_retries": self._connection_manager.max_retries,
                "base_timeout": self._connection_manager.base_timeout
            }
        }

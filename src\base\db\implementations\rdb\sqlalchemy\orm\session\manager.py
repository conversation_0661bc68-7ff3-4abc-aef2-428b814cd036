"""
会话管理器

提供SQLAlchemy会话的生命周期管理，支持同步和异步操作
"""

import threading
from contextlib import contextmanager, asynccontextmanager
from typing import Optional, Dict, Any, AsyncGenerator
import logging

from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import async_sessionmaker, AsyncSession
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine

from ..config import ORMConnectionConfig
from ..exceptions import SessionError, wrap_session_error

logger = logging.getLogger(__name__)


class SessionManager:
    """同步会话管理器"""
    
    def __init__(self, engine: Engine, config: ORMConnectionConfig):
        """
        初始化会话管理器
        
        Args:
            engine: SQLAlchemy引擎
            config: ORM配置
        """
        self.engine = engine
        self.config = config
        
        # 创建会话工厂
        self.session_factory = sessionmaker(
            bind=engine,
            autoflush=config.session_autoflush,
            autocommit=config.session_autocommit,
            expire_on_commit=config.session_expire_on_commit
        )
        
        # 活跃会话跟踪
        self._active_sessions: Dict[str, Session] = {}
        self._lock = threading.RLock()
        
        logger.debug("Initialized sync session manager")
    
    @contextmanager
    def get_session(self, session_id: Optional[str] = None):
        """
        获取会话上下文管理器
        
        Args:
            session_id: 可选的会话ID
        
        Yields:
            Session实例
        """
        session = None
        try:
            session = self.session_factory()
            
            # 跟踪活跃会话
            if session_id:
                with self._lock:
                    self._active_sessions[session_id] = session
            
            yield session
            
        except Exception as e:
            if session:
                session.rollback()
            raise wrap_session_error(e, "session operation", session_id)
        finally:
            if session:
                session.close()
            
            # 清理会话跟踪
            if session_id:
                with self._lock:
                    self._active_sessions.pop(session_id, None)
    
    def create_session(self) -> Session:
        """
        创建新的会话实例
        
        Returns:
            Session实例
        """
        try:
            return self.session_factory()
        except Exception as e:
            raise wrap_session_error(e, "create session")
    
    def get_active_session_count(self) -> int:
        """获取活跃会话数量"""
        with self._lock:
            return len(self._active_sessions)
    
    def close_session(self, session_id: str) -> bool:
        """
        关闭指定会话
        
        Args:
            session_id: 会话ID
        
        Returns:
            是否成功关闭
        """
        with self._lock:
            session = self._active_sessions.pop(session_id, None)
            if session:
                try:
                    session.close()
                    return True
                except Exception as e:
                    logger.error(f"Error closing session {session_id}: {e}")
                    return False
            return False
    
    def close_all(self) -> None:
        """关闭所有活跃会话"""
        with self._lock:
            for session_id, session in list(self._active_sessions.items()):
                try:
                    session.close()
                except Exception as e:
                    logger.error(f"Error closing session {session_id}: {e}")
            
            self._active_sessions.clear()
            logger.debug("Closed all active sessions")


class AsyncSessionManager:
    """异步会话管理器"""
    
    def __init__(self, engine: AsyncEngine, config: ORMConnectionConfig):
        """
        初始化异步会话管理器
        
        Args:
            engine: 异步SQLAlchemy引擎
            config: ORM配置
        """
        self.engine = engine
        self.config = config
        
        # 创建异步会话工厂
        self.session_factory = async_sessionmaker(
            bind=engine,
            class_=AsyncSession,
            autoflush=config.session_autoflush,
            autocommit=config.session_autocommit,
            expire_on_commit=config.session_expire_on_commit
        )
        
        # 活跃会话跟踪
        self._active_sessions: Dict[str, AsyncSession] = {}
        self._lock = threading.RLock()
        
        logger.debug("Initialized async session manager")
    
    @asynccontextmanager
    async def get_session(self, session_id: Optional[str] = None) -> AsyncGenerator[AsyncSession, None]:
        """
        获取异步会话上下文管理器
        
        Args:
            session_id: 可选的会话ID
        
        Yields:
            AsyncSession实例
        """
        session = None
        try:
            session = self.session_factory()
            
            # 跟踪活跃会话
            if session_id:
                with self._lock:
                    self._active_sessions[session_id] = session
            
            yield session
            
        except Exception as e:
            if session:
                await session.rollback()
            raise wrap_session_error(e, "async session operation", session_id)
        finally:
            if session:
                await session.close()
            
            # 清理会话跟踪
            if session_id:
                with self._lock:
                    self._active_sessions.pop(session_id, None)
    
    async def create_session(self) -> AsyncSession:
        """
        创建新的异步会话实例
        
        Returns:
            AsyncSession实例
        """
        try:
            return self.session_factory()
        except Exception as e:
            raise wrap_session_error(e, "create async session")
    
    def get_active_session_count(self) -> int:
        """获取活跃会话数量"""
        with self._lock:
            return len(self._active_sessions)
    
    async def close_session(self, session_id: str) -> bool:
        """
        关闭指定异步会话
        
        Args:
            session_id: 会话ID
        
        Returns:
            是否成功关闭
        """
        with self._lock:
            session = self._active_sessions.pop(session_id, None)
        
        if session:
            try:
                await session.close()
                return True
            except Exception as e:
                logger.error(f"Error closing async session {session_id}: {e}")
                return False
        return False
    
    async def close_all(self) -> None:
        """关闭所有活跃异步会话"""
        sessions_to_close = []
        with self._lock:
            sessions_to_close = list(self._active_sessions.items())
            self._active_sessions.clear()
        
        for session_id, session in sessions_to_close:
            try:
                await session.close()
            except Exception as e:
                logger.error(f"Error closing async session {session_id}: {e}")
        
        logger.debug("Closed all active async sessions")

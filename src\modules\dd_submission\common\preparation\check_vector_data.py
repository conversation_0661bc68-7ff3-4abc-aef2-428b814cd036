"""
检查向量数据库中的数据

验证向量数据预填充是否真正插入了数据
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).resolve().parent
src_dir = current_dir.parent.parent.parent.parent
sys.path.insert(0, str(src_dir))

from service import get_client, cleanup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def check_vector_database():
    """检查向量数据库中的数据"""
    logger.info("🔍 开始检查向量数据库...")
    
    try:
        # 建立PgVector连接
        pgvector_client = await get_client("database.vdbs.pgvector")
        logger.info("✅ PgVector连接成功")

        # 确保异步连接已建立
        await pgvector_client._aensure_connected()
        logger.info("✅ PgVector异步连接建立成功")
        
        # 检查dd_embeddings表结构
        logger.info("📋 检查dd_embeddings表结构...")
        structure_query = """
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'dd_embeddings'
        ORDER BY ordinal_position
        """
        
        async with pgvector_client.session_manager.get_async_cursor() as conn:
            structure_result = await conn.fetch(structure_query)
            
            if structure_result:
                logger.info("   表结构:")
                for row in structure_result:
                    logger.info(f"     {row['column_name']}: {row['data_type']} ({'NULL' if row['is_nullable'] == 'YES' else 'NOT NULL'})")
            else:
                logger.warning("⚠️ dd_embeddings表不存在或无法访问")
                return
        
        # 检查总记录数
        logger.info("📊 检查总记录数...")
        count_query = "SELECT COUNT(*) as total_count FROM dd_embeddings"
        
        async with pgvector_client.session_manager.get_async_cursor() as conn:
            count_result = await conn.fetch(count_query)
            total_count = count_result[0]['total_count'] if count_result else 0
            logger.info(f"   总记录数: {total_count}")
        
        if total_count == 0:
            logger.warning("⚠️ dd_embeddings表中没有数据")
            return
        
        # 检查最新记录
        logger.info("📝 检查最新记录...")
        latest_query = """
        SELECT id, knowledge_id, data_row_id, field_id, data_layer, is_latest,
               create_time, update_time
        FROM dd_embeddings
        ORDER BY create_time DESC
        LIMIT 5
        """
        
        async with pgvector_client.session_manager.get_async_cursor() as conn:
            latest_result = await conn.fetch(latest_query)
            
            if latest_result:
                logger.info("   最新5条记录:")
                for i, row in enumerate(latest_result, 1):
                    logger.info(f"     {i}. ID={row['id']}, knowledge_id={row['knowledge_id']}")
                    logger.info(f"        data_row_id={row['data_row_id']}, field_id={row['field_id']}")
                    logger.info(f"        data_layer={row['data_layer']}, is_latest={row['is_latest']}")
                    logger.info(f"        向量维度={row['vector_dimension']}, 创建时间={row['create_time']}")
        
        # 检查is_latest=true的记录
        logger.info("🔄 检查is_latest=true的记录...")
        latest_true_query = """
        SELECT COUNT(*) as latest_count,
               COUNT(DISTINCT knowledge_id) as unique_knowledge_ids,
               COUNT(DISTINCT data_row_id) as unique_data_row_ids,
               COUNT(DISTINCT field_id) as unique_field_ids
        FROM dd_embeddings 
        WHERE is_latest = true
        """
        
        async with pgvector_client.session_manager.get_async_cursor() as conn:
            latest_true_result = await conn.fetch(latest_true_query)
            
            if latest_true_result:
                row = latest_true_result[0]
                logger.info(f"   is_latest=true记录数: {row['latest_count']}")
                logger.info(f"   唯一knowledge_id数: {row['unique_knowledge_ids']}")
                logger.info(f"   唯一data_row_id数: {row['unique_data_row_ids']}")
                logger.info(f"   唯一field_id数: {row['unique_field_ids']}")
        
        # 检查字段分布
        logger.info("📈 检查字段分布...")
        field_query = """
        SELECT field_id, COUNT(*) as count
        FROM dd_embeddings 
        WHERE is_latest = true
        GROUP BY field_id
        ORDER BY field_id
        """
        
        async with pgvector_client.session_manager.get_async_cursor() as conn:
            field_result = await conn.fetch(field_query)
            
            if field_result:
                logger.info("   字段分布:")
                for row in field_result:
                    field_name = "dr09" if row['field_id'] == 9 else "dr17" if row['field_id'] == 17 else f"field_{row['field_id']}"
                    logger.info(f"     {field_name} (field_id={row['field_id']}): {row['count']}条记录")
        
        # 检查数据层分布
        logger.info("🏗️ 检查数据层分布...")
        layer_query = """
        SELECT data_layer, COUNT(*) as count
        FROM dd_embeddings 
        WHERE is_latest = true
        GROUP BY data_layer
        ORDER BY data_layer
        """
        
        async with pgvector_client.session_manager.get_async_cursor() as conn:
            layer_result = await conn.fetch(layer_query)
            
            if layer_result:
                logger.info("   数据层分布:")
                for row in layer_result:
                    logger.info(f"     {row['data_layer']}: {row['count']}条记录")
        
        # 检查向量维度
        logger.info("📐 检查向量维度...")
        dimension_query = """
        SELECT array_length(embedding, 1) as dimension, COUNT(*) as count
        FROM dd_embeddings 
        WHERE is_latest = true
        GROUP BY array_length(embedding, 1)
        ORDER BY dimension
        """
        
        async with pgvector_client.session_manager.get_async_cursor() as conn:
            dimension_result = await conn.fetch(dimension_query)
            
            if dimension_result:
                logger.info("   向量维度分布:")
                for row in dimension_result:
                    logger.info(f"     {row['dimension']}维: {row['count']}条记录")
        
        logger.info("🎉 向量数据库检查完成")
        
    except Exception as e:
        logger.error(f"❌ 检查向量数据库失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await cleanup()


async def main():
    """主函数"""
    await check_vector_database()


if __name__ == "__main__":
    asyncio.run(main())

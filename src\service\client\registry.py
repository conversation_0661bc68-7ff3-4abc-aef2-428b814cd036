import logging
from typing import Any, Dict, List, Optional, Type
from dataclasses import dataclass, field
from datetime import datetime

from ..exceptions import ClientError

logger = logging.getLogger(__name__)


@dataclass
class ClientInfo:
    """客户端信息"""
    name: str
    target_class: str
    client_type: str  # "rdb", "vdb", "llm", "embedding"
    description: str = ""
    version: str = "1.0.0"
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


class ClientRegistry:
    """
    客户端注册表
    
    管理：
    - 客户端类型注册
    - 客户端元数据
    - 客户端发现
    """
    
    def __init__(self):
        self._registry: Dict[str, ClientInfo] = {}
        self._type_mapping: Dict[str, List[str]] = {
            "rdb": [],
            "vdb": [],
            "llm": [],
            "embedding": []
        }
        self._initialized = False
    
    async def initialize(self):
        """初始化注册表"""
        if self._initialized:
            return
        
        # 注册内置客户端类型
        await self._register_builtin_clients()
        
        self._initialized = True
        logger.info("客户端注册表初始化完成")
    
    async def _register_builtin_clients(self):
        """注册内置客户端类型"""
        builtin_clients = [
            # RDB客户端
            ClientInfo(
                name="mysql_sqlalchemy",
                target_class="base.db.implementations.rdb.mysql.sqlalchemy_client.MySQLSQLAlchemyClient",
                client_type="rdb",
                description="MySQL SQLAlchemy客户端",
                metadata={"database_type": "mysql", "driver": "sqlalchemy"}
            ),
            
            # VDB客户端
            ClientInfo(
                name="pgvector",
                target_class="base.db.implementations.vs.pgvector.client.PGVectorClient",
                client_type="vdb",
                description="PGVector向量数据库客户端",
                metadata={"database_type": "postgresql", "vector_support": True}
            ),
            
            # TODO: 添加更多内置客户端
        ]
        
        for client_info in builtin_clients:
            await self.register(client_info)
    
    async def register(self, client_info: ClientInfo):
        """
        注册客户端
        
        Args:
            client_info: 客户端信息
        """
        try:
            # 检查是否已注册
            if client_info.name in self._registry:
                logger.warning(f"客户端已注册，将覆盖: {client_info.name}")
            
            # 注册客户端
            self._registry[client_info.name] = client_info
            
            # 更新类型映射
            if client_info.client_type in self._type_mapping:
                if client_info.name not in self._type_mapping[client_info.client_type]:
                    self._type_mapping[client_info.client_type].append(client_info.name)
            
            logger.info(f"客户端注册成功: {client_info.name} ({client_info.client_type})")
            
        except Exception as e:
            logger.error(f"客户端注册失败: {e}")
            raise ClientError(f"Failed to register client: {e}") from e
    
    async def unregister(self, name: str) -> bool:
        """
        注销客户端
        
        Args:
            name: 客户端名称
            
        Returns:
            是否成功注销
        """
        try:
            if name not in self._registry:
                logger.warning(f"客户端未注册: {name}")
                return False
            
            client_info = self._registry[name]
            
            # 从注册表中移除
            del self._registry[name]
            
            # 从类型映射中移除
            if client_info.client_type in self._type_mapping:
                if name in self._type_mapping[client_info.client_type]:
                    self._type_mapping[client_info.client_type].remove(name)
            
            logger.info(f"客户端注销成功: {name}")
            return True
            
        except Exception as e:
            logger.error(f"客户端注销失败: {e}")
            return False
    
    def get(self, name: str) -> Optional[ClientInfo]:
        """
        获取客户端信息
        
        Args:
            name: 客户端名称
            
        Returns:
            客户端信息
        """
        return self._registry.get(name)
    
    def get_by_target(self, target_class: str) -> Optional[ClientInfo]:
        """
        根据目标类获取客户端信息
        
        Args:
            target_class: 目标类路径
            
        Returns:
            客户端信息
        """
        for client_info in self._registry.values():
            if client_info.target_class == target_class:
                return client_info
        return None
    
    def list_by_type(self, client_type: str) -> List[ClientInfo]:
        """
        根据类型列出客户端
        
        Args:
            client_type: 客户端类型
            
        Returns:
            客户端信息列表
        """
        if client_type not in self._type_mapping:
            return []
        
        return [
            self._registry[name] 
            for name in self._type_mapping[client_type]
            if name in self._registry
        ]
    
    def list_all(self) -> List[ClientInfo]:
        """列出所有客户端"""
        return list(self._registry.values())
    
    def search(self, **criteria) -> List[ClientInfo]:
        """
        搜索客户端
        
        Args:
            **criteria: 搜索条件
            
        Returns:
            匹配的客户端信息列表
        """
        results = []
        
        for client_info in self._registry.values():
            match = True
            
            for key, value in criteria.items():
                if hasattr(client_info, key):
                    if getattr(client_info, key) != value:
                        match = False
                        break
                elif key in client_info.metadata:
                    if client_info.metadata[key] != value:
                        match = False
                        break
                else:
                    match = False
                    break
            
            if match:
                results.append(client_info)
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取注册表统计信息"""
        stats = {
            "total_clients": len(self._registry),
            "by_type": {
                client_type: len(names)
                for client_type, names in self._type_mapping.items()
            },
            "registered_types": list(self._type_mapping.keys()),
            "client_names": list(self._registry.keys())
        }
        
        return stats
    
    async def cleanup(self):
        """清理注册表"""
        self._registry.clear()
        for client_type in self._type_mapping:
            self._type_mapping[client_type].clear()
        
        self._initialized = False
        logger.info("客户端注册表清理完成")
    
    def __len__(self) -> int:
        """返回注册的客户端数量"""
        return len(self._registry)
    
    def __contains__(self, name: str) -> bool:
        """检查客户端是否已注册"""
        return name in self._registry
    
    def __iter__(self):
        """迭代所有客户端信息"""
        return iter(self._registry.values())

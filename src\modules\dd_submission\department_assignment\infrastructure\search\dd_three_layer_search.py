"""
DD系统专用的三层搜索实现

基于通用三层搜索框架，实现DD部门职责分配的具体搜索逻辑
"""

import time
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

from modules.dd_submission.common.search.three_layer_search import (
    SearchLayerExecutor, FilterLayerExecutor, DecisionMaker,
    SearchLayerResult, SearchLayerConfig, FilterLayerConfig,
    SearchLayerType, FilterLayerType, ThreeLayerSearchEngine,
    ThreeLayerSearchBuilder
)
from ..models import DepartmentAssignmentRequest, HistoricalRecord
from ..search_builder import DynamicSearchBuilder
from ..nlp_processor import NLPProcessor
from modules.knowledge.dd.search import DDSearch
from ...core.department_recommender import TFIDFDepartmentRecommender


class DDSearchLayerExecutor(SearchLayerExecutor[HistoricalRecord, DepartmentAssignmentRequest]):
    """DD系统搜索层执行器"""
    
    def __init__(self, rdb_client, vdb_client):
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.search_builder = DynamicSearchBuilder()
        self.nlp_processor = NLPProcessor()
        self.dd_search = DDSearch(rdb_client, vdb_client)
        self.dept_recommender = TFIDFDepartmentRecommender(rdb_client)
    
    async def execute_exact_match(
        self, 
        request: DepartmentAssignmentRequest, 
        config: SearchLayerConfig
    ) -> SearchLayerResult[HistoricalRecord]:
        """执行精确匹配搜索"""
        start_time = time.time()
        
        try:
            logger.debug(f"开始精确匹配搜索: dr09='{request.dr09}', dr17='{request.dr17}'")
            
            # 构建精确匹配查询
            query = self.search_builder.create_exact_match_search(
                dr09=request.dr09,
                dr17=request.dr17,
                limit=config.max_results
            )
            
            sql, params = query.to_sql()
            
            # 执行查询
            response = await self.rdb_client.afetch_all(sql, params)
            
            if not response.success:
                raise Exception(f"数据库查询失败: {response.message}")
            
            # 转换为HistoricalRecord对象
            records = [
                HistoricalRecord.from_dict(record) 
                for record in (response.data or [])
            ]
            
            execution_time = (time.time() - start_time) * 1000
            
            return SearchLayerResult[HistoricalRecord](
                layer_type=SearchLayerType.EXACT_MATCH,
                success=True,
                results=records,
                execution_time_ms=execution_time,
                confidence_score=1.0 if records else 0.0,
                metadata={
                    "query_sql": sql,
                    "query_params": params,
                    "total_found": len(records)
                }
            )
            
        except Exception as e:
            logger.error(f"精确匹配搜索失败: {e}")
            return SearchLayerResult[HistoricalRecord](
                layer_type=SearchLayerType.EXACT_MATCH,
                success=False,
                execution_time_ms=(time.time() - start_time) * 1000,
                error_message=str(e)
            )
    
    async def execute_hybrid_search(
        self, 
        request: DepartmentAssignmentRequest, 
        config: SearchLayerConfig
    ) -> SearchLayerResult[HistoricalRecord]:
        """执行混合搜索"""
        start_time = time.time()
        
        try:
            logger.debug(f"开始混合搜索: dr09='{request.dr09}', dr17='{request.dr17}'")
            
            # 对dr09和dr17进行分词处理，并获取去重后的关键词
            keywords_info = self.nlp_processor.extract_keywords_from_fields(request.dr09, request.dr17)

            # 使用all_keywords构建混合搜索查询（包含原始字段和去重后的关键词）
            query_text = ' '.join(keywords_info['all_keywords'])
            
            # 执行混合搜索
            search_results = await self.dd_search.hybrid_search(
                query_text=query_text,
                vector_weight=request.vector_weight or 0.7,
                text_weight=request.text_weight or 0.3,
                limit=config.max_results,
                similarity_threshold=config.min_confidence
            )
            
            # 转换为HistoricalRecord对象
            records = []
            for result in search_results:
                try:
                    record = HistoricalRecord.from_dict(result)
                    records.append(record)
                except Exception as e:
                    logger.warning(f"转换搜索结果失败: {e}")
                    continue
            
            execution_time = (time.time() - start_time) * 1000
            avg_confidence = sum(r.get("score", 0) for r in search_results) / len(search_results) if search_results else 0
            
            return SearchLayerResult[HistoricalRecord](
                layer_type=SearchLayerType.HYBRID_SEARCH,
                success=True,
                results=records,
                execution_time_ms=execution_time,
                confidence_score=avg_confidence,
                metadata={
                    "query_text": query_text,
                    "keywords_dr09": keywords_dr09,
                    "keywords_dr17": keywords_dr17,
                    "vector_weight": request.vector_weight or 0.7,
                    "text_weight": request.text_weight or 0.3,
                    "total_found": len(records)
                }
            )
            
        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            return SearchLayerResult[HistoricalRecord](
                layer_type=SearchLayerType.HYBRID_SEARCH,
                success=False,
                execution_time_ms=(time.time() - start_time) * 1000,
                error_message=str(e)
            )
    
    async def execute_tfidf_fallback(
        self, 
        request: DepartmentAssignmentRequest, 
        config: SearchLayerConfig
    ) -> SearchLayerResult[HistoricalRecord]:
        """执行TF-IDF兜底搜索"""
        start_time = time.time()
        
        try:
            logger.debug("开始TF-IDF部门推荐")
            
            # 执行TF-IDF推荐
            recommendations = await self.dept_recommender.recommend_departments(
                dr09=request.dr09,
                dr17=request.dr17,
                top_k=config.custom_params.get("top_k", 3),
                min_confidence=config.min_confidence
            )
            
            # 创建虚拟的HistoricalRecord对象表示推荐结果
            records = []
            for rec in recommendations:
                # 创建一个表示推荐结果的记录
                virtual_record = HistoricalRecord(
                    id=f"tfidf_{rec.dept_id}",
                    submission_id=f"recommendation_{rec.dept_id}",
                    dr09=request.dr09,
                    dr17=request.dr17,
                    dr22=rec.dept_id,  # 推荐的部门
                    set_value=request.set_value,
                    report_type=request.report_type,
                    submission_type=request.submission_type,
                    dr01=request.dr01
                )
                records.append(virtual_record)

            execution_time = (time.time() - start_time) * 1000
            avg_confidence = sum(r.confidence_score for r in recommendations) / len(recommendations) if recommendations else 0
            
            return SearchLayerResult[HistoricalRecord](
                layer_type=SearchLayerType.TFIDF_FALLBACK,
                success=True,
                results=records,
                execution_time_ms=execution_time,
                confidence_score=avg_confidence,
                metadata={
                    "recommendation_text": recommendation_text,
                    "recommendations": recommendations,
                    "total_found": len(records)
                }
            )
            
        except Exception as e:
            logger.error(f"TF-IDF兜底搜索失败: {e}")
            return SearchLayerResult[HistoricalRecord](
                layer_type=SearchLayerType.TFIDF_FALLBACK,
                success=False,
                execution_time_ms=(time.time() - start_time) * 1000,
                error_message=str(e)
            )


class DDFilterLayerExecutor(FilterLayerExecutor[HistoricalRecord, DepartmentAssignmentRequest]):
    """DD系统筛选层执行器"""
    
    def __init__(self):
        pass
    
    async def filter_by_set_type(
        self, 
        results: List[HistoricalRecord], 
        request: DepartmentAssignmentRequest, 
        config: FilterLayerConfig
    ) -> List[HistoricalRecord]:
        """按集合类型筛选"""
        if not request.set_value:
            return results
        
        filtered = [
            record for record in results 
            if record.set_value == request.set_value
        ]
        
        logger.debug(f"集合类型筛选: {len(results)} -> {len(filtered)} (set_value={request.set_value})")
        return filtered
    
    async def filter_by_report_type(
        self, 
        results: List[HistoricalRecord], 
        request: DepartmentAssignmentRequest, 
        config: FilterLayerConfig
    ) -> List[HistoricalRecord]:
        """按报表类型筛选"""
        if not request.report_type:
            return results
        
        filtered = [
            record for record in results 
            if record.report_type == request.report_type
        ]
        
        logger.debug(f"报表类型筛选: {len(results)} -> {len(filtered)} (report_type={request.report_type})")
        return filtered
    
    async def filter_by_submission_type(
        self, 
        results: List[HistoricalRecord], 
        request: DepartmentAssignmentRequest, 
        config: FilterLayerConfig
    ) -> List[HistoricalRecord]:
        """按提交类型筛选"""
        if not request.submission_type:
            return results
        
        filtered = [
            record for record in results 
            if record.submission_type == request.submission_type
        ]
        
        logger.debug(f"提交类型筛选: {len(results)} -> {len(filtered)} (submission_type={request.submission_type})")
        return filtered
    
    async def filter_by_data_layer(
        self, 
        results: List[HistoricalRecord], 
        request: DepartmentAssignmentRequest, 
        config: FilterLayerConfig
    ) -> List[HistoricalRecord]:
        """按数据层筛选"""
        if not request.dr01:
            return results
        
        filtered = [
            record for record in results 
            if record.dr01 == request.dr01
        ]
        
        logger.debug(f"数据层筛选: {len(results)} -> {len(filtered)} (dr01={request.dr01})")
        return filtered


class DDDecisionMaker(DecisionMaker[HistoricalRecord]):
    """DD系统决策器"""
    
    async def make_final_decision(self, results: List[HistoricalRecord]) -> Optional[HistoricalRecord]:
        """做出最终决策"""
        if not results:
            return None
        
        if len(results) == 1:
            return results[0]
        
        # 多个结果时，选择最常见的部门分配
        dept_counts = {}
        for record in results:
            dept = record.dr22
            if dept in dept_counts:
                dept_counts[dept] += 1
            else:
                dept_counts[dept] = 1
        
        # 选择出现次数最多的部门
        most_common_dept = max(dept_counts.keys(), key=lambda k: dept_counts[k])
        
        # 返回该部门的第一个记录
        for record in results:
            if record.dr22 == most_common_dept:
                return record
        
        return results[0]  # 兜底返回第一个
    
    def calculate_confidence_level(self, results: List[HistoricalRecord]) -> str:
        """计算置信度等级"""
        if not results:
            return "no_match"
        elif len(results) == 1:
            return "unique"
        elif len(results) <= 3:
            return "high"
        elif len(results) <= 10:
            return "medium"
        else:
            return "low"

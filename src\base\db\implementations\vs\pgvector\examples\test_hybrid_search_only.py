#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PGVector客户端混合搜索功能独立测试

这个脚本专门测试新增的混合搜索方法：
1. ahybrid_vector_exact_search - 混合向量搜索和精确搜索
2. ahybrid_vector_fuzzy_search - 混合向量搜索和模糊搜索

作者: HSBC Knowledge Team
日期: 2025-01-20
"""

import sys
import os
import time
import asyncio
import random
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../../..'))

from base.db.implementations.vs.pgvector.client import PGVectorClient
from base.db.base.vdb.core.models import CollectionSchema, FieldSchema, FieldType
from omegaconf import DictConfig


async def test_hybrid_search_methods():
    """测试混合搜索方法"""
    print("🚀 开始测试PGVector混合搜索方法")
    print("=" * 60)
    
    # 创建测试客户端
    config = DictConfig({
        "host": "**************",
        "port": 30146,
        "database": "hsbc_vectordb",
        "username": "pgvector",
        "password": "pgvector",
        "min_connections": 2,
        "max_connections": 20,
        "pool_size": 10,
        "max_overflow": 15
    })
    
    client = PGVectorClient(config)
    collection_name = "test_hybrid_search_demo"
    
    try:
        print("\n📋 准备测试环境...")
        
        # 清理已存在的集合
        if await client.ahas_collection(collection_name):
            await client.adrop_collection(collection_name)
            print(f"   ✅ 清理已存在的集合: {collection_name}")
        
        # 创建测试集合
        fields = [
            FieldSchema(name="id", dtype=FieldType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="title", dtype=FieldType.VARCHAR, max_length=200),
            FieldSchema(name="content", dtype=FieldType.VARCHAR, max_length=1000),
            FieldSchema(name="embedding", dtype=FieldType.FLOAT_VECTOR, dim=64),
            FieldSchema(name="category", dtype=FieldType.VARCHAR, max_length=50),
            FieldSchema(name="score", dtype=FieldType.FLOAT),
            FieldSchema(name="tags", dtype=FieldType.VARCHAR, max_length=500),
        ]
        
        schema = CollectionSchema(fields=fields, description="混合搜索测试集合")
        await client.acreate_collection(collection_name, schema)
        print(f"   ✅ 创建测试集合: {collection_name}")
        
        # 插入测试数据
        test_data = [
            {
                "title": "机器学习基础教程",
                "content": "本文介绍机器学习的基本概念和算法，包括监督学习、无监督学习等",
                "embedding": np.random.random(64).tolist(),
                "category": "技术",
                "score": 0.9,
                "tags": "机器学习,教程,基础"
            },
            {
                "title": "深度学习实战指南",
                "content": "深度学习是机器学习的一个重要分支，本文详细介绍神经网络的原理",
                "embedding": np.random.random(64).tolist(),
                "category": "技术",
                "score": 0.8,
                "tags": "深度学习,实战,神经网络"
            },
            {
                "title": "Python编程入门",
                "content": "Python是一种简单易学的编程语言，适合初学者学习",
                "embedding": np.random.random(64).tolist(),
                "category": "编程",
                "score": 0.7,
                "tags": "Python,编程,入门"
            },
            {
                "title": "数据科学概论",
                "content": "数据科学结合了统计学、计算机科学和领域专业知识",
                "embedding": np.random.random(64).tolist(),
                "category": "技术",
                "score": 0.6,
                "tags": "数据科学,统计学,概论"
            },
            {
                "title": "人工智能的未来",
                "content": "人工智能技术正在快速发展，将对社会产生深远影响",
                "embedding": np.random.random(64).tolist(),
                "category": "科技",
                "score": 0.85,
                "tags": "人工智能,未来,科技"
            }
        ]
        
        insert_result = await client.ainsert(collection_name, test_data)
        print(f"   ✅ 插入测试数据: {insert_result.get('insert_count', 0)} 条记录")
        
        # 测试混合向量精确搜索
        print("\n🔍 测试混合向量精确搜索...")
        query_vector = np.random.random(64).tolist()
        exact_conditions = {
            "category": "技术",
            "score": 0.7  # 这里应该是 >= 0.7 的逻辑，但简化为等值查询
        }
        
        print(f"   查询向量维度: {len(query_vector)}")
        print(f"   精确条件: {exact_conditions}")
        
        start_time = time.time()
        exact_results = await client.ahybrid_vector_exact_search(
            collection_name=collection_name,
            query_vector=query_vector,
            anns_field="embedding",
            exact_conditions=exact_conditions,
            vector_weight=0.7,
            exact_weight=0.3,
            vector_limit=10,
            exact_limit=10,
            final_limit=5
        )
        duration = time.time() - start_time
        
        print(f"   ✅ 混合向量精确搜索完成 ({duration:.3f}s)")
        print(f"   📊 搜索结果 ({len(exact_results)} 条):")
        
        for i, result in enumerate(exact_results, 1):
            entity_data = result.entity.data
            print(f"      {i}. ID={result.id}, 距离={result.distance:.4f}")
            print(f"         标题: {entity_data.get('title', 'N/A')}")
            print(f"         类别: {entity_data.get('category', 'N/A')}")
            print(f"         向量分数: {entity_data.get('vector_score', 0):.3f}")
            print(f"         精确分数: {entity_data.get('exact_score', 0):.3f}")
            print(f"         综合分数: {entity_data.get('combined_score', 0):.3f}")
            print(f"         搜索类型: {entity_data.get('search_type', 'unknown')}")
        
        # 测试混合向量模糊搜索
        print("\n🔍 测试混合向量模糊搜索...")
        fuzzy_conditions = {
            "title": "学习",
            "content": "机器"
        }
        
        print(f"   查询向量维度: {len(query_vector)}")
        print(f"   模糊条件: {fuzzy_conditions}")
        
        start_time = time.time()
        fuzzy_results = await client.ahybrid_vector_fuzzy_search(
            collection_name=collection_name,
            query_vector=query_vector,
            anns_field="embedding",
            fuzzy_conditions=fuzzy_conditions,
            vector_weight=0.6,
            fuzzy_weight=0.4,
            vector_limit=10,
            fuzzy_limit=10,
            final_limit=5,
            case_sensitive=False
        )
        duration = time.time() - start_time
        
        print(f"   ✅ 混合向量模糊搜索完成 ({duration:.3f}s)")
        print(f"   📊 搜索结果 ({len(fuzzy_results)} 条):")
        
        for i, result in enumerate(fuzzy_results, 1):
            entity_data = result.entity.data
            print(f"      {i}. ID={result.id}, 距离={result.distance:.4f}")
            print(f"         标题: {entity_data.get('title', 'N/A')}")
            print(f"         内容: {entity_data.get('content', 'N/A')[:50]}...")
            print(f"         向量分数: {entity_data.get('vector_score', 0):.3f}")
            print(f"         模糊分数: {entity_data.get('fuzzy_score', 0):.3f}")
            print(f"         综合分数: {entity_data.get('combined_score', 0):.3f}")
            print(f"         搜索类型: {entity_data.get('search_type', 'unknown')}")
        
        # 测试权重归一化
        print("\n⚖️  测试权重归一化...")
        start_time = time.time()
        weight_test_results = await client.ahybrid_vector_exact_search(
            collection_name=collection_name,
            query_vector=query_vector,
            anns_field="embedding",
            exact_conditions={"category": "技术"},
            vector_weight=0.8,  # 权重总和 > 1.0，测试归一化
            exact_weight=0.4,
            final_limit=3
        )
        duration = time.time() - start_time
        
        print(f"   ✅ 权重归一化测试完成 ({duration:.3f}s)")
        print(f"   📊 结果数量: {len(weight_test_results)}")
        
        print("\n🎉 所有混合搜索测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试数据
        try:
            if await client.ahas_collection(collection_name):
                await client.adrop_collection(collection_name)
                print(f"\n🧹 清理测试集合: {collection_name}")
        except Exception as e:
            print(f"\n⚠️  清理失败: {e}")
        
        # 断开连接
        await client.adisconnect()
        print("🔌 已断开数据库连接")
    
    return True


async def main():
    """主函数"""
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    
    try:
        success = await test_hybrid_search_methods()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        return 2
    except Exception as e:
        print(f"\n\n❌ 测试执行失败: {e}")
        return 3


if __name__ == "__main__":
    exit(asyncio.run(main()))

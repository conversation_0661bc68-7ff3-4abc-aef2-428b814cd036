"""
DD系统部门管理API路由

提供部门的完整CRUD操作接口，包括：
- 创建部门
- 获取部门详情
- 更新部门信息
- 删除部门
- 查询部门列表
"""

import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Depends

logger = logging.getLogger(__name__)

from api.knowledge.models.response_models import (
    ListResponse, CreateResponse, UpdateResponse, DeleteResponse, DetailResponse
)
from ..models.requests import DepartmentCreateRequest, DepartmentUpdateRequest
from ..models.responses import DepartmentResponse
from ..models.enums import DepartmentTypeEnum
from ..dependencies.common import get_dd_crud, validate_pagination
from ..utils.helpers import format_response, validate_dept_id

# 创建路由器
router = APIRouter(tags=["DD部门管理"], prefix="/departments")


@router.post("/", response_model=CreateResponse, summary="创建部门")
async def create_department(
    request: DepartmentCreateRequest,
    dd_crud = Depends(get_dd_crud)
):
    """
    创建新部门
    
    - **dept_id**: 部门ID，必须唯一，格式：字母开头，可包含字母、数字、下划线
    - **dept_name**: 部门名称
    - **dept_desc**: 部门描述（可选）
    - **dept_type**: 部门类型（normal/special）
    - **is_active**: 是否激活（默认true）
    """
    try:
        # 验证部门ID格式
        if not validate_dept_id(request.dept_id):
            raise HTTPException(
                status_code=400, 
                detail="部门ID格式无效，必须以字母开头，可包含字母、数字、下划线，长度3-30字符"
            )
        
        dept_data = request.model_dump()
        dept_id = await dd_crud.create_department(dept_data)
        
        return CreateResponse(
            success=True,
            message="部门创建成功",
            data={"dept_id": dept_id}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建部门失败: {e}")
        raise HTTPException(status_code=400, detail=f"创建部门失败: {str(e)}")


@router.get("/{dept_id}", response_model=DetailResponse, summary="获取部门详情")
async def get_department(
    dept_id: str,
    dd_crud = Depends(get_dd_crud)
):
    """
    根据部门ID获取部门详情
    
    - **dept_id**: 部门ID
    """
    try:
        department = await dd_crud.get_department(dept_id)
        
        if not department:
            raise HTTPException(status_code=404, detail=f"部门不存在: {dept_id}")
        
        return DetailResponse(
            success=True,
            message="获取部门详情成功",
            data=department
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取部门详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取部门详情失败: {str(e)}")


@router.put("/{dept_id}", response_model=UpdateResponse, summary="更新部门信息")
async def update_department(
    dept_id: str,
    request: DepartmentUpdateRequest,
    dd_crud = Depends(get_dd_crud)
):
    """
    更新部门信息
    
    - **dept_id**: 部门ID
    - **dept_name**: 部门名称（可选）
    - **dept_desc**: 部门描述（可选）
    - **dept_type**: 部门类型（可选）
    - **is_active**: 是否激活（可选）
    """
    try:
        # 检查部门是否存在
        existing_dept = await dd_crud.get_department(dept_id)
        if not existing_dept:
            raise HTTPException(status_code=404, detail=f"部门不存在: {dept_id}")
        
        # 只更新非空字段
        update_data = {k: v for k, v in request.model_dump().items() if v is not None}
        
        if not update_data:
            raise HTTPException(status_code=400, detail="没有提供要更新的字段")
        
        success = await dd_crud.update_department(dept_id, update_data)
        
        if not success:
            raise HTTPException(status_code=400, detail="更新部门失败")
        
        return UpdateResponse(
            success=True,
            message="部门更新成功",
            data={"dept_id": dept_id}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新部门失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新部门失败: {str(e)}")


@router.delete("/{dept_id}", response_model=DeleteResponse, summary="删除部门")
async def delete_department(
    dept_id: str,
    dd_crud = Depends(get_dd_crud)
):
    """
    删除部门
    
    - **dept_id**: 部门ID
    """
    try:
        # 检查部门是否存在
        existing_dept = await dd_crud.get_department(dept_id)
        if not existing_dept:
            raise HTTPException(status_code=404, detail=f"部门不存在: {dept_id}")
        
        success = await dd_crud.delete_department(dept_id)
        
        if not success:
            raise HTTPException(status_code=400, detail="删除部门失败")
        
        return DeleteResponse(
            success=True,
            message="部门删除成功",
            data={"dept_id": dept_id}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除部门失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除部门失败: {str(e)}")


@router.get("/", response_model=ListResponse, summary="查询部门列表")
async def list_departments(
    dept_type: Optional[DepartmentTypeEnum] = Query(None, description="部门类型过滤"),
    is_active: Optional[bool] = Query(None, description="激活状态过滤"),
    pagination = Depends(validate_pagination),
    dd_crud = Depends(get_dd_crud)
):
    """
    查询部门列表，支持分页和过滤
    
    - **dept_type**: 部门类型过滤（可选）
    - **is_active**: 激活状态过滤（可选）
    - **page**: 页码（默认1）
    - **page_size**: 每页数量（默认20，最大100）
    """
    try:
        page, page_size, offset = pagination
        
        # 构建查询参数
        filters = {}
        if dept_type is not None:
            filters["dept_type"] = dept_type.value
        if is_active is not None:
            filters["is_active"] = is_active
        
        # 查询部门列表
        departments = await dd_crud.list_departments(
            **filters,
            limit=page_size,
            offset=offset
        )
        
        # 查询总数（简化处理，实际应该有专门的count方法）
        total_departments = await dd_crud.list_departments(**filters)
        total = len(total_departments)
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        return ListResponse(
            success=True,
            message="查询部门列表成功",
            data={
                "items": departments,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }
        )
    except Exception as e:
        logger.error(f"查询部门列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询部门列表失败: {str(e)}")

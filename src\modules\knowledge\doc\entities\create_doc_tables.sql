-- ==========================================
-- 文档相关表结构设计 - 建表脚本
-- 创建时间: 2025-01-17
-- 数据库: MySQL 8.0+
-- 描述: RAG文档管理系统数据库表结构
-- 表名前缀: doc_
-- ==========================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ==========================================
-- 1. 文档表 (doc_documents)
-- ==========================================
DROP TABLE IF EXISTS `doc_documents`;
CREATE TABLE `doc_documents` (
  `doc_id` VARCHAR(255) NOT NULL COMMENT '文档ID（UUID格式）',
  `knowledge_id` VARCHAR(255) NOT NULL COMMENT '知识库ID',
  `doc_name` VARCHAR(255) NOT NULL COMMENT '文档名称',
  `doc_type` VARCHAR(255) DEFAULT NULL COMMENT '文档类型：outside/inside',
  `author` VARCHAR(255) DEFAULT NULL COMMENT '作者',
  `vector_similarity_weight` FLOAT DEFAULT NULL COMMENT '向量相似度权重',
  `similarity_threshold` FLOAT DEFAULT NULL COMMENT '相似度阈值',
  `chunk_nums` INT DEFAULT 0 COMMENT '分块总数',
  `percentage` FLOAT DEFAULT 0 COMMENT '分块进度',
  `parse_type` VARCHAR(50) NOT NULL COMMENT '解析方式：auto/pdf/docx/txt/ocr',
  `status` VARCHAR(50) NOT NULL COMMENT '解析状态：pending/processing/completed/failed/cancelled',
  `parse_end_time` DATETIME DEFAULT NULL COMMENT '解析结束时间',
  `parse_message` TEXT DEFAULT NULL COMMENT '解析状态描述',
  `location` VARCHAR(255) NOT NULL COMMENT '文件存储位置',
  `doc_format` VARCHAR(50) NOT NULL COMMENT '文件格式：pdf/docx/doc/txt/md/html/xml',
  `doc_ocr_result_path` VARCHAR(255) DEFAULT NULL COMMENT 'OCR识别结果路径',
  `metadata` TEXT DEFAULT NULL COMMENT '文档源数据',
  `task_id` VARCHAR(255) DEFAULT NULL COMMENT '任务队列ID',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否生效：1-生效，0-失效',
  PRIMARY KEY (`doc_id`),
  INDEX `idx_doc_knowledge` (`knowledge_id`),
  INDEX `idx_doc_status` (`status`),
  INDEX `idx_doc_parse_type` (`parse_type`),
  INDEX `idx_doc_format` (`doc_format`),
  INDEX `idx_doc_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档表';

-- ==========================================
-- 2. 分块表 (doc_chunks)
-- ==========================================
DROP TABLE IF EXISTS `doc_chunks`;
CREATE TABLE `doc_chunks` (
  `chunk_id` VARCHAR(255) NOT NULL COMMENT '分块ID',
  `doc_id` VARCHAR(255) NOT NULL COMMENT '文档ID',
  `chapter_layer` VARCHAR(100) DEFAULT NULL COMMENT '文档章节层级信息',
  `parent_id` VARCHAR(64) DEFAULT NULL COMMENT '父分块ID',
  `sub_chunk_ids` JSON DEFAULT NULL COMMENT '子分块ID列表',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否生效：1-生效，0-失效',
  PRIMARY KEY (`chunk_id`),
  INDEX `idx_chunk_doc_id` (`doc_id`),
  INDEX `idx_chunk_parent_id` (`parent_id`),
  INDEX `idx_chunk_created_time` (`created_time`),
  CONSTRAINT `fk_chunk_document` FOREIGN KEY (`doc_id`) REFERENCES `doc_documents` (`doc_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分块表';

-- ==========================================
-- 3. 分块信息表 (doc_chunks_info)
-- ==========================================
DROP TABLE IF EXISTS `doc_chunks_info`;
CREATE TABLE `doc_chunks_info` (
  `chunk_info_id` VARCHAR(255) NOT NULL COMMENT '分块信息ID（UUID格式）',
  `chunk_id` VARCHAR(255) NOT NULL COMMENT '关联的分块ID',
  `info_type` VARCHAR(255) NOT NULL COMMENT '信息类型：content/keyword/summary/metadata/tags',
  `info_value` TEXT NOT NULL COMMENT '信息值',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否生效：1-生效，0-失效',
  PRIMARY KEY (`chunk_info_id`),
  INDEX `idx_chunk_info_chunk_id` (`chunk_id`),
  INDEX `idx_chunk_info_type` (`info_type`),
  INDEX `idx_chunk_info_created_time` (`created_time`),
  CONSTRAINT `fk_chunk_info_chunk` FOREIGN KEY (`chunk_id`) REFERENCES `doc_chunks` (`chunk_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分块信息表';

-- ==========================================
-- 4. 文档别名映射表 (doc_document_alias)
-- ==========================================
DROP TABLE IF EXISTS `doc_document_alias`;
CREATE TABLE `doc_document_alias` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `doc_id` VARCHAR(64) NOT NULL COMMENT '文件ID',
  `doc_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
  `cleaned_name` VARCHAR(255) NOT NULL COMMENT '别名',
  PRIMARY KEY (`id`),
  INDEX `idx_alias_doc_id` (`doc_id`),
  INDEX `idx_alias_cleaned_name` (`cleaned_name`),
  CONSTRAINT `fk_alias_document` FOREIGN KEY (`doc_id`) REFERENCES `doc_documents` (`doc_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档别名映射表';

-- ==========================================
-- 5. 类别管理表 (doc_categories)
-- ==========================================
DROP TABLE IF EXISTS `doc_categories`;
CREATE TABLE `doc_categories` (
  `cate_id` VARCHAR(255) NOT NULL COMMENT '类别ID',
  `cate_name` VARCHAR(255) NOT NULL COMMENT '类别名称',
  `cate_status` VARCHAR(255) DEFAULT NULL COMMENT '类别状态：active/inactive/archived',
  `cate_layer` INT NOT NULL COMMENT '类别层级',
  `parent_id` VARCHAR(64) DEFAULT NULL COMMENT '父类别ID',
  `sub_categories_ids` JSON DEFAULT NULL COMMENT '子类别ID列表',
  PRIMARY KEY (`cate_id`),
  INDEX `idx_category_parent_id` (`parent_id`),
  INDEX `idx_category_layer` (`cate_layer`),
  INDEX `idx_category_status` (`cate_status`),
  INDEX `idx_category_name` (`cate_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='类别管理表';

-- ==========================================
-- 6. 文档类别表 (doc_document_categories)
-- ==========================================
DROP TABLE IF EXISTS `doc_document_categories`;
CREATE TABLE `doc_document_categories` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `doc_id` VARCHAR(255) NOT NULL COMMENT '文件ID',
  `cate_id` VARCHAR(255) NOT NULL COMMENT '类别ID',
  `cate_layer` INT NOT NULL COMMENT '类别层级',
  `doc_name` VARCHAR(255) NOT NULL COMMENT '文件名称',
  `doc_status` VARCHAR(255) DEFAULT NULL COMMENT '文件状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_doc_category` (`doc_id`, `cate_id`),
  INDEX `idx_doc_category_cate_id` (`cate_id`),
  INDEX `idx_doc_category_layer` (`cate_layer`),
  CONSTRAINT `fk_doc_category_document` FOREIGN KEY (`doc_id`) REFERENCES `doc_documents` (`doc_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_doc_category_category` FOREIGN KEY (`cate_id`) REFERENCES `doc_categories` (`cate_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档类别表';

-- ==========================================
-- 7. 类别关系表 (doc_category_relationship)
-- ==========================================
DROP TABLE IF EXISTS `doc_category_relationship`;
CREATE TABLE `doc_category_relationship` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_cate_id` VARCHAR(255) NOT NULL COMMENT '源类别ID',
  `target_cate_id` VARCHAR(255) NOT NULL COMMENT '目标类别ID',
  `rel_type` VARCHAR(255) DEFAULT NULL COMMENT '关系类型：reference/dependency/similarity/parent_child/related',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_relation` (`source_cate_id`, `target_cate_id`, `rel_type`),
  INDEX `idx_category_rel_source` (`source_cate_id`),
  INDEX `idx_category_rel_target` (`target_cate_id`),
  INDEX `idx_category_rel_type` (`rel_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='类别关系表';

-- ==========================================
-- 8. 文档关系表 (doc_document_relationship)
-- ==========================================
DROP TABLE IF EXISTS `doc_document_relationship`;
CREATE TABLE `doc_document_relationship` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_doc_id` VARCHAR(255) NOT NULL COMMENT '源文件ID',
  `source_doc_name` VARCHAR(255) DEFAULT NULL COMMENT '源文件名称',
  `target_doc_id` VARCHAR(255) NOT NULL COMMENT '目标文件ID',
  `target_doc_name` VARCHAR(255) DEFAULT NULL COMMENT '目标文件名称',
  `rel_type` VARCHAR(255) DEFAULT NULL COMMENT '关系类型：reference/dependency/similarity/parent_child/related',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_document_relation` (`source_doc_id`, `target_doc_id`, `rel_type`),
  INDEX `idx_doc_rel_source` (`source_doc_id`),
  INDEX `idx_doc_rel_target` (`target_doc_id`),
  INDEX `idx_doc_rel_type` (`rel_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档关系表';

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- ==========================================
-- 表结构验证查询
-- ==========================================

-- 查看所有文档相关表
SHOW TABLES LIKE 'doc_%';

-- 查看表结构示例
-- DESC doc_documents;
-- DESC doc_chunks;
-- DESC doc_chunks_info; 
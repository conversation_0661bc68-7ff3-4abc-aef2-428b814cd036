"""
表分析工具模块
提供SQL查询的表范围提取和分析功能
"""

import logging
import sys
import os
from typing import Dict, Set, Any, Optional

# 添加src目录到路径以便导入utils模块
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "../../")
sys.path.insert(0, src_dir)

from utils.db.schema.get_colkv import extract_columns_with_real_table

logger = logging.getLogger(__name__)


def extract_table_scope_with_colkv(sql: str) -> str:
    """
    使用get_colkv.py提取表范围信息
    
    Args:
        sql: SQL查询语句
        
    Returns:
        表范围描述字符串
    """
    if not sql:
        return "未提供SQL查询"
    
    try:
        # 使用get_colkv.py提取列与表的映射
        column_table_mapping = extract_columns_with_real_table(sql)
        
        if not column_table_mapping:
            return "无法从SQL中提取表范围信息"
        
        # 提取涉及的表名
        involved_tables = set(column_table_mapping.values())
        involved_tables.discard("")  # 移除空字符串
        
        if involved_tables:
            # 构建详细的表范围信息
            table_info = []
            for table in sorted(involved_tables):
                # 找到该表涉及的列
                table_columns = [col for col, tbl in column_table_mapping.items() if tbl == table]
                if table_columns:
                    table_info.append(f"{table}({', '.join(sorted(table_columns))})")
                else:
                    table_info.append(table)
            
            return f"涉及表及字段: {'; '.join(table_info)}"
        else:
            return "无法识别具体表名"
            
    except Exception as e:
        logger.warning(f"使用get_colkv.py提取表范围失败: {e}")
        # 降级到简单的正则表达式提取
        return _extract_table_scope_fallback(sql)


def _extract_table_scope_fallback(sql: str) -> str:
    """
    降级的表范围提取方法
    
    Args:
        sql: SQL查询语句
        
    Returns:
        表范围描述字符串
    """
    import re
    
    try:
        # 使用正则表达式提取表名
        table_pattern = r'(?:FROM|JOIN)\s+([a-zA-Z_][a-zA-Z0-9_]*)'
        tables = re.findall(table_pattern, sql, re.IGNORECASE)
        
        # 去重并排序
        unique_tables = sorted(set(tables))
        
        if unique_tables:
            return f"涉及表: {', '.join(unique_tables)}"
        else:
            return "无法识别表名"
            
    except Exception as e:
        logger.warning(f"降级表范围提取也失败: {e}")
        return "表范围提取失败"


# 预留的分析函数接口
def analyze_function_1(sql: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    预留分析函数1 - SQL复杂度分析
    
    Args:
        sql: SQL查询语句
        context: 可选的上下文信息
        
    Returns:
        分析结果字典
        
    TODO: 实现SQL复杂度分析功能
    - 分析JOIN数量和类型
    - 分析子查询层级
    - 分析聚合函数使用
    - 评估查询复杂度等级
    """
    logger.info("analyze_function_1 - SQL复杂度分析功能待实现")
    return {
        "complexity_level": "未分析",
        "join_count": 0,
        "subquery_count": 0,
        "aggregate_functions": [],
        "analysis_status": "pending_implementation"
    }


def analyze_function_2(sql: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    预留分析函数2 - 业务逻辑模式识别
    
    Args:
        sql: SQL查询语句
        context: 可选的上下文信息
        
    Returns:
        分析结果字典
        
    TODO: 实现业务逻辑模式识别功能
    - 识别常见业务模式（统计、排名、趋势分析等）
    - 分析数据处理流程
    - 识别业务规则和约束
    - 提取业务语义信息
    """
    logger.info("analyze_function_2 - 业务逻辑模式识别功能待实现")
    return {
        "business_pattern": "未识别",
        "data_flow": [],
        "business_rules": [],
        "semantic_info": {},
        "analysis_status": "pending_implementation"
    }


def analyze_function_3(sql: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    预留分析函数3 - 性能影响评估
    
    Args:
        sql: SQL查询语句
        context: 可选的上下文信息
        
    Returns:
        分析结果字典
        
    TODO: 实现性能影响评估功能
    - 分析潜在性能瓶颈
    - 评估索引使用情况
    - 识别优化机会
    - 提供性能建议
    """
    logger.info("analyze_function_3 - 性能影响评估功能待实现")
    return {
        "performance_risk": "未评估",
        "bottlenecks": [],
        "index_usage": {},
        "optimization_suggestions": [],
        "analysis_status": "pending_implementation"
    }


def analyze_function_4(sql: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    预留分析函数4 - 数据质量检查
    
    Args:
        sql: SQL查询语句
        context: 可选的上下文信息
        
    Returns:
        分析结果字典
        
    TODO: 实现数据质量检查功能
    - 检查数据完整性约束
    - 识别潜在的数据质量问题
    - 分析数据一致性
    - 提供数据质量建议
    """
    logger.info("analyze_function_4 - 数据质量检查功能待实现")
    return {
        "data_quality_score": "未评估",
        "integrity_issues": [],
        "consistency_checks": {},
        "quality_suggestions": [],
        "analysis_status": "pending_implementation"
    }


def run_all_analysis(sql: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    运行所有分析函数的便捷方法
    
    Args:
        sql: SQL查询语句
        context: 可选的上下文信息
        
    Returns:
        包含所有分析结果的字典
    """
    results = {
        "table_scope": extract_table_scope_with_colkv(sql),
        "complexity_analysis": analyze_function_1(sql, context),
        "business_pattern_analysis": analyze_function_2(sql, context),
        "performance_analysis": analyze_function_3(sql, context),
        "data_quality_analysis": analyze_function_4(sql, context)
    }
    
    logger.info(f"完成SQL分析: {len(results)} 个分析维度")
    return results


# 工具函数：将分析结果存储到context中
def store_analysis_results_to_context(results: Dict[str, Any], context: Any) -> None:
    """
    将分析结果存储到Pipeline上下文中
    
    Args:
        results: 分析结果字典
        context: Pipeline上下文对象
    """
    if hasattr(context, 'set'):
        # 存储各个分析结果
        for key, value in results.items():
            context.set(f"sql_analysis_{key}", value)
        
        # 存储完整分析结果
        context.set("sql_analysis_complete", results)
        
        logger.info(f"分析结果已存储到context: {list(results.keys())}")
    else:
        logger.warning("context对象不支持set方法，无法存储分析结果")


# 示例使用方法
if __name__ == "__main__":
    import sys
    import os

    # 添加项目根目录到路径
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../"))

    # 测试表范围提取功能
    test_sql = """
    SELECT l.loan_amount, c.customer_name, i.industry_name
    FROM loan_info l
    JOIN customer_info c ON l.customer_id = c.customer_id
    JOIN industry_info i ON c.industry_id = i.industry_id
    WHERE l.IS_CBIRC_LOAN = 'Y' AND l.loan_date >= '2024-01-01'
    """

    print("=== 表范围提取测试 ===")
    table_scope = extract_table_scope_with_colkv(test_sql)
    print(f"表范围: {table_scope}")

    print("\n=== 完整分析测试 ===")
    all_results = run_all_analysis(test_sql)
    for key, value in all_results.items():
        print(f"{key}: {value}")

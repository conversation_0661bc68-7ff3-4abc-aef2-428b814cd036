# DD-B模块真实环境测试指南

## 🚀 快速开始

### 1. 最简单的测试（推荐）

修改 `quick_test.py` 中的参数，然后运行：

```python
# 修改这两个参数
REPORT_CODE = "g0107_beta_v1.0"  # 您的报表代码
DEPT_ID = "DEPT_FINANCE"         # 您的部门ID

# 运行测试
python src/modules/dd_submission/dd_b/quick_test.py
```

### 2. 完整测试

修改 `test_real_environment.py` 中的参数：

```python
# 在main()函数中修改这些参数
TEST_REPORT_CODE = "g0107_beta_v1.0"  # 您的报表代码
TEST_DEPT_ID = "DEPT_FINANCE"         # 您的部门ID

# 运行测试
python src/modules/dd_submission/dd_b/test_real_environment.py
```

## 📋 测试参数说明

### 必需参数
- `report_code`: 报表代码，如 "g0107_beta_v1.0"
- `dept_id`: 部门ID，如 "DEPT_FINANCE"

### 可选参数
- `enable_auto_fill`: 是否启用自动填充（默认True）
- `return_original_data`: 是否返回原始数据（默认True）
- `batch_size`: 批量处理大小（默认15）
- `use_message_queue`: 是否使用消息队列（默认False）

## 🔧 测试功能

### 基础功能测试
- ✅ 数据库连接测试
- ✅ 记录查询和处理
- ✅ 字段自动填充
- ✅ 默认值应用逻辑
- ✅ 高/低置信度策略
- ✅ Pipeline集成
- ✅ 部门-表关系查询

### 批量处理测试
- ✅ 15条记录/批次处理
- ✅ 4层并发控制
- ✅ 内存优化处理
- ✅ 错误处理和降级

### 性能测试
- ✅ 处理耗时统计
- ✅ 并发性能测试
- ✅ 资源使用监控

## 📊 输出结果说明

### 基础结果
```
状态: ProcessingStatusEnum.SUCCESS
找到: 10 条记录
处理: 10 条记录
填充: 25 个字段
耗时: 150.25ms
```

### 详细统计
- `total_records_found`: 查询到的记录总数
- `records_processed`: 实际处理的记录数
- `records_with_complete_main_fields`: 主要字段完整的记录数
- `records_requiring_fill`: 需要填充的记录数
- `total_fields_filled`: 填充的字段总数
- `processing_time_ms`: 处理耗时（毫秒）

### 填充详情
显示每个字段的填充情况：
```
1. BDR05: 'None' -> '不适用' (低置信度默认值)
2. brd06: '' -> '不适用' (低置信度默认值)
3. SDR02: 'None' -> '1' (低置信度默认值)
```

## 🛠️ 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   ❌ MySQL客户端获取失败
   ```
   - 检查数据库服务是否启动
   - 检查连接配置是否正确

2. **无记录找到**
   ```
   找到: 0 条记录
   ```
   - 检查report_code和dept_id是否存在
   - 检查biz_dd_post表中是否有对应数据

3. **字段名不匹配**
   ```
   DDBRecord.__init__() got an unexpected keyword argument
   ```
   - 检查数据库字段名大小写
   - 确认模型定义与实际表结构一致

### 调试模式

启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 性能优化建议

### 数据库优化
- 确保biz_dd_post表有适当的索引
- 优化查询条件的字段索引
- 考虑分区表设计

### 并发优化
- 根据服务器性能调整max_llm_concurrent参数
- 监控数据库连接池使用情况
- 适当调整batch_size大小

### 内存优化
- 大量数据时启用use_message_queue
- 适当调整批量处理大小
- 监控内存使用情况

## 🔍 监控指标

### 关键指标
- 处理成功率
- 平均处理耗时
- 字段填充率
- 并发处理能力

### 性能基准
- 单条记录处理: < 50ms
- 批量处理(15条): < 500ms
- 字段填充率: > 80%
- 并发处理: 15个LLM并发

## 📞 技术支持

如果遇到问题，请提供：
1. 错误日志
2. 测试参数
3. 数据库表结构
4. 环境配置信息

# DD批量部门职责分配系统 - 数据库准备文件说明

## 📁 文件组织结构

```
src/modules/db/preparation/
├── README.md                                    # 本文件 - 总体说明
├── 
├── 📋 架构和规范文档
├── dd_submission_data_field_specifications.md  # DD字段规范文档 (62个字段详细定义)
├── dd_field_specifications_implementation_summary.md  # 实施总结文档
├── dd_test_data_comprehensive_guide.md         # 综合测试数据指南
├── 
├── 🗄️ 数据库架构文件
├── create_rdb_dd.sql                          # 数据库架构创建脚本 (已更新)
├── 
├── 🧪 测试数据文件
├── execute_dd_test_setup.sql                  # 一键部署测试环境脚本
├── insert_dd_test_data.sql                    # 基础测试数据 (部门、关联、分发前数据)
├── insert_dd_submission_data_bulk.sql         # 大规模填报数据 (80条记录)
├── 
├── ✅ 验证和检查文件
└── validate_dd_schema_changes.sql             # 全面验证脚本
```

## 🎯 使用指南

### 快速开始 (推荐)

```bash
# 1. 一键部署基础环境
mysql -u username -p database_name < execute_dd_test_setup.sql

# 2. 插入分发前测试数据
mysql -u username -p database_name < insert_dd_test_data.sql

# 3. 插入大规模填报数据
mysql -u username -p database_name < insert_dd_submission_data_bulk.sql

# 4. 执行全面验证
mysql -u username -p database_name < validate_dd_schema_changes.sql
```

### 分步执行 (详细控制)

#### 第一步：架构准备
```bash
# 如果是全新环境，先创建数据库架构
mysql -u username -p database_name < create_rdb_dd.sql
```

#### 第二步：基础数据
```bash
# 插入部门、关联关系、报表数据
mysql -u username -p database_name < execute_dd_test_setup.sql
```

#### 第三步：测试数据
```bash
# 插入15条分发前数据 (故意少于submission_data)
mysql -u username -p database_name < insert_dd_test_data.sql

# 插入80条完整填报数据 (大规模测试)
mysql -u username -p database_name < insert_dd_submission_data_bulk.sql
```

#### 第四步：验证检查
```bash
# 全面验证数据完整性和算法准备
mysql -u username -p database_name < validate_dd_schema_changes.sql
```

## 📊 测试数据规模

| 数据类型 | 记录数 | 文件位置 | 用途 |
|----------|--------|----------|------|
| 测试部门 | 15条 | execute_dd_test_setup.sql | 覆盖各业务领域的部门 |
| 部门关联关系 | 30条 | execute_dd_test_setup.sql | 测试多部门候选场景 |
| 报表数据 | 3条 | execute_dd_test_setup.sql | 主要测试G0107_beta_v1.0 |
| 分发前数据 | 15条 | insert_dd_test_data.sql | 测试分发逻辑 |
| 填报数据 | 80条 | insert_dd_submission_data_bulk.sql | 大规模性能测试 |

## 🔍 测试场景覆盖

### 三层搜索算法测试
- **精确匹配** (第一层): 基于table_id的直接匹配
- **混合搜索** (第二层): 基于业务关键词的语义匹配  
- **TF-IDF兜底** (第三层): 基于文本相似度的兜底匹配

### 四层业务筛选测试
- **集合类型筛选**: SET_A, SET_B, SET_C
- **报表类型筛选**: detail, index
- **提交类型筛选**: SUBMISSION, RANGE
- **数据层筛选**: ADS, BDM, IDM, ODS, ADM

### 部门分配场景测试
- **单一匹配**: 唯一部门精确匹配
- **多候选匹配**: 多个部门候选排序
- **边界情况**: 无明确匹配的处理

## 🎯 预期测试结果

### 部门分配预期

| 表ID | 表名称 | 预期候选部门 | 测试目的 |
|------|--------|--------------|----------|
| 16 | 客户信息表 | TEST_RETAIL, TEST_CORPORATE, TEST_CUSTOMER_SVC | 多部门候选测试 |
| 2 | 交易记录表 | TEST_RRMS, TEST_TREASURY, TEST_RISK_CTRL | 风险财务部门测试 |
| 31 | 用户档案表 | TEST_RETAIL, TEST_OPERATIONS, TEST_CUSTOMER_SVC | 运营服务部门测试 |
| 33 | API测试表 | TEST_IT | 单一部门精确匹配 |
| 4 | 风险汇总表 | TEST_RRMS, TEST_RISK_CTRL, TEST_COMPLIANCE | 风险合规部门测试 |

### 性能基准

| 测试场景 | 数据量 | 预期响应时间 | 准确率目标 |
|----------|--------|--------------|------------|
| 精确匹配 | 80条 | <100ms | >95% |
| 混合搜索 | 80条 | <500ms | >85% |
| 四层筛选 | 80条 | <200ms | 100% |

## 🔧 故障排除

### 常见问题

1. **外键约束错误**
   ```sql
   -- 检查表是否存在
   SHOW TABLES LIKE '%dd_%';
   
   -- 检查外键约束
   SELECT * FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
   WHERE TABLE_SCHEMA = DATABASE() AND REFERENCED_TABLE_NAME IS NOT NULL;
   ```

2. **字符编码问题**
   ```sql
   -- 检查表字符集
   SELECT TABLE_NAME, TABLE_COLLATION 
   FROM INFORMATION_SCHEMA.TABLES 
   WHERE TABLE_SCHEMA = DATABASE();
   ```

3. **数据插入失败**
   ```sql
   -- 检查表结构
   DESCRIBE dd_submission_data;
   
   -- 检查约束
   SHOW CREATE TABLE dd_submission_data;
   ```

### 验证命令

```sql
-- 快速验证数据完整性
SELECT 
    'dd_departments' as table_name, COUNT(*) as count 
FROM dd_departments WHERE dept_id LIKE 'TEST_%'
UNION ALL
SELECT 'dd_departments_relation', COUNT(*) 
FROM dd_departments_relation WHERE dept_id LIKE 'TEST_%'
UNION ALL
SELECT 'dd_report_data', COUNT(*) 
FROM dd_report_data WHERE knowledge_id = '58b452bc-24ca-46b2-89fb-cce1d68068c6'
UNION ALL
SELECT 'biz_dd_pre_distribution', COUNT(*) 
FROM biz_dd_pre_distribution WHERE submission_id LIKE 'TEST_%'
UNION ALL
SELECT 'dd_submission_data', COUNT(*) 
FROM dd_submission_data WHERE submission_id LIKE 'TEST_%';
```

## 📚 相关文档

### 核心文档
- **字段规范**: `dd_submission_data_field_specifications.md` - 62个DD字段的完整定义
- **实施总结**: `dd_field_specifications_implementation_summary.md` - 实施过程和结果
- **测试指南**: `dd_test_data_comprehensive_guide.md` - 详细的测试数据说明

### 技术文档
- **架构文档**: 参考 `create_rdb_dd.sql` 中的表结构定义
- **验证文档**: 参考 `validate_dd_schema_changes.sql` 中的验证逻辑

## 🚀 下一步

### 算法测试
1. 使用测试数据验证三层搜索算法
2. 测试四层业务筛选逻辑
3. 验证部门分配准确性

### 性能优化
1. 基于测试结果优化查询性能
2. 调整索引策略
3. 优化算法参数

### 扩展测试
1. 增加边界情况测试
2. 添加异常输入测试
3. 进行压力测试

---

**维护团队**: DD系统开发团队  
**版本**: v2.1.0  
**最后更新**: 2024-01-15

**联系方式**: 
- 技术支持: <EMAIL>
- 文档反馈: <EMAIL>

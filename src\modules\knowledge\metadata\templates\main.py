"""
文档模板管理器

提供文档模板的完整管理功能，参照DD系统的设计模式。
集成文档上传、解析、元数据提取和入库功能。
"""

import sys
import os
import uuid
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, BinaryIO
import logging

# 标准化日志导入
logger = logging.getLogger(__name__)

# 添加项目根目录到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from .parser import TemplateParser
from .validator import TemplateValidator
from .extractor import MetadataExtractor


class MetadataTemplateManager:
    """
    文档模板管理器
    
    提供文档模板的完整管理功能，包括：
    - 模板上传和存储
    - 模板解析和验证
    - 元数据提取和入库
    - 模板版本管理
    """
    
    def __init__(self, rdb_client, vdb_client, storage_path: str = "templates"):
        """
        初始化文档模板管理器
        
        Args:
            rdb_client: 关系数据库客户端
            vdb_client: 向量数据库客户端
            storage_path: 模板存储路径
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        
        # 初始化子模块
        self.parser = TemplateParser()
        self.validator = TemplateValidator()
        self.extractor = MetadataExtractor(rdb_client, vdb_client)
        
        logger.info("文档模板管理器初始化完成")
    
    async def upload_template(
        self, 
        knowledge_id: str,
        file_content: BinaryIO,
        filename: str,
        template_type: str = "excel",
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        上传文档模板
        
        Args:
            knowledge_id: 知识库ID
            file_content: 文件内容
            filename: 文件名
            template_type: 模板类型（excel/csv/json）
            description: 模板描述
            
        Returns:
            Dict[str, Any]: 上传结果
        """
        try:
            # 生成模板ID
            template_id = str(uuid.uuid4())
            
            # 保存文件
            file_path = await self._save_template_file(template_id, file_content, filename)
            
            # 解析模板
            parsed_data = await self.parser.parse_template(file_path, template_type)
            
            # 验证模板
            validation_result = await self.validator.validate_template(parsed_data, template_type)
            
            if not validation_result["valid"]:
                raise ValueError(f"模板验证失败: {validation_result['errors']}")
            
            # 保存模板记录
            template_record = {
                "template_id": template_id,
                "knowledge_id": knowledge_id,
                "filename": filename,
                "template_type": template_type,
                "description": description,
                "file_path": str(file_path),
                "status": "uploaded",
                "parsed_data": parsed_data
            }
            
            await self._save_template_record(template_record)
            
            logger.info(f"模板上传成功: template_id={template_id}, filename={filename}")
            
            return {
                "template_id": template_id,
                "filename": filename,
                "template_type": template_type,
                "status": "uploaded",
                "validation_result": validation_result,
                "parsed_data": parsed_data
            }
            
        except Exception as e:
            logger.error(f"模板上传失败: filename={filename}, error={e}")
            raise
    
    async def process_template(
        self,
        knowledge_id: str,
        template_id: str,
        auto_extract: bool = True
    ) -> Dict[str, Any]:
        """
        处理文档模板，提取元数据并入库
        
        Args:
            knowledge_id: 知识库ID
            template_id: 模板ID
            auto_extract: 是否自动提取元数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 获取模板记录
            template_record = await self._get_template_record(template_id)
            if not template_record:
                raise ValueError(f"模板不存在: {template_id}")
            
            # 提取元数据
            if auto_extract:
                extraction_result = await self.extractor.extract_metadata(
                    knowledge_id, template_record["parsed_data"], template_record["template_type"]
                )
            else:
                extraction_result = {"extracted": False, "message": "跳过自动提取"}
            
            # 更新模板状态
            await self._update_template_status(template_id, "processed")
            
            logger.info(f"模板处理完成: template_id={template_id}")
            
            return {
                "template_id": template_id,
                "status": "processed",
                "extraction_result": extraction_result
            }
            
        except Exception as e:
            logger.error(f"模板处理失败: template_id={template_id}, error={e}")
            await self._update_template_status(template_id, "failed")
            raise
    
    async def download_template(self, template_id: str) -> Tuple[str, bytes]:
        """
        下载文档模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            Tuple[str, bytes]: (文件名, 文件内容)
        """
        try:
            # 获取模板记录
            template_record = await self._get_template_record(template_id)
            if not template_record:
                raise ValueError(f"模板不存在: {template_id}")
            
            # 读取文件内容
            file_path = Path(template_record["file_path"])
            if not file_path.exists():
                raise FileNotFoundError(f"模板文件不存在: {file_path}")
            
            with open(file_path, "rb") as f:
                file_content = f.read()
            
            logger.info(f"模板下载成功: template_id={template_id}")
            
            return template_record["filename"], file_content
            
        except Exception as e:
            logger.error(f"模板下载失败: template_id={template_id}, error={e}")
            raise
    
    async def list_templates(
        self,
        knowledge_id: str,
        template_type: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        查询模板列表
        
        Args:
            knowledge_id: 知识库ID
            template_type: 模板类型过滤
            status: 状态过滤
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 模板列表
        """
        try:
            # 构建查询条件
            filters = {"knowledge_id": knowledge_id}
            if template_type:
                filters["template_type"] = template_type
            if status:
                filters["status"] = status
            
            # 查询模板列表
            templates = await self._query_template_records(filters, limit, offset)
            
            logger.info(f"查询模板列表成功: knowledge_id={knowledge_id}, count={len(templates)}")
            return templates
            
        except Exception as e:
            logger.error(f"查询模板列表失败: knowledge_id={knowledge_id}, error={e}")
            raise
    
    async def delete_template(self, template_id: str) -> bool:
        """
        删除文档模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 获取模板记录
            template_record = await self._get_template_record(template_id)
            if not template_record:
                logger.warning(f"模板不存在: {template_id}")
                return False
            
            # 删除文件
            file_path = Path(template_record["file_path"])
            if file_path.exists():
                file_path.unlink()
            
            # 删除记录
            await self._delete_template_record(template_id)
            
            logger.info(f"模板删除成功: template_id={template_id}")
            return True
            
        except Exception as e:
            logger.error(f"模板删除失败: template_id={template_id}, error={e}")
            raise
    
    # ==================== 私有方法 ====================
    
    async def _save_template_file(
        self, 
        template_id: str, 
        file_content: BinaryIO, 
        filename: str
    ) -> Path:
        """保存模板文件"""
        try:
            # 创建文件路径
            file_extension = Path(filename).suffix
            file_path = self.storage_path / f"{template_id}{file_extension}"
            
            # 保存文件
            with open(file_path, "wb") as f:
                f.write(file_content.read())
            
            return file_path
        except Exception as e:
            logger.error(f"保存模板文件失败: template_id={template_id}, error={e}")
            raise
    
    async def _save_template_record(self, template_record: Dict[str, Any]) -> bool:
        """保存模板记录"""
        try:
            # 使用新的数据库客户端API
            # 假设有一个模板表 md_templates
            result = await self.rdb_client.abatch_insert(
                table="md_templates",
                data=[template_record]
            )

            if not result.success:
                raise Exception(f"保存模板记录失败: {result.error_message}")

            logger.info(f"保存模板记录成功: template_id={template_record['template_id']}")
            return True
        except Exception as e:
            logger.error(f"保存模板记录失败: {e}")
            raise
    
    async def _get_template_record(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取模板记录"""
        try:
            # 使用新的数据库客户端API
            query_request = {
                "table": "md_templates",
                "filters": {"template_id": template_id},
                "limit": 1
            }

            result = await self.rdb_client.aquery(query_request)
            results = result.data if result else []

            if results:
                logger.info(f"获取模板记录成功: template_id={template_id}")
                return results[0]
            else:
                logger.warning(f"模板记录不存在: template_id={template_id}")
                return None

        except Exception as e:
            logger.error(f"获取模板记录失败: template_id={template_id}, error={e}")
            raise
    
    async def _update_template_status(self, template_id: str, status: str) -> bool:
        """更新模板状态"""
        try:
            # 使用新的数据库客户端API
            updates = [{
                "data": {"status": status, "update_time": datetime.now()},
                "filters": {"template_id": template_id}
            }]

            result = await self.rdb_client.abatch_update(
                table="md_templates",
                updates=updates
            )

            if result.success and result.affected_rows > 0:
                logger.info(f"更新模板状态成功: template_id={template_id}, status={status}")
                return True
            else:
                logger.warning(f"更新模板状态失败，未找到记录: template_id={template_id}")
                return False

        except Exception as e:
            logger.error(f"更新模板状态失败: template_id={template_id}, error={e}")
            raise
    
    async def _query_template_records(
        self,
        filters: Dict[str, Any],
        limit: int,
        offset: int
    ) -> List[Dict[str, Any]]:
        """查询模板记录"""
        try:
            # 使用新的数据库客户端API
            query_request = {
                "table": "md_templates",
                "filters": filters,
                "sorts": [{"field": "create_time", "order": "desc"}],
                "limit": limit,
                "offset": offset
            }

            result = await self.rdb_client.aquery(query_request)
            results = result.data if result else []

            logger.info(f"查询模板记录成功: filters={filters}, count={len(results)}")
            return results

        except Exception as e:
            logger.error(f"查询模板记录失败: {e}")
            raise
    
    async def _delete_template_record(self, template_id: str) -> bool:
        """删除模板记录"""
        try:
            # 使用新的数据库客户端API
            where_clause = "template_id = :template_id"
            sql = f"DELETE FROM md_templates WHERE {where_clause}"

            result = await self.rdb_client.aexecute(sql, {"template_id": template_id})

            if result.affected_rows > 0:
                logger.info(f"删除模板记录成功: template_id={template_id}")
                return True
            else:
                logger.warning(f"删除模板记录失败，未找到记录: template_id={template_id}")
                return False

        except Exception as e:
            logger.error(f"删除模板记录失败: template_id={template_id}, error={e}")
            raise

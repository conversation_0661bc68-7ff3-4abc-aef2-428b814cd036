"""
MySQL数据库连接配置

基于Universal架构设计的MySQL专用配置类
继承RDB抽象层的ConnectionConfig，添加MySQL特有的功能
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs, quote_plus

from ....base.rdb import ConnectionConfig
from .exceptions import MySQLConfigurationError


@dataclass
class MySQLConnectionConfig(ConnectionConfig):
    """MySQL数据库连接配置
    
    继承ConnectionConfig，添加MySQL特有的功能
    """
    
    # MySQL特有的配置
    database_url: Optional[str] = None  # 支持URL和组件两种初始化方式
    
    # MySQL连接选项
    autocommit: bool = False
    use_unicode: bool = True
    charset: str = "utf8mb4"
    collation: Optional[str] = None
    sql_mode: Optional[str] = None
    time_zone: Optional[str] = None
    
    # 性能和缓存设置
    enable_cache: bool = True
    cache_size: int = 1000
    enable_query_optimization: bool = True
    
    # 异步设置
    async_fallback: bool = True
    
    # MySQL特定选项
    mysql_options: Dict[str, Any] = field(default_factory=dict)
    
    # 连接重试设置
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # 连接池设置（覆盖基类默认值）
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: float = 30.0
    pool_recycle: int = 3600
    pool_pre_ping: bool = True

    def __post_init__(self):
        """初始化后处理"""
        # 如果提供了database_url，解析它来填充基础字段
        if self.database_url and not self.host:
            self._parse_database_url()
        
        # 设置MySQL默认端口
        if not self.port:
            self.port = 3306
            
        # 验证配置
        self._validate_config()
    
    def _parse_database_url(self):
        """解析database_url并填充基础连接字段"""
        if not self.database_url:
            return
            
        try:
            parsed = urlparse(self.database_url)
            
            # 验证scheme
            if parsed.scheme not in ['mysql', 'mysql+pymysql', 'mysql+aiomysql']:
                raise MySQLConfigurationError(f"Unsupported scheme: {parsed.scheme}")
            
            # 填充连接信息（如果尚未设置）
            if not self.host and parsed.hostname:
                self.host = parsed.hostname
            if not self.port and parsed.port:
                self.port = parsed.port
            if not self.database and parsed.path:
                self.database = parsed.path.lstrip('/')
            if not self.username and parsed.username:
                self.username = parsed.username
            if not self.password and parsed.password:
                self.password = parsed.password
            
            # 解析查询参数
            if parsed.query:
                query_params = {k: v[0] if len(v) == 1 else v 
                              for k, v in parse_qs(parsed.query).items()}
                
                # 将查询参数添加到mysql_options
                self.mysql_options.update(query_params)
                
        except Exception as e:
            raise MySQLConfigurationError(f"Invalid database URL '{self.database_url}': {e}")
    
    def _validate_config(self):
        """验证配置参数"""
        # 基础验证
        if not self.database_url and not self.host:
            raise MySQLConfigurationError("Either database_url or host must be provided")
        
        if not self.database:
            raise MySQLConfigurationError("Database name is required")
            
        if not self.username:
            raise MySQLConfigurationError("Username is required")
        
        if self.pool_size < 1:
            raise MySQLConfigurationError("pool_size must be at least 1")
        
        if self.max_overflow < 0:
            raise MySQLConfigurationError("max_overflow cannot be negative")
        
        if self.pool_timeout <= 0:
            raise MySQLConfigurationError("pool_timeout must be positive")
            
        if self.port <= 0 or self.port > 65535:
            raise MySQLConfigurationError("Port must be between 1 and 65535")
    
    def build_database_url(self) -> str:
        """构建MySQL数据库URL"""
        if self.database_url:
            return self.database_url

        # 从组件构建URL
        scheme = "mysql+pymysql"  # 默认使用pymysql驱动
        
        # URL编码用户名和密码
        encoded_username = quote_plus(self.username)
        encoded_password = quote_plus(self.password) if self.password else ""
        
        # 构建基础URL
        if encoded_password:
            url = f"{scheme}://{encoded_username}:{encoded_password}@{self.host}:{self.port}/{self.database}"
        else:
            url = f"{scheme}://{encoded_username}@{self.host}:{self.port}/{self.database}"
        
        # 添加查询参数
        query_params = []
        if self.charset:
            query_params.append(f"charset={self.charset}")
        if self.collation:
            query_params.append(f"collation={self.collation}")
        if self.mysql_options:
            for key, value in self.mysql_options.items():
                query_params.append(f"{key}={value}")
        
        if query_params:
            url += "?" + "&".join(query_params)
        
        return url
    
    def build_async_database_url(self) -> str:
        """构建异步MySQL数据库URL"""
        url = self.build_database_url()
        # 替换驱动为aiomysql
        return url.replace("mysql+pymysql", "mysql+aiomysql")
    
    def get_sync_connection_params(self) -> Dict[str, Any]:
        """获取同步连接参数（pymysql格式）"""
        params = {
            'host': self.host,
            'port': self.port,
            'user': self.username,
            'password': self.password,
            'database': self.database,
            'charset': self.charset,
            'use_unicode': self.use_unicode,
            'autocommit': self.autocommit,
            'connect_timeout': self.connect_timeout,
            'read_timeout': self.read_timeout,
            'write_timeout': self.write_timeout,
        }
        
        # 添加SSL配置
        if self.ssl_enabled:
            ssl_config = {}
            if self.ssl_cert:
                ssl_config['cert'] = self.ssl_cert
            if self.ssl_key:
                ssl_config['key'] = self.ssl_key
            if self.ssl_ca:
                ssl_config['ca'] = self.ssl_ca
            if ssl_config:
                params['ssl'] = ssl_config
        
        # 添加MySQL特定选项
        params.update(self.mysql_options)
        
        # 移除None值
        return {k: v for k, v in params.items() if v is not None}
    
    def get_async_connection_params(self) -> Dict[str, Any]:
        """获取异步连接参数（aiomysql格式）"""
        # 从基础参数开始，但只包含aiomysql支持的参数
        params = {
            'host': self.host,
            'port': self.port,
            'user': self.username,
            'password': self.password,
            'db': self.database,
            'charset': self.charset,
            'use_unicode': self.use_unicode,
            'autocommit': False,  # 明确设置为False，手动控制事务
        }

        # 添加SSL配置
        if self.ssl_enabled:
            params['ssl'] = True

        # 移除None值
        return {k: v for k, v in params.items() if v is not None}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        
        # 添加ConnectionConfig的字段
        for field_name in ['host', 'port', 'database', 'username', 'password',
                          'charset', 'timezone', 'connect_timeout', 'read_timeout',
                          'write_timeout', 'ssl_enabled', 'ssl_cert', 'ssl_key',
                          'ssl_ca', 'pool_size', 'max_overflow', 'pool_timeout',
                          'pool_recycle', 'echo', 'autocommit', 'isolation_level']:
            if hasattr(self, field_name):
                result[field_name] = getattr(self, field_name)
        
        # 添加MySQL特有的字段
        result.update({
            'database_url': self.database_url,
            'use_unicode': self.use_unicode,
            'collation': self.collation,
            'sql_mode': self.sql_mode,
            'time_zone': self.time_zone,
            'enable_cache': self.enable_cache,
            'cache_size': self.cache_size,
            'enable_query_optimization': self.enable_query_optimization,
            'async_fallback': self.async_fallback,
            'mysql_options': self.mysql_options,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay,
            'pool_pre_ping': self.pool_pre_ping,
        })
        
        return result
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'MySQLConnectionConfig':
        """从字典创建配置 - 支持Hydra风格的简单配置，智能过滤冗余参数"""

        # 创建配置副本，避免修改原字典
        config = config_dict.copy()

        # 定义所有支持的参数
        supported_params = {
            # ConnectionConfig 基础参数
            'host', 'port', 'database', 'username', 'password', 'charset', 'timezone',
            'connect_timeout', 'read_timeout', 'write_timeout', 'ssl_enabled', 'ssl_cert',
            'ssl_key', 'ssl_ca', 'pool_size', 'max_overflow', 'pool_timeout', 'pool_recycle',
            'echo', 'autocommit', 'isolation_level',

            # MySQLConnectionConfig 特有参数
            'database_url', 'use_unicode', 'collation', 'sql_mode', 'time_zone',
            'enable_cache', 'cache_size', 'enable_query_optimization', 'async_fallback',
            'mysql_options', 'max_retries', 'retry_delay', 'pool_pre_ping'
        }

        # 过滤配置，只保留支持的参数
        filtered_config = {k: v for k, v in config.items() if k in supported_params}

        # 记录被过滤的参数（用于调试）
        filtered_out = set(config.keys()) - supported_params
        if filtered_out:
            import logging
            logger = logging.getLogger(__name__)
            logger.debug(f"MySQLConnectionConfig 过滤了冗余参数: {filtered_out}")

        # 验证必需参数
        required_params = {'host', 'database', 'username'}
        missing_params = required_params - set(filtered_config.keys())
        if missing_params:
            raise ValueError(f"缺少必需的连接参数: {missing_params}")

        # 如果缺少可选字段，使用默认值
        defaults = {
            'port': 3306,
            'password': '',
            'charset': 'utf8mb4',
            'pool_size': 10,
            'max_overflow': 20,
            'pool_timeout': 30.0,
            'pool_recycle': 3600,
            'echo': False,
            'use_unicode': True,
            'autocommit': False,
            'enable_cache': True,
            'cache_size': 1000,
            'pool_pre_ping': True,
        }

        # 合并默认值（只对缺失的键）
        for key, default_value in defaults.items():
            if key not in filtered_config:
                filtered_config[key] = default_value

        return cls(**filtered_config)

"""
DocumentOperation - 文档相关的数据库操作类

提供文档表和文档别名表的完整CRUD操作，包括：
- 创建文档记录
- 查询文档记录
- 更新文档记录  
- 删除文档记录
- 批量操作
- 文档别名管理
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import uuid4

from service import get_client, get_config
from modules.knowledge.doc.entities.api_models import DocumentCreate
from modules.knowledge.doc.entities.base_models import DocumentStatus, ParseType, DocumentFormat
from base.db.base.rdb.core.models import OperationResponse, QueryResponse
from .db_wrapper import DatabaseOperationWrapper

logger = logging.getLogger(__name__)


class DocumentOperation:
    """文档操作类 - 负责文档表和文档别名表的CRUD操作"""

    def __init__(self, rdb_client=None, rdb_config_path=None):
        """
        初始化文档操作类
        
        Args:
            rdb_client: 关系型数据库客户端，如果为None则自动从配置获取
            rdb_config_path: 数据库配置路径，用于覆盖默认配置
        """
        self._rdb_client = rdb_client
        self._rdb_config_path = rdb_config_path
        self._rdb_client_wrapper = None
        self.table_name = "doc_dev_documents"
        self.alias_table_name = "doc_dev_document_alias"
        self.relationship_table_name = "doc_dev_document_relationship"

    async def _ensure_rdb_client(self):
        """确保数据库客户端已初始化"""
        if self._rdb_client_wrapper is None:
            if self._rdb_client is None:
                # 从配置获取数据库配置路径
                if self._rdb_config_path is None:
                    cfg = await get_config()
                    self._rdb_config_path = cfg.knowledge.doc.rdb

                self._rdb_client = await get_client(self._rdb_config_path)
                logger.debug(f"从配置获取数据库客户端: {self._rdb_config_path}")

            self._rdb_client_wrapper = DatabaseOperationWrapper(self._rdb_client)

        return self._rdb_client_wrapper

    async def create_document(self, document: DocumentCreate) -> str:
        """
        创建文档记录
        
        Args:
            document: 文档创建模型
            
        Returns:
            str: 创建的文档ID
            
        Raises:
            Exception: 创建失败时抛出异常
        """
        try:
            # 生成文档ID
            doc_id = str(uuid4())

            # 准备插入数据
            document_data = {
                "doc_id": doc_id,
                "knowledge_id": document.knowledge_id,
                "doc_name": document.doc_name,
                "doc_type": document.doc_type.value if hasattr(document.doc_type, 'value') else document.doc_type,
                "author": document.author,
                "vector_similarity_weight": document.vector_similarity_weight,
                "similarity_threshold": document.similarity_threshold,
                "chunk_nums": 0,  # 初始化为0
                "percentage": 0.0,  # 初始化为0
                "parse_type": document.parse_type.value if hasattr(document.parse_type, 'value') else document.parse_type,
                "status": document.status.value if hasattr(document.status, 'value') else document.status,
                "parse_end_time": document.parse_end_time,
                "parse_message": document.parse_message,
                "location": document.location,
                "doc_format": document.doc_format.value if hasattr(document.doc_format, 'value') else document.doc_format,
                "doc_ocr_result_path": None,  # 后续可更新
                "metadata": document.metadata,
                "task_id": None,  # 后续可更新
                "created_time": document.created_time or datetime.now(),
                "updated_time": None,
                "is_active": document.is_active
            }

            # 执行插入操作
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.insert(self.table_name, [document_data])

            if success:
                logger.info(f"文档创建成功: {doc_id}")
                return doc_id
            else:
                raise Exception("数据库插入操作失败")

        except Exception as e:
            logger.error(f"创建文档失败: {e}")
            raise Exception(f"创建文档失败: {str(e)}")

    async def get_document_by_id(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID查询文档
        
        Args:
            doc_id: 文档ID
            
        Returns:
            Optional[Dict[str, Any]]: 文档记录，如果不存在则返回None
        """
        try:
            condition: Dict[str, Any] = {"doc_id": doc_id, "is_active": True}
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.table_name,
                condition=condition,
                limit=1
            )

            if result:
                logger.debug(f"查询到文档: {doc_id}")
                return result[0]
            else:
                logger.debug(f"文档不存在: {doc_id}")
                return None

        except Exception as e:
            logger.error(f"查询文档失败: {e}")
            raise Exception(f"查询文档失败: {str(e)}")

    async def get_documents_by_knowledge_id(
        self,
        knowledge_id: str,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        status: Optional[DocumentStatus] = None
    ) -> List[Dict[str, Any]]:
        """
        根据知识库ID查询文档列表
        
        Args:
            knowledge_id: 知识库ID
            limit: 限制返回数量
            offset: 偏移量
            status: 文档状态过滤
            
        Returns:
            List[Dict[str, Any]]: 文档记录列表
        """
        try:
            # 构建查询条件
            condition: Dict[str, Any] = {
                "knowledge_id": knowledge_id,
                "is_active": True
            }

            if status:
                condition["status"] = status.value

            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.table_name,
                condition=condition,
                limit=limit,
                offset=offset,
                order_by=["created_time DESC"]
            )

            logger.debug(f"查询到 {len(result)} 个文档 (knowledge_id: {knowledge_id})")
            return result

        except Exception as e:
            logger.error(f"查询文档列表失败: {e}")
            raise Exception(f"查询文档列表失败: {str(e)}")

    async def update_document(
        self,
        doc_id: str,
        update_data: Dict[str, Any]
    ) -> bool:
        """
        更新文档记录
        
        Args:
            doc_id: 文档ID
            update_data: 要更新的数据字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 添加更新时间
            update_data["updated_time"] = datetime.now()

            # 执行更新操作
            rdb_client = await self._ensure_rdb_client()
            success = await rdb_client.aupdate(
                table=self.table_name,
                filter={"doc_id": doc_id, "is_active": True},
                data=update_data
            )

            if success:
                logger.info(f"文档更新成功: {doc_id}")
            else:
                logger.warning(f"文档更新失败，可能不存在: {doc_id}")

            return success

        except Exception as e:
            logger.error(f"更新文档失败: {e}")
            raise Exception(f"更新文档失败: {str(e)}")

    async def update_document_status(
        self,
        doc_id: str,
        status: DocumentStatus,
        parse_message: Optional[str] = None
    ) -> bool:
        """
        更新文档解析状态
        
        Args:
            doc_id: 文档ID
            status: 新状态
            parse_message: 状态描述信息
            
        Returns:
            bool: 更新是否成功
        """
        update_data = {
            "status": status.value,
            "parse_message": parse_message
        }

        # 如果状态为完成，设置解析结束时间
        if status == DocumentStatus.COMPLETED:
            update_data["parse_end_time"] = datetime.now()

        return await self.update_document(doc_id, update_data)

    async def update_document_progress(
        self,
        doc_id: str,
        chunk_nums: int,
        percentage: float
    ) -> bool:
        """
        更新文档分块进度
        
        Args:
            doc_id: 文档ID
            chunk_nums: 分块总数
            percentage: 完成百分比 (0-100)
            
        Returns:
            bool: 更新是否成功
        """
        update_data = {
            "chunk_nums": chunk_nums,
            "percentage": min(max(percentage, 0.0), 100.0)  # 确保在0-100范围内
        }

        return await self.update_document(doc_id, update_data)

    async def soft_delete_document(self, doc_id: str) -> bool:
        """
        软删除文档（设置is_active=False）
        
        Args:
            doc_id: 文档ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.update(
                table=self.table_name,
                filter={"doc_id": doc_id, "is_active": True},
                data={
                    "is_active": False,
                    "updated_time": datetime.now()
                }
            )

            if success:
                logger.info(f"文档软删除成功: {doc_id}")
            else:
                logger.warning(f"文档软删除失败，可能不存在: {doc_id}")

            return success

        except Exception as e:
            logger.error(f"软删除文档失败: {e}")
            raise Exception(f"软删除文档失败: {str(e)}")

    async def hard_delete_document(self, doc_id: str) -> bool:
        """
        硬删除文档（物理删除）
        
        Args:
            doc_id: 文档ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.delete(
                table=self.table_name,
                filter={"doc_id": doc_id}
            )

            if success:
                logger.info(f"文档硬删除成功: {doc_id}")
            else:
                logger.warning(f"文档硬删除失败，可能不存在: {doc_id}")

            return success

        except Exception as e:
            logger.error(f"硬删除文档失败: {e}")
            raise Exception(f"硬删除文档失败: {str(e)}")

    async def count_documents(
        self,
        knowledge_id: Optional[str] = None,
        status: Optional[DocumentStatus] = None
    ) -> int:
        """
        统计文档数量
        
        Args:
            knowledge_id: 知识库ID，为None时统计所有
            status: 文档状态，为None时统计所有状态
            
        Returns:
            int: 文档数量
        """
        try:
            # 构建查询条件
            condition: Dict[str, Any] = {"is_active": True}

            if knowledge_id:
                condition["knowledge_id"] = knowledge_id

            if status:
                condition["status"] = status.value

            # 使用select查询获取所有记录，然后计算数量
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.table_name,
                columns=["doc_id"],  # 只查询ID列以提高性能
                condition=condition
            )

            count = len(result)
            logger.debug(f"文档数量统计: {count}")
            return count

        except Exception as e:
            logger.error(f"统计文档数量失败: {e}")
            raise Exception(f"统计文档数量失败: {str(e)}")

    async def batch_create_documents(self, documents: List[DocumentCreate]) -> List[str]:
        """
        批量创建文档
        
        Args:
            documents: 文档创建模型列表
            
        Returns:
            List[str]: 创建的文档ID列表
        """
        try:
            doc_ids = []
            document_data_list = []

            for document in documents:
                doc_id = str(uuid4())
                doc_ids.append(doc_id)

                document_data = {
                    "doc_id": doc_id,
                    "knowledge_id": document.knowledge_id,
                    "doc_name": document.doc_name,
                    "doc_type": document.doc_type.value if document.doc_type else None,
                    "author": document.author,
                    "vector_similarity_weight": document.vector_similarity_weight,
                    "similarity_threshold": document.similarity_threshold,
                    "chunk_nums": 0,
                    "percentage": 0.0,
                    "parse_type": document.parse_type.value,
                    "status": document.status.value,
                    "parse_end_time": document.parse_end_time,
                    "parse_message": document.parse_message,
                    "location": document.location,
                    "doc_format": document.doc_format.value,
                    "doc_ocr_result_path": None,
                    "metadata": document.metadata,
                    "task_id": None,
                    "created_time": document.created_time or datetime.now(),
                    "updated_time": None,
                    "is_active": document.is_active
                }
                document_data_list.append(document_data)

            # 批量插入
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.insert(self.table_name, document_data_list)

            if success:
                logger.info(f"批量创建文档成功: {len(doc_ids)} 个文档")
                return doc_ids
            else:
                raise Exception("批量数据库插入操作失败")

        except Exception as e:
            logger.error(f"批量创建文档失败: {e}")
            raise Exception(f"批量创建文档失败: {str(e)}")

    async def search_documents(
        self,
        knowledge_id: Optional[str] = None,
        doc_name_pattern: Optional[str] = None,
        author: Optional[str] = None,
        doc_type: Optional[str] = None,
        status: Optional[DocumentStatus] = None,
        limit: Optional[int] = 50,
        offset: Optional[int] = 0,
        doc_name_search_type: Optional[str] = "exact"
    ) -> OperationResponse:
        """
        搜索文档（复杂查询）
        
        Args:
            knowledge_id: 知识库ID
            doc_name_pattern: 文档名称模式匹配
            author: 作者
            doc_type: 文档类型
            status: 文档状态
            limit: 限制返回数量
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 符合条件的文档列表
        """
        try:
            rdb_client = await self._ensure_rdb_client()

            # 构建基础查询条件
            condition: Dict[str, Any] = {"is_active": True}

            if knowledge_id:
                condition["knowledge_id"] = knowledge_id
            if author:
                condition["author"] = author
            if doc_type:
                condition["doc_type"] = doc_type
            if status:
                condition["status"] = status.value

            if doc_name_pattern:
                if doc_name_search_type == "exact":
                    condition["doc_name"] = doc_name_pattern
                elif doc_name_search_type == "like":
                    # 构造SQL和参数（使用命名参数风格，兼容SQLAlchemy）
                    sql = f"SELECT * FROM {self.table_name} WHERE is_active = TRUE"
                    params = {}
                    if knowledge_id:
                        sql += " AND knowledge_id = :knowledge_id"
                        params["knowledge_id"] = knowledge_id
                    if author:
                        sql += " AND author = :author"
                        params["author"] = author
                    if doc_type:
                        sql += " AND doc_type = :doc_type"
                        params["doc_type"] = doc_type
                    if status:
                        sql += " AND status = :status"
                        params["status"] = status.value
                    # LIKE 查询
                    sql += " AND doc_name LIKE :doc_name_pattern"
                    params["doc_name_pattern"] = f"%{doc_name_pattern}%"
                    # 排序和分页
                    sql += " ORDER BY created_time DESC"
                    if limit is not None:
                        sql += " LIMIT :limit"
                        params["limit"] = limit
                    if offset:
                        sql += " OFFSET :offset"
                        params["offset"] = offset
                    # 执行
                    result = await rdb_client.aexecute(sql, params)
                    return result
                else:
                    raise ValueError(f"不支持的搜索类型: {doc_name_search_type}")
            result = await rdb_client.aselect(
                table=self.table_name,
                condition=condition,
                limit=limit,
                offset=offset,
                order_by=["created_time DESC"]
            )

            logger.debug(f"搜索到 {len(result)} 个文档")
            return result

        except Exception as e:
            logger.error(f"搜索文档失败: {e}")
            raise Exception(f"搜索文档失败: {str(e)}")

    # ==========================================
    # 文档别名管理操作
    # ==========================================

    async def create_alias(
        self,
        doc_id: str,
        cleaned_name: str
    ) -> int:
        """
        创建文档别名记录
        
        Args:
            doc_id: 文档ID
            cleaned_name: 清理后的别名
            
        Returns:
            int: 创建的别名记录ID
            
        Raises:
            Exception: 创建失败时抛出异常
        """
        try:
            alias_data = {
                "doc_id": doc_id,
                "cleaned_name": cleaned_name
            }

            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.insert(self.alias_table_name, [alias_data])

            if success:
                # 获取刚插入的记录ID (这里简化处理，实际可能需要使用LAST_INSERT_ID()等)
                result = rdb_client.select(
                    table=self.alias_table_name,
                    condition={"doc_id": doc_id, "cleaned_name": cleaned_name},
                    order_by=["id DESC"],
                    limit=1
                )

                if result:
                    alias_id = result[0]["id"]
                    logger.info(f"文档别名创建成功: {alias_id}")
                    return alias_id
                else:
                    raise Exception("无法获取创建的别名记录ID")
            else:
                raise Exception("数据库插入操作失败")

        except Exception as e:
            logger.error(f"创建文档别名失败: {e}")
            raise Exception(f"创建文档别名失败: {str(e)}")

    async def get_aliases_by_doc_id(self, doc_id: str) -> List[Dict[str, Any]]:
        """
        根据文档ID查询别名列表
        
        Args:
            doc_id: 文档ID
            
        Returns:
            List[Dict[str, Any]]: 别名记录列表
        """
        try:
            condition: Dict[str, Any] = {"doc_id": doc_id}
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.alias_table_name,
                condition=condition,
                order_by=["id ASC"]
            )

            logger.debug(f"查询到 {len(result)} 个别名记录 (doc_id: {doc_id})")
            return result

        except Exception as e:
            logger.error(f"查询文档别名失败: {e}")
            raise Exception(f"查询文档别名失败: {str(e)}")

    async def search_by_cleaned_name(self, cleaned_name: str) -> List[Dict[str, Any]]:
        """
        根据清理后的名称搜索别名记录
        
        Args:
            cleaned_name: 清理后的名称
            
        Returns:
            List[Dict[str, Any]]: 匹配的别名记录列表
        """
        try:
            condition: Dict[str, Any] = {"cleaned_name": cleaned_name}
            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.alias_table_name,
                condition=condition
            )

            logger.debug(f"根据别名查询到 {len(result)} 个记录")
            return result

        except Exception as e:
            logger.error(f"根据别名搜索失败: {e}")
            raise Exception(f"根据别名搜索失败: {str(e)}")

    async def delete_alias(self, alias_id: int) -> bool:
        """
        删除别名记录
        
        Args:
            alias_id: 别名记录ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.delete(
                table=self.alias_table_name,
                filter={"id": alias_id}
            )

            if success:
                logger.info(f"文档别名删除成功: {alias_id}")
            else:
                logger.warning(f"文档别名删除失败，可能不存在: {alias_id}")

            return success

        except Exception as e:
            logger.error(f"删除文档别名失败: {e}")
            raise Exception(f"删除文档别名失败: {str(e)}")

    async def delete_aliases_by_doc_id(self, doc_id: str) -> bool:
        """
        删除文档的所有别名记录
        
        Args:
            doc_id: 文档ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.delete(
                table=self.alias_table_name,
                filter={"doc_id": doc_id}
            )

            if success:
                logger.info(f"文档所有别名删除成功: {doc_id}")
            else:
                logger.warning(f"文档别名删除失败，可能不存在: {doc_id}")

            return success

        except Exception as e:
            logger.error(f"删除文档所有别名失败: {e}")
            raise Exception(f"删除文档所有别名失败: {str(e)}")

    # ==========================================
    # 文档关系管理操作
    # ==========================================

    async def create_document_relationship(
        self,
        source_doc_id: str,
        target_doc_id: str,
        rel_type: Optional[str] = None
    ) -> int:
        """
        创建文档关系记录
        
        Args:
            source_doc_id: 源文档ID
            target_doc_id: 目标文档ID
            rel_type: 关系类型
            
        Returns:
            int: 创建的关系记录ID
            
        Raises:
            Exception: 创建失败时抛出异常
        """
        try:
            relationship_data = {
                "source_doc_id": source_doc_id,
                "target_doc_id": target_doc_id,
                "rel_type": rel_type
            }

            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.insert(self.relationship_table_name, [relationship_data])

            if success:
                # 获取刚插入的记录ID
                result = rdb_client.select(
                    table=self.relationship_table_name,
                    condition={"source_doc_id": source_doc_id, "target_doc_id": target_doc_id, "rel_type": rel_type},
                    order_by=["id DESC"],
                    limit=1
                )

                if result:
                    relationship_id = result[0]["id"]
                    logger.info(f"文档关系创建成功: {relationship_id}")
                    return relationship_id
                else:
                    raise Exception("无法获取创建的关系记录ID")
            else:
                raise Exception("数据库插入操作失败")

        except Exception as e:
            logger.error(f"创建文档关系失败: {e}")
            raise Exception(f"创建文档关系失败: {str(e)}")

    async def get_document_relationships_by_source(
        self,
        source_doc_id: str,
        rel_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        根据源文档ID查询关系列表
        
        Args:
            source_doc_id: 源文档ID
            rel_type: 关系类型过滤
            
        Returns:
            List[Dict[str, Any]]: 关系记录列表
        """
        try:
            condition: Dict[str, Any] = {"source_doc_id": source_doc_id}
            if rel_type:
                condition["rel_type"] = rel_type

            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.relationship_table_name,
                condition=condition,
                order_by=["id ASC"]
            )

            logger.debug(f"查询到 {len(result)} 个关系记录 (source_doc_id: {source_doc_id})")
            return result

        except Exception as e:
            logger.error(f"查询文档关系失败: {e}")
            raise Exception(f"查询文档关系失败: {str(e)}")

    async def get_document_relationships_by_target(
        self,
        target_doc_id: str,
        rel_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        根据目标文档ID查询关系列表
        
        Args:
            target_doc_id: 目标文档ID
            rel_type: 关系类型过滤
            
        Returns:
            List[Dict[str, Any]]: 关系记录列表
        """
        try:
            condition: Dict[str, Any] = {"target_doc_id": target_doc_id}
            if rel_type:
                condition["rel_type"] = rel_type

            rdb_client = await self._ensure_rdb_client()
            result = rdb_client.select(
                table=self.relationship_table_name,
                condition=condition,
                order_by=["id ASC"]
            )

            logger.debug(f"查询到 {len(result)} 个关系记录 (target_doc_id: {target_doc_id})")
            return result

        except Exception as e:
            logger.error(f"查询文档关系失败: {e}")
            raise Exception(f"查询文档关系失败: {str(e)}")

    async def delete_document_relationship(
        self,
        relationship_id: int
    ) -> bool:
        """
        删除文档关系记录
        
        Args:
            relationship_id: 关系记录ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.delete(
                table=self.relationship_table_name,
                filter={"id": relationship_id}
            )

            if success:
                logger.info(f"文档关系删除成功: {relationship_id}")
            else:
                logger.warning(f"文档关系删除失败，可能不存在: {relationship_id}")

            return success

        except Exception as e:
            logger.error(f"删除文档关系失败: {e}")
            raise Exception(f"删除文档关系失败: {str(e)}")

    async def delete_document_relationships_by_source(
        self,
        source_doc_id: str,
        rel_type: Optional[str] = None
    ) -> bool:
        """
        删除源文档的所有关系记录
        
        Args:
            source_doc_id: 源文档ID
            rel_type: 关系类型，为None时删除所有类型的关系
            
        Returns:
            bool: 删除是否成功
        """
        try:
            filter_condition = {"source_doc_id": source_doc_id}
            if rel_type:
                filter_condition["rel_type"] = rel_type

            rdb_client = await self._ensure_rdb_client()
            success = rdb_client.delete(
                table=self.relationship_table_name,
                filter=filter_condition
            )

            if success:
                logger.info(f"源文档关系删除成功: {source_doc_id}")
            else:
                logger.warning(f"源文档关系删除失败，可能不存在: {source_doc_id}")

            return success

        except Exception as e:
            logger.error(f"删除源文档关系失败: {e}")
            raise Exception(f"删除源文档关系失败: {str(e)}")


async def main():
    """测试函数 - 展示新的配置化使用方式"""
    try:
        print("=== DocumentOperation 配置化使用示例 ===")

        # 方式1: 使用默认配置（从 knowledge.doc.rdb 获取）
        print("1. 使用默认配置创建操作实例")
        document_operation = DocumentOperation()

        # 方式2: 使用自定义配置路径
        print("2. 使用自定义配置路径（可选）")
        # document_operation = DocumentOperation(rdb_config_path="database.rdbs.mysql")

        # 方式3: 使用传入的客户端（兼容旧版本）
        print("3. 向后兼容：使用预先获取的客户端")
        # rdb_client = await get_client("database.rdbs.mysql")
        # document_operation = DocumentOperation(rdb_client=rdb_client)

        # 测试创建文档
        test_document = DocumentCreate(
            knowledge_id="test-kb-001",
            doc_name="测试文档.pdf",
            doc_type=None,
            author=None,
            vector_similarity_weight=None,
            similarity_threshold=None,
            parse_type=ParseType.PDF,
            status=DocumentStatus.PENDING,
            parse_end_time=None,
            parse_message=None,
            doc_format=DocumentFormat.PDF,
            location="/uploads/test-document.pdf",
            metadata=None,
            created_time=None,
            updated_time=None,
            is_active=True
        )

        doc_id = await document_operation.create_document(test_document)
        print(f"创建文档成功，ID: {doc_id}")

        # 测试查询文档
        document = await document_operation.get_document_by_id(doc_id)
        print(f"查询文档成功: {document}")

        # 测试更新状态
        success = await document_operation.update_document_status(
            doc_id,
            DocumentStatus.PROCESSING,
            "开始解析文档"
        )
        print(f"更新状态成功: {success}")

        # 测试查询知识库文档列表
        documents = await document_operation.get_documents_by_knowledge_id("test-kb-001")
        print(f"知识库文档列表: {len(documents)} 个文档")

        # 测试文档别名功能
        print("\n=== 测试文档别名功能 ===")
        alias_id = await document_operation.create_alias(
            doc_id=doc_id,
            cleaned_name="清理后的文档名"
        )
        print(f"创建别名成功，ID: {alias_id}")

        # 查询别名
        aliases = await document_operation.get_aliases_by_doc_id(doc_id)
        print(f"查询到别名: {len(aliases)} 个")

        # 测试文档关系功能
        print("\n=== 测试文档关系功能 ===")
        # 创建第二个文档用于建立关系
        test_document_2 = DocumentCreate(
            knowledge_id="test-kb-001",
            doc_name="测试文档2.pdf",
            doc_type=None,
            author=None,
            vector_similarity_weight=None,
            similarity_threshold=None,
            parse_type=ParseType.PDF,
            status=DocumentStatus.PENDING,
            parse_end_time=None,
            parse_message=None,
            doc_format=DocumentFormat.PDF,
            location="/uploads/test-document-2.pdf",
            metadata=None,
            created_time=None,
            updated_time=None,
            is_active=True
        )

        doc_id_2 = await document_operation.create_document(test_document_2)
        print(f"创建第二个文档成功，ID: {doc_id_2}")

        # 创建文档关系
        rel_id = await document_operation.create_document_relationship(
            source_doc_id=doc_id,
            target_doc_id=doc_id_2,
            rel_type="reference"
        )
        print(f"创建文档关系成功，ID: {rel_id}")

        # 查询文档关系
        relationships = await document_operation.get_document_relationships_by_source(doc_id)
        print(f"查询到源文档关系: {len(relationships)} 个")

    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())

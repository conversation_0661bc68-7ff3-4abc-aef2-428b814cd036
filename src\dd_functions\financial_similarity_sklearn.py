#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于sklearn的金融部门相关度计算器
使用成熟的TF-IDF实现和金融领域专业词汇
"""

import jieba
import jieba.posseg as pseg
import numpy as np
from typing import List, Tuple, Dict
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import re


class FinancialTextProcessor:
    """金融文本处理器，专门针对金融领域优化"""
    
    def __init__(self):
        """初始化金融文本处理器"""
        self.financial_dict = self._load_financial_dict()
        self.stop_words = self._load_financial_stopwords()
        self._setup_jieba()
    
    def _load_financial_dict(self) -> set:
        """加载金融专业词汇"""
        financial_terms = {
            # 风险管理相关
            "风险管理", "风险控制", "风险评估", "风险监控", "风险识别", "信用风险", "市场风险", 
            "操作风险", "流动性风险", "合规风险", "风险缓释", "风险敞口", "风险偏好",
            "风险限额", "风险报告", "压力测试", "情景分析", "敏感性分析",
            
            # 信贷业务相关
            "不良贷款", "贷款损失", "拨备覆盖", "信贷投放", "授信审批", "贷后管理",
            "信贷结构", "信贷质量", "逾期贷款", "关注类贷款", "次级贷款", "可疑贷款",
            "损失贷款", "五级分类", "信贷政策", "放款审查", "担保方式", "抵押物",
            
            # 金融市场相关
            "利率风险", "汇率风险", "价格风险", "基准利率", "市场利率", "收益率曲线",
            "久期", "凸性", "价值在险", "预期损失", "非预期损失", "市场价格",
            
            # 资产负债管理
            "资产负债", "流动性管理", "资本充足率", "杠杆率", "存贷比", "资产质量",
            "负债结构", "期限错配", "利率敏感性", "资产配置", "负债成本",
            
            # 合规监管相关
            "合规管理", "内控制度", "监管要求", "监管指标", "监管报告", "反洗钱",
            "客户尽职调查", "可疑交易", "监管处罚", "合规检查", "内部审计",
            
            # 业务类型
            "零售银行", "公司银行", "投资银行", "资产管理", "私人银行", "金融市场",
            "同业业务", "票据业务", "债券投资", "股权投资", "衍生品交易",
            
            # 财务指标
            "净利润", "营业收入", "成本收入比", "净息差", "中间业务收入", "手续费收入",
            "投资收益", "资产减值损失", "营业税金", "所得税", "每股收益", "净资产收益率",
            
            # 其他金融术语
            "金融科技", "数字化转型", "普惠金融", "绿色金融", "供应链金融",
            "消费金融", "小微企业", "三农金融", "跨境金融", "离岸金融"
        }
        
        # 将金融词汇添加到jieba词典
        for term in financial_terms:
            jieba.add_word(term)
        
        return financial_terms
    
    def _load_financial_stopwords(self) -> set:
        """加载金融领域停用词"""
        basic_stopwords = {
            '的', '是', '在', '有', '和', '与', '或', '但', '了', '就', '都', '也',
            '要', '会', '可', '能', '等', '中', '及', '对', '为', '由', '从', '到',
            '这', '那', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
            '个', '些', '种', '类', '项', '分', '方', '面', '时', '间', '年', '月'
        }
        
        # 金融领域常见但信息量较低的词
        financial_stopwords = {
            '银行', '金融', '机构', '业务', '工作', '管理', '部门', '相关', '主要',
            '包括', '以及', '进行', '开展', '实施', '建立', '完善', '加强', '提高',
            '确保', '通过', '根据', '按照', '依据', '基于', '关于', '针对'
        }
        
        return basic_stopwords | financial_stopwords
    
    def _setup_jieba(self):
        """设置jieba分词参数"""
        # 设置精确模式，适合金融专业文本
        jieba.initialize()
    
    def process_text(self, text: str) -> str:
        """处理文本，返回清洗后的文本"""
        # 去除特殊字符，保留中文、英文、数字
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', text)
        
        # 使用词性标注进行分词，保留有意义的词性
        words = []
        for word, flag in pseg.cut(text):
            # 保留名词、动词、形容词、金融专业词汇
            if (len(word) > 1 and 
                word not in self.stop_words and
                (flag.startswith('n') or flag.startswith('v') or flag.startswith('a') or 
                 word in self.financial_dict)):
                words.append(word)
        
        return ' '.join(words)


class FinancialSimilarityCalculator:
    """基于sklearn的金融部门相关度计算器"""
    
    def __init__(self, max_features: int = 5000, min_df: int = 1, max_df: float = 0.8):
        """
        初始化相似度计算器
        
        Args:
            max_features: 最大特征数量
            min_df: 词汇最小文档频率
            max_df: 词汇最大文档频率
        """
        self.processor = FinancialTextProcessor()
        self.vectorizer = TfidfVectorizer(
            max_features=max_features,
            min_df=min_df,
            max_df=max_df,
            tokenizer=self._custom_tokenizer,
            token_pattern=None,  # 使用自定义tokenizer时设为None
            lowercase=False,  # 已在预处理中处理
            stop_words=None,  # 已在预处理中处理
            ngram_range=(1, 2),  # 包含1-2元词组
            sublinear_tf=True,  # 使用亚线性TF缩放
            smooth_idf=True,  # 平滑IDF
            norm='l2'  # L2归一化
        )
        self.departments_data = []
        self.tfidf_matrix = None
        self.fitted = False
    
    def _custom_tokenizer(self, text: str) -> List[str]:
        """自定义分词器，已经预处理过的文本直接分割"""
        return text.split()
    
    def set_departments(self, departments_data: List[Tuple[str, str]]):
        """
        设置部门数据并训练TF-IDF模型
        
        Args:
            departments_data: [(部门名称, 职责描述), ...]
        """
        self.departments_data = departments_data
        
        # 预处理所有部门文档
        processed_docs = []
        for dept_name, dept_desc in departments_data:
            # 合并部门名称和描述
            combined_text = f"{dept_name} {dept_desc}"
            processed_text = self.processor.process_text(combined_text)
            processed_docs.append(processed_text)
        
        # 训练TF-IDF模型
        self.tfidf_matrix = self.vectorizer.fit_transform(processed_docs)
        self.fitted = True
        
        print(f"✅ 成功训练TF-IDF模型")
        print(f"📊 词汇表大小: {len(self.vectorizer.vocabulary_)}")
        print(f"🏢 部门数量: {len(departments_data)}")
        print(f"📝 TF-IDF矩阵形状: {self.tfidf_matrix.shape}")
    
    def calculate_similarity(self, indicator_name: str, indicator_description: str, 
                           top_k: int = 5) -> List[Tuple[str, float]]:
        """
        计算指标与部门的相似度
        
        Args:
            indicator_name: 指标名称
            indicator_description: 指标描述
            top_k: 返回top-k个最相关部门
            
        Returns:
            [(部门名称, 相似度分数), ...]，按相似度降序排序
        """
        if not self.fitted:
            raise ValueError("请先调用set_departments()设置部门数据")
        
        # 预处理指标文本
        combined_indicator = f"{indicator_name} {indicator_description}"
        processed_indicator = self.processor.process_text(combined_indicator)
        
        # 转换为TF-IDF向量
        indicator_vector = self.vectorizer.transform([processed_indicator])
        
        # 计算余弦相似度
        similarities = cosine_similarity(indicator_vector, self.tfidf_matrix).flatten()
        
        # 获取top-k结果
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            dept_name = self.departments_data[idx][0]
            similarity_score = similarities[idx]
            results.append((dept_name, similarity_score))
        
        return results
    
    def get_feature_importance(self, indicator_name: str, indicator_description: str, 
                             top_features: int = 10) -> List[Tuple[str, float]]:
        """
        获取指标的关键特征词
        
        Args:
            indicator_name: 指标名称
            indicator_description: 指标描述
            top_features: 返回top特征数量
            
        Returns:
            [(特征词, TF-IDF权重), ...]，按权重降序排序
        """
        if not self.fitted:
            raise ValueError("请先调用set_departments()设置部门数据")
        
        # 预处理指标文本
        combined_indicator = f"{indicator_name} {indicator_description}"
        processed_indicator = self.processor.process_text(combined_indicator)
        
        # 转换为TF-IDF向量
        indicator_vector = self.vectorizer.transform([processed_indicator])
        
        # 获取特征名称
        feature_names = self.vectorizer.get_feature_names_out()
        
        # 获取非零特征及其权重
        feature_weights = []
        coo_matrix = indicator_vector.tocoo()
        for idx, weight in zip(coo_matrix.col, coo_matrix.data):
            feature_weights.append((feature_names[idx], weight))
        
        # 按权重降序排序
        feature_weights.sort(key=lambda x: x[1], reverse=True)
        
        return feature_weights[:top_features]
    
    def explain_similarity(self, indicator_name: str, indicator_description: str,
                          department_name: str) -> Dict:
        """
        解释指标与特定部门的相似度计算过程
        
        Args:
            indicator_name: 指标名称
            indicator_description: 指标描述
            department_name: 部门名称
            
        Returns:
            包含详细解释信息的字典
        """
        if not self.fitted:
            raise ValueError("请先调用set_departments()设置部门数据")
        
        # 找到目标部门索引
        dept_idx = None
        for i, (dept_name, _) in enumerate(self.departments_data):
            if dept_name == department_name:
                dept_idx = i
                break
        
        if dept_idx is None:
            raise ValueError(f"未找到部门: {department_name}")
        
        # 计算相似度
        similarity_results = self.calculate_similarity(indicator_name, indicator_description, top_k=len(self.departments_data))
        current_similarity = next(score for name, score in similarity_results if name == department_name)
        
        # 获取指标关键词
        indicator_features = self.get_feature_importance(indicator_name, indicator_description, top_features=10)
        
        # 获取部门关键词
        dept_vector = self.tfidf_matrix[dept_idx:dept_idx+1]
        feature_names = self.vectorizer.get_feature_names_out()
        dept_features = []
        coo_matrix = dept_vector.tocoo()
        for idx, weight in zip(coo_matrix.col, coo_matrix.data):
            dept_features.append((feature_names[idx], weight))
        dept_features.sort(key=lambda x: x[1], reverse=True)
        dept_features = dept_features[:10]
        
        # 找到共同特征
        indicator_words = {word for word, _ in indicator_features}
        dept_words = {word for word, _ in dept_features}
        common_features = indicator_words & dept_words
        
        return {
            "department": department_name,
            "similarity_score": current_similarity,
            "indicator_keywords": indicator_features,
            "department_keywords": dept_features,
            "common_features": list(common_features),
            "explanation": f"相似度 {current_similarity:.4f} 基于 {len(common_features)} 个共同关键词"
        }


def demo_usage():
    """演示用法"""
    print("=== 基于sklearn的金融部门相关度计算器演示 ===\n")
    
    # 银行部门数据（更真实的金融业务描述）
    departments_data = [
        ("风险管理部", "负责全行风险识别、评估、监控和控制，制定风险管理政策和流程，建立风险管理体系，包括信用风险、市场风险、操作风险、流动性风险等各类风险的管理"),
        ("信贷管理部", "负责信贷业务的审批、放款、贷后管理，控制信贷风险，制定信贷政策，管理不良贷款，实施五级分类，确保资产质量"),
        ("资产负债管理部", "负责银行资产负债结构优化，流动性管理，利率风险管理，资本充足率管理，期限错配控制，资产配置策略制定"),
        ("市场风险管理部", "负责市场风险的识别、计量、监控和控制，包括利率风险、汇率风险、商品价格风险，价值在险模型管理，压力测试"),
        ("信用风险管理部", "负责信用风险的识别、评估、监控，建立信用风险模型和评级体系，客户信用评级，违约概率预测，预期损失计算"),
        ("操作风险管理部", "负责操作风险的识别、评估、监控和控制，完善内控制度建设，业务流程管理，系统风险防控，人员风险管理"),
        ("合规管理部", "负责合规风险管理，监督各业务条线合规经营，开展合规检查，反洗钱管理，监管关系维护，合规培训"),
        ("内控管理部", "负责内部控制体系建设，内控制度制定和执行监督，内部审计，内控评价，制度流程优化"),
        ("财务管理部", "负责财务核算、预算管理、成本控制、财务分析和报告，会计核算，税务管理，资金管理，绩效考核"),
        ("零售银行部", "负责个人客户金融服务，储蓄存款，个人贷款，信用卡业务，财富管理，私人银行，消费金融"),
        ("公司银行部", "负责企业客户金融服务，公司存贷款，贸易融资，现金管理，供应链金融，投行业务，债券承销"),
        ("金融市场部", "负责金融市场交易，债券投资，货币市场业务，外汇交易，衍生品交易，同业业务，资金交易"),
        ("信息科技部", "负责银行信息系统建设维护，科技风险管理，网络安全，数字化转型，金融科技创新，系统开发运维"),
        ("人力资源部", "负责人员招聘、培训、绩效考核、薪酬管理和组织发展，人才梯队建设，企业文化，员工关系管理"),
        ("战略规划部", "负责银行发展战略制定，业务规划，市场分析，政策研究，创新业务孵化，战略执行监控")
    ]
    
    # 创建计算器
    calculator = FinancialSimilarityCalculator(max_features=3000, min_df=1, max_df=0.9)
    
    # 设置部门数据
    calculator.set_departments(departments_data)
    
    # 测试指标
    test_cases = [
        {
            "name": "不良贷款率",
            "description": "银行不良贷款余额占贷款总余额的比例，反映银行信贷资产质量和信用风险水平，是监管机构重点关注的风险指标"
        },
        {
            "name": "资本充足率",
            "description": "银行资本与风险加权资产的比率，衡量银行资本实力和抗风险能力，是巴塞尔协议的核心监管指标"
        },
        {
            "name": "净息差",
            "description": "银行净利息收入与平均生息资产的比率，反映银行盈利能力和资产运用效率，是衡量银行经营效益的重要指标"
        }
    ]
    
    for case in test_cases:
        print(f"\n{'='*60}")
        print(f"📊 指标分析：{case['name']}")
        print(f"📝 指标描述：{case['description']}")
        print(f"{'='*60}")
        
        # 计算相关度Top-5
        results = calculator.calculate_similarity(case['name'], case['description'], top_k=5)
        
        print(f"\n🏆 Top-5 相关部门：")
        for i, (dept_name, score) in enumerate(results, 1):
            print(f"{i:2d}. {dept_name:<12} (相似度: {score:.4f})")
        
        # 显示关键特征词
        features = calculator.get_feature_importance(case['name'], case['description'], top_features=8)
        print(f"\n🔑 关键特征词：")
        for word, weight in features:
            print(f"    {word:<15} (权重: {weight:.4f})")
        
        # 详细解释第一名部门的相似度
        if results:
            top_dept = results[0][0]
            explanation = calculator.explain_similarity(case['name'], case['description'], top_dept)
            print(f"\n💡 与 '{top_dept}' 的相似度解释：")
            print(f"    {explanation['explanation']}")
            if explanation['common_features']:
                print(f"    共同关键词: {', '.join(explanation['common_features'])}")


if __name__ == "__main__":
    demo_usage() 
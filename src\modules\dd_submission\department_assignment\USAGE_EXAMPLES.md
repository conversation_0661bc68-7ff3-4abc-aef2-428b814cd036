# DD部门职责分配模块使用示例

## 📋 概述

重构后的DD部门职责分配模块提供了两个主要接口：
- `DepartmentAssignment` - 部门分配功能
- `DataBackfill` - 数据回填功能

## 🎯 部门分配功能

### 基本使用

```python
from modules.dd_submission.department_assignment import DepartmentAssignment
from modules.dd_submission.department_assignment import (
    DepartmentAssignmentRequest,
    BatchAssignmentRequest
)

# 初始化
assignment = DepartmentAssignment(rdb_client, vdb_client)
```

### 单个部门分配

```python
# 创建分配请求
request = DepartmentAssignmentRequest(
    submission_id="TEST_001",
    dr09="银行存款余额",
    dr17="银行存款账户的期末余额，包括活期存款和定期存款",
    set_value="SET_A",
    report_type="detail",
    submission_type="submission",
    dr01="ADS"
)

# 执行分配
result = await assignment.assign_single(request)

print(f"推荐部门: {result.recommended_department}")
print(f"置信度: {result.confidence_level}")
```

### 批量部门分配

```python
# 创建批量请求
batch_request = BatchAssignmentRequest(
    report_code="g0107_beta_v1.0"
)

# 执行批量分配（不保存）
result = await assignment.assign_batch(batch_request)

print(f"处理成功: {result.success}")
print(f"成功分配: {result.successful_assignments}")
```

### 义务分发（分配+保存）

```python
# 执行完整的义务分发流程
result = await assignment.assign_and_save(batch_request)

print(f"处理成功: {result['success']}")
print(f"分配成功率: {result['processing_summary']['assignment_success_rate']:.2%}")
print(f"保存成功率: {result['processing_summary']['save_success_rate']:.2%}")
```

## 🔄 数据回填功能

### 基本使用

```python
from modules.dd_submission.department_assignment import DataBackfill

# 初始化
backfill = DataBackfill(rdb_client)
```

### 数据回填处理

```python
# 准备回填数据
backfill_data = [
    {
        "entry_id": "TEST_001",
        "entry_type": "填报项",
        "DR22": ["DEPT_FINANCE"],
        "BDR01": ["DEPT_FINANCE"],
        "BDR03": ["更新后的备注"]
    },
    {
        "entry_id": "TEST_002",
        "entry_type": "范围",
        "DR22": ["DEPT_CREDIT"],
        "BDR01": ["DEPT_CREDIT"],
        "BDR03": [""]
    }
]

# 执行回填处理
result = await backfill.process_backfill(
    report_code="g0107_beta_v1.0",
    step="义务解读",
    data=backfill_data
)

print(f"处理成功: {result['success']}")
print(f"更新数量: {result['statistics']['updated_count']}")
print(f"成功率: {result['statistics']['success_rate']:.2%}")
```

### 数据验证

```python
# 验证回填数据格式
validation_result = await backfill.validate_backfill_data(
    report_code="g0107_beta_v1.0",
    data=backfill_data
)

if not validation_result['success']:
    print("数据验证失败:")
    for error in validation_result['validation_errors']:
        print(f"  - {error['error']}")
```

### 状态查询

```python
# 查询回填状态
status_result = await backfill.get_backfill_status(
    report_code="g0107_beta_v1.0",
    entry_ids=["TEST_001", "TEST_002"]  # 可选，不提供则查询所有
)

print(f"查询成功: {status_result['success']}")
print(f"记录数量: {status_result['total_count']}")
```

## 🔧 便捷函数使用

### 部门分配便捷函数

```python
from modules.dd_submission.department_assignment import (
    assign_department_single,
    assign_department_batch,
    process_duty_distribution
)

# 单个分配
result = await assign_department_single(rdb_client, vdb_client, request)

# 批量分配
result = await assign_department_batch(rdb_client, vdb_client, batch_request)

# 义务分发
result = await process_duty_distribution(rdb_client, vdb_client, "g0107_beta_v1.0")
```

### 数据回填便捷函数

```python
from modules.dd_submission.department_assignment import (
    process_data_backfill,
    validate_backfill_data,
    get_backfill_status
)

# 处理回填
result = await process_data_backfill(rdb_client, "g0107_beta_v1.0", "义务解读", data)

# 验证数据
result = await validate_backfill_data(rdb_client, "g0107_beta_v1.0", data)

# 查询状态
result = await get_backfill_status(rdb_client, "g0107_beta_v1.0")
```

## 🔄 向后兼容使用

### 原有接口仍然可用

```python
# 原有的类名仍然可以使用
from modules.dd_submission.department_assignment import (
    DepartmentAssignmentLogic,  # 等同于 DepartmentAssignment
    DataBackfillLogic,          # 等同于 DataBackfill
    PostDistributionDBOperations
)

# 原有的使用方式
assignment_logic = DepartmentAssignmentLogic(rdb_client, vdb_client)
result = await assignment_logic.assign_department(request)
```

## 🧪 测试使用

### 运行测试

```python
# 运行部门分配测试
from modules.dd_submission.department_assignment.tests import run_department_assignment_tests
await run_department_assignment_tests(rdb_client, vdb_client)

# 运行数据回填测试
from modules.dd_submission.department_assignment.tests import run_data_backfill_tests
await run_data_backfill_tests(rdb_client, vdb_client)
```

### 演示功能

```python
# 运行功能演示
from modules.dd_submission.department_assignment.tests.demo import DepartmentAssignmentDemo

demo = DepartmentAssignmentDemo()
await demo.run_full_demo()
```

## 📊 错误处理

### 异常捕获

```python
from modules.dd_submission.department_assignment import (
    DepartmentAssignmentError,
    DepartmentAssignmentValidationError
)

try:
    result = await assignment.assign_single(request)
except DepartmentAssignmentError as e:
    print(f"分配失败: {e}")
except DepartmentAssignmentValidationError as e:
    print(f"验证失败: {e}")
```

### 结果检查

```python
# 检查处理结果
if result['success']:
    print("处理成功")
else:
    print(f"处理失败: {result['message']}")
```

## 🎯 最佳实践

### 1. 资源管理

```python
# 推荐使用上下文管理器
async with DatabaseConnection() as rdb_client:
    assignment = DepartmentAssignment(rdb_client, vdb_client)
    result = await assignment.assign_and_save(batch_request)
```

### 2. 错误处理

```python
# 完整的错误处理
try:
    result = await assignment.assign_and_save(batch_request)
    if result['success']:
        logger.info(f"处理成功: {result['processing_summary']}")
    else:
        logger.error(f"处理失败: {result['message']}")
except Exception as e:
    logger.error(f"系统错误: {e}")
```

### 3. 性能监控

```python
import time

start_time = time.time()
result = await assignment.assign_and_save(batch_request)
processing_time = time.time() - start_time

logger.info(f"处理耗时: {processing_time:.2f}秒")
```

---

**文档版本**: v2.0.0  
**更新时间**: 2025-07-24  
**适用版本**: 重构后版本

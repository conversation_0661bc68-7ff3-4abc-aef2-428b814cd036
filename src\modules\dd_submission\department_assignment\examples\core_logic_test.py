#!/usr/bin/env python3
"""
数据回填优化核心逻辑测试

专注测试优化引擎的核心逻辑，不依赖数据库操作
"""

import asyncio
import time
import logging
from datetime import datetime
from typing import List, Dict, Any
from unittest.mock import Mock, AsyncMock

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from modules.dd_submission.department_assignment.core.optimized_backfill_engine import (
    OptimizedBackfillEngine, BackfillConfig, BackfillResult
)


class CoreLogicTest:
    """核心逻辑测试类"""
    
    def __init__(self):
        self.mock_rdb_client = None
        self.mock_vdb_client = None
        self.optimized_engine = None
        
    async def initialize(self):
        """初始化测试环境"""
        try:
            logger.info("🔧 初始化核心逻辑测试环境...")
            
            # 创建模拟的数据库客户端
            self.mock_rdb_client = Mock()
            self.mock_vdb_client = Mock()
            
            # 设置模拟的DDCrud响应
            self._setup_mock_responses()
            
            # 创建优化引擎
            test_config = BackfillConfig(
                batch_size=10,
                max_concurrency=2,
                timeout_per_batch=60.0,
                enable_transaction=True,
                enable_validation=True,
                fallback_enabled=True
            )
            try:
                self.optimized_engine = OptimizedBackfillEngine(
                    self.mock_rdb_client, self.mock_vdb_client, test_config
                )

                # 手动设置模拟的DDCrud
                mock_dd_crud = Mock()
                mock_dd_crud.list_post_distributions = AsyncMock()
                self.optimized_engine.dd_crud = mock_dd_crud
            except Exception as e:
                logger.error(f"创建优化引擎失败: {e}")
                raise
            
            logger.info("✅ 核心逻辑测试环境初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试环境初始化失败: {e}")
            return False

    def _setup_mock_responses(self):
        """设置模拟响应"""
        
        # 模拟DDCrud的list_post_distributions响应
        mock_records = [
            {
                'submission_id': 'TEST_001',
                'dr07': 'TEST_TABLE',
                'version': 'v1.0',
                'dr22': '原始部门',
                'bdr01': '原始业务部门',
                'bdr03': '原始描述',
                'table_id': 'table_1'
            },
            {
                'submission_id': 'TEST_002',
                'dr07': 'TEST_TABLE',
                'version': 'v1.0',
                'dr22': '原始部门2',
                'bdr01': '原始业务部门2',
                'bdr03': '原始描述2',
                'table_id': 'table_2'
            }
        ]
        
        # 模拟数据库查询响应
        mock_dept_response = Mock()
        mock_dept_response.success = True
        mock_dept_response.data = [
            {'dept_id': 'DEPT_0', 'table_id': 'table_1'},
            {'dept_id': 'DEPT_1', 'table_id': 'table_2'},
            {'dept_id': 'DEPT_2', 'table_id': 'table_3'}
        ]
        
        # 模拟批量更新响应
        mock_update_response = Mock()
        mock_update_response.success = True
        mock_update_response.affected_rows = 2
        
        # 设置异步方法
        self.mock_rdb_client.afetch_all = AsyncMock(return_value=mock_dept_response)
        self.mock_rdb_client.abatch_update = AsyncMock(return_value=mock_update_response)
        self.mock_rdb_client.aexecute = AsyncMock(return_value=mock_update_response)
        
        # 模拟DDCrud
        self.optimized_engine.dd_crud.list_post_distributions = AsyncMock(return_value=mock_records)

    async def test_data_preprocessing(self) -> Dict[str, Any]:
        """测试数据预处理"""
        logger.info("📋 测试：数据预处理")
        
        test_results = {
            'test_name': '数据预处理',
            'success': True,
            'details': [],
            'errors': []
        }
        
        try:
            # 测试数据
            raw_data = [
                {
                    'entry_id': 'TEST_001',
                    'entry_type': 'test',
                    'DR22': ['DEPT_0'],
                    'BDR01': ['BIZ_DEPT_0'],
                    'BDR03': ['新描述1']
                },
                {
                    'entry_id': 'TEST_002',
                    'entry_type': 'test',
                    'DR22': ['DEPT_1'],
                    'BDR01': ['BIZ_DEPT_1'],
                    'BDR03': ['新描述2']
                },
                {
                    # 无效数据：缺少entry_id
                    'entry_type': 'test',
                    'DR22': ['DEPT_2']
                }
            ]
            
            # 执行数据预处理
            validated_data = await self.optimized_engine._preprocess_and_validate_data(raw_data)
            
            # 验证结果
            if len(validated_data) == 2:  # 应该过滤掉1条无效数据
                test_results['details'].append(f"数据预处理成功: {len(raw_data)} -> {len(validated_data)}")
                
                # 验证数据格式
                for item in validated_data:
                    if 'entry_id' in item and isinstance(item['DR22'], list):
                        test_results['details'].append(f"数据格式正确: {item['entry_id']}")
                    else:
                        test_results['errors'].append(f"数据格式错误: {item}")
                        test_results['success'] = False
            else:
                test_results['errors'].append(f"数据预处理结果错误: 期望2条，实际{len(validated_data)}条")
                test_results['success'] = False
            
            logger.info("✅ 数据预处理测试完成")
            
        except Exception as e:
            logger.error(f"❌ 数据预处理测试失败: {e}")
            test_results['success'] = False
            test_results['errors'].append(str(e))
        
        return test_results

    async def test_batch_data_retrieval(self) -> Dict[str, Any]:
        """测试批量数据获取"""
        logger.info("📋 测试：批量数据获取")
        
        test_results = {
            'test_name': '批量数据获取',
            'success': True,
            'details': [],
            'errors': []
        }
        
        try:
            # 测试参数
            report_code = "TEST_TABLE_123_v1.0"
            entry_ids = ['TEST_001', 'TEST_002']
            
            # 执行批量数据获取
            current_records = await self.optimized_engine._batch_get_current_records(
                report_code, entry_ids
            )
            
            # 验证结果
            if len(current_records) == 2:
                test_results['details'].append(f"批量获取成功: {len(current_records)}条记录")
                
                # 验证记录内容
                for entry_id in entry_ids:
                    if entry_id in current_records:
                        record = current_records[entry_id]
                        test_results['details'].append(f"记录存在: {entry_id} -> {record['dr22']}")
                    else:
                        test_results['errors'].append(f"记录缺失: {entry_id}")
                        test_results['success'] = False
            else:
                test_results['errors'].append(f"批量获取结果错误: 期望2条，实际{len(current_records)}条")
                test_results['success'] = False
            
            # 验证DDCrud调用
            self.optimized_engine.dd_crud.list_post_distributions.assert_called_once()
            call_args = self.optimized_engine.dd_crud.list_post_distributions.call_args
            test_results['details'].append(f"DDCrud调用参数: {call_args}")
            
            logger.info("✅ 批量数据获取测试完成")
            
        except Exception as e:
            logger.error(f"❌ 批量数据获取测试失败: {e}")
            test_results['success'] = False
            test_results['errors'].append(str(e))
        
        return test_results

    async def test_department_relations_batch(self) -> Dict[str, Any]:
        """测试批量部门关联获取"""
        logger.info("📋 测试：批量部门关联获取")
        
        test_results = {
            'test_name': '批量部门关联获取',
            'success': True,
            'details': [],
            'errors': []
        }
        
        try:
            # 测试数据
            dr22_list = [
                ['DEPT_0'],
                ['DEPT_1'],
                ['DEPT_2'],
                ['DEPT_0']  # 重复部门，应该去重
            ]
            
            # 执行批量部门关联获取
            dept_relations = await self.optimized_engine._batch_get_department_relations(dr22_list)
            
            # 验证结果
            if len(dept_relations) == 3:  # 应该去重后有3个部门
                test_results['details'].append(f"部门关联获取成功: {len(dept_relations)}个部门")
                
                # 验证部门关联内容
                for dept_id, table_ids in dept_relations.items():
                    test_results['details'].append(f"部门关联: {dept_id} -> {table_ids}")
            else:
                test_results['errors'].append(f"部门关联结果错误: 期望3个，实际{len(dept_relations)}个")
                test_results['success'] = False
            
            # 验证数据库调用
            self.mock_rdb_client.afetch_all.assert_called_once()
            
            logger.info("✅ 批量部门关联获取测试完成")
            
        except Exception as e:
            logger.error(f"❌ 批量部门关联获取测试失败: {e}")
            test_results['success'] = False
            test_results['errors'].append(str(e))
        
        return test_results

    async def test_operation_classification(self) -> Dict[str, Any]:
        """测试操作分类"""
        logger.info("📋 测试：操作分类")
        
        test_results = {
            'test_name': '操作分类',
            'success': True,
            'details': [],
            'errors': []
        }
        
        try:
            # 准备测试数据
            validated_data = [
                {
                    'entry_id': 'TEST_001',
                    'DR22': ['DEPT_0'],  # 有交集，应该是field_update
                    'BDR01': ['新业务部门1'],
                    'BDR03': ['新描述1']
                },
                {
                    'entry_id': 'TEST_002',
                    'DR22': ['DEPT_999'],  # 无交集，应该是clear_update
                    'BDR01': ['新业务部门2'],
                    'BDR03': ['新描述2']
                }
            ]
            
            current_records = {
                'TEST_001': {
                    'submission_id': 'TEST_001',
                    'dr22': '原始部门',
                    'bdr01': '原始业务部门',
                    'bdr03': '原始描述',
                    'table_id': 'table_1'
                },
                'TEST_002': {
                    'submission_id': 'TEST_002',
                    'dr22': '原始部门2',
                    'bdr01': '原始业务部门2',
                    'bdr03': '原始描述2',
                    'table_id': 'table_2'
                }
            }
            
            dept_relations = {
                'DEPT_0': ['table_1'],  # 与TEST_001有交集
                'DEPT_1': ['table_2'],
                'DEPT_2': ['table_3']
            }
            
            # 执行操作分类
            operations = await self.optimized_engine._classify_batch_operations(
                validated_data, current_records, dept_relations, "TEST_TABLE_123_v1.0"
            )
            
            # 验证结果
            if len(operations) == 2:
                test_results['details'].append(f"操作分类成功: {len(operations)}个操作")
                
                # 验证操作类型
                for op in operations:
                    test_results['details'].append(
                        f"操作: {op.entry_id} -> {op.operation_type}"
                    )
                    
                    # 验证第一个操作应该是field_update
                    if op.entry_id == 'TEST_001' and op.operation_type != 'field_update':
                        test_results['errors'].append(f"操作类型错误: {op.entry_id} 应该是 field_update")
                        test_results['success'] = False
                    
                    # 验证第二个操作应该是clear_update
                    if op.entry_id == 'TEST_002' and op.operation_type != 'clear_update':
                        test_results['errors'].append(f"操作类型错误: {op.entry_id} 应该是 clear_update")
                        test_results['success'] = False
            else:
                test_results['errors'].append(f"操作分类结果错误: 期望2个，实际{len(operations)}个")
                test_results['success'] = False
            
            logger.info("✅ 操作分类测试完成")
            
        except Exception as e:
            logger.error(f"❌ 操作分类测试失败: {e}")
            test_results['success'] = False
            test_results['errors'].append(str(e))
        
        return test_results

    async def test_performance_stats(self) -> Dict[str, Any]:
        """测试性能统计"""
        logger.info("📋 测试：性能统计")
        
        test_results = {
            'test_name': '性能统计',
            'success': True,
            'details': [],
            'errors': []
        }
        
        try:
            # 获取初始统计
            initial_stats = self.optimized_engine.get_performance_stats()
            test_results['details'].append(f"初始统计: {initial_stats}")
            
            # 验证统计结构
            expected_keys = ['total_operations', 'batch_operations', 'fallback_operations', 'total_processing_time']
            for key in expected_keys:
                if key in initial_stats:
                    test_results['details'].append(f"统计字段存在: {key}")
                else:
                    test_results['errors'].append(f"统计字段缺失: {key}")
                    test_results['success'] = False
            
            # 验证计算字段
            if 'avg_processing_time' in initial_stats and 'batch_success_rate' in initial_stats:
                test_results['details'].append("计算字段正常")
            else:
                test_results['errors'].append("计算字段缺失")
                test_results['success'] = False
            
            logger.info("✅ 性能统计测试完成")
            
        except Exception as e:
            logger.error(f"❌ 性能统计测试失败: {e}")
            test_results['success'] = False
            test_results['errors'].append(str(e))
        
        return test_results


async def main():
    """主测试函数"""
    print("🚀 数据回填优化核心逻辑测试")
    print("=" * 60)
    print("测试目标：验证优化引擎核心逻辑功能")
    print("=" * 60)
    
    # 创建测试实例
    test = CoreLogicTest()
    
    # 初始化测试环境
    if not await test.initialize():
        print("❌ 测试环境初始化失败，退出测试")
        return False
    
    # 执行测试用例
    test_results = []
    
    try:
        # 测试1：数据预处理
        result1 = await test.test_data_preprocessing()
        test_results.append(result1)
        
        # 测试2：批量数据获取
        result2 = await test.test_batch_data_retrieval()
        test_results.append(result2)
        
        # 测试3：批量部门关联获取
        result3 = await test.test_department_relations_batch()
        test_results.append(result3)
        
        # 测试4：操作分类
        result4 = await test.test_operation_classification()
        test_results.append(result4)
        
        # 测试5：性能统计
        result5 = await test.test_performance_stats()
        test_results.append(result5)
        
    except Exception as e:
        logger.error(f"测试执行过程中发生异常: {e}")
        return False
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 核心逻辑测试报告")
    print("=" * 60)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results if result['success'])
    
    for result in test_results:
        status = "✅ 通过" if result['success'] else "❌ 失败"
        print(f"\n🔍 {result['test_name']}: {status}")
        
        if result['details']:
            for detail in result['details'][:3]:  # 只显示前3条详情
                print(f"   📝 {detail}")
            if len(result['details']) > 3:
                print(f"   📝 ... 还有{len(result['details']) - 3}条详情")
        
        if result['errors']:
            for error in result['errors']:
                print(f"   ❌ {error}")
    
    # 总结
    print(f"\n📈 测试总结:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests / total_tests * 100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有核心逻辑测试通过！")
        print("✅ 数据预处理逻辑正常")
        print("✅ 批量数据获取逻辑正常")
        print("✅ 部门关联处理逻辑正常")
        print("✅ 操作分类逻辑正常")
        print("✅ 性能统计功能正常")
        print("\n🚀 优化引擎核心逻辑验证完成，可以进行集成测试！")
        return True
    else:
        print(f"\n⚠️ 有{total_tests - passed_tests}个测试失败")
        print("❌ 需要修复核心逻辑问题")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

"""
连接池上下文管理器

提供更好的连接池管理策略，包括：
1. 上下文管理器自动清理
2. 连接池监控
3. 资源泄漏检测
4. 异常安全的清理
"""

import asyncio
import logging
import weakref
from contextlib import contextmanager, asynccontextmanager
from typing import Optional, Dict, Any, Union
from .client import UniversalSQLAlchemyClient
from .factory import create_mysql_client, create_postgresql_client, create_sqlite_client

logger = logging.getLogger(__name__)


class ConnectionPoolManager:
    """连接池管理器"""
    
    def __init__(self):
        self._active_clients = weakref.WeakSet()
        self._cleanup_callbacks = []
    
    def register_client(self, client: UniversalSQLAlchemyClient):
        """注册客户端"""
        self._active_clients.add(client)
        logger.debug(f"Registered client, total active: {len(self._active_clients)}")
    
    def cleanup_all(self):
        """清理所有活跃的客户端"""
        logger.info(f"Cleaning up {len(self._active_clients)} active clients")
        
        for client in list(self._active_clients):
            try:
                if client.is_connected():
                    client.disconnect()
                    logger.debug(f"Cleaned up client: {id(client)}")
            except Exception as e:
                logger.error(f"Error cleaning up client {id(client)}: {e}")
        
        # 执行清理回调
        for callback in self._cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                logger.error(f"Error in cleanup callback: {e}")
    
    def add_cleanup_callback(self, callback):
        """添加清理回调"""
        self._cleanup_callbacks.append(callback)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        return {
            'active_clients': len(self._active_clients),
            'cleanup_callbacks': len(self._cleanup_callbacks)
        }


# 全局连接池管理器
_pool_manager = ConnectionPoolManager()


@contextmanager
def database_client(client_factory, *args, **kwargs):
    """
    数据库客户端上下文管理器（同步）
    
    使用示例：
        with database_client(create_mysql_client, host="localhost", ...) as client:
            result = client.query({"table": "users"})
    """
    client = None
    try:
        # 创建客户端
        client = client_factory(*args, **kwargs)
        _pool_manager.register_client(client)
        
        # 自动连接
        client.connect()
        logger.debug(f"Created and connected client: {id(client)}")
        
        yield client
        
    except Exception as e:
        logger.error(f"Error in database client context: {e}")
        raise
    finally:
        # 确保清理
        if client:
            try:
                if client.is_connected():
                    client.disconnect()
                    logger.debug(f"Disconnected client: {id(client)}")
            except Exception as e:
                logger.error(f"Error disconnecting client {id(client)}: {e}")


@asynccontextmanager
async def async_database_client(client_factory, *args, **kwargs):
    """
    数据库客户端异步上下文管理器
    
    使用示例：
        async with async_database_client(create_mysql_client, host="localhost", ...) as client:
            result = await client.aquery({"table": "users"})
    """
    client = None
    try:
        # 创建客户端
        client = client_factory(*args, **kwargs)
        _pool_manager.register_client(client)
        
        # 自动连接
        await client.aconnect()
        logger.debug(f"Created and connected async client: {id(client)}")
        
        yield client
        
    except Exception as e:
        logger.error(f"Error in async database client context: {e}")
        raise
    finally:
        # 确保清理
        if client:
            try:
                if client.is_connected():
                    await client.adisconnect()
                    logger.debug(f"Disconnected async client: {id(client)}")
            except Exception as e:
                logger.error(f"Error disconnecting async client {id(client)}: {e}")


class ManagedDatabaseClient:
    """
    托管数据库客户端
    
    提供自动资源管理和监控功能
    """
    
    def __init__(self, client_factory, *args, **kwargs):
        self._client_factory = client_factory
        self._args = args
        self._kwargs = kwargs
        self._client: Optional[UniversalSQLAlchemyClient] = None
        self._connected = False
    
    def __enter__(self):
        """同步上下文管理器入口"""
        self._client = self._client_factory(*self._args, **self._kwargs)
        _pool_manager.register_client(self._client)
        self._client.connect()
        self._connected = True
        logger.debug(f"Managed client connected: {id(self._client)}")
        return self._client
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """同步上下文管理器出口"""
        self._cleanup()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self._client = self._client_factory(*self._args, **self._kwargs)
        _pool_manager.register_client(self._client)
        await self._client.aconnect()
        self._connected = True
        logger.debug(f"Managed async client connected: {id(self._client)}")
        return self._client
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self._acleanup()
    
    def _cleanup(self):
        """同步清理"""
        if self._client and self._connected:
            try:
                self._client.disconnect()
                logger.debug(f"Managed client disconnected: {id(self._client)}")
            except Exception as e:
                logger.error(f"Error in managed client cleanup: {e}")
            finally:
                self._connected = False
    
    async def _acleanup(self):
        """异步清理"""
        if self._client and self._connected:
            try:
                await self._client.adisconnect()
                logger.debug(f"Managed async client disconnected: {id(self._client)}")
            except Exception as e:
                logger.error(f"Error in managed async client cleanup: {e}")
            finally:
                self._connected = False
    
    def __del__(self):
        """析构函数清理"""
        if self._connected:
            logger.warning(f"Managed client {id(self._client)} not properly closed, cleaning up in destructor")
            try:
                self._cleanup()
            except Exception:
                pass  # 忽略析构函数中的错误


def mysql_client(*args, **kwargs):
    """MySQL客户端上下文管理器工厂"""
    return ManagedDatabaseClient(create_mysql_client, *args, **kwargs)


def postgresql_client(*args, **kwargs):
    """PostgreSQL客户端上下文管理器工厂"""
    return ManagedDatabaseClient(create_postgresql_client, *args, **kwargs)


def sqlite_client(*args, **kwargs):
    """SQLite客户端上下文管理器工厂"""
    return ManagedDatabaseClient(create_sqlite_client, *args, **kwargs)


def cleanup_all_connections():
    """清理所有连接"""
    _pool_manager.cleanup_all()


def get_connection_stats():
    """获取连接统计信息"""
    return _pool_manager.get_stats()


def add_cleanup_callback(callback):
    """添加全局清理回调"""
    _pool_manager.add_cleanup_callback(callback)


# 注册程序退出时的清理
import atexit
atexit.register(cleanup_all_connections)

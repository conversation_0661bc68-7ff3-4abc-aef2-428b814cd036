import logging
import hashlib
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class PartitionType(Enum):
    """分区类型枚举"""
    HASH = "hash"
    LIST = "list"
    RANGE = "range"


@dataclass
class PartitionConfig:
    """分区配置"""
    partition_type: PartitionType
    partition_keys: List[str]  # 分区键字段
    partition_count: Optional[int] = None  # Hash分区的分区数量
    partition_values: Optional[Dict[str, List[Any]]] = None  # List分区的值列表
    partition_ranges: Optional[List[Tuple[Any, Any]]] = None  # Range分区的范围


@dataclass
class PartitionInfo:
    """分区信息"""
    table_name: str
    partition_name: str
    partition_type: PartitionType
    partition_keys: List[str]
    partition_expression: str
    is_active: bool = True


class PostgreSQLPartitionManager:
    """
    PostgreSQL分区管理器
    
    负责：
    1. 创建和管理PostgreSQL原生分区表
    2. 自动分区路由和查询优化
    3. 分区元数据管理
    4. 支持Hash、List、Range分区策略
    """
    
    def __init__(self, client):
        """
        初始化分区管理器
        
        Args:
            client: PGVector客户端实例
        """
        self.client = client
        self._partition_cache: Dict[str, List[PartitionInfo]] = {}
    
    def create_hash_partitioned_table(
        self,
        table_name: str,
        columns_definition: str,
        partition_keys: List[str],
        partition_count: int = 128,
        **kwargs
    ) -> bool:
        """
        创建Hash分区表
        
        Args:
            table_name: 表名
            columns_definition: 列定义SQL
            partition_keys: 分区键列表
            partition_count: 分区数量
            **kwargs: 其他参数
            
        Returns:
            是否创建成功
            
        Example:
            >>> manager.create_hash_partitioned_table(
            ...     "dd_embeddings",
            ...     '''
            ...     id BIGSERIAL,
            ...     embedding VECTOR(768) NOT NULL,
            ...     knowledge_id VARCHAR(255) NOT NULL,
            ...     value_id BIGINT NOT NULL,
            ...     field_id INT NOT NULL,
            ...     data_layer VARCHAR(20) NOT NULL,
            ...     create_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            ...     update_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            ...     PRIMARY KEY (knowledge_id, data_layer, field_id, id)
            ...     ''',
            ...     ["knowledge_id", "data_layer", "field_id"],
            ...     128
            ... )
        """
        try:
            # 构建分区键表达式
            partition_expression = ", ".join(partition_keys)
            
            # 创建主表
            create_table_sql = f"""
                CREATE TABLE {table_name} (
                    {columns_definition}
                ) PARTITION BY HASH ({partition_expression})
            """
            
            self.client.execute_sql(create_table_sql)
            logger.info(f"Created hash partitioned table: {table_name}")
            
            # 创建分区
            for i in range(partition_count):
                partition_name = f"{table_name}_part_{i:03d}"
                create_partition_sql = f"""
                    CREATE TABLE {partition_name} PARTITION OF {table_name}
                    FOR VALUES WITH (modulus {partition_count}, remainder {i})
                """
                self.client.execute_sql(create_partition_sql)
            
            logger.info(f"Created {partition_count} hash partitions for {table_name}")
            
            # 更新缓存
            self._update_partition_cache(table_name)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create hash partitioned table {table_name}: {e}")
            return False
    
    def create_list_partitioned_table(
        self,
        table_name: str,
        columns_definition: str,
        partition_key: str,
        partition_values: Dict[str, List[Any]],
        **kwargs
    ) -> bool:
        """
        创建List分区表
        
        Args:
            table_name: 表名
            columns_definition: 列定义SQL
            partition_key: 分区键
            partition_values: 分区值映射 {partition_name: [values]}
            
        Returns:
            是否创建成功
        """
        try:
            # 创建主表
            create_table_sql = f"""
                CREATE TABLE {table_name} (
                    {columns_definition}
                ) PARTITION BY LIST ({partition_key})
            """
            
            self.client.execute_sql(create_table_sql)
            logger.info(f"Created list partitioned table: {table_name}")
            
            # 创建分区
            for partition_name, values in partition_values.items():
                values_str = ", ".join([f"'{v}'" if isinstance(v, str) else str(v) for v in values])
                create_partition_sql = f"""
                    CREATE TABLE {table_name}_{partition_name} PARTITION OF {table_name}
                    FOR VALUES IN ({values_str})
                """
                self.client.execute_sql(create_partition_sql)
            
            logger.info(f"Created {len(partition_values)} list partitions for {table_name}")
            
            # 更新缓存
            self._update_partition_cache(table_name)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create list partitioned table {table_name}: {e}")
            return False
    
    def create_range_partitioned_table(
        self,
        table_name: str,
        columns_definition: str,
        partition_key: str,
        partition_ranges: List[Tuple[Any, Any]],
        **kwargs
    ) -> bool:
        """
        创建Range分区表
        
        Args:
            table_name: 表名
            columns_definition: 列定义SQL
            partition_key: 分区键
            partition_ranges: 分区范围列表 [(start, end), ...]
            
        Returns:
            是否创建成功
        """
        try:
            # 创建主表
            create_table_sql = f"""
                CREATE TABLE {table_name} (
                    {columns_definition}
                ) PARTITION BY RANGE ({partition_key})
            """
            
            self.client.execute_sql(create_table_sql)
            logger.info(f"Created range partitioned table: {table_name}")
            
            # 创建分区
            for i, (start, end) in enumerate(partition_ranges):
                partition_name = f"{table_name}_part_{i:03d}"
                create_partition_sql = f"""
                    CREATE TABLE {partition_name} PARTITION OF {table_name}
                    FOR VALUES FROM ('{start}') TO ('{end}')
                """
                self.client.execute_sql(create_partition_sql)
            
            logger.info(f"Created {len(partition_ranges)} range partitions for {table_name}")
            
            # 更新缓存
            self._update_partition_cache(table_name)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create range partitioned table {table_name}: {e}")
            return False
    
    def get_partition_for_values(
        self,
        table_name: str,
        values: Dict[str, Any]
    ) -> Optional[str]:
        """
        根据值获取对应的分区名
        
        Args:
            table_name: 表名
            values: 分区键值
            
        Returns:
            分区名，如果无法确定则返回None
        """
        try:
            partitions = self.get_table_partitions(table_name)
            if not partitions:
                return None
            
            # 对于Hash分区，计算hash值
            first_partition = partitions[0]
            if first_partition.partition_type == PartitionType.HASH:
                return self._calculate_hash_partition(table_name, values, first_partition.partition_keys)
            
            # 对于List和Range分区，需要查询PostgreSQL系统表
            # 这里简化处理，实际应用中可以通过查询pg_partitions等系统表来确定
            return None
            
        except Exception as e:
            logger.warning(f"Failed to determine partition for {table_name}: {e}")
            return None
    
    def _calculate_hash_partition(
        self,
        table_name: str,
        values: Dict[str, Any],
        partition_keys: List[str]
    ) -> str:
        """
        计算Hash分区名
        
        Args:
            table_name: 表名
            values: 分区键值
            partition_keys: 分区键列表
            
        Returns:
            分区名
        """
        # 构建hash输入字符串
        hash_input = ""
        for key in partition_keys:
            if key in values:
                hash_input += str(values[key])
        
        # 计算hash值
        hash_value = int(hashlib.md5(hash_input.encode()).hexdigest(), 16)
        
        # 获取分区数量
        partitions = self.get_table_partitions(table_name)
        partition_count = len(partitions)
        
        # 计算分区索引
        partition_index = hash_value % partition_count
        
        return f"{table_name}_part_{partition_index:03d}"
    
    def get_table_partitions(self, table_name: str) -> List[PartitionInfo]:
        """
        获取表的所有分区信息

        Args:
            table_name: 表名

        Returns:
            分区信息列表
        """
        if table_name in self._partition_cache:
            return self._partition_cache[table_name]

        return self._update_partition_cache(table_name)

    def _update_partition_cache(self, table_name: str) -> List[PartitionInfo]:
        """
        更新分区缓存 - 使用PostgreSQL系统表

        Args:
            table_name: 表名

        Returns:
            分区信息列表
        """
        try:
            # 查询PostgreSQL系统表获取分区信息
            sql = """
                SELECT
                    c.relname as partition_name,
                    parent.relname as parent_table,
                    pt.partstrat as partition_strategy,
                    pg_get_expr(pt.partexprs, pt.partrelid) as partition_expression,
                    pg_get_expr(c.relpartbound, c.oid) as partition_bound
                FROM pg_class c
                JOIN pg_inherits i ON c.oid = i.inhrelid
                JOIN pg_class parent ON i.inhparent = parent.oid
                LEFT JOIN pg_partitioned_table pt ON parent.oid = pt.partrelid
                WHERE parent.relname = %s
                AND c.relkind = 'r'
                ORDER BY c.relname
            """

            results = self.client.execute_query(sql, [table_name])

            partitions = []
            for row in results:
                # 解析分区策略
                strategy_map = {'h': PartitionType.HASH, 'l': PartitionType.LIST, 'r': PartitionType.RANGE}
                partition_type = strategy_map.get(row['partition_strategy'], PartitionType.HASH)

                partition_info = PartitionInfo(
                    table_name=row['parent_table'],
                    partition_name=row['partition_name'],
                    partition_type=partition_type,
                    partition_keys=self._extract_partition_keys_from_parent(table_name),
                    partition_expression=row['partition_expression'] or '',
                    is_active=True
                )
                partitions.append(partition_info)

            self._partition_cache[table_name] = partitions
            return partitions

        except Exception as e:
            logger.warning(f"Failed to update partition cache for {table_name}: {e}")
            return []

    def _extract_partition_keys_from_parent(self, table_name: str) -> List[str]:
        """
        从父表获取分区键信息

        Args:
            table_name: 表名

        Returns:
            分区键列表
        """
        try:
            sql = """
                SELECT
                    a.attname as column_name
                FROM pg_partitioned_table pt
                JOIN pg_class c ON pt.partrelid = c.oid
                JOIN pg_attribute a ON a.attrelid = c.oid
                WHERE c.relname = %s
                AND a.attnum = ANY(pt.partattrs)
                ORDER BY array_position(pt.partattrs, a.attnum)
            """

            results = self.client.execute_query(sql, [table_name])
            return [row['column_name'] for row in results]

        except Exception as e:
            logger.warning(f"Failed to extract partition keys for {table_name}: {e}")
            return []
    
    def _detect_partition_type(self, expression: str) -> PartitionType:
        """检测分区类型"""
        if "HASH" in expression.upper():
            return PartitionType.HASH
        elif "LIST" in expression.upper():
            return PartitionType.LIST
        elif "RANGE" in expression.upper():
            return PartitionType.RANGE
        else:
            return PartitionType.HASH  # 默认
    
    def _extract_partition_keys(self, expression: str) -> List[str]:
        """从分区表达式中提取分区键"""
        # 简化实现，实际应用中需要更复杂的解析
        import re
        keys = re.findall(r'\b\w+\b', expression)
        return [key for key in keys if key.upper() not in ['HASH', 'LIST', 'RANGE', 'BY']]
    
    def optimize_query_for_partition(
        self,
        sql: str,
        partition_values: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        为分区表优化查询
        
        Args:
            sql: 原始SQL
            partition_values: 分区键值，用于分区裁剪
            
        Returns:
            优化后的SQL
        """
        # PostgreSQL会自动进行分区裁剪，这里主要是添加分区键条件
        if partition_values:
            where_conditions = []
            for key, value in partition_values.items():
                if isinstance(value, str):
                    where_conditions.append(f"{key} = '{value}'")
                else:
                    where_conditions.append(f"{key} = {value}")
            
            if where_conditions:
                if "WHERE" in sql.upper():
                    sql += " AND " + " AND ".join(where_conditions)
                else:
                    sql += " WHERE " + " AND ".join(where_conditions)
        
        return sql
    
    def explain_partition_pruning(
        self,
        table_name: str,
        partition_keys: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        解释分区裁剪效果

        Args:
            table_name: 表名
            partition_keys: 分区键值

        Returns:
            分区裁剪信息
        """
        try:
            # 构建带分区键的查询
            where_conditions = []
            params = []
            for key, value in partition_keys.items():
                where_conditions.append(f"{key} = %s")
                params.append(value)

            where_clause = " AND ".join(where_conditions)
            sql = f"EXPLAIN (FORMAT JSON) SELECT * FROM {table_name} WHERE {where_clause}"

            results = self.client.execute_query(sql, params)

            if results:
                explain_result = results[0]
                plan = explain_result.get('QUERY PLAN', [{}])[0]

                # 提取分区信息
                partition_info = {
                    'table_name': table_name,
                    'partition_keys': partition_keys,
                    'total_cost': plan.get('Total Cost', 0),
                    'startup_cost': plan.get('Startup Cost', 0),
                    'plan_rows': plan.get('Plan Rows', 0),
                    'partitions_pruned': self._extract_partition_info(plan),
                    'execution_plan': plan
                }

                return partition_info

            return {}

        except Exception as e:
            logger.warning(f"Failed to explain partition pruning for {table_name}: {e}")
            return {}

    def _extract_partition_info(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """从执行计划中提取分区信息"""
        partition_info = {
            'partitions_scanned': [],
            'partitions_pruned': [],
            'pruning_effective': False
        }

        # 递归查找分区相关信息
        def extract_from_node(node):
            if isinstance(node, dict):
                node_type = node.get('Node Type', '')

                if 'Append' in node_type:
                    # Append节点包含分区信息
                    plans = node.get('Plans', [])
                    for plan in plans:
                        relation_name = plan.get('Relation Name', '')
                        if relation_name:
                            partition_info['partitions_scanned'].append(relation_name)

                # 递归处理子节点
                for key, value in node.items():
                    if isinstance(value, (list, dict)):
                        extract_from_node(value)

            elif isinstance(node, list):
                for item in node:
                    extract_from_node(item)

        extract_from_node(plan)

        # 判断分区裁剪是否有效
        total_partitions = len(self.get_table_partitions(plan.get('Relation Name', '')))
        scanned_partitions = len(partition_info['partitions_scanned'])

        if total_partitions > 0 and scanned_partitions < total_partitions:
            partition_info['pruning_effective'] = True
            partition_info['partitions_pruned'] = [
                f"partition_{i}" for i in range(total_partitions)
                if f"partition_{i}" not in partition_info['partitions_scanned']
            ]

        return partition_info

    def clear_cache(self):
        """清理分区缓存"""
        self._partition_cache.clear()

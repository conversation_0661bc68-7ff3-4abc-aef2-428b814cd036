"""
实体类和批量操作示例

展示了Doc模块的高效设计：
1. 实体类封装 - <PERSON>k, ChunkInfo, DocEmbedding实体的使用
2. 批量向量生成 - 一次性对多个文本进行向量化
3. 批量数据库操作 - 减少数据库交互次数
4. 业务聚合 - 多表联合操作保证数据一致性

重点演示：
- DocEmbedding实体与向量数据库表的映射
- 批量embedding生成（一次调用处理多个文本）
- 批量数据库插入（减少网络开销）
- 实体验证和错误处理
"""

import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

from modules.knowledge.doc.operations.document_ops import DocumentOperation
from modules.knowledge.doc.operations.chunk_ops import ChunkOperation
from modules.knowledge.doc.entities.api_models import DocumentCreate
from modules.knowledge.doc.entities.base_models import (
    DocumentStatus, ParseType, DocumentFormat, Chunk, ChunkInfo, DocEmbedding
)
from uuid import uuid4

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EntityBatchWorkflow:
    """实体类和批量操作演示"""
    
    def __init__(self):
        """初始化操作类"""
        self.doc_ops = DocumentOperation()
        self.chunk_ops = ChunkOperation()
        self.knowledge_id = "entity-batch-demo"
    
    async def scenario_1_entity_creation_demo(self):
        """
        场景1: 实体类创建和使用演示
        
        展示：
        - Chunk实体的创建和操作
        - ChunkInfo实体的关联管理
        - DocEmbedding实体的数据验证
        """
        print("\n" + "="*80)
        print("🏗️  场景1: 实体类创建和使用演示")
        print("="*80)
        
        try:
            # 1. 创建文档
            print("\n📄 步骤1: 创建测试文档")
            document_data = DocumentCreate(
                knowledge_id=self.knowledge_id,
                doc_name="实体类测试文档.md",
                doc_type=None,
                author="开发团队",
                vector_similarity_weight=0.8,
                similarity_threshold=0.7,
                parse_type=ParseType.TXT,
                status=DocumentStatus.PENDING,
                parse_end_time=None,
                parse_message=None,
                doc_format=DocumentFormat.MD,
                location="/test/entity-demo.md",
                metadata='{"type": "entity_demo"}',
                created_time=None,
                updated_time=None,
                is_active=True
            )
            
            doc_id = await self.doc_ops.create_document(document_data)
            print(f"✅ 文档创建成功: {doc_id}")
            
            # 2. 演示Chunk实体创建
            print("\n🧩 步骤2: 创建Chunk实体")
            
            # 手动创建Chunk实体
            chunk_entity = Chunk(
                chunk_id=str(uuid4()),
                doc_id=doc_id,
                chapter_layer="第一章.实体演示",
                parent_id=None,
                created_time=datetime.now(),
                updated_time=None,
                is_active=True,
                chunk_infos=[]  # 初始为空，后续添加
            )
            
            print(f"   📦 Chunk实体创建: {chunk_entity.chunk_id}")
            print(f"   📋 章节层级: {chunk_entity.chapter_layer}")
            print(f"   🔗 父分块ID: {chunk_entity.parent_id}")
            
            # 3. 演示ChunkInfo实体创建和关联
            print("\n📝 步骤3: 创建ChunkInfo实体")
            
            chunk_info_entities = []
            info_data_list = [
                {
                    "info_type": "content",
                    "info_value": "实体类是面向对象编程的核心概念，它封装了数据和行为，提供了清晰的业务语义和数据操作接口。"
                },
                {
                    "info_type": "summary", 
                    "info_value": "实体类封装数据和行为，提供清晰的业务语义"
                },
                {
                    "info_type": "keyword",
                    "info_value": "实体类,面向对象,封装,数据操作,业务语义"
                },
                {
                    "info_type": "title",
                    "info_value": "实体类设计原理"
                }
            ]
            
            for info_data in info_data_list:
                chunk_info = ChunkInfo(
                    chunk_info_id=str(uuid4()),
                    chunk_id=chunk_entity.chunk_id,
                    info_type=info_data["info_type"],
                    info_value=info_data["info_value"],
                    created_time=datetime.now(),
                    updated_time=None,
                    is_active=True
                )
                chunk_info_entities.append(chunk_info)
                
                print(f"   📝 {info_data['info_type']}: {chunk_info.chunk_info_id}")
                print(f"      内容预览: {info_data['info_value'][:50]}...")
            
            # 关联到Chunk实体
            chunk_entity.chunk_infos = chunk_info_entities
            
            # 4. 演示实体类的便捷方法
            print("\n🔍 步骤4: 实体类便捷方法演示")
            
            content = chunk_entity.get_content()
            summary = chunk_entity.get_summary()
            keywords = chunk_entity.get_keywords()
            
            print(f"   📖 内容: {content[:60] if content else 'None'}...")
            print(f"   📋 摘要: {summary}")
            print(f"   🏷️  关键词: {keywords}")
            
            # 获取特定类型的信息
            title_info = chunk_entity.get_info_by_type("title")
            if title_info:
                print(f"   📑 标题: {title_info.info_value}")
            
            # 5. 演示实体序列化
            print("\n💾 步骤5: 实体序列化演示")
            
            chunk_dict = chunk_entity.to_dict()
            print(f"   📦 Chunk字典键: {list(chunk_dict.keys())}")
            print(f"   📝 包含的信息数: {len(chunk_dict.get('chunk_infos', []))}")
            
            # 从字典重建实体
            rebuilt_chunk = Chunk.from_dict(chunk_dict)
            print(f"   🔄 重建Chunk ID: {rebuilt_chunk.chunk_id}")
            print(f"   🔄 重建信息数: {len(rebuilt_chunk.chunk_infos) if rebuilt_chunk.chunk_infos else 0}")
            
            print(f"\n✅ 实体类创建和使用演示完成！")
            return doc_id, chunk_entity
            
        except Exception as e:
            logger.error(f"实体类演示失败: {e}")
            raise
    
    async def scenario_2_batch_vector_operations(self, doc_id: str, chunk_entity: Chunk):
        """
        场景2: 批量向量操作演示
        
        展示：
        - 批量embedding生成（一次调用处理多个文本）
        - DocEmbedding实体创建和验证
        - 批量向量数据库插入
        """
        print("\n" + "="*80)
        print("🚀 场景2: 批量向量操作演示")
        print("="*80)
        
        try:
            # 1. 准备向量化数据
            print("\n📊 步骤1: 准备批量向量化数据")
            
            # 收集需要向量化的ChunkInfo
            vectorizable_infos = []
            if chunk_entity.chunk_infos:
                for chunk_info in chunk_entity.chunk_infos:
                    if chunk_info.info_type in ["content", "summary", "title"]:
                        vectorizable_infos.append(chunk_info)
            
            print(f"   📝 需要向量化的信息: {len(vectorizable_infos)} 条")
            for info in vectorizable_infos:
                print(f"      - {info.info_type}: {info.info_value[:50]}...")
            
            # 2. 批量生成向量
            print("\n⚡ 步骤2: 批量向量生成")
            
            vector_entities = await self.chunk_ops._batch_generate_vectors(
                self.knowledge_id, doc_id, vectorizable_infos
            )
            
            print(f"   🎯 向量生成结果: {len(vector_entities)}/{len(vectorizable_infos)}")
            
            # 3. 演示DocEmbedding实体
            print("\n🧮 步骤3: DocEmbedding实体演示")
            
            for i, vector_entity in enumerate(vector_entities, 1):
                print(f"\n   📊 向量实体 {i}:")
                print(f"      ID: {vector_entity.id}")
                print(f"      Knowledge ID: {vector_entity.knowledge_id}")
                print(f"      Doc ID: {vector_entity.doc_id[:16]}...")
                print(f"      Chunk ID: {vector_entity.chunk_id[:16]}...")
                print(f"      Chunk Info ID: {vector_entity.chunk_info_id[:16]}...")
                print(f"      Info Type: {vector_entity.info_type}")
                print(f"      Embedding维度: {len(vector_entity.embedding) if vector_entity.embedding else 0}")
                print(f"      验证结果: {'✅ 通过' if vector_entity.validate() else '❌ 失败'}")
                
                # 演示序列化
                vector_dict = vector_entity.to_dict()
                print(f"      字典键: {list(vector_dict.keys())}")
                
                # 从字典重建
                rebuilt_vector = DocEmbedding.from_dict(vector_dict)
                print(f"      重建验证: {'✅ 成功' if rebuilt_vector.validate() else '❌ 失败'}")
            
            # 4. 批量插入向量
            print("\n💾 步骤4: 批量向量插入")
            
            if vector_entities:
                success_count = await self.chunk_ops._batch_insert_vectors(vector_entities)
                print(f"   📈 插入结果: {success_count}/{len(vector_entities)} 成功")
            
            # 5. 验证插入结果
            print("\n🔍 步骤5: 验证向量插入结果")
            
            for vector_entity in vector_entities[:2]:  # 只验证前两个
                try:
                    vdb_client = await self.chunk_ops._ensure_vdb_client()
                    vectors = await vdb_client.aselect(
                        table=self.chunk_ops.chunk_vdb_table,
                        condition={"chunk_info_id": vector_entity.chunk_info_id},
                        limit=1
                    )
                    
                    status = "✅ 存在" if vectors else "❌ 不存在"
                    print(f"   向量验证 ({vector_entity.info_type}): {status}")
                    
                except Exception as e:
                    print(f"   向量验证失败: {e}")
            
            print(f"\n✅ 批量向量操作演示完成！")
            return vector_entities
            
        except Exception as e:
            logger.error(f"批量向量操作演示失败: {e}")
            raise
    
    async def scenario_3_performance_comparison(self):
        """
        场景3: 性能对比演示
        
        展示：
        - 批量操作 vs 单条操作的性能差异
        - 实体类操作的便利性
        - 错误处理和数据验证
        """
        print("\n" + "="*80)
        print("⚡ 场景3: 性能对比演示")
        print("="*80)
        
        try:
            # 创建测试文档
            print("\n📄 创建性能测试文档")
            document_data = DocumentCreate(
                knowledge_id=self.knowledge_id,
                doc_name="性能测试文档.md",
                doc_type=None,
                author="性能团队",
                vector_similarity_weight=0.8,
                similarity_threshold=0.7,
                parse_type=ParseType.TXT,
                status=DocumentStatus.PENDING,
                parse_end_time=None,
                parse_message=None,
                doc_format=DocumentFormat.MD,
                location="/test/performance-test.md",
                metadata='{"type": "performance_test"}',
                created_time=None,
                updated_time=None,
                is_active=True
            )
            
            perf_doc_id = await self.doc_ops.create_document(document_data)
            print(f"✅ 性能测试文档创建成功: {perf_doc_id}")
            
            # 准备批量测试数据
            print("\n📊 准备批量测试数据")
            test_chunks_data = []
            
            for i in range(5):  # 创建5个分块用于测试
                chunk_data = {
                    "chapter_layer": f"第{i+1}章.性能测试章节",
                    "parent_id": None,
                    "chunk_infos": [
                        {
                            "info_type": "content",
                            "info_value": f"这是第{i+1}个测试分块的内容，用于演示批量处理的性能优势。批量操作可以显著减少数据库交互次数，提高处理效率。"
                        },
                        {
                            "info_type": "summary",
                            "info_value": f"第{i+1}个测试分块的摘要信息"
                        },
                        {
                            "info_type": "title",
                            "info_value": f"性能测试分块{i+1}"
                        }
                    ]
                }
                test_chunks_data.append(chunk_data)
            
            print(f"   📦 准备了 {len(test_chunks_data)} 个测试分块")
            
            # 批量创建分块
            print("\n🚀 批量创建分块（带向量化）")
            batch_start_time = datetime.now()
            
            created_chunk_ids = []
            for chunk_data in test_chunks_data:
                chunk_id = await self.chunk_ops.create_chunk_with_info_and_vector(
                    knowledge_id=self.knowledge_id,
                    doc_id=perf_doc_id,
                    chapter_layer=chunk_data["chapter_layer"],
                    parent_id=chunk_data["parent_id"],
                    chunk_infos=chunk_data["chunk_infos"]
                )
                created_chunk_ids.append(chunk_id)
            
            batch_end_time = datetime.now()
            batch_duration = (batch_end_time - batch_start_time).total_seconds()
            
            print(f"   ⏱️  批量创建耗时: {batch_duration:.3f} 秒")
            print(f"   📊 创建的分块数: {len(created_chunk_ids)}")
            print(f"   📈 平均每个分块: {batch_duration/len(created_chunk_ids):.3f} 秒")
            
            # 验证批量创建结果
            print("\n🔍 验证批量创建结果")
            
            total_chunk_infos = 0
            total_vectors = 0
            
            for chunk_id in created_chunk_ids:
                chunk_entity = await self.chunk_ops.get_chunk_entity(chunk_id)
                if chunk_entity and chunk_entity.chunk_infos:
                    total_chunk_infos += len(chunk_entity.chunk_infos)
                    
                    # 统计向量数量
                    for chunk_info in chunk_entity.chunk_infos:
                        if chunk_info.info_type in ["content", "summary", "title"]:
                            total_vectors += 1
            
            print(f"   📝 总ChunkInfo数: {total_chunk_infos}")
            print(f"   🧮 总向量数: {total_vectors}")
            
            # 演示文档级别的批量向量化
            print("\n📊 文档级别批量向量化测试")
            
            batch_vector_start = datetime.now()
            batch_result = await self.chunk_ops.batch_generate_vectors_for_document(
                knowledge_id=self.knowledge_id,
                doc_id=perf_doc_id,
                info_types=["content", "summary", "title"]
            )
            batch_vector_end = datetime.now()
            batch_vector_duration = (batch_vector_end - batch_vector_start).total_seconds()
            
            print(f"   ⏱️  批量向量化耗时: {batch_vector_duration:.3f} 秒")
            print(f"   📊 处理结果:")
            print(f"      总信息数: {batch_result['total_infos']}")
            print(f"      成功数: {batch_result['success_count']}")
            print(f"      失败数: {batch_result['failed_count']}")
            print(f"      向量数: {batch_result['vector_count']}")
            
            # 性能总结
            print("\n📈 性能总结")
            print(f"   🚀 批量分块创建: {len(created_chunk_ids)} 个分块, {batch_duration:.3f} 秒")
            print(f"   ⚡ 批量向量处理: {batch_result['total_infos']} 条信息, {batch_vector_duration:.3f} 秒")
            print(f"   💾 数据验证: 实体类提供自动验证和错误处理")
            print(f"   🎯 效率提升: 批量操作减少了网络开销和数据库交互次数")
            
            print(f"\n✅ 性能对比演示完成！")
            
        except Exception as e:
            logger.error(f"性能对比演示失败: {e}")
            raise


async def main():
    """主函数 - 运行实体类和批量操作演示"""
    print("🌟 Knowledge Doc Module - 实体类和批量操作演示")
    print("=" * 80)
    print("本演示重点展示：")
    print("1. 🏗️  实体类封装 - Chunk, ChunkInfo, DocEmbedding实体的使用")
    print("2. ⚡ 批量向量生成 - 一次性对多个文本进行向量化")
    print("3. 💾 批量数据库操作 - 减少数据库交互次数")
    print("4. 📊 性能对比 - 批量操作 vs 单条操作的效率差异")
    print("=" * 80)
    
    workflow = EntityBatchWorkflow()
    
    try:
        # 场景1: 实体类创建和使用演示
        doc_id, chunk_entity = await workflow.scenario_1_entity_creation_demo()
        
        # 场景2: 批量向量操作演示
        vector_entities = await workflow.scenario_2_batch_vector_operations(doc_id, chunk_entity)
        
        # 场景3: 性能对比演示
        await workflow.scenario_3_performance_comparison()
        
        print("\n" + "🎉" * 30)
        print("🎉 实体类和批量操作演示完成！")
        print("🎉" * 30)
        
        print("\n✅ 核心技术验证总结:")
        print("   1. ✅ 实体类封装 - 提供类型安全和业务语义")
        print("   2. ✅ 批量向量生成 - 一次embedding调用处理多个文本")
        print("   3. ✅ 批量数据库操作 - 显著提升插入性能")
        print("   4. ✅ 数据验证 - 实体类提供自动验证和错误处理")
        print("   5. ✅ 序列化支持 - to_dict/from_dict方法支持数据转换")
        print("   6. ✅ 便捷访问 - 提供get_content/get_summary等业务方法")
        
        print("\n💡 性能优化效果:")
        print("   • 向量生成效率 - 批量调用减少网络延迟")
        print("   • 数据库效率 - 批量插入减少连接开销")
        print("   • 内存使用 - 实体类提供结构化的数据管理")
        print("   • 错误处理 - 实体验证提前发现数据问题")
        print("   • 代码质量 - 类型安全和业务语义清晰")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1) 
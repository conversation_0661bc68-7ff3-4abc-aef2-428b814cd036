"""
任务存储抽象接口
支持内存缓存和Redis两种实现，接口保持一致
"""

import json
import threading
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime
from utils.common.logger import logger


class TaskStorage(ABC):
    """任务存储抽象基类"""
    
    @abstractmethod
    async def create_task(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """创建新任务"""
        pass
    
    @abstractmethod
    async def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        pass
    
    @abstractmethod
    async def update_task(self, task_id: str, updates: Dict[str, Any]) -> bool:
        """更新任务信息"""
        pass
    
    @abstractmethod
    async def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        pass
    
    @abstractmethod
    async def add_task_result(self, task_id: str, result: Dict[str, Any]) -> bool:
        """添加任务结果"""
        pass


class MemoryTaskStorage(TaskStorage):
    """内存任务存储实现"""
    
    def __init__(self):
        self._cache = {}
        self._lock = threading.Lock()
        logger.info("初始化内存任务存储")
    
    async def create_task(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """创建新任务"""
        try:
            with self._lock:
                # 添加默认字段
                task_data.update({
                    'task_id': task_id,
                    'create_time': datetime.now().isoformat(),
                    'update_time': datetime.now().isoformat(),
                    'results': task_data.get('results', [])
                })
                self._cache[task_id] = task_data
                logger.info(f"创建内存任务: {task_id}")
                return True
        except Exception as e:
            logger.error(f"创建任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    async def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        try:
            with self._lock:
                task = self._cache.get(task_id)
                if task:
                    # 返回副本，避免外部修改
                    return task.copy()
                return None
        except Exception as e:
            logger.error(f"获取任务失败: {task_id}, 错误: {str(e)}")
            return None
    
    async def update_task(self, task_id: str, updates: Dict[str, Any]) -> bool:
        """更新任务信息"""
        try:
            with self._lock:
                if task_id not in self._cache:
                    logger.warning(f"任务不存在: {task_id}")
                    return False
                
                # 更新任务数据
                self._cache[task_id].update(updates)
                self._cache[task_id]['update_time'] = datetime.now().isoformat()
                
                # 自动计算进度
                task = self._cache[task_id]
                if 'processed_files' in updates and task.get('total_files', 0) > 0:
                    progress = (task['processed_files'] / task['total_files']) * 100
                    task['progress'] = round(progress, 2)
                
                # 如果任务完成，设置结束时间
                if updates.get('status') in ['completed', 'failed']:
                    task['end_time'] = datetime.now().isoformat()
                
                logger.debug(f"更新内存任务: {task_id}")
                return True
        except Exception as e:
            logger.error(f"更新任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    async def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        try:
            with self._lock:
                if task_id in self._cache:
                    del self._cache[task_id]
                    logger.info(f"删除内存任务: {task_id}")
                    return True
                return False
        except Exception as e:
            logger.error(f"删除任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    async def add_task_result(self, task_id: str, result: Dict[str, Any]) -> bool:
        """添加任务结果"""
        try:
            with self._lock:
                if task_id not in self._cache:
                    logger.warning(f"任务不存在: {task_id}")
                    return False
                
                self._cache[task_id]['results'].append(result)
                self._cache[task_id]['update_time'] = datetime.now().isoformat()
                logger.debug(f"添加任务结果: {task_id}")
                return True
        except Exception as e:
            logger.error(f"添加任务结果失败: {task_id}, 错误: {str(e)}")
            return False
    
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务（同步方法，可在后台调用）"""
        try:
            with self._lock:
                current_time = datetime.now()
                to_remove = []
                
                for task_id, task in self._cache.items():
                    create_time = datetime.fromisoformat(task['create_time'])
                    if (current_time - create_time).total_seconds() > max_age_hours * 3600:
                        to_remove.append(task_id)
                
                for task_id in to_remove:
                    del self._cache[task_id]
                    logger.info(f"清理过期任务: {task_id}")
                
                if to_remove:
                    logger.info(f"清理了 {len(to_remove)} 个过期任务")
        except Exception as e:
            logger.error(f"清理任务失败: {str(e)}")


class RedisTaskStorage(TaskStorage):
    """Redis任务存储实现（预留接口）"""
    
    def __init__(self, redis_client=None, key_prefix="task:"):
        self.redis_client = redis_client
        self.key_prefix = key_prefix
        logger.info("初始化Redis任务存储（未实现）")
    
    async def create_task(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """创建新任务"""
        # TODO: 实现Redis版本
        raise NotImplementedError("Redis任务存储尚未实现")
    
    async def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        # TODO: 实现Redis版本
        raise NotImplementedError("Redis任务存储尚未实现")
    
    async def update_task(self, task_id: str, updates: Dict[str, Any]) -> bool:
        """更新任务信息"""
        # TODO: 实现Redis版本
        raise NotImplementedError("Redis任务存储尚未实现")
    
    async def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        # TODO: 实现Redis版本
        raise NotImplementedError("Redis任务存储尚未实现")
    
    async def add_task_result(self, task_id: str, result: Dict[str, Any]) -> bool:
        """添加任务结果"""
        # TODO: 实现Redis版本
        raise NotImplementedError("Redis任务存储尚未实现")


# 全局任务存储实例
# 将来可以通过配置切换到Redis实现
_task_storage: Optional[TaskStorage] = None

def get_task_storage() -> TaskStorage:
    """获取任务存储实例"""
    global _task_storage
    if _task_storage is None:
        # 现在使用内存存储，将来可以根据配置选择Redis
        _task_storage = MemoryTaskStorage()
    return _task_storage

def set_task_storage(storage: TaskStorage):
    """设置任务存储实例（用于切换到Redis）"""
    global _task_storage
    _task_storage = storage
    logger.info(f"切换任务存储到: {type(storage).__name__}")


# 便捷函数
async def create_upload_task(task_id: str, template_type: str, total_files: int, knowledge_id: str) -> bool:
    """创建上传任务"""
    task_data = {
        'template_type': template_type,
        'knowledge_id': knowledge_id,
        'status': 'pending',
        'progress': 0.0,
        'total_files': total_files,
        'processed_files': 0,
        'success_files': 0,
        'failed_files': 0,
        'current_file': None,
        'error_message': None
    }
    storage = get_task_storage()
    return await storage.create_task(task_id, task_data)

async def update_upload_progress(task_id: str, **updates) -> bool:
    """更新上传进度"""
    storage = get_task_storage()
    return await storage.update_task(task_id, updates)

async def get_upload_task(task_id: str) -> Optional[Dict[str, Any]]:
    """获取上传任务信息"""
    storage = get_task_storage()
    return await storage.get_task(task_id)

async def add_upload_result(task_id: str, result: Dict[str, Any]) -> bool:
    """添加上传结果"""
    storage = get_task_storage()
    return await storage.add_task_result(task_id, result)

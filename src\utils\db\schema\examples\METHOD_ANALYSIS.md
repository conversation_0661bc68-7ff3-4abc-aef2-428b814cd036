# SchemaGenerator 核心方法详细分析

## 🔍 两个核心方法的实现机制

### 1. `.split(token_size=7500)` 方法

#### 功能概述
```python
def split(self, token_size: int = 7500) -> List[str]:
    return self._split_with_complete_context(token_size)
```

#### 具体工作流程

**第一步：数据验证**
```python
if not self._tables_info or not self._columns_info:
    return ["【Schema】\n# No data found"]
```

**第二步：智能分组** - 核心逻辑
```python
column_groups = self._group_columns_by_token_limit(token_size)
```

**分组算法详解：**
1. **按列级别分组**：以列为最小单位进行分割，而不是按表
2. **智能大小估算**：
   ```python
   # 估算单列的大小
   column_schema = self._build_single_column_schema(col)
   column_size = len(column_schema)
   
   # 加上基础上下文大小（数据库信息、表头等）
   base_context_size = 200
   estimated_total_size = current_size + column_size + base_context_size
   ```

3. **动态分组策略**：
   - 如果添加当前列会超过token限制 → 开始新组
   - 否则 → 添加到当前组
   - 确保每组都不为空

**第三步：构建完整上下文**
```python
for group in column_groups:
    # 获取当前组涉及的表
    table_ids = list(set(col.get('table_id') for col in group))
    involved_tables = [t for t in self._tables_info if t.get('table_id') in table_ids]
    
    # 为当前组构建完整的上下文
    schema = self._build_complete_context_for_columns(group, involved_tables)
    schemas.append(schema)
```

#### 关键特性
- ✅ **智能分割**：不是简单的字符串分割，而是基于列的语义分割
- ✅ **完整上下文**：每个片段都包含完整的数据库信息、表信息和列信息
- ✅ **跨表支持**：一个片段可能包含多个表的列
- ✅ **大小控制**：精确控制每个片段的token大小

---

### 2. `.get_prompt_list_with_mappings(token_size=7500)` 方法

#### 功能概述
```python
def get_prompt_list_with_mappings(self, token_size: int = 7500) -> List[Dict[str, Any]]:
    return self._split_with_mappings(token_size)
```

#### 具体工作流程

**第一步：相同的分组逻辑**
```python
# 使用与split()相同的分组算法
column_groups = self._group_columns_by_token_limit(token_size)
```

**第二步：构建prompt + 生成映射**
```python
for group in column_groups:
    # 1. 构建prompt（与split()相同）
    prompt = self._build_complete_context_for_columns(group, involved_tables)
    
    # 2. 生成映射信息（额外功能）
    db_to_tables, table_to_columns = self._generate_mappings_for_group(group, involved_tables)
    
    # 3. 组合结果
    result.append({
        "prompt": prompt,
        "db_to_tables": db_to_tables,
        "table_to_columns": table_to_columns
    })
```

#### 映射生成机制

**db_to_tables 映射生成：**
```python
db_to_tables = {}
for table in tables:
    db_name = table.get('db_name', 'unknown')
    table_name = table.get('table_name', 'unknown')
    if db_name not in db_to_tables:
        db_to_tables[db_name] = []
    if table_name not in db_to_tables[db_name]:
        db_to_tables[db_name].append(table_name)
```

**table_to_columns 映射生成：**
```python
table_to_columns = {}
for col in columns:
    table_name = col.get('table_name', 'unknown')
    column_name = col.get('column_name', 'unknown')
    if table_name not in table_to_columns:
        table_to_columns[table_name] = []
    if column_name not in table_to_columns[table_name]:
        table_to_columns[table_name].append(column_name)
```

---

## 🔄 两个方法的关系分析

### 共同点
1. **相同的分组逻辑**：都调用 `_group_columns_by_token_limit(token_size)`
2. **相同的prompt构建**：都使用 `_build_complete_context_for_columns()`
3. **相同的分割策略**：按列级别进行智能分组

### 差异点
| 特性 | `.split()` | `.get_prompt_list_with_mappings()` |
|------|------------|-----------------------------------|
| 返回类型 | `List[str]` | `List[Dict[str, Any]]` |
| 包含映射 | ❌ | ✅ |
| 性能开销 | 较低 | 较高（需要生成映射） |
| 使用场景 | 简单prompt发送 | 需要映射信息的复杂处理 |

### 性能分析

**计算复杂度：**
- 分组阶段：O(n) - n为列数
- prompt构建：O(n) - 每个列都需要处理
- 映射生成：O(n) - 额外的映射计算

**内存使用：**
- `.split()`：存储prompt字符串
- `.get_prompt_list_with_mappings()`：存储prompt + 映射字典（约2-3倍内存）

**是否重复计算：**
- ❌ 不会重复计算分组逻辑
- ❌ 不会重复构建prompt
- ✅ 只是额外生成映射信息

---

## 💡 使用建议

### 选择 `.split()` 的场景：
```python
# 1. 简单的LLM调用，不需要映射信息
generator = SchemaGenerator("source")
await generator.from_table_names(["adm_lon_varoius", "test_complete_db"])
prompts = generator.split(token_size=7500)

for prompt in prompts:
    llm_response = send_to_llm(prompt)  # 直接发送
    process_response(llm_response)      # 简单处理
```

### 选择 `.get_prompt_list_with_mappings()` 的场景：
```python
# 2. 需要根据映射信息进行复杂处理
generator = SchemaGenerator("source")
await generator.from_table_names(["adm_lon_varoius", "test_complete_db"])
mapping_prompts = generator.get_prompt_list_with_mappings(token_size=7500)

for item in mapping_prompts:
    llm_response = send_to_llm(item["prompt"])
    
    # 根据映射信息进行精确处理
    for db_name, tables in item["db_to_tables"].items():
        process_database_response(db_name, tables, llm_response)
    
    for table_name, columns in item["table_to_columns"].items():
        process_table_response(table_name, columns, llm_response)
```

### 性能优化建议：
1. **大数据量**：优先使用 `.split()` 减少内存占用
2. **需要映射**：使用 `.get_prompt_list_with_mappings()` 一次性获取所有信息
3. **避免重复调用**：不要同时调用两个方法，选择一个即可

---

## 🎯 核心优势

### 智能分割的优势：
1. **语义完整性**：按列分组，保持数据结构的完整性
2. **上下文保持**：每个片段都包含完整的数据库和表信息
3. **大小精确控制**：基于实际内容大小进行分割，而不是简单的字符数
4. **跨表支持**：一个片段可以包含多个表的相关列

### 映射信息的价值：
1. **精确定位**：知道每个prompt涉及哪些数据库和表
2. **响应处理**：可以根据映射信息精确处理LLM响应
3. **错误追踪**：出现问题时可以快速定位到具体的表和列
4. **批量处理**：可以按数据库或表进行批量处理

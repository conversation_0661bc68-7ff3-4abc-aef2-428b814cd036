"""
完整的Knowledge Doc模块操作示例

展示了新的配置化架构下的完整工作流程：
1. 文档管理 - 创建、查询、更新文档
2. 分块管理 - 创建分块、信息管理、向量化
3. 向量搜索 - 语义搜索、相似度匹配
4. 类别管理 - 分类创建、关系管理
5. 业务聚合 - 端到端的业务场景
"""

import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any
import sys,os
# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

from modules.knowledge.doc.operations.document_ops import DocumentOperation
from modules.knowledge.doc.operations.chunk_ops import ChunkOperation
from modules.knowledge.doc.operations.category_ops import CombinedCategoryOperation
from modules.knowledge.doc.entities.api_models import DocumentCreate
from modules.knowledge.doc.entities.base_models import DocumentStatus, ParseType, DocumentFormat, Chunk, ChunkInfo

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class KnowledgeDocWorkflow:
    """Knowledge Doc模块完整工作流程演示"""
    
    def __init__(self):
        """初始化操作类（使用配置驱动）"""
        self.doc_ops = DocumentOperation()
        self.chunk_ops = ChunkOperation()
        self.category_ops = CombinedCategoryOperation()
        self.knowledge_id = "enterprise-demo-kb"
    
    async def scenario_1_complete_document_ingestion(self):
        """场景1: 完整的文档入库流程"""
        print("\n" + "="*60)
        print("🚀 场景1: 完整的文档入库流程")
        print("="*60)
        
        try:
            # 1. 创建文档记录
            print("\n📄 步骤1: 创建文档记录")
            document_data = DocumentCreate(
                knowledge_id=self.knowledge_id,
                doc_name="企业AI技术白皮书.pdf",
                doc_type=None,
                author="技术团队",
                vector_similarity_weight=0.8,
                similarity_threshold=0.7,
                parse_type=ParseType.PDF,
                status=DocumentStatus.PENDING,
                parse_end_time=None,
                parse_message=None,
                doc_format=DocumentFormat.PDF,
                location="/uploads/enterprise-ai-whitepaper.pdf",
                metadata='{"category": "technology", "priority": "high"}',
                created_time=None,
                updated_time=None,
                is_active=True
            )
            
            doc_id = await self.doc_ops.create_document(document_data)
            print(f"✅ 文档创建成功: {doc_id}")
            
            # 2. 更新文档状态为处理中
            print("\n🔄 步骤2: 更新文档状态为处理中")
            await self.doc_ops.update_document_status(
                doc_id, 
                DocumentStatus.PROCESSING, 
                "开始文档解析和分块处理"
            )
            print(f"✅ 文档状态更新: PROCESSING")
            
            # 3. 模拟文档分块和内容提取
            print("\n🧩 步骤3: 文档分块和向量化")
            chapters_data = [
                {
                    "chapter_layer": "摘要",
                    "chunk_infos": [
                        {
                            "info_type": "title", 
                            "info_value": "企业AI技术发展概述"
                        },
                        {
                            "info_type": "content", 
                            "info_value": "本白皮书系统阐述了企业级人工智能技术的发展现状、核心技术栈、实施策略和未来展望。涵盖机器学习、深度学习、自然语言处理、计算机视觉等关键技术领域。"
                        },
                        {
                            "info_type": "summary", 
                            "info_value": "企业AI技术白皮书总览，涵盖核心技术和实施策略"
                        },
                        {
                            "info_type": "keywords", 
                            "info_value": "人工智能,机器学习,深度学习,企业级应用,技术战略"
                        }
                    ]
                },
                {
                    "chapter_layer": "第一章.技术基础",
                    "chunk_infos": [
                        {
                            "info_type": "title", 
                            "info_value": "机器学习基础理论"
                        },
                        {
                            "info_type": "content", 
                            "info_value": "机器学习是人工智能的核心分支，通过算法让计算机系统自动从数据中学习模式和规律。主要包括监督学习、无监督学习、强化学习三大类别。监督学习使用标注数据训练模型，无监督学习发现数据中的隐藏结构，强化学习通过与环境交互学习最优策略。"
                        },
                        {
                            "info_type": "summary", 
                            "info_value": "机器学习基础：监督学习、无监督学习、强化学习三大类别"
                        },
                        {
                            "info_type": "keywords", 
                            "info_value": "机器学习,监督学习,无监督学习,强化学习,算法"
                        }
                    ]
                },
                {
                    "chapter_layer": "第二章.深度学习",
                    "chunk_infos": [
                        {
                            "info_type": "title", 
                            "info_value": "深度神经网络架构"
                        },
                        {
                            "info_type": "content", 
                            "info_value": "深度学习基于多层神经网络，能够自动学习数据的层次化特征表示。卷积神经网络（CNN）擅长处理图像数据，循环神经网络（RNN）适合序列数据处理，Transformer架构在自然语言处理领域取得突破性进展。注意力机制的引入大幅提升了模型的表达能力。"
                        },
                        {
                            "info_type": "summary", 
                            "info_value": "深度学习架构：CNN、RNN、Transformer和注意力机制"
                        },
                        {
                            "info_type": "keywords", 
                            "info_value": "深度学习,神经网络,CNN,RNN,Transformer,注意力机制"
                        }
                    ]
                },
                {
                    "chapter_layer": "第三章.NLP技术",
                    "chunk_infos": [
                        {
                            "info_type": "title", 
                            "info_value": "自然语言处理技术栈"
                        },
                        {
                            "info_type": "content", 
                            "info_value": "自然语言处理技术使计算机能够理解、处理和生成人类语言。核心技术包括分词、词性标注、命名实体识别、句法分析、语义理解等。大语言模型如GPT、BERT的出现revolutionized了NLP领域，在文本生成、机器翻译、问答系统等任务上表现卓越。"
                        },
                        {
                            "info_type": "summary", 
                            "info_value": "NLP技术栈：从基础处理到大语言模型的技术演进"
                        },
                        {
                            "info_type": "keywords", 
                            "info_value": "自然语言处理,NLP,分词,语义理解,大语言模型,GPT,BERT"
                        }
                    ]
                },
                {
                    "chapter_layer": "第四章.应用实践",
                    "chunk_infos": [
                        {
                            "info_type": "title", 
                            "info_value": "企业AI应用场景"
                        },
                        {
                            "info_type": "content", 
                            "info_value": "企业AI应用涵盖多个业务领域：智能客服提升用户体验，推荐系统提高转化率，风险控制系统保障业务安全，供应链优化降低成本，智能制造提升生产效率。关键在于选择合适的技术方案，确保数据质量，建立完整的MLOps流程。"
                        },
                        {
                            "info_type": "summary", 
                            "info_value": "企业AI应用：客服、推荐、风控、供应链、制造等场景"
                        },
                        {
                            "info_type": "keywords", 
                            "info_value": "企业应用,智能客服,推荐系统,风险控制,供应链,智能制造,MLOps"
                        }
                    ]
                }
            ]
            
            # 4. 批量创建分块（包含向量化）
            chunk_ids = []
            total_info_count = 0
            
            for i, chapter_data in enumerate(chapters_data, 1):
                print(f"   处理第{i}个分块: {chapter_data['chapter_layer']}")
                
                chunk_id = await self.chunk_ops.create_chunk_with_info_and_vector(
                    knowledge_id=self.knowledge_id,
                    doc_id=doc_id,
                    chapter_layer=chapter_data["chapter_layer"],
                    chunk_infos=chapter_data["chunk_infos"]
                )
                
                chunk_ids.append(chunk_id)
                total_info_count += len(chapter_data["chunk_infos"])
                
                # 更新文档进度
                progress = (i / len(chapters_data)) * 100
                await self.doc_ops.update_document_progress(
                    doc_id, 
                    chunk_nums=i, 
                    percentage=progress
                )
            
            print(f"✅ 分块创建完成: {len(chunk_ids)}个分块, {total_info_count}条信息")
            
            # 5. 完成文档处理
            print("\n✅ 步骤4: 完成文档处理")
            await self.doc_ops.update_document_status(
                doc_id, 
                DocumentStatus.COMPLETED, 
                f"解析完成，共生成{len(chunk_ids)}个分块，{total_info_count}条信息"
            )
            
            # 6. 查询文档完整信息
            print("\n📋 步骤5: 查询文档完整信息")
            document = await self.doc_ops.get_document_by_id(doc_id)
            if document:
                print(f"   文档名称: {document['doc_name']}")
                print(f"   文档状态: {document['status']}")
                print(f"   分块数量: {document['chunk_nums']}")
                print(f"   完成度: {document['percentage']:.1f}%")
                print(f"   创建时间: {document['created_time']}")
            
            print(f"\n🎉 文档入库完整流程完成！")
            print(f"   📄 文档ID: {doc_id}")
            print(f"   🧩 分块数: {len(chunk_ids)}")
            print(f"   📝 信息条数: {total_info_count}")
            
            return doc_id, chunk_ids
            
        except Exception as e:
            logger.error(f"文档入库流程失败: {e}")
            raise
    
    async def scenario_2_intelligent_search(self, doc_id: str, chunk_ids: List[str]):
        """场景2: 智能搜索和内容发现"""
        print("\n" + "="*60)
        print("🔍 场景2: 智能搜索和内容发现")
        print("="*60)
        
        try:
            # 搜索测试用例
            search_queries = [
                "什么是深度学习",
                "企业如何应用人工智能",
                "机器学习算法有哪些类型",
                "自然语言处理技术",
                "AI在制造业的应用"
            ]
            
            for i, query in enumerate(search_queries, 1):
                print(f"\n🔍 搜索{i}: '{query}'")
                
                # 执行向量搜索
                search_results = await self.chunk_ops.search_similar_chunks(
                    query_text=query,
                    knowledge_id=self.knowledge_id,
                    info_types=["content", "summary", "title"],
                    top_k=3,
                    similarity_threshold=0.3
                )
                
                if search_results:
                    print(f"   📚 找到 {len(search_results)} 个相关结果:")
                    for j, result in enumerate(search_results, 1):
                        print(f"      {j}. [{result['info_type']}] 相似度: {result['similarity_score']:.3f}")
                        print(f"         分块ID: {result['chunk_id'][:16]}...")
                        print(f"         内容: {result['info_value'][:80]}...")
                        print()
                    
                    # 基于第一个搜索结果查找相关内容
                    if len(search_results) > 0:
                        top_result = search_results[0]
                        print(f"   🔗 基于顶部结果查找相关内容:")
                        
                        related_chunks = await self.chunk_ops.search_similar_chunks_by_chunk_info_id(
                            chunk_info_id=top_result['chunk_info_id'],
                            knowledge_id=self.knowledge_id,
                            top_k=2,
                            exclude_self=True,
                            similarity_threshold=0.3
                        )
                        
                        if related_chunks:
                            for k, chunk in enumerate(related_chunks, 1):
                                print(f"      相关{k}. 相似度: {chunk['similarity_score']:.3f}")
                                print(f"            内容: {chunk['info_value'][:60]}...")
                        else:
                            print("      暂无相关内容")
                else:
                    print(f"   ❌ 未找到相关结果")
                
                print("-" * 50)
            
            print(f"\n✅ 智能搜索演示完成！")
            
        except Exception as e:
            logger.error(f"智能搜索演示失败: {e}")
            raise
    
    async def scenario_3_category_management(self, doc_id: str):
        """场景3: 文档分类和关系管理"""
        print("\n" + "="*60)
        print("📂 场景3: 文档分类和关系管理")
        print("="*60)
        
        try:
            # 1. 创建类别层次结构
            print("\n📁 步骤1: 创建类别层次结构")
            
            # 一级类别
            tech_cate_id = await self.category_ops.category_ops.create_category(
                cate_id="tech-docs",
                cate_name="技术文档",
                cate_layer=1,
                parent_id=None,
                cate_status="active"
            )
            print(f"   ✅ 创建一级类别: 技术文档 ({tech_cate_id})")
            
            # 二级类别
            ai_cate_id = await self.category_ops.category_ops.create_category(
                cate_id="tech-ai",
                cate_name="人工智能",
                cate_layer=2,
                parent_id=tech_cate_id,
                cate_status="active"
            )
            print(f"   ✅ 创建二级类别: 人工智能 ({ai_cate_id})")
            
            # 三级类别
            ml_cate_id = await self.category_ops.category_ops.create_category(
                cate_id="tech-ai-ml",
                cate_name="机器学习",
                cate_layer=3,
                parent_id=ai_cate_id,
                cate_status="active"
            )
            print(f"   ✅ 创建三级类别: 机器学习 ({ml_cate_id})")
            
            dl_cate_id = await self.category_ops.category_ops.create_category(
                cate_id="tech-ai-dl", 
                cate_name="深度学习",
                cate_layer=3,
                parent_id=ai_cate_id,
                cate_status="active"
            )
            print(f"   ✅ 创建三级类别: 深度学习 ({dl_cate_id})")
            
            nlp_cate_id = await self.category_ops.category_ops.create_category(
                cate_id="tech-ai-nlp",
                cate_name="自然语言处理", 
                cate_layer=3,
                parent_id=ai_cate_id,
                cate_status="active"
            )
            print(f"   ✅ 创建三级类别: 自然语言处理 ({nlp_cate_id})")
            
            # 2. 将文档关联到类别
            print("\n🔗 步骤2: 文档分类关联")
            
            # 关联到主要类别
            await self.category_ops.doc_category_ops.create_document_category(
                doc_id=doc_id,
                cate_id=ai_cate_id,
                cate_layer=2,
                doc_name="企业AI技术白皮书",
                doc_status="active"
            )
            print(f"   ✅ 文档关联到类别: 人工智能")
            
            # 关联到子类别
            await self.category_ops.doc_category_ops.create_document_category(
                doc_id=doc_id,
                cate_id=ml_cate_id,
                cate_layer=3,
                doc_name="企业AI技术白皮书",
                doc_status="active"
            )
            print(f"   ✅ 文档关联到类别: 机器学习")
            
            await self.category_ops.doc_category_ops.create_document_category(
                doc_id=doc_id,
                cate_id=dl_cate_id,
                cate_layer=3,
                doc_name="企业AI技术白皮书",
                doc_status="active"
            )
            print(f"   ✅ 文档关联到类别: 深度学习")
            
            # 3. 查询文档的类别
            print("\n📋 步骤3: 查询文档分类")
            doc_categories = await self.category_ops.doc_category_ops.get_categories_by_doc_id(doc_id)
            
            if doc_categories:
                print(f"   📂 文档所属类别 ({len(doc_categories)}个):")
                for category in doc_categories:
                    print(f"      - {category['cate_id']} (层级: {category['cate_layer']})")
            
            # 4. 查询类别下的文档
            print("\n📋 步骤4: 查询类别文档")
            category_docs = await self.category_ops.doc_category_ops.get_documents_by_category_id(ai_cate_id)
            
            if category_docs:
                print(f"   📄 人工智能类别下的文档 ({len(category_docs)}个):")
                for doc in category_docs:
                    print(f"      - {doc['doc_name']} ({doc['doc_id'][:16]}...)")
            
            # 5. 创建类别关系
            print("\n🔗 步骤5: 创建类别关系")
            
            # 机器学习 -> 深度学习 (相关关系)
            rel_id = await self.category_ops.category_ops.create_category_relationship(
                source_cate_id=ml_cate_id,
                target_cate_id=dl_cate_id,
                rel_type="related"
            )
            print(f"   ✅ 创建类别关系: 机器学习 -> 深度学习 (相关)")
            
            # 深度学习 -> 自然语言处理 (应用关系)
            rel_id_2 = await self.category_ops.category_ops.create_category_relationship(
                source_cate_id=dl_cate_id,
                target_cate_id=nlp_cate_id,
                rel_type="application"
            )
            print(f"   ✅ 创建类别关系: 深度学习 -> 自然语言处理 (应用)")
            
            # 6. 查询类别关系
            print("\n📋 步骤6: 查询类别关系")
            ml_relationships = await self.category_ops.category_ops.get_category_relationships_by_source(ml_cate_id)
            if ml_relationships:
                print(f"   🔗 机器学习类别的关系:")
                for rel in ml_relationships:
                    print(f"      - 目标: {rel['target_cate_id']}, 关系: {rel['rel_type']}")
            
            print(f"\n✅ 文档分类管理演示完成！")
            
        except Exception as e:
            logger.error(f"分类管理演示失败: {e}")
            raise
    
    async def scenario_4_knowledge_analytics(self):
        """场景4: 知识库统计分析"""
        print("\n" + "="*60)
        print("📊 场景4: 知识库统计分析")
        print("="*60)
        
        try:
            # 1. 文档统计
            print("\n📈 步骤1: 文档统计分析")
            
            total_docs = await self.doc_ops.count_documents(self.knowledge_id)
            completed_docs = await self.doc_ops.count_documents(self.knowledge_id, DocumentStatus.COMPLETED)
            processing_docs = await self.doc_ops.count_documents(self.knowledge_id, DocumentStatus.PROCESSING)
            
            print(f"   📄 知识库文档统计 ({self.knowledge_id}):")
            print(f"      总文档数: {total_docs}")
            print(f"      已完成: {completed_docs}")
            print(f"      处理中: {processing_docs}")
            if total_docs > 0:
                print(f"      完成率: {completed_docs/total_docs*100:.1f}%")
            
            # 2. 分块信息统计
            print("\n🧩 步骤2: 分块信息统计")
            
            documents = await self.doc_ops.get_documents_by_knowledge_id(self.knowledge_id)
            total_chunks = 0
            total_chunk_infos = 0
            
            print(f"   📋 文档详细信息:")
            for doc in documents:
                chunks = await self.chunk_ops.get_chunks_by_document(doc['doc_id'])
                total_chunks += len(chunks)
                
                doc_info_count = 0
                for chunk in chunks:
                    chunk_infos = chunk.get('chunk_infos', [])
                    doc_info_count += len(chunk_infos)
                
                total_chunk_infos += doc_info_count
                
                print(f"      - {doc['doc_name'][:30]}...")
                print(f"        分块数: {len(chunks)}, 信息条数: {doc_info_count}")
                print(f"        状态: {doc['status']}, 进度: {doc.get('percentage', 0):.1f}%")
            
            print(f"\n   📊 总计统计:")
            print(f"      总分块数: {total_chunks}")
            print(f"      总信息条数: {total_chunk_infos}")
            if total_chunks > 0:
                print(f"      平均信息密度: {total_chunk_infos/total_chunks:.1f} 条/分块")
            
            # 3. 类别统计
            print("\n📂 步骤3: 类别统计分析")
            
            all_categories = await self.category_ops.category_ops.get_all_categories(cate_status="active")
            
            layer_stats = {}
            for category in all_categories:
                layer = category['cate_layer']
                if layer not in layer_stats:
                    layer_stats[layer] = 0
                layer_stats[layer] += 1
            
            print(f"   📁 类别层级统计:")
            for layer in sorted(layer_stats.keys()):
                print(f"      第{layer}层: {layer_stats[layer]} 个类别")
            
            # 统计各类别下的文档数量
            print(f"\n   📄 类别文档分布:")
            for category in all_categories[:5]:  # 只显示前5个类别
                cate_docs = await self.category_ops.doc_category_ops.get_documents_by_category_id(category['cate_id'])
                print(f"      - {category['cate_name']}: {len(cate_docs)} 个文档")
            
            print(f"\n✅ 知识库统计分析完成！")
            
        except Exception as e:
            logger.error(f"统计分析失败: {e}")
            raise
    
    async def scenario_5_vector_batch_operations(self):
        """场景5: 向量批量操作演示"""
        print("\n" + "="*60)
        print("🧮 场景5: 向量批量操作演示")
        print("="*60)
        
        try:
            print("\n📄 创建测试文档用于批量向量演示")
            
            # 创建测试文档
            test_doc_data = DocumentCreate(
                knowledge_id=self.knowledge_id,
                doc_name="向量测试文档.md",
                doc_type=None,
                author=None,
                vector_similarity_weight=None,
                similarity_threshold=None,
                parse_type=ParseType.TXT,  # 使用TXT代替MARKDOWN
                status=DocumentStatus.PENDING,
                parse_end_time=None,
                parse_message=None,
                doc_format=DocumentFormat.MD,  # 使用MD代替MARKDOWN
                location="/test/vector-test.md",
                metadata=None,
                created_time=None,
                updated_time=None,
                is_active=True
            )
            
            test_doc_id = await self.doc_ops.create_document(test_doc_data)
            print(f"✅ 测试文档创建成功: {test_doc_id}")
            
            # 创建分块但不自动向量化
            print("\n🧩 创建分块（不含向量）")
            test_chunks_data = [
                {
                    "chapter_layer": "概念",
                    "chunk_infos": [
                        {"info_type": "content", "info_value": "向量数据库是专门存储和检索高维向量数据的数据库系统"},
                        {"info_type": "summary", "info_value": "向量数据库概念介绍"}
                    ]
                },
                {
                    "chapter_layer": "应用",
                    "chunk_infos": [
                        {"info_type": "content", "info_value": "向量数据库广泛应用于语义搜索、推荐系统、相似度匹配等场景"},
                        {"info_type": "summary", "info_value": "向量数据库应用场景"}
                    ]
                }
            ]
            
            test_chunk_ids = []
            for chunk_data in test_chunks_data:
                chunk_id = await self.chunk_ops.create_chunk_with_info(
                    doc_id=test_doc_id,
                    chapter_layer=chunk_data["chapter_layer"],
                    chunk_infos=chunk_data["chunk_infos"]
                )
                test_chunk_ids.append(chunk_id)
            
            print(f"✅ 创建分块完成: {len(test_chunk_ids)} 个")
            
            # 批量生成向量
            print("\n🚀 批量向量生成")
            batch_result = await self.chunk_ops.batch_generate_vectors_for_document(
                knowledge_id=self.knowledge_id,
                doc_id=test_doc_id,
                info_types=["content", "summary"]
            )
            
            print(f"   📊 批量向量化结果:")
            print(f"      总信息数: {batch_result['total_infos']}")
            print(f"      成功向量化: {batch_result['success_count']}")
            print(f"      失败数: {batch_result['failed_count']}")
            print(f"      向量数量: {batch_result['vector_count']}")
            
            # 单独为特定分块信息生成向量
            print("\n🎯 单独向量生成演示")
            chunks_with_info = await self.chunk_ops.get_chunks_by_document(test_doc_id)
            if chunks_with_info and chunks_with_info[0].get('chunk_infos'):
                first_chunk_info = chunks_with_info[0]['chunk_infos'][0]
                
                success = await self.chunk_ops.add_vector_for_chunk_info(
                    knowledge_id=self.knowledge_id,
                    chunk_info_id=first_chunk_info['chunk_info_id'],
                    force_regenerate=True  # 强制重新生成
                )
                
                print(f"   ✅ 单独向量生成: {'成功' if success else '失败'}")
            
            # 向量搜索验证
            print("\n🔍 向量搜索验证")
            search_results = await self.chunk_ops.search_similar_chunks(
                query_text="什么是向量数据库",
                knowledge_id=self.knowledge_id,
                top_k=3,
                similarity_threshold=0.1
            )
            
            print(f"   📚 搜索结果: {len(search_results)} 个")
            for i, result in enumerate(search_results, 1):
                print(f"      {i}. 相似度: {result['similarity_score']:.3f}")
                print(f"         内容: {result['info_value'][:50]}...")
            
            print(f"\n✅ 向量批量操作演示完成！")
            
        except Exception as e:
            logger.error(f"向量批量操作演示失败: {e}")
            raise


async def main():
    """主函数 - 运行完整的演示流程"""
    print("🌟 Knowledge Doc Module - 完整操作演示")
    print("=" * 80)
    print("本演示展示了新架构下的完整功能：")
    print("• 配置驱动的客户端管理")
    print("• 文档生命周期管理")
    print("• 智能分块和向量化")
    print("• 语义搜索和内容发现")
    print("• 分类管理和关系维护")
    print("• 知识库统计分析")
    print("=" * 80)
    
    workflow = KnowledgeDocWorkflow()
    
    try:
        # 场景1: 完整的文档入库流程
        doc_id, chunk_ids = await workflow.scenario_1_complete_document_ingestion()
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")

    try:
        # 场景2: 智能搜索和内容发现
        await workflow.scenario_2_intelligent_search(doc_id, chunk_ids)
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")

    try:
        # 场景3: 文档分类和关系管理
        await workflow.scenario_3_category_management(doc_id)
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")

    try:
        # 场景4: 知识库统计分析
        await workflow.scenario_4_knowledge_analytics()
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")

    try:
        # 场景5: 向量批量操作演示
        await workflow.scenario_5_vector_batch_operations()
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")

    print("\n" + "🎉" * 30)
    print("🎉 所有演示场景完成！Knowledge Doc 模块功能正常运行 🎉")
    print("🎉" * 30)
    
    print("\n✅ 主要功能验证总结:")
    print("   1. ✅ 配置驱动架构 - 自动获取 RDB、VDB、Embedding 客户端")
    print("   2. ✅ 文档生命周期 - 创建、解析、分块、完成全流程")
    print("   3. ✅ 智能向量化 - 自动生成和存储向量数据")
    print("   4. ✅ 语义搜索 - 基于向量的相似度搜索")
    print("   5. ✅ 分类管理 - 层次化类别和文档关联")
    print("   6. ✅ 批量操作 - 高效的批量向量生成")
    print("   7. ✅ 统计分析 - 全面的知识库分析功能")
    
    print("\n💡 新架构优势:")
    print("   • 简化使用方式 - 无需手动管理数据库客户端")
    print("   • 配置化管理 - 通过 Hydra 配置动态切换数据源")
    print("   • 业务聚合 - 多表操作保证数据一致性")
    print("   • 向量集成 - 端到端的向量搜索能力")
    print("   • 向后兼容 - 支持旧版本的使用方式")
        

    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1) 
# Priority 机制使用指南

## 1. 简介

Priority 机制是 HSBC Knowledge 项目中用于管理不同业务场景下资源配置的重要特性。通过为不同优先级的服务分配不同的连接池大小、超时设置和缓存策略，实现系统资源的优化分配。

本文档旨在为开发者提供 Priority 机制的使用指南，帮助开发者正确、高效地使用这一特性。

## 2. 基本概念

### 2.1 优先级级别

Priority 机制定义了三个优先级级别：

1. **HIGH (高优先级)**：用于关键业务场景，分配最多的系统资源
2. **STANDARD (标准优先级)**：系统默认级别，用于常规业务场景
3. **LOW (低优先级)**：用于批处理和后台任务，分配较少的系统资源

### 2.2 数据库类型

目前支持的数据库类型：

1. **MySQL**：关系型数据库
2. **PGVector**：向量数据库

## 3. 使用方法

### 3.1 基本用法

#### 3.1.1 获取标准优先级客户端
```python
from service import get_client

# 获取标准优先级的 MySQL 客户端
mysql_client = await get_client("database.rdbs.mysql")

# 获取标准优先级的 PGVector 客户端
pgvector_client = await get_client("database.vdbs.pgvector")
```

#### 3.1.2 获取指定优先级客户端
```python
from service import get_client

# 获取高优先级的 MySQL 客户端
high_mysql_client = await get_client("database.rdbs.mysql", priority='high')

# 获取低优先级的 PGVector 客户端
low_pgvector_client = await get_client("database.vdbs.pgvector", priority='low')
```

### 3.2 使用配置对象
```python
from service import get_client, get_config
from omegaconf import DictConfig

# 获取配置管理器
cfg = await get_config()

# 使用配置对象获取客户端
mysql_client = await get_client(cfg.database.rdbs.mysql, priority='high')
```

### 3.3 直接使用配置路径
```python
from service import get_client

# 直接使用完整配置路径
high_mysql_client = await get_client("database.rdbs.mysql_high_priority")
low_pgvector_client = await get_client("database.vdbs.pgvector_low_priority")
```

### 3.4 使用便捷方法
```python
from service import get_high_priority_client, get_standard_priority_client, get_low_priority_client

# 获取高优先级客户端
high_client = await get_high_priority_client("database.rdbs.mysql")

# 获取标准优先级客户端
standard_client = await get_standard_priority_client("database.rdbs.mysql")

# 获取低优先级客户端
low_client = await get_low_priority_client("database.rdbs.mysql")
```

### 3.5 手动配置覆盖
```python
from service import get_client

# 使用手动优先级配置覆盖
custom_client = await get_client(
    "database.rdbs.mysql",
    priority='high',
    priority_config_override={
        'pool_size': 100,
        'max_overflow': 200,
        'pool_timeout': 5
    }
)
```

## 4. 优先级选择指南

### 4.1 HIGH 优先级适用场景
1. **关键业务操作**：直接影响用户体验的核心功能
2. **实时数据处理**：需要快速响应的数据处理任务
3. **高并发场景**：需要大量连接资源的业务场景
4. **用户交互**：直接面向用户的交互操作

示例：
```python
# 用户登录验证
auth_client = await get_client("database.rdbs.mysql", priority='high')

# 实时数据查询
realtime_client = await get_client("database.vdbs.pgvector", priority='high')
```

### 4.2 STANDARD 优先级适用场景
1. **常规业务操作**：大部分日常业务逻辑
2. **批量数据处理**：非实时但需要稳定执行的任务
3. **配置管理**：系统配置的读取和更新
4. **报表生成**：定期生成的业务报表

示例：
```python
# 用户信息查询
user_client = await get_client("database.rdbs.mysql", priority='standard')

# 业务数据统计
stats_client = await get_client("database.vdbs.pgvector", priority='standard')
```

### 4.3 LOW 优先级适用场景
1. **后台任务**：不需要立即完成的维护性任务
2. **数据同步**：非实时的数据同步操作
3. **日志处理**：系统日志的处理和分析
4. **批量导入**：大批量数据的导入操作

示例：
```python
# 数据备份
backup_client = await get_client("database.rdbs.mysql", priority='low')

# 日志分析
log_client = await get_client("database.vdbs.pgvector", priority='low')
```

## 5. 配置文件

### 5.1 配置文件结构
```
src/config/
└── database/
    ├── rdbs/
    │   └── mysql/
    │       └── priority/
    │           ├── high.yaml
    │           ├── standard.yaml
    │           └── low.yaml
    └── vdbs/
        └── pgvector/
            └── priority/
                ├── high.yaml
                ├── standard.yaml
                └── low.yaml
```

### 5.2 配置参数说明

#### 5.2.1 MySQL 配置参数
```yaml
# 连接池配置
pool_size: 20          # 基础连接数
max_overflow: 40       # 最大溢出连接数
pool_timeout: 30.0     # 连接超时时间(秒)
pool_recycle: 3600     # 连接回收时间(秒)
pool_pre_ping: true    # 连接预检查

# 缓存配置
cache_size: 1000       # 缓存大小
cache_ttl: 3600        # 缓存过期时间(秒)

# 优先级标识
priority: "standard"   # 优先级标识
service_tier: "normal" # 服务级别
```

#### 5.2.2 PGVector 配置参数
```yaml
# 连接池配置
min_connections: 5     # 最小连接数
max_connections: 25    # 最大连接数
pool_timeout: 30.0     # 连接超时时间(秒)
pool_recycle: 3600     # 连接回收时间(秒)
pool_pre_ping: true    # 连接预检查

# 缓存配置
cache_size: 1000       # 缓存大小
cache_ttl: 3600        # 缓存过期时间(秒)

# 搜索配置
search_params:
  default_limit: 10    # 默认搜索结果数量
  max_limit: 100       # 最大搜索结果数量
  similarity_threshold: 0.7  # 相似度阈值

# 优先级标识
priority: "standard"   # 优先级标识
service_tier: "normal" # 服务级别
```

## 6. 最佳实践

### 6.1 优先级选择原则
1. **默认使用 STANDARD**：除非有特殊需求，否则使用 STANDARD 优先级
2. **根据业务重要性选择**：关键业务使用 HIGH，后台任务使用 LOW
3. **考虑资源消耗**：高资源消耗的操作考虑使用 LOW 优先级

### 6.2 代码实现建议
1. **封装客户端获取**：通过封装函数统一管理客户端获取
2. **正确处理资源清理**：确保在使用完客户端后正确清理资源
3. **异常处理**：对客户端获取和使用过程中的异常进行适当处理

示例：
```python
class DatabaseManager:
    def __init__(self):
        self._clients = {}
    
    async def get_mysql_client(self, priority='standard'):
        """获取 MySQL 客户端"""
        key = f"mysql_{priority}"
        if key not in self._clients:
            self._clients[key] = await get_client("database.rdbs.mysql", priority=priority)
        return self._clients[key]
    
    async def get_pgvector_client(self, priority='standard'):
        """获取 PGVector 客户端"""
        key = f"pgvector_{priority}"
        if key not in self._clients:
            self._clients[key] = await get_client("database.vdbs.pgvector", priority=priority)
        return self._clients[key]
    
    async def cleanup(self):
        """清理资源"""
        self._clients.clear()
```

### 6.3 性能优化建议
1. **合理配置连接池参数**：根据实际业务需求调整连接池大小
2. **监控资源使用情况**：定期检查不同优先级客户端的资源使用情况
3. **动态调整优先级**：根据业务高峰期动态调整客户端优先级

### 6.4 错误处理建议
1. **重试机制**：对于临时性错误实现重试机制
2. **降级处理**：对于非关键操作实现降级处理
3. **日志记录**：详细记录错误信息便于问题排查

示例：
```python
import asyncio
import logging

logger = logging.getLogger(__name__)

async def safe_database_operation(priority='standard', max_retries=3):
    """安全的数据库操作"""
    for attempt in range(max_retries):
        try:
            client = await get_client("database.rdbs.mysql", priority=priority)
            # 执行数据库操作
            result = await client.afetch_all("SELECT 1")
            return result
        except Exception as e:
            logger.warning(f"数据库操作失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)  # 指数退避
            else:
                logger.error(f"数据库操作最终失败: {e}")
                raise
```

## 7. 常见问题和解决方案

### 7.1 客户端获取失败
**问题**：无法获取客户端实例
**解决方案**：
1. 检查配置路径是否正确
2. 检查数据库连接配置是否正确
3. 查看日志了解具体错误原因

### 7.2 优先级冲突
**问题**：配置路径和优先级参数冲突
**解决方案**：
1. 使用完整配置路径或仅使用优先级参数，不要同时使用
2. 检查配置路径格式是否正确

### 7.3 资源不足
**问题**：连接池资源不足
**解决方案**：
1. 调整对应优先级的连接池配置
2. 考虑使用更高优先级的客户端
3. 优化业务逻辑减少资源消耗

## 8. 监控和调试

### 8.1 日志记录
Priority 机制提供了详细的日志记录，便于监控和调试：

```python
import logging

# 设置日志级别
logging.basicConfig(level=logging.DEBUG)

# 查看详细日志
logger = logging.getLogger('service.client')
```

### 8.2 性能监控
可以通过监控不同优先级客户端的使用情况来优化资源配置：

```python
# TODO: 实现性能监控功能
```

## 9. 总结

Priority 机制为 HSBC Knowledge 项目提供了灵活、高效的资源配置管理能力。通过合理使用这一机制，可以显著提升系统的性能和稳定性。

开发者应该根据实际业务需求选择合适的优先级，并遵循最佳实践来确保系统的高效运行。
#!/usr/bin/env python3
"""
MySQL数据库备份脚本

使用方法:
    python scripts/database/backup/backup_mysql.py --env development
    python scripts/database/backup/backup_mysql.py --env production --tables metadata,dd
"""

import argparse
import asyncio
from datetime import datetime
import os

# 🔧 步骤1: 设置路径（必须在所有src导入之前）
from scripts.utils.path_helper import init_script_env
init_script_env()

# 🚀 步骤2: 现在可以正常导入src模块了
from service import get_client
from modules.knowledge.dd import DDCrud
from modules.knowledge.metadata import MetadataCrud
from utils.common.logger import setup_enterprise_logger
from utils.common.config_util import config


async def backup_database(env: str, tables: list = None):
    """
    备份数据库
    
    Args:
        env: 环境类型 (development/production)
        tables: 要备份的表列表，为None时备份全部
    """
    print(f"🗄️ 开始备份 {env} 环境的数据库...")
    
    try:
        # 获取数据库客户端
        rdb_client = await get_client("database.rdbs.mysql")
        
        # 创建备份目录
        backup_dir = f"backups/{env}/{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        
        if tables:
            print(f"📋 备份指定表: {', '.join(tables)}")
        else:
            print("📋 备份所有表")
            
        # 这里添加实际的备份逻辑
        # ...
        
        print(f"✅ 备份完成，文件保存在: {backup_dir}")
        
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MySQL数据库备份工具')
    parser.add_argument('--env', 
                       choices=['development', 'production'], 
                       required=True, 
                       help='环境类型')
    parser.add_argument('--tables', 
                       help='要备份的表，用逗号分隔（可选）')
    
    args = parser.parse_args()
    
    # 解析表列表
    tables = args.tables.split(',') if args.tables else None
    
    # 设置日志
    setup_enterprise_logger(
        level="INFO",
        service_name=f"database-backup-{args.env}"
    )
    
    # 执行备份
    asyncio.run(backup_database(args.env, tables))


if __name__ == '__main__':
    main() 
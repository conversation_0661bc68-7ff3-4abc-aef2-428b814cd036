"""
模型服务全局访问点

本模块的职责是提供统一的、全局的、懒加载的模型服务访问入口。
它利用 `ServiceClient` 直接从全局配置中创建和管理单例服务实例。

应用中任何需要调用 LLM 或 Embedding 模型的代码，都应该从本模块导入
`llm_provider` 和 `embedding_provider`。
"""

from utils.common.config_util import config
from utils.common.service_client import ServiceClient

# ==================== 全局模型服务访问点（真正的懒加载）====================
# ServiceClient 现在原生支持延迟加载，只需传递一个获取配置的 lambda 函数。
# 只有当 llm_provider 或 embedding_provider 第一次被使用时，lambda 才会被调用，
# 届时 config 对象早已被 Hydra 初始化完毕。

llm_provider = ServiceClient(lambda: config.llm)
embedding_provider = ServiceClient(lambda: config.embedding)


__all__ = [
    'llm_provider',
    'embedding_provider'
]

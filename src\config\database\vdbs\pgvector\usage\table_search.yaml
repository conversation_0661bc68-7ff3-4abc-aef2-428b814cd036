# @package usage.table_search
# 表级向量搜索配置

table_name: "md_table_embeddings"

search_schema:
  vector_field: "embedding"
  topk: 10
  metric_type: "cosine"
  output_fields: 
    - "id"
    - "knowledge_id"
    - "source_type"
    - "rdb_table_id"
    - "content_type"
    - "data_layer"
    - "create_time"
  expr: ""
  partition_name: ""

query_schema:
  topk: 10
  expr: ""
  partition_name: ""
  output_fields:
    - "id"
    - "knowledge_id"
    - "source_type"
    - "rdb_table_id"
    - "content_type"
    - "data_layer"
    - "create_time"

# 表级搜索特定配置
table_search_config:
  # 支持的内容类型
  supported_content_types:
    - "table_name"
    - "table_name_cn"
    - "table_desc"
  
  # 支持的数据源类型
  supported_source_types:
    - "SOURCE"
    - "INDEX"
  
  # 默认搜索过滤条件
  default_filters:
    # 只搜索激活的表
    active_only: true
    # 默认搜索所有内容类型
    content_types: ["table_name", "table_name_cn", "table_desc"]
  
  # 搜索结果排序
  result_ordering:
    # 主要排序：相似度得分（降序）
    primary: "similarity_score DESC"
    # 次要排序：创建时间（降序）
    secondary: "create_time DESC"

#!/bin/bash
"""
通用Python脚本运行器

自动设置正确的PYTHONPATH和工作目录，然后运行指定的Python脚本。

使用方法:
    # 运行数据库备份脚本
    ./scripts/utils/run_python_script.sh database/backup/backup_mysql.py --env development

    # 运行部署脚本
    ./scripts/utils/run_python_script.sh deployment/deploy_production.py --auto-path

    # 运行数据导入脚本
    ./scripts/utils/run_python_script.sh data/import/import_metadata.py --file data.xlsx --type database
"""

set -e  # 遇到错误立即退出

# 颜色输出函数
print_info() {
    echo -e "\033[1;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $1"
}

# 获取脚本目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
SRC_DIR="$PROJECT_ROOT/src"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"

# 检查参数
if [ $# -lt 1 ]; then
    print_error "用法: $0 <python_script_path> [script_arguments...]"
    print_info "示例: $0 database/backup/backup_mysql.py --env development"
    exit 1
fi

PYTHON_SCRIPT_PATH="$1"
shift  # 移除第一个参数，剩下的都是脚本参数

# 构建完整的脚本路径
FULL_SCRIPT_PATH="$SCRIPTS_DIR/$PYTHON_SCRIPT_PATH"

# 检查脚本是否存在
if [ ! -f "$FULL_SCRIPT_PATH" ]; then
    print_error "脚本文件不存在: $FULL_SCRIPT_PATH"
    exit 1
fi

# 检查src目录是否存在
if [ ! -d "$SRC_DIR" ]; then
    print_error "src目录不存在: $SRC_DIR"
    exit 1
fi

# 设置环境变量
export PROJECT_ROOT="$PROJECT_ROOT"
export SRC_DIR="$SRC_DIR"
export SCRIPTS_DIR="$SCRIPTS_DIR"

# 设置PYTHONPATH
if [ -z "$PYTHONPATH" ]; then
    export PYTHONPATH="$SRC_DIR"
else
    export PYTHONPATH="$SRC_DIR:$PYTHONPATH"
fi

# 切换到项目根目录
cd "$PROJECT_ROOT"

print_info "🚀 运行Python脚本"
print_info "   项目根目录: $PROJECT_ROOT"
print_info "   源码目录: $SRC_DIR"
print_info "   脚本目录: $SCRIPTS_DIR"
print_info "   目标脚本: $PYTHON_SCRIPT_PATH"
print_info "   PYTHONPATH: $PYTHONPATH"
print_info "   工作目录: $(pwd)"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        print_error "Python未找到，请确保Python已安装并在PATH中"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

print_info "   Python命令: $PYTHON_CMD"

# 运行Python脚本
print_info ""
print_info "📋 执行脚本命令:"
print_info "   $PYTHON_CMD \"$FULL_SCRIPT_PATH\" $*"
print_info ""

# 执行脚本
if $PYTHON_CMD "$FULL_SCRIPT_PATH" "$@"; then
    print_success "✅ 脚本执行成功"
    exit 0
else
    exit_code=$?
    print_error "❌ 脚本执行失败，退出代码: $exit_code"
    exit $exit_code
fi 
import asyncio
import sys
import hydra
import json
from pathlib import Path


current_dir = Path(__file__).parent
project_root = current_dir / 'src'
sys.path.insert(0, str(project_root))


async def test_document_operations():
    """测试文档操作"""
    print("=" * 60)
    print("测试文档操作")
    print("=" * 60)
    
    try:
        from modules.knowledge.doc.operations.document_ops import main as doc_main
        await doc_main()
    except Exception as e:
        print(f"文档操作测试失败: {e}")


async def test_category_operations():
    """测试类别操作"""
    print("=" * 60)
    print("测试类别操作")
    print("=" * 60)
    
    try:
        from modules.knowledge.doc.operations.category_ops import main as category_main
        await category_main()
    except Exception as e:
        print(f"类别操作测试失败: {e}")


async def test_chunk_operations():
    """测试分块操作"""
    print("=" * 60)
    print("测试分块操作")
    print("=" * 60)
    
    try:
        from modules.knowledge.doc.operations.chunk_ops import main as chunk_main
        await chunk_main()
    except Exception as e:
        print(f"分块操作测试失败: {e}")


async def test_combined_operations():
    """测试组合操作"""
    print("=" * 60)
    print("测试组合操作 - 文档、类别、分块完整流程")
    print("=" * 60)
    
    try:
        from service import get_client
        from modules.knowledge.doc.operations.document_ops import DocumentOperation
        from modules.knowledge.doc.operations.category_ops import CombinedCategoryOperation
        from modules.knowledge.doc.operations.chunk_ops import ChunkOperation
        from modules.knowledge.doc.entities import DocumentCreate
        from modules.knowledge.doc.entities import DocumentStatus, ParseType, DocumentFormat
        
        # 获取数据库客户端
        rdb_client = await get_client("database.rdbs.mysql")
        vdb_client = await get_client("database.vdbs.pgvector")
        
        # 创建操作实例
        doc_ops = DocumentOperation(rdb_client, vdb_client)
        category_ops = CombinedCategoryOperation(rdb_client, vdb_client)
        chunk_ops = ChunkOperation(rdb_client, vdb_client)
        
        print("步骤1: 创建文档")
        test_document = DocumentCreate(
            knowledge_id="test-combined-kb-001",
            doc_name="组合测试文档.pdf",
            doc_type=None,

            author="测试作者",
            vector_similarity_weight=0.8,
            similarity_threshold=0.7,
            parse_type=ParseType.PDF,
            status=DocumentStatus.PENDING,
            parse_end_time=None,
            parse_message=None,
            doc_format=DocumentFormat.PDF,
            location="/uploads/combined-test-document.pdf",
            metadata=json.dumps({"test": "combined"}),
            created_time=None,
            updated_time=None,
            is_active=True
        )
        
        doc_id = await doc_ops.create_document(test_document)
        print(f"✅ 文档创建成功: {doc_id}")
        
        print("\n步骤2: 创建文档别名")
        alias_id = await doc_ops.create_alias(
            doc_id=doc_id,
            cleaned_name="combined_test_document"
        )
        print(f"✅ 别名创建成功: {alias_id}")
        
        print("\n步骤3: 创建类别")
        cate_id = await category_ops.category_ops.create_category(
            cate_id="combined-test-category",
            cate_name="组合测试类别",
            cate_layer=1,
            parent_id=None,
            cate_status="active"
        )
        print(f"✅ 类别创建成功: {cate_id}")
        
        print("\n步骤4: 建立文档-类别关联")
        doc_cat_id = await category_ops.doc_category_ops.create_document_category(
            doc_id=doc_id,
            cate_id=cate_id,
            cate_layer=1,
            doc_name="组合测试文档.pdf",
            doc_status="active"
        )
        print(f"✅ 文档类别关联创建成功: {doc_cat_id}")
        
        print("\n步骤5: 创建分块")
        chunk_infos = [
            {"info_type": "content", "info_value": "这是组合测试文档的第一个分块内容"},
            {"info_type": "summary", "info_value": "第一个分块的摘要"},
        ]
        
        chunk_id = await chunk_ops.create_chunk_with_info(
            doc_id=doc_id,
            chapter_layer="第一章",
            chunk_infos=chunk_infos
        )
        print(f"✅ 分块创建成功: {chunk_id}")
        
        print("\n步骤6: 验证数据关联")
        # 查询文档及其别名
        aliases = await doc_ops.get_aliases_by_doc_id(doc_id)
        print(f"✅ 文档别名数量: {len(aliases)}")
        
        # 查询文档的类别关联
        doc_categories = await category_ops.doc_category_ops.get_categories_by_doc_id(doc_id)
        print(f"✅ 文档类别关联数量: {len(doc_categories)}")
        
        # 查询文档的分块
        doc_chunks = await chunk_ops.get_chunks_by_document(doc_id)
        print(f"✅ 文档分块数量: {len(doc_chunks)}")
        
        print("\n步骤7: 创建第二个文档用于关系测试")
        test_document_2 = DocumentCreate(
            knowledge_id="test-combined-kb-001",
            doc_name="组合测试文档2.pdf",
            doc_type=None,
            author="测试作者2",
            vector_similarity_weight=0.7,
            similarity_threshold=0.6,
            parse_type=ParseType.PDF,
            status=DocumentStatus.PENDING,
            parse_end_time=None,
            parse_message=None,
            doc_format=DocumentFormat.PDF,
            location="/uploads/combined-test-document-2.pdf",
            metadata=json.dumps({"test": "combined2"}),
            created_time=None,
            updated_time=None,
            is_active=True
        )
        
        doc_id_2 = await doc_ops.create_document(test_document_2)
        print(f"✅ 第二个文档创建成功: {doc_id_2}")
        
        print("\n步骤8: 创建文档关系")
        doc_rel_id = await doc_ops.create_document_relationship(
            source_doc_id=doc_id,
            target_doc_id=doc_id_2,
            rel_type="reference"
        )
        print(f"✅ 文档关系创建成功: {doc_rel_id}")
        
        print("\n步骤9: 创建第二个类别和类别关系")
        cate_id_2 = await category_ops.category_ops.create_category(
            cate_id="combined-test-category-2",
            cate_name="组合测试子类别",
            cate_layer=2,
            parent_id=cate_id,
            cate_status="active"
        )
        print(f"✅ 第二个类别创建成功: {cate_id_2}")
        
        cate_rel_id = await category_ops.category_ops.create_category_relationship(
            source_cate_id=cate_id,
            target_cate_id=cate_id_2,
            rel_type="parent_child"
        )
        print(f"✅ 类别关系创建成功: {cate_rel_id}")
        
        print("\n步骤10: 验证关系数据")
        # 查询文档关系
        doc_relationships = await doc_ops.get_document_relationships_by_source(doc_id)
        print(f"✅ 文档关系数量: {len(doc_relationships)}")
        
        # 查询类别关系
        cate_relationships = await category_ops.category_ops.get_category_relationships_by_source(cate_id)
        print(f"✅ 类别关系数量: {len(cate_relationships)}")
        
        print("\n组合操作测试完成！")
        
    except Exception as e:
        print(f"组合操作测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主测试函数"""
    print("开始RAG操作模块测试...")
    
    # 暂时注释掉单独的模块测试，专注于组合测试
    # await test_document_operations()
    # print("\n")
    
    # await test_category_operations()
    # print("\n")
    
    # await test_chunk_operations()
    # print("\n")
    
    await test_combined_operations()
    
    print("\n所有测试完成！")


if __name__ == "__main__":
    asyncio.run(main()) 
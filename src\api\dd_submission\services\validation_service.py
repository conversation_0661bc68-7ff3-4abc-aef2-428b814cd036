#!/usr/bin/env python3
"""
验证服务

提供统一的数据验证功能
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class ValidationService:
    """验证服务"""
    
    def __init__(self, rdb_client):
        """初始化验证服务"""
        self.rdb_client = rdb_client
        
        # 验证配置
        self.allowed_steps = ["报表解读", "义务解读", "业务解读", "IT解读"]
        self.allowed_update_fields = ["dr22", "bdr01", "bdr02", "bdr03", "bdr04"]
        self.submission_type_mapping = {"SUBMISSION": "ITEM", "RANGE": "TABLE"}
    
    def validate_report_code(self, report_code: str) -> Tuple[bool, str]:
        """验证report_code格式"""
        try:
            if not report_code or not report_code.strip():
                return False, "report_code不能为空"
            
            # 基本格式检查
            if len(report_code.strip()) > 200:
                return False, "report_code长度不能超过200字符"
            
            # 检查特殊字符（允许字母、数字、下划线、点号）
            if not re.match(r'^[a-zA-Z0-9_.-]+$', report_code.strip()):
                return False, "report_code只能包含字母、数字、下划线、点号"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"验证report_code失败: {e}")
            return False, f"验证失败: {str(e)}"
    
    def validate_dept_id(self, dept_id: str) -> Tuple[bool, str]:
        """验证dept_id格式"""
        try:
            if not dept_id or not dept_id.strip():
                return False, "dept_id不能为空"
            
            if len(dept_id.strip()) > 50:
                return False, "dept_id长度不能超过50字符"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"验证dept_id失败: {e}")
            return False, f"验证失败: {str(e)}"
    
    def validate_step(self, step: str) -> Tuple[bool, str]:
        """验证step值"""
        try:
            if step not in self.allowed_steps:
                return False, f"step必须是以下值之一: {self.allowed_steps}"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"验证step失败: {e}")
            return False, f"验证失败: {str(e)}"
    
    def validate_data_array(self, data: List[Dict[str, Any]]) -> Tuple[bool, str]:
        """验证data数组"""
        try:
            if not data:
                return False, "data不能为空"
            
            if len(data) > 10000:
                return False, "data数组长度不能超过10000"
            
            # 验证每个数据项
            for i, item in enumerate(data):
                valid, error = self.validate_data_item(item)
                if not valid:
                    return False, f"data[{i}]: {error}"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"验证data数组失败: {e}")
            return False, f"验证失败: {str(e)}"
    
    def validate_data_item(self, item: Dict[str, Any]) -> Tuple[bool, str]:
        """验证单个数据项"""
        try:
            # 检查entry_id
            if 'entry_id' not in item:
                return False, "缺少entry_id字段"
            
            if not item['entry_id'] or not str(item['entry_id']).strip():
                return False, "entry_id不能为空"
            
            if len(str(item['entry_id']).strip()) > 100:
                return False, "entry_id长度不能超过100字符"
            
            # 检查entry_type（可选）
            if 'entry_type' in item and item['entry_type']:
                if item['entry_type'] not in ['范围项', '填报项', 'ITEM', 'TABLE']:
                    return False, f"entry_type必须是以下值之一: ['范围项', '填报项', 'ITEM', 'TABLE']"
            
            # 检查更新字段
            has_update_fields = False
            for field in self.allowed_update_fields:
                field_upper = field.upper()
                if field_upper in item:
                    has_update_fields = True
                    
                    # 验证字段值
                    valid, error = self._validate_field_value(field_upper, item[field_upper])
                    if not valid:
                        return False, f"{field_upper}字段验证失败: {error}"
            
            if not has_update_fields:
                return False, f"至少需要包含一个可更新字段: {[f.upper() for f in self.allowed_update_fields]}"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"验证数据项失败: {e}")
            return False, f"验证失败: {str(e)}"
    
    def _validate_field_value(self, field_name: str, value: Any) -> Tuple[bool, str]:
        """验证字段值"""
        try:
            if value is None:
                return True, ""  # 允许None值
            
            if field_name in ['DR22', 'BDR01']:
                # 数组字段
                if isinstance(value, list):
                    if len(value) > 10:
                        return False, "数组长度不能超过10"
                    
                    for item in value:
                        if not isinstance(item, str) or len(item) > 50:
                            return False, "数组元素必须是字符串且长度不超过50"
                    
                    return True, ""
                elif isinstance(value, str):
                    if len(value) > 50:
                        return False, "字符串长度不能超过50"
                    return True, ""
                else:
                    return False, "必须是字符串或字符串数组"
            
            elif field_name in ['BDR02', 'BDR03', 'BDR04']:
                # 字符串字段
                if isinstance(value, str):
                    if len(value) > 200:
                        return False, "字符串长度不能超过200"
                    return True, ""
                elif value is None:
                    return True, ""
                else:
                    return False, "必须是字符串"
            
            else:
                return False, f"未知字段: {field_name}"
                
        except Exception as e:
            logger.error(f"验证字段值失败: {e}")
            return False, f"验证失败: {str(e)}"
    
    async def validate_record_exists(self, 
                                   version: str, 
                                   dept_id: str, 
                                   submission_id: str) -> Tuple[bool, str]:
        """验证记录是否存在"""
        try:
            sql = """
            SELECT COUNT(*) as count 
            FROM biz_dd_post_distribution 
            WHERE version = %s AND dept_id = %s AND submission_id = %s
            """
            
            result = await self.rdb_client.afetch_one(sql, (version, dept_id, submission_id))
            
            if result.success and result.data and result.data['count'] > 0:
                return True, ""
            else:
                return False, f"记录不存在: version={version}, dept_id={dept_id}, submission_id={submission_id}"
                
        except Exception as e:
            logger.error(f"验证记录存在性失败: {e}")
            return False, f"验证失败: {str(e)}"
    
    async def validate_version_exists(self, version: str) -> Tuple[bool, str]:
        """验证version是否存在于biz_dd_pre表"""
        try:
            sql = "SELECT COUNT(*) as count FROM biz_dd_pre_distribution WHERE version = %s"
            result = await self.rdb_client.afetch_one(sql, (version,))
            
            if result.success and result.data and result.data['count'] > 0:
                return True, ""
            else:
                return False, f"version不存在: {version}"
                
        except Exception as e:
            logger.error(f"验证version存在性失败: {e}")
            return False, f"验证失败: {str(e)}"
    
    def normalize_field_value(self, field_name: str, value: Any) -> Any:
        """标准化字段值"""
        try:
            if value is None:
                return None
            
            if field_name in ['DR22', 'BDR01']:
                # 数组字段标准化
                if isinstance(value, list):
                    return [str(item).strip() for item in value if item]
                elif isinstance(value, str):
                    return [value.strip()] if value.strip() else []
                else:
                    return [str(value).strip()] if str(value).strip() else []
            
            elif field_name in ['BDR02', 'BDR03', 'BDR04']:
                # 字符串字段标准化
                return str(value).strip() if value else ""
            
            else:
                return value
                
        except Exception as e:
            logger.error(f"标准化字段值失败: {e}")
            return value
    
    def convert_submission_type(self, submission_type: str) -> str:
        """转换submission_type"""
        return self.submission_type_mapping.get(submission_type, submission_type)
    
    def validate_business_submission_request(self, report_code: str) -> Tuple[bool, str]:
        """验证业务报送请求"""
        return self.validate_report_code(report_code)
    
    def validate_data_backfill_request(self, 
                                     report_code: str, 
                                     dept_id: str, 
                                     step: str, 
                                     data: List[Dict[str, Any]]) -> Tuple[bool, str]:
        """验证数据回填请求"""
        # 验证report_code
        valid, error = self.validate_report_code(report_code)
        if not valid:
            return False, f"report_code验证失败: {error}"
        
        # 验证dept_id
        valid, error = self.validate_dept_id(dept_id)
        if not valid:
            return False, f"dept_id验证失败: {error}"
        
        # 验证step
        valid, error = self.validate_step(step)
        if not valid:
            return False, f"step验证失败: {error}"
        
        # 验证data
        valid, error = self.validate_data_array(data)
        if not valid:
            return False, f"data验证失败: {error}"
        
        return True, ""

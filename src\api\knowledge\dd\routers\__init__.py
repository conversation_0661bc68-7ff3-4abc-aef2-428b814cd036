"""
DD系统API路由模块

包含所有DD系统的API路由定义，按功能模块组织：
- departments: 部门管理API
- submissions: 填报数据管理API  
- search: 搜索功能API
- distribution: 分发数据管理API
- reports: 报表数据管理API
- system: 系统管理API（健康检查、概览等）
"""

from .departments import router as departments_router
from .submissions import router as submissions_router
from .search import router as search_router
from .distribution import router as distribution_router
from .reports import router as reports_router
from .system import router as system_router

__all__ = [
    "departments_router",
    "submissions_router", 
    "search_router",
    "distribution_router",
    "reports_router",
    "system_router",
]

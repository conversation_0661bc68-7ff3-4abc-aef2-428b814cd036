# -*- coding: utf-8 -*-
"""
SchemaGenerator 现代化API示例

展示所有4种数据加载方法和2种prompt生成方法的完整工作流程
使用真实的数据库连接和中文表名
"""

import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..','..'))

from generate import SchemaGenerator
from service import get_client

# 使用现有的数据库客户端配置
client = get_client("database.rdbs.mysql")


async def demo_from_table_names():
    """演示 from_table_names() 方法的完整工作流程"""
    print("🏦 演示 from_table_names() - 从表名加载数据")
    print("=" * 60)

    # 步骤1: 创建生成器，使用现有客户端
    generator = SchemaGenerator("source", client=client)

    # 步骤2: 从表名加载数据
    table_names = ["贷款信息表", "交易记录表", "客户信息表"]
    print(f"\n📋 加载表: {table_names}")
    await generator.from_table_names(table_names, column_limit=6)
    print("✅ 数据加载完成")

    # 步骤3: 生成简单prompt列表
    print("\n📄 生成简单prompt列表...")
    simple_prompts = generator.split(token_size=7500)
    print(f"✅ 生成了 {len(simple_prompts)} 个prompt片段")

    for i, prompt in enumerate(simple_prompts):
        print(f"   Prompt {i+1}: {len(prompt)} 字符")
        print(f"   预览: {prompt[:100]}...")

    # 步骤4: 生成带映射信息的prompt列表
    print("\n🎯 生成带映射信息的prompt列表...")
    mapping_prompts = generator.get_prompt_list_with_mappings(token_size=7500)
    print(f"✅ 生成了 {len(mapping_prompts)} 个带映射的prompt片段")

    for i, item in enumerate(mapping_prompts):
        print(f"\n📊 Prompt片段 {i+1}:")
        print(f"   Prompt长度: {len(item['prompt'])} 字符")
        print(f"   数据库映射: {item['db_to_tables']}")
        print(f"   表列映射: {item['table_to_columns']}")

        # 实际使用示例
        # send_to_llm(item['prompt'])
        # process_db_mappings(item['db_to_tables'])
        # process_table_mappings(item['table_to_columns'])

    return mapping_prompts


async def demo_from_table_ids():
    """演示 from_table_ids() 方法的完整工作流程"""
    print("\n\n🔢 演示 from_table_ids() - 从表ID加载数据")
    print("=" * 60)

    # 步骤1: 创建生成器
    generator = SchemaGenerator("source", client=client)

    # 步骤2: 从表ID加载数据（使用实际的表ID）
    table_ids = [34, 65, 78]  # 假设这些是真实的表ID
    print(f"\n🔢 加载表ID: {table_ids}")
    await generator.from_table_ids(table_ids, column_limit=5)
    print("✅ 数据加载完成")

    # 步骤3: 生成prompt并展示映射信息
    print("\n🎯 生成带映射的prompt...")
    mapping_prompts = generator.get_prompt_list_with_mappings(token_size=6000)
    print(f"✅ 生成了 {len(mapping_prompts)} 个prompt片段")

    for i, item in enumerate(mapping_prompts):
        print(f"\n📊 片段 {i+1} 映射信息:")

        # 显示数据库到表的映射
        for db_name, tables in item['db_to_tables'].items():
            print(f"   数据库 '{db_name}': {len(tables)} 个表 {tables}")

        # 显示表到列的映射
        for table_name, columns in item['table_to_columns'].items():
            print(f"   表 '{table_name}': {len(columns)} 列 {columns[:3]}...")

    return mapping_prompts


async def demo_from_column_ids():
    """演示 from_column_ids() 方法的完整工作流程"""
    print("\n\n📊 演示 from_column_ids() - 从列ID加载数据")
    print("=" * 60)

    # 步骤1: 创建生成器
    generator = SchemaGenerator("source", client=client)

    # 步骤2: 从列ID加载数据（使用实际的列ID）
    column_ids = [125, 127, 129, 130, 145, 150]  # 假设这些是真实的列ID
    print(f"\n📊 加载列ID: {column_ids}")
    await generator.from_column_ids(column_ids)
    print("✅ 数据加载完成")

    # 步骤3: 生成简单prompt
    print("\n📄 生成简单prompt...")
    simple_prompts = generator.split(token_size=5000)
    print(f"✅ 生成了 {len(simple_prompts)} 个prompt")

    # 步骤4: 生成带映射的prompt
    print("\n🎯 生成带映射的prompt...")
    mapping_prompts = generator.get_prompt_list_with_mappings(token_size=5000)

    for i, item in enumerate(mapping_prompts):
        print(f"\n📋 片段 {i+1} 详细信息:")
        print(f"   涉及数据库: {list(item['db_to_tables'].keys())}")
        print(f"   涉及表: {list(item['table_to_columns'].keys())}")

        # 统计信息
        total_columns = sum(len(cols) for cols in item['table_to_columns'].values())
        print(f"   总列数: {total_columns}")
        print(f"   Prompt大小: {len(item['prompt'])} 字符")

    return mapping_prompts


async def demo_from_candidate_columns():
    """演示 from_candidate_columns() 方法的完整工作流程"""
    print("\n\n🎯 演示 from_candidate_columns() - 从候选列字典加载数据")
    print("=" * 60)

    # 步骤1: 创建生成器
    generator = SchemaGenerator("source", client=client)

    # 步骤2: 定义候选列字典（使用中文表名和实际列名）
    candidate_columns = {
        "贷款信息表": ["loan_id", "customer_id", "loan_amount", "interest_rate", "loan_status"],
        "交易记录表": ["transaction_id", "account_id", "amount", "transaction_type", "transaction_date"],
        "客户信息表": ["customer_id", "customer_name", "phone", "email", "address"]
    }

    print(f"\n🎯 加载候选列:")
    for table, columns in candidate_columns.items():
        print(f"   {table}: {columns}")

    await generator.from_candidate_columns(candidate_columns)
    print("✅ 数据加载完成")

    # 步骤3: 生成带映射的prompt
    print("\n🎯 生成带映射的prompt...")
    mapping_prompts = generator.get_prompt_list_with_mappings(token_size=8000)
    print(f"✅ 生成了 {len(mapping_prompts)} 个prompt片段")

    for i, item in enumerate(mapping_prompts):
        print(f"\n📊 片段 {i+1} 完整映射:")

        # 详细显示映射信息
        print("   数据库到表映射:")
        for db_name, tables in item['db_to_tables'].items():
            print(f"     {db_name}: {tables}")

        print("   表到列映射:")
        for table_name, columns in item['table_to_columns'].items():
            print(f"     {table_name}: {columns}")

        print(f"   Prompt长度: {len(item['prompt'])} 字符")

        # 模拟实际使用
        print("   🚀 模拟LLM调用:")
        print(f"     send_to_llm(prompt) # {len(item['prompt'])} 字符")
        print(f"     process_mappings({len(item['db_to_tables'])} dbs, {len(item['table_to_columns'])} tables)")

    return mapping_prompts


async def demo_complete_workflow():
    """演示完整的LLM工作流程 - 从数据加载到prompt处理"""
    print("\n\n🌍 完整LLM工作流程演示")
    print("=" * 60)

    # 步骤1: 创建生成器并加载银行业务数据
    generator = SchemaGenerator("source", client=client)

    print("\n🏦 加载银行核心业务表...")
    business_tables = ["贷款信息表", "交易记录表", "客户信息表", "账户信息表", "风险评估表"]
    await generator.from_table_names(business_tables, column_limit=8)
    print(f"✅ 成功加载 {len(business_tables)} 个业务表")

    # 步骤2: 生成适合LLM的prompt片段
    print("\n🤖 生成LLM prompt片段...")
    llm_prompts = generator.get_prompt_list_with_mappings(token_size=6000)
    print(f"✅ 为 {len(business_tables)} 个表生成了 {len(llm_prompts)} 个LLM prompt")

    # 步骤3: 模拟完整的LLM处理流程
    print("\n🚀 模拟LLM处理流程:")

    llm_results = []
    for i, prompt_item in enumerate(llm_prompts):
        print(f"\n📋 处理Prompt {i+1}/{len(llm_prompts)}:")

        # 分析当前prompt的覆盖范围
        db_count = len(prompt_item['db_to_tables'])
        table_count = len(prompt_item['table_to_columns'])
        column_count = sum(len(cols) for cols in prompt_item['table_to_columns'].values())

        print(f"   📊 覆盖范围: {db_count}个数据库, {table_count}个表, {column_count}个列")
        print(f"   📏 Prompt大小: {len(prompt_item['prompt'])} 字符")

        # 显示具体的映射信息
        print("   🗂️ 数据库映射:")
        for db_name, tables in prompt_item['db_to_tables'].items():
            print(f"     📁 {db_name}: {tables}")

        print("   📋 表列映射:")
        for table_name, columns in prompt_item['table_to_columns'].items():
            print(f"     📊 {table_name}: {len(columns)}列 {columns[:2]}...")

        # 模拟LLM调用和响应处理
        print("   🤖 模拟LLM调用:")
        print(f"     → send_to_llm(prompt) # 发送 {len(prompt_item['prompt'])} 字符")
        # llm_response = send_to_llm(prompt_item['prompt'])

        print("   📝 模拟响应处理:")
        # 根据映射信息处理LLM响应
        for db_name, tables in prompt_item['db_to_tables'].items():
            print(f"     → process_database_response('{db_name}', {tables})")

        for table_name, columns in prompt_item['table_to_columns'].items():
            print(f"     → process_table_response('{table_name}', {len(columns)} columns)")

        # 保存处理结果
        result = {
            'prompt_index': i + 1,
            'db_mappings': prompt_item['db_to_tables'],
            'table_mappings': prompt_item['table_to_columns'],
            'prompt_size': len(prompt_item['prompt']),
            'coverage': {
                'databases': db_count,
                'tables': table_count,
                'columns': column_count
            }
            # 'llm_response': llm_response  # 实际使用时添加
        }
        llm_results.append(result)

        print(f"   ✅ Prompt {i+1} 处理完成")

    # 步骤4: 汇总处理结果
    print(f"\n📊 处理结果汇总:")
    total_dbs = len(set().union(*[r['db_mappings'].keys() for r in llm_results]))
    total_tables = len(set().union(*[r['table_mappings'].keys() for r in llm_results]))
    total_columns = sum(r['coverage']['columns'] for r in llm_results)

    print(f"   🎯 总覆盖范围: {total_dbs}个数据库, {total_tables}个表, {total_columns}个列")
    print(f"   📄 总prompt数: {len(llm_results)}个")
    print(f"   📏 平均prompt大小: {sum(r['prompt_size'] for r in llm_results) // len(llm_results)} 字符")

    print("\n🎉 完整工作流程演示完成！")
    return llm_results


async def demo_practical_usage_patterns():
    """演示实际使用模式和最佳实践"""
    print("\n\n💡 实际使用模式和最佳实践")
    print("=" * 60)

    # 模式1: 快速原型开发
    print("\n🚀 模式1: 快速原型开发")
    generator = SchemaGenerator("source", client=client)
    await generator.from_table_names(["贷款信息表", "客户信息表"], column_limit=3)
    quick_prompts = generator.split(token_size=5000)
    print(f"   ✅ 快速生成 {len(quick_prompts)} 个原型prompt")

    # 模式2: 生产环境批量处理
    print("\n🏭 模式2: 生产环境批量处理")
    production_generator = SchemaGenerator("source", client=client)

    # 分批处理大量表
    table_batches = [
        ["贷款信息表", "交易记录表"],
        ["客户信息表", "账户信息表"],
        ["风险评估表", "审批记录表"]
    ]

    all_production_prompts = []
    for i, batch in enumerate(table_batches):
        print(f"   📦 处理批次 {i+1}: {batch}")
        await production_generator.from_table_names(batch, column_limit=6)
        batch_prompts = production_generator.get_prompt_list_with_mappings(token_size=7000)
        all_production_prompts.extend(batch_prompts)
        print(f"   ✅ 批次 {i+1} 生成 {len(batch_prompts)} 个prompt")

    print(f"   🎯 生产环境总计: {len(all_production_prompts)} 个prompt")

    # 模式3: 精确列选择
    print("\n🎯 模式3: 精确列选择")
    precise_generator = SchemaGenerator("source", client=client)

    # 精确指定需要的列
    precise_columns = {
        "贷款信息表": ["loan_id", "customer_id", "loan_amount", "loan_status"],
        "客户信息表": ["customer_id", "customer_name", "credit_score", "risk_level"]
    }

    await precise_generator.from_candidate_columns(precise_columns)
    precise_prompts = precise_generator.get_prompt_list_with_mappings(token_size=6000)
    print(f"   ✅ 精确选择生成 {len(precise_prompts)} 个prompt")

    # 显示精确选择的效果
    for prompt_item in precise_prompts:
        for table, columns in prompt_item['table_to_columns'].items():
            print(f"   📊 {table}: 精确包含 {columns}")

    print("\n💡 最佳实践总结:")
    print("   1. 快速原型: 使用 from_table_names() + split()")
    print("   2. 生产环境: 使用 get_prompt_list_with_mappings() 获取完整映射")
    print("   3. 精确控制: 使用 from_candidate_columns() 指定具体列")
    print("   4. 大数据量: 分批处理，控制 token_size 和 column_limit")


# 所有旧函数已移除，现在只保留现代化API演示


async def main():
    """主函数 - 演示所有现代化API功能"""
    print("🚀 SchemaGenerator 现代化API完整演示")
    print("=" * 80)
    print("展示所有4种数据加载方法和2种prompt生成方法")
    print("使用真实数据库连接和中文表名")
    print("=" * 80)

    try:
        # 演示所有4种数据加载方法
        print("\n📋 第一部分：4种数据加载方法演示")
        await demo_from_table_names()      # 方法1：从表名加载
        await demo_from_table_ids()        # 方法2：从表ID加载
        await demo_from_column_ids()       # 方法3：从列ID加载
        await demo_from_candidate_columns() # 方法4：从候选列字典加载

        # 演示完整工作流程
        print("\n🌍 第二部分：完整工作流程演示")
        await demo_complete_workflow()     # 完整的LLM处理流程

        # 演示实际使用模式
        print("\n💡 第三部分：实际使用模式")
        await demo_practical_usage_patterns() # 最佳实践和使用模式

        # 总结
        print("\n\n🎉 所有演示完成！")
        print("\n📚 API总结:")
        print("  📋 数据加载方法 (4种):")
        print("    - from_table_names(table_names, column_limit=5)")
        print("    - from_table_ids(table_ids, column_limit=5)")
        print("    - from_column_ids(column_ids)")
        print("    - from_candidate_columns(candidate_dict)")
        print("\n  📄 Prompt生成方法 (2种):")
        print("    - split(token_size=7500) # 简单prompt列表")
        print("    - get_prompt_list_with_mappings(token_size=7500) # 带映射信息")
        print("\n  🎯 核心特性:")
        print("    - ✅ 现代化API，无向后兼容负担")
        print("    - ✅ 完整的数据库到表映射")
        print("    - ✅ 详细的表到列映射")
        print("    - ✅ 智能token分割")
        print("    - ✅ 支持中文表名和真实业务场景")
        print("    - ✅ 完整的工作流程支持")

    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        print("\n💡 提示：确保数据库连接正常且表名/ID存在")


if __name__ == "__main__":
    print("🔗 使用真实数据库连接运行演示")
    print("确保数据库中存在相应的表和数据")
    print("-" * 50)

    asyncio.run(main())

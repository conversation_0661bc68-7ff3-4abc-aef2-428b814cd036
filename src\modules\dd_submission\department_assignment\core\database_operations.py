"""
数据库操作模块 - 改进版本

处理部门职责分配结果的数据库入库操作
使用统一的DDCrud接口替代直接的数据库客户端调用

改进说明：
- 统一使用DDCrud接口进行数据库操作
- 保持所有现有接口和返回格式不变
- 增强错误处理和重试机制
- 提高代码可维护性和一致性
"""

import time
import logging
import os
import functools
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class BatchOperationConfig:
    """批量操作配置"""
    # 批量插入配置
    default_batch_size: int = 500
    max_concurrency: int = 3
    timeout_per_batch: float = 60.0

    # 降级处理配置
    fallback_batch_size: int = 100
    max_retry_attempts: int = 3
    retry_delay: float = 1.0

    # 性能监控配置
    enable_performance_logging: bool = True
    slow_operation_threshold: float = 10.0

    @classmethod
    def from_env(cls) -> 'BatchOperationConfig':
        """从环境变量加载配置"""
        return cls(
            default_batch_size=int(os.getenv('BATCH_SIZE', 500)),
            max_concurrency=int(os.getenv('MAX_CONCURRENCY', 3)),
            timeout_per_batch=float(os.getenv('TIMEOUT_PER_BATCH', 60.0)),
            fallback_batch_size=int(os.getenv('FALLBACK_BATCH_SIZE', 100)),
            max_retry_attempts=int(os.getenv('MAX_RETRY_ATTEMPTS', 3)),
            retry_delay=float(os.getenv('RETRY_DELAY', 1.0)),
            enable_performance_logging=os.getenv('ENABLE_PERF_LOGGING', 'true').lower() == 'true',
            slow_operation_threshold=float(os.getenv('SLOW_OP_THRESHOLD', 10.0))
        )


def monitor_batch_operation(operation_name: str):
    """批量操作性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            start_time = time.time()

            try:
                result = await func(*args, **kwargs)

                # 记录成功指标
                execution_time = time.time() - start_time
                record_count = result if isinstance(result, int) else getattr(result, 'count', 'unknown')

                logger.info(
                    f"批量操作成功: {operation_name}, "
                    f"耗时: {execution_time:.2f}秒, "
                    f"处理记录数: {record_count}"
                )

                # 慢操作告警
                if execution_time > 10.0:  # 可配置阈值
                    logger.warning(
                        f"慢操作检测: {operation_name} 耗时 {execution_time:.2f}秒"
                    )

                return result

            except Exception as e:
                # 记录失败指标
                execution_time = time.time() - start_time
                logger.error(
                    f"批量操作失败: {operation_name}, "
                    f"耗时: {execution_time:.2f}秒, "
                    f"错误: {str(e)}"
                )
                raise

        return wrapper
    return decorator

from ..infrastructure.models import BatchAssignmentItem, BatchProcessingResult
# 引入最新的CRUD实现
from modules.knowledge.dd.crud import DDCrud
from modules.knowledge.dd.shared.constants import DDTableNames
from modules.knowledge.dd.shared.exceptions import DDError


class DatabaseOperationError(Exception):
    """数据库操作错误"""
    pass


class PostDistributionDBOperations:
    """分发后数据库操作类"""

    def __init__(self, rdb_client: Any, vdb_client: Any = None, config: Optional[BatchOperationConfig] = None):
        """
        初始化数据库操作类

        Args:
            rdb_client: 关系型数据库客户端
            vdb_client: 向量数据库客户端（可选）
            config: 批量操作配置，如果为None则使用默认配置
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        # 使用最新的CRUD实现
        self.dd_crud = DDCrud(rdb_client, vdb_client)
        self.table_name = DDTableNames.BIZ_POST_DISTRIBUTION
        self.config = config or BatchOperationConfig.from_env()
    
    async def save_batch_results(
        self,
        batch_result: BatchProcessingResult,
        pre_distribution_records: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        保存批量处理结果到数据库（使用事务确保原子性）

        Args:
            batch_result: 批量处理结果
            pre_distribution_records: 原始分发前数据记录

        Returns:
            保存操作结果统计
        """
        start_time = time.time()
        save_stats = {
            "total_items": len(batch_result.assignment_items),
            "successful_saves": 0,
            "failed_saves": 0,
            "save_time_ms": 0.0,
            "errors": [],
            "transaction_used": True
        }

        try:
            logger.info(f"开始保存批量处理结果到数据库: {len(batch_result.assignment_items)}条记录")

            # 创建submission_id到原始记录的映射
            pre_records_map = {
                record.get('submission_id'): record
                for record in pre_distribution_records
            }

            # 使用事务确保批量操作的原子性
            async with self.rdb_client.atransaction():
                logger.debug("开始数据库事务")

                # 批量操作优化：收集所有记录后批量处理
                batch_records = []
                invalid_items = []

                # 第一步：收集和验证所有记录
                for assignment_item in batch_result.assignment_items:
                    # 获取原始记录
                    pre_record = pre_records_map.get(assignment_item.entry_id)
                    if not pre_record:
                        error_msg = f"未找到submission_id={assignment_item.entry_id}的原始记录"
                        logger.warning(error_msg)
                        save_stats["errors"].append(error_msg)
                        save_stats["failed_saves"] += 1
                        invalid_items.append(assignment_item.entry_id)
                        continue

                    # 构建入库数据
                    post_record = self._build_post_distribution_record(
                        assignment_item, pre_record, batch_result
                    )
                    batch_records.append(post_record)

                # 第二步：批量处理有效记录
                if batch_records:
                    success_count = await self._batch_save_post_distributions(
                        batch_records, batch_result.version
                    )
                    save_stats["successful_saves"] += success_count

                    # 记录失败的数量
                    failed_count = len(batch_records) - success_count
                    save_stats["failed_saves"] += failed_count

                    logger.info(f"批量保存完成: 成功{success_count}条, 失败{failed_count}条, 无效{len(invalid_items)}条")
                else:
                    logger.warning("没有有效记录需要保存")

                logger.debug("数据库事务提交成功")

            save_stats["save_time_ms"] = (time.time() - start_time) * 1000

            logger.info(f"批量保存完成: 成功={save_stats['successful_saves']}, "
                       f"失败={save_stats['failed_saves']}, "
                       f"耗时={save_stats['save_time_ms']:.2f}ms")

            return save_stats

        except Exception as e:
            logger.error(f"批量保存失败，事务已回滚: {e}")
            save_stats["errors"].append(f"批量保存失败，事务已回滚: {e}")
            save_stats["save_time_ms"] = (time.time() - start_time) * 1000
            save_stats["transaction_used"] = True
            save_stats["transaction_rollback"] = True
            return save_stats
    
    def _build_post_distribution_record(
        self, 
        assignment_item: BatchAssignmentItem,
        pre_record: Dict[str, Any],
        batch_result: BatchProcessingResult
    ) -> Dict[str, Any]:
        """
        构建分发后数据记录
        
        Args:
            assignment_item: 分配结果项
            pre_record: 原始分发前记录
            batch_result: 批量处理结果
            
        Returns:
            分发后数据记录
        """
        current_time = datetime.now()
        
        # 基础字段映射 - 完全符合真实表结构 (biz_dd_post_distribution)
        post_record = {
            # 必填关联字段
            "pre_distribution_id": pre_record.get("id", 0),  # 关联分发前记录ID
            "submission_id": assignment_item.entry_id,
            "submission_type": pre_record.get("submission_type", "SUBMISSION"),
            "report_type": pre_record.get("report_type", "detail"),
            "set": pre_record.get("set"),  # 可以为NULL
            "version": batch_result.version,

            # 部门分配结果 - 使用推荐的第一个部门或占位符
            "dept_id": self._extract_department_id(assignment_item),

            # DR字段 (从分发前数据继承和分配结果)
            "dr01": pre_record.get("dr01"),  # 可以为NULL
            "dr07": batch_result.dr07,  # 表名ID
            "dr22": self._format_department_list(assignment_item.DR22),  # RRMS数据提供部门

            # BDR字段 (业务解读数据需求 - 分配结果)
            "bdr01": self._format_department_list(assignment_item.BDR01),  # 数据生产/处理部门
            "bdr03": assignment_item.BDR03,  # 数据管理部门

            # 其他BDR字段设为NULL，后续可由各部门填写
            "bdr02": None,  # 数据生成/处理部门代表
            "bdr04": None,  # 数据管理部门代表

            # 时间字段由数据库自动生成 (create_time, update_time)
        }
        
        return post_record

    def _extract_department_id(self, assignment_item: BatchAssignmentItem) -> str:
        """
        从分配结果中提取部门ID

        Args:
            assignment_item: 分配结果项

        Returns:
            部门ID字符串
        """
        # 优先使用BDR03（数据管理部门）
        if assignment_item.BDR03 and assignment_item.BDR03 != "TODO_PLACEHOLDER":
            # 简单映射：将部门名称转换为部门ID
            dept_name = assignment_item.BDR03
            return self._map_department_name_to_id(dept_name)

        # 其次使用BDR01（数据生产/处理部门）的第一个
        if assignment_item.BDR01:
            dept_name = assignment_item.BDR01[0] if assignment_item.BDR01 else ""
            if dept_name and dept_name != "TODO_PLACEHOLDER":
                return self._map_department_name_to_id(dept_name)

        # 最后使用DR22（RRMS数据提供部门）的第一个
        if assignment_item.DR22:
            dept_name = assignment_item.DR22[0] if assignment_item.DR22 else ""
            if dept_name and dept_name != "TODO_PLACEHOLDER":
                return self._map_department_name_to_id(dept_name)

        # 默认使用业务部门
        return "DEPT_BUSINESS"

    def _map_department_name_to_id(self, dept_name: str) -> str:
        """
        将部门名称映射为部门ID

        Args:
            dept_name: 部门名称

        Returns:
            部门ID
        """
        # 简单的名称到ID映射
        name_to_id_mapping = {
            "RRMS数据提供部门": "DEPT_RRMS",
            "业务部门": "DEPT_BUSINESS",
            "IT部门": "DEPT_IT",
            "数据管理部门": "DEPT_DATA_MGT",
            "合规部门": "DEPT_COMPLIANCE"
        }

        # 精确匹配
        if dept_name in name_to_id_mapping:
            return name_to_id_mapping[dept_name]

        # 模糊匹配
        dept_name_lower = dept_name.lower()
        if "rrms" in dept_name_lower or "监管" in dept_name or "报送" in dept_name:
            return "DEPT_RRMS"
        elif "it" in dept_name_lower or "技术" in dept_name or "系统" in dept_name:
            return "DEPT_IT"
        elif "数据管理" in dept_name or "数据治理" in dept_name:
            return "DEPT_DATA_MGT"
        elif "合规" in dept_name or "风控" in dept_name:
            return "DEPT_COMPLIANCE"
        else:
            return "DEPT_BUSINESS"  # 默认业务部门

    def _format_department_list(self, dept_list: List[str]) -> str:
        """
        格式化部门列表为字符串
        
        Args:
            dept_list: 部门列表
            
        Returns:
            格式化的部门字符串
        """
        if not dept_list:
            return ""
        
        # 如果只有一个部门，直接返回
        if len(dept_list) == 1:
            return dept_list[0]
        
        # 多个部门用逗号分隔
        return ",".join(dept_list)
    
    def _determine_assignment_method(self, assignment_item: BatchAssignmentItem) -> str:
        """
        确定分配方法
        
        Args:
            assignment_item: 分配结果项
            
        Returns:
            分配方法标识
        """
        # 根据是否有推荐部门判断分配方法
        if assignment_item.DR22:
            return "auto_assignment"  # 自动分配
        else:
            return "manual_required"  # 需要人工分配
    
    async def _upsert_post_distribution_record(self, record: Dict[str, Any]) -> None:
        """
        插入或更新分发后数据记录（带重试机制，用于非事务操作）

        Args:
            record: 分发后数据记录
        """
        try:
            # 检查记录是否已存在
            existing_record = await self._check_existing_record(
                record["submission_id"], record.get("dr07", ""), record["version"]
            )

            if existing_record:
                # 更新现有记录
                await self._update_existing_record(record)
                logger.debug(f"更新现有记录: submission_id={record['submission_id']}")
            else:
                # 插入新记录
                await self._insert_new_record(record)
                logger.debug(f"插入新记录: submission_id={record['submission_id']}")

        except Exception as e:
            logger.error(f"入库操作失败: {e}")
            raise

    async def _upsert_post_distribution_record_in_transaction(self, record: Dict[str, Any]) -> None:
        """
        在事务内插入或更新分发后数据记录（无重试机制，依赖事务回滚）

        Args:
            record: 分发后数据记录
        """
        try:
            # 检查记录是否已存在
            existing_record = await self._check_existing_record(
                record["submission_id"], record.get("dr07", ""), record["version"]
            )

            if existing_record:
                # 更新现有记录（事务内，无重试）
                await self._update_existing_record_in_transaction(record)
                logger.debug(f"事务内更新现有记录: submission_id={record['submission_id']}")
            else:
                # 插入新记录（事务内，无重试）
                await self._insert_new_record_in_transaction(record)
                logger.debug(f"事务内插入新记录: submission_id={record['submission_id']}")

        except Exception as e:
            logger.error(f"事务内入库操作失败: {e}")
            raise
    
    async def _check_existing_record(
        self,
        submission_id: str,
        dr07: str,
        version: str
    ) -> Optional[Dict[str, Any]]:
        """
        检查记录是否已存在 - 使用DDCrud统一接口

        Args:
            submission_id: 填报项ID
            dr07: 数据需求编号
            version: 版本号

        Returns:
            现有记录（如果存在）
        """
        try:
            # 使用DDCrud的查询接口替代直接SQL查询，现在支持version参数
            records = await self.dd_crud.list_post_distributions(
                submission_id=submission_id,
                version=version,
                limit=1
            )

            return records[0] if records else None

        except Exception as e:
            logger.error(f"检查现有记录失败: {e}")
            # 使用DDCrud的统一错误处理
            raise DatabaseOperationError(f"检查现有记录失败: {e}")
    
    async def _insert_new_record(self, record: Dict[str, Any]) -> None:
        """
        插入新记录 - 使用DDCrud统一接口（内置重试机制）

        Args:
            record: 要插入的记录
        """
        try:
            # 使用DDCrud的统一插入接口，内置重试机制和锁处理
            result = await self.dd_crud.create_post_distribution(record)
            logger.debug(f"插入记录成功: {result}")
            return result

        except Exception as e:
            logger.error(f"插入记录失败: {e}")
            # 使用DDCrud的统一错误处理
            raise DatabaseOperationError(f"插入记录失败: {e}")

    async def _insert_new_record_in_transaction(self, record: Dict[str, Any]) -> None:
        """
        在事务内插入新记录 - 使用DDCrud统一接口

        Args:
            record: 要插入的记录
        """
        try:
            # 使用DDCrud的事务内插入接口
            result = await self.dd_crud.create_post_distribution(record)
            logger.debug(f"事务内插入记录成功: {result}")
            return result

        except Exception as e:
            logger.error(f"事务内插入记录失败: {e}")
            raise DatabaseOperationError(f"事务内插入记录失败: {e}")

    async def _update_existing_record_in_transaction(self, record: Dict[str, Any]) -> None:
        """
        在事务内更新现有记录 - 使用DDCrud统一接口

        Args:
            record: 要更新的记录
        """
        # 移除不需要更新的字段
        update_data = record.copy()
        update_data.pop("created_at", None)  # 不更新创建时间

        try:
            # 使用DDCrud的事务内更新接口
            result = await self.dd_crud.update_post_distributions(
                update_data,
                conditions={
                    "submission_id": record["submission_id"],
                    "version": record["version"]
                }
            )
            logger.debug(f"事务内更新记录成功: {result}")
            return result

        except Exception as e:
            logger.error(f"事务内更新记录失败: {e}")
            raise DatabaseOperationError(f"事务内更新记录失败: {e}")
    
    async def _update_existing_record(self, record: Dict[str, Any]) -> None:
        """
        更新现有记录 - 使用DDCrud统一接口

        Args:
            record: 要更新的记录
        """
        # 移除不需要更新的字段
        update_data = record.copy()
        update_data.pop("created_at", None)  # 不更新创建时间

        try:
            # 使用DDCrud的更新接口，内置重试和锁处理机制
            result = await self.dd_crud.update_post_distributions(
                update_data,
                conditions={
                    "submission_id": record["submission_id"],
                    "version": record["version"]
                }
            )
            logger.debug(f"更新记录成功: {result}")
            return result

        except Exception as e:
            logger.error(f"更新记录失败: {e}")
            raise DatabaseOperationError(f"更新记录失败: {e}")
    
    async def get_batch_processing_stats(
        self, 
        dr07: str, 
        version: str
    ) -> Dict[str, Any]:
        """
        获取批量处理统计信息
        
        Args:
            dr07: 数据需求编号
            version: 版本号
            
        Returns:
            处理统计信息
        """
        try:
            # 使用DDCrud获取统计信息，现在支持dr07和version参数
            records = await self.dd_crud.list_post_distributions(
                dr07=dr07,
                version=version,
                limit=1000  # 设置合理的限制
            )

            total_count = len(records)
            assigned_count = sum(1 for r in records if r.get('dr22'))
            unassigned_count = total_count - assigned_count
            assignment_rate = assigned_count / total_count if total_count > 0 else 0.0

            stats = {
                'total_count': total_count,
                'assigned_count': assigned_count,
                'unassigned_count': unassigned_count,
                'assignment_rate': assignment_rate
            }

            return {
                "total_records": stats.get("total_count", 0),
                "assigned_records": stats.get("assigned_count", 0),
                "unassigned_records": stats.get("unassigned_count", 0),
                "assignment_rate": stats.get("assignment_rate", 0.0)
            }
                
        except Exception as e:
            logger.error(f"获取处理统计信息失败: {e}")
            return {
                "total_records": 0,
                "assigned_records": 0,
                "unassigned_records": 0,
                "assignment_rate": 0.0,
                "error": str(e)
            }

    async def get_pre_distribution_data(self, report_code: str) -> List[Dict[str, Any]]:
        """获取分发前数据 - 使用DDCrud统一接口"""
        try:
            # 解析report_code (格式: G0107_beta_v1.0)
            parts = report_code.split('_')
            if len(parts) >= 3:
                dr07 = parts[0]  # G0107
                version = parts[-1]  # v1.0
            else:
                dr07 = report_code
                version = 'v1.0'

            # 使用DDCrud的查询接口，现在支持dr07参数
            records = await self.dd_crud.list_pre_distributions(
                version=version,
                dr07=dr07,
                limit=1000  # 设置合理的限制
            )

            if records:
                logger.info(f"找到 {len(records)} 条分发前数据: dr07={dr07}, version={version}")
                return records
            else:
                logger.warning(f"未找到分发前数据: dr07={dr07}, version={version}")
                return []

        except Exception as e:
            logger.error(f"获取分发前数据失败: {e}")
            raise DatabaseOperationError(f"获取分发前数据失败: {e}")

    async def save_assignment_results(
        self,
        report_code: str,
        pre_distribution_data: List[Dict[str, Any]],
        assignment_results: List[Dict[str, Any]]
    ) -> int:
        """保存分配结果到biz_dd_post_distribution表"""
        try:
            # 批量操作优化：收集所有数据后批量处理
            batch_save_data = []

            # 第一步：收集所有保存数据
            for i, (pre_data, result) in enumerate(zip(pre_distribution_data, assignment_results)):
                try:
                    # 构建保存数据
                    save_data = {
                        'pre_distribution_id': pre_data.get('id'),
                        'submission_id': result.get('entry_id', pre_data.get('submission_id')),
                        'submission_type': pre_data.get('submission_type', 'REGULAR'),
                        'report_type': pre_data.get('report_type', 'MONTHLY'),
                        'set': pre_data.get('set'),
                        'version': pre_data.get('version', 'v1.0'),
                        'dept_id': result['DR22'][0] if result.get('DR22') else 'DEPT_BUSINESS',
                        'create_time': datetime.now(),
                        'update_time': datetime.now(),
                        'dr01': pre_data.get('dr01'),
                        'dr07': pre_data.get('dr07'),
                        'dr22': result['DR22'][0] if result.get('DR22') else 'DEPT_BUSINESS',
                        'bdr01': result['BDR01'][0] if result.get('BDR01') else 'DEPT_BUSINESS',
                        'bdr03': result.get('BDR03', '智能推荐结果')
                    }
                    batch_save_data.append(save_data)

                except Exception as e:
                    logger.error(f"构建保存数据失败 {result.get('entry_id')}: {e}")
                    continue

            # 第二步：批量保存数据
            if batch_save_data:
                saved_count = await self._batch_upsert_assignment_results(batch_save_data)
                logger.info(f"批量分配结果保存完成: {saved_count}/{len(assignment_results)} 条记录")
                return saved_count
            else:
                logger.warning("没有有效的分配结果需要保存")
                return 0

        except Exception as e:
            logger.error(f"保存分配结果失败: {e}")
            raise DatabaseOperationError(f"保存分配结果失败: {e}")

    async def update_assignment_by_user_feedback(
        self,
        report_code: str,
        entry_id: str,
        feedback_data: Dict[str, Any]
    ) -> bool:
        """根据用户反馈更新分配结果"""
        try:
            # 解析report_code
            parts = report_code.split('_')
            if len(parts) >= 3:
                dr07 = parts[0]
                version = parts[-1]
            else:
                dr07 = report_code
                version = 'v1.0'

            # 构建更新数据
            update_fields = []
            update_params = {'entry_id': entry_id, 'dr07': dr07, 'version': version}

            # 处理用户修改的字段
            if 'A4.DR22' in feedback_data:
                dept_ids = feedback_data['A4.DR22']
                if dept_ids and len(dept_ids) > 0:
                    update_fields.append("dr22 = :dr22")
                    update_fields.append("dept_id = :dept_id")
                    update_params['dr22'] = dept_ids[0]
                    update_params['dept_id'] = dept_ids[0]

            if 'B1.BDR01' in feedback_data:
                dept_ids = feedback_data['B1.BDR01']
                if dept_ids and len(dept_ids) > 0:
                    update_fields.append("bdr01 = :bdr01")
                    update_params['bdr01'] = dept_ids[0]

            if 'B1.BDR03' in feedback_data:
                dept_ids = feedback_data['B1.BDR03']
                if dept_ids and len(dept_ids) > 0:
                    update_fields.append("bdr03 = :bdr03")
                    update_params['bdr03'] = dept_ids[0]

            if not update_fields:
                logger.warning(f"用户反馈中没有需要更新的字段: {entry_id}")
                return False

            # 添加更新时间
            update_fields.append("update_time = :update_time")
            update_params['update_time'] = datetime.now()

            # 使用DDCrud执行更新
            update_data = {k: v for k, v in update_params.items()
                          if k not in ['entry_id', 'dr07', 'version']}

            result = await self.dd_crud.update_post_distributions(
                update_data,
                conditions={
                    'submission_id': entry_id,
                    'dr07': dr07,
                    'version': version
                }
            )

            if result:
                logger.info(f"用户反馈更新成功: {entry_id}")
                return True
            else:
                logger.warning(f"用户反馈更新失败，没有找到匹配的记录: {entry_id}")
                return False

        except Exception as e:
            logger.error(f"用户反馈更新失败 {entry_id}: {e}")
            return False

    # ==================== 批量操作优化方法 ====================

    @monitor_batch_operation("batch_save_post_distributions")
    async def _batch_save_post_distributions(
        self,
        batch_records: List[Dict[str, Any]],
        version: str
    ) -> int:
        """
        批量保存post_distribution记录，支持智能upsert

        Args:
            batch_records: 要保存的记录列表
            version: 版本号

        Returns:
            成功保存的记录数量
        """
        if not batch_records:
            return 0

        try:
            # 尝试批量插入（使用配置参数）
            logger.info(f"开始批量插入{len(batch_records)}条post_distribution记录")

            result = await self.rdb_client.abatch_insert(
                table="biz_dd_post_distribution",
                data=batch_records,
                batch_size=self.config.default_batch_size,
                max_concurrency=self.config.max_concurrency,
                timeout_per_batch=self.config.timeout_per_batch
            )

            if result.success:
                logger.info(f"批量插入成功: {len(batch_records)}条记录")
                return len(batch_records)
            else:
                logger.warning(f"批量插入失败: {result.error_message}")
                # 降级到冲突处理
                return await self._handle_batch_insert_conflicts(batch_records, version)

        except Exception as e:
            logger.warning(f"批量插入异常，降级处理: {e}")
            # 降级到冲突处理
            return await self._handle_batch_insert_conflicts(batch_records, version)

    async def _handle_batch_insert_conflicts(
        self,
        batch_records: List[Dict[str, Any]],
        version: str
    ) -> int:
        """
        处理批量插入冲突，实现智能upsert

        Args:
            batch_records: 要处理的记录列表
            version: 版本号

        Returns:
            成功处理的记录数量
        """
        success_count = 0

        # 分批处理，使用配置的降级批次大小
        batch_size = self.config.fallback_batch_size
        for i in range(0, len(batch_records), batch_size):
            batch = batch_records[i:i+batch_size]

            try:
                # 尝试批量插入小批次
                result = await self.rdb_client.abatch_insert(
                    table="biz_dd_post_distribution",
                    data=batch
                )

                if result.success:
                    success_count += len(batch)
                    logger.debug(f"小批次插入成功: {len(batch)}条")
                else:
                    # 小批次失败，逐条处理
                    success_count += await self._handle_individual_upserts(batch, version)

            except Exception as e:
                logger.warning(f"小批次插入失败: {e}")
                # 逐条处理
                success_count += await self._handle_individual_upserts(batch, version)

        return success_count

    async def _handle_individual_upserts(
        self,
        records: List[Dict[str, Any]],
        version: str
    ) -> int:
        """
        逐条处理upsert操作

        Args:
            records: 要处理的记录列表
            version: 版本号

        Returns:
            成功处理的记录数量
        """
        success_count = 0

        for record in records:
            try:
                # 尝试插入
                await self.dd_crud.create_post_distribution(record)
                success_count += 1

            except DDError as e:
                # 如果是重复记录，尝试更新
                if "duplicate" in str(e).lower() or "exists" in str(e).lower():
                    try:
                        update_data = {
                            'bdr03': record.get('bdr03'),
                            'update_time': record.get('update_time')
                        }
                        await self.dd_crud.update_post_distributions(
                            update_data,
                            conditions={
                                'submission_id': record.get('submission_id'),
                                'version': version
                            }
                        )
                        success_count += 1
                        logger.debug(f"更新记录成功: {record.get('submission_id')}")

                    except Exception as update_e:
                        logger.error(f"更新记录失败: {record.get('submission_id')}, 错误: {update_e}")
                else:
                    logger.error(f"插入记录失败: {record.get('submission_id')}, 错误: {e}")
            except Exception as e:
                logger.error(f"处理记录失败: {record.get('submission_id')}, 错误: {e}")

        return success_count

    async def _batch_upsert_assignment_results(
        self,
        batch_save_data: List[Dict[str, Any]]
    ) -> int:
        """
        批量upsert分配结果，专门优化assignment_results保存

        Args:
            batch_save_data: 要保存的分配结果数据列表

        Returns:
            成功保存的记录数量
        """
        if not batch_save_data:
            return 0

        try:
            # 尝试批量插入
            logger.info(f"开始批量保存{len(batch_save_data)}条分配结果")

            result = await self.rdb_client.abatch_insert(
                table="biz_dd_post_distribution",
                data=batch_save_data,
                batch_size=500,  # 每批500条
                max_concurrency=3,  # 最大3个并发批次
                timeout_per_batch=60.0
            )

            if result.success:
                logger.info(f"批量保存分配结果成功: {len(batch_save_data)}条记录")
                return len(batch_save_data)
            else:
                logger.warning(f"批量保存分配结果失败: {result.error_message}")
                # 降级到冲突处理
                return await self._handle_assignment_results_conflicts(batch_save_data)

        except Exception as e:
            logger.warning(f"批量保存分配结果异常，降级处理: {e}")
            # 降级到冲突处理
            return await self._handle_assignment_results_conflicts(batch_save_data)

    async def _handle_assignment_results_conflicts(
        self,
        batch_save_data: List[Dict[str, Any]]
    ) -> int:
        """
        处理分配结果保存冲突，实现智能upsert

        Args:
            batch_save_data: 要处理的分配结果数据列表

        Returns:
            成功处理的记录数量
        """
        success_count = 0

        # 分批处理，每批50条（分配结果通常冲突较多，使用较小批次）
        batch_size = 50
        for i in range(0, len(batch_save_data), batch_size):
            batch = batch_save_data[i:i+batch_size]

            try:
                # 尝试批量插入小批次
                result = await self.rdb_client.abatch_insert(
                    table="biz_dd_post_distribution",
                    data=batch
                )

                if result.success:
                    success_count += len(batch)
                    logger.debug(f"分配结果小批次插入成功: {len(batch)}条")
                else:
                    # 小批次失败，逐条处理upsert
                    success_count += await self._handle_individual_assignment_upserts(batch)

            except Exception as e:
                logger.warning(f"分配结果小批次插入失败: {e}")
                # 逐条处理upsert
                success_count += await self._handle_individual_assignment_upserts(batch)

        return success_count

    async def _handle_individual_assignment_upserts(
        self,
        save_data_list: List[Dict[str, Any]]
    ) -> int:
        """
        逐条处理分配结果upsert操作

        Args:
            save_data_list: 要处理的分配结果数据列表

        Returns:
            成功处理的记录数量
        """
        success_count = 0

        for save_data in save_data_list:
            try:
                # 尝试插入
                await self.dd_crud.create_post_distribution(save_data)
                success_count += 1

            except DDError as e:
                # 如果是重复记录，尝试更新
                if "duplicate" in str(e).lower() or "exists" in str(e).lower():
                    try:
                        update_data = {
                            'bdr03': save_data['bdr03'],
                            'update_time': save_data['update_time']
                        }
                        await self.dd_crud.update_post_distributions(
                            update_data,
                            conditions={
                                'pre_distribution_id': save_data['pre_distribution_id'],
                                'dept_id': save_data['dept_id']
                            }
                        )
                        success_count += 1
                        logger.debug(f"更新分配结果成功: {save_data.get('submission_id')}")

                    except Exception as update_e:
                        logger.error(f"更新分配结果失败: {save_data.get('submission_id')}, 错误: {update_e}")
                else:
                    logger.error(f"插入分配结果失败: {save_data.get('submission_id')}, 错误: {e}")
            except Exception as e:
                logger.error(f"处理分配结果失败: {save_data.get('submission_id')}, 错误: {e}")

        return success_count

    async def _batch_upsert_assignment_results(
        self,
        batch_save_data: List[Dict[str, Any]]
    ) -> int:
        """
        批量upsert分配结果，专门优化assignment_results保存

        Args:
            batch_save_data: 要保存的分配结果数据列表

        Returns:
            成功保存的记录数量
        """
        if not batch_save_data:
            return 0

        try:
            # 尝试批量插入
            logger.info(f"开始批量保存{len(batch_save_data)}条分配结果")

            result = await self.rdb_client.abatch_insert(
                table="biz_dd_post_distribution",
                data=batch_save_data,
                batch_size=500,  # 每批500条
                max_concurrency=3,  # 最大3个并发批次
                timeout_per_batch=60.0
            )

            if result.success:
                logger.info(f"批量保存分配结果成功: {len(batch_save_data)}条记录")
                return len(batch_save_data)
            else:
                logger.warning(f"批量保存分配结果失败: {result.error_message}")
                # 降级到冲突处理
                return await self._handle_assignment_results_conflicts(batch_save_data)

        except Exception as e:
            logger.warning(f"批量保存分配结果异常，降级处理: {e}")
            # 降级到冲突处理
            return await self._handle_assignment_results_conflicts(batch_save_data)

    async def _handle_assignment_results_conflicts(
        self,
        batch_save_data: List[Dict[str, Any]]
    ) -> int:
        """
        处理分配结果保存冲突，实现智能upsert

        Args:
            batch_save_data: 要处理的分配结果数据列表

        Returns:
            成功处理的记录数量
        """
        success_count = 0

        # 分批处理，每批50条（分配结果通常冲突较多，使用较小批次）
        batch_size = 50
        for i in range(0, len(batch_save_data), batch_size):
            batch = batch_save_data[i:i+batch_size]

            try:
                # 尝试批量插入小批次
                result = await self.rdb_client.abatch_insert(
                    table="biz_dd_post_distribution",
                    data=batch
                )

                if result.success:
                    success_count += len(batch)
                    logger.debug(f"分配结果小批次插入成功: {len(batch)}条")
                else:
                    # 小批次失败，逐条处理upsert
                    success_count += await self._handle_individual_assignment_upserts(batch)

            except Exception as e:
                logger.warning(f"分配结果小批次插入失败: {e}")
                # 逐条处理upsert
                success_count += await self._handle_individual_assignment_upserts(batch)

        return success_count

    async def _handle_individual_assignment_upserts(
        self,
        save_data_list: List[Dict[str, Any]]
    ) -> int:
        """
        逐条处理分配结果upsert操作

        Args:
            save_data_list: 要处理的分配结果数据列表

        Returns:
            成功处理的记录数量
        """
        success_count = 0

        for save_data in save_data_list:
            try:
                # 尝试插入
                await self.dd_crud.create_post_distribution(save_data)
                success_count += 1

            except DDError as e:
                # 如果是重复记录，尝试更新
                if "duplicate" in str(e).lower() or "exists" in str(e).lower():
                    try:
                        update_data = {
                            'bdr03': save_data['bdr03'],
                            'update_time': save_data['update_time']
                        }
                        await self.dd_crud.update_post_distributions(
                            update_data,
                            conditions={
                                'pre_distribution_id': save_data['pre_distribution_id'],
                                'dept_id': save_data['dept_id']
                            }
                        )
                        success_count += 1
                        logger.debug(f"更新分配结果成功: {save_data.get('submission_id')}")

                    except Exception as update_e:
                        logger.error(f"更新分配结果失败: {save_data.get('submission_id')}, 错误: {update_e}")
                else:
                    logger.error(f"插入分配结果失败: {save_data.get('submission_id')}, 错误: {e}")
            except Exception as e:
                logger.error(f"处理分配结果失败: {save_data.get('submission_id')}, 错误: {e}")

        return success_count

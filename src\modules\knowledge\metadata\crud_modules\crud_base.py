"""
Metadata系统CRUD操作基类

提供所有CRUD模块共享的基础功能：
- 向量数据库操作
- 错误处理
- 批处理优化
- 级联关系处理
"""

from typing import Any, Dict, List, Optional, Tuple, Union
import logging
from datetime import datetime

from ..shared.exceptions import MetadataError, MetadataValidationError, MetadataNotFoundError, MetadataConflictError
from ..shared.constants import MetadataConstants, MetadataTableNames, MetadataCascadeRelations, MetadataVectorCollections
from ..shared.utils import MetadataUtils

logger = logging.getLogger(__name__)


class MetadataCrudBase:
    """Metadata系统CRUD操作基类"""

    def __init__(self, rdb_client: Any, vdb_client: Any = None, embedding_client: Any = None):
        """
        初始化CRUD操作

        Args:
            rdb_client: 关系型数据库客户端（MySQL）
            vdb_client: 向量数据库客户端（PGVector，可选）
            embedding_client: 向量化模型客户端（可选）
        """
        self.rdb_client = rdb_client
        self.vdb_client = vdb_client
        self.embedding_client = embedding_client

    # ==================== 向量数据库操作框架 ====================

    def _get_vector_collection_name(self, entity_type: str) -> str:
        """
        根据实体类型获取对应的向量集合名称

        Args:
            entity_type: 实体类型

        Returns:
            向量集合名称

        Raises:
            ValueError: 不支持的实体类型
        """
        collection_name = MetadataVectorCollections.ENTITY_TYPE_TO_COLLECTION.get(entity_type)
        if not collection_name:
            raise ValueError(f"不支持的实体类型: {entity_type}")
        return collection_name

    def _build_vector_data(self, entity_type: str, entity_id: int, field_code: str,
                          knowledge_id: str, embedding: list, content: str, data: dict) -> list:
        """
        根据实体类型构建向量数据（适配现有向量集合结构）

        Args:
            entity_type: 实体类型
            entity_id: 实体ID
            field_code: 字段代码
            knowledge_id: 知识库ID
            embedding: 向量数据
            content: 内容
            data: 原始数据

        Returns:
            向量数据列表
        """
        if entity_type in ['source_database', 'index_database']:
            # 数据库级向量集合结构 - 使用表级集合，db_id设为entity_id，table_id设为0
            return [{
                "embedding": embedding,
                "knowledge_id": knowledge_id,
                "source_type": "SOURCE" if "source" in entity_type else "INDEX",
                "db_id": entity_id,
                "table_id": 0,  # 数据库级别，table_id设为0
                "content_type": field_code
            }]
        elif entity_type in ['source_table', 'index_table']:
            # 表级向量集合结构
            return [{
                "embedding": embedding,
                "knowledge_id": knowledge_id,
                "source_type": "SOURCE" if "source" in entity_type else "INDEX",
                "db_id": data.get('db_id', 0),
                "table_id": entity_id,
                "content_type": field_code
            }]
        elif entity_type in ['source_column', 'index_column']:
            # 字段级向量集合结构
            return [{
                "embedding": embedding,
                "knowledge_id": knowledge_id,
                "source_type": "SOURCE" if "source" in entity_type else "INDEX",
                "table_id": data.get('table_id', 0),
                "column_id": entity_id,
                "content_type": field_code
            }]
        elif entity_type == 'code_value':
            # 码值级向量集合结构 - 只有码值才创建向量，码值集不创建向量
            return [{
                "embedding": embedding,
                "knowledge_id": knowledge_id,
                "code_set_id": data.get('code_set_id', 0),
                "code_value_id": entity_id,
                "content_type": field_code
            }]
        else:
            raise ValueError(f"不支持的实体类型: {entity_type}")

    def _build_delete_expression(self, entity_type: str, entity_id: int, knowledge_id: str) -> str:
        """
        根据实体类型构建删除表达式（适配现有向量集合结构）

        Args:
            entity_type: 实体类型
            entity_id: 实体ID
            knowledge_id: 知识库ID

        Returns:
            删除表达式
        """
        if entity_type in ['source_database', 'index_database']:
            # 数据库级向量集合：按db_id删除（table_id=0的记录）
            return f"knowledge_id = '{knowledge_id}' AND db_id = {entity_id} AND table_id = 0"
        elif entity_type in ['source_table', 'index_table']:
            # 表级向量集合：按table_id删除
            return f"knowledge_id = '{knowledge_id}' AND table_id = {entity_id}"
        elif entity_type in ['source_column', 'index_column']:
            # 字段级向量集合：按column_id删除
            return f"knowledge_id = '{knowledge_id}' AND column_id = {entity_id}"
        elif entity_type == 'code_value':
            # 码值级向量集合：按code_value_id删除
            return f"knowledge_id = '{knowledge_id}' AND code_value_id = {entity_id}"
        else:
            raise ValueError(f"不支持的实体类型: {entity_type}")

    def _build_delete_expression_without_knowledge_id(self, entity_type: str, entity_id: int) -> str:
        """
        根据实体类型构建删除表达式（不需要knowledge_id）

        Args:
            entity_type: 实体类型
            entity_id: 实体ID

        Returns:
            删除表达式
        """
        if entity_type in ['source_database', 'index_database', 'source_table', 'index_table']:
            # 表级向量集合：按table_id删除
            return f"table_id = {entity_id}"
        elif entity_type in ['source_column', 'index_column']:
            # 字段级向量集合：按column_id删除
            return f"column_id = {entity_id}"
        elif entity_type == 'code_value':
            # 码值级向量集合：按code_value_id删除
            return f"code_value_id = {entity_id}"
        else:
            raise ValueError(f"不支持的实体类型: {entity_type}")

    def _get_vectorized_entity_types(self) -> List[str]:
        """获取需要向量化的实体类型列表"""
        return MetadataVectorCollections.VECTORIZED_ENTITY_TYPES

    def _is_vectorized_entity(self, entity_type: str) -> bool:
        """判断实体类型是否需要向量化"""
        return entity_type in MetadataVectorCollections.VECTORIZED_ENTITY_TYPES

    def _get_id_field_for_entity_type(self, entity_type: str) -> str:
        """获取实体类型对应的ID字段名"""
        return MetadataCascadeRelations.ENTITY_TYPE_TO_ID_FIELD.get(entity_type, 'id')

    # ==================== 通用查询方法 ====================

    async def _aselect(self, table: str, where: Optional[Dict[str, Any]] = None,
                      order_by: Optional[List[str]] = None, limit: Optional[int] = None,
                      offset: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        兼容性方法：将aselect调用转换为aquery调用

        Args:
            table: 表名
            where: WHERE条件
            order_by: 排序字段
            limit: 限制数量
            offset: 偏移量

        Returns:
            查询结果列表
        """
        query_request = {
            "table": table
        }

        # 只有在有条件时才添加filters
        if where:
            query_request["filters"] = where

        # 只有在有值时才添加limit和offset
        if limit is not None:
            query_request["limit"] = limit
        if offset is not None:
            query_request["offset"] = offset

        if order_by:
            # 转换order_by格式
            sorts = []
            for order_field in order_by:
                if ' DESC' in order_field.upper():
                    field = order_field.replace(' DESC', '').replace(' desc', '').strip()
                    sorts.append({"field": field, "order": "desc"})
                elif ' ASC' in order_field.upper():
                    field = order_field.replace(' ASC', '').replace(' asc', '').strip()
                    sorts.append({"field": field, "order": "asc"})
                else:
                    sorts.append({"field": order_field.strip(), "order": "asc"})
            query_request["sorts"] = sorts

        result = await self.rdb_client.aquery(query_request)
        return result.data if result else []

    async def _abatch_delete_by_conditions(self, table: str, where_conditions: List[Dict[str, Any]]) -> Any:
        """
        兼容性方法：通过条件批量删除

        Args:
            table: 表名
            where_conditions: 删除条件列表

        Returns:
            操作结果

        Raises:
            MetadataError: 当批量删除操作失败时
        """
        try:
            # 使用新的客户端批量删除接口
            result = await self.rdb_client.abatch_delete(
                table=table,
                conditions=where_conditions,
                batch_size=len(where_conditions) if len(where_conditions) <= 100 else 100,
                max_concurrency=1 if len(where_conditions) <= 10 else 3
            )
            return result
        except Exception as e:
            logger.error(f"批量删除失败: table={table}, conditions={where_conditions}, error={e}")
            raise MetadataError(f"批量删除失败: {e}")

    # ==================== 向量操作方法 ====================

    async def _create_vectors(self, entity_type: str, entity_id: int, entity_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        为实体创建向量记录

        Args:
            entity_type: 实体类型 (source_database, index_database, source_table, etc.)
            entity_id: 实体ID
            entity_data: 实体数据

        Returns:
            向量创建结果列表
        """
        if not self.vdb_client or not self.embedding_client:
            return []

        try:
            # 提取需要向量化的内容
            vectorized_content = MetadataUtils.extract_vectorized_content(entity_data)
            if not vectorized_content:
                return []

            vector_results = []
            knowledge_id = entity_data.get('knowledge_id')

            # 收集所有需要向量化的内容
            contents_to_vectorize = []
            field_codes = []

            for field_code, content in vectorized_content.items():
                if content and content.strip():
                    contents_to_vectorize.append(content)
                    field_codes.append(field_code)

            if not contents_to_vectorize:
                return []

            try:
                # 批量生成向量
                embedding_result = await self.embedding_client.ainvoke(
                    texts=contents_to_vectorize,
                    user="metadata_system"
                )

                if not embedding_result.embeddings or len(embedding_result.embeddings) != len(contents_to_vectorize):
                    raise ValueError("向量生成失败：返回结果数量不匹配")

                # 按集合分组向量数据
                collection_data_map = {}

                for i, (field_code, content) in enumerate(zip(field_codes, contents_to_vectorize)):
                    embedding = embedding_result.embeddings[i]
                    collection_name = self._get_vector_collection_name(entity_type)

                    # 根据实体类型构造向量数据
                    vector_data = self._build_vector_data(
                        entity_type, entity_id, field_code, knowledge_id,
                        embedding, content, entity_data
                    )

                    if collection_name not in collection_data_map:
                        collection_data_map[collection_name] = []
                    collection_data_map[collection_name].extend(vector_data)

                    vector_results.append({
                        "vector_id": f"{entity_type}_{entity_id}_{field_code}",
                        "field_code": field_code,
                        "status": "success"
                    })

                # 批量插入向量数据
                for collection_name, data_list in collection_data_map.items():
                    if data_list:
                        await self.vdb_client.ainsert(
                            collection_name=collection_name,
                            data=data_list
                        )

            except Exception as e:
                logger.warning(f"批量向量化失败: {e}")
                # 回退到逐个处理
                for field_code, content in zip(field_codes, contents_to_vectorize):
                    try:
                        # 单个生成向量
                        embedding_result = await self.embedding_client.ainvoke(
                            texts=[content],
                            user="metadata_system"
                        )

                        if embedding_result.embeddings:
                            embedding = embedding_result.embeddings[0]
                            collection_name = self._get_vector_collection_name(entity_type)
                            vector_data = self._build_vector_data(
                                entity_type, entity_id, field_code, knowledge_id,
                                embedding, content, entity_data
                            )

                            await self.vdb_client.ainsert(
                                collection_name=collection_name,
                                data=vector_data
                            )

                            vector_results.append({
                                "vector_id": f"{entity_type}_{entity_id}_{field_code}",
                                "field_code": field_code,
                                "status": "success"
                            })
                        else:
                            raise ValueError("向量生成失败")

                    except Exception as inner_e:
                        logger.warning(f"字段 {field_code} 向量化失败: {inner_e}")
                        vector_results.append({
                            "vector_id": f"{entity_type}_{entity_id}_{field_code}",
                            "field_code": field_code,
                            "status": "failed",
                            "error": str(inner_e)
                        })

            logger.info(f"实体 {entity_type}_{entity_id} 向量创建完成: {len(vector_results)} 个向量")
            return vector_results

        except Exception as e:
            logger.error(f"创建向量失败: entity_type={entity_type}, entity_id={entity_id}, error={e}")
            return []

    async def _update_vectors(self, entity_type: str, entity_id: int, entity_data: Dict[str, Any], full_entity_data: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        更新实体的向量记录

        Args:
            entity_type: 实体类型
            entity_id: 实体ID
            entity_data: 更新的实体数据
            full_entity_data: 完整的实体数据（包含knowledge_id等必要字段）

        Returns:
            向量更新结果列表
        """
        if not self.vdb_client or not self.embedding_client:
            return []

        try:
            # 使用完整的实体数据，如果没有提供则使用更新数据
            data_for_vectors = full_entity_data if full_entity_data else entity_data

            # 确保有knowledge_id
            knowledge_id = data_for_vectors.get('knowledge_id')
            if not knowledge_id:
                logger.warning(f"更新向量时缺少knowledge_id: entity_type={entity_type}, entity_id={entity_id}")
                return []

            # 先删除旧向量
            await self._delete_vectors(entity_type, entity_id, knowledge_id)

            # 合并更新数据到完整数据中
            if full_entity_data and entity_data:
                merged_data = {**full_entity_data, **entity_data}
            else:
                merged_data = data_for_vectors

            # 重新创建向量
            return await self._create_vectors(entity_type, entity_id, merged_data)

        except Exception as e:
            logger.error(f"更新向量失败: entity_type={entity_type}, entity_id={entity_id}, error={e}")
            return []

    async def _delete_vectors(self, entity_type: str, entity_id: int, knowledge_id: str = None) -> bool:
        """
        删除实体的所有向量记录

        Args:
            entity_type: 实体类型
            entity_id: 实体ID
            knowledge_id: 知识库ID（可选，如果不提供则删除所有相关向量）

        Returns:
            删除是否成功
        """
        if not self.vdb_client:
            return True

        try:
            # 根据实体类型获取正确的向量集合名称
            collection_name = self._get_vector_collection_name(entity_type)

            # 根据实体类型构造删除表达式（适配现有向量集合结构）
            if knowledge_id:
                delete_expr = self._build_delete_expression(entity_type, entity_id, knowledge_id)
            else:
                # 如果没有knowledge_id，只按实体ID删除
                delete_expr = self._build_delete_expression_without_knowledge_id(entity_type, entity_id)

            try:
                # 使用批量删除方法
                result = await self.vdb_client.abatch_delete(
                    collection_name=collection_name,
                    exprs=[delete_expr]
                )
                deleted_count = result.get('delete_count', 0)
                logger.info(f"实体 {entity_type}_{entity_id} 向量删除完成: {deleted_count} 个向量")
                return True
            except Exception as e:
                # 如果集合不存在或其他错误，记录警告但不抛出异常
                logger.warning(f"删除向量失败（可能是集合不存在）: {e}")
                return True  # 返回True，因为这不应该阻止数据库记录的删除

        except Exception as e:
            logger.error(f"删除向量失败: entity_type={entity_type}, entity_id={entity_id}, error={e}")
            return False

    async def _cascade_delete_vectors(self, parent_type: str, parent_id: int) -> int:
        """
        级联删除向量记录

        Args:
            parent_type: 父实体类型
            parent_id: 父实体ID

        Returns:
            删除的向量数量
        """
        if not self.vdb_client:
            return 0

        deleted_count = 0

        try:
            if parent_type in ['source_database', 'index_database']:
                # 删除数据库时，直接删除数据库相关的所有向量（包括表和字段）
                try:
                    # 批量删除表级和字段级向量
                    delete_operations = [
                        (MetadataVectorCollections.MD_TABLE_EMBEDDINGS, f"db_id = {parent_id}"),
                        # 字段级向量需要通过table_id关联，这里使用简化策略
                        # 在实际应用中可能需要先查询相关table_id再删除
                    ]

                    # 批量执行删除操作
                    for collection, expr in delete_operations:
                        await self.vdb_client.abatch_delete(
                            collection_name=collection,
                            exprs=[expr]
                        )

                    deleted_count += 1
                    logger.info(f"数据库 {parent_id} 的向量已通过批量级联删除清理")

                except Exception as e:
                    logger.warning(f"数据库 {parent_id} 向量级联删除失败: {e}")

            elif parent_type in ['source_table', 'index_table']:
                # 删除表时，批量删除表级和字段级向量
                try:
                    delete_operations = [
                        (MetadataVectorCollections.MD_TABLE_EMBEDDINGS, f"table_id = {parent_id}"),
                        (MetadataVectorCollections.MD_COLUMN_EMBEDDINGS, f"table_id = {parent_id}")
                    ]

                    # 批量执行删除操作
                    for collection, expr in delete_operations:
                        await self.vdb_client.abatch_delete(
                            collection_name=collection,
                            exprs=[expr]
                        )

                    deleted_count += 1
                    logger.info(f"表 {parent_id} 的向量已通过批量级联删除清理")

                except Exception as e:
                    logger.warning(f"表 {parent_id} 向量级联删除失败: {e}")

            else:
                # 其他类型直接删除
                await self._delete_vectors(parent_type, parent_id)
                deleted_count += 1

        except Exception as e:
            logger.error(f"级联删除向量失败: parent_type={parent_type}, parent_id={parent_id}, error={e}")

        return deleted_count

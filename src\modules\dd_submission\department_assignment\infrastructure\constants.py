"""
部门职责分配业务常量配置

定义义务解读部门职责分配的业务常量和配置参数
"""

from typing import List, Dict, Any


class DepartmentAssignmentConstants:
    """部门职责分配常量"""
    
    # ==================== 套系分类配置 ====================

    # 非标准套系类型（survey类型）
    NON_STANDARD_SET_TYPES = [
        "survey",
        "Survey",
        "SURVEY"
    ]

    # 标准套系类型（除survey外的所有套系）
    STANDARD_SET_TYPES = [
        "SET_A",
        "SET_B",
        "SET_C",
        "1104",
        "DJZ",
        "EAST",
        "一表通"
    ]

    # 套系类型映射（用于标准化）
    SET_TYPE_MAPPING = {
        # 标准套系
        "SET_A": "SET_A",
        "SET_B": "SET_B",
        "SET_C": "SET_C",
        "1104": "1104",
        "djz": "DJZ",
        "DJZ": "DJZ",
        "east": "EAST",
        "EAST": "EAST",
        "一表通": "一表通",
        # 非标准套系（survey）
        "survey": "survey",
        "Survey": "survey",
        "SURVEY": "survey"
    }
    
    # ==================== 报表类型配置 ====================
    
    # 报表类型
    REPORT_TYPES = [
        "detail",    # 明细
        "index"      # 指标
    ]
    
    # 报表类型映射
    REPORT_TYPE_MAPPING = {
        "detail": "detail",
        "Detail": "detail",
        "DETAIL": "detail",
        "明细": "detail",
        "index": "index",
        "Index": "index", 
        "INDEX": "index",
        "指标": "index"
    }
    
    # ==================== 提交类型配置 ====================
    
    # 提交类型
    SUBMISSION_TYPES = [
        "range",       # 范围
        "submission"   # 填报项
    ]
    
    # 提交类型映射
    SUBMISSION_TYPE_MAPPING = {
        "range": "range",
        "Range": "range",
        "RANGE": "range", 
        "范围": "range",
        "submission": "submission",
        "Submission": "submission",
        "SUBMISSION": "submission",
        "填报项": "submission"
    }
    
    # ==================== 数据层配置 ====================
    
    # 数据层类型
    DATA_LAYERS = [
        "ADS",
        "BDM", 
        "IDM",
        "ADM",
        "ODS",
        "范围"
    ]
    
    # 数据层映射
    DATA_LAYER_MAPPING = {
        "ads": "ADS",
        "ADS": "ADS",
        "bdm": "BDM",
        "BDM": "BDM",
        "idm": "IDM", 
        "IDM": "IDM",
        "adm": "ADM",
        "ADM": "ADM",
        "ods": "ODS",
        "ODS": "ODS",
        "范围": "范围",
        "range": "范围"
    }
    
    # ==================== 搜索配置 ====================
    
    # 精确匹配字段
    EXACT_MATCH_FIELDS = ["dr09", "dr17"]
    
    # 混合搜索字段
    HYBRID_SEARCH_FIELDS = ["dr09", "dr17"]
    
    # 搜索权重配置
    DEFAULT_VECTOR_WEIGHT = 0.6
    DEFAULT_TEXT_WEIGHT = 0.4
    
    # 搜索限制
    DEFAULT_SEARCH_LIMIT = 100
    MAX_SEARCH_LIMIT = 500
    MIN_SIMILARITY_THRESHOLD = 0.3
    
    # ==================== 四层筛选配置 ====================
    
    # 筛选层级定义
    FILTER_LAYERS = [
        {
            "layer": "set_type",
            "name": "套系类型筛选",
            "field": "set",
            "description": "根据套系类型进行筛选"
        },
        {
            "layer": "report_type", 
            "name": "报表类型筛选",
            "field": "report_type",
            "description": "根据报表类型（明细/指标）进行筛选"
        },
        {
            "layer": "submission_type",
            "name": "提交类型筛选", 
            "field": "submission_type",
            "description": "根据提交类型（范围/填报项）进行筛选"
        },
        {
            "layer": "data_layer",
            "name": "数据层筛选",
            "field": "dr01", 
            "description": "根据数据层进行筛选"
        }
    ]
    
    # ==================== 置信度配置 ====================
    
    # 置信度级别
    CONFIDENCE_LEVELS = {
        "unique": "唯一匹配",      # 筛选后剩余唯一记录
        "multiple": "多个匹配",    # 筛选后剩余多个记录
        "fallback": "回退匹配"     # 某层筛选后无结果，回退到上一层
    }
    
    # 高置信度阈值
    HIGH_CONFIDENCE_THRESHOLD = 0.9
    MEDIUM_CONFIDENCE_THRESHOLD = 0.7
    LOW_CONFIDENCE_THRESHOLD = 0.5
    
    # ==================== 数据库表配置 ====================
    
    # 相关表名
    TABLE_NAMES = {
        "pre_distribution": "biz_dd_pre_distribution",
        "submission_data": "dd_submission_data", 
        "embeddings": "dd_embeddings"
    }
    
    # 关键字段映射
    FIELD_MAPPINGS = {
        "submission_id": "submission_id",
        "dr09": "dr09",
        "dr17": "dr17", 
        "dr01": "dr01",
        "dr22": "dr22",
        "set": "set",
        "report_type": "report_type",
        "submission_type": "submission_type"
    }
    
    # ==================== 错误消息配置 ====================
    
    ERROR_MESSAGES = {
        "invalid_request": "请求参数无效",
        "missing_required_field": "缺少必填字段: {field}",
        "invalid_set_type": "无效的套系类型: {value}",
        "invalid_report_type": "无效的报表类型: {value}",
        "invalid_submission_type": "无效的提交类型: {value}",
        "invalid_data_layer": "无效的数据层: {value}",
        "search_failed": "搜索失败: {error}",
        "filter_failed": "筛选失败: {error}",
        "no_candidates_found": "未找到候选记录",
        "database_error": "数据库操作失败: {error}"
    }
    
    # ==================== 日志配置 ====================
    
    LOG_MESSAGES = {
        "start_assignment": "开始部门职责分配: submission_id={submission_id}",
        "exact_match_found": "找到精确匹配: {count}条记录",
        "exact_match_not_found": "未找到精确匹配，启用混合搜索",
        "hybrid_search_completed": "混合搜索完成: {count}条记录",
        "filter_layer_start": "开始{layer_name}: 筛选前{before_count}条记录",
        "filter_layer_complete": "完成{layer_name}: 筛选后{after_count}条记录",
        "unique_result_found": "在{layer_name}找到唯一结果，提前结束",
        "no_result_after_filter": "在{layer_name}筛选后无结果，回退到上一层",
        "assignment_complete": "部门职责分配完成: 推荐部门={department}, 置信度={confidence}"
    }
    
    # ==================== 性能配置 ====================
    
    # 缓存配置
    CACHE_TTL = 3600  # 1小时缓存
    
    # 超时配置
    SEARCH_TIMEOUT = 30  # 搜索超时（秒）
    FILTER_TIMEOUT = 10  # 筛选超时（秒）
    
    # 批处理配置
    BATCH_SIZE = 100
    MAX_CONCURRENT_REQUESTS = 10


class DepartmentAssignmentUtils:
    """部门职责分配工具类"""
    
    @staticmethod
    def normalize_set_type(set_value: str) -> str:
        """标准化套系类型"""
        if not set_value:
            return "other"
        
        normalized = DepartmentAssignmentConstants.SET_TYPE_MAPPING.get(
            set_value.strip(), 
            "other"
        )
        return normalized
    
    @staticmethod
    def normalize_report_type(report_type: str) -> str:
        """标准化报表类型"""
        if not report_type:
            return "detail"
        
        normalized = DepartmentAssignmentConstants.REPORT_TYPE_MAPPING.get(
            report_type.strip(),
            "detail"
        )
        return normalized
    
    @staticmethod
    def normalize_submission_type(submission_type: str) -> str:
        """标准化提交类型"""
        if not submission_type:
            return "submission"
        
        normalized = DepartmentAssignmentConstants.SUBMISSION_TYPE_MAPPING.get(
            submission_type.strip(),
            "submission"
        )
        return normalized
    
    @staticmethod
    def normalize_data_layer(data_layer: str) -> str:
        """标准化数据层"""
        if not data_layer:
            return "ADS"
        
        normalized = DepartmentAssignmentConstants.DATA_LAYER_MAPPING.get(
            data_layer.strip(),
            "ADS"
        )
        return normalized
    
    @staticmethod
    def is_standard_set_type(set_value: str) -> bool:
        """
        判断是否为标准套系类型

        根据要求：survey为非标准套系，其他为标准套系
        """
        if not set_value:
            return True  # 空值默认为标准套系

        normalized = DepartmentAssignmentUtils.normalize_set_type(set_value)
        # survey为非标准套系，其他都为标准套系
        return normalized not in DepartmentAssignmentConstants.NON_STANDARD_SET_TYPES

    @staticmethod
    def classify_set_type(set_value: str) -> str:
        """
        分类套系类型

        Returns:
            'standard': 标准套系
            'survey': 非标准套系（survey类型）
        """
        if not set_value:
            return 'standard'

        normalized = DepartmentAssignmentUtils.normalize_set_type(set_value)
        if normalized in DepartmentAssignmentConstants.NON_STANDARD_SET_TYPES:
            return 'survey'
        else:
            return 'standard'
    
    @staticmethod
    def get_filter_layer_config(layer_name: str) -> Dict[str, Any]:
        """获取筛选层配置"""
        for layer_config in DepartmentAssignmentConstants.FILTER_LAYERS:
            if layer_config["layer"] == layer_name:
                return layer_config
        return {}

"""
DD系统共享组件

提供系统级的共享功能：
- 常量定义
- 异常类
- 工具函数
- 数据验证器
"""

from .constants import DDConstants, DDTableNames, DDFieldCategories, DDWorkflowStatus
from .exceptions import (
    DDError, DDValidationError, DDNotFoundError, DDDatabaseError,
    DDWorkflowError, DDVectorizationError
)
from .utils import DDUtils

__all__ = [
    # 常量
    "DDConstants",
    "DDTableNames",
    "DDFieldCategories",
    "DDWorkflowStatus",

    # 异常
    "DDError",
    "DDValidationError",
    "DDNotFoundError",
    "DDDatabaseError",
    "DDWorkflowError",
    "DDVectorizationError",

    # 工具
    "DDUtils",
]

# Service Layer 配置
# 简化后只保留数据库配置

defaults:
  - _self_
  # - database: default
  # --- 加载所有可用的数据库和模型配置 ---
  # 下面的配置项会委托给对应配置组（如'database'）下的defaults.yaml文件，
  # 来完成该模块的完整配置组合。
  - database: defaults
  - model: defaults
  # --- 工作流加载方式变更 ---
  # 直接加载工作流配置文件
  - workflow/<EMAIL>
  - workflow/<EMAIL>

# --- 模块级配置 ---
# 各模块可以指定自己使用的数据库配置
knowledge:
  doc:
    rdb: database.rdbs.mysql  # 文档模块使用的关系型数据库配置路径
    vdb: database.vdbs.pgvector  # 文档模块使用的向量数据库配置路径
    embedding: model.embeddings.moka-m3e-base  # 文档模块使用的嵌入模型配置路径
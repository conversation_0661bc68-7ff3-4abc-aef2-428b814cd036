#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：外规内化 
@File    ：ier_api.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/7/22 10:34 
@Desc    ：外规内化api测试
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.ier.api import router as ier_router


# 创建FastAPI应用
app = FastAPI(
    title="HSBC知识管理系统",
    description="HSBC Knowledge Management System\n\n"
                "企业级知识管理和数据治理平台，提供完整的API服务。\n\n"
                "## 核心功能模块\n\n"
                "### 📊 DD提交管理\n"
                "• **填报记录匹配**: 智能匹配历史填报数据\n"
                "• **部门职责分配**: 基于AI的智能部门分配\n"
                "• **批量处理**: 高效的批量数据处理\n\n"
                "### 📚 知识库管理\n"
                "• **知识库管理**: 支持灵活的模型配置\n"
                "• **元数据管理**: MetaData类型的模板管理\n"
                "• **DD数据字典**: DD类型的数据需求管理\n"
                "• **向量搜索**: 语义检索和智能搜索\n\n"
                "### 🔧 企业级特性\n"
                "• **数据治理**: 完整的数据管理流程\n"
                "• **性能优化**: 高性能数据库操作\n"
                "• **安全可靠**: 企业级安全和稳定性\n"
                "• **API文档**: 完整的接口文档和测试\n\n"
                "✅ **生产就绪**: 经过充分测试，性能优化，数据稳定\n"
                "🚀 **最新功能**: 智能部门分配和批量处理API",
    version="3.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Register routes
app.include_router(ier_router, prefix="/api")

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动外规内化测试主服务")
    print("📍 端口: 30337")
    print("🌐 文档: http://localhost:30337/docs")
    # print("✅ 环境: Production (生产)")
    # print("💼 用途: 企业级知识管理和数据治理")
    # print("🔧 模块: DD提交管理 + 知识库管理")
    uvicorn.run(app, host="0.0.0.0", port=30337)
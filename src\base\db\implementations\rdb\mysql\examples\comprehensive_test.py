"""
MySQL客户端综合测试

基于Universal架构设计的MySQL客户端完整功能测试
参考universal_sqlalchemy_complete.py的测试模式
"""

import asyncio
import time
import logging
from typing import Dict, Any, List
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入MySQL客户端
from .. import (
    create_mysql_client_from_dict,
    create_mysql_client,
    MySQLClient,
    MySQLConnectionConfig,
    MySQLError
)

# 导入RDB接口
from .....base.rdb import (
    QueryRequest, InsertRequest, UpdateRequest, DeleteRequest,
    QueryFilter, QueryFilterGroup, ComparisonOperator, LogicalOperator,
    SortOrder, QuerySort, TransactionIsolation
)


class MySQLComprehensiveTest:
    """MySQL客户端综合测试类"""

    def __init__(self):
        self.test_config = {
            "host": "localhost",
            "port": 3306,
            "database": "test_db",
            "username": "root",
            "password": "password",
            "charset": "utf8mb4",
            "pool_size": 5,
            "max_overflow": 10,
            "pool_timeout": 30.0,
            "echo": False
        }
        
        self.client: MySQLClient = None
        self.test_results = {}
        self.test_table = "test_users"

    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("开始MySQL客户端综合测试")
        
        test_methods = [
            ("1. 连接测试", self.test_connection),
            ("2. 表创建测试", self.test_table_creation),
            ("3. 插入操作测试", self.test_insert_operations),
            ("4. 查询操作测试", self.test_query_operations),
            ("5. 更新操作测试", self.test_update_operations),
            ("6. 删除操作测试", self.test_delete_operations),
            ("7. 事务测试", self.test_transaction_operations),
            ("8. 异步操作测试", self.test_async_operations),
            ("9. 性能和安全测试", self.test_performance_security)
        ]

        for test_name, test_method in test_methods:
            try:
                logger.info(f"执行 {test_name}")
                result = test_method()
                self.test_results[test_name] = {
                    "status": "PASS" if result else "FAIL",
                    "details": result
                }
                logger.info(f"{test_name} - {'通过' if result else '失败'}")
            except Exception as e:
                logger.error(f"{test_name} - 异常: {e}")
                self.test_results[test_name] = {
                    "status": "ERROR",
                    "error": str(e),
                    "traceback": traceback.format_exc()
                }

        # 清理资源
        self._cleanup()
        
        # 生成测试报告
        return self._generate_test_report()

    def test_connection(self) -> bool:
        """测试连接功能"""
        try:
            # 测试从字典创建客户端
            self.client = create_mysql_client_from_dict(self.test_config)
            
            # 测试连接
            self.client.connect()
            
            # 测试连接状态
            assert self.client.is_connected(), "连接状态检查失败"
            assert self.client.is_sync_connected, "同步连接状态检查失败"
            
            # 测试健康检查
            health = self.client.health_check()
            assert health["status"] == "healthy", f"健康检查失败: {health}"
            
            logger.info(f"连接成功: {self.test_config['host']}:{self.test_config['port']}")
            return True
            
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False

    def test_table_creation(self) -> bool:
        """测试表创建"""
        try:
            # 删除测试表（如果存在）
            drop_sql = f"DROP TABLE IF EXISTS `{self.test_table}`"
            self.client.execute(drop_sql)
            
            # 创建测试表
            create_sql = f"""
            CREATE TABLE `{self.test_table}` (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(150) UNIQUE,
                age INT,
                active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """
            
            result = self.client.execute(create_sql)
            assert result.success, "表创建失败"
            
            # 验证表是否存在
            check_sql = f"SHOW TABLES LIKE '{self.test_table}'"
            check_result = self.client.fetch_all(check_sql)
            assert len(check_result.data) == 1, "表创建验证失败"
            
            logger.info(f"测试表 {self.test_table} 创建成功")
            return True
            
        except Exception as e:
            logger.error(f"表创建测试失败: {e}")
            return False

    def test_insert_operations(self) -> bool:
        """测试插入操作"""
        try:
            # 测试单条插入 - 使用RDB接口
            insert_request = InsertRequest(
                table=self.test_table,
                data={
                    "name": "Alice",
                    "email": "<EMAIL>",
                    "age": 25,
                    "active": True
                }
            )
            
            result = self.client.insert(insert_request)
            assert result.success, "单条插入失败"
            assert result.affected_rows == 1, f"插入影响行数错误: {result.affected_rows}"
            
            # 测试批量插入 - 使用原生SQL
            batch_data = [
                ("Bob", "<EMAIL>", 30, True),
                ("Charlie", "<EMAIL>", 35, False),
                ("Diana", "<EMAIL>", 28, True)
            ]
            
            insert_sql = f"""
            INSERT INTO `{self.test_table}` (name, email, age, active) 
            VALUES (%s, %s, %s, %s)
            """
            
            for data in batch_data:
                result = self.client.execute(insert_sql, list(data))
                assert result.success, f"批量插入失败: {data}"
            
            # 验证插入结果
            count_result = self.client.fetch_all(f"SELECT COUNT(*) as count FROM `{self.test_table}`")
            total_count = count_result.data[0]["count"]
            assert total_count == 4, f"插入数据总数错误: {total_count}"
            
            logger.info(f"插入操作测试成功，共插入 {total_count} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"插入操作测试失败: {e}")
            return False

    def test_query_operations(self) -> bool:
        """测试查询操作"""
        try:
            # 测试基本查询 - 使用RDB接口
            query_request = QueryRequest(
                table=self.test_table,
                columns=["id", "name", "email", "age", "active"]
            )
            
            result = self.client.query(query_request)
            assert len(result.data) == 4, f"查询结果数量错误: {len(result.data)}"
            assert result.total_count == 4, f"总数统计错误: {result.total_count}"
            
            logger.info(f"基本查询成功，返回 {len(result.data)} 条记录")
            
            # 测试条件查询
            filter_request = QueryRequest(
                table=self.test_table,
                columns=["name", "age"],
                filters=QueryFilter(
                    field="active",
                    operator=ComparisonOperator.EQ,
                    value=True
                )
            )
            
            filter_result = self.client.query(filter_request)
            assert len(filter_result.data) == 3, f"条件查询结果错误: {len(filter_result.data)}"
            
            logger.info(f"条件查询成功，返回 {len(filter_result.data)} 条活跃用户")
            
            # 测试排序查询
            sort_request = QueryRequest(
                table=self.test_table,
                columns=["name", "age"],
                sorts=[QuerySort(field="age", order=SortOrder.DESC)]
            )
            
            sort_result = self.client.query(sort_request)
            ages = [row["age"] for row in sort_result.data]
            assert ages == sorted(ages, reverse=True), f"排序结果错误: {ages}"
            
            logger.info(f"排序查询成功，年龄降序: {ages}")
            
            # 测试分页查询
            page_request = QueryRequest(
                table=self.test_table,
                columns=["name"],
                limit=2,
                offset=1
            )
            
            page_result = self.client.query(page_request)
            assert len(page_result.data) == 2, f"分页查询结果错误: {len(page_result.data)}"
            
            logger.info(f"分页查询成功，返回 {len(page_result.data)} 条记录")
            
            # 测试复杂条件查询
            complex_filter = QueryFilterGroup(
                operator=LogicalOperator.AND,
                filters=[
                    QueryFilter(field="age", operator=ComparisonOperator.GTE, value=25),
                    QueryFilter(field="active", operator=ComparisonOperator.EQ, value=True)
                ]
            )
            
            complex_request = QueryRequest(
                table=self.test_table,
                filters=complex_filter
            )
            
            complex_result = self.client.query(complex_request)
            logger.info(f"复杂条件查询成功，返回 {len(complex_result.data)} 条记录")
            
            return True
            
        except Exception as e:
            logger.error(f"查询操作测试失败: {e}")
            return False

    def test_update_operations(self) -> bool:
        """测试更新操作"""
        try:
            # 测试单条更新 - 使用RDB接口
            update_request = UpdateRequest(
                table=self.test_table,
                data={"age": 26},
                filters=QueryFilter(
                    field="name",
                    operator=ComparisonOperator.EQ,
                    value="Alice"
                )
            )
            
            result = self.client.update(update_request)
            assert result.success, "更新操作失败"
            assert result.affected_rows == 1, f"更新影响行数错误: {result.affected_rows}"
            
            # 验证更新结果
            verify_result = self.client.fetch_all(
                f"SELECT age FROM `{self.test_table}` WHERE name = %s", 
                ["Alice"]
            )
            assert verify_result.data[0]["age"] == 26, "更新结果验证失败"
            
            logger.info("单条更新测试成功")
            
            # 测试批量更新
            batch_update_sql = f"""
            UPDATE `{self.test_table}` 
            SET updated_at = CURRENT_TIMESTAMP 
            WHERE active = %s
            """
            
            batch_result = self.client.execute(batch_update_sql, [True])
            assert batch_result.success, "批量更新失败"
            assert batch_result.affected_rows == 3, f"批量更新影响行数错误: {batch_result.affected_rows}"
            
            logger.info(f"批量更新测试成功，更新了 {batch_result.affected_rows} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"更新操作测试失败: {e}")
            return False

    def test_delete_operations(self) -> bool:
        """测试删除操作"""
        try:
            # 测试条件删除 - 使用RDB接口
            delete_request = DeleteRequest(
                table=self.test_table,
                filters=QueryFilter(
                    field="active",
                    operator=ComparisonOperator.EQ,
                    value=False
                )
            )
            
            result = self.client.delete(delete_request)
            assert result.success, "删除操作失败"
            assert result.affected_rows == 1, f"删除影响行数错误: {result.affected_rows}"
            
            # 验证删除结果
            count_result = self.client.fetch_all(f"SELECT COUNT(*) as count FROM `{self.test_table}`")
            remaining_count = count_result.data[0]["count"]
            assert remaining_count == 3, f"删除后剩余记录数错误: {remaining_count}"
            
            logger.info(f"条件删除测试成功，剩余 {remaining_count} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"删除操作测试失败: {e}")
            return False

    def test_transaction_operations(self) -> bool:
        """测试事务操作"""
        try:
            # 测试事务提交
            with self.client.transaction():
                # 在事务中插入数据
                insert_sql = f"INSERT INTO `{self.test_table}` (name, email, age) VALUES (%s, %s, %s)"
                self.client.execute(insert_sql, ["Transaction User", "<EMAIL>", 40])

                # 验证事务中的数据
                check_result = self.client.fetch_all(
                    f"SELECT COUNT(*) as count FROM `{self.test_table}` WHERE name = %s",
                    ["Transaction User"]
                )
                assert check_result.data[0]["count"] == 1, "事务中数据插入失败"

            # 验证事务提交后的数据
            final_check = self.client.fetch_all(
                f"SELECT COUNT(*) as count FROM `{self.test_table}` WHERE name = %s",
                ["Transaction User"]
            )
            assert final_check.data[0]["count"] == 1, "事务提交后数据验证失败"

            logger.info("事务提交测试成功")

            # 测试事务回滚
            try:
                with self.client.transaction():
                    # 插入数据
                    self.client.execute(insert_sql, ["Rollback User", "<EMAIL>", 45])

                    # 故意抛出异常触发回滚
                    raise Exception("测试回滚")

            except Exception as e:
                if "测试回滚" not in str(e):
                    raise

            # 验证回滚后数据不存在
            rollback_check = self.client.fetch_all(
                f"SELECT COUNT(*) as count FROM `{self.test_table}` WHERE name = %s",
                ["Rollback User"]
            )
            assert rollback_check.data[0]["count"] == 0, "事务回滚验证失败"

            logger.info("事务回滚测试成功")
            return True

        except Exception as e:
            logger.error(f"事务操作测试失败: {e}")
            return False

    def test_async_operations(self) -> bool:
        """测试异步操作"""
        try:
            async def async_test():
                # 测试异步连接
                await self.client.aconnect()
                assert self.client.is_async_connected, "异步连接状态检查失败"

                # 测试异步查询
                query_request = QueryRequest(
                    table=self.test_table,
                    columns=["name", "email"],
                    limit=2
                )

                result = await self.client.aquery(query_request)
                assert len(result.data) == 2, f"异步查询结果错误: {len(result.data)}"

                # 测试异步插入
                insert_request = InsertRequest(
                    table=self.test_table,
                    data={
                        "name": "Async User",
                        "email": "<EMAIL>",
                        "age": 33
                    }
                )

                insert_result = await self.client.ainsert(insert_request)
                assert insert_result.success, "异步插入失败"

                # 测试异步事务
                async with self.client.atransaction():
                    await self.client.aexecute(
                        f"UPDATE `{self.test_table}` SET age = %s WHERE name = %s",
                        [34, "Async User"]
                    )

                # 验证异步事务结果
                verify_result = await self.client.afetch_all(
                    f"SELECT age FROM `{self.test_table}` WHERE name = %s",
                    ["Async User"]
                )
                assert verify_result.data[0]["age"] == 34, "异步事务验证失败"

                # 测试异步断开连接
                await self.client.adisconnect()

                return True

            # 运行异步测试
            result = asyncio.run(async_test())
            logger.info("异步操作测试成功")
            return result

        except Exception as e:
            logger.error(f"异步操作测试失败: {e}")
            return False

    def test_performance_security(self) -> bool:
        """测试性能和安全性"""
        try:
            # 性能测试 - 批量操作
            start_time = time.time()

            # 批量插入测试数据
            batch_insert_sql = f"INSERT INTO `{self.test_table}` (name, email, age) VALUES (%s, %s, %s)"
            for i in range(100):
                self.client.execute(batch_insert_sql, [f"User{i}", f"user{i}@test.com", 20 + (i % 50)])

            insert_time = time.time() - start_time
            logger.info(f"批量插入100条记录耗时: {insert_time:.3f}秒")

            # 查询性能测试
            start_time = time.time()

            query_request = QueryRequest(
                table=self.test_table,
                columns=["id", "name", "age"],
                filters=QueryFilter(field="age", operator=ComparisonOperator.GTE, value=30),
                sorts=[QuerySort(field="age", order=SortOrder.ASC)],
                limit=50
            )

            result = self.client.query(query_request)
            query_time = time.time() - start_time

            logger.info(f"复杂查询耗时: {query_time:.3f}秒，返回 {len(result.data)} 条记录")

            # 连接池测试
            pool_stats = self.client.get_pool_stats()
            logger.info(f"连接池统计: {pool_stats}")

            # 性能统计测试
            perf_stats = self.client.get_performance_stats()
            logger.info(f"性能统计: 操作次数={perf_stats['operations_count']}, "
                       f"平均查询时间={perf_stats.get('avg_query_time', 0):.3f}秒")

            # SQL注入防护测试
            malicious_input = "'; DROP TABLE users; --"
            safe_query = QueryRequest(
                table=self.test_table,
                columns=["name"],
                filters=QueryFilter(field="name", operator=ComparisonOperator.EQ, value=malicious_input)
            )

            # 这应该安全执行，不会导致SQL注入
            safe_result = self.client.query(safe_query)
            assert len(safe_result.data) == 0, "SQL注入防护测试失败"

            logger.info("SQL注入防护测试通过")

            # 并发测试
            def concurrent_query():
                try:
                    result = self.client.fetch_all(f"SELECT COUNT(*) as count FROM `{self.test_table}`")
                    return result.data[0]["count"]
                except Exception as e:
                    logger.error(f"并发查询错误: {e}")
                    return None

            import threading
            threads = []
            results = []

            for i in range(5):
                thread = threading.Thread(target=lambda: results.append(concurrent_query()))
                threads.append(thread)
                thread.start()

            for thread in threads:
                thread.join()

            # 验证并发结果一致性
            valid_results = [r for r in results if r is not None]
            assert len(valid_results) == 5, f"并发测试失败，有效结果数: {len(valid_results)}"
            assert all(r == valid_results[0] for r in valid_results), "并发结果不一致"

            logger.info(f"并发测试成功，5个线程都返回了一致的结果: {valid_results[0]}")

            return True

        except Exception as e:
            logger.error(f"性能和安全测试失败: {e}")
            return False

    def _cleanup(self):
        """清理测试资源"""
        try:
            if self.client:
                # 删除测试表
                self.client.execute(f"DROP TABLE IF EXISTS `{self.test_table}`")

                # 断开连接
                self.client.disconnect()

                logger.info("测试资源清理完成")
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")

    def _generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["status"] == "PASS")
        failed_tests = sum(1 for result in self.test_results.values() if result["status"] == "FAIL")
        error_tests = sum(1 for result in self.test_results.values() if result["status"] == "ERROR")

        report = {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "errors": error_tests,
                "success_rate": f"{(passed_tests / total_tests * 100):.1f}%" if total_tests > 0 else "0%"
            },
            "test_results": self.test_results,
            "configuration": self.test_config
        }

        logger.info(f"测试完成 - 总计: {total_tests}, 通过: {passed_tests}, 失败: {failed_tests}, 错误: {error_tests}")
        return report


def main():
    """主函数 - 运行综合测试"""
    print("=" * 60)
    print("MySQL客户端综合测试")
    print("=" * 60)

    tester = MySQLComprehensiveTest()

    try:
        report = tester.run_all_tests()

        print("\n" + "=" * 60)
        print("测试报告")
        print("=" * 60)
        print(f"总测试数: {report['summary']['total_tests']}")
        print(f"通过: {report['summary']['passed']}")
        print(f"失败: {report['summary']['failed']}")
        print(f"错误: {report['summary']['errors']}")
        print(f"成功率: {report['summary']['success_rate']}")

        print("\n详细结果:")
        for test_name, result in report['test_results'].items():
            status_symbol = "✓" if result["status"] == "PASS" else "✗" if result["status"] == "FAIL" else "!"
            print(f"{status_symbol} {test_name}: {result['status']}")

            if result["status"] == "ERROR":
                print(f"  错误: {result.get('error', 'Unknown error')}")

        return report['summary']['passed'] == report['summary']['total_tests']

    except Exception as e:
        print(f"测试执行失败: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
